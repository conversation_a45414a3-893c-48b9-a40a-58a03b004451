// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *DescribeDBInspectionsReq) IsValid() error {
	if p.SearchParam != nil {
		if err := p.SearchParam.IsValid(); err != nil {
			return fmt.Errorf("field SearchParam not valid, %w", err)
		}
	}
	return nil
}
func (p *ScoreDistribution) IsValid() error {
	return nil
}
func (p *DescribeDBInspectionsResp) IsValid() error {
	return nil
}
func (p *InspectionSearchParam) IsValid() error {
	return nil
}
func (p *InspectAgg) IsValid() error {
	return nil
}
func (p *InspectVal) IsValid() error {
	return nil
}
func (p *InspectionItem) IsValid() error {
	if p.LastOneAgg != nil {
		if err := p.LastOneAgg.IsValid(); err != nil {
			return fmt.Errorf("field LastOneAgg not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateAutomaticInspectionReq) IsValid() error {
	return nil
}
func (p *CreateAutomaticInspectionResp) IsValid() error {
	return nil
}
func (p *InspectionInstanceInfo) IsValid() error {
	return nil
}
func (p *InspectionTaskInfo) IsValid() error {
	return nil
}
func (p *CreateManualInspectionReq) IsValid() error {
	return nil
}
func (p *CreateManualInspectionResp) IsValid() error {
	return nil
}
func (p *DescribeDBInspectionReportReq) IsValid() error {
	return nil
}
func (p *InspectionNodeReport) IsValid() error {
	if p.NodeInfoObject != nil {
		if err := p.NodeInfoObject.IsValid(); err != nil {
			return fmt.Errorf("field NodeInfoObject not valid, %w", err)
		}
	}
	if p.Agg != nil {
		if err := p.Agg.IsValid(); err != nil {
			return fmt.Errorf("field Agg not valid, %w", err)
		}
	}
	if p.DescribeTableSpaceAutoIncrResp != nil {
		if err := p.DescribeTableSpaceAutoIncrResp.IsValid(); err != nil {
			return fmt.Errorf("field DescribeTableSpaceAutoIncrResp not valid, %w", err)
		}
	}
	if p.DescribeTableSpaceResp != nil {
		if err := p.DescribeTableSpaceResp.IsValid(); err != nil {
			return fmt.Errorf("field DescribeTableSpaceResp not valid, %w", err)
		}
	}
	if p.DescribeFullSqlStatusResp != nil {
		if err := p.DescribeFullSqlStatusResp.IsValid(); err != nil {
			return fmt.Errorf("field DescribeFullSqlStatusResp not valid, %w", err)
		}
	}
	if p.DescribeAggregateDialogsResp != nil {
		if err := p.DescribeAggregateDialogsResp.IsValid(); err != nil {
			return fmt.Errorf("field DescribeAggregateDialogsResp not valid, %w", err)
		}
	}
	if p.DescribeSqlTemplatesContrastResp != nil {
		if err := p.DescribeSqlTemplatesContrastResp.IsValid(); err != nil {
			return fmt.Errorf("field DescribeSqlTemplatesContrastResp not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeDBInspectionReportResp) IsValid() error {
	if p.InstanceInfo != nil {
		if err := p.InstanceInfo.IsValid(); err != nil {
			return fmt.Errorf("field InstanceInfo not valid, %w", err)
		}
	}
	if p.InspectionNodeReport != nil {
		if err := p.InspectionNodeReport.IsValid(); err != nil {
			return fmt.Errorf("field InspectionNodeReport not valid, %w", err)
		}
	}
	if p.DescribeTableSpaceAutoIncrResp != nil {
		if err := p.DescribeTableSpaceAutoIncrResp.IsValid(); err != nil {
			return fmt.Errorf("field DescribeTableSpaceAutoIncrResp not valid, %w", err)
		}
	}
	if p.DescribeTableSpaceResp != nil {
		if err := p.DescribeTableSpaceResp.IsValid(); err != nil {
			return fmt.Errorf("field DescribeTableSpaceResp not valid, %w", err)
		}
	}
	if p.DescribeFullSqlStatusResp != nil {
		if err := p.DescribeFullSqlStatusResp.IsValid(); err != nil {
			return fmt.Errorf("field DescribeFullSqlStatusResp not valid, %w", err)
		}
	}
	if p.DescribeAggregateDialogsResp != nil {
		if err := p.DescribeAggregateDialogsResp.IsValid(); err != nil {
			return fmt.Errorf("field DescribeAggregateDialogsResp not valid, %w", err)
		}
	}
	if p.DescribeSqlTemplatesContrastResp != nil {
		if err := p.DescribeSqlTemplatesContrastResp.IsValid(); err != nil {
			return fmt.Errorf("field DescribeSqlTemplatesContrastResp not valid, %w", err)
		}
	}
	return nil
}
func (p *InspectErr) IsValid() error {
	return nil
}
func (p *InspectionNodeItem) IsValid() error {
	if p.NodeInfoObject != nil {
		if err := p.NodeInfoObject.IsValid(); err != nil {
			return fmt.Errorf("field NodeInfoObject not valid, %w", err)
		}
	}
	if p.Agg != nil {
		if err := p.Agg.IsValid(); err != nil {
			return fmt.Errorf("field Agg not valid, %w", err)
		}
	}
	return nil
}
func (p *InspectionSlowLog) IsValid() error {
	if p.QueryTimeStats != nil {
		if err := p.QueryTimeStats.IsValid(); err != nil {
			return fmt.Errorf("field QueryTimeStats not valid, %w", err)
		}
	}
	return nil
}
func (p *InspectionInstanceBasicInfo) IsValid() error {
	return nil
}
func (p *InspectionInstanceResourceUsage) IsValid() error {
	return nil
}
func (p *InspectionConnectInfo) IsValid() error {
	return nil
}
func (p *DescribeDBInspectionScoreReq) IsValid() error {
	return nil
}
func (p *DescribeDBInspectionScoreResp) IsValid() error {
	return nil
}
func (p *MetricScore) IsValid() error {
	return nil
}
func (p *InspectionDataMetric) IsValid() error {
	return nil
}
func (p *DescribeBigKeysReq) IsValid() error {
	return nil
}
func (p *DescribeBigKeysResp) IsValid() error {
	return nil
}
func (p *DescribeHotKeysReq) IsValid() error {
	return nil
}
func (p *DescribeHotKeysResp) IsValid() error {
	return nil
}
func (p *InspectionBigKeysItem) IsValid() error {
	return nil
}
func (p *InspectionHotKeysItem) IsValid() error {
	return nil
}
func (p *InspectionUnit) IsValid() error {
	return nil
}
func (p *InspectionConfig) IsValid() error {
	return nil
}
func (p *ModifyInspectionConfigReq) IsValid() error {
	return nil
}
func (p *ModifyInspectionConfigResp) IsValid() error {
	return nil
}
func (p *QueryInspectionConfigReq) IsValid() error {
	return nil
}
func (p *QueryInspectionConfigResp) IsValid() error {
	return nil
}
