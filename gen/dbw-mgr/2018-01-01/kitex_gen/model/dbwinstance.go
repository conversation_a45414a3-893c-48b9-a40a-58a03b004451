// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SyncDBInstancesReq struct {
	InstanceType *InstanceType `thrift:"InstanceType,1,optional" frugal:"1,optional,InstanceType" json:"InstanceType,omitempty"`
	InstanceId   *string       `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
}

func NewSyncDBInstancesReq() *SyncDBInstancesReq {
	return &SyncDBInstancesReq{}
}

func (p *SyncDBInstancesReq) InitDefault() {
}

var SyncDBInstancesReq_InstanceType_DEFAULT InstanceType

func (p *SyncDBInstancesReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return SyncDBInstancesReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var SyncDBInstancesReq_InstanceId_DEFAULT string

func (p *SyncDBInstancesReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return SyncDBInstancesReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}
func (p *SyncDBInstancesReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *SyncDBInstancesReq) SetInstanceId(val *string) {
	p.InstanceId = val
}

var fieldIDToName_SyncDBInstancesReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
}

func (p *SyncDBInstancesReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *SyncDBInstancesReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *SyncDBInstancesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncDBInstancesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SyncDBInstancesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SyncDBInstancesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *SyncDBInstancesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}

func (p *SyncDBInstancesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncDBInstancesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SyncDBInstancesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SyncDBInstancesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SyncDBInstancesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SyncDBInstancesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SyncDBInstancesReq(%+v)", *p)

}

func (p *SyncDBInstancesReq) DeepEqual(ano *SyncDBInstancesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *SyncDBInstancesReq) Field1DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *SyncDBInstancesReq) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}

type SyncDBInstancesResp struct {
}

func NewSyncDBInstancesResp() *SyncDBInstancesResp {
	return &SyncDBInstancesResp{}
}

func (p *SyncDBInstancesResp) InitDefault() {
}

var fieldIDToName_SyncDBInstancesResp = map[int16]string{}

func (p *SyncDBInstancesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncDBInstancesResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SyncDBInstancesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncDBInstancesResp")

	if err = oprot.WriteStructBegin("SyncDBInstancesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SyncDBInstancesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SyncDBInstancesResp(%+v)", *p)

}

func (p *SyncDBInstancesResp) DeepEqual(ano *SyncDBInstancesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type SyncDBInstanceReq struct {
	InstanceType InstanceType `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId   string       `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
}

func NewSyncDBInstanceReq() *SyncDBInstanceReq {
	return &SyncDBInstanceReq{}
}

func (p *SyncDBInstanceReq) InitDefault() {
}

func (p *SyncDBInstanceReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *SyncDBInstanceReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *SyncDBInstanceReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *SyncDBInstanceReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_SyncDBInstanceReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
}

func (p *SyncDBInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncDBInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SyncDBInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SyncDBInstanceReq[fieldId]))
}

func (p *SyncDBInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *SyncDBInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *SyncDBInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncDBInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SyncDBInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SyncDBInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SyncDBInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SyncDBInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SyncDBInstanceReq(%+v)", *p)

}

func (p *SyncDBInstanceReq) DeepEqual(ano *SyncDBInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *SyncDBInstanceReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *SyncDBInstanceReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type SyncDBInstanceResp struct {
	Success bool `thrift:"Success,1,required" frugal:"1,required,bool" json:"Success"`
}

func NewSyncDBInstanceResp() *SyncDBInstanceResp {
	return &SyncDBInstanceResp{}
}

func (p *SyncDBInstanceResp) InitDefault() {
}

func (p *SyncDBInstanceResp) GetSuccess() (v bool) {
	return p.Success
}
func (p *SyncDBInstanceResp) SetSuccess(val bool) {
	p.Success = val
}

var fieldIDToName_SyncDBInstanceResp = map[int16]string{
	1: "Success",
}

func (p *SyncDBInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncDBInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSuccess bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSuccess = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSuccess {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SyncDBInstanceResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SyncDBInstanceResp[fieldId]))
}

func (p *SyncDBInstanceResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Success = _field
	return nil
}

func (p *SyncDBInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SyncDBInstanceResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SyncDBInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SyncDBInstanceResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Success", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Success); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SyncDBInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SyncDBInstanceResp(%+v)", *p)

}

func (p *SyncDBInstanceResp) DeepEqual(ano *SyncDBInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Success) {
		return false
	}
	return true
}

func (p *SyncDBInstanceResp) Field1DeepEqual(src bool) bool {

	if p.Success != src {
		return false
	}
	return true
}

type InstanceManagementDetail struct {
	InstanceType *DSType   `thrift:"InstanceType,1,optional" frugal:"1,optional,DSType" json:"InstanceType,omitempty"`
	InstanceId   *string   `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
	Source       *LinkType `thrift:"Source,3,optional" frugal:"3,optional,LinkType" json:"Source,omitempty"`
}

func NewInstanceManagementDetail() *InstanceManagementDetail {
	return &InstanceManagementDetail{}
}

func (p *InstanceManagementDetail) InitDefault() {
}

var InstanceManagementDetail_InstanceType_DEFAULT DSType

func (p *InstanceManagementDetail) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return InstanceManagementDetail_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var InstanceManagementDetail_InstanceId_DEFAULT string

func (p *InstanceManagementDetail) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return InstanceManagementDetail_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var InstanceManagementDetail_Source_DEFAULT LinkType

func (p *InstanceManagementDetail) GetSource() (v LinkType) {
	if !p.IsSetSource() {
		return InstanceManagementDetail_Source_DEFAULT
	}
	return *p.Source
}
func (p *InstanceManagementDetail) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *InstanceManagementDetail) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *InstanceManagementDetail) SetSource(val *LinkType) {
	p.Source = val
}

var fieldIDToName_InstanceManagementDetail = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "Source",
}

func (p *InstanceManagementDetail) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *InstanceManagementDetail) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *InstanceManagementDetail) IsSetSource() bool {
	return p.Source != nil
}

func (p *InstanceManagementDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceManagementDetail")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InstanceManagementDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InstanceManagementDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *InstanceManagementDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *InstanceManagementDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.Source = _field
	return nil
}

func (p *InstanceManagementDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceManagementDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("InstanceManagementDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InstanceManagementDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InstanceManagementDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InstanceManagementDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSource() {
		if err = oprot.WriteFieldBegin("Source", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Source)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InstanceManagementDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InstanceManagementDetail(%+v)", *p)

}

func (p *InstanceManagementDetail) DeepEqual(ano *InstanceManagementDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Source) {
		return false
	}
	return true
}

func (p *InstanceManagementDetail) Field1DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *InstanceManagementDetail) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceManagementDetail) Field3DeepEqual(src *LinkType) bool {

	if p.Source == src {
		return true
	} else if p.Source == nil || src == nil {
		return false
	}
	if *p.Source != *src {
		return false
	}
	return true
}

type InstanceManagementConfig struct {
	InstanceType     *DSType   `thrift:"InstanceType,1,optional" frugal:"1,optional,DSType" json:"InstanceType,omitempty"`
	InstanceId       *string   `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
	DBAUser          *string   `thrift:"DBAUser,3,optional" frugal:"3,optional,string" json:"DBAUser,omitempty"`
	OwnerUser        *string   `thrift:"OwnerUser,4,optional" frugal:"4,optional,string" json:"OwnerUser,omitempty"`
	Account          *string   `thrift:"Account,5,optional" frugal:"5,optional,string" json:"Account,omitempty"`
	Password         *string   `thrift:"Password,6,optional" frugal:"6,optional,string" json:"Password,omitempty"`
	Source           *LinkType `thrift:"Source,7,optional" frugal:"7,optional,LinkType" json:"Source,omitempty"`
	SecurityRuleId   *string   `thrift:"SecurityRuleId,8,optional" frugal:"8,optional,string" json:"SecurityRuleId,omitempty"`
	ApprovalConfigId *string   `thrift:"ApprovalConfigId,9,optional" frugal:"9,optional,string" json:"ApprovalConfigId,omitempty"`
}

func NewInstanceManagementConfig() *InstanceManagementConfig {
	return &InstanceManagementConfig{}
}

func (p *InstanceManagementConfig) InitDefault() {
}

var InstanceManagementConfig_InstanceType_DEFAULT DSType

func (p *InstanceManagementConfig) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return InstanceManagementConfig_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var InstanceManagementConfig_InstanceId_DEFAULT string

func (p *InstanceManagementConfig) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return InstanceManagementConfig_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var InstanceManagementConfig_DBAUser_DEFAULT string

func (p *InstanceManagementConfig) GetDBAUser() (v string) {
	if !p.IsSetDBAUser() {
		return InstanceManagementConfig_DBAUser_DEFAULT
	}
	return *p.DBAUser
}

var InstanceManagementConfig_OwnerUser_DEFAULT string

func (p *InstanceManagementConfig) GetOwnerUser() (v string) {
	if !p.IsSetOwnerUser() {
		return InstanceManagementConfig_OwnerUser_DEFAULT
	}
	return *p.OwnerUser
}

var InstanceManagementConfig_Account_DEFAULT string

func (p *InstanceManagementConfig) GetAccount() (v string) {
	if !p.IsSetAccount() {
		return InstanceManagementConfig_Account_DEFAULT
	}
	return *p.Account
}

var InstanceManagementConfig_Password_DEFAULT string

func (p *InstanceManagementConfig) GetPassword() (v string) {
	if !p.IsSetPassword() {
		return InstanceManagementConfig_Password_DEFAULT
	}
	return *p.Password
}

var InstanceManagementConfig_Source_DEFAULT LinkType

func (p *InstanceManagementConfig) GetSource() (v LinkType) {
	if !p.IsSetSource() {
		return InstanceManagementConfig_Source_DEFAULT
	}
	return *p.Source
}

var InstanceManagementConfig_SecurityRuleId_DEFAULT string

func (p *InstanceManagementConfig) GetSecurityRuleId() (v string) {
	if !p.IsSetSecurityRuleId() {
		return InstanceManagementConfig_SecurityRuleId_DEFAULT
	}
	return *p.SecurityRuleId
}

var InstanceManagementConfig_ApprovalConfigId_DEFAULT string

func (p *InstanceManagementConfig) GetApprovalConfigId() (v string) {
	if !p.IsSetApprovalConfigId() {
		return InstanceManagementConfig_ApprovalConfigId_DEFAULT
	}
	return *p.ApprovalConfigId
}
func (p *InstanceManagementConfig) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *InstanceManagementConfig) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *InstanceManagementConfig) SetDBAUser(val *string) {
	p.DBAUser = val
}
func (p *InstanceManagementConfig) SetOwnerUser(val *string) {
	p.OwnerUser = val
}
func (p *InstanceManagementConfig) SetAccount(val *string) {
	p.Account = val
}
func (p *InstanceManagementConfig) SetPassword(val *string) {
	p.Password = val
}
func (p *InstanceManagementConfig) SetSource(val *LinkType) {
	p.Source = val
}
func (p *InstanceManagementConfig) SetSecurityRuleId(val *string) {
	p.SecurityRuleId = val
}
func (p *InstanceManagementConfig) SetApprovalConfigId(val *string) {
	p.ApprovalConfigId = val
}

var fieldIDToName_InstanceManagementConfig = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "DBAUser",
	4: "OwnerUser",
	5: "Account",
	6: "Password",
	7: "Source",
	8: "SecurityRuleId",
	9: "ApprovalConfigId",
}

func (p *InstanceManagementConfig) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *InstanceManagementConfig) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *InstanceManagementConfig) IsSetDBAUser() bool {
	return p.DBAUser != nil
}

func (p *InstanceManagementConfig) IsSetOwnerUser() bool {
	return p.OwnerUser != nil
}

func (p *InstanceManagementConfig) IsSetAccount() bool {
	return p.Account != nil
}

func (p *InstanceManagementConfig) IsSetPassword() bool {
	return p.Password != nil
}

func (p *InstanceManagementConfig) IsSetSource() bool {
	return p.Source != nil
}

func (p *InstanceManagementConfig) IsSetSecurityRuleId() bool {
	return p.SecurityRuleId != nil
}

func (p *InstanceManagementConfig) IsSetApprovalConfigId() bool {
	return p.ApprovalConfigId != nil
}

func (p *InstanceManagementConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceManagementConfig")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InstanceManagementConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InstanceManagementConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *InstanceManagementConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *InstanceManagementConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBAUser = _field
	return nil
}
func (p *InstanceManagementConfig) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OwnerUser = _field
	return nil
}
func (p *InstanceManagementConfig) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Account = _field
	return nil
}
func (p *InstanceManagementConfig) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Password = _field
	return nil
}
func (p *InstanceManagementConfig) ReadField7(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.Source = _field
	return nil
}
func (p *InstanceManagementConfig) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SecurityRuleId = _field
	return nil
}
func (p *InstanceManagementConfig) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ApprovalConfigId = _field
	return nil
}

func (p *InstanceManagementConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InstanceManagementConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("InstanceManagementConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InstanceManagementConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InstanceManagementConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InstanceManagementConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBAUser() {
		if err = oprot.WriteFieldBegin("DBAUser", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBAUser); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InstanceManagementConfig) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOwnerUser() {
		if err = oprot.WriteFieldBegin("OwnerUser", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OwnerUser); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InstanceManagementConfig) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccount() {
		if err = oprot.WriteFieldBegin("Account", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Account); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InstanceManagementConfig) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPassword() {
		if err = oprot.WriteFieldBegin("Password", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Password); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InstanceManagementConfig) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSource() {
		if err = oprot.WriteFieldBegin("Source", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Source)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *InstanceManagementConfig) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecurityRuleId() {
		if err = oprot.WriteFieldBegin("SecurityRuleId", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SecurityRuleId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *InstanceManagementConfig) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetApprovalConfigId() {
		if err = oprot.WriteFieldBegin("ApprovalConfigId", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ApprovalConfigId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *InstanceManagementConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InstanceManagementConfig(%+v)", *p)

}

func (p *InstanceManagementConfig) DeepEqual(ano *InstanceManagementConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DBAUser) {
		return false
	}
	if !p.Field4DeepEqual(ano.OwnerUser) {
		return false
	}
	if !p.Field5DeepEqual(ano.Account) {
		return false
	}
	if !p.Field6DeepEqual(ano.Password) {
		return false
	}
	if !p.Field7DeepEqual(ano.Source) {
		return false
	}
	if !p.Field8DeepEqual(ano.SecurityRuleId) {
		return false
	}
	if !p.Field9DeepEqual(ano.ApprovalConfigId) {
		return false
	}
	return true
}

func (p *InstanceManagementConfig) Field1DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *InstanceManagementConfig) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceManagementConfig) Field3DeepEqual(src *string) bool {

	if p.DBAUser == src {
		return true
	} else if p.DBAUser == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBAUser, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceManagementConfig) Field4DeepEqual(src *string) bool {

	if p.OwnerUser == src {
		return true
	} else if p.OwnerUser == nil || src == nil {
		return false
	}
	if strings.Compare(*p.OwnerUser, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceManagementConfig) Field5DeepEqual(src *string) bool {

	if p.Account == src {
		return true
	} else if p.Account == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Account, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceManagementConfig) Field6DeepEqual(src *string) bool {

	if p.Password == src {
		return true
	} else if p.Password == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Password, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceManagementConfig) Field7DeepEqual(src *LinkType) bool {

	if p.Source == src {
		return true
	} else if p.Source == nil || src == nil {
		return false
	}
	if *p.Source != *src {
		return false
	}
	return true
}
func (p *InstanceManagementConfig) Field8DeepEqual(src *string) bool {

	if p.SecurityRuleId == src {
		return true
	} else if p.SecurityRuleId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SecurityRuleId, *src) != 0 {
		return false
	}
	return true
}
func (p *InstanceManagementConfig) Field9DeepEqual(src *string) bool {

	if p.ApprovalConfigId == src {
		return true
	} else if p.ApprovalConfigId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ApprovalConfigId, *src) != 0 {
		return false
	}
	return true
}

type DescribeInstanceManagementReq struct {
	InstanceType *DSType   `thrift:"InstanceType,1,optional" frugal:"1,optional,DSType" json:"InstanceType,omitempty"`
	InstanceId   *string   `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
	Source       *LinkType `thrift:"Source,3,optional" frugal:"3,optional,LinkType" json:"Source,omitempty"`
}

func NewDescribeInstanceManagementReq() *DescribeInstanceManagementReq {
	return &DescribeInstanceManagementReq{}
}

func (p *DescribeInstanceManagementReq) InitDefault() {
}

var DescribeInstanceManagementReq_InstanceType_DEFAULT DSType

func (p *DescribeInstanceManagementReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeInstanceManagementReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeInstanceManagementReq_InstanceId_DEFAULT string

func (p *DescribeInstanceManagementReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeInstanceManagementReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeInstanceManagementReq_Source_DEFAULT LinkType

func (p *DescribeInstanceManagementReq) GetSource() (v LinkType) {
	if !p.IsSetSource() {
		return DescribeInstanceManagementReq_Source_DEFAULT
	}
	return *p.Source
}
func (p *DescribeInstanceManagementReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DescribeInstanceManagementReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeInstanceManagementReq) SetSource(val *LinkType) {
	p.Source = val
}

var fieldIDToName_DescribeInstanceManagementReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "Source",
}

func (p *DescribeInstanceManagementReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeInstanceManagementReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeInstanceManagementReq) IsSetSource() bool {
	return p.Source != nil
}

func (p *DescribeInstanceManagementReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceManagementReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceManagementReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeInstanceManagementReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeInstanceManagementReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeInstanceManagementReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.Source = _field
	return nil
}

func (p *DescribeInstanceManagementReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceManagementReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceManagementReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceManagementReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceManagementReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceManagementReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSource() {
		if err = oprot.WriteFieldBegin("Source", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Source)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeInstanceManagementReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceManagementReq(%+v)", *p)

}

func (p *DescribeInstanceManagementReq) DeepEqual(ano *DescribeInstanceManagementReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Source) {
		return false
	}
	return true
}

func (p *DescribeInstanceManagementReq) Field1DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeInstanceManagementReq) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceManagementReq) Field3DeepEqual(src *LinkType) bool {

	if p.Source == src {
		return true
	} else if p.Source == nil || src == nil {
		return false
	}
	if *p.Source != *src {
		return false
	}
	return true
}

type DescribeInstanceManagementResp struct {
	DBAUser          string `thrift:"DBAUser,1,required" frugal:"1,required,string" json:"DBAUser"`
	OwnerUser        string `thrift:"OwnerUser,2,required" frugal:"2,required,string" json:"OwnerUser"`
	Account          string `thrift:"Account,3,required" frugal:"3,required,string" json:"Account"`
	Password         string `thrift:"Password,4,required" frugal:"4,required,string" json:"Password"`
	SecurityRule     string `thrift:"SecurityRule,5,required" frugal:"5,required,string" json:"SecurityRule"`
	SecurityRuleId   string `thrift:"SecurityRuleId,6,required" frugal:"6,required,string" json:"SecurityRuleId"`
	ApprovalConfig   string `thrift:"ApprovalConfig,7,required" frugal:"7,required,string" json:"ApprovalConfig"`
	ApprovalConfigId string `thrift:"ApprovalConfigId,8,required" frugal:"8,required,string" json:"ApprovalConfigId"`
}

func NewDescribeInstanceManagementResp() *DescribeInstanceManagementResp {
	return &DescribeInstanceManagementResp{}
}

func (p *DescribeInstanceManagementResp) InitDefault() {
}

func (p *DescribeInstanceManagementResp) GetDBAUser() (v string) {
	return p.DBAUser
}

func (p *DescribeInstanceManagementResp) GetOwnerUser() (v string) {
	return p.OwnerUser
}

func (p *DescribeInstanceManagementResp) GetAccount() (v string) {
	return p.Account
}

func (p *DescribeInstanceManagementResp) GetPassword() (v string) {
	return p.Password
}

func (p *DescribeInstanceManagementResp) GetSecurityRule() (v string) {
	return p.SecurityRule
}

func (p *DescribeInstanceManagementResp) GetSecurityRuleId() (v string) {
	return p.SecurityRuleId
}

func (p *DescribeInstanceManagementResp) GetApprovalConfig() (v string) {
	return p.ApprovalConfig
}

func (p *DescribeInstanceManagementResp) GetApprovalConfigId() (v string) {
	return p.ApprovalConfigId
}
func (p *DescribeInstanceManagementResp) SetDBAUser(val string) {
	p.DBAUser = val
}
func (p *DescribeInstanceManagementResp) SetOwnerUser(val string) {
	p.OwnerUser = val
}
func (p *DescribeInstanceManagementResp) SetAccount(val string) {
	p.Account = val
}
func (p *DescribeInstanceManagementResp) SetPassword(val string) {
	p.Password = val
}
func (p *DescribeInstanceManagementResp) SetSecurityRule(val string) {
	p.SecurityRule = val
}
func (p *DescribeInstanceManagementResp) SetSecurityRuleId(val string) {
	p.SecurityRuleId = val
}
func (p *DescribeInstanceManagementResp) SetApprovalConfig(val string) {
	p.ApprovalConfig = val
}
func (p *DescribeInstanceManagementResp) SetApprovalConfigId(val string) {
	p.ApprovalConfigId = val
}

var fieldIDToName_DescribeInstanceManagementResp = map[int16]string{
	1: "DBAUser",
	2: "OwnerUser",
	3: "Account",
	4: "Password",
	5: "SecurityRule",
	6: "SecurityRuleId",
	7: "ApprovalConfig",
	8: "ApprovalConfigId",
}

func (p *DescribeInstanceManagementResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceManagementResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDBAUser bool = false
	var issetOwnerUser bool = false
	var issetAccount bool = false
	var issetPassword bool = false
	var issetSecurityRule bool = false
	var issetSecurityRuleId bool = false
	var issetApprovalConfig bool = false
	var issetApprovalConfigId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBAUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOwnerUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSecurityRule = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetSecurityRuleId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetApprovalConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetApprovalConfigId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDBAUser {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOwnerUser {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAccount {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetPassword {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSecurityRule {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetSecurityRuleId {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetApprovalConfig {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetApprovalConfigId {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeInstanceManagementResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeInstanceManagementResp[fieldId]))
}

func (p *DescribeInstanceManagementResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBAUser = _field
	return nil
}
func (p *DescribeInstanceManagementResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OwnerUser = _field
	return nil
}
func (p *DescribeInstanceManagementResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Account = _field
	return nil
}
func (p *DescribeInstanceManagementResp) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Password = _field
	return nil
}
func (p *DescribeInstanceManagementResp) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SecurityRule = _field
	return nil
}
func (p *DescribeInstanceManagementResp) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SecurityRuleId = _field
	return nil
}
func (p *DescribeInstanceManagementResp) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ApprovalConfig = _field
	return nil
}
func (p *DescribeInstanceManagementResp) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ApprovalConfigId = _field
	return nil
}

func (p *DescribeInstanceManagementResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeInstanceManagementResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeInstanceManagementResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeInstanceManagementResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBAUser", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBAUser); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeInstanceManagementResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OwnerUser", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OwnerUser); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeInstanceManagementResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Account", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Account); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeInstanceManagementResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Password", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Password); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeInstanceManagementResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SecurityRule", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SecurityRule); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeInstanceManagementResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SecurityRuleId", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SecurityRuleId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeInstanceManagementResp) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ApprovalConfig", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ApprovalConfig); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeInstanceManagementResp) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ApprovalConfigId", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ApprovalConfigId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeInstanceManagementResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeInstanceManagementResp(%+v)", *p)

}

func (p *DescribeInstanceManagementResp) DeepEqual(ano *DescribeInstanceManagementResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DBAUser) {
		return false
	}
	if !p.Field2DeepEqual(ano.OwnerUser) {
		return false
	}
	if !p.Field3DeepEqual(ano.Account) {
		return false
	}
	if !p.Field4DeepEqual(ano.Password) {
		return false
	}
	if !p.Field5DeepEqual(ano.SecurityRule) {
		return false
	}
	if !p.Field6DeepEqual(ano.SecurityRuleId) {
		return false
	}
	if !p.Field7DeepEqual(ano.ApprovalConfig) {
		return false
	}
	if !p.Field8DeepEqual(ano.ApprovalConfigId) {
		return false
	}
	return true
}

func (p *DescribeInstanceManagementResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DBAUser, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceManagementResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.OwnerUser, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceManagementResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Account, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceManagementResp) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Password, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceManagementResp) Field5DeepEqual(src string) bool {

	if strings.Compare(p.SecurityRule, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceManagementResp) Field6DeepEqual(src string) bool {

	if strings.Compare(p.SecurityRuleId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceManagementResp) Field7DeepEqual(src string) bool {

	if strings.Compare(p.ApprovalConfig, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeInstanceManagementResp) Field8DeepEqual(src string) bool {

	if strings.Compare(p.ApprovalConfigId, src) != 0 {
		return false
	}
	return true
}

type EnableInstanceManagementReq struct {
	InstanceType     *DSType                     `thrift:"InstanceType,1,optional" frugal:"1,optional,DSType" json:"InstanceType,omitempty"`
	InstanceId       *string                     `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
	DBAUser          *string                     `thrift:"DBAUser,3,optional" frugal:"3,optional,string" json:"DBAUser,omitempty"`
	OwnerUser        *string                     `thrift:"OwnerUser,4,optional" frugal:"4,optional,string" json:"OwnerUser,omitempty"`
	Account          *string                     `thrift:"Account,5,optional" frugal:"5,optional,string" json:"Account,omitempty"`
	Password         *string                     `thrift:"Password,6,optional" frugal:"6,optional,string" json:"Password,omitempty"`
	Source           *LinkType                   `thrift:"Source,7,optional" frugal:"7,optional,LinkType" json:"Source,omitempty"`
	SecurityRuleId   *string                     `thrift:"SecurityRuleId,8,optional" frugal:"8,optional,string" json:"SecurityRuleId,omitempty"`
	ApprovalConfigId *string                     `thrift:"ApprovalConfigId,9,optional" frugal:"9,optional,string" json:"ApprovalConfigId,omitempty"`
	ConfigList       []*InstanceManagementConfig `thrift:"ConfigList,10,optional" frugal:"10,optional,list<InstanceManagementConfig>" json:"ConfigList,omitempty"`
}

func NewEnableInstanceManagementReq() *EnableInstanceManagementReq {
	return &EnableInstanceManagementReq{}
}

func (p *EnableInstanceManagementReq) InitDefault() {
}

var EnableInstanceManagementReq_InstanceType_DEFAULT DSType

func (p *EnableInstanceManagementReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return EnableInstanceManagementReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var EnableInstanceManagementReq_InstanceId_DEFAULT string

func (p *EnableInstanceManagementReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return EnableInstanceManagementReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var EnableInstanceManagementReq_DBAUser_DEFAULT string

func (p *EnableInstanceManagementReq) GetDBAUser() (v string) {
	if !p.IsSetDBAUser() {
		return EnableInstanceManagementReq_DBAUser_DEFAULT
	}
	return *p.DBAUser
}

var EnableInstanceManagementReq_OwnerUser_DEFAULT string

func (p *EnableInstanceManagementReq) GetOwnerUser() (v string) {
	if !p.IsSetOwnerUser() {
		return EnableInstanceManagementReq_OwnerUser_DEFAULT
	}
	return *p.OwnerUser
}

var EnableInstanceManagementReq_Account_DEFAULT string

func (p *EnableInstanceManagementReq) GetAccount() (v string) {
	if !p.IsSetAccount() {
		return EnableInstanceManagementReq_Account_DEFAULT
	}
	return *p.Account
}

var EnableInstanceManagementReq_Password_DEFAULT string

func (p *EnableInstanceManagementReq) GetPassword() (v string) {
	if !p.IsSetPassword() {
		return EnableInstanceManagementReq_Password_DEFAULT
	}
	return *p.Password
}

var EnableInstanceManagementReq_Source_DEFAULT LinkType

func (p *EnableInstanceManagementReq) GetSource() (v LinkType) {
	if !p.IsSetSource() {
		return EnableInstanceManagementReq_Source_DEFAULT
	}
	return *p.Source
}

var EnableInstanceManagementReq_SecurityRuleId_DEFAULT string

func (p *EnableInstanceManagementReq) GetSecurityRuleId() (v string) {
	if !p.IsSetSecurityRuleId() {
		return EnableInstanceManagementReq_SecurityRuleId_DEFAULT
	}
	return *p.SecurityRuleId
}

var EnableInstanceManagementReq_ApprovalConfigId_DEFAULT string

func (p *EnableInstanceManagementReq) GetApprovalConfigId() (v string) {
	if !p.IsSetApprovalConfigId() {
		return EnableInstanceManagementReq_ApprovalConfigId_DEFAULT
	}
	return *p.ApprovalConfigId
}

var EnableInstanceManagementReq_ConfigList_DEFAULT []*InstanceManagementConfig

func (p *EnableInstanceManagementReq) GetConfigList() (v []*InstanceManagementConfig) {
	if !p.IsSetConfigList() {
		return EnableInstanceManagementReq_ConfigList_DEFAULT
	}
	return p.ConfigList
}
func (p *EnableInstanceManagementReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *EnableInstanceManagementReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *EnableInstanceManagementReq) SetDBAUser(val *string) {
	p.DBAUser = val
}
func (p *EnableInstanceManagementReq) SetOwnerUser(val *string) {
	p.OwnerUser = val
}
func (p *EnableInstanceManagementReq) SetAccount(val *string) {
	p.Account = val
}
func (p *EnableInstanceManagementReq) SetPassword(val *string) {
	p.Password = val
}
func (p *EnableInstanceManagementReq) SetSource(val *LinkType) {
	p.Source = val
}
func (p *EnableInstanceManagementReq) SetSecurityRuleId(val *string) {
	p.SecurityRuleId = val
}
func (p *EnableInstanceManagementReq) SetApprovalConfigId(val *string) {
	p.ApprovalConfigId = val
}
func (p *EnableInstanceManagementReq) SetConfigList(val []*InstanceManagementConfig) {
	p.ConfigList = val
}

var fieldIDToName_EnableInstanceManagementReq = map[int16]string{
	1:  "InstanceType",
	2:  "InstanceId",
	3:  "DBAUser",
	4:  "OwnerUser",
	5:  "Account",
	6:  "Password",
	7:  "Source",
	8:  "SecurityRuleId",
	9:  "ApprovalConfigId",
	10: "ConfigList",
}

func (p *EnableInstanceManagementReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *EnableInstanceManagementReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *EnableInstanceManagementReq) IsSetDBAUser() bool {
	return p.DBAUser != nil
}

func (p *EnableInstanceManagementReq) IsSetOwnerUser() bool {
	return p.OwnerUser != nil
}

func (p *EnableInstanceManagementReq) IsSetAccount() bool {
	return p.Account != nil
}

func (p *EnableInstanceManagementReq) IsSetPassword() bool {
	return p.Password != nil
}

func (p *EnableInstanceManagementReq) IsSetSource() bool {
	return p.Source != nil
}

func (p *EnableInstanceManagementReq) IsSetSecurityRuleId() bool {
	return p.SecurityRuleId != nil
}

func (p *EnableInstanceManagementReq) IsSetApprovalConfigId() bool {
	return p.ApprovalConfigId != nil
}

func (p *EnableInstanceManagementReq) IsSetConfigList() bool {
	return p.ConfigList != nil
}

func (p *EnableInstanceManagementReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EnableInstanceManagementReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_EnableInstanceManagementReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *EnableInstanceManagementReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *EnableInstanceManagementReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *EnableInstanceManagementReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBAUser = _field
	return nil
}
func (p *EnableInstanceManagementReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OwnerUser = _field
	return nil
}
func (p *EnableInstanceManagementReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Account = _field
	return nil
}
func (p *EnableInstanceManagementReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Password = _field
	return nil
}
func (p *EnableInstanceManagementReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.Source = _field
	return nil
}
func (p *EnableInstanceManagementReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SecurityRuleId = _field
	return nil
}
func (p *EnableInstanceManagementReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ApprovalConfigId = _field
	return nil
}
func (p *EnableInstanceManagementReq) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceManagementConfig, 0, size)
	values := make([]InstanceManagementConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ConfigList = _field
	return nil
}

func (p *EnableInstanceManagementReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EnableInstanceManagementReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("EnableInstanceManagementReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *EnableInstanceManagementReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *EnableInstanceManagementReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *EnableInstanceManagementReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBAUser() {
		if err = oprot.WriteFieldBegin("DBAUser", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBAUser); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *EnableInstanceManagementReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOwnerUser() {
		if err = oprot.WriteFieldBegin("OwnerUser", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OwnerUser); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *EnableInstanceManagementReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccount() {
		if err = oprot.WriteFieldBegin("Account", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Account); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *EnableInstanceManagementReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPassword() {
		if err = oprot.WriteFieldBegin("Password", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Password); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *EnableInstanceManagementReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSource() {
		if err = oprot.WriteFieldBegin("Source", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Source)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *EnableInstanceManagementReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecurityRuleId() {
		if err = oprot.WriteFieldBegin("SecurityRuleId", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SecurityRuleId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *EnableInstanceManagementReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetApprovalConfigId() {
		if err = oprot.WriteFieldBegin("ApprovalConfigId", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ApprovalConfigId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *EnableInstanceManagementReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetConfigList() {
		if err = oprot.WriteFieldBegin("ConfigList", thrift.LIST, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ConfigList)); err != nil {
			return err
		}
		for _, v := range p.ConfigList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *EnableInstanceManagementReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EnableInstanceManagementReq(%+v)", *p)

}

func (p *EnableInstanceManagementReq) DeepEqual(ano *EnableInstanceManagementReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DBAUser) {
		return false
	}
	if !p.Field4DeepEqual(ano.OwnerUser) {
		return false
	}
	if !p.Field5DeepEqual(ano.Account) {
		return false
	}
	if !p.Field6DeepEqual(ano.Password) {
		return false
	}
	if !p.Field7DeepEqual(ano.Source) {
		return false
	}
	if !p.Field8DeepEqual(ano.SecurityRuleId) {
		return false
	}
	if !p.Field9DeepEqual(ano.ApprovalConfigId) {
		return false
	}
	if !p.Field10DeepEqual(ano.ConfigList) {
		return false
	}
	return true
}

func (p *EnableInstanceManagementReq) Field1DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *EnableInstanceManagementReq) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *EnableInstanceManagementReq) Field3DeepEqual(src *string) bool {

	if p.DBAUser == src {
		return true
	} else if p.DBAUser == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBAUser, *src) != 0 {
		return false
	}
	return true
}
func (p *EnableInstanceManagementReq) Field4DeepEqual(src *string) bool {

	if p.OwnerUser == src {
		return true
	} else if p.OwnerUser == nil || src == nil {
		return false
	}
	if strings.Compare(*p.OwnerUser, *src) != 0 {
		return false
	}
	return true
}
func (p *EnableInstanceManagementReq) Field5DeepEqual(src *string) bool {

	if p.Account == src {
		return true
	} else if p.Account == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Account, *src) != 0 {
		return false
	}
	return true
}
func (p *EnableInstanceManagementReq) Field6DeepEqual(src *string) bool {

	if p.Password == src {
		return true
	} else if p.Password == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Password, *src) != 0 {
		return false
	}
	return true
}
func (p *EnableInstanceManagementReq) Field7DeepEqual(src *LinkType) bool {

	if p.Source == src {
		return true
	} else if p.Source == nil || src == nil {
		return false
	}
	if *p.Source != *src {
		return false
	}
	return true
}
func (p *EnableInstanceManagementReq) Field8DeepEqual(src *string) bool {

	if p.SecurityRuleId == src {
		return true
	} else if p.SecurityRuleId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SecurityRuleId, *src) != 0 {
		return false
	}
	return true
}
func (p *EnableInstanceManagementReq) Field9DeepEqual(src *string) bool {

	if p.ApprovalConfigId == src {
		return true
	} else if p.ApprovalConfigId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ApprovalConfigId, *src) != 0 {
		return false
	}
	return true
}
func (p *EnableInstanceManagementReq) Field10DeepEqual(src []*InstanceManagementConfig) bool {

	if len(p.ConfigList) != len(src) {
		return false
	}
	for i, v := range p.ConfigList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type EnableInstanceManagementResp struct {
}

func NewEnableInstanceManagementResp() *EnableInstanceManagementResp {
	return &EnableInstanceManagementResp{}
}

func (p *EnableInstanceManagementResp) InitDefault() {
}

var fieldIDToName_EnableInstanceManagementResp = map[int16]string{}

func (p *EnableInstanceManagementResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EnableInstanceManagementResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *EnableInstanceManagementResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EnableInstanceManagementResp")

	if err = oprot.WriteStructBegin("EnableInstanceManagementResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *EnableInstanceManagementResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EnableInstanceManagementResp(%+v)", *p)

}

func (p *EnableInstanceManagementResp) DeepEqual(ano *EnableInstanceManagementResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type UpdateInstanceManagementConfigReq struct {
	InstanceType     *DSType                     `thrift:"InstanceType,1,optional" frugal:"1,optional,DSType" json:"InstanceType,omitempty"`
	InstanceId       *string                     `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
	DBAUser          *string                     `thrift:"DBAUser,3,optional" frugal:"3,optional,string" json:"DBAUser,omitempty"`
	OwnerUser        *string                     `thrift:"OwnerUser,4,optional" frugal:"4,optional,string" json:"OwnerUser,omitempty"`
	Account          *string                     `thrift:"Account,5,optional" frugal:"5,optional,string" json:"Account,omitempty"`
	Password         *string                     `thrift:"Password,6,optional" frugal:"6,optional,string" json:"Password,omitempty"`
	Source           *LinkType                   `thrift:"Source,7,optional" frugal:"7,optional,LinkType" json:"Source,omitempty"`
	SecurityRuleId   *string                     `thrift:"SecurityRuleId,8,optional" frugal:"8,optional,string" json:"SecurityRuleId,omitempty"`
	ApprovalConfigId *string                     `thrift:"ApprovalConfigId,9,optional" frugal:"9,optional,string" json:"ApprovalConfigId,omitempty"`
	ConfigList       []*InstanceManagementConfig `thrift:"ConfigList,10,optional" frugal:"10,optional,list<InstanceManagementConfig>" json:"ConfigList,omitempty"`
}

func NewUpdateInstanceManagementConfigReq() *UpdateInstanceManagementConfigReq {
	return &UpdateInstanceManagementConfigReq{}
}

func (p *UpdateInstanceManagementConfigReq) InitDefault() {
}

var UpdateInstanceManagementConfigReq_InstanceType_DEFAULT DSType

func (p *UpdateInstanceManagementConfigReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return UpdateInstanceManagementConfigReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var UpdateInstanceManagementConfigReq_InstanceId_DEFAULT string

func (p *UpdateInstanceManagementConfigReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return UpdateInstanceManagementConfigReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var UpdateInstanceManagementConfigReq_DBAUser_DEFAULT string

func (p *UpdateInstanceManagementConfigReq) GetDBAUser() (v string) {
	if !p.IsSetDBAUser() {
		return UpdateInstanceManagementConfigReq_DBAUser_DEFAULT
	}
	return *p.DBAUser
}

var UpdateInstanceManagementConfigReq_OwnerUser_DEFAULT string

func (p *UpdateInstanceManagementConfigReq) GetOwnerUser() (v string) {
	if !p.IsSetOwnerUser() {
		return UpdateInstanceManagementConfigReq_OwnerUser_DEFAULT
	}
	return *p.OwnerUser
}

var UpdateInstanceManagementConfigReq_Account_DEFAULT string

func (p *UpdateInstanceManagementConfigReq) GetAccount() (v string) {
	if !p.IsSetAccount() {
		return UpdateInstanceManagementConfigReq_Account_DEFAULT
	}
	return *p.Account
}

var UpdateInstanceManagementConfigReq_Password_DEFAULT string

func (p *UpdateInstanceManagementConfigReq) GetPassword() (v string) {
	if !p.IsSetPassword() {
		return UpdateInstanceManagementConfigReq_Password_DEFAULT
	}
	return *p.Password
}

var UpdateInstanceManagementConfigReq_Source_DEFAULT LinkType

func (p *UpdateInstanceManagementConfigReq) GetSource() (v LinkType) {
	if !p.IsSetSource() {
		return UpdateInstanceManagementConfigReq_Source_DEFAULT
	}
	return *p.Source
}

var UpdateInstanceManagementConfigReq_SecurityRuleId_DEFAULT string

func (p *UpdateInstanceManagementConfigReq) GetSecurityRuleId() (v string) {
	if !p.IsSetSecurityRuleId() {
		return UpdateInstanceManagementConfigReq_SecurityRuleId_DEFAULT
	}
	return *p.SecurityRuleId
}

var UpdateInstanceManagementConfigReq_ApprovalConfigId_DEFAULT string

func (p *UpdateInstanceManagementConfigReq) GetApprovalConfigId() (v string) {
	if !p.IsSetApprovalConfigId() {
		return UpdateInstanceManagementConfigReq_ApprovalConfigId_DEFAULT
	}
	return *p.ApprovalConfigId
}

var UpdateInstanceManagementConfigReq_ConfigList_DEFAULT []*InstanceManagementConfig

func (p *UpdateInstanceManagementConfigReq) GetConfigList() (v []*InstanceManagementConfig) {
	if !p.IsSetConfigList() {
		return UpdateInstanceManagementConfigReq_ConfigList_DEFAULT
	}
	return p.ConfigList
}
func (p *UpdateInstanceManagementConfigReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *UpdateInstanceManagementConfigReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *UpdateInstanceManagementConfigReq) SetDBAUser(val *string) {
	p.DBAUser = val
}
func (p *UpdateInstanceManagementConfigReq) SetOwnerUser(val *string) {
	p.OwnerUser = val
}
func (p *UpdateInstanceManagementConfigReq) SetAccount(val *string) {
	p.Account = val
}
func (p *UpdateInstanceManagementConfigReq) SetPassword(val *string) {
	p.Password = val
}
func (p *UpdateInstanceManagementConfigReq) SetSource(val *LinkType) {
	p.Source = val
}
func (p *UpdateInstanceManagementConfigReq) SetSecurityRuleId(val *string) {
	p.SecurityRuleId = val
}
func (p *UpdateInstanceManagementConfigReq) SetApprovalConfigId(val *string) {
	p.ApprovalConfigId = val
}
func (p *UpdateInstanceManagementConfigReq) SetConfigList(val []*InstanceManagementConfig) {
	p.ConfigList = val
}

var fieldIDToName_UpdateInstanceManagementConfigReq = map[int16]string{
	1:  "InstanceType",
	2:  "InstanceId",
	3:  "DBAUser",
	4:  "OwnerUser",
	5:  "Account",
	6:  "Password",
	7:  "Source",
	8:  "SecurityRuleId",
	9:  "ApprovalConfigId",
	10: "ConfigList",
}

func (p *UpdateInstanceManagementConfigReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *UpdateInstanceManagementConfigReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *UpdateInstanceManagementConfigReq) IsSetDBAUser() bool {
	return p.DBAUser != nil
}

func (p *UpdateInstanceManagementConfigReq) IsSetOwnerUser() bool {
	return p.OwnerUser != nil
}

func (p *UpdateInstanceManagementConfigReq) IsSetAccount() bool {
	return p.Account != nil
}

func (p *UpdateInstanceManagementConfigReq) IsSetPassword() bool {
	return p.Password != nil
}

func (p *UpdateInstanceManagementConfigReq) IsSetSource() bool {
	return p.Source != nil
}

func (p *UpdateInstanceManagementConfigReq) IsSetSecurityRuleId() bool {
	return p.SecurityRuleId != nil
}

func (p *UpdateInstanceManagementConfigReq) IsSetApprovalConfigId() bool {
	return p.ApprovalConfigId != nil
}

func (p *UpdateInstanceManagementConfigReq) IsSetConfigList() bool {
	return p.ConfigList != nil
}

func (p *UpdateInstanceManagementConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateInstanceManagementConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateInstanceManagementConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *UpdateInstanceManagementConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *UpdateInstanceManagementConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBAUser = _field
	return nil
}
func (p *UpdateInstanceManagementConfigReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OwnerUser = _field
	return nil
}
func (p *UpdateInstanceManagementConfigReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Account = _field
	return nil
}
func (p *UpdateInstanceManagementConfigReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Password = _field
	return nil
}
func (p *UpdateInstanceManagementConfigReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.Source = _field
	return nil
}
func (p *UpdateInstanceManagementConfigReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SecurityRuleId = _field
	return nil
}
func (p *UpdateInstanceManagementConfigReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ApprovalConfigId = _field
	return nil
}
func (p *UpdateInstanceManagementConfigReq) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceManagementConfig, 0, size)
	values := make([]InstanceManagementConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ConfigList = _field
	return nil
}

func (p *UpdateInstanceManagementConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateInstanceManagementConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateInstanceManagementConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBAUser() {
		if err = oprot.WriteFieldBegin("DBAUser", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.DBAUser); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetOwnerUser() {
		if err = oprot.WriteFieldBegin("OwnerUser", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OwnerUser); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccount() {
		if err = oprot.WriteFieldBegin("Account", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Account); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPassword() {
		if err = oprot.WriteFieldBegin("Password", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Password); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSource() {
		if err = oprot.WriteFieldBegin("Source", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Source)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecurityRuleId() {
		if err = oprot.WriteFieldBegin("SecurityRuleId", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SecurityRuleId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetApprovalConfigId() {
		if err = oprot.WriteFieldBegin("ApprovalConfigId", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ApprovalConfigId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetConfigList() {
		if err = oprot.WriteFieldBegin("ConfigList", thrift.LIST, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ConfigList)); err != nil {
			return err
		}
		for _, v := range p.ConfigList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateInstanceManagementConfigReq(%+v)", *p)

}

func (p *UpdateInstanceManagementConfigReq) DeepEqual(ano *UpdateInstanceManagementConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.DBAUser) {
		return false
	}
	if !p.Field4DeepEqual(ano.OwnerUser) {
		return false
	}
	if !p.Field5DeepEqual(ano.Account) {
		return false
	}
	if !p.Field6DeepEqual(ano.Password) {
		return false
	}
	if !p.Field7DeepEqual(ano.Source) {
		return false
	}
	if !p.Field8DeepEqual(ano.SecurityRuleId) {
		return false
	}
	if !p.Field9DeepEqual(ano.ApprovalConfigId) {
		return false
	}
	if !p.Field10DeepEqual(ano.ConfigList) {
		return false
	}
	return true
}

func (p *UpdateInstanceManagementConfigReq) Field1DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *UpdateInstanceManagementConfigReq) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateInstanceManagementConfigReq) Field3DeepEqual(src *string) bool {

	if p.DBAUser == src {
		return true
	} else if p.DBAUser == nil || src == nil {
		return false
	}
	if strings.Compare(*p.DBAUser, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateInstanceManagementConfigReq) Field4DeepEqual(src *string) bool {

	if p.OwnerUser == src {
		return true
	} else if p.OwnerUser == nil || src == nil {
		return false
	}
	if strings.Compare(*p.OwnerUser, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateInstanceManagementConfigReq) Field5DeepEqual(src *string) bool {

	if p.Account == src {
		return true
	} else if p.Account == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Account, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateInstanceManagementConfigReq) Field6DeepEqual(src *string) bool {

	if p.Password == src {
		return true
	} else if p.Password == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Password, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateInstanceManagementConfigReq) Field7DeepEqual(src *LinkType) bool {

	if p.Source == src {
		return true
	} else if p.Source == nil || src == nil {
		return false
	}
	if *p.Source != *src {
		return false
	}
	return true
}
func (p *UpdateInstanceManagementConfigReq) Field8DeepEqual(src *string) bool {

	if p.SecurityRuleId == src {
		return true
	} else if p.SecurityRuleId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SecurityRuleId, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateInstanceManagementConfigReq) Field9DeepEqual(src *string) bool {

	if p.ApprovalConfigId == src {
		return true
	} else if p.ApprovalConfigId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ApprovalConfigId, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateInstanceManagementConfigReq) Field10DeepEqual(src []*InstanceManagementConfig) bool {

	if len(p.ConfigList) != len(src) {
		return false
	}
	for i, v := range p.ConfigList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type UpdateInstanceManagementConfigResp struct {
}

func NewUpdateInstanceManagementConfigResp() *UpdateInstanceManagementConfigResp {
	return &UpdateInstanceManagementConfigResp{}
}

func (p *UpdateInstanceManagementConfigResp) InitDefault() {
}

var fieldIDToName_UpdateInstanceManagementConfigResp = map[int16]string{}

func (p *UpdateInstanceManagementConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateInstanceManagementConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateInstanceManagementConfigResp")

	if err = oprot.WriteStructBegin("UpdateInstanceManagementConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateInstanceManagementConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateInstanceManagementConfigResp(%+v)", *p)

}

func (p *UpdateInstanceManagementConfigResp) DeepEqual(ano *UpdateInstanceManagementConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DisableInstanceManagementReq struct {
	InstanceType *DSType                     `thrift:"InstanceType,1,optional" frugal:"1,optional,DSType" json:"InstanceType,omitempty"`
	InstanceId   *string                     `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
	Source       *LinkType                   `thrift:"Source,3,optional" frugal:"3,optional,LinkType" json:"Source,omitempty"`
	DetailList   []*InstanceManagementDetail `thrift:"DetailList,4,optional" frugal:"4,optional,list<InstanceManagementDetail>" json:"DetailList,omitempty"`
}

func NewDisableInstanceManagementReq() *DisableInstanceManagementReq {
	return &DisableInstanceManagementReq{}
}

func (p *DisableInstanceManagementReq) InitDefault() {
}

var DisableInstanceManagementReq_InstanceType_DEFAULT DSType

func (p *DisableInstanceManagementReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DisableInstanceManagementReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DisableInstanceManagementReq_InstanceId_DEFAULT string

func (p *DisableInstanceManagementReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DisableInstanceManagementReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DisableInstanceManagementReq_Source_DEFAULT LinkType

func (p *DisableInstanceManagementReq) GetSource() (v LinkType) {
	if !p.IsSetSource() {
		return DisableInstanceManagementReq_Source_DEFAULT
	}
	return *p.Source
}

var DisableInstanceManagementReq_DetailList_DEFAULT []*InstanceManagementDetail

func (p *DisableInstanceManagementReq) GetDetailList() (v []*InstanceManagementDetail) {
	if !p.IsSetDetailList() {
		return DisableInstanceManagementReq_DetailList_DEFAULT
	}
	return p.DetailList
}
func (p *DisableInstanceManagementReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DisableInstanceManagementReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DisableInstanceManagementReq) SetSource(val *LinkType) {
	p.Source = val
}
func (p *DisableInstanceManagementReq) SetDetailList(val []*InstanceManagementDetail) {
	p.DetailList = val
}

var fieldIDToName_DisableInstanceManagementReq = map[int16]string{
	1: "InstanceType",
	2: "InstanceId",
	3: "Source",
	4: "DetailList",
}

func (p *DisableInstanceManagementReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DisableInstanceManagementReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DisableInstanceManagementReq) IsSetSource() bool {
	return p.Source != nil
}

func (p *DisableInstanceManagementReq) IsSetDetailList() bool {
	return p.DetailList != nil
}

func (p *DisableInstanceManagementReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisableInstanceManagementReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DisableInstanceManagementReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DisableInstanceManagementReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DisableInstanceManagementReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DisableInstanceManagementReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.Source = _field
	return nil
}
func (p *DisableInstanceManagementReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InstanceManagementDetail, 0, size)
	values := make([]InstanceManagementDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DetailList = _field
	return nil
}

func (p *DisableInstanceManagementReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisableInstanceManagementReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DisableInstanceManagementReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DisableInstanceManagementReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DisableInstanceManagementReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DisableInstanceManagementReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSource() {
		if err = oprot.WriteFieldBegin("Source", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Source)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DisableInstanceManagementReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDetailList() {
		if err = oprot.WriteFieldBegin("DetailList", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DetailList)); err != nil {
			return err
		}
		for _, v := range p.DetailList {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DisableInstanceManagementReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DisableInstanceManagementReq(%+v)", *p)

}

func (p *DisableInstanceManagementReq) DeepEqual(ano *DisableInstanceManagementReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.Source) {
		return false
	}
	if !p.Field4DeepEqual(ano.DetailList) {
		return false
	}
	return true
}

func (p *DisableInstanceManagementReq) Field1DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DisableInstanceManagementReq) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DisableInstanceManagementReq) Field3DeepEqual(src *LinkType) bool {

	if p.Source == src {
		return true
	} else if p.Source == nil || src == nil {
		return false
	}
	if *p.Source != *src {
		return false
	}
	return true
}
func (p *DisableInstanceManagementReq) Field4DeepEqual(src []*InstanceManagementDetail) bool {

	if len(p.DetailList) != len(src) {
		return false
	}
	for i, v := range p.DetailList {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DisableInstanceManagementResp struct {
}

func NewDisableInstanceManagementResp() *DisableInstanceManagementResp {
	return &DisableInstanceManagementResp{}
}

func (p *DisableInstanceManagementResp) InitDefault() {
}

var fieldIDToName_DisableInstanceManagementResp = map[int16]string{}

func (p *DisableInstanceManagementResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisableInstanceManagementResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DisableInstanceManagementResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisableInstanceManagementResp")

	if err = oprot.WriteStructBegin("DisableInstanceManagementResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DisableInstanceManagementResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DisableInstanceManagementResp(%+v)", *p)

}

func (p *DisableInstanceManagementResp) DeepEqual(ano *DisableInstanceManagementResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type RegisterDBInstanceReq struct {
	InstanceType   InstanceType      `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	LinkType       LinkType          `thrift:"LinkType,2,required" frugal:"2,required,LinkType" json:"LinkType"`
	Address        string            `thrift:"Address,3,required" frugal:"3,required,string" json:"Address"`
	UserName       string            `thrift:"UserName,4,required" frugal:"4,required,string" json:"UserName"`
	Password       string            `thrift:"Password,5,required" frugal:"5,required,string" json:"Password"`
	InstanceName   string            `thrift:"InstanceName,6,required" frugal:"6,required,string" json:"InstanceName"`
	InstanceId     *string           `thrift:"InstanceId,7,optional" frugal:"7,optional,string" json:"InstanceId,omitempty"`
	Region         string            `thrift:"Region,8,required" frugal:"8,required,string" json:"Region"`
	SecurityConfig *SecurityConfig   `thrift:"SecurityConfig,9,optional" frugal:"9,optional,SecurityConfig" json:"SecurityConfig,omitempty"`
	Extra          map[string]string `thrift:"Extra,10,optional" frugal:"10,optional,map<string:string>" json:"Extra,omitempty"`
}

func NewRegisterDBInstanceReq() *RegisterDBInstanceReq {
	return &RegisterDBInstanceReq{}
}

func (p *RegisterDBInstanceReq) InitDefault() {
}

func (p *RegisterDBInstanceReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *RegisterDBInstanceReq) GetLinkType() (v LinkType) {
	return p.LinkType
}

func (p *RegisterDBInstanceReq) GetAddress() (v string) {
	return p.Address
}

func (p *RegisterDBInstanceReq) GetUserName() (v string) {
	return p.UserName
}

func (p *RegisterDBInstanceReq) GetPassword() (v string) {
	return p.Password
}

func (p *RegisterDBInstanceReq) GetInstanceName() (v string) {
	return p.InstanceName
}

var RegisterDBInstanceReq_InstanceId_DEFAULT string

func (p *RegisterDBInstanceReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return RegisterDBInstanceReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

func (p *RegisterDBInstanceReq) GetRegion() (v string) {
	return p.Region
}

var RegisterDBInstanceReq_SecurityConfig_DEFAULT *SecurityConfig

func (p *RegisterDBInstanceReq) GetSecurityConfig() (v *SecurityConfig) {
	if !p.IsSetSecurityConfig() {
		return RegisterDBInstanceReq_SecurityConfig_DEFAULT
	}
	return p.SecurityConfig
}

var RegisterDBInstanceReq_Extra_DEFAULT map[string]string

func (p *RegisterDBInstanceReq) GetExtra() (v map[string]string) {
	if !p.IsSetExtra() {
		return RegisterDBInstanceReq_Extra_DEFAULT
	}
	return p.Extra
}
func (p *RegisterDBInstanceReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *RegisterDBInstanceReq) SetLinkType(val LinkType) {
	p.LinkType = val
}
func (p *RegisterDBInstanceReq) SetAddress(val string) {
	p.Address = val
}
func (p *RegisterDBInstanceReq) SetUserName(val string) {
	p.UserName = val
}
func (p *RegisterDBInstanceReq) SetPassword(val string) {
	p.Password = val
}
func (p *RegisterDBInstanceReq) SetInstanceName(val string) {
	p.InstanceName = val
}
func (p *RegisterDBInstanceReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *RegisterDBInstanceReq) SetRegion(val string) {
	p.Region = val
}
func (p *RegisterDBInstanceReq) SetSecurityConfig(val *SecurityConfig) {
	p.SecurityConfig = val
}
func (p *RegisterDBInstanceReq) SetExtra(val map[string]string) {
	p.Extra = val
}

var fieldIDToName_RegisterDBInstanceReq = map[int16]string{
	1:  "InstanceType",
	2:  "LinkType",
	3:  "Address",
	4:  "UserName",
	5:  "Password",
	6:  "InstanceName",
	7:  "InstanceId",
	8:  "Region",
	9:  "SecurityConfig",
	10: "Extra",
}

func (p *RegisterDBInstanceReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *RegisterDBInstanceReq) IsSetSecurityConfig() bool {
	return p.SecurityConfig != nil
}

func (p *RegisterDBInstanceReq) IsSetExtra() bool {
	return p.Extra != nil
}

func (p *RegisterDBInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RegisterDBInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetLinkType bool = false
	var issetAddress bool = false
	var issetUserName bool = false
	var issetPassword bool = false
	var issetInstanceName bool = false
	var issetRegion bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetLinkType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAddress = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetUserName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetLinkType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAddress {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetUserName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPassword {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetInstanceName {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetRegion {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RegisterDBInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RegisterDBInstanceReq[fieldId]))
}

func (p *RegisterDBInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *RegisterDBInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = LinkType(v)
	}
	p.LinkType = _field
	return nil
}
func (p *RegisterDBInstanceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Address = _field
	return nil
}
func (p *RegisterDBInstanceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserName = _field
	return nil
}
func (p *RegisterDBInstanceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Password = _field
	return nil
}
func (p *RegisterDBInstanceReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceName = _field
	return nil
}
func (p *RegisterDBInstanceReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *RegisterDBInstanceReq) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Region = _field
	return nil
}
func (p *RegisterDBInstanceReq) ReadField9(iprot thrift.TProtocol) error {
	_field := NewSecurityConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SecurityConfig = _field
	return nil
}
func (p *RegisterDBInstanceReq) ReadField10(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return err
	}
	_field := make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_key = v
		}

		var _val string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_val = v
		}

		_field[_key] = _val
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return err
	}
	p.Extra = _field
	return nil
}

func (p *RegisterDBInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RegisterDBInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RegisterDBInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RegisterDBInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RegisterDBInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.LinkType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RegisterDBInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Address", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Address); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RegisterDBInstanceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UserName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RegisterDBInstanceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Password", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Password); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *RegisterDBInstanceReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *RegisterDBInstanceReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *RegisterDBInstanceReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Region", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Region); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *RegisterDBInstanceReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSecurityConfig() {
		if err = oprot.WriteFieldBegin("SecurityConfig", thrift.STRUCT, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SecurityConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *RegisterDBInstanceReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetExtra() {
		if err = oprot.WriteFieldBegin("Extra", thrift.MAP, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Extra)); err != nil {
			return err
		}
		for k, v := range p.Extra {
			if err := oprot.WriteString(k); err != nil {
				return err
			}
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *RegisterDBInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterDBInstanceReq(%+v)", *p)

}

func (p *RegisterDBInstanceReq) DeepEqual(ano *RegisterDBInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.LinkType) {
		return false
	}
	if !p.Field3DeepEqual(ano.Address) {
		return false
	}
	if !p.Field4DeepEqual(ano.UserName) {
		return false
	}
	if !p.Field5DeepEqual(ano.Password) {
		return false
	}
	if !p.Field6DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field7DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field8DeepEqual(ano.Region) {
		return false
	}
	if !p.Field9DeepEqual(ano.SecurityConfig) {
		return false
	}
	if !p.Field10DeepEqual(ano.Extra) {
		return false
	}
	return true
}

func (p *RegisterDBInstanceReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *RegisterDBInstanceReq) Field2DeepEqual(src LinkType) bool {

	if p.LinkType != src {
		return false
	}
	return true
}
func (p *RegisterDBInstanceReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Address, src) != 0 {
		return false
	}
	return true
}
func (p *RegisterDBInstanceReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.UserName, src) != 0 {
		return false
	}
	return true
}
func (p *RegisterDBInstanceReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Password, src) != 0 {
		return false
	}
	return true
}
func (p *RegisterDBInstanceReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.InstanceName, src) != 0 {
		return false
	}
	return true
}
func (p *RegisterDBInstanceReq) Field7DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *RegisterDBInstanceReq) Field8DeepEqual(src string) bool {

	if strings.Compare(p.Region, src) != 0 {
		return false
	}
	return true
}
func (p *RegisterDBInstanceReq) Field9DeepEqual(src *SecurityConfig) bool {

	if !p.SecurityConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *RegisterDBInstanceReq) Field10DeepEqual(src map[string]string) bool {

	if len(p.Extra) != len(src) {
		return false
	}
	for k, v := range p.Extra {
		_src := src[k]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type SecurityConfig struct {
	DBA                  string `thrift:"DBA,1,required" frugal:"1,required,string" json:"DBA"`
	Owner                string `thrift:"Owner,2,required" frugal:"2,required,string" json:"Owner"`
	SecurityRuleId       string `thrift:"SecurityRuleId,3,required" frugal:"3,required,string" json:"SecurityRuleId"`
	ApprovalFlowConfigId string `thrift:"ApprovalFlowConfigId,4,required" frugal:"4,required,string" json:"ApprovalFlowConfigId"`
}

func NewSecurityConfig() *SecurityConfig {
	return &SecurityConfig{}
}

func (p *SecurityConfig) InitDefault() {
}

func (p *SecurityConfig) GetDBA() (v string) {
	return p.DBA
}

func (p *SecurityConfig) GetOwner() (v string) {
	return p.Owner
}

func (p *SecurityConfig) GetSecurityRuleId() (v string) {
	return p.SecurityRuleId
}

func (p *SecurityConfig) GetApprovalFlowConfigId() (v string) {
	return p.ApprovalFlowConfigId
}
func (p *SecurityConfig) SetDBA(val string) {
	p.DBA = val
}
func (p *SecurityConfig) SetOwner(val string) {
	p.Owner = val
}
func (p *SecurityConfig) SetSecurityRuleId(val string) {
	p.SecurityRuleId = val
}
func (p *SecurityConfig) SetApprovalFlowConfigId(val string) {
	p.ApprovalFlowConfigId = val
}

var fieldIDToName_SecurityConfig = map[int16]string{
	1: "DBA",
	2: "Owner",
	3: "SecurityRuleId",
	4: "ApprovalFlowConfigId",
}

func (p *SecurityConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SecurityConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDBA bool = false
	var issetOwner bool = false
	var issetSecurityRuleId bool = false
	var issetApprovalFlowConfigId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBA = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetOwner = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetSecurityRuleId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetApprovalFlowConfigId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDBA {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetOwner {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetSecurityRuleId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetApprovalFlowConfigId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SecurityConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SecurityConfig[fieldId]))
}

func (p *SecurityConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBA = _field
	return nil
}
func (p *SecurityConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Owner = _field
	return nil
}
func (p *SecurityConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SecurityRuleId = _field
	return nil
}
func (p *SecurityConfig) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ApprovalFlowConfigId = _field
	return nil
}

func (p *SecurityConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SecurityConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("SecurityConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SecurityConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBA", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBA); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SecurityConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Owner", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Owner); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SecurityConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SecurityRuleId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SecurityRuleId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SecurityConfig) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ApprovalFlowConfigId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ApprovalFlowConfigId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SecurityConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SecurityConfig(%+v)", *p)

}

func (p *SecurityConfig) DeepEqual(ano *SecurityConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DBA) {
		return false
	}
	if !p.Field2DeepEqual(ano.Owner) {
		return false
	}
	if !p.Field3DeepEqual(ano.SecurityRuleId) {
		return false
	}
	if !p.Field4DeepEqual(ano.ApprovalFlowConfigId) {
		return false
	}
	return true
}

func (p *SecurityConfig) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DBA, src) != 0 {
		return false
	}
	return true
}
func (p *SecurityConfig) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Owner, src) != 0 {
		return false
	}
	return true
}
func (p *SecurityConfig) Field3DeepEqual(src string) bool {

	if strings.Compare(p.SecurityRuleId, src) != 0 {
		return false
	}
	return true
}
func (p *SecurityConfig) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ApprovalFlowConfigId, src) != 0 {
		return false
	}
	return true
}

type RegisterDBInstanceResp struct {
}

func NewRegisterDBInstanceResp() *RegisterDBInstanceResp {
	return &RegisterDBInstanceResp{}
}

func (p *RegisterDBInstanceResp) InitDefault() {
}

var fieldIDToName_RegisterDBInstanceResp = map[int16]string{}

func (p *RegisterDBInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RegisterDBInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RegisterDBInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RegisterDBInstanceResp")

	if err = oprot.WriteStructBegin("RegisterDBInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RegisterDBInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterDBInstanceResp(%+v)", *p)

}

func (p *RegisterDBInstanceResp) DeepEqual(ano *RegisterDBInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteDBInstanceReq struct {
	InstanceId   string       `thrift:"instanceId,1,required" frugal:"1,required,string" json:"instanceId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	LinkType     LinkType     `thrift:"LinkType,3,required" frugal:"3,required,LinkType" json:"LinkType"`
}

func NewDeleteDBInstanceReq() *DeleteDBInstanceReq {
	return &DeleteDBInstanceReq{}
}

func (p *DeleteDBInstanceReq) InitDefault() {
}

func (p *DeleteDBInstanceReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DeleteDBInstanceReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DeleteDBInstanceReq) GetLinkType() (v LinkType) {
	return p.LinkType
}
func (p *DeleteDBInstanceReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteDBInstanceReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DeleteDBInstanceReq) SetLinkType(val LinkType) {
	p.LinkType = val
}

var fieldIDToName_DeleteDBInstanceReq = map[int16]string{
	1: "instanceId",
	2: "InstanceType",
	3: "LinkType",
}

func (p *DeleteDBInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetLinkType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetLinkType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetLinkType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteDBInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteDBInstanceReq[fieldId]))
}

func (p *DeleteDBInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteDBInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DeleteDBInstanceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = LinkType(v)
	}
	p.LinkType = _field
	return nil
}

func (p *DeleteDBInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDBInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDBInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("instanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteDBInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteDBInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.LinkType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DeleteDBInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDBInstanceReq(%+v)", *p)

}

func (p *DeleteDBInstanceReq) DeepEqual(ano *DeleteDBInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.LinkType) {
		return false
	}
	return true
}

func (p *DeleteDBInstanceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteDBInstanceReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DeleteDBInstanceReq) Field3DeepEqual(src LinkType) bool {

	if p.LinkType != src {
		return false
	}
	return true
}

type DeleteDBInstanceResp struct {
}

func NewDeleteDBInstanceResp() *DeleteDBInstanceResp {
	return &DeleteDBInstanceResp{}
}

func (p *DeleteDBInstanceResp) InitDefault() {
}

var fieldIDToName_DeleteDBInstanceResp = map[int16]string{}

func (p *DeleteDBInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteDBInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteDBInstanceResp")

	if err = oprot.WriteStructBegin("DeleteDBInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteDBInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteDBInstanceResp(%+v)", *p)

}

func (p *DeleteDBInstanceResp) DeepEqual(ano *DeleteDBInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeRecentlyUsedDBInstancesReq struct {
	InstanceType *InstanceType `thrift:"InstanceType,1,optional" frugal:"1,optional,InstanceType" json:"InstanceType,omitempty"`
	LinkType     *LinkType     `thrift:"LinkType,2,optional" frugal:"2,optional,LinkType" json:"LinkType,omitempty"`
	RegionId     *string       `thrift:"RegionId,3,optional" frugal:"3,optional,string" json:"RegionId,omitempty"`
	Keyword      *string       `thrift:"Keyword,4,optional" frugal:"4,optional,string" json:"Keyword,omitempty"`
	SortBy       *SortBy       `thrift:"SortBy,5,optional" frugal:"5,optional,SortBy" json:"SortBy,omitempty"`
	PageNumber   *int32        `thrift:"PageNumber,6,optional" frugal:"6,optional,i32" json:"PageNumber,omitempty"`
	PageSize     *int32        `thrift:"PageSize,7,optional" frugal:"7,optional,i32" json:"PageSize,omitempty"`
}

func NewDescribeRecentlyUsedDBInstancesReq() *DescribeRecentlyUsedDBInstancesReq {
	return &DescribeRecentlyUsedDBInstancesReq{}
}

func (p *DescribeRecentlyUsedDBInstancesReq) InitDefault() {
}

var DescribeRecentlyUsedDBInstancesReq_InstanceType_DEFAULT InstanceType

func (p *DescribeRecentlyUsedDBInstancesReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return DescribeRecentlyUsedDBInstancesReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeRecentlyUsedDBInstancesReq_LinkType_DEFAULT LinkType

func (p *DescribeRecentlyUsedDBInstancesReq) GetLinkType() (v LinkType) {
	if !p.IsSetLinkType() {
		return DescribeRecentlyUsedDBInstancesReq_LinkType_DEFAULT
	}
	return *p.LinkType
}

var DescribeRecentlyUsedDBInstancesReq_RegionId_DEFAULT string

func (p *DescribeRecentlyUsedDBInstancesReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeRecentlyUsedDBInstancesReq_RegionId_DEFAULT
	}
	return *p.RegionId
}

var DescribeRecentlyUsedDBInstancesReq_Keyword_DEFAULT string

func (p *DescribeRecentlyUsedDBInstancesReq) GetKeyword() (v string) {
	if !p.IsSetKeyword() {
		return DescribeRecentlyUsedDBInstancesReq_Keyword_DEFAULT
	}
	return *p.Keyword
}

var DescribeRecentlyUsedDBInstancesReq_SortBy_DEFAULT SortBy

func (p *DescribeRecentlyUsedDBInstancesReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeRecentlyUsedDBInstancesReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeRecentlyUsedDBInstancesReq_PageNumber_DEFAULT int32

func (p *DescribeRecentlyUsedDBInstancesReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeRecentlyUsedDBInstancesReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeRecentlyUsedDBInstancesReq_PageSize_DEFAULT int32

func (p *DescribeRecentlyUsedDBInstancesReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeRecentlyUsedDBInstancesReq_PageSize_DEFAULT
	}
	return *p.PageSize
}
func (p *DescribeRecentlyUsedDBInstancesReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *DescribeRecentlyUsedDBInstancesReq) SetLinkType(val *LinkType) {
	p.LinkType = val
}
func (p *DescribeRecentlyUsedDBInstancesReq) SetRegionId(val *string) {
	p.RegionId = val
}
func (p *DescribeRecentlyUsedDBInstancesReq) SetKeyword(val *string) {
	p.Keyword = val
}
func (p *DescribeRecentlyUsedDBInstancesReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeRecentlyUsedDBInstancesReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeRecentlyUsedDBInstancesReq) SetPageSize(val *int32) {
	p.PageSize = val
}

var fieldIDToName_DescribeRecentlyUsedDBInstancesReq = map[int16]string{
	1: "InstanceType",
	2: "LinkType",
	3: "RegionId",
	4: "Keyword",
	5: "SortBy",
	6: "PageNumber",
	7: "PageSize",
}

func (p *DescribeRecentlyUsedDBInstancesReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeRecentlyUsedDBInstancesReq) IsSetLinkType() bool {
	return p.LinkType != nil
}

func (p *DescribeRecentlyUsedDBInstancesReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeRecentlyUsedDBInstancesReq) IsSetKeyword() bool {
	return p.Keyword != nil
}

func (p *DescribeRecentlyUsedDBInstancesReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeRecentlyUsedDBInstancesReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeRecentlyUsedDBInstancesReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeRecentlyUsedDBInstancesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecentlyUsedDBInstancesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRecentlyUsedDBInstancesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeRecentlyUsedDBInstancesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return nil
}
func (p *DescribeRecentlyUsedDBInstancesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeRecentlyUsedDBInstancesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Keyword = _field
	return nil
}
func (p *DescribeRecentlyUsedDBInstancesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeRecentlyUsedDBInstancesReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeRecentlyUsedDBInstancesReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *DescribeRecentlyUsedDBInstancesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecentlyUsedDBInstancesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeRecentlyUsedDBInstancesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinkType() {
		if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeyword() {
		if err = oprot.WriteFieldBegin("Keyword", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Keyword); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeRecentlyUsedDBInstancesReq(%+v)", *p)

}

func (p *DescribeRecentlyUsedDBInstancesReq) DeepEqual(ano *DescribeRecentlyUsedDBInstancesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.LinkType) {
		return false
	}
	if !p.Field3DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field4DeepEqual(ano.Keyword) {
		return false
	}
	if !p.Field5DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageSize) {
		return false
	}
	return true
}

func (p *DescribeRecentlyUsedDBInstancesReq) Field1DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeRecentlyUsedDBInstancesReq) Field2DeepEqual(src *LinkType) bool {

	if p.LinkType == src {
		return true
	} else if p.LinkType == nil || src == nil {
		return false
	}
	if *p.LinkType != *src {
		return false
	}
	return true
}
func (p *DescribeRecentlyUsedDBInstancesReq) Field3DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeRecentlyUsedDBInstancesReq) Field4DeepEqual(src *string) bool {

	if p.Keyword == src {
		return true
	} else if p.Keyword == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Keyword, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeRecentlyUsedDBInstancesReq) Field5DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeRecentlyUsedDBInstancesReq) Field6DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeRecentlyUsedDBInstancesReq) Field7DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}

type DescribeRecentlyUsedDBInstancesResp struct {
	Instances []*UsedInstanceInfo `thrift:"Instances,1,required" frugal:"1,required,list<UsedInstanceInfo>" json:"Instances"`
	Total     int32               `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeRecentlyUsedDBInstancesResp() *DescribeRecentlyUsedDBInstancesResp {
	return &DescribeRecentlyUsedDBInstancesResp{}
}

func (p *DescribeRecentlyUsedDBInstancesResp) InitDefault() {
}

func (p *DescribeRecentlyUsedDBInstancesResp) GetInstances() (v []*UsedInstanceInfo) {
	return p.Instances
}

func (p *DescribeRecentlyUsedDBInstancesResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeRecentlyUsedDBInstancesResp) SetInstances(val []*UsedInstanceInfo) {
	p.Instances = val
}
func (p *DescribeRecentlyUsedDBInstancesResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeRecentlyUsedDBInstancesResp = map[int16]string{
	1: "Instances",
	2: "Total",
}

func (p *DescribeRecentlyUsedDBInstancesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecentlyUsedDBInstancesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstances bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstances = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstances {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeRecentlyUsedDBInstancesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeRecentlyUsedDBInstancesResp[fieldId]))
}

func (p *DescribeRecentlyUsedDBInstancesResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*UsedInstanceInfo, 0, size)
	values := make([]UsedInstanceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Instances = _field
	return nil
}
func (p *DescribeRecentlyUsedDBInstancesResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeRecentlyUsedDBInstancesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeRecentlyUsedDBInstancesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeRecentlyUsedDBInstancesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Instances", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Instances)); err != nil {
		return err
	}
	for _, v := range p.Instances {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeRecentlyUsedDBInstancesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeRecentlyUsedDBInstancesResp(%+v)", *p)

}

func (p *DescribeRecentlyUsedDBInstancesResp) DeepEqual(ano *DescribeRecentlyUsedDBInstancesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Instances) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeRecentlyUsedDBInstancesResp) Field1DeepEqual(src []*UsedInstanceInfo) bool {

	if len(p.Instances) != len(src) {
		return false
	}
	for i, v := range p.Instances {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeRecentlyUsedDBInstancesResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type UsedInstanceInfo struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceName string       `thrift:"InstanceName,2,required" frugal:"2,required,string" json:"InstanceName"`
	InstanceType InstanceType `thrift:"InstanceType,3,required" frugal:"3,required,InstanceType" json:"InstanceType"`
	LastTime     string       `thrift:"LastTime,4,required" frugal:"4,required,string" json:"LastTime"`
	UUID         string       `thrift:"UUID,5,required" frugal:"5,required,string" json:"UUID"`
	PsmList      []string     `thrift:"PsmList,6,optional" frugal:"6,optional,list<string>" json:"PsmList,omitempty"`
}

func NewUsedInstanceInfo() *UsedInstanceInfo {
	return &UsedInstanceInfo{}
}

func (p *UsedInstanceInfo) InitDefault() {
}

func (p *UsedInstanceInfo) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *UsedInstanceInfo) GetInstanceName() (v string) {
	return p.InstanceName
}

func (p *UsedInstanceInfo) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *UsedInstanceInfo) GetLastTime() (v string) {
	return p.LastTime
}

func (p *UsedInstanceInfo) GetUUID() (v string) {
	return p.UUID
}

var UsedInstanceInfo_PsmList_DEFAULT []string

func (p *UsedInstanceInfo) GetPsmList() (v []string) {
	if !p.IsSetPsmList() {
		return UsedInstanceInfo_PsmList_DEFAULT
	}
	return p.PsmList
}
func (p *UsedInstanceInfo) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *UsedInstanceInfo) SetInstanceName(val string) {
	p.InstanceName = val
}
func (p *UsedInstanceInfo) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *UsedInstanceInfo) SetLastTime(val string) {
	p.LastTime = val
}
func (p *UsedInstanceInfo) SetUUID(val string) {
	p.UUID = val
}
func (p *UsedInstanceInfo) SetPsmList(val []string) {
	p.PsmList = val
}

var fieldIDToName_UsedInstanceInfo = map[int16]string{
	1: "InstanceId",
	2: "InstanceName",
	3: "InstanceType",
	4: "LastTime",
	5: "UUID",
	6: "PsmList",
}

func (p *UsedInstanceInfo) IsSetPsmList() bool {
	return p.PsmList != nil
}

func (p *UsedInstanceInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UsedInstanceInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceName bool = false
	var issetInstanceType bool = false
	var issetLastTime bool = false
	var issetUUID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetUUID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetLastTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetUUID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UsedInstanceInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UsedInstanceInfo[fieldId]))
}

func (p *UsedInstanceInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *UsedInstanceInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceName = _field
	return nil
}
func (p *UsedInstanceInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *UsedInstanceInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LastTime = _field
	return nil
}
func (p *UsedInstanceInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UUID = _field
	return nil
}
func (p *UsedInstanceInfo) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PsmList = _field
	return nil
}

func (p *UsedInstanceInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UsedInstanceInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("UsedInstanceInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UsedInstanceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UsedInstanceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UsedInstanceInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UsedInstanceInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LastTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LastTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *UsedInstanceInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UUID", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UUID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *UsedInstanceInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPsmList() {
		if err = oprot.WriteFieldBegin("PsmList", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.PsmList)); err != nil {
			return err
		}
		for _, v := range p.PsmList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *UsedInstanceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UsedInstanceInfo(%+v)", *p)

}

func (p *UsedInstanceInfo) DeepEqual(ano *UsedInstanceInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.LastTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.UUID) {
		return false
	}
	if !p.Field6DeepEqual(ano.PsmList) {
		return false
	}
	return true
}

func (p *UsedInstanceInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *UsedInstanceInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceName, src) != 0 {
		return false
	}
	return true
}
func (p *UsedInstanceInfo) Field3DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *UsedInstanceInfo) Field4DeepEqual(src string) bool {

	if strings.Compare(p.LastTime, src) != 0 {
		return false
	}
	return true
}
func (p *UsedInstanceInfo) Field5DeepEqual(src string) bool {

	if strings.Compare(p.UUID, src) != 0 {
		return false
	}
	return true
}
func (p *UsedInstanceInfo) Field6DeepEqual(src []string) bool {

	if len(p.PsmList) != len(src) {
		return false
	}
	for i, v := range p.PsmList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type DeleteRecentlyUsedDBInstancesReq struct {
	UUID  *string `thrift:"UUID,1,optional" frugal:"1,optional,string" json:"UUID,omitempty"`
	IsAll *bool   `thrift:"IsAll,2,optional" frugal:"2,optional,bool" json:"IsAll,omitempty"`
}

func NewDeleteRecentlyUsedDBInstancesReq() *DeleteRecentlyUsedDBInstancesReq {
	return &DeleteRecentlyUsedDBInstancesReq{}
}

func (p *DeleteRecentlyUsedDBInstancesReq) InitDefault() {
}

var DeleteRecentlyUsedDBInstancesReq_UUID_DEFAULT string

func (p *DeleteRecentlyUsedDBInstancesReq) GetUUID() (v string) {
	if !p.IsSetUUID() {
		return DeleteRecentlyUsedDBInstancesReq_UUID_DEFAULT
	}
	return *p.UUID
}

var DeleteRecentlyUsedDBInstancesReq_IsAll_DEFAULT bool

func (p *DeleteRecentlyUsedDBInstancesReq) GetIsAll() (v bool) {
	if !p.IsSetIsAll() {
		return DeleteRecentlyUsedDBInstancesReq_IsAll_DEFAULT
	}
	return *p.IsAll
}
func (p *DeleteRecentlyUsedDBInstancesReq) SetUUID(val *string) {
	p.UUID = val
}
func (p *DeleteRecentlyUsedDBInstancesReq) SetIsAll(val *bool) {
	p.IsAll = val
}

var fieldIDToName_DeleteRecentlyUsedDBInstancesReq = map[int16]string{
	1: "UUID",
	2: "IsAll",
}

func (p *DeleteRecentlyUsedDBInstancesReq) IsSetUUID() bool {
	return p.UUID != nil
}

func (p *DeleteRecentlyUsedDBInstancesReq) IsSetIsAll() bool {
	return p.IsAll != nil
}

func (p *DeleteRecentlyUsedDBInstancesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteRecentlyUsedDBInstancesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteRecentlyUsedDBInstancesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteRecentlyUsedDBInstancesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UUID = _field
	return nil
}
func (p *DeleteRecentlyUsedDBInstancesReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsAll = _field
	return nil
}

func (p *DeleteRecentlyUsedDBInstancesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteRecentlyUsedDBInstancesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteRecentlyUsedDBInstancesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteRecentlyUsedDBInstancesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetUUID() {
		if err = oprot.WriteFieldBegin("UUID", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.UUID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteRecentlyUsedDBInstancesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsAll() {
		if err = oprot.WriteFieldBegin("IsAll", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsAll); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteRecentlyUsedDBInstancesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteRecentlyUsedDBInstancesReq(%+v)", *p)

}

func (p *DeleteRecentlyUsedDBInstancesReq) DeepEqual(ano *DeleteRecentlyUsedDBInstancesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.UUID) {
		return false
	}
	if !p.Field2DeepEqual(ano.IsAll) {
		return false
	}
	return true
}

func (p *DeleteRecentlyUsedDBInstancesReq) Field1DeepEqual(src *string) bool {

	if p.UUID == src {
		return true
	} else if p.UUID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.UUID, *src) != 0 {
		return false
	}
	return true
}
func (p *DeleteRecentlyUsedDBInstancesReq) Field2DeepEqual(src *bool) bool {

	if p.IsAll == src {
		return true
	} else if p.IsAll == nil || src == nil {
		return false
	}
	if *p.IsAll != *src {
		return false
	}
	return true
}

type DeleteRecentlyUsedDBInstancesResp struct {
}

func NewDeleteRecentlyUsedDBInstancesResp() *DeleteRecentlyUsedDBInstancesResp {
	return &DeleteRecentlyUsedDBInstancesResp{}
}

func (p *DeleteRecentlyUsedDBInstancesResp) InitDefault() {
}

var fieldIDToName_DeleteRecentlyUsedDBInstancesResp = map[int16]string{}

func (p *DeleteRecentlyUsedDBInstancesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteRecentlyUsedDBInstancesResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteRecentlyUsedDBInstancesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteRecentlyUsedDBInstancesResp")

	if err = oprot.WriteStructBegin("DeleteRecentlyUsedDBInstancesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteRecentlyUsedDBInstancesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteRecentlyUsedDBInstancesResp(%+v)", *p)

}

func (p *DeleteRecentlyUsedDBInstancesResp) DeepEqual(ano *DeleteRecentlyUsedDBInstancesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type AddRecentlyUsedDBInstanceReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceName string       `thrift:"InstanceName,2,required" frugal:"2,required,string" json:"InstanceName"`
	InstanceType InstanceType `thrift:"InstanceType,3,required" frugal:"3,required,InstanceType" json:"InstanceType"`
	LinkType     *LinkType    `thrift:"LinkType,4,optional" frugal:"4,optional,LinkType" json:"LinkType,omitempty"`
	RegionId     *string      `thrift:"RegionId,5,optional" frugal:"5,optional,string" json:"RegionId,omitempty"`
	PsmList      []string     `thrift:"PsmList,6,optional" frugal:"6,optional,list<string>" json:"PsmList,omitempty"`
}

func NewAddRecentlyUsedDBInstanceReq() *AddRecentlyUsedDBInstanceReq {
	return &AddRecentlyUsedDBInstanceReq{}
}

func (p *AddRecentlyUsedDBInstanceReq) InitDefault() {
}

func (p *AddRecentlyUsedDBInstanceReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *AddRecentlyUsedDBInstanceReq) GetInstanceName() (v string) {
	return p.InstanceName
}

func (p *AddRecentlyUsedDBInstanceReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

var AddRecentlyUsedDBInstanceReq_LinkType_DEFAULT LinkType

func (p *AddRecentlyUsedDBInstanceReq) GetLinkType() (v LinkType) {
	if !p.IsSetLinkType() {
		return AddRecentlyUsedDBInstanceReq_LinkType_DEFAULT
	}
	return *p.LinkType
}

var AddRecentlyUsedDBInstanceReq_RegionId_DEFAULT string

func (p *AddRecentlyUsedDBInstanceReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return AddRecentlyUsedDBInstanceReq_RegionId_DEFAULT
	}
	return *p.RegionId
}

var AddRecentlyUsedDBInstanceReq_PsmList_DEFAULT []string

func (p *AddRecentlyUsedDBInstanceReq) GetPsmList() (v []string) {
	if !p.IsSetPsmList() {
		return AddRecentlyUsedDBInstanceReq_PsmList_DEFAULT
	}
	return p.PsmList
}
func (p *AddRecentlyUsedDBInstanceReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *AddRecentlyUsedDBInstanceReq) SetInstanceName(val string) {
	p.InstanceName = val
}
func (p *AddRecentlyUsedDBInstanceReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *AddRecentlyUsedDBInstanceReq) SetLinkType(val *LinkType) {
	p.LinkType = val
}
func (p *AddRecentlyUsedDBInstanceReq) SetRegionId(val *string) {
	p.RegionId = val
}
func (p *AddRecentlyUsedDBInstanceReq) SetPsmList(val []string) {
	p.PsmList = val
}

var fieldIDToName_AddRecentlyUsedDBInstanceReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceName",
	3: "InstanceType",
	4: "LinkType",
	5: "RegionId",
	6: "PsmList",
}

func (p *AddRecentlyUsedDBInstanceReq) IsSetLinkType() bool {
	return p.LinkType != nil
}

func (p *AddRecentlyUsedDBInstanceReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *AddRecentlyUsedDBInstanceReq) IsSetPsmList() bool {
	return p.PsmList != nil
}

func (p *AddRecentlyUsedDBInstanceReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddRecentlyUsedDBInstanceReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceName bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AddRecentlyUsedDBInstanceReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_AddRecentlyUsedDBInstanceReq[fieldId]))
}

func (p *AddRecentlyUsedDBInstanceReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *AddRecentlyUsedDBInstanceReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceName = _field
	return nil
}
func (p *AddRecentlyUsedDBInstanceReq) ReadField3(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *AddRecentlyUsedDBInstanceReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return nil
}
func (p *AddRecentlyUsedDBInstanceReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}
func (p *AddRecentlyUsedDBInstanceReq) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PsmList = _field
	return nil
}

func (p *AddRecentlyUsedDBInstanceReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddRecentlyUsedDBInstanceReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("AddRecentlyUsedDBInstanceReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AddRecentlyUsedDBInstanceReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *AddRecentlyUsedDBInstanceReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AddRecentlyUsedDBInstanceReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AddRecentlyUsedDBInstanceReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinkType() {
		if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *AddRecentlyUsedDBInstanceReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *AddRecentlyUsedDBInstanceReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPsmList() {
		if err = oprot.WriteFieldBegin("PsmList", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.PsmList)); err != nil {
			return err
		}
		for _, v := range p.PsmList {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *AddRecentlyUsedDBInstanceReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddRecentlyUsedDBInstanceReq(%+v)", *p)

}

func (p *AddRecentlyUsedDBInstanceReq) DeepEqual(ano *AddRecentlyUsedDBInstanceReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.LinkType) {
		return false
	}
	if !p.Field5DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field6DeepEqual(ano.PsmList) {
		return false
	}
	return true
}

func (p *AddRecentlyUsedDBInstanceReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *AddRecentlyUsedDBInstanceReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceName, src) != 0 {
		return false
	}
	return true
}
func (p *AddRecentlyUsedDBInstanceReq) Field3DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *AddRecentlyUsedDBInstanceReq) Field4DeepEqual(src *LinkType) bool {

	if p.LinkType == src {
		return true
	} else if p.LinkType == nil || src == nil {
		return false
	}
	if *p.LinkType != *src {
		return false
	}
	return true
}
func (p *AddRecentlyUsedDBInstanceReq) Field5DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}
func (p *AddRecentlyUsedDBInstanceReq) Field6DeepEqual(src []string) bool {

	if len(p.PsmList) != len(src) {
		return false
	}
	for i, v := range p.PsmList {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type AddRecentlyUsedDBInstanceResp struct {
}

func NewAddRecentlyUsedDBInstanceResp() *AddRecentlyUsedDBInstanceResp {
	return &AddRecentlyUsedDBInstanceResp{}
}

func (p *AddRecentlyUsedDBInstanceResp) InitDefault() {
}

var fieldIDToName_AddRecentlyUsedDBInstanceResp = map[int16]string{}

func (p *AddRecentlyUsedDBInstanceResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddRecentlyUsedDBInstanceResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AddRecentlyUsedDBInstanceResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("AddRecentlyUsedDBInstanceResp")

	if err = oprot.WriteStructBegin("AddRecentlyUsedDBInstanceResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AddRecentlyUsedDBInstanceResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddRecentlyUsedDBInstanceResp(%+v)", *p)

}

func (p *AddRecentlyUsedDBInstanceResp) DeepEqual(ano *AddRecentlyUsedDBInstanceResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}
