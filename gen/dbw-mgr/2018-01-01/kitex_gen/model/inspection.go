// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type InspectionStatus int64

const (
	InspectionStatus_InspectionUnStart InspectionStatus = 0
	InspectionStatus_InspectionRunning InspectionStatus = 1
	InspectionStatus_InspectionSuccess InspectionStatus = 2
	InspectionStatus_InspectionFailed  InspectionStatus = 3
)

func (p InspectionStatus) String() string {
	switch p {
	case InspectionStatus_InspectionUnStart:
		return "InspectionUnStart"
	case InspectionStatus_InspectionRunning:
		return "InspectionRunning"
	case InspectionStatus_InspectionSuccess:
		return "InspectionSuccess"
	case InspectionStatus_InspectionFailed:
		return "InspectionFailed"
	}
	return "<UNSET>"
}

func InspectionStatusFromString(s string) (InspectionStatus, error) {
	switch s {
	case "InspectionUnStart":
		return InspectionStatus_InspectionUnStart, nil
	case "InspectionRunning":
		return InspectionStatus_InspectionRunning, nil
	case "InspectionSuccess":
		return InspectionStatus_InspectionSuccess, nil
	case "InspectionFailed":
		return InspectionStatus_InspectionFailed, nil
	}
	return InspectionStatus(0), fmt.Errorf("not a valid InspectionStatus string")
}

func InspectionStatusPtr(v InspectionStatus) *InspectionStatus { return &v }

func (p InspectionStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *InspectionStatus) UnmarshalText(text []byte) error {
	q, err := InspectionStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type InspectionType int64

const (
	InspectionType_Auto   InspectionType = 0
	InspectionType_Manual InspectionType = 1
)

func (p InspectionType) String() string {
	switch p {
	case InspectionType_Auto:
		return "Auto"
	case InspectionType_Manual:
		return "Manual"
	}
	return "<UNSET>"
}

func InspectionTypeFromString(s string) (InspectionType, error) {
	switch s {
	case "Auto":
		return InspectionType_Auto, nil
	case "Manual":
		return InspectionType_Manual, nil
	}
	return InspectionType(0), fmt.Errorf("not a valid InspectionType string")
}

func InspectionTypePtr(v InspectionType) *InspectionType { return &v }

func (p InspectionType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *InspectionType) UnmarshalText(text []byte) error {
	q, err := InspectionTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type OrderByForInspection int64

const (
	OrderByForInspection_ExecuteTime OrderByForInspection = 0
	OrderByForInspection_HealthScore OrderByForInspection = 1
	OrderByForInspection_CpuUsage    OrderByForInspection = 2
	OrderByForInspection_MemUsage    OrderByForInspection = 3
	OrderByForInspection_DiskUsage   OrderByForInspection = 4
	OrderByForInspection_ConnUsage   OrderByForInspection = 5
	OrderByForInspection_QPS         OrderByForInspection = 6
	OrderByForInspection_TPS         OrderByForInspection = 7
	OrderByForInspection_SlowLogNum  OrderByForInspection = 8
)

func (p OrderByForInspection) String() string {
	switch p {
	case OrderByForInspection_ExecuteTime:
		return "ExecuteTime"
	case OrderByForInspection_HealthScore:
		return "HealthScore"
	case OrderByForInspection_CpuUsage:
		return "CpuUsage"
	case OrderByForInspection_MemUsage:
		return "MemUsage"
	case OrderByForInspection_DiskUsage:
		return "DiskUsage"
	case OrderByForInspection_ConnUsage:
		return "ConnUsage"
	case OrderByForInspection_QPS:
		return "QPS"
	case OrderByForInspection_TPS:
		return "TPS"
	case OrderByForInspection_SlowLogNum:
		return "SlowLogNum"
	}
	return "<UNSET>"
}

func OrderByForInspectionFromString(s string) (OrderByForInspection, error) {
	switch s {
	case "ExecuteTime":
		return OrderByForInspection_ExecuteTime, nil
	case "HealthScore":
		return OrderByForInspection_HealthScore, nil
	case "CpuUsage":
		return OrderByForInspection_CpuUsage, nil
	case "MemUsage":
		return OrderByForInspection_MemUsage, nil
	case "DiskUsage":
		return OrderByForInspection_DiskUsage, nil
	case "ConnUsage":
		return OrderByForInspection_ConnUsage, nil
	case "QPS":
		return OrderByForInspection_QPS, nil
	case "TPS":
		return OrderByForInspection_TPS, nil
	case "SlowLogNum":
		return OrderByForInspection_SlowLogNum, nil
	}
	return OrderByForInspection(0), fmt.Errorf("not a valid OrderByForInspection string")
}

func OrderByForInspectionPtr(v OrderByForInspection) *OrderByForInspection { return &v }

func (p OrderByForInspection) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *OrderByForInspection) UnmarshalText(text []byte) error {
	q, err := OrderByForInspectionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type InspectionMetricName int64

const (
	InspectionMetricName_CpuUsage   InspectionMetricName = 0
	InspectionMetricName_MemUsage   InspectionMetricName = 1
	InspectionMetricName_DiskUsage  InspectionMetricName = 2
	InspectionMetricName_ConnUsage  InspectionMetricName = 3
	InspectionMetricName_TPS        InspectionMetricName = 4
	InspectionMetricName_QPS        InspectionMetricName = 5
	InspectionMetricName_SlowLogNum InspectionMetricName = 6
)

func (p InspectionMetricName) String() string {
	switch p {
	case InspectionMetricName_CpuUsage:
		return "CpuUsage"
	case InspectionMetricName_MemUsage:
		return "MemUsage"
	case InspectionMetricName_DiskUsage:
		return "DiskUsage"
	case InspectionMetricName_ConnUsage:
		return "ConnUsage"
	case InspectionMetricName_TPS:
		return "TPS"
	case InspectionMetricName_QPS:
		return "QPS"
	case InspectionMetricName_SlowLogNum:
		return "SlowLogNum"
	}
	return "<UNSET>"
}

func InspectionMetricNameFromString(s string) (InspectionMetricName, error) {
	switch s {
	case "CpuUsage":
		return InspectionMetricName_CpuUsage, nil
	case "MemUsage":
		return InspectionMetricName_MemUsage, nil
	case "DiskUsage":
		return InspectionMetricName_DiskUsage, nil
	case "ConnUsage":
		return InspectionMetricName_ConnUsage, nil
	case "TPS":
		return InspectionMetricName_TPS, nil
	case "QPS":
		return InspectionMetricName_QPS, nil
	case "SlowLogNum":
		return InspectionMetricName_SlowLogNum, nil
	}
	return InspectionMetricName(0), fmt.Errorf("not a valid InspectionMetricName string")
}

func InspectionMetricNamePtr(v InspectionMetricName) *InspectionMetricName { return &v }

func (p InspectionMetricName) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *InspectionMetricName) UnmarshalText(text []byte) error {
	q, err := InspectionMetricNameFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type InspectionMetric int64

const (
	InspectionMetric_SlowLog      InspectionMetric = 0
	InspectionMetric_CpuUsage     InspectionMetric = 1
	InspectionMetric_MemUsage     InspectionMetric = 2
	InspectionMetric_SpaceUsage   InspectionMetric = 3
	InspectionMetric_SessionUsage InspectionMetric = 4
)

func (p InspectionMetric) String() string {
	switch p {
	case InspectionMetric_SlowLog:
		return "SlowLog"
	case InspectionMetric_CpuUsage:
		return "CpuUsage"
	case InspectionMetric_MemUsage:
		return "MemUsage"
	case InspectionMetric_SpaceUsage:
		return "SpaceUsage"
	case InspectionMetric_SessionUsage:
		return "SessionUsage"
	}
	return "<UNSET>"
}

func InspectionMetricFromString(s string) (InspectionMetric, error) {
	switch s {
	case "SlowLog":
		return InspectionMetric_SlowLog, nil
	case "CpuUsage":
		return InspectionMetric_CpuUsage, nil
	case "MemUsage":
		return InspectionMetric_MemUsage, nil
	case "SpaceUsage":
		return InspectionMetric_SpaceUsage, nil
	case "SessionUsage":
		return InspectionMetric_SessionUsage, nil
	}
	return InspectionMetric(0), fmt.Errorf("not a valid InspectionMetric string")
}

func InspectionMetricPtr(v InspectionMetric) *InspectionMetric { return &v }

func (p InspectionMetric) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *InspectionMetric) UnmarshalText(text []byte) error {
	q, err := InspectionMetricFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ValueType int64

const (
	ValueType_Max     ValueType = 0
	ValueType_Min     ValueType = 1
	ValueType_Avg     ValueType = 2
	ValueType_P99     ValueType = 3
	ValueType_Current ValueType = 4
)

func (p ValueType) String() string {
	switch p {
	case ValueType_Max:
		return "Max"
	case ValueType_Min:
		return "Min"
	case ValueType_Avg:
		return "Avg"
	case ValueType_P99:
		return "P99"
	case ValueType_Current:
		return "Current"
	}
	return "<UNSET>"
}

func ValueTypeFromString(s string) (ValueType, error) {
	switch s {
	case "Max":
		return ValueType_Max, nil
	case "Min":
		return ValueType_Min, nil
	case "Avg":
		return ValueType_Avg, nil
	case "P99":
		return ValueType_P99, nil
	case "Current":
		return ValueType_Current, nil
	}
	return ValueType(0), fmt.Errorf("not a valid ValueType string")
}

func ValueTypePtr(v ValueType) *ValueType { return &v }

func (p ValueType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ValueType) UnmarshalText(text []byte) error {
	q, err := ValueTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DownSampleType int64

const (
	DownSampleType_Max     DownSampleType = 0
	DownSampleType_Min     DownSampleType = 1
	DownSampleType_Avg     DownSampleType = 2
	DownSampleType_P99     DownSampleType = 3
	DownSampleType_Current DownSampleType = 4
)

func (p DownSampleType) String() string {
	switch p {
	case DownSampleType_Max:
		return "Max"
	case DownSampleType_Min:
		return "Min"
	case DownSampleType_Avg:
		return "Avg"
	case DownSampleType_P99:
		return "P99"
	case DownSampleType_Current:
		return "Current"
	}
	return "<UNSET>"
}

func DownSampleTypeFromString(s string) (DownSampleType, error) {
	switch s {
	case "Max":
		return DownSampleType_Max, nil
	case "Min":
		return DownSampleType_Min, nil
	case "Avg":
		return DownSampleType_Avg, nil
	case "P99":
		return DownSampleType_P99, nil
	case "Current":
		return DownSampleType_Current, nil
	}
	return DownSampleType(0), fmt.Errorf("not a valid DownSampleType string")
}

func DownSampleTypePtr(v DownSampleType) *DownSampleType { return &v }

func (p DownSampleType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *DownSampleType) UnmarshalText(text []byte) error {
	q, err := DownSampleTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type InspectLevel int64

const (
	InspectLevel_InspectLevelZero  InspectLevel = 0
	InspectLevel_InspectLevelOne   InspectLevel = 1
	InspectLevel_InspectLevelTwo   InspectLevel = 2
	InspectLevel_InspectLevelThree InspectLevel = 3
)

func (p InspectLevel) String() string {
	switch p {
	case InspectLevel_InspectLevelZero:
		return "InspectLevelZero"
	case InspectLevel_InspectLevelOne:
		return "InspectLevelOne"
	case InspectLevel_InspectLevelTwo:
		return "InspectLevelTwo"
	case InspectLevel_InspectLevelThree:
		return "InspectLevelThree"
	}
	return "<UNSET>"
}

func InspectLevelFromString(s string) (InspectLevel, error) {
	switch s {
	case "InspectLevelZero":
		return InspectLevel_InspectLevelZero, nil
	case "InspectLevelOne":
		return InspectLevel_InspectLevelOne, nil
	case "InspectLevelTwo":
		return InspectLevel_InspectLevelTwo, nil
	case "InspectLevelThree":
		return InspectLevel_InspectLevelThree, nil
	}
	return InspectLevel(0), fmt.Errorf("not a valid InspectLevel string")
}

func InspectLevelPtr(v InspectLevel) *InspectLevel { return &v }

func (p InspectLevel) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *InspectLevel) UnmarshalText(text []byte) error {
	q, err := InspectLevelFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type OverWriteType int64

const (
	OverWriteType_OverWriteTheSame OverWriteType = 0
	OverWriteType_OverWriteAll     OverWriteType = 1
)

func (p OverWriteType) String() string {
	switch p {
	case OverWriteType_OverWriteTheSame:
		return "OverWriteTheSame"
	case OverWriteType_OverWriteAll:
		return "OverWriteAll"
	}
	return "<UNSET>"
}

func OverWriteTypeFromString(s string) (OverWriteType, error) {
	switch s {
	case "OverWriteTheSame":
		return OverWriteType_OverWriteTheSame, nil
	case "OverWriteAll":
		return OverWriteType_OverWriteAll, nil
	}
	return OverWriteType(0), fmt.Errorf("not a valid OverWriteType string")
}

func OverWriteTypePtr(v OverWriteType) *OverWriteType { return &v }

func (p OverWriteType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *OverWriteType) UnmarshalText(text []byte) error {
	q, err := OverWriteTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type DescribeDBInspectionsReq struct {
	InstanceType InstanceType           `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	RegionId     string                 `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	SearchParam  *InspectionSearchParam `thrift:"SearchParam,3,optional" frugal:"3,optional,InspectionSearchParam" json:"SearchParam,omitempty"`
	StartTime    *string                `thrift:"StartTime,4,optional" frugal:"4,optional,string" json:"StartTime,omitempty"`
	EndTime      *string                `thrift:"EndTime,5,optional" frugal:"5,optional,string" json:"EndTime,omitempty"`
	PageNumber   *int32                 `thrift:"PageNumber,6,optional" frugal:"6,optional,i32" json:"PageNumber,omitempty"`
	PageSize     *int32                 `thrift:"PageSize,7,optional" frugal:"7,optional,i32" json:"PageSize,omitempty"`
	OrderBy      *OrderByForInspection  `thrift:"OrderBy,8,optional" frugal:"8,optional,OrderByForInspection" json:"OrderBy,omitempty"`
	SortBy       *SortBy                `thrift:"SortBy,9,optional" frugal:"9,optional,SortBy" json:"SortBy,omitempty"`
}

func NewDescribeDBInspectionsReq() *DescribeDBInspectionsReq {
	return &DescribeDBInspectionsReq{}
}

func (p *DescribeDBInspectionsReq) InitDefault() {
}

func (p *DescribeDBInspectionsReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeDBInspectionsReq) GetRegionId() (v string) {
	return p.RegionId
}

var DescribeDBInspectionsReq_SearchParam_DEFAULT *InspectionSearchParam

func (p *DescribeDBInspectionsReq) GetSearchParam() (v *InspectionSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeDBInspectionsReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeDBInspectionsReq_StartTime_DEFAULT string

func (p *DescribeDBInspectionsReq) GetStartTime() (v string) {
	if !p.IsSetStartTime() {
		return DescribeDBInspectionsReq_StartTime_DEFAULT
	}
	return *p.StartTime
}

var DescribeDBInspectionsReq_EndTime_DEFAULT string

func (p *DescribeDBInspectionsReq) GetEndTime() (v string) {
	if !p.IsSetEndTime() {
		return DescribeDBInspectionsReq_EndTime_DEFAULT
	}
	return *p.EndTime
}

var DescribeDBInspectionsReq_PageNumber_DEFAULT int32

func (p *DescribeDBInspectionsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeDBInspectionsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeDBInspectionsReq_PageSize_DEFAULT int32

func (p *DescribeDBInspectionsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeDBInspectionsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeDBInspectionsReq_OrderBy_DEFAULT OrderByForInspection

func (p *DescribeDBInspectionsReq) GetOrderBy() (v OrderByForInspection) {
	if !p.IsSetOrderBy() {
		return DescribeDBInspectionsReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeDBInspectionsReq_SortBy_DEFAULT SortBy

func (p *DescribeDBInspectionsReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeDBInspectionsReq_SortBy_DEFAULT
	}
	return *p.SortBy
}
func (p *DescribeDBInspectionsReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeDBInspectionsReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *DescribeDBInspectionsReq) SetSearchParam(val *InspectionSearchParam) {
	p.SearchParam = val
}
func (p *DescribeDBInspectionsReq) SetStartTime(val *string) {
	p.StartTime = val
}
func (p *DescribeDBInspectionsReq) SetEndTime(val *string) {
	p.EndTime = val
}
func (p *DescribeDBInspectionsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeDBInspectionsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeDBInspectionsReq) SetOrderBy(val *OrderByForInspection) {
	p.OrderBy = val
}
func (p *DescribeDBInspectionsReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}

var fieldIDToName_DescribeDBInspectionsReq = map[int16]string{
	1: "InstanceType",
	2: "RegionId",
	3: "SearchParam",
	4: "StartTime",
	5: "EndTime",
	6: "PageNumber",
	7: "PageSize",
	8: "OrderBy",
	9: "SortBy",
}

func (p *DescribeDBInspectionsReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeDBInspectionsReq) IsSetStartTime() bool {
	return p.StartTime != nil
}

func (p *DescribeDBInspectionsReq) IsSetEndTime() bool {
	return p.EndTime != nil
}

func (p *DescribeDBInspectionsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeDBInspectionsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeDBInspectionsReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeDBInspectionsReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeDBInspectionsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInspectionsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInspectionsReq[fieldId]))
}

func (p *DescribeDBInspectionsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeDBInspectionsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *DescribeDBInspectionsReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewInspectionSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeDBInspectionsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeDBInspectionsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeDBInspectionsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeDBInspectionsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeDBInspectionsReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *OrderByForInspection
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForInspection(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeDBInspectionsReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}

func (p *DescribeDBInspectionsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInspectionsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInspectionsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInspectionsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInspectionsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBInspectionsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStartTime() {
		if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.StartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBInspectionsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndTime() {
		if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBInspectionsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDBInspectionsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeDBInspectionsReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeDBInspectionsReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeDBInspectionsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInspectionsReq(%+v)", *p)

}

func (p *DescribeDBInspectionsReq) DeepEqual(ano *DescribeDBInspectionsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field7DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field8DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field9DeepEqual(ano.SortBy) {
		return false
	}
	return true
}

func (p *DescribeDBInspectionsReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeDBInspectionsReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInspectionsReq) Field3DeepEqual(src *InspectionSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBInspectionsReq) Field4DeepEqual(src *string) bool {

	if p.StartTime == src {
		return true
	} else if p.StartTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.StartTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInspectionsReq) Field5DeepEqual(src *string) bool {

	if p.EndTime == src {
		return true
	} else if p.EndTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInspectionsReq) Field6DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeDBInspectionsReq) Field7DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeDBInspectionsReq) Field8DeepEqual(src *OrderByForInspection) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeDBInspectionsReq) Field9DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}

type ScoreDistribution struct {
	MinScore int64 `thrift:"MinScore,1,required" frugal:"1,required,i64" json:"MinScore"`
	MaxScore int64 `thrift:"MaxScore,2,required" frugal:"2,required,i64" json:"MaxScore"`
	Num      int64 `thrift:"Num,3,required" frugal:"3,required,i64" json:"Num"`
}

func NewScoreDistribution() *ScoreDistribution {
	return &ScoreDistribution{}
}

func (p *ScoreDistribution) InitDefault() {
}

func (p *ScoreDistribution) GetMinScore() (v int64) {
	return p.MinScore
}

func (p *ScoreDistribution) GetMaxScore() (v int64) {
	return p.MaxScore
}

func (p *ScoreDistribution) GetNum() (v int64) {
	return p.Num
}
func (p *ScoreDistribution) SetMinScore(val int64) {
	p.MinScore = val
}
func (p *ScoreDistribution) SetMaxScore(val int64) {
	p.MaxScore = val
}
func (p *ScoreDistribution) SetNum(val int64) {
	p.Num = val
}

var fieldIDToName_ScoreDistribution = map[int16]string{
	1: "MinScore",
	2: "MaxScore",
	3: "Num",
}

func (p *ScoreDistribution) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ScoreDistribution")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMinScore bool = false
	var issetMaxScore bool = false
	var issetNum bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMinScore = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxScore = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMinScore {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMaxScore {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetNum {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ScoreDistribution[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ScoreDistribution[fieldId]))
}

func (p *ScoreDistribution) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MinScore = _field
	return nil
}
func (p *ScoreDistribution) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxScore = _field
	return nil
}
func (p *ScoreDistribution) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Num = _field
	return nil
}

func (p *ScoreDistribution) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ScoreDistribution")

	var fieldId int16
	if err = oprot.WriteStructBegin("ScoreDistribution"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ScoreDistribution) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MinScore", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MinScore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ScoreDistribution) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxScore", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MaxScore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ScoreDistribution) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Num", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Num); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ScoreDistribution) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ScoreDistribution(%+v)", *p)

}

func (p *ScoreDistribution) DeepEqual(ano *ScoreDistribution) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MinScore) {
		return false
	}
	if !p.Field2DeepEqual(ano.MaxScore) {
		return false
	}
	if !p.Field3DeepEqual(ano.Num) {
		return false
	}
	return true
}

func (p *ScoreDistribution) Field1DeepEqual(src int64) bool {

	if p.MinScore != src {
		return false
	}
	return true
}
func (p *ScoreDistribution) Field2DeepEqual(src int64) bool {

	if p.MaxScore != src {
		return false
	}
	return true
}
func (p *ScoreDistribution) Field3DeepEqual(src int64) bool {

	if p.Num != src {
		return false
	}
	return true
}

type DescribeDBInspectionsResp struct {
	InspectionItems   []*InspectionItem    `thrift:"InspectionItems,1,required" frugal:"1,required,list<InspectionItem>" json:"InspectionItems"`
	Total             int32                `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
	ScoreDistribution []*ScoreDistribution `thrift:"ScoreDistribution,3,required" frugal:"3,required,list<ScoreDistribution>" json:"ScoreDistribution"`
}

func NewDescribeDBInspectionsResp() *DescribeDBInspectionsResp {
	return &DescribeDBInspectionsResp{}
}

func (p *DescribeDBInspectionsResp) InitDefault() {
}

func (p *DescribeDBInspectionsResp) GetInspectionItems() (v []*InspectionItem) {
	return p.InspectionItems
}

func (p *DescribeDBInspectionsResp) GetTotal() (v int32) {
	return p.Total
}

func (p *DescribeDBInspectionsResp) GetScoreDistribution() (v []*ScoreDistribution) {
	return p.ScoreDistribution
}
func (p *DescribeDBInspectionsResp) SetInspectionItems(val []*InspectionItem) {
	p.InspectionItems = val
}
func (p *DescribeDBInspectionsResp) SetTotal(val int32) {
	p.Total = val
}
func (p *DescribeDBInspectionsResp) SetScoreDistribution(val []*ScoreDistribution) {
	p.ScoreDistribution = val
}

var fieldIDToName_DescribeDBInspectionsResp = map[int16]string{
	1: "InspectionItems",
	2: "Total",
	3: "ScoreDistribution",
}

func (p *DescribeDBInspectionsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInspectionItems bool = false
	var issetTotal bool = false
	var issetScoreDistribution bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetScoreDistribution = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInspectionItems {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetScoreDistribution {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInspectionsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInspectionsResp[fieldId]))
}

func (p *DescribeDBInspectionsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionItem, 0, size)
	values := make([]InspectionItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InspectionItems = _field
	return nil
}
func (p *DescribeDBInspectionsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}
func (p *DescribeDBInspectionsResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ScoreDistribution, 0, size)
	values := make([]ScoreDistribution, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ScoreDistribution = _field
	return nil
}

func (p *DescribeDBInspectionsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInspectionsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInspectionsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionItems", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InspectionItems)); err != nil {
		return err
	}
	for _, v := range p.InspectionItems {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInspectionsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInspectionsResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ScoreDistribution", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ScoreDistribution)); err != nil {
		return err
	}
	for _, v := range p.ScoreDistribution {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBInspectionsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInspectionsResp(%+v)", *p)

}

func (p *DescribeDBInspectionsResp) DeepEqual(ano *DescribeDBInspectionsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InspectionItems) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	if !p.Field3DeepEqual(ano.ScoreDistribution) {
		return false
	}
	return true
}

func (p *DescribeDBInspectionsResp) Field1DeepEqual(src []*InspectionItem) bool {

	if len(p.InspectionItems) != len(src) {
		return false
	}
	for i, v := range p.InspectionItems {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDBInspectionsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
func (p *DescribeDBInspectionsResp) Field3DeepEqual(src []*ScoreDistribution) bool {

	if len(p.ScoreDistribution) != len(src) {
		return false
	}
	for i, v := range p.ScoreDistribution {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type InspectionSearchParam struct {
	InstanceId       *string           `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	InstanceName     *string           `thrift:"InstanceName,2,optional" frugal:"2,optional,string" json:"InstanceName,omitempty"`
	InspectionStatus *InspectionStatus `thrift:"InspectionStatus,3,optional" frugal:"3,optional,InspectionStatus" json:"InspectionStatus,omitempty"`
	InspectionType   *InspectionType   `thrift:"InspectionType,4,optional" frugal:"4,optional,InspectionType" json:"InspectionType,omitempty"`
	MinScore         *int64            `thrift:"MinScore,6,optional" frugal:"6,optional,i64" json:"MinScore,omitempty"`
	MaxScore         *int64            `thrift:"MaxScore,7,optional" frugal:"7,optional,i64" json:"MaxScore,omitempty"`
}

func NewInspectionSearchParam() *InspectionSearchParam {
	return &InspectionSearchParam{}
}

func (p *InspectionSearchParam) InitDefault() {
}

var InspectionSearchParam_InstanceId_DEFAULT string

func (p *InspectionSearchParam) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return InspectionSearchParam_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var InspectionSearchParam_InstanceName_DEFAULT string

func (p *InspectionSearchParam) GetInstanceName() (v string) {
	if !p.IsSetInstanceName() {
		return InspectionSearchParam_InstanceName_DEFAULT
	}
	return *p.InstanceName
}

var InspectionSearchParam_InspectionStatus_DEFAULT InspectionStatus

func (p *InspectionSearchParam) GetInspectionStatus() (v InspectionStatus) {
	if !p.IsSetInspectionStatus() {
		return InspectionSearchParam_InspectionStatus_DEFAULT
	}
	return *p.InspectionStatus
}

var InspectionSearchParam_InspectionType_DEFAULT InspectionType

func (p *InspectionSearchParam) GetInspectionType() (v InspectionType) {
	if !p.IsSetInspectionType() {
		return InspectionSearchParam_InspectionType_DEFAULT
	}
	return *p.InspectionType
}

var InspectionSearchParam_MinScore_DEFAULT int64

func (p *InspectionSearchParam) GetMinScore() (v int64) {
	if !p.IsSetMinScore() {
		return InspectionSearchParam_MinScore_DEFAULT
	}
	return *p.MinScore
}

var InspectionSearchParam_MaxScore_DEFAULT int64

func (p *InspectionSearchParam) GetMaxScore() (v int64) {
	if !p.IsSetMaxScore() {
		return InspectionSearchParam_MaxScore_DEFAULT
	}
	return *p.MaxScore
}
func (p *InspectionSearchParam) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *InspectionSearchParam) SetInstanceName(val *string) {
	p.InstanceName = val
}
func (p *InspectionSearchParam) SetInspectionStatus(val *InspectionStatus) {
	p.InspectionStatus = val
}
func (p *InspectionSearchParam) SetInspectionType(val *InspectionType) {
	p.InspectionType = val
}
func (p *InspectionSearchParam) SetMinScore(val *int64) {
	p.MinScore = val
}
func (p *InspectionSearchParam) SetMaxScore(val *int64) {
	p.MaxScore = val
}

var fieldIDToName_InspectionSearchParam = map[int16]string{
	1: "InstanceId",
	2: "InstanceName",
	3: "InspectionStatus",
	4: "InspectionType",
	6: "MinScore",
	7: "MaxScore",
}

func (p *InspectionSearchParam) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *InspectionSearchParam) IsSetInstanceName() bool {
	return p.InstanceName != nil
}

func (p *InspectionSearchParam) IsSetInspectionStatus() bool {
	return p.InspectionStatus != nil
}

func (p *InspectionSearchParam) IsSetInspectionType() bool {
	return p.InspectionType != nil
}

func (p *InspectionSearchParam) IsSetMinScore() bool {
	return p.MinScore != nil
}

func (p *InspectionSearchParam) IsSetMaxScore() bool {
	return p.MaxScore != nil
}

func (p *InspectionSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *InspectionSearchParam) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *InspectionSearchParam) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceName = _field
	return nil
}
func (p *InspectionSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *InspectionStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InspectionStatus(v)
		_field = &tmp
	}
	p.InspectionStatus = _field
	return nil
}
func (p *InspectionSearchParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *InspectionType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InspectionType(v)
		_field = &tmp
	}
	p.InspectionType = _field
	return nil
}
func (p *InspectionSearchParam) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MinScore = _field
	return nil
}
func (p *InspectionSearchParam) ReadField7(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxScore = _field
	return nil
}

func (p *InspectionSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceName() {
		if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInspectionStatus() {
		if err = oprot.WriteFieldBegin("InspectionStatus", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InspectionStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInspectionType() {
		if err = oprot.WriteFieldBegin("InspectionType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InspectionType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InspectionSearchParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetMinScore() {
		if err = oprot.WriteFieldBegin("MinScore", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MinScore); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InspectionSearchParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxScore() {
		if err = oprot.WriteFieldBegin("MaxScore", thrift.I64, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MaxScore); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *InspectionSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionSearchParam(%+v)", *p)

}

func (p *InspectionSearchParam) DeepEqual(ano *InspectionSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field3DeepEqual(ano.InspectionStatus) {
		return false
	}
	if !p.Field4DeepEqual(ano.InspectionType) {
		return false
	}
	if !p.Field6DeepEqual(ano.MinScore) {
		return false
	}
	if !p.Field7DeepEqual(ano.MaxScore) {
		return false
	}
	return true
}

func (p *InspectionSearchParam) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *InspectionSearchParam) Field2DeepEqual(src *string) bool {

	if p.InstanceName == src {
		return true
	} else if p.InstanceName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceName, *src) != 0 {
		return false
	}
	return true
}
func (p *InspectionSearchParam) Field3DeepEqual(src *InspectionStatus) bool {

	if p.InspectionStatus == src {
		return true
	} else if p.InspectionStatus == nil || src == nil {
		return false
	}
	if *p.InspectionStatus != *src {
		return false
	}
	return true
}
func (p *InspectionSearchParam) Field4DeepEqual(src *InspectionType) bool {

	if p.InspectionType == src {
		return true
	} else if p.InspectionType == nil || src == nil {
		return false
	}
	if *p.InspectionType != *src {
		return false
	}
	return true
}
func (p *InspectionSearchParam) Field6DeepEqual(src *int64) bool {

	if p.MinScore == src {
		return true
	} else if p.MinScore == nil || src == nil {
		return false
	}
	if *p.MinScore != *src {
		return false
	}
	return true
}
func (p *InspectionSearchParam) Field7DeepEqual(src *int64) bool {

	if p.MaxScore == src {
		return true
	} else if p.MaxScore == nil || src == nil {
		return false
	}
	if *p.MaxScore != *src {
		return false
	}
	return true
}

type InspectAgg struct {
	HealthScore int32         `thrift:"HealthScore,1,required" frugal:"1,required,i32" json:"HealthScore"`
	Vals        []*InspectVal `thrift:"Vals,2,required" frugal:"2,required,list<InspectVal>" json:"Vals"`
}

func NewInspectAgg() *InspectAgg {
	return &InspectAgg{}
}

func (p *InspectAgg) InitDefault() {
}

func (p *InspectAgg) GetHealthScore() (v int32) {
	return p.HealthScore
}

func (p *InspectAgg) GetVals() (v []*InspectVal) {
	return p.Vals
}
func (p *InspectAgg) SetHealthScore(val int32) {
	p.HealthScore = val
}
func (p *InspectAgg) SetVals(val []*InspectVal) {
	p.Vals = val
}

var fieldIDToName_InspectAgg = map[int16]string{
	1: "HealthScore",
	2: "Vals",
}

func (p *InspectAgg) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectAgg")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetHealthScore bool = false
	var issetVals bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetHealthScore = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetVals = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetHealthScore {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetVals {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectAgg[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectAgg[fieldId]))
}

func (p *InspectAgg) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HealthScore = _field
	return nil
}
func (p *InspectAgg) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectVal, 0, size)
	values := make([]InspectVal, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Vals = _field
	return nil
}

func (p *InspectAgg) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectAgg")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectAgg"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectAgg) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HealthScore", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.HealthScore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectAgg) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Vals", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Vals)); err != nil {
		return err
	}
	for _, v := range p.Vals {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectAgg) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectAgg(%+v)", *p)

}

func (p *InspectAgg) DeepEqual(ano *InspectAgg) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.HealthScore) {
		return false
	}
	if !p.Field2DeepEqual(ano.Vals) {
		return false
	}
	return true
}

func (p *InspectAgg) Field1DeepEqual(src int32) bool {

	if p.HealthScore != src {
		return false
	}
	return true
}
func (p *InspectAgg) Field2DeepEqual(src []*InspectVal) bool {

	if len(p.Vals) != len(src) {
		return false
	}
	for i, v := range p.Vals {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type InspectVal struct {
	DiagItem InspectionMetricName `thrift:"DiagItem,1,required" frugal:"1,required,InspectionMetricName" json:"DiagItem"`
	Val      float64              `thrift:"Val,2,required" frugal:"2,required,double" json:"Val"`
	ErrMsg   string               `thrift:"ErrMsg,5,required" frugal:"5,required,string" json:"ErrMsg"`
}

func NewInspectVal() *InspectVal {
	return &InspectVal{}
}

func (p *InspectVal) InitDefault() {
}

func (p *InspectVal) GetDiagItem() (v InspectionMetricName) {
	return p.DiagItem
}

func (p *InspectVal) GetVal() (v float64) {
	return p.Val
}

func (p *InspectVal) GetErrMsg() (v string) {
	return p.ErrMsg
}
func (p *InspectVal) SetDiagItem(val InspectionMetricName) {
	p.DiagItem = val
}
func (p *InspectVal) SetVal(val float64) {
	p.Val = val
}
func (p *InspectVal) SetErrMsg(val string) {
	p.ErrMsg = val
}

var fieldIDToName_InspectVal = map[int16]string{
	1: "DiagItem",
	2: "Val",
	5: "ErrMsg",
}

func (p *InspectVal) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectVal")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDiagItem bool = false
	var issetVal bool = false
	var issetErrMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDiagItem = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetVal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDiagItem {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetVal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetErrMsg {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectVal[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectVal[fieldId]))
}

func (p *InspectVal) ReadField1(iprot thrift.TProtocol) error {

	var _field InspectionMetricName
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InspectionMetricName(v)
	}
	p.DiagItem = _field
	return nil
}
func (p *InspectVal) ReadField2(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Val = _field
	return nil
}
func (p *InspectVal) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrMsg = _field
	return nil
}

func (p *InspectVal) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectVal")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectVal"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectVal) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DiagItem", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.DiagItem)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectVal) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Val", thrift.DOUBLE, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Val); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectVal) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrMsg", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InspectVal) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectVal(%+v)", *p)

}

func (p *InspectVal) DeepEqual(ano *InspectVal) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DiagItem) {
		return false
	}
	if !p.Field2DeepEqual(ano.Val) {
		return false
	}
	if !p.Field5DeepEqual(ano.ErrMsg) {
		return false
	}
	return true
}

func (p *InspectVal) Field1DeepEqual(src InspectionMetricName) bool {

	if p.DiagItem != src {
		return false
	}
	return true
}
func (p *InspectVal) Field2DeepEqual(src float64) bool {

	if p.Val != src {
		return false
	}
	return true
}
func (p *InspectVal) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ErrMsg, src) != 0 {
		return false
	}
	return true
}

type InspectionItem struct {
	TaskId              string           `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
	InstanceId          string           `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	InstanceName        string           `thrift:"InstanceName,3,required" frugal:"3,required,string" json:"InstanceName"`
	TaskStatus          InspectionStatus `thrift:"TaskStatus,4,required" frugal:"4,required,InspectionStatus" json:"TaskStatus"`
	ExecuteTime         string           `thrift:"ExecuteTime,5,required" frugal:"5,required,string" json:"ExecuteTime"`
	TaskType            InspectionType   `thrift:"TaskType,6,required" frugal:"6,required,InspectionType" json:"TaskType"`
	InspectionStartTime string           `thrift:"InspectionStartTime,7,required" frugal:"7,required,string" json:"InspectionStartTime"`
	InspectionEndTime   string           `thrift:"InspectionEndTime,8,required" frugal:"8,required,string" json:"InspectionEndTime"`
	HealthScore         int32            `thrift:"HealthScore,9,required" frugal:"9,required,i32" json:"HealthScore"`
	CpuUsage            string           `thrift:"CpuUsage,10,required" frugal:"10,required,string" json:"CpuUsage"`
	MemUsage            string           `thrift:"MemUsage,11,required" frugal:"11,required,string" json:"MemUsage"`
	DiskUsage           string           `thrift:"DiskUsage,12,required" frugal:"12,required,string" json:"DiskUsage"`
	ConnUsage           string           `thrift:"ConnUsage,13,required" frugal:"13,required,string" json:"ConnUsage"`
	QPS                 string           `thrift:"QPS,14,required" frugal:"14,required,string" json:"QPS"`
	TPS                 string           `thrift:"TPS,15,required" frugal:"15,required,string" json:"TPS"`
	SlowLogNum          int32            `thrift:"SlowLogNum,16,required" frugal:"16,required,i32" json:"SlowLogNum"`
	LastOneAgg          *InspectAgg      `thrift:"LastOneAgg,17,required" frugal:"17,required,InspectAgg" json:"LastOneAgg"`
}

func NewInspectionItem() *InspectionItem {
	return &InspectionItem{}
}

func (p *InspectionItem) InitDefault() {
}

func (p *InspectionItem) GetTaskId() (v string) {
	return p.TaskId
}

func (p *InspectionItem) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *InspectionItem) GetInstanceName() (v string) {
	return p.InstanceName
}

func (p *InspectionItem) GetTaskStatus() (v InspectionStatus) {
	return p.TaskStatus
}

func (p *InspectionItem) GetExecuteTime() (v string) {
	return p.ExecuteTime
}

func (p *InspectionItem) GetTaskType() (v InspectionType) {
	return p.TaskType
}

func (p *InspectionItem) GetInspectionStartTime() (v string) {
	return p.InspectionStartTime
}

func (p *InspectionItem) GetInspectionEndTime() (v string) {
	return p.InspectionEndTime
}

func (p *InspectionItem) GetHealthScore() (v int32) {
	return p.HealthScore
}

func (p *InspectionItem) GetCpuUsage() (v string) {
	return p.CpuUsage
}

func (p *InspectionItem) GetMemUsage() (v string) {
	return p.MemUsage
}

func (p *InspectionItem) GetDiskUsage() (v string) {
	return p.DiskUsage
}

func (p *InspectionItem) GetConnUsage() (v string) {
	return p.ConnUsage
}

func (p *InspectionItem) GetQPS() (v string) {
	return p.QPS
}

func (p *InspectionItem) GetTPS() (v string) {
	return p.TPS
}

func (p *InspectionItem) GetSlowLogNum() (v int32) {
	return p.SlowLogNum
}

var InspectionItem_LastOneAgg_DEFAULT *InspectAgg

func (p *InspectionItem) GetLastOneAgg() (v *InspectAgg) {
	if !p.IsSetLastOneAgg() {
		return InspectionItem_LastOneAgg_DEFAULT
	}
	return p.LastOneAgg
}
func (p *InspectionItem) SetTaskId(val string) {
	p.TaskId = val
}
func (p *InspectionItem) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *InspectionItem) SetInstanceName(val string) {
	p.InstanceName = val
}
func (p *InspectionItem) SetTaskStatus(val InspectionStatus) {
	p.TaskStatus = val
}
func (p *InspectionItem) SetExecuteTime(val string) {
	p.ExecuteTime = val
}
func (p *InspectionItem) SetTaskType(val InspectionType) {
	p.TaskType = val
}
func (p *InspectionItem) SetInspectionStartTime(val string) {
	p.InspectionStartTime = val
}
func (p *InspectionItem) SetInspectionEndTime(val string) {
	p.InspectionEndTime = val
}
func (p *InspectionItem) SetHealthScore(val int32) {
	p.HealthScore = val
}
func (p *InspectionItem) SetCpuUsage(val string) {
	p.CpuUsage = val
}
func (p *InspectionItem) SetMemUsage(val string) {
	p.MemUsage = val
}
func (p *InspectionItem) SetDiskUsage(val string) {
	p.DiskUsage = val
}
func (p *InspectionItem) SetConnUsage(val string) {
	p.ConnUsage = val
}
func (p *InspectionItem) SetQPS(val string) {
	p.QPS = val
}
func (p *InspectionItem) SetTPS(val string) {
	p.TPS = val
}
func (p *InspectionItem) SetSlowLogNum(val int32) {
	p.SlowLogNum = val
}
func (p *InspectionItem) SetLastOneAgg(val *InspectAgg) {
	p.LastOneAgg = val
}

var fieldIDToName_InspectionItem = map[int16]string{
	1:  "TaskId",
	2:  "InstanceId",
	3:  "InstanceName",
	4:  "TaskStatus",
	5:  "ExecuteTime",
	6:  "TaskType",
	7:  "InspectionStartTime",
	8:  "InspectionEndTime",
	9:  "HealthScore",
	10: "CpuUsage",
	11: "MemUsage",
	12: "DiskUsage",
	13: "ConnUsage",
	14: "QPS",
	15: "TPS",
	16: "SlowLogNum",
	17: "LastOneAgg",
}

func (p *InspectionItem) IsSetLastOneAgg() bool {
	return p.LastOneAgg != nil
}

func (p *InspectionItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	var issetInstanceId bool = false
	var issetInstanceName bool = false
	var issetTaskStatus bool = false
	var issetExecuteTime bool = false
	var issetTaskType bool = false
	var issetInspectionStartTime bool = false
	var issetInspectionEndTime bool = false
	var issetHealthScore bool = false
	var issetCpuUsage bool = false
	var issetMemUsage bool = false
	var issetDiskUsage bool = false
	var issetConnUsage bool = false
	var issetQPS bool = false
	var issetTPS bool = false
	var issetSlowLogNum bool = false
	var issetLastOneAgg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecuteTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetHealthScore = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetCpuUsage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetMemUsage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetDiskUsage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetConnUsage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetQPS = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetTPS = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
				issetSlowLogNum = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
				issetLastOneAgg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTaskStatus {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetExecuteTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetTaskType {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetInspectionStartTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetInspectionEndTime {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetHealthScore {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetCpuUsage {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetMemUsage {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetDiskUsage {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetConnUsage {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetQPS {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetTPS {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetSlowLogNum {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetLastOneAgg {
		fieldId = 17
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionItem[fieldId]))
}

func (p *InspectionItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *InspectionItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *InspectionItem) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceName = _field
	return nil
}
func (p *InspectionItem) ReadField4(iprot thrift.TProtocol) error {

	var _field InspectionStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InspectionStatus(v)
	}
	p.TaskStatus = _field
	return nil
}
func (p *InspectionItem) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecuteTime = _field
	return nil
}
func (p *InspectionItem) ReadField6(iprot thrift.TProtocol) error {

	var _field InspectionType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InspectionType(v)
	}
	p.TaskType = _field
	return nil
}
func (p *InspectionItem) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InspectionStartTime = _field
	return nil
}
func (p *InspectionItem) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InspectionEndTime = _field
	return nil
}
func (p *InspectionItem) ReadField9(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HealthScore = _field
	return nil
}
func (p *InspectionItem) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CpuUsage = _field
	return nil
}
func (p *InspectionItem) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MemUsage = _field
	return nil
}
func (p *InspectionItem) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DiskUsage = _field
	return nil
}
func (p *InspectionItem) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConnUsage = _field
	return nil
}
func (p *InspectionItem) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.QPS = _field
	return nil
}
func (p *InspectionItem) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TPS = _field
	return nil
}
func (p *InspectionItem) ReadField16(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SlowLogNum = _field
	return nil
}
func (p *InspectionItem) ReadField17(iprot thrift.TProtocol) error {
	_field := NewInspectAgg()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.LastOneAgg = _field
	return nil
}

func (p *InspectionItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskStatus", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TaskStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InspectionItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecuteTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecuteTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InspectionItem) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskType", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TaskType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InspectionItem) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionStartTime", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InspectionStartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *InspectionItem) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionEndTime", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InspectionEndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *InspectionItem) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HealthScore", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.HealthScore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *InspectionItem) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CpuUsage", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CpuUsage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *InspectionItem) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MemUsage", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.MemUsage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *InspectionItem) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DiskUsage", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DiskUsage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *InspectionItem) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConnUsage", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ConnUsage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *InspectionItem) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("QPS", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.QPS); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *InspectionItem) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TPS", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TPS); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *InspectionItem) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SlowLogNum", thrift.I32, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.SlowLogNum); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *InspectionItem) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("LastOneAgg", thrift.STRUCT, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.LastOneAgg.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *InspectionItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionItem(%+v)", *p)

}

func (p *InspectionItem) DeepEqual(ano *InspectionItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceName) {
		return false
	}
	if !p.Field4DeepEqual(ano.TaskStatus) {
		return false
	}
	if !p.Field5DeepEqual(ano.ExecuteTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.TaskType) {
		return false
	}
	if !p.Field7DeepEqual(ano.InspectionStartTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.InspectionEndTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.HealthScore) {
		return false
	}
	if !p.Field10DeepEqual(ano.CpuUsage) {
		return false
	}
	if !p.Field11DeepEqual(ano.MemUsage) {
		return false
	}
	if !p.Field12DeepEqual(ano.DiskUsage) {
		return false
	}
	if !p.Field13DeepEqual(ano.ConnUsage) {
		return false
	}
	if !p.Field14DeepEqual(ano.QPS) {
		return false
	}
	if !p.Field15DeepEqual(ano.TPS) {
		return false
	}
	if !p.Field16DeepEqual(ano.SlowLogNum) {
		return false
	}
	if !p.Field17DeepEqual(ano.LastOneAgg) {
		return false
	}
	return true
}

func (p *InspectionItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceName, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field4DeepEqual(src InspectionStatus) bool {

	if p.TaskStatus != src {
		return false
	}
	return true
}
func (p *InspectionItem) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ExecuteTime, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field6DeepEqual(src InspectionType) bool {

	if p.TaskType != src {
		return false
	}
	return true
}
func (p *InspectionItem) Field7DeepEqual(src string) bool {

	if strings.Compare(p.InspectionStartTime, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field8DeepEqual(src string) bool {

	if strings.Compare(p.InspectionEndTime, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field9DeepEqual(src int32) bool {

	if p.HealthScore != src {
		return false
	}
	return true
}
func (p *InspectionItem) Field10DeepEqual(src string) bool {

	if strings.Compare(p.CpuUsage, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field11DeepEqual(src string) bool {

	if strings.Compare(p.MemUsage, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field12DeepEqual(src string) bool {

	if strings.Compare(p.DiskUsage, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field13DeepEqual(src string) bool {

	if strings.Compare(p.ConnUsage, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field14DeepEqual(src string) bool {

	if strings.Compare(p.QPS, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field15DeepEqual(src string) bool {

	if strings.Compare(p.TPS, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionItem) Field16DeepEqual(src int32) bool {

	if p.SlowLogNum != src {
		return false
	}
	return true
}
func (p *InspectionItem) Field17DeepEqual(src *InspectAgg) bool {

	if !p.LastOneAgg.DeepEqual(src) {
		return false
	}
	return true
}

type CreateAutomaticInspectionReq struct {
	InstanceType         InstanceType              `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	RegionId             string                    `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	InstanceInfo         []*InspectionInstanceInfo `thrift:"InstanceInfo,3,required" frugal:"3,required,list<InspectionInstanceInfo>" json:"InstanceInfo"`
	EnableAutoInspection bool                      `thrift:"EnableAutoInspection,4,required" frugal:"4,required,bool" json:"EnableAutoInspection"`
	StartTime            int32                     `thrift:"StartTime,5,required" frugal:"5,required,i32" json:"StartTime"`
	EndTime              int32                     `thrift:"EndTime,6,required" frugal:"6,required,i32" json:"EndTime"`
	ExecStartTime        *int32                    `thrift:"ExecStartTime,7,optional" frugal:"7,optional,i32" json:"ExecStartTime,omitempty"`
	ExecEndTime          *int32                    `thrift:"ExecEndTime,8,optional" frugal:"8,optional,i32" json:"ExecEndTime,omitempty"`
	CreateTime           string                    `thrift:"CreateTime,9,required" frugal:"9,required,string" json:"CreateTime"`
}

func NewCreateAutomaticInspectionReq() *CreateAutomaticInspectionReq {
	return &CreateAutomaticInspectionReq{}
}

func (p *CreateAutomaticInspectionReq) InitDefault() {
}

func (p *CreateAutomaticInspectionReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *CreateAutomaticInspectionReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *CreateAutomaticInspectionReq) GetInstanceInfo() (v []*InspectionInstanceInfo) {
	return p.InstanceInfo
}

func (p *CreateAutomaticInspectionReq) GetEnableAutoInspection() (v bool) {
	return p.EnableAutoInspection
}

func (p *CreateAutomaticInspectionReq) GetStartTime() (v int32) {
	return p.StartTime
}

func (p *CreateAutomaticInspectionReq) GetEndTime() (v int32) {
	return p.EndTime
}

var CreateAutomaticInspectionReq_ExecStartTime_DEFAULT int32

func (p *CreateAutomaticInspectionReq) GetExecStartTime() (v int32) {
	if !p.IsSetExecStartTime() {
		return CreateAutomaticInspectionReq_ExecStartTime_DEFAULT
	}
	return *p.ExecStartTime
}

var CreateAutomaticInspectionReq_ExecEndTime_DEFAULT int32

func (p *CreateAutomaticInspectionReq) GetExecEndTime() (v int32) {
	if !p.IsSetExecEndTime() {
		return CreateAutomaticInspectionReq_ExecEndTime_DEFAULT
	}
	return *p.ExecEndTime
}

func (p *CreateAutomaticInspectionReq) GetCreateTime() (v string) {
	return p.CreateTime
}
func (p *CreateAutomaticInspectionReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *CreateAutomaticInspectionReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *CreateAutomaticInspectionReq) SetInstanceInfo(val []*InspectionInstanceInfo) {
	p.InstanceInfo = val
}
func (p *CreateAutomaticInspectionReq) SetEnableAutoInspection(val bool) {
	p.EnableAutoInspection = val
}
func (p *CreateAutomaticInspectionReq) SetStartTime(val int32) {
	p.StartTime = val
}
func (p *CreateAutomaticInspectionReq) SetEndTime(val int32) {
	p.EndTime = val
}
func (p *CreateAutomaticInspectionReq) SetExecStartTime(val *int32) {
	p.ExecStartTime = val
}
func (p *CreateAutomaticInspectionReq) SetExecEndTime(val *int32) {
	p.ExecEndTime = val
}
func (p *CreateAutomaticInspectionReq) SetCreateTime(val string) {
	p.CreateTime = val
}

var fieldIDToName_CreateAutomaticInspectionReq = map[int16]string{
	1: "InstanceType",
	2: "RegionId",
	3: "InstanceInfo",
	4: "EnableAutoInspection",
	5: "StartTime",
	6: "EndTime",
	7: "ExecStartTime",
	8: "ExecEndTime",
	9: "CreateTime",
}

func (p *CreateAutomaticInspectionReq) IsSetExecStartTime() bool {
	return p.ExecStartTime != nil
}

func (p *CreateAutomaticInspectionReq) IsSetExecEndTime() bool {
	return p.ExecEndTime != nil
}

func (p *CreateAutomaticInspectionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAutomaticInspectionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetInstanceInfo bool = false
	var issetEnableAutoInspection bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetCreateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableAutoInspection = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceInfo {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEnableAutoInspection {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateAutomaticInspectionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateAutomaticInspectionReq[fieldId]))
}

func (p *CreateAutomaticInspectionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *CreateAutomaticInspectionReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *CreateAutomaticInspectionReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionInstanceInfo, 0, size)
	values := make([]InspectionInstanceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceInfo = _field
	return nil
}
func (p *CreateAutomaticInspectionReq) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EnableAutoInspection = _field
	return nil
}
func (p *CreateAutomaticInspectionReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *CreateAutomaticInspectionReq) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *CreateAutomaticInspectionReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecStartTime = _field
	return nil
}
func (p *CreateAutomaticInspectionReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecEndTime = _field
	return nil
}
func (p *CreateAutomaticInspectionReq) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}

func (p *CreateAutomaticInspectionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAutomaticInspectionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateAutomaticInspectionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateAutomaticInspectionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateAutomaticInspectionReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateAutomaticInspectionReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceInfo", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InstanceInfo)); err != nil {
		return err
	}
	for _, v := range p.InstanceInfo {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateAutomaticInspectionReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableAutoInspection", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.EnableAutoInspection); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateAutomaticInspectionReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateAutomaticInspectionReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateAutomaticInspectionReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecStartTime() {
		if err = oprot.WriteFieldBegin("ExecStartTime", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ExecStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateAutomaticInspectionReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecEndTime() {
		if err = oprot.WriteFieldBegin("ExecEndTime", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ExecEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateAutomaticInspectionReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *CreateAutomaticInspectionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAutomaticInspectionReq(%+v)", *p)

}

func (p *CreateAutomaticInspectionReq) DeepEqual(ano *CreateAutomaticInspectionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceInfo) {
		return false
	}
	if !p.Field4DeepEqual(ano.EnableAutoInspection) {
		return false
	}
	if !p.Field5DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.ExecStartTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.ExecEndTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.CreateTime) {
		return false
	}
	return true
}

func (p *CreateAutomaticInspectionReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *CreateAutomaticInspectionReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateAutomaticInspectionReq) Field3DeepEqual(src []*InspectionInstanceInfo) bool {

	if len(p.InstanceInfo) != len(src) {
		return false
	}
	for i, v := range p.InstanceInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *CreateAutomaticInspectionReq) Field4DeepEqual(src bool) bool {

	if p.EnableAutoInspection != src {
		return false
	}
	return true
}
func (p *CreateAutomaticInspectionReq) Field5DeepEqual(src int32) bool {

	if p.StartTime != src {
		return false
	}
	return true
}
func (p *CreateAutomaticInspectionReq) Field6DeepEqual(src int32) bool {

	if p.EndTime != src {
		return false
	}
	return true
}
func (p *CreateAutomaticInspectionReq) Field7DeepEqual(src *int32) bool {

	if p.ExecStartTime == src {
		return true
	} else if p.ExecStartTime == nil || src == nil {
		return false
	}
	if *p.ExecStartTime != *src {
		return false
	}
	return true
}
func (p *CreateAutomaticInspectionReq) Field8DeepEqual(src *int32) bool {

	if p.ExecEndTime == src {
		return true
	} else if p.ExecEndTime == nil || src == nil {
		return false
	}
	if *p.ExecEndTime != *src {
		return false
	}
	return true
}
func (p *CreateAutomaticInspectionReq) Field9DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}

type CreateAutomaticInspectionResp struct {
	TaskInfos []*InspectionTaskInfo `thrift:"TaskInfos,1,required" frugal:"1,required,list<InspectionTaskInfo>" json:"TaskInfos"`
}

func NewCreateAutomaticInspectionResp() *CreateAutomaticInspectionResp {
	return &CreateAutomaticInspectionResp{}
}

func (p *CreateAutomaticInspectionResp) InitDefault() {
}

func (p *CreateAutomaticInspectionResp) GetTaskInfos() (v []*InspectionTaskInfo) {
	return p.TaskInfos
}
func (p *CreateAutomaticInspectionResp) SetTaskInfos(val []*InspectionTaskInfo) {
	p.TaskInfos = val
}

var fieldIDToName_CreateAutomaticInspectionResp = map[int16]string{
	1: "TaskInfos",
}

func (p *CreateAutomaticInspectionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAutomaticInspectionResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskInfos bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskInfos {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateAutomaticInspectionResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateAutomaticInspectionResp[fieldId]))
}

func (p *CreateAutomaticInspectionResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionTaskInfo, 0, size)
	values := make([]InspectionTaskInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TaskInfos = _field
	return nil
}

func (p *CreateAutomaticInspectionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateAutomaticInspectionResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateAutomaticInspectionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateAutomaticInspectionResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TaskInfos)); err != nil {
		return err
	}
	for _, v := range p.TaskInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateAutomaticInspectionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAutomaticInspectionResp(%+v)", *p)

}

func (p *CreateAutomaticInspectionResp) DeepEqual(ano *CreateAutomaticInspectionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskInfos) {
		return false
	}
	return true
}

func (p *CreateAutomaticInspectionResp) Field1DeepEqual(src []*InspectionTaskInfo) bool {

	if len(p.TaskInfos) != len(src) {
		return false
	}
	for i, v := range p.TaskInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type InspectionInstanceInfo struct {
	InstanceId   string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceName string `thrift:"InstanceName,2,required" frugal:"2,required,string" json:"InstanceName"`
}

func NewInspectionInstanceInfo() *InspectionInstanceInfo {
	return &InspectionInstanceInfo{}
}

func (p *InspectionInstanceInfo) InitDefault() {
}

func (p *InspectionInstanceInfo) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *InspectionInstanceInfo) GetInstanceName() (v string) {
	return p.InstanceName
}
func (p *InspectionInstanceInfo) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *InspectionInstanceInfo) SetInstanceName(val string) {
	p.InstanceName = val
}

var fieldIDToName_InspectionInstanceInfo = map[int16]string{
	1: "InstanceId",
	2: "InstanceName",
}

func (p *InspectionInstanceInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionInstanceInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionInstanceInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionInstanceInfo[fieldId]))
}

func (p *InspectionInstanceInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *InspectionInstanceInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceName = _field
	return nil
}

func (p *InspectionInstanceInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionInstanceInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionInstanceInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionInstanceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionInstanceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionInstanceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionInstanceInfo(%+v)", *p)

}

func (p *InspectionInstanceInfo) DeepEqual(ano *InspectionInstanceInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceName) {
		return false
	}
	return true
}

func (p *InspectionInstanceInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionInstanceInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceName, src) != 0 {
		return false
	}
	return true
}

type InspectionTaskInfo struct {
	TaskId string `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
}

func NewInspectionTaskInfo() *InspectionTaskInfo {
	return &InspectionTaskInfo{}
}

func (p *InspectionTaskInfo) InitDefault() {
}

func (p *InspectionTaskInfo) GetTaskId() (v string) {
	return p.TaskId
}
func (p *InspectionTaskInfo) SetTaskId(val string) {
	p.TaskId = val
}

var fieldIDToName_InspectionTaskInfo = map[int16]string{
	1: "TaskId",
}

func (p *InspectionTaskInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionTaskInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionTaskInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionTaskInfo[fieldId]))
}

func (p *InspectionTaskInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}

func (p *InspectionTaskInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionTaskInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionTaskInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionTaskInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionTaskInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionTaskInfo(%+v)", *p)

}

func (p *InspectionTaskInfo) DeepEqual(ano *InspectionTaskInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	return true
}

func (p *InspectionTaskInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}

type CreateManualInspectionReq struct {
	InstanceType InstanceType              `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	RegionId     string                    `thrift:"RegionId,2,required" frugal:"2,required,string" json:"RegionId"`
	InstanceInfo []*InspectionInstanceInfo `thrift:"InstanceInfo,3,required" frugal:"3,required,list<InspectionInstanceInfo>" json:"InstanceInfo"`
	StartTime    string                    `thrift:"StartTime,4,required" frugal:"4,required,string" json:"StartTime"`
	EndTime      string                    `thrift:"EndTime,5,required" frugal:"5,required,string" json:"EndTime"`
	CreateTime   string                    `thrift:"CreateTime,6,required" frugal:"6,required,string" json:"CreateTime"`
}

func NewCreateManualInspectionReq() *CreateManualInspectionReq {
	return &CreateManualInspectionReq{}
}

func (p *CreateManualInspectionReq) InitDefault() {
}

func (p *CreateManualInspectionReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *CreateManualInspectionReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *CreateManualInspectionReq) GetInstanceInfo() (v []*InspectionInstanceInfo) {
	return p.InstanceInfo
}

func (p *CreateManualInspectionReq) GetStartTime() (v string) {
	return p.StartTime
}

func (p *CreateManualInspectionReq) GetEndTime() (v string) {
	return p.EndTime
}

func (p *CreateManualInspectionReq) GetCreateTime() (v string) {
	return p.CreateTime
}
func (p *CreateManualInspectionReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *CreateManualInspectionReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *CreateManualInspectionReq) SetInstanceInfo(val []*InspectionInstanceInfo) {
	p.InstanceInfo = val
}
func (p *CreateManualInspectionReq) SetStartTime(val string) {
	p.StartTime = val
}
func (p *CreateManualInspectionReq) SetEndTime(val string) {
	p.EndTime = val
}
func (p *CreateManualInspectionReq) SetCreateTime(val string) {
	p.CreateTime = val
}

var fieldIDToName_CreateManualInspectionReq = map[int16]string{
	1: "InstanceType",
	2: "RegionId",
	3: "InstanceInfo",
	4: "StartTime",
	5: "EndTime",
	6: "CreateTime",
}

func (p *CreateManualInspectionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateManualInspectionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetRegionId bool = false
	var issetInstanceInfo bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetCreateTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetRegionId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceInfo {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateManualInspectionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateManualInspectionReq[fieldId]))
}

func (p *CreateManualInspectionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *CreateManualInspectionReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *CreateManualInspectionReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionInstanceInfo, 0, size)
	values := make([]InspectionInstanceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceInfo = _field
	return nil
}
func (p *CreateManualInspectionReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *CreateManualInspectionReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *CreateManualInspectionReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}

func (p *CreateManualInspectionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateManualInspectionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateManualInspectionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateManualInspectionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateManualInspectionReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateManualInspectionReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceInfo", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InstanceInfo)); err != nil {
		return err
	}
	for _, v := range p.InstanceInfo {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateManualInspectionReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateManualInspectionReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateManualInspectionReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateManualInspectionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateManualInspectionReq(%+v)", *p)

}

func (p *CreateManualInspectionReq) DeepEqual(ano *CreateManualInspectionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceInfo) {
		return false
	}
	if !p.Field4DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.CreateTime) {
		return false
	}
	return true
}

func (p *CreateManualInspectionReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *CreateManualInspectionReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateManualInspectionReq) Field3DeepEqual(src []*InspectionInstanceInfo) bool {

	if len(p.InstanceInfo) != len(src) {
		return false
	}
	for i, v := range p.InstanceInfo {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *CreateManualInspectionReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *CreateManualInspectionReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}
func (p *CreateManualInspectionReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}

type CreateManualInspectionResp struct {
	TaskInfos []*InspectionTaskInfo `thrift:"TaskInfos,1,required" frugal:"1,required,list<InspectionTaskInfo>" json:"TaskInfos"`
}

func NewCreateManualInspectionResp() *CreateManualInspectionResp {
	return &CreateManualInspectionResp{}
}

func (p *CreateManualInspectionResp) InitDefault() {
}

func (p *CreateManualInspectionResp) GetTaskInfos() (v []*InspectionTaskInfo) {
	return p.TaskInfos
}
func (p *CreateManualInspectionResp) SetTaskInfos(val []*InspectionTaskInfo) {
	p.TaskInfos = val
}

var fieldIDToName_CreateManualInspectionResp = map[int16]string{
	1: "TaskInfos",
}

func (p *CreateManualInspectionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateManualInspectionResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskInfos bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskInfos {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateManualInspectionResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateManualInspectionResp[fieldId]))
}

func (p *CreateManualInspectionResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionTaskInfo, 0, size)
	values := make([]InspectionTaskInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TaskInfos = _field
	return nil
}

func (p *CreateManualInspectionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateManualInspectionResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateManualInspectionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateManualInspectionResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TaskInfos)); err != nil {
		return err
	}
	for _, v := range p.TaskInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateManualInspectionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateManualInspectionResp(%+v)", *p)

}

func (p *CreateManualInspectionResp) DeepEqual(ano *CreateManualInspectionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskInfos) {
		return false
	}
	return true
}

func (p *CreateManualInspectionResp) Field1DeepEqual(src []*InspectionTaskInfo) bool {

	if len(p.TaskInfos) != len(src) {
		return false
	}
	for i, v := range p.TaskInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeDBInspectionReportReq struct {
	TaskID     string  `thrift:"TaskID,1,required" frugal:"1,required,string" json:"TaskID"`
	NodeTaskID *string `thrift:"NodeTaskID,2,optional" frugal:"2,optional,string" json:"NodeTaskID,omitempty"`
}

func NewDescribeDBInspectionReportReq() *DescribeDBInspectionReportReq {
	return &DescribeDBInspectionReportReq{}
}

func (p *DescribeDBInspectionReportReq) InitDefault() {
}

func (p *DescribeDBInspectionReportReq) GetTaskID() (v string) {
	return p.TaskID
}

var DescribeDBInspectionReportReq_NodeTaskID_DEFAULT string

func (p *DescribeDBInspectionReportReq) GetNodeTaskID() (v string) {
	if !p.IsSetNodeTaskID() {
		return DescribeDBInspectionReportReq_NodeTaskID_DEFAULT
	}
	return *p.NodeTaskID
}
func (p *DescribeDBInspectionReportReq) SetTaskID(val string) {
	p.TaskID = val
}
func (p *DescribeDBInspectionReportReq) SetNodeTaskID(val *string) {
	p.NodeTaskID = val
}

var fieldIDToName_DescribeDBInspectionReportReq = map[int16]string{
	1: "TaskID",
	2: "NodeTaskID",
}

func (p *DescribeDBInspectionReportReq) IsSetNodeTaskID() bool {
	return p.NodeTaskID != nil
}

func (p *DescribeDBInspectionReportReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionReportReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInspectionReportReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInspectionReportReq[fieldId]))
}

func (p *DescribeDBInspectionReportReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskID = _field
	return nil
}
func (p *DescribeDBInspectionReportReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeTaskID = _field
	return nil
}

func (p *DescribeDBInspectionReportReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionReportReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInspectionReportReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInspectionReportReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInspectionReportReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeTaskID() {
		if err = oprot.WriteFieldBegin("NodeTaskID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeTaskID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInspectionReportReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInspectionReportReq(%+v)", *p)

}

func (p *DescribeDBInspectionReportReq) DeepEqual(ano *DescribeDBInspectionReportReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskID) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeTaskID) {
		return false
	}
	return true
}

func (p *DescribeDBInspectionReportReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInspectionReportReq) Field2DeepEqual(src *string) bool {

	if p.NodeTaskID == src {
		return true
	} else if p.NodeTaskID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeTaskID, *src) != 0 {
		return false
	}
	return true
}

type InspectionNodeReport struct {
	NodeInfoObject                   *NodeInfoObject                   `thrift:"NodeInfoObject,1,required" frugal:"1,required,NodeInfoObject" json:"NodeInfoObject"`
	Agg                              *InspectAgg                       `thrift:"Agg,2,required" frugal:"2,required,InspectAgg" json:"Agg"`
	ScoreDetail                      []*MetricScore                    `thrift:"ScoreDetail,3,required" frugal:"3,required,list<MetricScore>" json:"ScoreDetail"`
	DataMetric                       []*InspectionDataMetric           `thrift:"DataMetric,4,required" frugal:"4,required,list<InspectionDataMetric>" json:"DataMetric"`
	SlowlogItems                     []*InspectionSlowLog              `thrift:"SlowlogItems,5,required" frugal:"5,required,list<InspectionSlowLog>" json:"SlowlogItems"`
	DescribeTableSpaceAutoIncrResp   *DescribeTableSpaceAutoIncrResp   `thrift:"DescribeTableSpaceAutoIncrResp,6,optional" frugal:"6,optional,DescribeTableSpaceAutoIncrResp" json:"DescribeTableSpaceAutoIncrResp,omitempty"`
	DescribeTableSpaceResp           *DescribeTableSpaceResp           `thrift:"DescribeTableSpaceResp,7,optional" frugal:"7,optional,DescribeTableSpaceResp" json:"DescribeTableSpaceResp,omitempty"`
	DescribeFullSqlStatusResp        *DescribeFullSqlStatusResp        `thrift:"DescribeFullSqlStatusResp,8,optional" frugal:"8,optional,DescribeFullSqlStatusResp" json:"DescribeFullSqlStatusResp,omitempty"`
	DescribeAggregateDialogsResp     *DescribeAggregateDialogsResp     `thrift:"DescribeAggregateDialogsResp,9,optional" frugal:"9,optional,DescribeAggregateDialogsResp" json:"DescribeAggregateDialogsResp,omitempty"`
	DescribeSqlTemplatesContrastResp *DescribeSqlTemplatesContrastResp `thrift:"DescribeSqlTemplatesContrastResp,10,optional" frugal:"10,optional,DescribeSqlTemplatesContrastResp" json:"DescribeSqlTemplatesContrastResp,omitempty"`
	InspectErr                       []*InspectErr                     `thrift:"InspectErr,200,required" frugal:"200,required,list<InspectErr>" json:"InspectErr"`
}

func NewInspectionNodeReport() *InspectionNodeReport {
	return &InspectionNodeReport{}
}

func (p *InspectionNodeReport) InitDefault() {
}

var InspectionNodeReport_NodeInfoObject_DEFAULT *NodeInfoObject

func (p *InspectionNodeReport) GetNodeInfoObject() (v *NodeInfoObject) {
	if !p.IsSetNodeInfoObject() {
		return InspectionNodeReport_NodeInfoObject_DEFAULT
	}
	return p.NodeInfoObject
}

var InspectionNodeReport_Agg_DEFAULT *InspectAgg

func (p *InspectionNodeReport) GetAgg() (v *InspectAgg) {
	if !p.IsSetAgg() {
		return InspectionNodeReport_Agg_DEFAULT
	}
	return p.Agg
}

func (p *InspectionNodeReport) GetScoreDetail() (v []*MetricScore) {
	return p.ScoreDetail
}

func (p *InspectionNodeReport) GetDataMetric() (v []*InspectionDataMetric) {
	return p.DataMetric
}

func (p *InspectionNodeReport) GetSlowlogItems() (v []*InspectionSlowLog) {
	return p.SlowlogItems
}

var InspectionNodeReport_DescribeTableSpaceAutoIncrResp_DEFAULT *DescribeTableSpaceAutoIncrResp

func (p *InspectionNodeReport) GetDescribeTableSpaceAutoIncrResp() (v *DescribeTableSpaceAutoIncrResp) {
	if !p.IsSetDescribeTableSpaceAutoIncrResp() {
		return InspectionNodeReport_DescribeTableSpaceAutoIncrResp_DEFAULT
	}
	return p.DescribeTableSpaceAutoIncrResp
}

var InspectionNodeReport_DescribeTableSpaceResp_DEFAULT *DescribeTableSpaceResp

func (p *InspectionNodeReport) GetDescribeTableSpaceResp() (v *DescribeTableSpaceResp) {
	if !p.IsSetDescribeTableSpaceResp() {
		return InspectionNodeReport_DescribeTableSpaceResp_DEFAULT
	}
	return p.DescribeTableSpaceResp
}

var InspectionNodeReport_DescribeFullSqlStatusResp_DEFAULT *DescribeFullSqlStatusResp

func (p *InspectionNodeReport) GetDescribeFullSqlStatusResp() (v *DescribeFullSqlStatusResp) {
	if !p.IsSetDescribeFullSqlStatusResp() {
		return InspectionNodeReport_DescribeFullSqlStatusResp_DEFAULT
	}
	return p.DescribeFullSqlStatusResp
}

var InspectionNodeReport_DescribeAggregateDialogsResp_DEFAULT *DescribeAggregateDialogsResp

func (p *InspectionNodeReport) GetDescribeAggregateDialogsResp() (v *DescribeAggregateDialogsResp) {
	if !p.IsSetDescribeAggregateDialogsResp() {
		return InspectionNodeReport_DescribeAggregateDialogsResp_DEFAULT
	}
	return p.DescribeAggregateDialogsResp
}

var InspectionNodeReport_DescribeSqlTemplatesContrastResp_DEFAULT *DescribeSqlTemplatesContrastResp

func (p *InspectionNodeReport) GetDescribeSqlTemplatesContrastResp() (v *DescribeSqlTemplatesContrastResp) {
	if !p.IsSetDescribeSqlTemplatesContrastResp() {
		return InspectionNodeReport_DescribeSqlTemplatesContrastResp_DEFAULT
	}
	return p.DescribeSqlTemplatesContrastResp
}

func (p *InspectionNodeReport) GetInspectErr() (v []*InspectErr) {
	return p.InspectErr
}
func (p *InspectionNodeReport) SetNodeInfoObject(val *NodeInfoObject) {
	p.NodeInfoObject = val
}
func (p *InspectionNodeReport) SetAgg(val *InspectAgg) {
	p.Agg = val
}
func (p *InspectionNodeReport) SetScoreDetail(val []*MetricScore) {
	p.ScoreDetail = val
}
func (p *InspectionNodeReport) SetDataMetric(val []*InspectionDataMetric) {
	p.DataMetric = val
}
func (p *InspectionNodeReport) SetSlowlogItems(val []*InspectionSlowLog) {
	p.SlowlogItems = val
}
func (p *InspectionNodeReport) SetDescribeTableSpaceAutoIncrResp(val *DescribeTableSpaceAutoIncrResp) {
	p.DescribeTableSpaceAutoIncrResp = val
}
func (p *InspectionNodeReport) SetDescribeTableSpaceResp(val *DescribeTableSpaceResp) {
	p.DescribeTableSpaceResp = val
}
func (p *InspectionNodeReport) SetDescribeFullSqlStatusResp(val *DescribeFullSqlStatusResp) {
	p.DescribeFullSqlStatusResp = val
}
func (p *InspectionNodeReport) SetDescribeAggregateDialogsResp(val *DescribeAggregateDialogsResp) {
	p.DescribeAggregateDialogsResp = val
}
func (p *InspectionNodeReport) SetDescribeSqlTemplatesContrastResp(val *DescribeSqlTemplatesContrastResp) {
	p.DescribeSqlTemplatesContrastResp = val
}
func (p *InspectionNodeReport) SetInspectErr(val []*InspectErr) {
	p.InspectErr = val
}

var fieldIDToName_InspectionNodeReport = map[int16]string{
	1:   "NodeInfoObject",
	2:   "Agg",
	3:   "ScoreDetail",
	4:   "DataMetric",
	5:   "SlowlogItems",
	6:   "DescribeTableSpaceAutoIncrResp",
	7:   "DescribeTableSpaceResp",
	8:   "DescribeFullSqlStatusResp",
	9:   "DescribeAggregateDialogsResp",
	10:  "DescribeSqlTemplatesContrastResp",
	200: "InspectErr",
}

func (p *InspectionNodeReport) IsSetNodeInfoObject() bool {
	return p.NodeInfoObject != nil
}

func (p *InspectionNodeReport) IsSetAgg() bool {
	return p.Agg != nil
}

func (p *InspectionNodeReport) IsSetDescribeTableSpaceAutoIncrResp() bool {
	return p.DescribeTableSpaceAutoIncrResp != nil
}

func (p *InspectionNodeReport) IsSetDescribeTableSpaceResp() bool {
	return p.DescribeTableSpaceResp != nil
}

func (p *InspectionNodeReport) IsSetDescribeFullSqlStatusResp() bool {
	return p.DescribeFullSqlStatusResp != nil
}

func (p *InspectionNodeReport) IsSetDescribeAggregateDialogsResp() bool {
	return p.DescribeAggregateDialogsResp != nil
}

func (p *InspectionNodeReport) IsSetDescribeSqlTemplatesContrastResp() bool {
	return p.DescribeSqlTemplatesContrastResp != nil
}

func (p *InspectionNodeReport) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionNodeReport")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetNodeInfoObject bool = false
	var issetAgg bool = false
	var issetScoreDetail bool = false
	var issetDataMetric bool = false
	var issetSlowlogItems bool = false
	var issetInspectErr bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeInfoObject = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAgg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetScoreDetail = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataMetric = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSlowlogItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectErr = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetNodeInfoObject {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAgg {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetScoreDetail {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDataMetric {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSlowlogItems {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetInspectErr {
		fieldId = 200
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionNodeReport[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionNodeReport[fieldId]))
}

func (p *InspectionNodeReport) ReadField1(iprot thrift.TProtocol) error {
	_field := NewNodeInfoObject()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.NodeInfoObject = _field
	return nil
}
func (p *InspectionNodeReport) ReadField2(iprot thrift.TProtocol) error {
	_field := NewInspectAgg()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Agg = _field
	return nil
}
func (p *InspectionNodeReport) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MetricScore, 0, size)
	values := make([]MetricScore, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ScoreDetail = _field
	return nil
}
func (p *InspectionNodeReport) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionDataMetric, 0, size)
	values := make([]InspectionDataMetric, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DataMetric = _field
	return nil
}
func (p *InspectionNodeReport) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionSlowLog, 0, size)
	values := make([]InspectionSlowLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SlowlogItems = _field
	return nil
}
func (p *InspectionNodeReport) ReadField6(iprot thrift.TProtocol) error {
	_field := NewDescribeTableSpaceAutoIncrResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DescribeTableSpaceAutoIncrResp = _field
	return nil
}
func (p *InspectionNodeReport) ReadField7(iprot thrift.TProtocol) error {
	_field := NewDescribeTableSpaceResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DescribeTableSpaceResp = _field
	return nil
}
func (p *InspectionNodeReport) ReadField8(iprot thrift.TProtocol) error {
	_field := NewDescribeFullSqlStatusResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DescribeFullSqlStatusResp = _field
	return nil
}
func (p *InspectionNodeReport) ReadField9(iprot thrift.TProtocol) error {
	_field := NewDescribeAggregateDialogsResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DescribeAggregateDialogsResp = _field
	return nil
}
func (p *InspectionNodeReport) ReadField10(iprot thrift.TProtocol) error {
	_field := NewDescribeSqlTemplatesContrastResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DescribeSqlTemplatesContrastResp = _field
	return nil
}
func (p *InspectionNodeReport) ReadField200(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectErr, 0, size)
	values := make([]InspectErr, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InspectErr = _field
	return nil
}

func (p *InspectionNodeReport) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionNodeReport")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionNodeReport"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionNodeReport) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeInfoObject", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.NodeInfoObject.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionNodeReport) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Agg", thrift.STRUCT, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Agg.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionNodeReport) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ScoreDetail", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ScoreDetail)); err != nil {
		return err
	}
	for _, v := range p.ScoreDetail {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionNodeReport) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataMetric", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DataMetric)); err != nil {
		return err
	}
	for _, v := range p.DataMetric {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InspectionNodeReport) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SlowlogItems", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SlowlogItems)); err != nil {
		return err
	}
	for _, v := range p.SlowlogItems {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InspectionNodeReport) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeTableSpaceAutoIncrResp() {
		if err = oprot.WriteFieldBegin("DescribeTableSpaceAutoIncrResp", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DescribeTableSpaceAutoIncrResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InspectionNodeReport) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeTableSpaceResp() {
		if err = oprot.WriteFieldBegin("DescribeTableSpaceResp", thrift.STRUCT, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DescribeTableSpaceResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *InspectionNodeReport) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeFullSqlStatusResp() {
		if err = oprot.WriteFieldBegin("DescribeFullSqlStatusResp", thrift.STRUCT, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DescribeFullSqlStatusResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *InspectionNodeReport) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeAggregateDialogsResp() {
		if err = oprot.WriteFieldBegin("DescribeAggregateDialogsResp", thrift.STRUCT, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DescribeAggregateDialogsResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *InspectionNodeReport) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeSqlTemplatesContrastResp() {
		if err = oprot.WriteFieldBegin("DescribeSqlTemplatesContrastResp", thrift.STRUCT, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DescribeSqlTemplatesContrastResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *InspectionNodeReport) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectErr", thrift.LIST, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InspectErr)); err != nil {
		return err
	}
	for _, v := range p.InspectErr {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *InspectionNodeReport) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionNodeReport(%+v)", *p)

}

func (p *InspectionNodeReport) DeepEqual(ano *InspectionNodeReport) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.NodeInfoObject) {
		return false
	}
	if !p.Field2DeepEqual(ano.Agg) {
		return false
	}
	if !p.Field3DeepEqual(ano.ScoreDetail) {
		return false
	}
	if !p.Field4DeepEqual(ano.DataMetric) {
		return false
	}
	if !p.Field5DeepEqual(ano.SlowlogItems) {
		return false
	}
	if !p.Field6DeepEqual(ano.DescribeTableSpaceAutoIncrResp) {
		return false
	}
	if !p.Field7DeepEqual(ano.DescribeTableSpaceResp) {
		return false
	}
	if !p.Field8DeepEqual(ano.DescribeFullSqlStatusResp) {
		return false
	}
	if !p.Field9DeepEqual(ano.DescribeAggregateDialogsResp) {
		return false
	}
	if !p.Field10DeepEqual(ano.DescribeSqlTemplatesContrastResp) {
		return false
	}
	if !p.Field200DeepEqual(ano.InspectErr) {
		return false
	}
	return true
}

func (p *InspectionNodeReport) Field1DeepEqual(src *NodeInfoObject) bool {

	if !p.NodeInfoObject.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InspectionNodeReport) Field2DeepEqual(src *InspectAgg) bool {

	if !p.Agg.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InspectionNodeReport) Field3DeepEqual(src []*MetricScore) bool {

	if len(p.ScoreDetail) != len(src) {
		return false
	}
	for i, v := range p.ScoreDetail {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *InspectionNodeReport) Field4DeepEqual(src []*InspectionDataMetric) bool {

	if len(p.DataMetric) != len(src) {
		return false
	}
	for i, v := range p.DataMetric {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *InspectionNodeReport) Field5DeepEqual(src []*InspectionSlowLog) bool {

	if len(p.SlowlogItems) != len(src) {
		return false
	}
	for i, v := range p.SlowlogItems {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *InspectionNodeReport) Field6DeepEqual(src *DescribeTableSpaceAutoIncrResp) bool {

	if !p.DescribeTableSpaceAutoIncrResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InspectionNodeReport) Field7DeepEqual(src *DescribeTableSpaceResp) bool {

	if !p.DescribeTableSpaceResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InspectionNodeReport) Field8DeepEqual(src *DescribeFullSqlStatusResp) bool {

	if !p.DescribeFullSqlStatusResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InspectionNodeReport) Field9DeepEqual(src *DescribeAggregateDialogsResp) bool {

	if !p.DescribeAggregateDialogsResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InspectionNodeReport) Field10DeepEqual(src *DescribeSqlTemplatesContrastResp) bool {

	if !p.DescribeSqlTemplatesContrastResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InspectionNodeReport) Field200DeepEqual(src []*InspectErr) bool {

	if len(p.InspectErr) != len(src) {
		return false
	}
	for i, v := range p.InspectErr {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeDBInspectionReportResp struct {
	InstanceInfo                             *InspectionInstanceBasicInfo      `thrift:"InstanceInfo,1,required" frugal:"1,required,InspectionInstanceBasicInfo" json:"InstanceInfo"`
	HealthScore                              int32                             `thrift:"HealthScore,2,required" frugal:"2,required,i32" json:"HealthScore"`
	ScoreDetail                              []*MetricScore                    `thrift:"ScoreDetail,3,required" frugal:"3,required,list<MetricScore>" json:"ScoreDetail"`
	DataMetric                               []*InspectionDataMetric           `thrift:"DataMetric,4,required" frugal:"4,required,list<InspectionDataMetric>" json:"DataMetric"`
	SlowlogItems                             []*InspectionSlowLog              `thrift:"SlowlogItems,5,required" frugal:"5,required,list<InspectionSlowLog>" json:"SlowlogItems"`
	RedisInspectionBigKeysItemItemsValueSize []*InspectionBigKeysItem          `thrift:"RedisInspectionBigKeysItemItemsValueSize,6,optional" frugal:"6,optional,list<InspectionBigKeysItem>" json:"RedisInspectionBigKeysItemItemsValueSize,omitempty"`
	RedisInspectionBigKeysItemItemsValueLen  []*InspectionBigKeysItem          `thrift:"RedisInspectionBigKeysItemItemsValueLen,7,optional" frugal:"7,optional,list<InspectionBigKeysItem>" json:"RedisInspectionBigKeysItemItemsValueLen,omitempty"`
	RedisInspectionHotKeysItemItems          []*InspectionHotKeysItem          `thrift:"RedisInspectionHotKeysItemItems,8,optional" frugal:"8,optional,list<InspectionHotKeysItem>" json:"RedisInspectionHotKeysItemItems,omitempty"`
	InspectionNodeItems                      []*InspectionNodeItem             `thrift:"InspectionNodeItems,10,required" frugal:"10,required,list<InspectionNodeItem>" json:"InspectionNodeItems"`
	InspectionNodeReport                     *InspectionNodeReport             `thrift:"InspectionNodeReport,11,optional" frugal:"11,optional,InspectionNodeReport" json:"InspectionNodeReport,omitempty"`
	DescribeTableSpaceAutoIncrResp           *DescribeTableSpaceAutoIncrResp   `thrift:"DescribeTableSpaceAutoIncrResp,12,optional" frugal:"12,optional,DescribeTableSpaceAutoIncrResp" json:"DescribeTableSpaceAutoIncrResp,omitempty"`
	DescribeTableSpaceResp                   *DescribeTableSpaceResp           `thrift:"DescribeTableSpaceResp,13,optional" frugal:"13,optional,DescribeTableSpaceResp" json:"DescribeTableSpaceResp,omitempty"`
	DescribeFullSqlStatusResp                *DescribeFullSqlStatusResp        `thrift:"DescribeFullSqlStatusResp,14,optional" frugal:"14,optional,DescribeFullSqlStatusResp" json:"DescribeFullSqlStatusResp,omitempty"`
	DescribeAggregateDialogsResp             *DescribeAggregateDialogsResp     `thrift:"DescribeAggregateDialogsResp,15,optional" frugal:"15,optional,DescribeAggregateDialogsResp" json:"DescribeAggregateDialogsResp,omitempty"`
	DescribeSqlTemplatesContrastResp         *DescribeSqlTemplatesContrastResp `thrift:"DescribeSqlTemplatesContrastResp,16,optional" frugal:"16,optional,DescribeSqlTemplatesContrastResp" json:"DescribeSqlTemplatesContrastResp,omitempty"`
	InspectErr                               []*InspectErr                     `thrift:"InspectErr,200,required" frugal:"200,required,list<InspectErr>" json:"InspectErr"`
}

func NewDescribeDBInspectionReportResp() *DescribeDBInspectionReportResp {
	return &DescribeDBInspectionReportResp{}
}

func (p *DescribeDBInspectionReportResp) InitDefault() {
}

var DescribeDBInspectionReportResp_InstanceInfo_DEFAULT *InspectionInstanceBasicInfo

func (p *DescribeDBInspectionReportResp) GetInstanceInfo() (v *InspectionInstanceBasicInfo) {
	if !p.IsSetInstanceInfo() {
		return DescribeDBInspectionReportResp_InstanceInfo_DEFAULT
	}
	return p.InstanceInfo
}

func (p *DescribeDBInspectionReportResp) GetHealthScore() (v int32) {
	return p.HealthScore
}

func (p *DescribeDBInspectionReportResp) GetScoreDetail() (v []*MetricScore) {
	return p.ScoreDetail
}

func (p *DescribeDBInspectionReportResp) GetDataMetric() (v []*InspectionDataMetric) {
	return p.DataMetric
}

func (p *DescribeDBInspectionReportResp) GetSlowlogItems() (v []*InspectionSlowLog) {
	return p.SlowlogItems
}

var DescribeDBInspectionReportResp_RedisInspectionBigKeysItemItemsValueSize_DEFAULT []*InspectionBigKeysItem

func (p *DescribeDBInspectionReportResp) GetRedisInspectionBigKeysItemItemsValueSize() (v []*InspectionBigKeysItem) {
	if !p.IsSetRedisInspectionBigKeysItemItemsValueSize() {
		return DescribeDBInspectionReportResp_RedisInspectionBigKeysItemItemsValueSize_DEFAULT
	}
	return p.RedisInspectionBigKeysItemItemsValueSize
}

var DescribeDBInspectionReportResp_RedisInspectionBigKeysItemItemsValueLen_DEFAULT []*InspectionBigKeysItem

func (p *DescribeDBInspectionReportResp) GetRedisInspectionBigKeysItemItemsValueLen() (v []*InspectionBigKeysItem) {
	if !p.IsSetRedisInspectionBigKeysItemItemsValueLen() {
		return DescribeDBInspectionReportResp_RedisInspectionBigKeysItemItemsValueLen_DEFAULT
	}
	return p.RedisInspectionBigKeysItemItemsValueLen
}

var DescribeDBInspectionReportResp_RedisInspectionHotKeysItemItems_DEFAULT []*InspectionHotKeysItem

func (p *DescribeDBInspectionReportResp) GetRedisInspectionHotKeysItemItems() (v []*InspectionHotKeysItem) {
	if !p.IsSetRedisInspectionHotKeysItemItems() {
		return DescribeDBInspectionReportResp_RedisInspectionHotKeysItemItems_DEFAULT
	}
	return p.RedisInspectionHotKeysItemItems
}

func (p *DescribeDBInspectionReportResp) GetInspectionNodeItems() (v []*InspectionNodeItem) {
	return p.InspectionNodeItems
}

var DescribeDBInspectionReportResp_InspectionNodeReport_DEFAULT *InspectionNodeReport

func (p *DescribeDBInspectionReportResp) GetInspectionNodeReport() (v *InspectionNodeReport) {
	if !p.IsSetInspectionNodeReport() {
		return DescribeDBInspectionReportResp_InspectionNodeReport_DEFAULT
	}
	return p.InspectionNodeReport
}

var DescribeDBInspectionReportResp_DescribeTableSpaceAutoIncrResp_DEFAULT *DescribeTableSpaceAutoIncrResp

func (p *DescribeDBInspectionReportResp) GetDescribeTableSpaceAutoIncrResp() (v *DescribeTableSpaceAutoIncrResp) {
	if !p.IsSetDescribeTableSpaceAutoIncrResp() {
		return DescribeDBInspectionReportResp_DescribeTableSpaceAutoIncrResp_DEFAULT
	}
	return p.DescribeTableSpaceAutoIncrResp
}

var DescribeDBInspectionReportResp_DescribeTableSpaceResp_DEFAULT *DescribeTableSpaceResp

func (p *DescribeDBInspectionReportResp) GetDescribeTableSpaceResp() (v *DescribeTableSpaceResp) {
	if !p.IsSetDescribeTableSpaceResp() {
		return DescribeDBInspectionReportResp_DescribeTableSpaceResp_DEFAULT
	}
	return p.DescribeTableSpaceResp
}

var DescribeDBInspectionReportResp_DescribeFullSqlStatusResp_DEFAULT *DescribeFullSqlStatusResp

func (p *DescribeDBInspectionReportResp) GetDescribeFullSqlStatusResp() (v *DescribeFullSqlStatusResp) {
	if !p.IsSetDescribeFullSqlStatusResp() {
		return DescribeDBInspectionReportResp_DescribeFullSqlStatusResp_DEFAULT
	}
	return p.DescribeFullSqlStatusResp
}

var DescribeDBInspectionReportResp_DescribeAggregateDialogsResp_DEFAULT *DescribeAggregateDialogsResp

func (p *DescribeDBInspectionReportResp) GetDescribeAggregateDialogsResp() (v *DescribeAggregateDialogsResp) {
	if !p.IsSetDescribeAggregateDialogsResp() {
		return DescribeDBInspectionReportResp_DescribeAggregateDialogsResp_DEFAULT
	}
	return p.DescribeAggregateDialogsResp
}

var DescribeDBInspectionReportResp_DescribeSqlTemplatesContrastResp_DEFAULT *DescribeSqlTemplatesContrastResp

func (p *DescribeDBInspectionReportResp) GetDescribeSqlTemplatesContrastResp() (v *DescribeSqlTemplatesContrastResp) {
	if !p.IsSetDescribeSqlTemplatesContrastResp() {
		return DescribeDBInspectionReportResp_DescribeSqlTemplatesContrastResp_DEFAULT
	}
	return p.DescribeSqlTemplatesContrastResp
}

func (p *DescribeDBInspectionReportResp) GetInspectErr() (v []*InspectErr) {
	return p.InspectErr
}
func (p *DescribeDBInspectionReportResp) SetInstanceInfo(val *InspectionInstanceBasicInfo) {
	p.InstanceInfo = val
}
func (p *DescribeDBInspectionReportResp) SetHealthScore(val int32) {
	p.HealthScore = val
}
func (p *DescribeDBInspectionReportResp) SetScoreDetail(val []*MetricScore) {
	p.ScoreDetail = val
}
func (p *DescribeDBInspectionReportResp) SetDataMetric(val []*InspectionDataMetric) {
	p.DataMetric = val
}
func (p *DescribeDBInspectionReportResp) SetSlowlogItems(val []*InspectionSlowLog) {
	p.SlowlogItems = val
}
func (p *DescribeDBInspectionReportResp) SetRedisInspectionBigKeysItemItemsValueSize(val []*InspectionBigKeysItem) {
	p.RedisInspectionBigKeysItemItemsValueSize = val
}
func (p *DescribeDBInspectionReportResp) SetRedisInspectionBigKeysItemItemsValueLen(val []*InspectionBigKeysItem) {
	p.RedisInspectionBigKeysItemItemsValueLen = val
}
func (p *DescribeDBInspectionReportResp) SetRedisInspectionHotKeysItemItems(val []*InspectionHotKeysItem) {
	p.RedisInspectionHotKeysItemItems = val
}
func (p *DescribeDBInspectionReportResp) SetInspectionNodeItems(val []*InspectionNodeItem) {
	p.InspectionNodeItems = val
}
func (p *DescribeDBInspectionReportResp) SetInspectionNodeReport(val *InspectionNodeReport) {
	p.InspectionNodeReport = val
}
func (p *DescribeDBInspectionReportResp) SetDescribeTableSpaceAutoIncrResp(val *DescribeTableSpaceAutoIncrResp) {
	p.DescribeTableSpaceAutoIncrResp = val
}
func (p *DescribeDBInspectionReportResp) SetDescribeTableSpaceResp(val *DescribeTableSpaceResp) {
	p.DescribeTableSpaceResp = val
}
func (p *DescribeDBInspectionReportResp) SetDescribeFullSqlStatusResp(val *DescribeFullSqlStatusResp) {
	p.DescribeFullSqlStatusResp = val
}
func (p *DescribeDBInspectionReportResp) SetDescribeAggregateDialogsResp(val *DescribeAggregateDialogsResp) {
	p.DescribeAggregateDialogsResp = val
}
func (p *DescribeDBInspectionReportResp) SetDescribeSqlTemplatesContrastResp(val *DescribeSqlTemplatesContrastResp) {
	p.DescribeSqlTemplatesContrastResp = val
}
func (p *DescribeDBInspectionReportResp) SetInspectErr(val []*InspectErr) {
	p.InspectErr = val
}

var fieldIDToName_DescribeDBInspectionReportResp = map[int16]string{
	1:   "InstanceInfo",
	2:   "HealthScore",
	3:   "ScoreDetail",
	4:   "DataMetric",
	5:   "SlowlogItems",
	6:   "RedisInspectionBigKeysItemItemsValueSize",
	7:   "RedisInspectionBigKeysItemItemsValueLen",
	8:   "RedisInspectionHotKeysItemItems",
	10:  "InspectionNodeItems",
	11:  "InspectionNodeReport",
	12:  "DescribeTableSpaceAutoIncrResp",
	13:  "DescribeTableSpaceResp",
	14:  "DescribeFullSqlStatusResp",
	15:  "DescribeAggregateDialogsResp",
	16:  "DescribeSqlTemplatesContrastResp",
	200: "InspectErr",
}

func (p *DescribeDBInspectionReportResp) IsSetInstanceInfo() bool {
	return p.InstanceInfo != nil
}

func (p *DescribeDBInspectionReportResp) IsSetRedisInspectionBigKeysItemItemsValueSize() bool {
	return p.RedisInspectionBigKeysItemItemsValueSize != nil
}

func (p *DescribeDBInspectionReportResp) IsSetRedisInspectionBigKeysItemItemsValueLen() bool {
	return p.RedisInspectionBigKeysItemItemsValueLen != nil
}

func (p *DescribeDBInspectionReportResp) IsSetRedisInspectionHotKeysItemItems() bool {
	return p.RedisInspectionHotKeysItemItems != nil
}

func (p *DescribeDBInspectionReportResp) IsSetInspectionNodeReport() bool {
	return p.InspectionNodeReport != nil
}

func (p *DescribeDBInspectionReportResp) IsSetDescribeTableSpaceAutoIncrResp() bool {
	return p.DescribeTableSpaceAutoIncrResp != nil
}

func (p *DescribeDBInspectionReportResp) IsSetDescribeTableSpaceResp() bool {
	return p.DescribeTableSpaceResp != nil
}

func (p *DescribeDBInspectionReportResp) IsSetDescribeFullSqlStatusResp() bool {
	return p.DescribeFullSqlStatusResp != nil
}

func (p *DescribeDBInspectionReportResp) IsSetDescribeAggregateDialogsResp() bool {
	return p.DescribeAggregateDialogsResp != nil
}

func (p *DescribeDBInspectionReportResp) IsSetDescribeSqlTemplatesContrastResp() bool {
	return p.DescribeSqlTemplatesContrastResp != nil
}

func (p *DescribeDBInspectionReportResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionReportResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceInfo bool = false
	var issetHealthScore bool = false
	var issetScoreDetail bool = false
	var issetDataMetric bool = false
	var issetSlowlogItems bool = false
	var issetInspectionNodeItems bool = false
	var issetInspectErr bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetHealthScore = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetScoreDetail = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataMetric = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSlowlogItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionNodeItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 200:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField200(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectErr = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceInfo {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetHealthScore {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetScoreDetail {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDataMetric {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetSlowlogItems {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetInspectionNodeItems {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetInspectErr {
		fieldId = 200
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInspectionReportResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInspectionReportResp[fieldId]))
}

func (p *DescribeDBInspectionReportResp) ReadField1(iprot thrift.TProtocol) error {
	_field := NewInspectionInstanceBasicInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InstanceInfo = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HealthScore = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MetricScore, 0, size)
	values := make([]MetricScore, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ScoreDetail = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionDataMetric, 0, size)
	values := make([]InspectionDataMetric, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DataMetric = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionSlowLog, 0, size)
	values := make([]InspectionSlowLog, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SlowlogItems = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionBigKeysItem, 0, size)
	values := make([]InspectionBigKeysItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RedisInspectionBigKeysItemItemsValueSize = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionBigKeysItem, 0, size)
	values := make([]InspectionBigKeysItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RedisInspectionBigKeysItemItemsValueLen = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionHotKeysItem, 0, size)
	values := make([]InspectionHotKeysItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RedisInspectionHotKeysItemItems = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionNodeItem, 0, size)
	values := make([]InspectionNodeItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InspectionNodeItems = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField11(iprot thrift.TProtocol) error {
	_field := NewInspectionNodeReport()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.InspectionNodeReport = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField12(iprot thrift.TProtocol) error {
	_field := NewDescribeTableSpaceAutoIncrResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DescribeTableSpaceAutoIncrResp = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField13(iprot thrift.TProtocol) error {
	_field := NewDescribeTableSpaceResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DescribeTableSpaceResp = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField14(iprot thrift.TProtocol) error {
	_field := NewDescribeFullSqlStatusResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DescribeFullSqlStatusResp = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField15(iprot thrift.TProtocol) error {
	_field := NewDescribeAggregateDialogsResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DescribeAggregateDialogsResp = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField16(iprot thrift.TProtocol) error {
	_field := NewDescribeSqlTemplatesContrastResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DescribeSqlTemplatesContrastResp = _field
	return nil
}
func (p *DescribeDBInspectionReportResp) ReadField200(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectErr, 0, size)
	values := make([]InspectErr, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InspectErr = _field
	return nil
}

func (p *DescribeDBInspectionReportResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionReportResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInspectionReportResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField200(oprot); err != nil {
			fieldId = 200
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceInfo", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.InstanceInfo.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("HealthScore", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.HealthScore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ScoreDetail", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ScoreDetail)); err != nil {
		return err
	}
	for _, v := range p.ScoreDetail {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataMetric", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DataMetric)); err != nil {
		return err
	}
	for _, v := range p.DataMetric {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SlowlogItems", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SlowlogItems)); err != nil {
		return err
	}
	for _, v := range p.SlowlogItems {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetRedisInspectionBigKeysItemItemsValueSize() {
		if err = oprot.WriteFieldBegin("RedisInspectionBigKeysItemItemsValueSize", thrift.LIST, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RedisInspectionBigKeysItemItemsValueSize)); err != nil {
			return err
		}
		for _, v := range p.RedisInspectionBigKeysItemItemsValueSize {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetRedisInspectionBigKeysItemItemsValueLen() {
		if err = oprot.WriteFieldBegin("RedisInspectionBigKeysItemItemsValueLen", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RedisInspectionBigKeysItemItemsValueLen)); err != nil {
			return err
		}
		for _, v := range p.RedisInspectionBigKeysItemItemsValueLen {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetRedisInspectionHotKeysItemItems() {
		if err = oprot.WriteFieldBegin("RedisInspectionHotKeysItemItems", thrift.LIST, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RedisInspectionHotKeysItemItems)); err != nil {
			return err
		}
		for _, v := range p.RedisInspectionHotKeysItemItems {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionNodeItems", thrift.LIST, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InspectionNodeItems)); err != nil {
		return err
	}
	for _, v := range p.InspectionNodeItems {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetInspectionNodeReport() {
		if err = oprot.WriteFieldBegin("InspectionNodeReport", thrift.STRUCT, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.InspectionNodeReport.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeTableSpaceAutoIncrResp() {
		if err = oprot.WriteFieldBegin("DescribeTableSpaceAutoIncrResp", thrift.STRUCT, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DescribeTableSpaceAutoIncrResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeTableSpaceResp() {
		if err = oprot.WriteFieldBegin("DescribeTableSpaceResp", thrift.STRUCT, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DescribeTableSpaceResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeFullSqlStatusResp() {
		if err = oprot.WriteFieldBegin("DescribeFullSqlStatusResp", thrift.STRUCT, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DescribeFullSqlStatusResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeAggregateDialogsResp() {
		if err = oprot.WriteFieldBegin("DescribeAggregateDialogsResp", thrift.STRUCT, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DescribeAggregateDialogsResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescribeSqlTemplatesContrastResp() {
		if err = oprot.WriteFieldBegin("DescribeSqlTemplatesContrastResp", thrift.STRUCT, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.DescribeSqlTemplatesContrastResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) writeField200(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectErr", thrift.LIST, 200); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InspectErr)); err != nil {
		return err
	}
	for _, v := range p.InspectErr {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 200 end error: ", p), err)
}

func (p *DescribeDBInspectionReportResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInspectionReportResp(%+v)", *p)

}

func (p *DescribeDBInspectionReportResp) DeepEqual(ano *DescribeDBInspectionReportResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceInfo) {
		return false
	}
	if !p.Field2DeepEqual(ano.HealthScore) {
		return false
	}
	if !p.Field3DeepEqual(ano.ScoreDetail) {
		return false
	}
	if !p.Field4DeepEqual(ano.DataMetric) {
		return false
	}
	if !p.Field5DeepEqual(ano.SlowlogItems) {
		return false
	}
	if !p.Field6DeepEqual(ano.RedisInspectionBigKeysItemItemsValueSize) {
		return false
	}
	if !p.Field7DeepEqual(ano.RedisInspectionBigKeysItemItemsValueLen) {
		return false
	}
	if !p.Field8DeepEqual(ano.RedisInspectionHotKeysItemItems) {
		return false
	}
	if !p.Field10DeepEqual(ano.InspectionNodeItems) {
		return false
	}
	if !p.Field11DeepEqual(ano.InspectionNodeReport) {
		return false
	}
	if !p.Field12DeepEqual(ano.DescribeTableSpaceAutoIncrResp) {
		return false
	}
	if !p.Field13DeepEqual(ano.DescribeTableSpaceResp) {
		return false
	}
	if !p.Field14DeepEqual(ano.DescribeFullSqlStatusResp) {
		return false
	}
	if !p.Field15DeepEqual(ano.DescribeAggregateDialogsResp) {
		return false
	}
	if !p.Field16DeepEqual(ano.DescribeSqlTemplatesContrastResp) {
		return false
	}
	if !p.Field200DeepEqual(ano.InspectErr) {
		return false
	}
	return true
}

func (p *DescribeDBInspectionReportResp) Field1DeepEqual(src *InspectionInstanceBasicInfo) bool {

	if !p.InstanceInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field2DeepEqual(src int32) bool {

	if p.HealthScore != src {
		return false
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field3DeepEqual(src []*MetricScore) bool {

	if len(p.ScoreDetail) != len(src) {
		return false
	}
	for i, v := range p.ScoreDetail {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field4DeepEqual(src []*InspectionDataMetric) bool {

	if len(p.DataMetric) != len(src) {
		return false
	}
	for i, v := range p.DataMetric {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field5DeepEqual(src []*InspectionSlowLog) bool {

	if len(p.SlowlogItems) != len(src) {
		return false
	}
	for i, v := range p.SlowlogItems {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field6DeepEqual(src []*InspectionBigKeysItem) bool {

	if len(p.RedisInspectionBigKeysItemItemsValueSize) != len(src) {
		return false
	}
	for i, v := range p.RedisInspectionBigKeysItemItemsValueSize {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field7DeepEqual(src []*InspectionBigKeysItem) bool {

	if len(p.RedisInspectionBigKeysItemItemsValueLen) != len(src) {
		return false
	}
	for i, v := range p.RedisInspectionBigKeysItemItemsValueLen {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field8DeepEqual(src []*InspectionHotKeysItem) bool {

	if len(p.RedisInspectionHotKeysItemItems) != len(src) {
		return false
	}
	for i, v := range p.RedisInspectionHotKeysItemItems {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field10DeepEqual(src []*InspectionNodeItem) bool {

	if len(p.InspectionNodeItems) != len(src) {
		return false
	}
	for i, v := range p.InspectionNodeItems {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field11DeepEqual(src *InspectionNodeReport) bool {

	if !p.InspectionNodeReport.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field12DeepEqual(src *DescribeTableSpaceAutoIncrResp) bool {

	if !p.DescribeTableSpaceAutoIncrResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field13DeepEqual(src *DescribeTableSpaceResp) bool {

	if !p.DescribeTableSpaceResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field14DeepEqual(src *DescribeFullSqlStatusResp) bool {

	if !p.DescribeFullSqlStatusResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field15DeepEqual(src *DescribeAggregateDialogsResp) bool {

	if !p.DescribeAggregateDialogsResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field16DeepEqual(src *DescribeSqlTemplatesContrastResp) bool {

	if !p.DescribeSqlTemplatesContrastResp.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeDBInspectionReportResp) Field200DeepEqual(src []*InspectErr) bool {

	if len(p.InspectErr) != len(src) {
		return false
	}
	for i, v := range p.InspectErr {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type InspectErr struct {
	InspectionItem string `thrift:"InspectionItem,1,required" frugal:"1,required,string" json:"InspectionItem"`
	ErrMsg         string `thrift:"ErrMsg,2,required" frugal:"2,required,string" json:"ErrMsg"`
}

func NewInspectErr() *InspectErr {
	return &InspectErr{}
}

func (p *InspectErr) InitDefault() {
}

func (p *InspectErr) GetInspectionItem() (v string) {
	return p.InspectionItem
}

func (p *InspectErr) GetErrMsg() (v string) {
	return p.ErrMsg
}
func (p *InspectErr) SetInspectionItem(val string) {
	p.InspectionItem = val
}
func (p *InspectErr) SetErrMsg(val string) {
	p.ErrMsg = val
}

var fieldIDToName_InspectErr = map[int16]string{
	1: "InspectionItem",
	2: "ErrMsg",
}

func (p *InspectErr) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectErr")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInspectionItem bool = false
	var issetErrMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionItem = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInspectionItem {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetErrMsg {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectErr[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectErr[fieldId]))
}

func (p *InspectErr) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InspectionItem = _field
	return nil
}
func (p *InspectErr) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrMsg = _field
	return nil
}

func (p *InspectErr) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectErr")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectErr"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectErr) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionItem", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InspectionItem); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectErr) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrMsg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectErr) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectErr(%+v)", *p)

}

func (p *InspectErr) DeepEqual(ano *InspectErr) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InspectionItem) {
		return false
	}
	if !p.Field2DeepEqual(ano.ErrMsg) {
		return false
	}
	return true
}

func (p *InspectErr) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InspectionItem, src) != 0 {
		return false
	}
	return true
}
func (p *InspectErr) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ErrMsg, src) != 0 {
		return false
	}
	return true
}

type InspectionNodeItem struct {
	TaskId              string           `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
	NodeTaskId          string           `thrift:"NodeTaskId,2,required" frugal:"2,required,string" json:"NodeTaskId"`
	NodeInfoObject      *NodeInfoObject  `thrift:"NodeInfoObject,3,required" frugal:"3,required,NodeInfoObject" json:"NodeInfoObject"`
	InstanceId          string           `thrift:"InstanceId,4,required" frugal:"4,required,string" json:"InstanceId"`
	TaskStatus          InspectionStatus `thrift:"TaskStatus,5,required" frugal:"5,required,InspectionStatus" json:"TaskStatus"`
	ExecuteTime         string           `thrift:"ExecuteTime,6,required" frugal:"6,required,string" json:"ExecuteTime"`
	TaskType            InspectionType   `thrift:"TaskType,7,required" frugal:"7,required,InspectionType" json:"TaskType"`
	InspectionStartTime string           `thrift:"InspectionStartTime,8,required" frugal:"8,required,string" json:"InspectionStartTime"`
	InspectionEndTime   string           `thrift:"InspectionEndTime,9,required" frugal:"9,required,string" json:"InspectionEndTime"`
	ErrMsg              string           `thrift:"ErrMsg,10,required" frugal:"10,required,string" json:"ErrMsg"`
	Agg                 *InspectAgg      `thrift:"Agg,11,required" frugal:"11,required,InspectAgg" json:"Agg"`
}

func NewInspectionNodeItem() *InspectionNodeItem {
	return &InspectionNodeItem{}
}

func (p *InspectionNodeItem) InitDefault() {
}

func (p *InspectionNodeItem) GetTaskId() (v string) {
	return p.TaskId
}

func (p *InspectionNodeItem) GetNodeTaskId() (v string) {
	return p.NodeTaskId
}

var InspectionNodeItem_NodeInfoObject_DEFAULT *NodeInfoObject

func (p *InspectionNodeItem) GetNodeInfoObject() (v *NodeInfoObject) {
	if !p.IsSetNodeInfoObject() {
		return InspectionNodeItem_NodeInfoObject_DEFAULT
	}
	return p.NodeInfoObject
}

func (p *InspectionNodeItem) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *InspectionNodeItem) GetTaskStatus() (v InspectionStatus) {
	return p.TaskStatus
}

func (p *InspectionNodeItem) GetExecuteTime() (v string) {
	return p.ExecuteTime
}

func (p *InspectionNodeItem) GetTaskType() (v InspectionType) {
	return p.TaskType
}

func (p *InspectionNodeItem) GetInspectionStartTime() (v string) {
	return p.InspectionStartTime
}

func (p *InspectionNodeItem) GetInspectionEndTime() (v string) {
	return p.InspectionEndTime
}

func (p *InspectionNodeItem) GetErrMsg() (v string) {
	return p.ErrMsg
}

var InspectionNodeItem_Agg_DEFAULT *InspectAgg

func (p *InspectionNodeItem) GetAgg() (v *InspectAgg) {
	if !p.IsSetAgg() {
		return InspectionNodeItem_Agg_DEFAULT
	}
	return p.Agg
}
func (p *InspectionNodeItem) SetTaskId(val string) {
	p.TaskId = val
}
func (p *InspectionNodeItem) SetNodeTaskId(val string) {
	p.NodeTaskId = val
}
func (p *InspectionNodeItem) SetNodeInfoObject(val *NodeInfoObject) {
	p.NodeInfoObject = val
}
func (p *InspectionNodeItem) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *InspectionNodeItem) SetTaskStatus(val InspectionStatus) {
	p.TaskStatus = val
}
func (p *InspectionNodeItem) SetExecuteTime(val string) {
	p.ExecuteTime = val
}
func (p *InspectionNodeItem) SetTaskType(val InspectionType) {
	p.TaskType = val
}
func (p *InspectionNodeItem) SetInspectionStartTime(val string) {
	p.InspectionStartTime = val
}
func (p *InspectionNodeItem) SetInspectionEndTime(val string) {
	p.InspectionEndTime = val
}
func (p *InspectionNodeItem) SetErrMsg(val string) {
	p.ErrMsg = val
}
func (p *InspectionNodeItem) SetAgg(val *InspectAgg) {
	p.Agg = val
}

var fieldIDToName_InspectionNodeItem = map[int16]string{
	1:  "TaskId",
	2:  "NodeTaskId",
	3:  "NodeInfoObject",
	4:  "InstanceId",
	5:  "TaskStatus",
	6:  "ExecuteTime",
	7:  "TaskType",
	8:  "InspectionStartTime",
	9:  "InspectionEndTime",
	10: "ErrMsg",
	11: "Agg",
}

func (p *InspectionNodeItem) IsSetNodeInfoObject() bool {
	return p.NodeInfoObject != nil
}

func (p *InspectionNodeItem) IsSetAgg() bool {
	return p.Agg != nil
}

func (p *InspectionNodeItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionNodeItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	var issetNodeTaskId bool = false
	var issetNodeInfoObject bool = false
	var issetInstanceId bool = false
	var issetTaskStatus bool = false
	var issetExecuteTime bool = false
	var issetTaskType bool = false
	var issetInspectionStartTime bool = false
	var issetInspectionEndTime bool = false
	var issetErrMsg bool = false
	var issetAgg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeInfoObject = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecuteTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetAgg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetNodeTaskId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetNodeInfoObject {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTaskStatus {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetExecuteTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetTaskType {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetInspectionStartTime {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetInspectionEndTime {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetErrMsg {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetAgg {
		fieldId = 11
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionNodeItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionNodeItem[fieldId]))
}

func (p *InspectionNodeItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *InspectionNodeItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeTaskId = _field
	return nil
}
func (p *InspectionNodeItem) ReadField3(iprot thrift.TProtocol) error {
	_field := NewNodeInfoObject()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.NodeInfoObject = _field
	return nil
}
func (p *InspectionNodeItem) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *InspectionNodeItem) ReadField5(iprot thrift.TProtocol) error {

	var _field InspectionStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InspectionStatus(v)
	}
	p.TaskStatus = _field
	return nil
}
func (p *InspectionNodeItem) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecuteTime = _field
	return nil
}
func (p *InspectionNodeItem) ReadField7(iprot thrift.TProtocol) error {

	var _field InspectionType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InspectionType(v)
	}
	p.TaskType = _field
	return nil
}
func (p *InspectionNodeItem) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InspectionStartTime = _field
	return nil
}
func (p *InspectionNodeItem) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InspectionEndTime = _field
	return nil
}
func (p *InspectionNodeItem) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrMsg = _field
	return nil
}
func (p *InspectionNodeItem) ReadField11(iprot thrift.TProtocol) error {
	_field := NewInspectAgg()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Agg = _field
	return nil
}

func (p *InspectionNodeItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionNodeItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionNodeItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionNodeItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionNodeItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeTaskId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeTaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionNodeItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeInfoObject", thrift.STRUCT, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.NodeInfoObject.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionNodeItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InspectionNodeItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskStatus", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TaskStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InspectionNodeItem) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecuteTime", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecuteTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InspectionNodeItem) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskType", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TaskType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *InspectionNodeItem) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionStartTime", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InspectionStartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *InspectionNodeItem) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionEndTime", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InspectionEndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *InspectionNodeItem) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrMsg", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *InspectionNodeItem) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Agg", thrift.STRUCT, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Agg.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *InspectionNodeItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionNodeItem(%+v)", *p)

}

func (p *InspectionNodeItem) DeepEqual(ano *InspectionNodeItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeTaskId) {
		return false
	}
	if !p.Field3DeepEqual(ano.NodeInfoObject) {
		return false
	}
	if !p.Field4DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field5DeepEqual(ano.TaskStatus) {
		return false
	}
	if !p.Field6DeepEqual(ano.ExecuteTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.TaskType) {
		return false
	}
	if !p.Field8DeepEqual(ano.InspectionStartTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.InspectionEndTime) {
		return false
	}
	if !p.Field10DeepEqual(ano.ErrMsg) {
		return false
	}
	if !p.Field11DeepEqual(ano.Agg) {
		return false
	}
	return true
}

func (p *InspectionNodeItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionNodeItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.NodeTaskId, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionNodeItem) Field3DeepEqual(src *NodeInfoObject) bool {

	if !p.NodeInfoObject.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InspectionNodeItem) Field4DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionNodeItem) Field5DeepEqual(src InspectionStatus) bool {

	if p.TaskStatus != src {
		return false
	}
	return true
}
func (p *InspectionNodeItem) Field6DeepEqual(src string) bool {

	if strings.Compare(p.ExecuteTime, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionNodeItem) Field7DeepEqual(src InspectionType) bool {

	if p.TaskType != src {
		return false
	}
	return true
}
func (p *InspectionNodeItem) Field8DeepEqual(src string) bool {

	if strings.Compare(p.InspectionStartTime, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionNodeItem) Field9DeepEqual(src string) bool {

	if strings.Compare(p.InspectionEndTime, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionNodeItem) Field10DeepEqual(src string) bool {

	if strings.Compare(p.ErrMsg, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionNodeItem) Field11DeepEqual(src *InspectAgg) bool {

	if !p.Agg.DeepEqual(src) {
		return false
	}
	return true
}

type InspectionSlowLog struct {
	SqlTemplate     string            `thrift:"SqlTemplate,1,required" frugal:"1,required,string" json:"SqlTemplate"`
	DBName          string            `thrift:"DBName,2,required" frugal:"2,required,string" json:"DBName"`
	ExecuteUser     string            `thrift:"ExecuteUser,3,required" frugal:"3,required,string" json:"ExecuteUser"`
	ExecuteCount    int32             `thrift:"ExecuteCount,4,required" frugal:"4,required,i32" json:"ExecuteCount"`
	TotalQueryTime  float64           `thrift:"TotalQueryTime,5,required" frugal:"5,required,double" json:"TotalQueryTime"`
	AvgQueryTime    float64           `thrift:"AvgQueryTime,6,required" frugal:"6,required,double" json:"AvgQueryTime"`
	AvgLockTime     float64           `thrift:"AvgLockTime,7,required" frugal:"7,required,double" json:"AvgLockTime"`
	AvgRowsExamined float64           `thrift:"AvgRowsExamined,8,required" frugal:"8,required,double" json:"AvgRowsExamined"`
	InstanceId      string            `thrift:"InstanceId,9,required" frugal:"9,required,string" json:"InstanceId"`
	NumberId        int32             `thrift:"NumberId,10,required" frugal:"10,required,i32" json:"NumberId"`
	TaskId          int64             `thrift:"TaskId,11,required" frugal:"11,required,i64" json:"TaskId"`
	QueryTimeStats  *StatisticResult_ `thrift:"QueryTimeStats,12,required" frugal:"12,required,StatisticResult_" json:"QueryTimeStats"`
	SQLKeepDays     int32             `thrift:"SQLKeepDays,13,required" frugal:"13,required,i32" json:"SQLKeepDays"`
}

func NewInspectionSlowLog() *InspectionSlowLog {
	return &InspectionSlowLog{}
}

func (p *InspectionSlowLog) InitDefault() {
}

func (p *InspectionSlowLog) GetSqlTemplate() (v string) {
	return p.SqlTemplate
}

func (p *InspectionSlowLog) GetDBName() (v string) {
	return p.DBName
}

func (p *InspectionSlowLog) GetExecuteUser() (v string) {
	return p.ExecuteUser
}

func (p *InspectionSlowLog) GetExecuteCount() (v int32) {
	return p.ExecuteCount
}

func (p *InspectionSlowLog) GetTotalQueryTime() (v float64) {
	return p.TotalQueryTime
}

func (p *InspectionSlowLog) GetAvgQueryTime() (v float64) {
	return p.AvgQueryTime
}

func (p *InspectionSlowLog) GetAvgLockTime() (v float64) {
	return p.AvgLockTime
}

func (p *InspectionSlowLog) GetAvgRowsExamined() (v float64) {
	return p.AvgRowsExamined
}

func (p *InspectionSlowLog) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *InspectionSlowLog) GetNumberId() (v int32) {
	return p.NumberId
}

func (p *InspectionSlowLog) GetTaskId() (v int64) {
	return p.TaskId
}

var InspectionSlowLog_QueryTimeStats_DEFAULT *StatisticResult_

func (p *InspectionSlowLog) GetQueryTimeStats() (v *StatisticResult_) {
	if !p.IsSetQueryTimeStats() {
		return InspectionSlowLog_QueryTimeStats_DEFAULT
	}
	return p.QueryTimeStats
}

func (p *InspectionSlowLog) GetSQLKeepDays() (v int32) {
	return p.SQLKeepDays
}
func (p *InspectionSlowLog) SetSqlTemplate(val string) {
	p.SqlTemplate = val
}
func (p *InspectionSlowLog) SetDBName(val string) {
	p.DBName = val
}
func (p *InspectionSlowLog) SetExecuteUser(val string) {
	p.ExecuteUser = val
}
func (p *InspectionSlowLog) SetExecuteCount(val int32) {
	p.ExecuteCount = val
}
func (p *InspectionSlowLog) SetTotalQueryTime(val float64) {
	p.TotalQueryTime = val
}
func (p *InspectionSlowLog) SetAvgQueryTime(val float64) {
	p.AvgQueryTime = val
}
func (p *InspectionSlowLog) SetAvgLockTime(val float64) {
	p.AvgLockTime = val
}
func (p *InspectionSlowLog) SetAvgRowsExamined(val float64) {
	p.AvgRowsExamined = val
}
func (p *InspectionSlowLog) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *InspectionSlowLog) SetNumberId(val int32) {
	p.NumberId = val
}
func (p *InspectionSlowLog) SetTaskId(val int64) {
	p.TaskId = val
}
func (p *InspectionSlowLog) SetQueryTimeStats(val *StatisticResult_) {
	p.QueryTimeStats = val
}
func (p *InspectionSlowLog) SetSQLKeepDays(val int32) {
	p.SQLKeepDays = val
}

var fieldIDToName_InspectionSlowLog = map[int16]string{
	1:  "SqlTemplate",
	2:  "DBName",
	3:  "ExecuteUser",
	4:  "ExecuteCount",
	5:  "TotalQueryTime",
	6:  "AvgQueryTime",
	7:  "AvgLockTime",
	8:  "AvgRowsExamined",
	9:  "InstanceId",
	10: "NumberId",
	11: "TaskId",
	12: "QueryTimeStats",
	13: "SQLKeepDays",
}

func (p *InspectionSlowLog) IsSetQueryTimeStats() bool {
	return p.QueryTimeStats != nil
}

func (p *InspectionSlowLog) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionSlowLog")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlTemplate bool = false
	var issetDBName bool = false
	var issetExecuteUser bool = false
	var issetExecuteCount bool = false
	var issetTotalQueryTime bool = false
	var issetAvgQueryTime bool = false
	var issetAvgLockTime bool = false
	var issetAvgRowsExamined bool = false
	var issetInstanceId bool = false
	var issetNumberId bool = false
	var issetTaskId bool = false
	var issetQueryTimeStats bool = false
	var issetSQLKeepDays bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlTemplate = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecuteUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecuteCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotalQueryTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetAvgQueryTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetAvgLockTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetAvgRowsExamined = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetNumberId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetQueryTimeStats = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLKeepDays = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlTemplate {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetExecuteUser {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetExecuteCount {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetTotalQueryTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetAvgQueryTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetAvgLockTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetAvgRowsExamined {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetNumberId {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetTaskId {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetQueryTimeStats {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetSQLKeepDays {
		fieldId = 13
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionSlowLog[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionSlowLog[fieldId]))
}

func (p *InspectionSlowLog) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlTemplate = _field
	return nil
}
func (p *InspectionSlowLog) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *InspectionSlowLog) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecuteUser = _field
	return nil
}
func (p *InspectionSlowLog) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecuteCount = _field
	return nil
}
func (p *InspectionSlowLog) ReadField5(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalQueryTime = _field
	return nil
}
func (p *InspectionSlowLog) ReadField6(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvgQueryTime = _field
	return nil
}
func (p *InspectionSlowLog) ReadField7(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvgLockTime = _field
	return nil
}
func (p *InspectionSlowLog) ReadField8(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvgRowsExamined = _field
	return nil
}
func (p *InspectionSlowLog) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *InspectionSlowLog) ReadField10(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NumberId = _field
	return nil
}
func (p *InspectionSlowLog) ReadField11(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *InspectionSlowLog) ReadField12(iprot thrift.TProtocol) error {
	_field := NewStatisticResult_()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.QueryTimeStats = _field
	return nil
}
func (p *InspectionSlowLog) ReadField13(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SQLKeepDays = _field
	return nil
}

func (p *InspectionSlowLog) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionSlowLog")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionSlowLog"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionSlowLog) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlTemplate", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlTemplate); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecuteUser", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ExecuteUser); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecuteCount", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ExecuteCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TotalQueryTime", thrift.DOUBLE, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.TotalQueryTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AvgQueryTime", thrift.DOUBLE, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.AvgQueryTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AvgLockTime", thrift.DOUBLE, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.AvgLockTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AvgRowsExamined", thrift.DOUBLE, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.AvgRowsExamined); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NumberId", thrift.I32, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.NumberId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.I64, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("QueryTimeStats", thrift.STRUCT, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.QueryTimeStats.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *InspectionSlowLog) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLKeepDays", thrift.I32, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.SQLKeepDays); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *InspectionSlowLog) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionSlowLog(%+v)", *p)

}

func (p *InspectionSlowLog) DeepEqual(ano *InspectionSlowLog) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlTemplate) {
		return false
	}
	if !p.Field2DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field3DeepEqual(ano.ExecuteUser) {
		return false
	}
	if !p.Field4DeepEqual(ano.ExecuteCount) {
		return false
	}
	if !p.Field5DeepEqual(ano.TotalQueryTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.AvgQueryTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.AvgLockTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.AvgRowsExamined) {
		return false
	}
	if !p.Field9DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field10DeepEqual(ano.NumberId) {
		return false
	}
	if !p.Field11DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field12DeepEqual(ano.QueryTimeStats) {
		return false
	}
	if !p.Field13DeepEqual(ano.SQLKeepDays) {
		return false
	}
	return true
}

func (p *InspectionSlowLog) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SqlTemplate, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field2DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ExecuteUser, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field4DeepEqual(src int32) bool {

	if p.ExecuteCount != src {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field5DeepEqual(src float64) bool {

	if p.TotalQueryTime != src {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field6DeepEqual(src float64) bool {

	if p.AvgQueryTime != src {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field7DeepEqual(src float64) bool {

	if p.AvgLockTime != src {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field8DeepEqual(src float64) bool {

	if p.AvgRowsExamined != src {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field9DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field10DeepEqual(src int32) bool {

	if p.NumberId != src {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field11DeepEqual(src int64) bool {

	if p.TaskId != src {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field12DeepEqual(src *StatisticResult_) bool {

	if !p.QueryTimeStats.DeepEqual(src) {
		return false
	}
	return true
}
func (p *InspectionSlowLog) Field13DeepEqual(src int32) bool {

	if p.SQLKeepDays != src {
		return false
	}
	return true
}

type InspectionInstanceBasicInfo struct {
	InstanceId            string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceSpecification string `thrift:"InstanceSpecification,2,required" frugal:"2,required,string" json:"InstanceSpecification"`
	InstanceVersion       string `thrift:"InstanceVersion,3,required" frugal:"3,required,string" json:"InstanceVersion"`
}

func NewInspectionInstanceBasicInfo() *InspectionInstanceBasicInfo {
	return &InspectionInstanceBasicInfo{}
}

func (p *InspectionInstanceBasicInfo) InitDefault() {
}

func (p *InspectionInstanceBasicInfo) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *InspectionInstanceBasicInfo) GetInstanceSpecification() (v string) {
	return p.InstanceSpecification
}

func (p *InspectionInstanceBasicInfo) GetInstanceVersion() (v string) {
	return p.InstanceVersion
}
func (p *InspectionInstanceBasicInfo) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *InspectionInstanceBasicInfo) SetInstanceSpecification(val string) {
	p.InstanceSpecification = val
}
func (p *InspectionInstanceBasicInfo) SetInstanceVersion(val string) {
	p.InstanceVersion = val
}

var fieldIDToName_InspectionInstanceBasicInfo = map[int16]string{
	1: "InstanceId",
	2: "InstanceSpecification",
	3: "InstanceVersion",
}

func (p *InspectionInstanceBasicInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionInstanceBasicInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceSpecification bool = false
	var issetInstanceVersion bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceSpecification = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceVersion = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceSpecification {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceVersion {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionInstanceBasicInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionInstanceBasicInfo[fieldId]))
}

func (p *InspectionInstanceBasicInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *InspectionInstanceBasicInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceSpecification = _field
	return nil
}
func (p *InspectionInstanceBasicInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceVersion = _field
	return nil
}

func (p *InspectionInstanceBasicInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionInstanceBasicInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionInstanceBasicInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionInstanceBasicInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionInstanceBasicInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceSpecification", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceSpecification); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionInstanceBasicInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceVersion", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceVersion); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionInstanceBasicInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionInstanceBasicInfo(%+v)", *p)

}

func (p *InspectionInstanceBasicInfo) DeepEqual(ano *InspectionInstanceBasicInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceSpecification) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceVersion) {
		return false
	}
	return true
}

func (p *InspectionInstanceBasicInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionInstanceBasicInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceSpecification, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionInstanceBasicInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceVersion, src) != 0 {
		return false
	}
	return true
}

type InspectionInstanceResourceUsage struct {
	Name InspectionMetricName `thrift:"Name,1,required" frugal:"1,required,InspectionMetricName" json:"Name"`
	Max  float64              `thrift:"Max,2,required" frugal:"2,required,double" json:"Max"`
	Min  float64              `thrift:"Min,3,required" frugal:"3,required,double" json:"Min"`
	Avg  float64              `thrift:"Avg,4,required" frugal:"4,required,double" json:"Avg"`
	Unit string               `thrift:"Unit,5,required" frugal:"5,required,string" json:"Unit"`
}

func NewInspectionInstanceResourceUsage() *InspectionInstanceResourceUsage {
	return &InspectionInstanceResourceUsage{}
}

func (p *InspectionInstanceResourceUsage) InitDefault() {
}

func (p *InspectionInstanceResourceUsage) GetName() (v InspectionMetricName) {
	return p.Name
}

func (p *InspectionInstanceResourceUsage) GetMax() (v float64) {
	return p.Max
}

func (p *InspectionInstanceResourceUsage) GetMin() (v float64) {
	return p.Min
}

func (p *InspectionInstanceResourceUsage) GetAvg() (v float64) {
	return p.Avg
}

func (p *InspectionInstanceResourceUsage) GetUnit() (v string) {
	return p.Unit
}
func (p *InspectionInstanceResourceUsage) SetName(val InspectionMetricName) {
	p.Name = val
}
func (p *InspectionInstanceResourceUsage) SetMax(val float64) {
	p.Max = val
}
func (p *InspectionInstanceResourceUsage) SetMin(val float64) {
	p.Min = val
}
func (p *InspectionInstanceResourceUsage) SetAvg(val float64) {
	p.Avg = val
}
func (p *InspectionInstanceResourceUsage) SetUnit(val string) {
	p.Unit = val
}

var fieldIDToName_InspectionInstanceResourceUsage = map[int16]string{
	1: "Name",
	2: "Max",
	3: "Min",
	4: "Avg",
	5: "Unit",
}

func (p *InspectionInstanceResourceUsage) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionInstanceResourceUsage")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false
	var issetMax bool = false
	var issetMin bool = false
	var issetAvg bool = false
	var issetUnit bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMax = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMin = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAvg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetUnit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMax {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMin {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAvg {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetUnit {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionInstanceResourceUsage[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionInstanceResourceUsage[fieldId]))
}

func (p *InspectionInstanceResourceUsage) ReadField1(iprot thrift.TProtocol) error {

	var _field InspectionMetricName
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InspectionMetricName(v)
	}
	p.Name = _field
	return nil
}
func (p *InspectionInstanceResourceUsage) ReadField2(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Max = _field
	return nil
}
func (p *InspectionInstanceResourceUsage) ReadField3(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Min = _field
	return nil
}
func (p *InspectionInstanceResourceUsage) ReadField4(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Avg = _field
	return nil
}
func (p *InspectionInstanceResourceUsage) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Unit = _field
	return nil
}

func (p *InspectionInstanceResourceUsage) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionInstanceResourceUsage")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionInstanceResourceUsage"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionInstanceResourceUsage) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Name", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Name)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionInstanceResourceUsage) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Max", thrift.DOUBLE, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Max); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionInstanceResourceUsage) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Min", thrift.DOUBLE, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Min); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionInstanceResourceUsage) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Avg", thrift.DOUBLE, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Avg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InspectionInstanceResourceUsage) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Unit", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Unit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InspectionInstanceResourceUsage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionInstanceResourceUsage(%+v)", *p)

}

func (p *InspectionInstanceResourceUsage) DeepEqual(ano *InspectionInstanceResourceUsage) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Name) {
		return false
	}
	if !p.Field2DeepEqual(ano.Max) {
		return false
	}
	if !p.Field3DeepEqual(ano.Min) {
		return false
	}
	if !p.Field4DeepEqual(ano.Avg) {
		return false
	}
	if !p.Field5DeepEqual(ano.Unit) {
		return false
	}
	return true
}

func (p *InspectionInstanceResourceUsage) Field1DeepEqual(src InspectionMetricName) bool {

	if p.Name != src {
		return false
	}
	return true
}
func (p *InspectionInstanceResourceUsage) Field2DeepEqual(src float64) bool {

	if p.Max != src {
		return false
	}
	return true
}
func (p *InspectionInstanceResourceUsage) Field3DeepEqual(src float64) bool {

	if p.Min != src {
		return false
	}
	return true
}
func (p *InspectionInstanceResourceUsage) Field4DeepEqual(src float64) bool {

	if p.Avg != src {
		return false
	}
	return true
}
func (p *InspectionInstanceResourceUsage) Field5DeepEqual(src string) bool {

	if strings.Compare(p.Unit, src) != 0 {
		return false
	}
	return true
}

type InspectionConnectInfo struct {
	MaxConnection      int32 `thrift:"MaxConnection,1,required" frugal:"1,required,i32" json:"MaxConnection"`
	CurrentConnections int32 `thrift:"CurrentConnections,2,required" frugal:"2,required,i32" json:"CurrentConnections"`
	RunningConnections int32 `thrift:"RunningConnections,3,required" frugal:"3,required,i32" json:"RunningConnections"`
}

func NewInspectionConnectInfo() *InspectionConnectInfo {
	return &InspectionConnectInfo{}
}

func (p *InspectionConnectInfo) InitDefault() {
}

func (p *InspectionConnectInfo) GetMaxConnection() (v int32) {
	return p.MaxConnection
}

func (p *InspectionConnectInfo) GetCurrentConnections() (v int32) {
	return p.CurrentConnections
}

func (p *InspectionConnectInfo) GetRunningConnections() (v int32) {
	return p.RunningConnections
}
func (p *InspectionConnectInfo) SetMaxConnection(val int32) {
	p.MaxConnection = val
}
func (p *InspectionConnectInfo) SetCurrentConnections(val int32) {
	p.CurrentConnections = val
}
func (p *InspectionConnectInfo) SetRunningConnections(val int32) {
	p.RunningConnections = val
}

var fieldIDToName_InspectionConnectInfo = map[int16]string{
	1: "MaxConnection",
	2: "CurrentConnections",
	3: "RunningConnections",
}

func (p *InspectionConnectInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionConnectInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMaxConnection bool = false
	var issetCurrentConnections bool = false
	var issetRunningConnections bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxConnection = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCurrentConnections = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRunningConnections = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMaxConnection {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCurrentConnections {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRunningConnections {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionConnectInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionConnectInfo[fieldId]))
}

func (p *InspectionConnectInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxConnection = _field
	return nil
}
func (p *InspectionConnectInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CurrentConnections = _field
	return nil
}
func (p *InspectionConnectInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RunningConnections = _field
	return nil
}

func (p *InspectionConnectInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionConnectInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionConnectInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionConnectInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxConnection", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.MaxConnection); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionConnectInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CurrentConnections", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.CurrentConnections); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionConnectInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RunningConnections", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.RunningConnections); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionConnectInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionConnectInfo(%+v)", *p)

}

func (p *InspectionConnectInfo) DeepEqual(ano *InspectionConnectInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.MaxConnection) {
		return false
	}
	if !p.Field2DeepEqual(ano.CurrentConnections) {
		return false
	}
	if !p.Field3DeepEqual(ano.RunningConnections) {
		return false
	}
	return true
}

func (p *InspectionConnectInfo) Field1DeepEqual(src int32) bool {

	if p.MaxConnection != src {
		return false
	}
	return true
}
func (p *InspectionConnectInfo) Field2DeepEqual(src int32) bool {

	if p.CurrentConnections != src {
		return false
	}
	return true
}
func (p *InspectionConnectInfo) Field3DeepEqual(src int32) bool {

	if p.RunningConnections != src {
		return false
	}
	return true
}

type DescribeDBInspectionScoreReq struct {
	TaskID     string  `thrift:"TaskID,1,required" frugal:"1,required,string" json:"TaskID"`
	NodeTaskID *string `thrift:"NodeTaskID,2,optional" frugal:"2,optional,string" json:"NodeTaskID,omitempty"`
}

func NewDescribeDBInspectionScoreReq() *DescribeDBInspectionScoreReq {
	return &DescribeDBInspectionScoreReq{}
}

func (p *DescribeDBInspectionScoreReq) InitDefault() {
}

func (p *DescribeDBInspectionScoreReq) GetTaskID() (v string) {
	return p.TaskID
}

var DescribeDBInspectionScoreReq_NodeTaskID_DEFAULT string

func (p *DescribeDBInspectionScoreReq) GetNodeTaskID() (v string) {
	if !p.IsSetNodeTaskID() {
		return DescribeDBInspectionScoreReq_NodeTaskID_DEFAULT
	}
	return *p.NodeTaskID
}
func (p *DescribeDBInspectionScoreReq) SetTaskID(val string) {
	p.TaskID = val
}
func (p *DescribeDBInspectionScoreReq) SetNodeTaskID(val *string) {
	p.NodeTaskID = val
}

var fieldIDToName_DescribeDBInspectionScoreReq = map[int16]string{
	1: "TaskID",
	2: "NodeTaskID",
}

func (p *DescribeDBInspectionScoreReq) IsSetNodeTaskID() bool {
	return p.NodeTaskID != nil
}

func (p *DescribeDBInspectionScoreReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionScoreReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInspectionScoreReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInspectionScoreReq[fieldId]))
}

func (p *DescribeDBInspectionScoreReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskID = _field
	return nil
}
func (p *DescribeDBInspectionScoreReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeTaskID = _field
	return nil
}

func (p *DescribeDBInspectionScoreReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionScoreReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInspectionScoreReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInspectionScoreReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInspectionScoreReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeTaskID() {
		if err = oprot.WriteFieldBegin("NodeTaskID", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeTaskID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeDBInspectionScoreReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInspectionScoreReq(%+v)", *p)

}

func (p *DescribeDBInspectionScoreReq) DeepEqual(ano *DescribeDBInspectionScoreReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskID) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeTaskID) {
		return false
	}
	return true
}

func (p *DescribeDBInspectionScoreReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskID, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeDBInspectionScoreReq) Field2DeepEqual(src *string) bool {

	if p.NodeTaskID == src {
		return true
	} else if p.NodeTaskID == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeTaskID, *src) != 0 {
		return false
	}
	return true
}

type DescribeDBInspectionScoreResp struct {
	ScoreDetail []*MetricScore `thrift:"ScoreDetail,1,required" frugal:"1,required,list<MetricScore>" json:"ScoreDetail"`
}

func NewDescribeDBInspectionScoreResp() *DescribeDBInspectionScoreResp {
	return &DescribeDBInspectionScoreResp{}
}

func (p *DescribeDBInspectionScoreResp) InitDefault() {
}

func (p *DescribeDBInspectionScoreResp) GetScoreDetail() (v []*MetricScore) {
	return p.ScoreDetail
}
func (p *DescribeDBInspectionScoreResp) SetScoreDetail(val []*MetricScore) {
	p.ScoreDetail = val
}

var fieldIDToName_DescribeDBInspectionScoreResp = map[int16]string{
	1: "ScoreDetail",
}

func (p *DescribeDBInspectionScoreResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionScoreResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetScoreDetail bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetScoreDetail = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetScoreDetail {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeDBInspectionScoreResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeDBInspectionScoreResp[fieldId]))
}

func (p *DescribeDBInspectionScoreResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*MetricScore, 0, size)
	values := make([]MetricScore, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ScoreDetail = _field
	return nil
}

func (p *DescribeDBInspectionScoreResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeDBInspectionScoreResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeDBInspectionScoreResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeDBInspectionScoreResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ScoreDetail", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ScoreDetail)); err != nil {
		return err
	}
	for _, v := range p.ScoreDetail {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeDBInspectionScoreResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeDBInspectionScoreResp(%+v)", *p)

}

func (p *DescribeDBInspectionScoreResp) DeepEqual(ano *DescribeDBInspectionScoreResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ScoreDetail) {
		return false
	}
	return true
}

func (p *DescribeDBInspectionScoreResp) Field1DeepEqual(src []*MetricScore) bool {

	if len(p.ScoreDetail) != len(src) {
		return false
	}
	for i, v := range p.ScoreDetail {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type MetricScore struct {
	InspectionMetric InspectionMetric `thrift:"InspectionMetric,1,required" frugal:"1,required,InspectionMetric" json:"InspectionMetric"`
	Score            int8             `thrift:"Score,2,required" frugal:"2,required,i8" json:"Score"`
	Description      string           `thrift:"Description,3,required" frugal:"3,required,string" json:"Description"`
	ErrMsg           string           `thrift:"ErrMsg,4,required" frugal:"4,required,string" json:"ErrMsg"`
}

func NewMetricScore() *MetricScore {
	return &MetricScore{}
}

func (p *MetricScore) InitDefault() {
}

func (p *MetricScore) GetInspectionMetric() (v InspectionMetric) {
	return p.InspectionMetric
}

func (p *MetricScore) GetScore() (v int8) {
	return p.Score
}

func (p *MetricScore) GetDescription() (v string) {
	return p.Description
}

func (p *MetricScore) GetErrMsg() (v string) {
	return p.ErrMsg
}
func (p *MetricScore) SetInspectionMetric(val InspectionMetric) {
	p.InspectionMetric = val
}
func (p *MetricScore) SetScore(val int8) {
	p.Score = val
}
func (p *MetricScore) SetDescription(val string) {
	p.Description = val
}
func (p *MetricScore) SetErrMsg(val string) {
	p.ErrMsg = val
}

var fieldIDToName_MetricScore = map[int16]string{
	1: "InspectionMetric",
	2: "Score",
	3: "Description",
	4: "ErrMsg",
}

func (p *MetricScore) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("MetricScore")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInspectionMetric bool = false
	var issetScore bool = false
	var issetDescription bool = false
	var issetErrMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionMetric = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BYTE {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetScore = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInspectionMetric {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetScore {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetDescription {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetErrMsg {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_MetricScore[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_MetricScore[fieldId]))
}

func (p *MetricScore) ReadField1(iprot thrift.TProtocol) error {

	var _field InspectionMetric
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InspectionMetric(v)
	}
	p.InspectionMetric = _field
	return nil
}
func (p *MetricScore) ReadField2(iprot thrift.TProtocol) error {

	var _field int8
	if v, err := iprot.ReadByte(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Score = _field
	return nil
}
func (p *MetricScore) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *MetricScore) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrMsg = _field
	return nil
}

func (p *MetricScore) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("MetricScore")

	var fieldId int16
	if err = oprot.WriteStructBegin("MetricScore"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *MetricScore) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionMetric", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InspectionMetric)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *MetricScore) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Score", thrift.BYTE, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteByte(p.Score); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *MetricScore) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *MetricScore) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrMsg", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *MetricScore) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MetricScore(%+v)", *p)

}

func (p *MetricScore) DeepEqual(ano *MetricScore) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InspectionMetric) {
		return false
	}
	if !p.Field2DeepEqual(ano.Score) {
		return false
	}
	if !p.Field3DeepEqual(ano.Description) {
		return false
	}
	if !p.Field4DeepEqual(ano.ErrMsg) {
		return false
	}
	return true
}

func (p *MetricScore) Field1DeepEqual(src InspectionMetric) bool {

	if p.InspectionMetric != src {
		return false
	}
	return true
}
func (p *MetricScore) Field2DeepEqual(src int8) bool {

	if p.Score != src {
		return false
	}
	return true
}
func (p *MetricScore) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Description, src) != 0 {
		return false
	}
	return true
}
func (p *MetricScore) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ErrMsg, src) != 0 {
		return false
	}
	return true
}

type InspectionDataMetric struct {
	DataPoints  []*DataPoint `thrift:"DataPoints,1,required" frugal:"1,required,list<DataPoint>" json:"DataPoints"`
	Description string       `thrift:"Description,2,required" frugal:"2,required,string" json:"Description"`
	Unit        string       `thrift:"Unit,3,required" frugal:"3,required,string" json:"Unit"`
	Avg         float64      `thrift:"Avg,4,required" frugal:"4,required,double" json:"Avg"`
	Min         float64      `thrift:"Min,5,required" frugal:"5,required,double" json:"Min"`
	Max         float64      `thrift:"Max,6,required" frugal:"6,required,double" json:"Max"`
	NodeId      *string      `thrift:"NodeId,7,optional" frugal:"7,optional,string" json:"NodeId,omitempty"`
	ErrMsg      string       `thrift:"ErrMsg,8,required" frugal:"8,required,string" json:"ErrMsg"`
}

func NewInspectionDataMetric() *InspectionDataMetric {
	return &InspectionDataMetric{}
}

func (p *InspectionDataMetric) InitDefault() {
}

func (p *InspectionDataMetric) GetDataPoints() (v []*DataPoint) {
	return p.DataPoints
}

func (p *InspectionDataMetric) GetDescription() (v string) {
	return p.Description
}

func (p *InspectionDataMetric) GetUnit() (v string) {
	return p.Unit
}

func (p *InspectionDataMetric) GetAvg() (v float64) {
	return p.Avg
}

func (p *InspectionDataMetric) GetMin() (v float64) {
	return p.Min
}

func (p *InspectionDataMetric) GetMax() (v float64) {
	return p.Max
}

var InspectionDataMetric_NodeId_DEFAULT string

func (p *InspectionDataMetric) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return InspectionDataMetric_NodeId_DEFAULT
	}
	return *p.NodeId
}

func (p *InspectionDataMetric) GetErrMsg() (v string) {
	return p.ErrMsg
}
func (p *InspectionDataMetric) SetDataPoints(val []*DataPoint) {
	p.DataPoints = val
}
func (p *InspectionDataMetric) SetDescription(val string) {
	p.Description = val
}
func (p *InspectionDataMetric) SetUnit(val string) {
	p.Unit = val
}
func (p *InspectionDataMetric) SetAvg(val float64) {
	p.Avg = val
}
func (p *InspectionDataMetric) SetMin(val float64) {
	p.Min = val
}
func (p *InspectionDataMetric) SetMax(val float64) {
	p.Max = val
}
func (p *InspectionDataMetric) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *InspectionDataMetric) SetErrMsg(val string) {
	p.ErrMsg = val
}

var fieldIDToName_InspectionDataMetric = map[int16]string{
	1: "DataPoints",
	2: "Description",
	3: "Unit",
	4: "Avg",
	5: "Min",
	6: "Max",
	7: "NodeId",
	8: "ErrMsg",
}

func (p *InspectionDataMetric) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *InspectionDataMetric) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionDataMetric")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDataPoints bool = false
	var issetDescription bool = false
	var issetUnit bool = false
	var issetAvg bool = false
	var issetMin bool = false
	var issetMax bool = false
	var issetErrMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataPoints = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetUnit = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetAvg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetMin = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.DOUBLE {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetMax = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDataPoints {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetDescription {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetUnit {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetAvg {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetMin {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetMax {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetErrMsg {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionDataMetric[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionDataMetric[fieldId]))
}

func (p *InspectionDataMetric) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DataPoint, 0, size)
	values := make([]DataPoint, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DataPoints = _field
	return nil
}
func (p *InspectionDataMetric) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *InspectionDataMetric) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Unit = _field
	return nil
}
func (p *InspectionDataMetric) ReadField4(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Avg = _field
	return nil
}
func (p *InspectionDataMetric) ReadField5(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Min = _field
	return nil
}
func (p *InspectionDataMetric) ReadField6(iprot thrift.TProtocol) error {

	var _field float64
	if v, err := iprot.ReadDouble(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Max = _field
	return nil
}
func (p *InspectionDataMetric) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *InspectionDataMetric) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrMsg = _field
	return nil
}

func (p *InspectionDataMetric) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionDataMetric")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionDataMetric"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionDataMetric) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataPoints", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DataPoints)); err != nil {
		return err
	}
	for _, v := range p.DataPoints {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionDataMetric) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionDataMetric) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Unit", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Unit); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionDataMetric) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Avg", thrift.DOUBLE, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Avg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InspectionDataMetric) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Min", thrift.DOUBLE, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Min); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InspectionDataMetric) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Max", thrift.DOUBLE, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteDouble(p.Max); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InspectionDataMetric) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *InspectionDataMetric) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrMsg", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *InspectionDataMetric) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionDataMetric(%+v)", *p)

}

func (p *InspectionDataMetric) DeepEqual(ano *InspectionDataMetric) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DataPoints) {
		return false
	}
	if !p.Field2DeepEqual(ano.Description) {
		return false
	}
	if !p.Field3DeepEqual(ano.Unit) {
		return false
	}
	if !p.Field4DeepEqual(ano.Avg) {
		return false
	}
	if !p.Field5DeepEqual(ano.Min) {
		return false
	}
	if !p.Field6DeepEqual(ano.Max) {
		return false
	}
	if !p.Field7DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field8DeepEqual(ano.ErrMsg) {
		return false
	}
	return true
}

func (p *InspectionDataMetric) Field1DeepEqual(src []*DataPoint) bool {

	if len(p.DataPoints) != len(src) {
		return false
	}
	for i, v := range p.DataPoints {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *InspectionDataMetric) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Description, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionDataMetric) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Unit, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionDataMetric) Field4DeepEqual(src float64) bool {

	if p.Avg != src {
		return false
	}
	return true
}
func (p *InspectionDataMetric) Field5DeepEqual(src float64) bool {

	if p.Min != src {
		return false
	}
	return true
}
func (p *InspectionDataMetric) Field6DeepEqual(src float64) bool {

	if p.Max != src {
		return false
	}
	return true
}
func (p *InspectionDataMetric) Field7DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *InspectionDataMetric) Field8DeepEqual(src string) bool {

	if strings.Compare(p.ErrMsg, src) != 0 {
		return false
	}
	return true
}

type DescribeBigKeysReq struct {
	InstanceId     string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	PageSize       int32  `thrift:"PageSize,2,required" frugal:"2,required,i32" json:"PageSize"`
	QueryStartTime string `thrift:"QueryStartTime,3,required" frugal:"3,required,string" json:"QueryStartTime"`
	QueryEndTime   string `thrift:"QueryEndTime,4,required" frugal:"4,required,string" json:"QueryEndTime"`
	KeyType        string `thrift:"KeyType,5,required" frugal:"5,required,string" json:"KeyType"`
	OrderBy        string `thrift:"OrderBy,6,required" frugal:"6,required,string" json:"OrderBy"`
}

func NewDescribeBigKeysReq() *DescribeBigKeysReq {
	return &DescribeBigKeysReq{}
}

func (p *DescribeBigKeysReq) InitDefault() {
}

func (p *DescribeBigKeysReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeBigKeysReq) GetPageSize() (v int32) {
	return p.PageSize
}

func (p *DescribeBigKeysReq) GetQueryStartTime() (v string) {
	return p.QueryStartTime
}

func (p *DescribeBigKeysReq) GetQueryEndTime() (v string) {
	return p.QueryEndTime
}

func (p *DescribeBigKeysReq) GetKeyType() (v string) {
	return p.KeyType
}

func (p *DescribeBigKeysReq) GetOrderBy() (v string) {
	return p.OrderBy
}
func (p *DescribeBigKeysReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeBigKeysReq) SetPageSize(val int32) {
	p.PageSize = val
}
func (p *DescribeBigKeysReq) SetQueryStartTime(val string) {
	p.QueryStartTime = val
}
func (p *DescribeBigKeysReq) SetQueryEndTime(val string) {
	p.QueryEndTime = val
}
func (p *DescribeBigKeysReq) SetKeyType(val string) {
	p.KeyType = val
}
func (p *DescribeBigKeysReq) SetOrderBy(val string) {
	p.OrderBy = val
}

var fieldIDToName_DescribeBigKeysReq = map[int16]string{
	1: "InstanceId",
	2: "PageSize",
	3: "QueryStartTime",
	4: "QueryEndTime",
	5: "KeyType",
	6: "OrderBy",
}

func (p *DescribeBigKeysReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBigKeysReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetPageSize bool = false
	var issetQueryStartTime bool = false
	var issetQueryEndTime bool = false
	var issetKeyType bool = false
	var issetOrderBy bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetQueryStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetQueryEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetKeyType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderBy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetQueryStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetQueryEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetKeyType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetOrderBy {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBigKeysReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBigKeysReq[fieldId]))
}

func (p *DescribeBigKeysReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeBigKeysReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeBigKeysReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.QueryStartTime = _field
	return nil
}
func (p *DescribeBigKeysReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.QueryEndTime = _field
	return nil
}
func (p *DescribeBigKeysReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.KeyType = _field
	return nil
}
func (p *DescribeBigKeysReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderBy = _field
	return nil
}

func (p *DescribeBigKeysReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBigKeysReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBigKeysReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBigKeysReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBigKeysReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBigKeysReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("QueryStartTime", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.QueryStartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeBigKeysReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("QueryEndTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.QueryEndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeBigKeysReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("KeyType", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.KeyType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeBigKeysReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderBy", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderBy); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeBigKeysReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBigKeysReq(%+v)", *p)

}

func (p *DescribeBigKeysReq) DeepEqual(ano *DescribeBigKeysReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.QueryStartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.QueryEndTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.KeyType) {
		return false
	}
	if !p.Field6DeepEqual(ano.OrderBy) {
		return false
	}
	return true
}

func (p *DescribeBigKeysReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBigKeysReq) Field2DeepEqual(src int32) bool {

	if p.PageSize != src {
		return false
	}
	return true
}
func (p *DescribeBigKeysReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.QueryStartTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBigKeysReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.QueryEndTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBigKeysReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.KeyType, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeBigKeysReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.OrderBy, src) != 0 {
		return false
	}
	return true
}

type DescribeBigKeysResp struct {
	InspectionItems []*InspectionBigKeysItem `thrift:"InspectionItems,1,required" frugal:"1,required,list<InspectionBigKeysItem>" json:"InspectionItems"`
	Total           int32                    `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeBigKeysResp() *DescribeBigKeysResp {
	return &DescribeBigKeysResp{}
}

func (p *DescribeBigKeysResp) InitDefault() {
}

func (p *DescribeBigKeysResp) GetInspectionItems() (v []*InspectionBigKeysItem) {
	return p.InspectionItems
}

func (p *DescribeBigKeysResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeBigKeysResp) SetInspectionItems(val []*InspectionBigKeysItem) {
	p.InspectionItems = val
}
func (p *DescribeBigKeysResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeBigKeysResp = map[int16]string{
	1: "InspectionItems",
	2: "Total",
}

func (p *DescribeBigKeysResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBigKeysResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInspectionItems bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInspectionItems {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeBigKeysResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeBigKeysResp[fieldId]))
}

func (p *DescribeBigKeysResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionBigKeysItem, 0, size)
	values := make([]InspectionBigKeysItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InspectionItems = _field
	return nil
}
func (p *DescribeBigKeysResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeBigKeysResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeBigKeysResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeBigKeysResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeBigKeysResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionItems", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InspectionItems)); err != nil {
		return err
	}
	for _, v := range p.InspectionItems {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeBigKeysResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeBigKeysResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeBigKeysResp(%+v)", *p)

}

func (p *DescribeBigKeysResp) DeepEqual(ano *DescribeBigKeysResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InspectionItems) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeBigKeysResp) Field1DeepEqual(src []*InspectionBigKeysItem) bool {

	if len(p.InspectionItems) != len(src) {
		return false
	}
	for i, v := range p.InspectionItems {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeBigKeysResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeHotKeysReq struct {
	InstanceId     string `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	PageSize       int32  `thrift:"PageSize,2,required" frugal:"2,required,i32" json:"PageSize"`
	QueryStartTime string `thrift:"QueryStartTime,3,required" frugal:"3,required,string" json:"QueryStartTime"`
	QueryEndTime   string `thrift:"QueryEndTime,4,required" frugal:"4,required,string" json:"QueryEndTime"`
	KeyType        string `thrift:"KeyType,5,required" frugal:"5,required,string" json:"KeyType"`
	OrderBy        string `thrift:"OrderBy,6,required" frugal:"6,required,string" json:"OrderBy"`
}

func NewDescribeHotKeysReq() *DescribeHotKeysReq {
	return &DescribeHotKeysReq{}
}

func (p *DescribeHotKeysReq) InitDefault() {
}

func (p *DescribeHotKeysReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeHotKeysReq) GetPageSize() (v int32) {
	return p.PageSize
}

func (p *DescribeHotKeysReq) GetQueryStartTime() (v string) {
	return p.QueryStartTime
}

func (p *DescribeHotKeysReq) GetQueryEndTime() (v string) {
	return p.QueryEndTime
}

func (p *DescribeHotKeysReq) GetKeyType() (v string) {
	return p.KeyType
}

func (p *DescribeHotKeysReq) GetOrderBy() (v string) {
	return p.OrderBy
}
func (p *DescribeHotKeysReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeHotKeysReq) SetPageSize(val int32) {
	p.PageSize = val
}
func (p *DescribeHotKeysReq) SetQueryStartTime(val string) {
	p.QueryStartTime = val
}
func (p *DescribeHotKeysReq) SetQueryEndTime(val string) {
	p.QueryEndTime = val
}
func (p *DescribeHotKeysReq) SetKeyType(val string) {
	p.KeyType = val
}
func (p *DescribeHotKeysReq) SetOrderBy(val string) {
	p.OrderBy = val
}

var fieldIDToName_DescribeHotKeysReq = map[int16]string{
	1: "InstanceId",
	2: "PageSize",
	3: "QueryStartTime",
	4: "QueryEndTime",
	5: "KeyType",
	6: "OrderBy",
}

func (p *DescribeHotKeysReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeHotKeysReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetPageSize bool = false
	var issetQueryStartTime bool = false
	var issetQueryEndTime bool = false
	var issetKeyType bool = false
	var issetOrderBy bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetPageSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetQueryStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetQueryEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetKeyType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetOrderBy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetPageSize {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetQueryStartTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetQueryEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetKeyType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetOrderBy {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeHotKeysReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeHotKeysReq[fieldId]))
}

func (p *DescribeHotKeysReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeHotKeysReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeHotKeysReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.QueryStartTime = _field
	return nil
}
func (p *DescribeHotKeysReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.QueryEndTime = _field
	return nil
}
func (p *DescribeHotKeysReq) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.KeyType = _field
	return nil
}
func (p *DescribeHotKeysReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OrderBy = _field
	return nil
}

func (p *DescribeHotKeysReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeHotKeysReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeHotKeysReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeHotKeysReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeHotKeysReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.PageSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeHotKeysReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("QueryStartTime", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.QueryStartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeHotKeysReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("QueryEndTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.QueryEndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeHotKeysReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("KeyType", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.KeyType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeHotKeysReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OrderBy", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.OrderBy); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeHotKeysReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeHotKeysReq(%+v)", *p)

}

func (p *DescribeHotKeysReq) DeepEqual(ano *DescribeHotKeysReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field3DeepEqual(ano.QueryStartTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.QueryEndTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.KeyType) {
		return false
	}
	if !p.Field6DeepEqual(ano.OrderBy) {
		return false
	}
	return true
}

func (p *DescribeHotKeysReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeHotKeysReq) Field2DeepEqual(src int32) bool {

	if p.PageSize != src {
		return false
	}
	return true
}
func (p *DescribeHotKeysReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.QueryStartTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeHotKeysReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.QueryEndTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeHotKeysReq) Field5DeepEqual(src string) bool {

	if strings.Compare(p.KeyType, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeHotKeysReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.OrderBy, src) != 0 {
		return false
	}
	return true
}

type DescribeHotKeysResp struct {
	InspectionItems []*InspectionHotKeysItem `thrift:"InspectionItems,1,required" frugal:"1,required,list<InspectionHotKeysItem>" json:"InspectionItems"`
	Total           int32                    `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeHotKeysResp() *DescribeHotKeysResp {
	return &DescribeHotKeysResp{}
}

func (p *DescribeHotKeysResp) InitDefault() {
}

func (p *DescribeHotKeysResp) GetInspectionItems() (v []*InspectionHotKeysItem) {
	return p.InspectionItems
}

func (p *DescribeHotKeysResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeHotKeysResp) SetInspectionItems(val []*InspectionHotKeysItem) {
	p.InspectionItems = val
}
func (p *DescribeHotKeysResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeHotKeysResp = map[int16]string{
	1: "InspectionItems",
	2: "Total",
}

func (p *DescribeHotKeysResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeHotKeysResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInspectionItems bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInspectionItems {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeHotKeysResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeHotKeysResp[fieldId]))
}

func (p *DescribeHotKeysResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionHotKeysItem, 0, size)
	values := make([]InspectionHotKeysItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InspectionItems = _field
	return nil
}
func (p *DescribeHotKeysResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeHotKeysResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeHotKeysResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeHotKeysResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeHotKeysResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionItems", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InspectionItems)); err != nil {
		return err
	}
	for _, v := range p.InspectionItems {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeHotKeysResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeHotKeysResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeHotKeysResp(%+v)", *p)

}

func (p *DescribeHotKeysResp) DeepEqual(ano *DescribeHotKeysResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InspectionItems) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeHotKeysResp) Field1DeepEqual(src []*InspectionHotKeysItem) bool {

	if len(p.InspectionItems) != len(src) {
		return false
	}
	for i, v := range p.InspectionItems {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeHotKeysResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type InspectionBigKeysItem struct {
	DBName    string `thrift:"DBName,1,required" frugal:"1,required,string" json:"DBName"`
	KeyInfo   string `thrift:"KeyInfo,2,required" frugal:"2,required,string" json:"KeyInfo"`
	KeyType   string `thrift:"KeyType,3,required" frugal:"3,required,string" json:"KeyType"`
	ValueLen  string `thrift:"ValueLen,4,required" frugal:"4,required,string" json:"ValueLen"`
	ValueSize string `thrift:"ValueSize,5,required" frugal:"5,required,string" json:"ValueSize"`
}

func NewInspectionBigKeysItem() *InspectionBigKeysItem {
	return &InspectionBigKeysItem{}
}

func (p *InspectionBigKeysItem) InitDefault() {
}

func (p *InspectionBigKeysItem) GetDBName() (v string) {
	return p.DBName
}

func (p *InspectionBigKeysItem) GetKeyInfo() (v string) {
	return p.KeyInfo
}

func (p *InspectionBigKeysItem) GetKeyType() (v string) {
	return p.KeyType
}

func (p *InspectionBigKeysItem) GetValueLen() (v string) {
	return p.ValueLen
}

func (p *InspectionBigKeysItem) GetValueSize() (v string) {
	return p.ValueSize
}
func (p *InspectionBigKeysItem) SetDBName(val string) {
	p.DBName = val
}
func (p *InspectionBigKeysItem) SetKeyInfo(val string) {
	p.KeyInfo = val
}
func (p *InspectionBigKeysItem) SetKeyType(val string) {
	p.KeyType = val
}
func (p *InspectionBigKeysItem) SetValueLen(val string) {
	p.ValueLen = val
}
func (p *InspectionBigKeysItem) SetValueSize(val string) {
	p.ValueSize = val
}

var fieldIDToName_InspectionBigKeysItem = map[int16]string{
	1: "DBName",
	2: "KeyInfo",
	3: "KeyType",
	4: "ValueLen",
	5: "ValueSize",
}

func (p *InspectionBigKeysItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionBigKeysItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDBName bool = false
	var issetKeyInfo bool = false
	var issetKeyType bool = false
	var issetValueLen bool = false
	var issetValueSize bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetKeyInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetKeyType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetValueLen = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetValueSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDBName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetKeyInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetKeyType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetValueLen {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetValueSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionBigKeysItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionBigKeysItem[fieldId]))
}

func (p *InspectionBigKeysItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *InspectionBigKeysItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.KeyInfo = _field
	return nil
}
func (p *InspectionBigKeysItem) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.KeyType = _field
	return nil
}
func (p *InspectionBigKeysItem) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ValueLen = _field
	return nil
}
func (p *InspectionBigKeysItem) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ValueSize = _field
	return nil
}

func (p *InspectionBigKeysItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionBigKeysItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionBigKeysItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionBigKeysItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionBigKeysItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("KeyInfo", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.KeyInfo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionBigKeysItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("KeyType", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.KeyType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionBigKeysItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ValueLen", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ValueLen); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InspectionBigKeysItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ValueSize", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ValueSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InspectionBigKeysItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionBigKeysItem(%+v)", *p)

}

func (p *InspectionBigKeysItem) DeepEqual(ano *InspectionBigKeysItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field2DeepEqual(ano.KeyInfo) {
		return false
	}
	if !p.Field3DeepEqual(ano.KeyType) {
		return false
	}
	if !p.Field4DeepEqual(ano.ValueLen) {
		return false
	}
	if !p.Field5DeepEqual(ano.ValueSize) {
		return false
	}
	return true
}

func (p *InspectionBigKeysItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionBigKeysItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.KeyInfo, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionBigKeysItem) Field3DeepEqual(src string) bool {

	if strings.Compare(p.KeyType, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionBigKeysItem) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ValueLen, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionBigKeysItem) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ValueSize, src) != 0 {
		return false
	}
	return true
}

type InspectionHotKeysItem struct {
	DBName     string `thrift:"DBName,1,required" frugal:"1,required,string" json:"DBName"`
	KeyInfo    string `thrift:"KeyInfo,2,required" frugal:"2,required,string" json:"KeyInfo"`
	KeyType    string `thrift:"KeyType,3,required" frugal:"3,required,string" json:"KeyType"`
	ValueLen   string `thrift:"ValueLen,4,required" frugal:"4,required,string" json:"ValueLen"`
	ValueSize  string `thrift:"ValueSize,5,required" frugal:"5,required,string" json:"ValueSize"`
	QueryCount string `thrift:"QueryCount,6,required" frugal:"6,required,string" json:"QueryCount"`
}

func NewInspectionHotKeysItem() *InspectionHotKeysItem {
	return &InspectionHotKeysItem{}
}

func (p *InspectionHotKeysItem) InitDefault() {
}

func (p *InspectionHotKeysItem) GetDBName() (v string) {
	return p.DBName
}

func (p *InspectionHotKeysItem) GetKeyInfo() (v string) {
	return p.KeyInfo
}

func (p *InspectionHotKeysItem) GetKeyType() (v string) {
	return p.KeyType
}

func (p *InspectionHotKeysItem) GetValueLen() (v string) {
	return p.ValueLen
}

func (p *InspectionHotKeysItem) GetValueSize() (v string) {
	return p.ValueSize
}

func (p *InspectionHotKeysItem) GetQueryCount() (v string) {
	return p.QueryCount
}
func (p *InspectionHotKeysItem) SetDBName(val string) {
	p.DBName = val
}
func (p *InspectionHotKeysItem) SetKeyInfo(val string) {
	p.KeyInfo = val
}
func (p *InspectionHotKeysItem) SetKeyType(val string) {
	p.KeyType = val
}
func (p *InspectionHotKeysItem) SetValueLen(val string) {
	p.ValueLen = val
}
func (p *InspectionHotKeysItem) SetValueSize(val string) {
	p.ValueSize = val
}
func (p *InspectionHotKeysItem) SetQueryCount(val string) {
	p.QueryCount = val
}

var fieldIDToName_InspectionHotKeysItem = map[int16]string{
	1: "DBName",
	2: "KeyInfo",
	3: "KeyType",
	4: "ValueLen",
	5: "ValueSize",
	6: "QueryCount",
}

func (p *InspectionHotKeysItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionHotKeysItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDBName bool = false
	var issetKeyInfo bool = false
	var issetKeyType bool = false
	var issetValueLen bool = false
	var issetValueSize bool = false
	var issetQueryCount bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetKeyInfo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetKeyType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetValueLen = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetValueSize = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetQueryCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDBName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetKeyInfo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetKeyType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetValueLen {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetValueSize {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetQueryCount {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionHotKeysItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionHotKeysItem[fieldId]))
}

func (p *InspectionHotKeysItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *InspectionHotKeysItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.KeyInfo = _field
	return nil
}
func (p *InspectionHotKeysItem) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.KeyType = _field
	return nil
}
func (p *InspectionHotKeysItem) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ValueLen = _field
	return nil
}
func (p *InspectionHotKeysItem) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ValueSize = _field
	return nil
}
func (p *InspectionHotKeysItem) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.QueryCount = _field
	return nil
}

func (p *InspectionHotKeysItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionHotKeysItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionHotKeysItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionHotKeysItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionHotKeysItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("KeyInfo", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.KeyInfo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionHotKeysItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("KeyType", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.KeyType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionHotKeysItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ValueLen", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ValueLen); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InspectionHotKeysItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ValueSize", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ValueSize); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *InspectionHotKeysItem) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("QueryCount", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.QueryCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *InspectionHotKeysItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionHotKeysItem(%+v)", *p)

}

func (p *InspectionHotKeysItem) DeepEqual(ano *InspectionHotKeysItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field2DeepEqual(ano.KeyInfo) {
		return false
	}
	if !p.Field3DeepEqual(ano.KeyType) {
		return false
	}
	if !p.Field4DeepEqual(ano.ValueLen) {
		return false
	}
	if !p.Field5DeepEqual(ano.ValueSize) {
		return false
	}
	if !p.Field6DeepEqual(ano.QueryCount) {
		return false
	}
	return true
}

func (p *InspectionHotKeysItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionHotKeysItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.KeyInfo, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionHotKeysItem) Field3DeepEqual(src string) bool {

	if strings.Compare(p.KeyType, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionHotKeysItem) Field4DeepEqual(src string) bool {

	if strings.Compare(p.ValueLen, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionHotKeysItem) Field5DeepEqual(src string) bool {

	if strings.Compare(p.ValueSize, src) != 0 {
		return false
	}
	return true
}
func (p *InspectionHotKeysItem) Field6DeepEqual(src string) bool {

	if strings.Compare(p.QueryCount, src) != 0 {
		return false
	}
	return true
}

type InspectionUnit struct {
	InspectionMetric InspectionMetric `thrift:"InspectionMetric,1,required" frugal:"1,required,InspectionMetric" json:"InspectionMetric"`
	ValueType        *ValueType       `thrift:"ValueType,2,optional" frugal:"2,optional,ValueType" json:"ValueType,omitempty"`
	DownSampleType   *DownSampleType  `thrift:"DownSampleType,3,optional" frugal:"3,optional,DownSampleType" json:"DownSampleType,omitempty"`
	InspectLevel     *InspectLevel    `thrift:"InspectLevel,4,optional" frugal:"4,optional,InspectLevel" json:"InspectLevel,omitempty"`
}

func NewInspectionUnit() *InspectionUnit {
	return &InspectionUnit{}
}

func (p *InspectionUnit) InitDefault() {
}

func (p *InspectionUnit) GetInspectionMetric() (v InspectionMetric) {
	return p.InspectionMetric
}

var InspectionUnit_ValueType_DEFAULT ValueType

func (p *InspectionUnit) GetValueType() (v ValueType) {
	if !p.IsSetValueType() {
		return InspectionUnit_ValueType_DEFAULT
	}
	return *p.ValueType
}

var InspectionUnit_DownSampleType_DEFAULT DownSampleType

func (p *InspectionUnit) GetDownSampleType() (v DownSampleType) {
	if !p.IsSetDownSampleType() {
		return InspectionUnit_DownSampleType_DEFAULT
	}
	return *p.DownSampleType
}

var InspectionUnit_InspectLevel_DEFAULT InspectLevel

func (p *InspectionUnit) GetInspectLevel() (v InspectLevel) {
	if !p.IsSetInspectLevel() {
		return InspectionUnit_InspectLevel_DEFAULT
	}
	return *p.InspectLevel
}
func (p *InspectionUnit) SetInspectionMetric(val InspectionMetric) {
	p.InspectionMetric = val
}
func (p *InspectionUnit) SetValueType(val *ValueType) {
	p.ValueType = val
}
func (p *InspectionUnit) SetDownSampleType(val *DownSampleType) {
	p.DownSampleType = val
}
func (p *InspectionUnit) SetInspectLevel(val *InspectLevel) {
	p.InspectLevel = val
}

var fieldIDToName_InspectionUnit = map[int16]string{
	1: "InspectionMetric",
	2: "ValueType",
	3: "DownSampleType",
	4: "InspectLevel",
}

func (p *InspectionUnit) IsSetValueType() bool {
	return p.ValueType != nil
}

func (p *InspectionUnit) IsSetDownSampleType() bool {
	return p.DownSampleType != nil
}

func (p *InspectionUnit) IsSetInspectLevel() bool {
	return p.InspectLevel != nil
}

func (p *InspectionUnit) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionUnit")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInspectionMetric bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionMetric = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInspectionMetric {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionUnit[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionUnit[fieldId]))
}

func (p *InspectionUnit) ReadField1(iprot thrift.TProtocol) error {

	var _field InspectionMetric
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InspectionMetric(v)
	}
	p.InspectionMetric = _field
	return nil
}
func (p *InspectionUnit) ReadField2(iprot thrift.TProtocol) error {

	var _field *ValueType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ValueType(v)
		_field = &tmp
	}
	p.ValueType = _field
	return nil
}
func (p *InspectionUnit) ReadField3(iprot thrift.TProtocol) error {

	var _field *DownSampleType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DownSampleType(v)
		_field = &tmp
	}
	p.DownSampleType = _field
	return nil
}
func (p *InspectionUnit) ReadField4(iprot thrift.TProtocol) error {

	var _field *InspectLevel
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InspectLevel(v)
		_field = &tmp
	}
	p.InspectLevel = _field
	return nil
}

func (p *InspectionUnit) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionUnit")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionUnit"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionUnit) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionMetric", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InspectionMetric)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionUnit) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetValueType() {
		if err = oprot.WriteFieldBegin("ValueType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ValueType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionUnit) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDownSampleType() {
		if err = oprot.WriteFieldBegin("DownSampleType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.DownSampleType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionUnit) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInspectLevel() {
		if err = oprot.WriteFieldBegin("InspectLevel", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InspectLevel)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *InspectionUnit) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionUnit(%+v)", *p)

}

func (p *InspectionUnit) DeepEqual(ano *InspectionUnit) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InspectionMetric) {
		return false
	}
	if !p.Field2DeepEqual(ano.ValueType) {
		return false
	}
	if !p.Field3DeepEqual(ano.DownSampleType) {
		return false
	}
	if !p.Field4DeepEqual(ano.InspectLevel) {
		return false
	}
	return true
}

func (p *InspectionUnit) Field1DeepEqual(src InspectionMetric) bool {

	if p.InspectionMetric != src {
		return false
	}
	return true
}
func (p *InspectionUnit) Field2DeepEqual(src *ValueType) bool {

	if p.ValueType == src {
		return true
	} else if p.ValueType == nil || src == nil {
		return false
	}
	if *p.ValueType != *src {
		return false
	}
	return true
}
func (p *InspectionUnit) Field3DeepEqual(src *DownSampleType) bool {

	if p.DownSampleType == src {
		return true
	} else if p.DownSampleType == nil || src == nil {
		return false
	}
	if *p.DownSampleType != *src {
		return false
	}
	return true
}
func (p *InspectionUnit) Field4DeepEqual(src *InspectLevel) bool {

	if p.InspectLevel == src {
		return true
	} else if p.InspectLevel == nil || src == nil {
		return false
	}
	if *p.InspectLevel != *src {
		return false
	}
	return true
}

type InspectionConfig struct {
	InstanceType    InstanceType      `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	NodeId          *string           `thrift:"NodeId,2,optional" frugal:"2,optional,string" json:"NodeId,omitempty"`
	InspectionUnits []*InspectionUnit `thrift:"InspectionUnits,3,required" frugal:"3,required,list<InspectionUnit>" json:"InspectionUnits"`
}

func NewInspectionConfig() *InspectionConfig {
	return &InspectionConfig{}
}

func (p *InspectionConfig) InitDefault() {
}

func (p *InspectionConfig) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

var InspectionConfig_NodeId_DEFAULT string

func (p *InspectionConfig) GetNodeId() (v string) {
	if !p.IsSetNodeId() {
		return InspectionConfig_NodeId_DEFAULT
	}
	return *p.NodeId
}

func (p *InspectionConfig) GetInspectionUnits() (v []*InspectionUnit) {
	return p.InspectionUnits
}
func (p *InspectionConfig) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *InspectionConfig) SetNodeId(val *string) {
	p.NodeId = val
}
func (p *InspectionConfig) SetInspectionUnits(val []*InspectionUnit) {
	p.InspectionUnits = val
}

var fieldIDToName_InspectionConfig = map[int16]string{
	1: "InstanceType",
	2: "NodeId",
	3: "InspectionUnits",
}

func (p *InspectionConfig) IsSetNodeId() bool {
	return p.NodeId != nil
}

func (p *InspectionConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInspectionUnits bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionUnits = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInspectionUnits {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_InspectionConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_InspectionConfig[fieldId]))
}

func (p *InspectionConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *InspectionConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeId = _field
	return nil
}
func (p *InspectionConfig) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionUnit, 0, size)
	values := make([]InspectionUnit, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InspectionUnits = _field
	return nil
}

func (p *InspectionConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("InspectionConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("InspectionConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *InspectionConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *InspectionConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeId() {
		if err = oprot.WriteFieldBegin("NodeId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *InspectionConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionUnits", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InspectionUnits)); err != nil {
		return err
	}
	for _, v := range p.InspectionUnits {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *InspectionConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InspectionConfig(%+v)", *p)

}

func (p *InspectionConfig) DeepEqual(ano *InspectionConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.NodeId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InspectionUnits) {
		return false
	}
	return true
}

func (p *InspectionConfig) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *InspectionConfig) Field2DeepEqual(src *string) bool {

	if p.NodeId == src {
		return true
	} else if p.NodeId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeId, *src) != 0 {
		return false
	}
	return true
}
func (p *InspectionConfig) Field3DeepEqual(src []*InspectionUnit) bool {

	if len(p.InspectionUnits) != len(src) {
		return false
	}
	for i, v := range p.InspectionUnits {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ModifyInspectionConfigReq struct {
	RegionId         string              `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceType     InstanceType        `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	InstanceId       []string            `thrift:"InstanceId,3,required" frugal:"3,required,list<string>" json:"InstanceId"`
	InspectionConfig []*InspectionConfig `thrift:"InspectionConfig,4,required" frugal:"4,required,list<InspectionConfig>" json:"InspectionConfig"`
	OverWriteType    OverWriteType       `thrift:"OverWriteType,5,required" frugal:"5,required,OverWriteType" json:"OverWriteType"`
}

func NewModifyInspectionConfigReq() *ModifyInspectionConfigReq {
	return &ModifyInspectionConfigReq{}
}

func (p *ModifyInspectionConfigReq) InitDefault() {
}

func (p *ModifyInspectionConfigReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *ModifyInspectionConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *ModifyInspectionConfigReq) GetInstanceId() (v []string) {
	return p.InstanceId
}

func (p *ModifyInspectionConfigReq) GetInspectionConfig() (v []*InspectionConfig) {
	return p.InspectionConfig
}

func (p *ModifyInspectionConfigReq) GetOverWriteType() (v OverWriteType) {
	return p.OverWriteType
}
func (p *ModifyInspectionConfigReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *ModifyInspectionConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *ModifyInspectionConfigReq) SetInstanceId(val []string) {
	p.InstanceId = val
}
func (p *ModifyInspectionConfigReq) SetInspectionConfig(val []*InspectionConfig) {
	p.InspectionConfig = val
}
func (p *ModifyInspectionConfigReq) SetOverWriteType(val OverWriteType) {
	p.OverWriteType = val
}

var fieldIDToName_ModifyInspectionConfigReq = map[int16]string{
	1: "RegionId",
	2: "InstanceType",
	3: "InstanceId",
	4: "InspectionConfig",
	5: "OverWriteType",
}

func (p *ModifyInspectionConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInspectionConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetInspectionConfig bool = false
	var issetOverWriteType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetInspectionConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetOverWriteType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetInspectionConfig {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetOverWriteType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyInspectionConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyInspectionConfigReq[fieldId]))
}

func (p *ModifyInspectionConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *ModifyInspectionConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *ModifyInspectionConfigReq) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyInspectionConfigReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionConfig, 0, size)
	values := make([]InspectionConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InspectionConfig = _field
	return nil
}
func (p *ModifyInspectionConfigReq) ReadField5(iprot thrift.TProtocol) error {

	var _field OverWriteType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = OverWriteType(v)
	}
	p.OverWriteType = _field
	return nil
}

func (p *ModifyInspectionConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInspectionConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyInspectionConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyInspectionConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyInspectionConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyInspectionConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.InstanceId)); err != nil {
		return err
	}
	for _, v := range p.InstanceId {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyInspectionConfigReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InspectionConfig", thrift.LIST, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InspectionConfig)); err != nil {
		return err
	}
	for _, v := range p.InspectionConfig {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyInspectionConfigReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OverWriteType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.OverWriteType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyInspectionConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyInspectionConfigReq(%+v)", *p)

}

func (p *ModifyInspectionConfigReq) DeepEqual(ano *ModifyInspectionConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field4DeepEqual(ano.InspectionConfig) {
		return false
	}
	if !p.Field5DeepEqual(ano.OverWriteType) {
		return false
	}
	return true
}

func (p *ModifyInspectionConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyInspectionConfigReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *ModifyInspectionConfigReq) Field3DeepEqual(src []string) bool {

	if len(p.InstanceId) != len(src) {
		return false
	}
	for i, v := range p.InstanceId {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *ModifyInspectionConfigReq) Field4DeepEqual(src []*InspectionConfig) bool {

	if len(p.InspectionConfig) != len(src) {
		return false
	}
	for i, v := range p.InspectionConfig {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *ModifyInspectionConfigReq) Field5DeepEqual(src OverWriteType) bool {

	if p.OverWriteType != src {
		return false
	}
	return true
}

type ModifyInspectionConfigResp struct {
}

func NewModifyInspectionConfigResp() *ModifyInspectionConfigResp {
	return &ModifyInspectionConfigResp{}
}

func (p *ModifyInspectionConfigResp) InitDefault() {
}

var fieldIDToName_ModifyInspectionConfigResp = map[int16]string{}

func (p *ModifyInspectionConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInspectionConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyInspectionConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyInspectionConfigResp")

	if err = oprot.WriteStructBegin("ModifyInspectionConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyInspectionConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyInspectionConfigResp(%+v)", *p)

}

func (p *ModifyInspectionConfigResp) DeepEqual(ano *ModifyInspectionConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type QueryInspectionConfigReq struct {
	RegionId     string       `thrift:"RegionId,1,required" frugal:"1,required,string" json:"RegionId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	InstanceId   string       `thrift:"InstanceId,3,required" frugal:"3,required,string" json:"InstanceId"`
}

func NewQueryInspectionConfigReq() *QueryInspectionConfigReq {
	return &QueryInspectionConfigReq{}
}

func (p *QueryInspectionConfigReq) InitDefault() {
}

func (p *QueryInspectionConfigReq) GetRegionId() (v string) {
	return p.RegionId
}

func (p *QueryInspectionConfigReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *QueryInspectionConfigReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *QueryInspectionConfigReq) SetRegionId(val string) {
	p.RegionId = val
}
func (p *QueryInspectionConfigReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *QueryInspectionConfigReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_QueryInspectionConfigReq = map[int16]string{
	1: "RegionId",
	2: "InstanceType",
	3: "InstanceId",
}

func (p *QueryInspectionConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("QueryInspectionConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRegionId bool = false
	var issetInstanceType bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRegionId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRegionId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryInspectionConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_QueryInspectionConfigReq[fieldId]))
}

func (p *QueryInspectionConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RegionId = _field
	return nil
}
func (p *QueryInspectionConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *QueryInspectionConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *QueryInspectionConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("QueryInspectionConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryInspectionConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *QueryInspectionConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.RegionId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *QueryInspectionConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *QueryInspectionConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *QueryInspectionConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryInspectionConfigReq(%+v)", *p)

}

func (p *QueryInspectionConfigReq) DeepEqual(ano *QueryInspectionConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *QueryInspectionConfigReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.RegionId, src) != 0 {
		return false
	}
	return true
}
func (p *QueryInspectionConfigReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *QueryInspectionConfigReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type QueryInspectionConfigResp struct {
	InspectionConfigs []*InspectionConfig `thrift:"InspectionConfigs,1,optional" frugal:"1,optional,list<InspectionConfig>" json:"InspectionConfigs,omitempty"`
}

func NewQueryInspectionConfigResp() *QueryInspectionConfigResp {
	return &QueryInspectionConfigResp{}
}

func (p *QueryInspectionConfigResp) InitDefault() {
}

var QueryInspectionConfigResp_InspectionConfigs_DEFAULT []*InspectionConfig

func (p *QueryInspectionConfigResp) GetInspectionConfigs() (v []*InspectionConfig) {
	if !p.IsSetInspectionConfigs() {
		return QueryInspectionConfigResp_InspectionConfigs_DEFAULT
	}
	return p.InspectionConfigs
}
func (p *QueryInspectionConfigResp) SetInspectionConfigs(val []*InspectionConfig) {
	p.InspectionConfigs = val
}

var fieldIDToName_QueryInspectionConfigResp = map[int16]string{
	1: "InspectionConfigs",
}

func (p *QueryInspectionConfigResp) IsSetInspectionConfigs() bool {
	return p.InspectionConfigs != nil
}

func (p *QueryInspectionConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("QueryInspectionConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_QueryInspectionConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *QueryInspectionConfigResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*InspectionConfig, 0, size)
	values := make([]InspectionConfig, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.InspectionConfigs = _field
	return nil
}

func (p *QueryInspectionConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("QueryInspectionConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("QueryInspectionConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *QueryInspectionConfigResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInspectionConfigs() {
		if err = oprot.WriteFieldBegin("InspectionConfigs", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InspectionConfigs)); err != nil {
			return err
		}
		for _, v := range p.InspectionConfigs {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *QueryInspectionConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryInspectionConfigResp(%+v)", *p)

}

func (p *QueryInspectionConfigResp) DeepEqual(ano *QueryInspectionConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InspectionConfigs) {
		return false
	}
	return true
}

func (p *QueryInspectionConfigResp) Field1DeepEqual(src []*InspectionConfig) bool {

	if len(p.InspectionConfigs) != len(src) {
		return false
	}
	for i, v := range p.InspectionConfigs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
