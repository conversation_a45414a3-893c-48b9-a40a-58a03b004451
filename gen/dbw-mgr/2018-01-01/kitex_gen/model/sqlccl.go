// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SqlType int64

const (
	SqlType_SELECT  SqlType = 0
	SqlType_UPDATE  SqlType = 1
	SqlType_DELETE  SqlType = 2
	SqlType_INSERT  SqlType = 3
	SqlType_REPLACE SqlType = 4
)

func (p SqlType) String() string {
	switch p {
	case SqlType_SELECT:
		return "SELECT"
	case SqlType_UPDATE:
		return "UPDATE"
	case SqlType_DELETE:
		return "DELETE"
	case SqlType_INSERT:
		return "INSERT"
	case SqlType_REPLACE:
		return "REPLACE"
	}
	return "<UNSET>"
}

func SqlTypeFromString(s string) (SqlType, error) {
	switch s {
	case "SELECT":
		return SqlType_SELECT, nil
	case "UPDATE":
		return SqlType_UPDATE, nil
	case "DELETE":
		return SqlType_DELETE, nil
	case "INSERT":
		return SqlType_INSERT, nil
	case "REPLACE":
		return SqlType_REPLACE, nil
	}
	return SqlType(0), fmt.Errorf("not a valid SqlType string")
}

func SqlTypePtr(v SqlType) *SqlType { return &v }

func (p SqlType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SqlType) UnmarshalText(text []byte) error {
	q, err := SqlTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type RuleState int64

const (
	RuleState_ACTIVE        RuleState = 0
	RuleState_STOPPED       RuleState = 1
	RuleState_NONE          RuleState = 2
	RuleState_DELETED       RuleState = 3
	RuleState_DONE          RuleState = 4
	RuleState_STOP_FAILED   RuleState = 5
	RuleState_DELETE_FAILED RuleState = 6
	RuleState_ADD_FAILED    RuleState = 7
)

func (p RuleState) String() string {
	switch p {
	case RuleState_ACTIVE:
		return "ACTIVE"
	case RuleState_STOPPED:
		return "STOPPED"
	case RuleState_NONE:
		return "NONE"
	case RuleState_DELETED:
		return "DELETED"
	case RuleState_DONE:
		return "DONE"
	case RuleState_STOP_FAILED:
		return "STOP_FAILED"
	case RuleState_DELETE_FAILED:
		return "DELETE_FAILED"
	case RuleState_ADD_FAILED:
		return "ADD_FAILED"
	}
	return "<UNSET>"
}

func RuleStateFromString(s string) (RuleState, error) {
	switch s {
	case "ACTIVE":
		return RuleState_ACTIVE, nil
	case "STOPPED":
		return RuleState_STOPPED, nil
	case "NONE":
		return RuleState_NONE, nil
	case "DELETED":
		return RuleState_DELETED, nil
	case "DONE":
		return RuleState_DONE, nil
	case "STOP_FAILED":
		return RuleState_STOP_FAILED, nil
	case "DELETE_FAILED":
		return RuleState_DELETE_FAILED, nil
	case "ADD_FAILED":
		return RuleState_ADD_FAILED, nil
	}
	return RuleState(0), fmt.Errorf("not a valid RuleState string")
}

func RuleStatePtr(v RuleState) *RuleState { return &v }

func (p RuleState) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *RuleState) UnmarshalText(text []byte) error {
	q, err := RuleStateFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type CCLEventType int64

const (
	CCLEventType_Add    CCLEventType = 0
	CCLEventType_Delete CCLEventType = 1
	CCLEventType_Stop   CCLEventType = 2
	CCLEventType_Modify CCLEventType = 3
)

func (p CCLEventType) String() string {
	switch p {
	case CCLEventType_Add:
		return "Add"
	case CCLEventType_Delete:
		return "Delete"
	case CCLEventType_Stop:
		return "Stop"
	case CCLEventType_Modify:
		return "Modify"
	}
	return "<UNSET>"
}

func CCLEventTypeFromString(s string) (CCLEventType, error) {
	switch s {
	case "Add":
		return CCLEventType_Add, nil
	case "Delete":
		return CCLEventType_Delete, nil
	case "Stop":
		return CCLEventType_Stop, nil
	case "Modify":
		return CCLEventType_Modify, nil
	}
	return CCLEventType(0), fmt.Errorf("not a valid CCLEventType string")
}

func CCLEventTypePtr(v CCLEventType) *CCLEventType { return &v }

func (p CCLEventType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *CCLEventType) UnmarshalText(text []byte) error {
	q, err := CCLEventTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type OrderByForDBRule int64

const (
	OrderByForDBRule_CreateTime OrderByForDBRule = 0
	OrderByForDBRule_EndTime    OrderByForDBRule = 1
)

func (p OrderByForDBRule) String() string {
	switch p {
	case OrderByForDBRule_CreateTime:
		return "CreateTime"
	case OrderByForDBRule_EndTime:
		return "EndTime"
	}
	return "<UNSET>"
}

func OrderByForDBRuleFromString(s string) (OrderByForDBRule, error) {
	switch s {
	case "CreateTime":
		return OrderByForDBRule_CreateTime, nil
	case "EndTime":
		return OrderByForDBRule_EndTime, nil
	}
	return OrderByForDBRule(0), fmt.Errorf("not a valid OrderByForDBRule string")
}

func OrderByForDBRulePtr(v OrderByForDBRule) *OrderByForDBRule { return &v }

func (p OrderByForDBRule) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *OrderByForDBRule) UnmarshalText(text []byte) error {
	q, err := OrderByForDBRuleFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type EndpointType int64

const (
	EndpointType_ReadOnly  EndpointType = 0
	EndpointType_ReadWrite EndpointType = 1
	EndpointType_Offline   EndpointType = 2
	EndpointType_Htap      EndpointType = 3
)

func (p EndpointType) String() string {
	switch p {
	case EndpointType_ReadOnly:
		return "ReadOnly"
	case EndpointType_ReadWrite:
		return "ReadWrite"
	case EndpointType_Offline:
		return "Offline"
	case EndpointType_Htap:
		return "Htap"
	}
	return "<UNSET>"
}

func EndpointTypeFromString(s string) (EndpointType, error) {
	switch s {
	case "ReadOnly":
		return EndpointType_ReadOnly, nil
	case "ReadWrite":
		return EndpointType_ReadWrite, nil
	case "Offline":
		return EndpointType_Offline, nil
	case "Htap":
		return EndpointType_Htap, nil
	}
	return EndpointType(0), fmt.Errorf("not a valid EndpointType string")
}

func EndpointTypePtr(v EndpointType) *EndpointType { return &v }

func (p EndpointType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *EndpointType) UnmarshalText(text []byte) error {
	q, err := EndpointTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ThrottleType int64

const (
	ThrottleType_ConcurrencyCount ThrottleType = 0
	ThrottleType_QPSThrottle      ThrottleType = 1
	ThrottleType_ConnThrottle     ThrottleType = 2
)

func (p ThrottleType) String() string {
	switch p {
	case ThrottleType_ConcurrencyCount:
		return "ConcurrencyCount"
	case ThrottleType_QPSThrottle:
		return "QPSThrottle"
	case ThrottleType_ConnThrottle:
		return "ConnThrottle"
	}
	return "<UNSET>"
}

func ThrottleTypeFromString(s string) (ThrottleType, error) {
	switch s {
	case "ConcurrencyCount":
		return ThrottleType_ConcurrencyCount, nil
	case "QPSThrottle":
		return ThrottleType_QPSThrottle, nil
	case "ConnThrottle":
		return ThrottleType_ConnThrottle, nil
	}
	return ThrottleType(0), fmt.Errorf("not a valid ThrottleType string")
}

func ThrottleTypePtr(v ThrottleType) *ThrottleType { return &v }

func (p ThrottleType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ThrottleType) UnmarshalText(text []byte) error {
	q, err := ThrottleTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ThrottleTarget int64

const (
	ThrottleTarget_MaxConcurrencyCount  ThrottleTarget = 0
	ThrottleTarget_FingerQPS            ThrottleTarget = 1
	ThrottleTarget_FrontQPS             ThrottleTarget = 2
	ThrottleTarget_KeywordQPS           ThrottleTarget = 3
	ThrottleTarget_MaxFrontConn         ThrottleTarget = 4
	ThrottleTarget_SqlQPS               ThrottleTarget = 5
	ThrottleTarget_PsmDbQPS             ThrottleTarget = 6
	ThrottleTarget_MaxEndConn           ThrottleTarget = 7
	ThrottleTarget_FingerProhibitionQPS ThrottleTarget = 8
	ThrottleTarget_GroupQPS             ThrottleTarget = 9
	ThrottleTarget_SqlCCL               ThrottleTarget = 10
	ThrottleTarget_FingerCCL            ThrottleTarget = 11
)

func (p ThrottleTarget) String() string {
	switch p {
	case ThrottleTarget_MaxConcurrencyCount:
		return "MaxConcurrencyCount"
	case ThrottleTarget_FingerQPS:
		return "FingerQPS"
	case ThrottleTarget_FrontQPS:
		return "FrontQPS"
	case ThrottleTarget_KeywordQPS:
		return "KeywordQPS"
	case ThrottleTarget_MaxFrontConn:
		return "MaxFrontConn"
	case ThrottleTarget_SqlQPS:
		return "SqlQPS"
	case ThrottleTarget_PsmDbQPS:
		return "PsmDbQPS"
	case ThrottleTarget_MaxEndConn:
		return "MaxEndConn"
	case ThrottleTarget_FingerProhibitionQPS:
		return "FingerProhibitionQPS"
	case ThrottleTarget_GroupQPS:
		return "GroupQPS"
	case ThrottleTarget_SqlCCL:
		return "SqlCCL"
	case ThrottleTarget_FingerCCL:
		return "FingerCCL"
	}
	return "<UNSET>"
}

func ThrottleTargetFromString(s string) (ThrottleTarget, error) {
	switch s {
	case "MaxConcurrencyCount":
		return ThrottleTarget_MaxConcurrencyCount, nil
	case "FingerQPS":
		return ThrottleTarget_FingerQPS, nil
	case "FrontQPS":
		return ThrottleTarget_FrontQPS, nil
	case "KeywordQPS":
		return ThrottleTarget_KeywordQPS, nil
	case "MaxFrontConn":
		return ThrottleTarget_MaxFrontConn, nil
	case "SqlQPS":
		return ThrottleTarget_SqlQPS, nil
	case "PsmDbQPS":
		return ThrottleTarget_PsmDbQPS, nil
	case "MaxEndConn":
		return ThrottleTarget_MaxEndConn, nil
	case "FingerProhibitionQPS":
		return ThrottleTarget_FingerProhibitionQPS, nil
	case "GroupQPS":
		return ThrottleTarget_GroupQPS, nil
	case "SqlCCL":
		return ThrottleTarget_SqlCCL, nil
	case "FingerCCL":
		return ThrottleTarget_FingerCCL, nil
	}
	return ThrottleTarget(0), fmt.Errorf("not a valid ThrottleTarget string")
}

func ThrottleTargetPtr(v ThrottleTarget) *ThrottleTarget { return &v }

func (p ThrottleTarget) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ThrottleTarget) UnmarshalText(text []byte) error {
	q, err := ThrottleTargetFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type TerminationType int64

const (
	TerminationType_Auto   TerminationType = 0
	TerminationType_Manual TerminationType = 1
)

func (p TerminationType) String() string {
	switch p {
	case TerminationType_Auto:
		return "Auto"
	case TerminationType_Manual:
		return "Manual"
	}
	return "<UNSET>"
}

func TerminationTypeFromString(s string) (TerminationType, error) {
	switch s {
	case "Auto":
		return TerminationType_Auto, nil
	case "Manual":
		return TerminationType_Manual, nil
	}
	return TerminationType(0), fmt.Errorf("not a valid TerminationType string")
}

func TerminationTypePtr(v TerminationType) *TerminationType { return &v }

func (p TerminationType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TerminationType) UnmarshalText(text []byte) error {
	q, err := TerminationTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ThrottleMode int64

const (
	ThrottleMode_DBThrottle    ThrottleMode = 0
	ThrottleMode_ProxyThrottle ThrottleMode = 1
)

func (p ThrottleMode) String() string {
	switch p {
	case ThrottleMode_DBThrottle:
		return "DBThrottle"
	case ThrottleMode_ProxyThrottle:
		return "ProxyThrottle"
	}
	return "<UNSET>"
}

func ThrottleModeFromString(s string) (ThrottleMode, error) {
	switch s {
	case "DBThrottle":
		return ThrottleMode_DBThrottle, nil
	case "ProxyThrottle":
		return ThrottleMode_ProxyThrottle, nil
	}
	return ThrottleMode(0), fmt.Errorf("not a valid ThrottleMode string")
}

func ThrottleModePtr(v ThrottleMode) *ThrottleMode { return &v }

func (p ThrottleMode) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ThrottleMode) UnmarshalText(text []byte) error {
	q, err := ThrottleModeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SqlSearchParam struct {
	SqlType        []SqlType       `thrift:"SqlType,1,optional" frugal:"1,optional,list<SqlType>" json:"SqlType,omitempty"`
	RuleState      []RuleState     `thrift:"RuleState,2,optional" frugal:"2,optional,list<RuleState>" json:"RuleState,omitempty"`
	EndpointType   *EndpointType   `thrift:"EndpointType,3,optional" frugal:"3,optional,EndpointType" json:"EndpointType,omitempty"`
	ThrottleType   *ThrottleType   `thrift:"ThrottleType,4,optional" frugal:"4,optional,ThrottleType" json:"ThrottleType,omitempty"`
	ThrottleTarget *ThrottleTarget `thrift:"ThrottleTarget,5,optional" frugal:"5,optional,ThrottleTarget" json:"ThrottleTarget,omitempty"`
	TaskId         *string         `thrift:"TaskId,6,optional" frugal:"6,optional,string" json:"TaskId,omitempty"`
}

func NewSqlSearchParam() *SqlSearchParam {
	return &SqlSearchParam{}
}

func (p *SqlSearchParam) InitDefault() {
}

var SqlSearchParam_SqlType_DEFAULT []SqlType

func (p *SqlSearchParam) GetSqlType() (v []SqlType) {
	if !p.IsSetSqlType() {
		return SqlSearchParam_SqlType_DEFAULT
	}
	return p.SqlType
}

var SqlSearchParam_RuleState_DEFAULT []RuleState

func (p *SqlSearchParam) GetRuleState() (v []RuleState) {
	if !p.IsSetRuleState() {
		return SqlSearchParam_RuleState_DEFAULT
	}
	return p.RuleState
}

var SqlSearchParam_EndpointType_DEFAULT EndpointType

func (p *SqlSearchParam) GetEndpointType() (v EndpointType) {
	if !p.IsSetEndpointType() {
		return SqlSearchParam_EndpointType_DEFAULT
	}
	return *p.EndpointType
}

var SqlSearchParam_ThrottleType_DEFAULT ThrottleType

func (p *SqlSearchParam) GetThrottleType() (v ThrottleType) {
	if !p.IsSetThrottleType() {
		return SqlSearchParam_ThrottleType_DEFAULT
	}
	return *p.ThrottleType
}

var SqlSearchParam_ThrottleTarget_DEFAULT ThrottleTarget

func (p *SqlSearchParam) GetThrottleTarget() (v ThrottleTarget) {
	if !p.IsSetThrottleTarget() {
		return SqlSearchParam_ThrottleTarget_DEFAULT
	}
	return *p.ThrottleTarget
}

var SqlSearchParam_TaskId_DEFAULT string

func (p *SqlSearchParam) GetTaskId() (v string) {
	if !p.IsSetTaskId() {
		return SqlSearchParam_TaskId_DEFAULT
	}
	return *p.TaskId
}
func (p *SqlSearchParam) SetSqlType(val []SqlType) {
	p.SqlType = val
}
func (p *SqlSearchParam) SetRuleState(val []RuleState) {
	p.RuleState = val
}
func (p *SqlSearchParam) SetEndpointType(val *EndpointType) {
	p.EndpointType = val
}
func (p *SqlSearchParam) SetThrottleType(val *ThrottleType) {
	p.ThrottleType = val
}
func (p *SqlSearchParam) SetThrottleTarget(val *ThrottleTarget) {
	p.ThrottleTarget = val
}
func (p *SqlSearchParam) SetTaskId(val *string) {
	p.TaskId = val
}

var fieldIDToName_SqlSearchParam = map[int16]string{
	1: "SqlType",
	2: "RuleState",
	3: "EndpointType",
	4: "ThrottleType",
	5: "ThrottleTarget",
	6: "TaskId",
}

func (p *SqlSearchParam) IsSetSqlType() bool {
	return p.SqlType != nil
}

func (p *SqlSearchParam) IsSetRuleState() bool {
	return p.RuleState != nil
}

func (p *SqlSearchParam) IsSetEndpointType() bool {
	return p.EndpointType != nil
}

func (p *SqlSearchParam) IsSetThrottleType() bool {
	return p.ThrottleType != nil
}

func (p *SqlSearchParam) IsSetThrottleTarget() bool {
	return p.ThrottleTarget != nil
}

func (p *SqlSearchParam) IsSetTaskId() bool {
	return p.TaskId != nil
}

func (p *SqlSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SqlSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SqlSearchParam) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]SqlType, 0, size)
	for i := 0; i < size; i++ {

		var _elem SqlType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = SqlType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SqlType = _field
	return nil
}
func (p *SqlSearchParam) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]RuleState, 0, size)
	for i := 0; i < size; i++ {

		var _elem RuleState
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = RuleState(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RuleState = _field
	return nil
}
func (p *SqlSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *EndpointType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EndpointType(v)
		_field = &tmp
	}
	p.EndpointType = _field
	return nil
}
func (p *SqlSearchParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *ThrottleType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ThrottleType(v)
		_field = &tmp
	}
	p.ThrottleType = _field
	return nil
}
func (p *SqlSearchParam) ReadField5(iprot thrift.TProtocol) error {

	var _field *ThrottleTarget
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ThrottleTarget(v)
		_field = &tmp
	}
	p.ThrottleTarget = _field
	return nil
}
func (p *SqlSearchParam) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TaskId = _field
	return nil
}

func (p *SqlSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SqlSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("SqlSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SqlSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlType() {
		if err = oprot.WriteFieldBegin("SqlType", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SqlType)); err != nil {
			return err
		}
		for _, v := range p.SqlType {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SqlSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetRuleState() {
		if err = oprot.WriteFieldBegin("RuleState", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.RuleState)); err != nil {
			return err
		}
		for _, v := range p.RuleState {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SqlSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndpointType() {
		if err = oprot.WriteFieldBegin("EndpointType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.EndpointType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SqlSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleType() {
		if err = oprot.WriteFieldBegin("ThrottleType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ThrottleType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SqlSearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleTarget() {
		if err = oprot.WriteFieldBegin("ThrottleTarget", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ThrottleTarget)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SqlSearchParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskId() {
		if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TaskId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SqlSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SqlSearchParam(%+v)", *p)

}

func (p *SqlSearchParam) DeepEqual(ano *SqlSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlType) {
		return false
	}
	if !p.Field2DeepEqual(ano.RuleState) {
		return false
	}
	if !p.Field3DeepEqual(ano.EndpointType) {
		return false
	}
	if !p.Field4DeepEqual(ano.ThrottleType) {
		return false
	}
	if !p.Field5DeepEqual(ano.ThrottleTarget) {
		return false
	}
	if !p.Field6DeepEqual(ano.TaskId) {
		return false
	}
	return true
}

func (p *SqlSearchParam) Field1DeepEqual(src []SqlType) bool {

	if len(p.SqlType) != len(src) {
		return false
	}
	for i, v := range p.SqlType {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *SqlSearchParam) Field2DeepEqual(src []RuleState) bool {

	if len(p.RuleState) != len(src) {
		return false
	}
	for i, v := range p.RuleState {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *SqlSearchParam) Field3DeepEqual(src *EndpointType) bool {

	if p.EndpointType == src {
		return true
	} else if p.EndpointType == nil || src == nil {
		return false
	}
	if *p.EndpointType != *src {
		return false
	}
	return true
}
func (p *SqlSearchParam) Field4DeepEqual(src *ThrottleType) bool {

	if p.ThrottleType == src {
		return true
	} else if p.ThrottleType == nil || src == nil {
		return false
	}
	if *p.ThrottleType != *src {
		return false
	}
	return true
}
func (p *SqlSearchParam) Field5DeepEqual(src *ThrottleTarget) bool {

	if p.ThrottleTarget == src {
		return true
	} else if p.ThrottleTarget == nil || src == nil {
		return false
	}
	if *p.ThrottleTarget != *src {
		return false
	}
	return true
}
func (p *SqlSearchParam) Field6DeepEqual(src *string) bool {

	if p.TaskId == src {
		return true
	} else if p.TaskId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TaskId, *src) != 0 {
		return false
	}
	return true
}

type GetSqlConcurrencyControlStatusReq struct {
	InstanceId   *string `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	InstanceType *DSType `thrift:"InstanceType,2,optional" frugal:"2,optional,DSType" json:"InstanceType,omitempty"`
}

func NewGetSqlConcurrencyControlStatusReq() *GetSqlConcurrencyControlStatusReq {
	return &GetSqlConcurrencyControlStatusReq{}
}

func (p *GetSqlConcurrencyControlStatusReq) InitDefault() {
}

var GetSqlConcurrencyControlStatusReq_InstanceId_DEFAULT string

func (p *GetSqlConcurrencyControlStatusReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return GetSqlConcurrencyControlStatusReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var GetSqlConcurrencyControlStatusReq_InstanceType_DEFAULT DSType

func (p *GetSqlConcurrencyControlStatusReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return GetSqlConcurrencyControlStatusReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}
func (p *GetSqlConcurrencyControlStatusReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *GetSqlConcurrencyControlStatusReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}

var fieldIDToName_GetSqlConcurrencyControlStatusReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
}

func (p *GetSqlConcurrencyControlStatusReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *GetSqlConcurrencyControlStatusReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *GetSqlConcurrencyControlStatusReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlConcurrencyControlStatusReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSqlConcurrencyControlStatusReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetSqlConcurrencyControlStatusReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *GetSqlConcurrencyControlStatusReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}

func (p *GetSqlConcurrencyControlStatusReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlConcurrencyControlStatusReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetSqlConcurrencyControlStatusReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSqlConcurrencyControlStatusReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetSqlConcurrencyControlStatusReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *GetSqlConcurrencyControlStatusReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSqlConcurrencyControlStatusReq(%+v)", *p)

}

func (p *GetSqlConcurrencyControlStatusReq) DeepEqual(ano *GetSqlConcurrencyControlStatusReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *GetSqlConcurrencyControlStatusReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *GetSqlConcurrencyControlStatusReq) Field2DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}

type GetSqlConcurrencyControlStatusResp struct {
	Status bool `thrift:"Status,1,required" frugal:"1,required,bool" json:"Status"`
}

func NewGetSqlConcurrencyControlStatusResp() *GetSqlConcurrencyControlStatusResp {
	return &GetSqlConcurrencyControlStatusResp{}
}

func (p *GetSqlConcurrencyControlStatusResp) InitDefault() {
}

func (p *GetSqlConcurrencyControlStatusResp) GetStatus() (v bool) {
	return p.Status
}
func (p *GetSqlConcurrencyControlStatusResp) SetStatus(val bool) {
	p.Status = val
}

var fieldIDToName_GetSqlConcurrencyControlStatusResp = map[int16]string{
	1: "Status",
}

func (p *GetSqlConcurrencyControlStatusResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlConcurrencyControlStatusResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStatus bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStatus {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSqlConcurrencyControlStatusResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetSqlConcurrencyControlStatusResp[fieldId]))
}

func (p *GetSqlConcurrencyControlStatusResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Status = _field
	return nil
}

func (p *GetSqlConcurrencyControlStatusResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetSqlConcurrencyControlStatusResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetSqlConcurrencyControlStatusResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSqlConcurrencyControlStatusResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.Status); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetSqlConcurrencyControlStatusResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSqlConcurrencyControlStatusResp(%+v)", *p)

}

func (p *GetSqlConcurrencyControlStatusResp) DeepEqual(ano *GetSqlConcurrencyControlStatusResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Status) {
		return false
	}
	return true
}

func (p *GetSqlConcurrencyControlStatusResp) Field1DeepEqual(src bool) bool {

	if p.Status != src {
		return false
	}
	return true
}

type EnableSqlConcurrencyControlReq struct {
	InstanceId   *string `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	InstanceType *DSType `thrift:"InstanceType,2,optional" frugal:"2,optional,DSType" json:"InstanceType,omitempty"`
}

func NewEnableSqlConcurrencyControlReq() *EnableSqlConcurrencyControlReq {
	return &EnableSqlConcurrencyControlReq{}
}

func (p *EnableSqlConcurrencyControlReq) InitDefault() {
}

var EnableSqlConcurrencyControlReq_InstanceId_DEFAULT string

func (p *EnableSqlConcurrencyControlReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return EnableSqlConcurrencyControlReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var EnableSqlConcurrencyControlReq_InstanceType_DEFAULT DSType

func (p *EnableSqlConcurrencyControlReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return EnableSqlConcurrencyControlReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}
func (p *EnableSqlConcurrencyControlReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *EnableSqlConcurrencyControlReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}

var fieldIDToName_EnableSqlConcurrencyControlReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
}

func (p *EnableSqlConcurrencyControlReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *EnableSqlConcurrencyControlReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *EnableSqlConcurrencyControlReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EnableSqlConcurrencyControlReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_EnableSqlConcurrencyControlReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *EnableSqlConcurrencyControlReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *EnableSqlConcurrencyControlReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}

func (p *EnableSqlConcurrencyControlReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EnableSqlConcurrencyControlReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("EnableSqlConcurrencyControlReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *EnableSqlConcurrencyControlReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *EnableSqlConcurrencyControlReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *EnableSqlConcurrencyControlReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EnableSqlConcurrencyControlReq(%+v)", *p)

}

func (p *EnableSqlConcurrencyControlReq) DeepEqual(ano *EnableSqlConcurrencyControlReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *EnableSqlConcurrencyControlReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *EnableSqlConcurrencyControlReq) Field2DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}

type EnableSqlConcurrencyControlResp struct {
}

func NewEnableSqlConcurrencyControlResp() *EnableSqlConcurrencyControlResp {
	return &EnableSqlConcurrencyControlResp{}
}

func (p *EnableSqlConcurrencyControlResp) InitDefault() {
}

var fieldIDToName_EnableSqlConcurrencyControlResp = map[int16]string{}

func (p *EnableSqlConcurrencyControlResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EnableSqlConcurrencyControlResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *EnableSqlConcurrencyControlResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("EnableSqlConcurrencyControlResp")

	if err = oprot.WriteStructBegin("EnableSqlConcurrencyControlResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *EnableSqlConcurrencyControlResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EnableSqlConcurrencyControlResp(%+v)", *p)

}

func (p *EnableSqlConcurrencyControlResp) DeepEqual(ano *EnableSqlConcurrencyControlResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DisableSqlConcurrencyControlReq struct {
	InstanceId   *string `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	InstanceType *DSType `thrift:"InstanceType,2,optional" frugal:"2,optional,DSType" json:"InstanceType,omitempty"`
}

func NewDisableSqlConcurrencyControlReq() *DisableSqlConcurrencyControlReq {
	return &DisableSqlConcurrencyControlReq{}
}

func (p *DisableSqlConcurrencyControlReq) InitDefault() {
}

var DisableSqlConcurrencyControlReq_InstanceId_DEFAULT string

func (p *DisableSqlConcurrencyControlReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DisableSqlConcurrencyControlReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DisableSqlConcurrencyControlReq_InstanceType_DEFAULT DSType

func (p *DisableSqlConcurrencyControlReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DisableSqlConcurrencyControlReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}
func (p *DisableSqlConcurrencyControlReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DisableSqlConcurrencyControlReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}

var fieldIDToName_DisableSqlConcurrencyControlReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
}

func (p *DisableSqlConcurrencyControlReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DisableSqlConcurrencyControlReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DisableSqlConcurrencyControlReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisableSqlConcurrencyControlReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DisableSqlConcurrencyControlReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DisableSqlConcurrencyControlReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DisableSqlConcurrencyControlReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}

func (p *DisableSqlConcurrencyControlReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisableSqlConcurrencyControlReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DisableSqlConcurrencyControlReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DisableSqlConcurrencyControlReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DisableSqlConcurrencyControlReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DisableSqlConcurrencyControlReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DisableSqlConcurrencyControlReq(%+v)", *p)

}

func (p *DisableSqlConcurrencyControlReq) DeepEqual(ano *DisableSqlConcurrencyControlReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DisableSqlConcurrencyControlReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DisableSqlConcurrencyControlReq) Field2DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}

type DisableSqlConcurrencyControlResp struct {
}

func NewDisableSqlConcurrencyControlResp() *DisableSqlConcurrencyControlResp {
	return &DisableSqlConcurrencyControlResp{}
}

func (p *DisableSqlConcurrencyControlResp) InitDefault() {
}

var fieldIDToName_DisableSqlConcurrencyControlResp = map[int16]string{}

func (p *DisableSqlConcurrencyControlResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisableSqlConcurrencyControlResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DisableSqlConcurrencyControlResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DisableSqlConcurrencyControlResp")

	if err = oprot.WriteStructBegin("DisableSqlConcurrencyControlResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DisableSqlConcurrencyControlResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DisableSqlConcurrencyControlResp(%+v)", *p)

}

func (p *DisableSqlConcurrencyControlResp) DeepEqual(ano *DisableSqlConcurrencyControlResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type CreateSqlConcurrencyControlRuleReq struct {
	InstanceId          *string          `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	SqlType             *SqlType         `thrift:"SqlType,2,optional" frugal:"2,optional,SqlType" json:"SqlType,omitempty"`
	ConcurrencyCount    int32            `thrift:"ConcurrencyCount,3,required" frugal:"3,required,i32" json:"ConcurrencyCount"`
	EffectiveTime       int32            `thrift:"EffectiveTime,4,required" frugal:"4,required,i32" json:"EffectiveTime"`
	KeyWords            *string          `thrift:"KeyWords,5,optional" frugal:"5,optional,string" json:"KeyWords,omitempty"`
	InstanceType        *DSType          `thrift:"InstanceType,6,optional" frugal:"6,optional,DSType" json:"InstanceType,omitempty"`
	ThrottleThreshold   *int32           `thrift:"ThrottleThreshold,7,optional" frugal:"7,optional,i32" json:"ThrottleThreshold,omitempty"`
	LinkType            *LinkType        `thrift:"LinkType,8,optional" frugal:"8,optional,LinkType" json:"LinkType,omitempty"`
	ThrottleType        *ThrottleType    `thrift:"ThrottleType,9,optional" frugal:"9,optional,ThrottleType" json:"ThrottleType,omitempty"`
	EndpointId          *string          `thrift:"EndpointId,10,optional" frugal:"10,optional,string" json:"EndpointId,omitempty"`
	EndpointType        *EndpointType    `thrift:"EndpointType,11,optional" frugal:"11,optional,EndpointType" json:"EndpointType,omitempty"`
	ThrottleHost        *string          `thrift:"ThrottleHost,12,optional" frugal:"12,optional,string" json:"ThrottleHost,omitempty"`
	ThrottleDB          *string          `thrift:"ThrottleDB,13,optional" frugal:"13,optional,string" json:"ThrottleDB,omitempty"`
	ThrottleSqlText     *string          `thrift:"ThrottleSqlText,14,optional" frugal:"14,optional,string" json:"ThrottleSqlText,omitempty"`
	ThrottleFingerPrint *string          `thrift:"ThrottleFingerPrint,15,optional" frugal:"15,optional,string" json:"ThrottleFingerPrint,omitempty"`
	TerminationType     *TerminationType `thrift:"TerminationType,16,optional" frugal:"16,optional,TerminationType" json:"TerminationType,omitempty"`
	ThrottleMode        *ThrottleMode    `thrift:"ThrottleMode,17,optional" frugal:"17,optional,ThrottleMode" json:"ThrottleMode,omitempty"`
	ThrottleTarget      *ThrottleTarget  `thrift:"ThrottleTarget,18,optional" frugal:"18,optional,ThrottleTarget" json:"ThrottleTarget,omitempty"`
	GroupIds            []int64          `thrift:"GroupIds,19,optional" frugal:"19,optional,list<i64>" json:"GroupIds,omitempty"`
	RegionId            *string          `thrift:"RegionId,20,optional" frugal:"20,optional,string" json:"RegionId,omitempty"`
	Desc                *string          `thrift:"Desc,21,optional" frugal:"21,optional,string" json:"Desc,omitempty"`
}

func NewCreateSqlConcurrencyControlRuleReq() *CreateSqlConcurrencyControlRuleReq {
	return &CreateSqlConcurrencyControlRuleReq{}
}

func (p *CreateSqlConcurrencyControlRuleReq) InitDefault() {
}

var CreateSqlConcurrencyControlRuleReq_InstanceId_DEFAULT string

func (p *CreateSqlConcurrencyControlRuleReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return CreateSqlConcurrencyControlRuleReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var CreateSqlConcurrencyControlRuleReq_SqlType_DEFAULT SqlType

func (p *CreateSqlConcurrencyControlRuleReq) GetSqlType() (v SqlType) {
	if !p.IsSetSqlType() {
		return CreateSqlConcurrencyControlRuleReq_SqlType_DEFAULT
	}
	return *p.SqlType
}

func (p *CreateSqlConcurrencyControlRuleReq) GetConcurrencyCount() (v int32) {
	return p.ConcurrencyCount
}

func (p *CreateSqlConcurrencyControlRuleReq) GetEffectiveTime() (v int32) {
	return p.EffectiveTime
}

var CreateSqlConcurrencyControlRuleReq_KeyWords_DEFAULT string

func (p *CreateSqlConcurrencyControlRuleReq) GetKeyWords() (v string) {
	if !p.IsSetKeyWords() {
		return CreateSqlConcurrencyControlRuleReq_KeyWords_DEFAULT
	}
	return *p.KeyWords
}

var CreateSqlConcurrencyControlRuleReq_InstanceType_DEFAULT DSType

func (p *CreateSqlConcurrencyControlRuleReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return CreateSqlConcurrencyControlRuleReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var CreateSqlConcurrencyControlRuleReq_ThrottleThreshold_DEFAULT int32

func (p *CreateSqlConcurrencyControlRuleReq) GetThrottleThreshold() (v int32) {
	if !p.IsSetThrottleThreshold() {
		return CreateSqlConcurrencyControlRuleReq_ThrottleThreshold_DEFAULT
	}
	return *p.ThrottleThreshold
}

var CreateSqlConcurrencyControlRuleReq_LinkType_DEFAULT LinkType

func (p *CreateSqlConcurrencyControlRuleReq) GetLinkType() (v LinkType) {
	if !p.IsSetLinkType() {
		return CreateSqlConcurrencyControlRuleReq_LinkType_DEFAULT
	}
	return *p.LinkType
}

var CreateSqlConcurrencyControlRuleReq_ThrottleType_DEFAULT ThrottleType

func (p *CreateSqlConcurrencyControlRuleReq) GetThrottleType() (v ThrottleType) {
	if !p.IsSetThrottleType() {
		return CreateSqlConcurrencyControlRuleReq_ThrottleType_DEFAULT
	}
	return *p.ThrottleType
}

var CreateSqlConcurrencyControlRuleReq_EndpointId_DEFAULT string

func (p *CreateSqlConcurrencyControlRuleReq) GetEndpointId() (v string) {
	if !p.IsSetEndpointId() {
		return CreateSqlConcurrencyControlRuleReq_EndpointId_DEFAULT
	}
	return *p.EndpointId
}

var CreateSqlConcurrencyControlRuleReq_EndpointType_DEFAULT EndpointType

func (p *CreateSqlConcurrencyControlRuleReq) GetEndpointType() (v EndpointType) {
	if !p.IsSetEndpointType() {
		return CreateSqlConcurrencyControlRuleReq_EndpointType_DEFAULT
	}
	return *p.EndpointType
}

var CreateSqlConcurrencyControlRuleReq_ThrottleHost_DEFAULT string

func (p *CreateSqlConcurrencyControlRuleReq) GetThrottleHost() (v string) {
	if !p.IsSetThrottleHost() {
		return CreateSqlConcurrencyControlRuleReq_ThrottleHost_DEFAULT
	}
	return *p.ThrottleHost
}

var CreateSqlConcurrencyControlRuleReq_ThrottleDB_DEFAULT string

func (p *CreateSqlConcurrencyControlRuleReq) GetThrottleDB() (v string) {
	if !p.IsSetThrottleDB() {
		return CreateSqlConcurrencyControlRuleReq_ThrottleDB_DEFAULT
	}
	return *p.ThrottleDB
}

var CreateSqlConcurrencyControlRuleReq_ThrottleSqlText_DEFAULT string

func (p *CreateSqlConcurrencyControlRuleReq) GetThrottleSqlText() (v string) {
	if !p.IsSetThrottleSqlText() {
		return CreateSqlConcurrencyControlRuleReq_ThrottleSqlText_DEFAULT
	}
	return *p.ThrottleSqlText
}

var CreateSqlConcurrencyControlRuleReq_ThrottleFingerPrint_DEFAULT string

func (p *CreateSqlConcurrencyControlRuleReq) GetThrottleFingerPrint() (v string) {
	if !p.IsSetThrottleFingerPrint() {
		return CreateSqlConcurrencyControlRuleReq_ThrottleFingerPrint_DEFAULT
	}
	return *p.ThrottleFingerPrint
}

var CreateSqlConcurrencyControlRuleReq_TerminationType_DEFAULT TerminationType

func (p *CreateSqlConcurrencyControlRuleReq) GetTerminationType() (v TerminationType) {
	if !p.IsSetTerminationType() {
		return CreateSqlConcurrencyControlRuleReq_TerminationType_DEFAULT
	}
	return *p.TerminationType
}

var CreateSqlConcurrencyControlRuleReq_ThrottleMode_DEFAULT ThrottleMode

func (p *CreateSqlConcurrencyControlRuleReq) GetThrottleMode() (v ThrottleMode) {
	if !p.IsSetThrottleMode() {
		return CreateSqlConcurrencyControlRuleReq_ThrottleMode_DEFAULT
	}
	return *p.ThrottleMode
}

var CreateSqlConcurrencyControlRuleReq_ThrottleTarget_DEFAULT ThrottleTarget

func (p *CreateSqlConcurrencyControlRuleReq) GetThrottleTarget() (v ThrottleTarget) {
	if !p.IsSetThrottleTarget() {
		return CreateSqlConcurrencyControlRuleReq_ThrottleTarget_DEFAULT
	}
	return *p.ThrottleTarget
}

var CreateSqlConcurrencyControlRuleReq_GroupIds_DEFAULT []int64

func (p *CreateSqlConcurrencyControlRuleReq) GetGroupIds() (v []int64) {
	if !p.IsSetGroupIds() {
		return CreateSqlConcurrencyControlRuleReq_GroupIds_DEFAULT
	}
	return p.GroupIds
}

var CreateSqlConcurrencyControlRuleReq_RegionId_DEFAULT string

func (p *CreateSqlConcurrencyControlRuleReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return CreateSqlConcurrencyControlRuleReq_RegionId_DEFAULT
	}
	return *p.RegionId
}

var CreateSqlConcurrencyControlRuleReq_Desc_DEFAULT string

func (p *CreateSqlConcurrencyControlRuleReq) GetDesc() (v string) {
	if !p.IsSetDesc() {
		return CreateSqlConcurrencyControlRuleReq_Desc_DEFAULT
	}
	return *p.Desc
}
func (p *CreateSqlConcurrencyControlRuleReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetSqlType(val *SqlType) {
	p.SqlType = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetConcurrencyCount(val int32) {
	p.ConcurrencyCount = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetEffectiveTime(val int32) {
	p.EffectiveTime = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetKeyWords(val *string) {
	p.KeyWords = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetThrottleThreshold(val *int32) {
	p.ThrottleThreshold = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetLinkType(val *LinkType) {
	p.LinkType = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetThrottleType(val *ThrottleType) {
	p.ThrottleType = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetEndpointId(val *string) {
	p.EndpointId = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetEndpointType(val *EndpointType) {
	p.EndpointType = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetThrottleHost(val *string) {
	p.ThrottleHost = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetThrottleDB(val *string) {
	p.ThrottleDB = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetThrottleSqlText(val *string) {
	p.ThrottleSqlText = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetThrottleFingerPrint(val *string) {
	p.ThrottleFingerPrint = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetTerminationType(val *TerminationType) {
	p.TerminationType = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetThrottleMode(val *ThrottleMode) {
	p.ThrottleMode = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetThrottleTarget(val *ThrottleTarget) {
	p.ThrottleTarget = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetGroupIds(val []int64) {
	p.GroupIds = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetRegionId(val *string) {
	p.RegionId = val
}
func (p *CreateSqlConcurrencyControlRuleReq) SetDesc(val *string) {
	p.Desc = val
}

var fieldIDToName_CreateSqlConcurrencyControlRuleReq = map[int16]string{
	1:  "InstanceId",
	2:  "SqlType",
	3:  "ConcurrencyCount",
	4:  "EffectiveTime",
	5:  "KeyWords",
	6:  "InstanceType",
	7:  "ThrottleThreshold",
	8:  "LinkType",
	9:  "ThrottleType",
	10: "EndpointId",
	11: "EndpointType",
	12: "ThrottleHost",
	13: "ThrottleDB",
	14: "ThrottleSqlText",
	15: "ThrottleFingerPrint",
	16: "TerminationType",
	17: "ThrottleMode",
	18: "ThrottleTarget",
	19: "GroupIds",
	20: "RegionId",
	21: "Desc",
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetSqlType() bool {
	return p.SqlType != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetKeyWords() bool {
	return p.KeyWords != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetThrottleThreshold() bool {
	return p.ThrottleThreshold != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetLinkType() bool {
	return p.LinkType != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetThrottleType() bool {
	return p.ThrottleType != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetEndpointId() bool {
	return p.EndpointId != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetEndpointType() bool {
	return p.EndpointType != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetThrottleHost() bool {
	return p.ThrottleHost != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetThrottleDB() bool {
	return p.ThrottleDB != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetThrottleSqlText() bool {
	return p.ThrottleSqlText != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetThrottleFingerPrint() bool {
	return p.ThrottleFingerPrint != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetTerminationType() bool {
	return p.TerminationType != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetThrottleMode() bool {
	return p.ThrottleMode != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetThrottleTarget() bool {
	return p.ThrottleTarget != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetGroupIds() bool {
	return p.GroupIds != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) IsSetDesc() bool {
	return p.Desc != nil
}

func (p *CreateSqlConcurrencyControlRuleReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlConcurrencyControlRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConcurrencyCount bool = false
	var issetEffectiveTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetConcurrencyCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEffectiveTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConcurrencyCount {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEffectiveTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSqlConcurrencyControlRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateSqlConcurrencyControlRuleReq[fieldId]))
}

func (p *CreateSqlConcurrencyControlRuleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *SqlType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SqlType(v)
		_field = &tmp
	}
	p.SqlType = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConcurrencyCount = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EffectiveTime = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.KeyWords = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleThreshold = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *ThrottleType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ThrottleType(v)
		_field = &tmp
	}
	p.ThrottleType = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndpointId = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *EndpointType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := EndpointType(v)
		_field = &tmp
	}
	p.EndpointType = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleHost = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleDB = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleSqlText = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField15(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleFingerPrint = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField16(iprot thrift.TProtocol) error {

	var _field *TerminationType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TerminationType(v)
		_field = &tmp
	}
	p.TerminationType = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField17(iprot thrift.TProtocol) error {

	var _field *ThrottleMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ThrottleMode(v)
		_field = &tmp
	}
	p.ThrottleMode = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField18(iprot thrift.TProtocol) error {

	var _field *ThrottleTarget
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ThrottleTarget(v)
		_field = &tmp
	}
	p.ThrottleTarget = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField19(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int64, 0, size)
	for i := 0; i < size; i++ {

		var _elem int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.GroupIds = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField20(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}
func (p *CreateSqlConcurrencyControlRuleReq) ReadField21(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Desc = _field
	return nil
}

func (p *CreateSqlConcurrencyControlRuleReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlConcurrencyControlRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSqlConcurrencyControlRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlType() {
		if err = oprot.WriteFieldBegin("SqlType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SqlType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConcurrencyCount", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ConcurrencyCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EffectiveTime", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EffectiveTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeyWords() {
		if err = oprot.WriteFieldBegin("KeyWords", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.KeyWords); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleThreshold() {
		if err = oprot.WriteFieldBegin("ThrottleThreshold", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ThrottleThreshold); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinkType() {
		if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleType() {
		if err = oprot.WriteFieldBegin("ThrottleType", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ThrottleType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndpointId() {
		if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndpointId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndpointType() {
		if err = oprot.WriteFieldBegin("EndpointType", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.EndpointType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleHost() {
		if err = oprot.WriteFieldBegin("ThrottleHost", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleHost); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleDB() {
		if err = oprot.WriteFieldBegin("ThrottleDB", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleDB); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleSqlText() {
		if err = oprot.WriteFieldBegin("ThrottleSqlText", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleSqlText); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleFingerPrint() {
		if err = oprot.WriteFieldBegin("ThrottleFingerPrint", thrift.STRING, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleFingerPrint); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetTerminationType() {
		if err = oprot.WriteFieldBegin("TerminationType", thrift.I32, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TerminationType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleMode() {
		if err = oprot.WriteFieldBegin("ThrottleMode", thrift.I32, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ThrottleMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleTarget() {
		if err = oprot.WriteFieldBegin("ThrottleTarget", thrift.I32, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ThrottleTarget)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetGroupIds() {
		if err = oprot.WriteFieldBegin("GroupIds", thrift.LIST, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.GroupIds)); err != nil {
			return err
		}
		for _, v := range p.GroupIds {
			if err := oprot.WriteI64(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("RegionId", thrift.STRING, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetDesc() {
		if err = oprot.WriteFieldBegin("Desc", thrift.STRING, 21); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Desc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSqlConcurrencyControlRuleReq(%+v)", *p)

}

func (p *CreateSqlConcurrencyControlRuleReq) DeepEqual(ano *CreateSqlConcurrencyControlRuleReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SqlType) {
		return false
	}
	if !p.Field3DeepEqual(ano.ConcurrencyCount) {
		return false
	}
	if !p.Field4DeepEqual(ano.EffectiveTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.KeyWords) {
		return false
	}
	if !p.Field6DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field7DeepEqual(ano.ThrottleThreshold) {
		return false
	}
	if !p.Field8DeepEqual(ano.LinkType) {
		return false
	}
	if !p.Field9DeepEqual(ano.ThrottleType) {
		return false
	}
	if !p.Field10DeepEqual(ano.EndpointId) {
		return false
	}
	if !p.Field11DeepEqual(ano.EndpointType) {
		return false
	}
	if !p.Field12DeepEqual(ano.ThrottleHost) {
		return false
	}
	if !p.Field13DeepEqual(ano.ThrottleDB) {
		return false
	}
	if !p.Field14DeepEqual(ano.ThrottleSqlText) {
		return false
	}
	if !p.Field15DeepEqual(ano.ThrottleFingerPrint) {
		return false
	}
	if !p.Field16DeepEqual(ano.TerminationType) {
		return false
	}
	if !p.Field17DeepEqual(ano.ThrottleMode) {
		return false
	}
	if !p.Field18DeepEqual(ano.ThrottleTarget) {
		return false
	}
	if !p.Field19DeepEqual(ano.GroupIds) {
		return false
	}
	if !p.Field20DeepEqual(ano.RegionId) {
		return false
	}
	if !p.Field21DeepEqual(ano.Desc) {
		return false
	}
	return true
}

func (p *CreateSqlConcurrencyControlRuleReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field2DeepEqual(src *SqlType) bool {

	if p.SqlType == src {
		return true
	} else if p.SqlType == nil || src == nil {
		return false
	}
	if *p.SqlType != *src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field3DeepEqual(src int32) bool {

	if p.ConcurrencyCount != src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field4DeepEqual(src int32) bool {

	if p.EffectiveTime != src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field5DeepEqual(src *string) bool {

	if p.KeyWords == src {
		return true
	} else if p.KeyWords == nil || src == nil {
		return false
	}
	if strings.Compare(*p.KeyWords, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field6DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field7DeepEqual(src *int32) bool {

	if p.ThrottleThreshold == src {
		return true
	} else if p.ThrottleThreshold == nil || src == nil {
		return false
	}
	if *p.ThrottleThreshold != *src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field8DeepEqual(src *LinkType) bool {

	if p.LinkType == src {
		return true
	} else if p.LinkType == nil || src == nil {
		return false
	}
	if *p.LinkType != *src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field9DeepEqual(src *ThrottleType) bool {

	if p.ThrottleType == src {
		return true
	} else if p.ThrottleType == nil || src == nil {
		return false
	}
	if *p.ThrottleType != *src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field10DeepEqual(src *string) bool {

	if p.EndpointId == src {
		return true
	} else if p.EndpointId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndpointId, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field11DeepEqual(src *EndpointType) bool {

	if p.EndpointType == src {
		return true
	} else if p.EndpointType == nil || src == nil {
		return false
	}
	if *p.EndpointType != *src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field12DeepEqual(src *string) bool {

	if p.ThrottleHost == src {
		return true
	} else if p.ThrottleHost == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleHost, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field13DeepEqual(src *string) bool {

	if p.ThrottleDB == src {
		return true
	} else if p.ThrottleDB == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleDB, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field14DeepEqual(src *string) bool {

	if p.ThrottleSqlText == src {
		return true
	} else if p.ThrottleSqlText == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleSqlText, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field15DeepEqual(src *string) bool {

	if p.ThrottleFingerPrint == src {
		return true
	} else if p.ThrottleFingerPrint == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleFingerPrint, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field16DeepEqual(src *TerminationType) bool {

	if p.TerminationType == src {
		return true
	} else if p.TerminationType == nil || src == nil {
		return false
	}
	if *p.TerminationType != *src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field17DeepEqual(src *ThrottleMode) bool {

	if p.ThrottleMode == src {
		return true
	} else if p.ThrottleMode == nil || src == nil {
		return false
	}
	if *p.ThrottleMode != *src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field18DeepEqual(src *ThrottleTarget) bool {

	if p.ThrottleTarget == src {
		return true
	} else if p.ThrottleTarget == nil || src == nil {
		return false
	}
	if *p.ThrottleTarget != *src {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field19DeepEqual(src []int64) bool {

	if len(p.GroupIds) != len(src) {
		return false
	}
	for i, v := range p.GroupIds {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field20DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlConcurrencyControlRuleReq) Field21DeepEqual(src *string) bool {

	if p.Desc == src {
		return true
	} else if p.Desc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Desc, *src) != 0 {
		return false
	}
	return true
}

type CreateSqlConcurrencyControlRuleResp struct {
	RuleId int64 `thrift:"RuleId,1,required" frugal:"1,required,i64" json:"RuleId"`
}

func NewCreateSqlConcurrencyControlRuleResp() *CreateSqlConcurrencyControlRuleResp {
	return &CreateSqlConcurrencyControlRuleResp{}
}

func (p *CreateSqlConcurrencyControlRuleResp) InitDefault() {
}

func (p *CreateSqlConcurrencyControlRuleResp) GetRuleId() (v int64) {
	return p.RuleId
}
func (p *CreateSqlConcurrencyControlRuleResp) SetRuleId(val int64) {
	p.RuleId = val
}

var fieldIDToName_CreateSqlConcurrencyControlRuleResp = map[int16]string{
	1: "RuleId",
}

func (p *CreateSqlConcurrencyControlRuleResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlConcurrencyControlRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRuleId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRuleId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSqlConcurrencyControlRuleResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateSqlConcurrencyControlRuleResp[fieldId]))
}

func (p *CreateSqlConcurrencyControlRuleResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RuleId = _field
	return nil
}

func (p *CreateSqlConcurrencyControlRuleResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlConcurrencyControlRuleResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSqlConcurrencyControlRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RuleId", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RuleId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateSqlConcurrencyControlRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSqlConcurrencyControlRuleResp(%+v)", *p)

}

func (p *CreateSqlConcurrencyControlRuleResp) DeepEqual(ano *CreateSqlConcurrencyControlRuleResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RuleId) {
		return false
	}
	return true
}

func (p *CreateSqlConcurrencyControlRuleResp) Field1DeepEqual(src int64) bool {

	if p.RuleId != src {
		return false
	}
	return true
}

type StopSqlConcurrencyControlRuleReq struct {
	InstanceId   *string       `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	TaskId       []string      `thrift:"TaskId,2,optional" frugal:"2,optional,list<string>" json:"TaskId,omitempty"`
	InstanceType *DSType       `thrift:"InstanceType,3,optional" frugal:"3,optional,DSType" json:"InstanceType,omitempty"`
	LinkType     *LinkType     `thrift:"LinkType,4,optional" frugal:"4,optional,LinkType" json:"LinkType,omitempty"`
	ThrottleMode *ThrottleMode `thrift:"ThrottleMode,5,optional" frugal:"5,optional,ThrottleMode" json:"ThrottleMode,omitempty"`
	RegionId     *string       `thrift:"regionId,6,optional" frugal:"6,optional,string" json:"regionId,omitempty"`
}

func NewStopSqlConcurrencyControlRuleReq() *StopSqlConcurrencyControlRuleReq {
	return &StopSqlConcurrencyControlRuleReq{}
}

func (p *StopSqlConcurrencyControlRuleReq) InitDefault() {
}

var StopSqlConcurrencyControlRuleReq_InstanceId_DEFAULT string

func (p *StopSqlConcurrencyControlRuleReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return StopSqlConcurrencyControlRuleReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var StopSqlConcurrencyControlRuleReq_TaskId_DEFAULT []string

func (p *StopSqlConcurrencyControlRuleReq) GetTaskId() (v []string) {
	if !p.IsSetTaskId() {
		return StopSqlConcurrencyControlRuleReq_TaskId_DEFAULT
	}
	return p.TaskId
}

var StopSqlConcurrencyControlRuleReq_InstanceType_DEFAULT DSType

func (p *StopSqlConcurrencyControlRuleReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return StopSqlConcurrencyControlRuleReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var StopSqlConcurrencyControlRuleReq_LinkType_DEFAULT LinkType

func (p *StopSqlConcurrencyControlRuleReq) GetLinkType() (v LinkType) {
	if !p.IsSetLinkType() {
		return StopSqlConcurrencyControlRuleReq_LinkType_DEFAULT
	}
	return *p.LinkType
}

var StopSqlConcurrencyControlRuleReq_ThrottleMode_DEFAULT ThrottleMode

func (p *StopSqlConcurrencyControlRuleReq) GetThrottleMode() (v ThrottleMode) {
	if !p.IsSetThrottleMode() {
		return StopSqlConcurrencyControlRuleReq_ThrottleMode_DEFAULT
	}
	return *p.ThrottleMode
}

var StopSqlConcurrencyControlRuleReq_RegionId_DEFAULT string

func (p *StopSqlConcurrencyControlRuleReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return StopSqlConcurrencyControlRuleReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *StopSqlConcurrencyControlRuleReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *StopSqlConcurrencyControlRuleReq) SetTaskId(val []string) {
	p.TaskId = val
}
func (p *StopSqlConcurrencyControlRuleReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *StopSqlConcurrencyControlRuleReq) SetLinkType(val *LinkType) {
	p.LinkType = val
}
func (p *StopSqlConcurrencyControlRuleReq) SetThrottleMode(val *ThrottleMode) {
	p.ThrottleMode = val
}
func (p *StopSqlConcurrencyControlRuleReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_StopSqlConcurrencyControlRuleReq = map[int16]string{
	1: "InstanceId",
	2: "TaskId",
	3: "InstanceType",
	4: "LinkType",
	5: "ThrottleMode",
	6: "regionId",
}

func (p *StopSqlConcurrencyControlRuleReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *StopSqlConcurrencyControlRuleReq) IsSetTaskId() bool {
	return p.TaskId != nil
}

func (p *StopSqlConcurrencyControlRuleReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *StopSqlConcurrencyControlRuleReq) IsSetLinkType() bool {
	return p.LinkType != nil
}

func (p *StopSqlConcurrencyControlRuleReq) IsSetThrottleMode() bool {
	return p.ThrottleMode != nil
}

func (p *StopSqlConcurrencyControlRuleReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *StopSqlConcurrencyControlRuleReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopSqlConcurrencyControlRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StopSqlConcurrencyControlRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *StopSqlConcurrencyControlRuleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *StopSqlConcurrencyControlRuleReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TaskId = _field
	return nil
}
func (p *StopSqlConcurrencyControlRuleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *StopSqlConcurrencyControlRuleReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return nil
}
func (p *StopSqlConcurrencyControlRuleReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *ThrottleMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ThrottleMode(v)
		_field = &tmp
	}
	p.ThrottleMode = _field
	return nil
}
func (p *StopSqlConcurrencyControlRuleReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *StopSqlConcurrencyControlRuleReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopSqlConcurrencyControlRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("StopSqlConcurrencyControlRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StopSqlConcurrencyControlRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *StopSqlConcurrencyControlRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskId() {
		if err = oprot.WriteFieldBegin("TaskId", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TaskId)); err != nil {
			return err
		}
		for _, v := range p.TaskId {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *StopSqlConcurrencyControlRuleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *StopSqlConcurrencyControlRuleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinkType() {
		if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *StopSqlConcurrencyControlRuleReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleMode() {
		if err = oprot.WriteFieldBegin("ThrottleMode", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ThrottleMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *StopSqlConcurrencyControlRuleReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("regionId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *StopSqlConcurrencyControlRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopSqlConcurrencyControlRuleReq(%+v)", *p)

}

func (p *StopSqlConcurrencyControlRuleReq) DeepEqual(ano *StopSqlConcurrencyControlRuleReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.LinkType) {
		return false
	}
	if !p.Field5DeepEqual(ano.ThrottleMode) {
		return false
	}
	if !p.Field6DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *StopSqlConcurrencyControlRuleReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *StopSqlConcurrencyControlRuleReq) Field2DeepEqual(src []string) bool {

	if len(p.TaskId) != len(src) {
		return false
	}
	for i, v := range p.TaskId {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *StopSqlConcurrencyControlRuleReq) Field3DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *StopSqlConcurrencyControlRuleReq) Field4DeepEqual(src *LinkType) bool {

	if p.LinkType == src {
		return true
	} else if p.LinkType == nil || src == nil {
		return false
	}
	if *p.LinkType != *src {
		return false
	}
	return true
}
func (p *StopSqlConcurrencyControlRuleReq) Field5DeepEqual(src *ThrottleMode) bool {

	if p.ThrottleMode == src {
		return true
	} else if p.ThrottleMode == nil || src == nil {
		return false
	}
	if *p.ThrottleMode != *src {
		return false
	}
	return true
}
func (p *StopSqlConcurrencyControlRuleReq) Field6DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type StopSqlConcurrencyControlRuleResp struct {
}

func NewStopSqlConcurrencyControlRuleResp() *StopSqlConcurrencyControlRuleResp {
	return &StopSqlConcurrencyControlRuleResp{}
}

func (p *StopSqlConcurrencyControlRuleResp) InitDefault() {
}

var fieldIDToName_StopSqlConcurrencyControlRuleResp = map[int16]string{}

func (p *StopSqlConcurrencyControlRuleResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopSqlConcurrencyControlRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *StopSqlConcurrencyControlRuleResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopSqlConcurrencyControlRuleResp")

	if err = oprot.WriteStructBegin("StopSqlConcurrencyControlRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StopSqlConcurrencyControlRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopSqlConcurrencyControlRuleResp(%+v)", *p)

}

func (p *StopSqlConcurrencyControlRuleResp) DeepEqual(ano *StopSqlConcurrencyControlRuleResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type RestartSqlConcurrencyControlRuleReq struct {
	InstanceId   *string       `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	TaskId       *string       `thrift:"TaskId,2,optional" frugal:"2,optional,string" json:"TaskId,omitempty"`
	InstanceType *DSType       `thrift:"InstanceType,3,optional" frugal:"3,optional,DSType" json:"InstanceType,omitempty"`
	LinkType     *LinkType     `thrift:"LinkType,4,optional" frugal:"4,optional,LinkType" json:"LinkType,omitempty"`
	ThrottleMode *ThrottleMode `thrift:"ThrottleMode,5,optional" frugal:"5,optional,ThrottleMode" json:"ThrottleMode,omitempty"`
	RegionId     *string       `thrift:"regionId,6,optional" frugal:"6,optional,string" json:"regionId,omitempty"`
}

func NewRestartSqlConcurrencyControlRuleReq() *RestartSqlConcurrencyControlRuleReq {
	return &RestartSqlConcurrencyControlRuleReq{}
}

func (p *RestartSqlConcurrencyControlRuleReq) InitDefault() {
}

var RestartSqlConcurrencyControlRuleReq_InstanceId_DEFAULT string

func (p *RestartSqlConcurrencyControlRuleReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return RestartSqlConcurrencyControlRuleReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var RestartSqlConcurrencyControlRuleReq_TaskId_DEFAULT string

func (p *RestartSqlConcurrencyControlRuleReq) GetTaskId() (v string) {
	if !p.IsSetTaskId() {
		return RestartSqlConcurrencyControlRuleReq_TaskId_DEFAULT
	}
	return *p.TaskId
}

var RestartSqlConcurrencyControlRuleReq_InstanceType_DEFAULT DSType

func (p *RestartSqlConcurrencyControlRuleReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return RestartSqlConcurrencyControlRuleReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var RestartSqlConcurrencyControlRuleReq_LinkType_DEFAULT LinkType

func (p *RestartSqlConcurrencyControlRuleReq) GetLinkType() (v LinkType) {
	if !p.IsSetLinkType() {
		return RestartSqlConcurrencyControlRuleReq_LinkType_DEFAULT
	}
	return *p.LinkType
}

var RestartSqlConcurrencyControlRuleReq_ThrottleMode_DEFAULT ThrottleMode

func (p *RestartSqlConcurrencyControlRuleReq) GetThrottleMode() (v ThrottleMode) {
	if !p.IsSetThrottleMode() {
		return RestartSqlConcurrencyControlRuleReq_ThrottleMode_DEFAULT
	}
	return *p.ThrottleMode
}

var RestartSqlConcurrencyControlRuleReq_RegionId_DEFAULT string

func (p *RestartSqlConcurrencyControlRuleReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return RestartSqlConcurrencyControlRuleReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *RestartSqlConcurrencyControlRuleReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *RestartSqlConcurrencyControlRuleReq) SetTaskId(val *string) {
	p.TaskId = val
}
func (p *RestartSqlConcurrencyControlRuleReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *RestartSqlConcurrencyControlRuleReq) SetLinkType(val *LinkType) {
	p.LinkType = val
}
func (p *RestartSqlConcurrencyControlRuleReq) SetThrottleMode(val *ThrottleMode) {
	p.ThrottleMode = val
}
func (p *RestartSqlConcurrencyControlRuleReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_RestartSqlConcurrencyControlRuleReq = map[int16]string{
	1: "InstanceId",
	2: "TaskId",
	3: "InstanceType",
	4: "LinkType",
	5: "ThrottleMode",
	6: "regionId",
}

func (p *RestartSqlConcurrencyControlRuleReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *RestartSqlConcurrencyControlRuleReq) IsSetTaskId() bool {
	return p.TaskId != nil
}

func (p *RestartSqlConcurrencyControlRuleReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *RestartSqlConcurrencyControlRuleReq) IsSetLinkType() bool {
	return p.LinkType != nil
}

func (p *RestartSqlConcurrencyControlRuleReq) IsSetThrottleMode() bool {
	return p.ThrottleMode != nil
}

func (p *RestartSqlConcurrencyControlRuleReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *RestartSqlConcurrencyControlRuleReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestartSqlConcurrencyControlRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RestartSqlConcurrencyControlRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RestartSqlConcurrencyControlRuleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *RestartSqlConcurrencyControlRuleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TaskId = _field
	return nil
}
func (p *RestartSqlConcurrencyControlRuleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *RestartSqlConcurrencyControlRuleReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return nil
}
func (p *RestartSqlConcurrencyControlRuleReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *ThrottleMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ThrottleMode(v)
		_field = &tmp
	}
	p.ThrottleMode = _field
	return nil
}
func (p *RestartSqlConcurrencyControlRuleReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *RestartSqlConcurrencyControlRuleReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestartSqlConcurrencyControlRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("RestartSqlConcurrencyControlRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestartSqlConcurrencyControlRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RestartSqlConcurrencyControlRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskId() {
		if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TaskId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RestartSqlConcurrencyControlRuleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RestartSqlConcurrencyControlRuleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinkType() {
		if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RestartSqlConcurrencyControlRuleReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleMode() {
		if err = oprot.WriteFieldBegin("ThrottleMode", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ThrottleMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *RestartSqlConcurrencyControlRuleReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("regionId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *RestartSqlConcurrencyControlRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestartSqlConcurrencyControlRuleReq(%+v)", *p)

}

func (p *RestartSqlConcurrencyControlRuleReq) DeepEqual(ano *RestartSqlConcurrencyControlRuleReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.LinkType) {
		return false
	}
	if !p.Field5DeepEqual(ano.ThrottleMode) {
		return false
	}
	if !p.Field6DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *RestartSqlConcurrencyControlRuleReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *RestartSqlConcurrencyControlRuleReq) Field2DeepEqual(src *string) bool {

	if p.TaskId == src {
		return true
	} else if p.TaskId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TaskId, *src) != 0 {
		return false
	}
	return true
}
func (p *RestartSqlConcurrencyControlRuleReq) Field3DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *RestartSqlConcurrencyControlRuleReq) Field4DeepEqual(src *LinkType) bool {

	if p.LinkType == src {
		return true
	} else if p.LinkType == nil || src == nil {
		return false
	}
	if *p.LinkType != *src {
		return false
	}
	return true
}
func (p *RestartSqlConcurrencyControlRuleReq) Field5DeepEqual(src *ThrottleMode) bool {

	if p.ThrottleMode == src {
		return true
	} else if p.ThrottleMode == nil || src == nil {
		return false
	}
	if *p.ThrottleMode != *src {
		return false
	}
	return true
}
func (p *RestartSqlConcurrencyControlRuleReq) Field6DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type RestartSqlConcurrencyControlRuleResp struct {
}

func NewRestartSqlConcurrencyControlRuleResp() *RestartSqlConcurrencyControlRuleResp {
	return &RestartSqlConcurrencyControlRuleResp{}
}

func (p *RestartSqlConcurrencyControlRuleResp) InitDefault() {
}

var fieldIDToName_RestartSqlConcurrencyControlRuleResp = map[int16]string{}

func (p *RestartSqlConcurrencyControlRuleResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestartSqlConcurrencyControlRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *RestartSqlConcurrencyControlRuleResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RestartSqlConcurrencyControlRuleResp")

	if err = oprot.WriteStructBegin("RestartSqlConcurrencyControlRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RestartSqlConcurrencyControlRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RestartSqlConcurrencyControlRuleResp(%+v)", *p)

}

func (p *RestartSqlConcurrencyControlRuleResp) DeepEqual(ano *RestartSqlConcurrencyControlRuleResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteSqlConcurrencyControlRuleReq struct {
	InstanceId   *string       `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	TaskId       []string      `thrift:"TaskId,2,optional" frugal:"2,optional,list<string>" json:"TaskId,omitempty"`
	InstanceType *DSType       `thrift:"InstanceType,3,optional" frugal:"3,optional,DSType" json:"InstanceType,omitempty"`
	LinkType     *LinkType     `thrift:"LinkType,4,optional" frugal:"4,optional,LinkType" json:"LinkType,omitempty"`
	ThrottleMode *ThrottleMode `thrift:"ThrottleMode,5,optional" frugal:"5,optional,ThrottleMode" json:"ThrottleMode,omitempty"`
	RegionId     *string       `thrift:"regionId,6,optional" frugal:"6,optional,string" json:"regionId,omitempty"`
}

func NewDeleteSqlConcurrencyControlRuleReq() *DeleteSqlConcurrencyControlRuleReq {
	return &DeleteSqlConcurrencyControlRuleReq{}
}

func (p *DeleteSqlConcurrencyControlRuleReq) InitDefault() {
}

var DeleteSqlConcurrencyControlRuleReq_InstanceId_DEFAULT string

func (p *DeleteSqlConcurrencyControlRuleReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DeleteSqlConcurrencyControlRuleReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DeleteSqlConcurrencyControlRuleReq_TaskId_DEFAULT []string

func (p *DeleteSqlConcurrencyControlRuleReq) GetTaskId() (v []string) {
	if !p.IsSetTaskId() {
		return DeleteSqlConcurrencyControlRuleReq_TaskId_DEFAULT
	}
	return p.TaskId
}

var DeleteSqlConcurrencyControlRuleReq_InstanceType_DEFAULT DSType

func (p *DeleteSqlConcurrencyControlRuleReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DeleteSqlConcurrencyControlRuleReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DeleteSqlConcurrencyControlRuleReq_LinkType_DEFAULT LinkType

func (p *DeleteSqlConcurrencyControlRuleReq) GetLinkType() (v LinkType) {
	if !p.IsSetLinkType() {
		return DeleteSqlConcurrencyControlRuleReq_LinkType_DEFAULT
	}
	return *p.LinkType
}

var DeleteSqlConcurrencyControlRuleReq_ThrottleMode_DEFAULT ThrottleMode

func (p *DeleteSqlConcurrencyControlRuleReq) GetThrottleMode() (v ThrottleMode) {
	if !p.IsSetThrottleMode() {
		return DeleteSqlConcurrencyControlRuleReq_ThrottleMode_DEFAULT
	}
	return *p.ThrottleMode
}

var DeleteSqlConcurrencyControlRuleReq_RegionId_DEFAULT string

func (p *DeleteSqlConcurrencyControlRuleReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DeleteSqlConcurrencyControlRuleReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *DeleteSqlConcurrencyControlRuleReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DeleteSqlConcurrencyControlRuleReq) SetTaskId(val []string) {
	p.TaskId = val
}
func (p *DeleteSqlConcurrencyControlRuleReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DeleteSqlConcurrencyControlRuleReq) SetLinkType(val *LinkType) {
	p.LinkType = val
}
func (p *DeleteSqlConcurrencyControlRuleReq) SetThrottleMode(val *ThrottleMode) {
	p.ThrottleMode = val
}
func (p *DeleteSqlConcurrencyControlRuleReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_DeleteSqlConcurrencyControlRuleReq = map[int16]string{
	1: "InstanceId",
	2: "TaskId",
	3: "InstanceType",
	4: "LinkType",
	5: "ThrottleMode",
	6: "regionId",
}

func (p *DeleteSqlConcurrencyControlRuleReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) IsSetTaskId() bool {
	return p.TaskId != nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) IsSetLinkType() bool {
	return p.LinkType != nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) IsSetThrottleMode() bool {
	return p.ThrottleMode != nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteSqlConcurrencyControlRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteSqlConcurrencyControlRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteSqlConcurrencyControlRuleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteSqlConcurrencyControlRuleReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TaskId = _field
	return nil
}
func (p *DeleteSqlConcurrencyControlRuleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DeleteSqlConcurrencyControlRuleReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return nil
}
func (p *DeleteSqlConcurrencyControlRuleReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *ThrottleMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ThrottleMode(v)
		_field = &tmp
	}
	p.ThrottleMode = _field
	return nil
}
func (p *DeleteSqlConcurrencyControlRuleReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *DeleteSqlConcurrencyControlRuleReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteSqlConcurrencyControlRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteSqlConcurrencyControlRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteSqlConcurrencyControlRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteSqlConcurrencyControlRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskId() {
		if err = oprot.WriteFieldBegin("TaskId", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TaskId)); err != nil {
			return err
		}
		for _, v := range p.TaskId {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteSqlConcurrencyControlRuleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DeleteSqlConcurrencyControlRuleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinkType() {
		if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DeleteSqlConcurrencyControlRuleReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleMode() {
		if err = oprot.WriteFieldBegin("ThrottleMode", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ThrottleMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DeleteSqlConcurrencyControlRuleReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("regionId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DeleteSqlConcurrencyControlRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteSqlConcurrencyControlRuleReq(%+v)", *p)

}

func (p *DeleteSqlConcurrencyControlRuleReq) DeepEqual(ano *DeleteSqlConcurrencyControlRuleReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.LinkType) {
		return false
	}
	if !p.Field5DeepEqual(ano.ThrottleMode) {
		return false
	}
	if !p.Field6DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DeleteSqlConcurrencyControlRuleReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DeleteSqlConcurrencyControlRuleReq) Field2DeepEqual(src []string) bool {

	if len(p.TaskId) != len(src) {
		return false
	}
	for i, v := range p.TaskId {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DeleteSqlConcurrencyControlRuleReq) Field3DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DeleteSqlConcurrencyControlRuleReq) Field4DeepEqual(src *LinkType) bool {

	if p.LinkType == src {
		return true
	} else if p.LinkType == nil || src == nil {
		return false
	}
	if *p.LinkType != *src {
		return false
	}
	return true
}
func (p *DeleteSqlConcurrencyControlRuleReq) Field5DeepEqual(src *ThrottleMode) bool {

	if p.ThrottleMode == src {
		return true
	} else if p.ThrottleMode == nil || src == nil {
		return false
	}
	if *p.ThrottleMode != *src {
		return false
	}
	return true
}
func (p *DeleteSqlConcurrencyControlRuleReq) Field6DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type DeleteSqlConcurrencyControlRuleResp struct {
}

func NewDeleteSqlConcurrencyControlRuleResp() *DeleteSqlConcurrencyControlRuleResp {
	return &DeleteSqlConcurrencyControlRuleResp{}
}

func (p *DeleteSqlConcurrencyControlRuleResp) InitDefault() {
}

var fieldIDToName_DeleteSqlConcurrencyControlRuleResp = map[int16]string{}

func (p *DeleteSqlConcurrencyControlRuleResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteSqlConcurrencyControlRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteSqlConcurrencyControlRuleResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteSqlConcurrencyControlRuleResp")

	if err = oprot.WriteStructBegin("DeleteSqlConcurrencyControlRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteSqlConcurrencyControlRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteSqlConcurrencyControlRuleResp(%+v)", *p)

}

func (p *DeleteSqlConcurrencyControlRuleResp) DeepEqual(ano *DeleteSqlConcurrencyControlRuleResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeSqlConcurrencyControlRulesReq struct {
	InstanceId   *string           `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	SearchParam  *SqlSearchParam   `thrift:"SearchParam,2,optional" frugal:"2,optional,SqlSearchParam" json:"SearchParam,omitempty"`
	PageNumber   *int32            `thrift:"PageNumber,3,optional" frugal:"3,optional,i32" json:"PageNumber,omitempty"`
	PageSize     *int32            `thrift:"PageSize,4,optional" frugal:"4,optional,i32" json:"PageSize,omitempty"`
	SortBy       *SortBy           `thrift:"SortBy,5,optional" frugal:"5,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy      *OrderByForDBRule `thrift:"OrderBy,6,optional" frugal:"6,optional,OrderByForDBRule" json:"OrderBy,omitempty"`
	InstanceType *DSType           `thrift:"InstanceType,7,optional" frugal:"7,optional,DSType" json:"InstanceType,omitempty"`
	LinkType     *LinkType         `thrift:"LinkType,8,optional" frugal:"8,optional,LinkType" json:"LinkType,omitempty"`
	ThrottleMode *ThrottleMode     `thrift:"ThrottleMode,9,optional" frugal:"9,optional,ThrottleMode" json:"ThrottleMode,omitempty"`
	RegionId     *string           `thrift:"regionId,10,optional" frugal:"10,optional,string" json:"regionId,omitempty"`
}

func NewDescribeSqlConcurrencyControlRulesReq() *DescribeSqlConcurrencyControlRulesReq {
	return &DescribeSqlConcurrencyControlRulesReq{}
}

func (p *DescribeSqlConcurrencyControlRulesReq) InitDefault() {
}

var DescribeSqlConcurrencyControlRulesReq_InstanceId_DEFAULT string

func (p *DescribeSqlConcurrencyControlRulesReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeSqlConcurrencyControlRulesReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeSqlConcurrencyControlRulesReq_SearchParam_DEFAULT *SqlSearchParam

func (p *DescribeSqlConcurrencyControlRulesReq) GetSearchParam() (v *SqlSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeSqlConcurrencyControlRulesReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeSqlConcurrencyControlRulesReq_PageNumber_DEFAULT int32

func (p *DescribeSqlConcurrencyControlRulesReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeSqlConcurrencyControlRulesReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeSqlConcurrencyControlRulesReq_PageSize_DEFAULT int32

func (p *DescribeSqlConcurrencyControlRulesReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSqlConcurrencyControlRulesReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeSqlConcurrencyControlRulesReq_SortBy_DEFAULT SortBy

func (p *DescribeSqlConcurrencyControlRulesReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeSqlConcurrencyControlRulesReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeSqlConcurrencyControlRulesReq_OrderBy_DEFAULT OrderByForDBRule

func (p *DescribeSqlConcurrencyControlRulesReq) GetOrderBy() (v OrderByForDBRule) {
	if !p.IsSetOrderBy() {
		return DescribeSqlConcurrencyControlRulesReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeSqlConcurrencyControlRulesReq_InstanceType_DEFAULT DSType

func (p *DescribeSqlConcurrencyControlRulesReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeSqlConcurrencyControlRulesReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeSqlConcurrencyControlRulesReq_LinkType_DEFAULT LinkType

func (p *DescribeSqlConcurrencyControlRulesReq) GetLinkType() (v LinkType) {
	if !p.IsSetLinkType() {
		return DescribeSqlConcurrencyControlRulesReq_LinkType_DEFAULT
	}
	return *p.LinkType
}

var DescribeSqlConcurrencyControlRulesReq_ThrottleMode_DEFAULT ThrottleMode

func (p *DescribeSqlConcurrencyControlRulesReq) GetThrottleMode() (v ThrottleMode) {
	if !p.IsSetThrottleMode() {
		return DescribeSqlConcurrencyControlRulesReq_ThrottleMode_DEFAULT
	}
	return *p.ThrottleMode
}

var DescribeSqlConcurrencyControlRulesReq_RegionId_DEFAULT string

func (p *DescribeSqlConcurrencyControlRulesReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return DescribeSqlConcurrencyControlRulesReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *DescribeSqlConcurrencyControlRulesReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeSqlConcurrencyControlRulesReq) SetSearchParam(val *SqlSearchParam) {
	p.SearchParam = val
}
func (p *DescribeSqlConcurrencyControlRulesReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeSqlConcurrencyControlRulesReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeSqlConcurrencyControlRulesReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeSqlConcurrencyControlRulesReq) SetOrderBy(val *OrderByForDBRule) {
	p.OrderBy = val
}
func (p *DescribeSqlConcurrencyControlRulesReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DescribeSqlConcurrencyControlRulesReq) SetLinkType(val *LinkType) {
	p.LinkType = val
}
func (p *DescribeSqlConcurrencyControlRulesReq) SetThrottleMode(val *ThrottleMode) {
	p.ThrottleMode = val
}
func (p *DescribeSqlConcurrencyControlRulesReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_DescribeSqlConcurrencyControlRulesReq = map[int16]string{
	1:  "InstanceId",
	2:  "SearchParam",
	3:  "PageNumber",
	4:  "PageSize",
	5:  "SortBy",
	6:  "OrderBy",
	7:  "InstanceType",
	8:  "LinkType",
	9:  "ThrottleMode",
	10: "regionId",
}

func (p *DescribeSqlConcurrencyControlRulesReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) IsSetLinkType() bool {
	return p.LinkType != nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) IsSetThrottleMode() bool {
	return p.ThrottleMode != nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlConcurrencyControlRulesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlConcurrencyControlRulesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRulesReq) ReadField2(iprot thrift.TProtocol) error {
	_field := NewSqlSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRulesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRulesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRulesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRulesReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *OrderByForDBRule
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForDBRule(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRulesReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRulesReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRulesReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *ThrottleMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ThrottleMode(v)
		_field = &tmp
	}
	p.ThrottleMode = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRulesReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *DescribeSqlConcurrencyControlRulesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlConcurrencyControlRulesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlConcurrencyControlRulesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinkType() {
		if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleMode() {
		if err = oprot.WriteFieldBegin("ThrottleMode", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ThrottleMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("regionId", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlConcurrencyControlRulesReq(%+v)", *p)

}

func (p *DescribeSqlConcurrencyControlRulesReq) DeepEqual(ano *DescribeSqlConcurrencyControlRulesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field6DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field7DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field8DeepEqual(ano.LinkType) {
		return false
	}
	if !p.Field9DeepEqual(ano.ThrottleMode) {
		return false
	}
	if !p.Field10DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *DescribeSqlConcurrencyControlRulesReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRulesReq) Field2DeepEqual(src *SqlSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRulesReq) Field3DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRulesReq) Field4DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRulesReq) Field5DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRulesReq) Field6DeepEqual(src *OrderByForDBRule) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRulesReq) Field7DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRulesReq) Field8DeepEqual(src *LinkType) bool {

	if p.LinkType == src {
		return true
	} else if p.LinkType == nil || src == nil {
		return false
	}
	if *p.LinkType != *src {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRulesReq) Field9DeepEqual(src *ThrottleMode) bool {

	if p.ThrottleMode == src {
		return true
	} else if p.ThrottleMode == nil || src == nil {
		return false
	}
	if *p.ThrottleMode != *src {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRulesReq) Field10DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type RuleDetail struct {
	TaskId              string    `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
	SqlType             SqlType   `thrift:"SqlType,2,required" frugal:"2,required,SqlType" json:"SqlType"`
	RuleState           RuleState `thrift:"RuleState,3,required" frugal:"3,required,RuleState" json:"RuleState"`
	KeyWord             string    `thrift:"KeyWord,4,required" frugal:"4,required,string" json:"KeyWord"`
	CreateTime          string    `thrift:"CreateTime,5,required" frugal:"5,required,string" json:"CreateTime"`
	RemainingTime       int32     `thrift:"RemainingTime,6,required" frugal:"6,required,i32" json:"RemainingTime"`
	EndTime             string    `thrift:"EndTime,7,required" frugal:"7,required,string" json:"EndTime"`
	ConcurrencyCount    int32     `thrift:"ConcurrencyCount,8,required" frugal:"8,required,i32" json:"ConcurrencyCount"`
	EffectiveTime       int32     `thrift:"EffectiveTime,9,required" frugal:"9,required,i32" json:"EffectiveTime"`
	EndpointType        *string   `thrift:"EndpointType,10,optional" frugal:"10,optional,string" json:"EndpointType,omitempty"`
	EndpointId          *string   `thrift:"EndpointId,11,optional" frugal:"11,optional,string" json:"EndpointId,omitempty"`
	ThrottleType        *string   `thrift:"ThrottleType,12,optional" frugal:"12,optional,string" json:"ThrottleType,omitempty"`
	ThrottleHost        *string   `thrift:"ThrottleHost,13,optional" frugal:"13,optional,string" json:"ThrottleHost,omitempty"`
	ThrottleDB          *string   `thrift:"ThrottleDB,14,optional" frugal:"14,optional,string" json:"ThrottleDB,omitempty"`
	ThrottleSqlText     *string   `thrift:"ThrottleSqlText,15,optional" frugal:"15,optional,string" json:"ThrottleSqlText,omitempty"`
	ThrottleFingerPrint *string   `thrift:"ThrottleFingerPrint,16,optional" frugal:"16,optional,string" json:"ThrottleFingerPrint,omitempty"`
	ThrottleThreshold   *int32    `thrift:"ThrottleThreshold,17,optional" frugal:"17,optional,i32" json:"ThrottleThreshold,omitempty"`
	RejectCount         *int64    `thrift:"RejectCount,18,optional" frugal:"18,optional,i64" json:"RejectCount,omitempty"`
	ThrottleTarget      *string   `thrift:"ThrottleTarget,19,optional" frugal:"19,optional,string" json:"ThrottleTarget,omitempty"`
	ThrottleMode        *string   `thrift:"ThrottleMode,20,optional" frugal:"20,optional,string" json:"ThrottleMode,omitempty"`
	Desc                *string   `thrift:"Desc,21,optional" frugal:"21,optional,string" json:"Desc,omitempty"`
	TerminationType     *string   `thrift:"TerminationType,22,optional" frugal:"22,optional,string" json:"TerminationType,omitempty"`
}

func NewRuleDetail() *RuleDetail {
	return &RuleDetail{}
}

func (p *RuleDetail) InitDefault() {
}

func (p *RuleDetail) GetTaskId() (v string) {
	return p.TaskId
}

func (p *RuleDetail) GetSqlType() (v SqlType) {
	return p.SqlType
}

func (p *RuleDetail) GetRuleState() (v RuleState) {
	return p.RuleState
}

func (p *RuleDetail) GetKeyWord() (v string) {
	return p.KeyWord
}

func (p *RuleDetail) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *RuleDetail) GetRemainingTime() (v int32) {
	return p.RemainingTime
}

func (p *RuleDetail) GetEndTime() (v string) {
	return p.EndTime
}

func (p *RuleDetail) GetConcurrencyCount() (v int32) {
	return p.ConcurrencyCount
}

func (p *RuleDetail) GetEffectiveTime() (v int32) {
	return p.EffectiveTime
}

var RuleDetail_EndpointType_DEFAULT string

func (p *RuleDetail) GetEndpointType() (v string) {
	if !p.IsSetEndpointType() {
		return RuleDetail_EndpointType_DEFAULT
	}
	return *p.EndpointType
}

var RuleDetail_EndpointId_DEFAULT string

func (p *RuleDetail) GetEndpointId() (v string) {
	if !p.IsSetEndpointId() {
		return RuleDetail_EndpointId_DEFAULT
	}
	return *p.EndpointId
}

var RuleDetail_ThrottleType_DEFAULT string

func (p *RuleDetail) GetThrottleType() (v string) {
	if !p.IsSetThrottleType() {
		return RuleDetail_ThrottleType_DEFAULT
	}
	return *p.ThrottleType
}

var RuleDetail_ThrottleHost_DEFAULT string

func (p *RuleDetail) GetThrottleHost() (v string) {
	if !p.IsSetThrottleHost() {
		return RuleDetail_ThrottleHost_DEFAULT
	}
	return *p.ThrottleHost
}

var RuleDetail_ThrottleDB_DEFAULT string

func (p *RuleDetail) GetThrottleDB() (v string) {
	if !p.IsSetThrottleDB() {
		return RuleDetail_ThrottleDB_DEFAULT
	}
	return *p.ThrottleDB
}

var RuleDetail_ThrottleSqlText_DEFAULT string

func (p *RuleDetail) GetThrottleSqlText() (v string) {
	if !p.IsSetThrottleSqlText() {
		return RuleDetail_ThrottleSqlText_DEFAULT
	}
	return *p.ThrottleSqlText
}

var RuleDetail_ThrottleFingerPrint_DEFAULT string

func (p *RuleDetail) GetThrottleFingerPrint() (v string) {
	if !p.IsSetThrottleFingerPrint() {
		return RuleDetail_ThrottleFingerPrint_DEFAULT
	}
	return *p.ThrottleFingerPrint
}

var RuleDetail_ThrottleThreshold_DEFAULT int32

func (p *RuleDetail) GetThrottleThreshold() (v int32) {
	if !p.IsSetThrottleThreshold() {
		return RuleDetail_ThrottleThreshold_DEFAULT
	}
	return *p.ThrottleThreshold
}

var RuleDetail_RejectCount_DEFAULT int64

func (p *RuleDetail) GetRejectCount() (v int64) {
	if !p.IsSetRejectCount() {
		return RuleDetail_RejectCount_DEFAULT
	}
	return *p.RejectCount
}

var RuleDetail_ThrottleTarget_DEFAULT string

func (p *RuleDetail) GetThrottleTarget() (v string) {
	if !p.IsSetThrottleTarget() {
		return RuleDetail_ThrottleTarget_DEFAULT
	}
	return *p.ThrottleTarget
}

var RuleDetail_ThrottleMode_DEFAULT string

func (p *RuleDetail) GetThrottleMode() (v string) {
	if !p.IsSetThrottleMode() {
		return RuleDetail_ThrottleMode_DEFAULT
	}
	return *p.ThrottleMode
}

var RuleDetail_Desc_DEFAULT string

func (p *RuleDetail) GetDesc() (v string) {
	if !p.IsSetDesc() {
		return RuleDetail_Desc_DEFAULT
	}
	return *p.Desc
}

var RuleDetail_TerminationType_DEFAULT string

func (p *RuleDetail) GetTerminationType() (v string) {
	if !p.IsSetTerminationType() {
		return RuleDetail_TerminationType_DEFAULT
	}
	return *p.TerminationType
}
func (p *RuleDetail) SetTaskId(val string) {
	p.TaskId = val
}
func (p *RuleDetail) SetSqlType(val SqlType) {
	p.SqlType = val
}
func (p *RuleDetail) SetRuleState(val RuleState) {
	p.RuleState = val
}
func (p *RuleDetail) SetKeyWord(val string) {
	p.KeyWord = val
}
func (p *RuleDetail) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *RuleDetail) SetRemainingTime(val int32) {
	p.RemainingTime = val
}
func (p *RuleDetail) SetEndTime(val string) {
	p.EndTime = val
}
func (p *RuleDetail) SetConcurrencyCount(val int32) {
	p.ConcurrencyCount = val
}
func (p *RuleDetail) SetEffectiveTime(val int32) {
	p.EffectiveTime = val
}
func (p *RuleDetail) SetEndpointType(val *string) {
	p.EndpointType = val
}
func (p *RuleDetail) SetEndpointId(val *string) {
	p.EndpointId = val
}
func (p *RuleDetail) SetThrottleType(val *string) {
	p.ThrottleType = val
}
func (p *RuleDetail) SetThrottleHost(val *string) {
	p.ThrottleHost = val
}
func (p *RuleDetail) SetThrottleDB(val *string) {
	p.ThrottleDB = val
}
func (p *RuleDetail) SetThrottleSqlText(val *string) {
	p.ThrottleSqlText = val
}
func (p *RuleDetail) SetThrottleFingerPrint(val *string) {
	p.ThrottleFingerPrint = val
}
func (p *RuleDetail) SetThrottleThreshold(val *int32) {
	p.ThrottleThreshold = val
}
func (p *RuleDetail) SetRejectCount(val *int64) {
	p.RejectCount = val
}
func (p *RuleDetail) SetThrottleTarget(val *string) {
	p.ThrottleTarget = val
}
func (p *RuleDetail) SetThrottleMode(val *string) {
	p.ThrottleMode = val
}
func (p *RuleDetail) SetDesc(val *string) {
	p.Desc = val
}
func (p *RuleDetail) SetTerminationType(val *string) {
	p.TerminationType = val
}

var fieldIDToName_RuleDetail = map[int16]string{
	1:  "TaskId",
	2:  "SqlType",
	3:  "RuleState",
	4:  "KeyWord",
	5:  "CreateTime",
	6:  "RemainingTime",
	7:  "EndTime",
	8:  "ConcurrencyCount",
	9:  "EffectiveTime",
	10: "EndpointType",
	11: "EndpointId",
	12: "ThrottleType",
	13: "ThrottleHost",
	14: "ThrottleDB",
	15: "ThrottleSqlText",
	16: "ThrottleFingerPrint",
	17: "ThrottleThreshold",
	18: "RejectCount",
	19: "ThrottleTarget",
	20: "ThrottleMode",
	21: "Desc",
	22: "TerminationType",
}

func (p *RuleDetail) IsSetEndpointType() bool {
	return p.EndpointType != nil
}

func (p *RuleDetail) IsSetEndpointId() bool {
	return p.EndpointId != nil
}

func (p *RuleDetail) IsSetThrottleType() bool {
	return p.ThrottleType != nil
}

func (p *RuleDetail) IsSetThrottleHost() bool {
	return p.ThrottleHost != nil
}

func (p *RuleDetail) IsSetThrottleDB() bool {
	return p.ThrottleDB != nil
}

func (p *RuleDetail) IsSetThrottleSqlText() bool {
	return p.ThrottleSqlText != nil
}

func (p *RuleDetail) IsSetThrottleFingerPrint() bool {
	return p.ThrottleFingerPrint != nil
}

func (p *RuleDetail) IsSetThrottleThreshold() bool {
	return p.ThrottleThreshold != nil
}

func (p *RuleDetail) IsSetRejectCount() bool {
	return p.RejectCount != nil
}

func (p *RuleDetail) IsSetThrottleTarget() bool {
	return p.ThrottleTarget != nil
}

func (p *RuleDetail) IsSetThrottleMode() bool {
	return p.ThrottleMode != nil
}

func (p *RuleDetail) IsSetDesc() bool {
	return p.Desc != nil
}

func (p *RuleDetail) IsSetTerminationType() bool {
	return p.TerminationType != nil
}

func (p *RuleDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RuleDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	var issetSqlType bool = false
	var issetRuleState bool = false
	var issetKeyWord bool = false
	var issetCreateTime bool = false
	var issetRemainingTime bool = false
	var issetEndTime bool = false
	var issetConcurrencyCount bool = false
	var issetEffectiveTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetKeyWord = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetRemainingTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetConcurrencyCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetEffectiveTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSqlType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRuleState {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetKeyWord {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetRemainingTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetConcurrencyCount {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetEffectiveTime {
		fieldId = 9
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_RuleDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_RuleDetail[fieldId]))
}

func (p *RuleDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *RuleDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field SqlType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SqlType(v)
	}
	p.SqlType = _field
	return nil
}
func (p *RuleDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field RuleState
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = RuleState(v)
	}
	p.RuleState = _field
	return nil
}
func (p *RuleDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.KeyWord = _field
	return nil
}
func (p *RuleDetail) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *RuleDetail) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RemainingTime = _field
	return nil
}
func (p *RuleDetail) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *RuleDetail) ReadField8(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ConcurrencyCount = _field
	return nil
}
func (p *RuleDetail) ReadField9(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EffectiveTime = _field
	return nil
}
func (p *RuleDetail) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndpointType = _field
	return nil
}
func (p *RuleDetail) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EndpointId = _field
	return nil
}
func (p *RuleDetail) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleType = _field
	return nil
}
func (p *RuleDetail) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleHost = _field
	return nil
}
func (p *RuleDetail) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleDB = _field
	return nil
}
func (p *RuleDetail) ReadField15(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleSqlText = _field
	return nil
}
func (p *RuleDetail) ReadField16(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleFingerPrint = _field
	return nil
}
func (p *RuleDetail) ReadField17(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleThreshold = _field
	return nil
}
func (p *RuleDetail) ReadField18(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RejectCount = _field
	return nil
}
func (p *RuleDetail) ReadField19(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleTarget = _field
	return nil
}
func (p *RuleDetail) ReadField20(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ThrottleMode = _field
	return nil
}
func (p *RuleDetail) ReadField21(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Desc = _field
	return nil
}
func (p *RuleDetail) ReadField22(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TerminationType = _field
	return nil
}

func (p *RuleDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("RuleDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("RuleDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *RuleDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *RuleDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.SqlType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *RuleDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RuleState", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.RuleState)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *RuleDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("KeyWord", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.KeyWord); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *RuleDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *RuleDetail) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RemainingTime", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.RemainingTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *RuleDetail) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *RuleDetail) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConcurrencyCount", thrift.I32, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ConcurrencyCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *RuleDetail) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EffectiveTime", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EffectiveTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *RuleDetail) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndpointType() {
		if err = oprot.WriteFieldBegin("EndpointType", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndpointType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *RuleDetail) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetEndpointId() {
		if err = oprot.WriteFieldBegin("EndpointId", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.EndpointId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *RuleDetail) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleType() {
		if err = oprot.WriteFieldBegin("ThrottleType", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *RuleDetail) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleHost() {
		if err = oprot.WriteFieldBegin("ThrottleHost", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleHost); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *RuleDetail) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleDB() {
		if err = oprot.WriteFieldBegin("ThrottleDB", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleDB); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *RuleDetail) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleSqlText() {
		if err = oprot.WriteFieldBegin("ThrottleSqlText", thrift.STRING, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleSqlText); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *RuleDetail) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleFingerPrint() {
		if err = oprot.WriteFieldBegin("ThrottleFingerPrint", thrift.STRING, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleFingerPrint); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *RuleDetail) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleThreshold() {
		if err = oprot.WriteFieldBegin("ThrottleThreshold", thrift.I32, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ThrottleThreshold); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *RuleDetail) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetRejectCount() {
		if err = oprot.WriteFieldBegin("RejectCount", thrift.I64, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.RejectCount); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *RuleDetail) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleTarget() {
		if err = oprot.WriteFieldBegin("ThrottleTarget", thrift.STRING, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleTarget); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *RuleDetail) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleMode() {
		if err = oprot.WriteFieldBegin("ThrottleMode", thrift.STRING, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ThrottleMode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *RuleDetail) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetDesc() {
		if err = oprot.WriteFieldBegin("Desc", thrift.STRING, 21); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Desc); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}

func (p *RuleDetail) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetTerminationType() {
		if err = oprot.WriteFieldBegin("TerminationType", thrift.STRING, 22); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TerminationType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}

func (p *RuleDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RuleDetail(%+v)", *p)

}

func (p *RuleDetail) DeepEqual(ano *RuleDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SqlType) {
		return false
	}
	if !p.Field3DeepEqual(ano.RuleState) {
		return false
	}
	if !p.Field4DeepEqual(ano.KeyWord) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field6DeepEqual(ano.RemainingTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.ConcurrencyCount) {
		return false
	}
	if !p.Field9DeepEqual(ano.EffectiveTime) {
		return false
	}
	if !p.Field10DeepEqual(ano.EndpointType) {
		return false
	}
	if !p.Field11DeepEqual(ano.EndpointId) {
		return false
	}
	if !p.Field12DeepEqual(ano.ThrottleType) {
		return false
	}
	if !p.Field13DeepEqual(ano.ThrottleHost) {
		return false
	}
	if !p.Field14DeepEqual(ano.ThrottleDB) {
		return false
	}
	if !p.Field15DeepEqual(ano.ThrottleSqlText) {
		return false
	}
	if !p.Field16DeepEqual(ano.ThrottleFingerPrint) {
		return false
	}
	if !p.Field17DeepEqual(ano.ThrottleThreshold) {
		return false
	}
	if !p.Field18DeepEqual(ano.RejectCount) {
		return false
	}
	if !p.Field19DeepEqual(ano.ThrottleTarget) {
		return false
	}
	if !p.Field20DeepEqual(ano.ThrottleMode) {
		return false
	}
	if !p.Field21DeepEqual(ano.Desc) {
		return false
	}
	if !p.Field22DeepEqual(ano.TerminationType) {
		return false
	}
	return true
}

func (p *RuleDetail) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field2DeepEqual(src SqlType) bool {

	if p.SqlType != src {
		return false
	}
	return true
}
func (p *RuleDetail) Field3DeepEqual(src RuleState) bool {

	if p.RuleState != src {
		return false
	}
	return true
}
func (p *RuleDetail) Field4DeepEqual(src string) bool {

	if strings.Compare(p.KeyWord, src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field5DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field6DeepEqual(src int32) bool {

	if p.RemainingTime != src {
		return false
	}
	return true
}
func (p *RuleDetail) Field7DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field8DeepEqual(src int32) bool {

	if p.ConcurrencyCount != src {
		return false
	}
	return true
}
func (p *RuleDetail) Field9DeepEqual(src int32) bool {

	if p.EffectiveTime != src {
		return false
	}
	return true
}
func (p *RuleDetail) Field10DeepEqual(src *string) bool {

	if p.EndpointType == src {
		return true
	} else if p.EndpointType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndpointType, *src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field11DeepEqual(src *string) bool {

	if p.EndpointId == src {
		return true
	} else if p.EndpointId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.EndpointId, *src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field12DeepEqual(src *string) bool {

	if p.ThrottleType == src {
		return true
	} else if p.ThrottleType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleType, *src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field13DeepEqual(src *string) bool {

	if p.ThrottleHost == src {
		return true
	} else if p.ThrottleHost == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleHost, *src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field14DeepEqual(src *string) bool {

	if p.ThrottleDB == src {
		return true
	} else if p.ThrottleDB == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleDB, *src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field15DeepEqual(src *string) bool {

	if p.ThrottleSqlText == src {
		return true
	} else if p.ThrottleSqlText == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleSqlText, *src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field16DeepEqual(src *string) bool {

	if p.ThrottleFingerPrint == src {
		return true
	} else if p.ThrottleFingerPrint == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleFingerPrint, *src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field17DeepEqual(src *int32) bool {

	if p.ThrottleThreshold == src {
		return true
	} else if p.ThrottleThreshold == nil || src == nil {
		return false
	}
	if *p.ThrottleThreshold != *src {
		return false
	}
	return true
}
func (p *RuleDetail) Field18DeepEqual(src *int64) bool {

	if p.RejectCount == src {
		return true
	} else if p.RejectCount == nil || src == nil {
		return false
	}
	if *p.RejectCount != *src {
		return false
	}
	return true
}
func (p *RuleDetail) Field19DeepEqual(src *string) bool {

	if p.ThrottleTarget == src {
		return true
	} else if p.ThrottleTarget == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleTarget, *src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field20DeepEqual(src *string) bool {

	if p.ThrottleMode == src {
		return true
	} else if p.ThrottleMode == nil || src == nil {
		return false
	}
	if strings.Compare(*p.ThrottleMode, *src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field21DeepEqual(src *string) bool {

	if p.Desc == src {
		return true
	} else if p.Desc == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Desc, *src) != 0 {
		return false
	}
	return true
}
func (p *RuleDetail) Field22DeepEqual(src *string) bool {

	if p.TerminationType == src {
		return true
	} else if p.TerminationType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TerminationType, *src) != 0 {
		return false
	}
	return true
}

type DescribeSqlConcurrencyControlRulesResp struct {
	RuleDetails []*RuleDetail `thrift:"RuleDetails,1,required" frugal:"1,required,list<RuleDetail>" json:"RuleDetails"`
	Total       int32         `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeSqlConcurrencyControlRulesResp() *DescribeSqlConcurrencyControlRulesResp {
	return &DescribeSqlConcurrencyControlRulesResp{}
}

func (p *DescribeSqlConcurrencyControlRulesResp) InitDefault() {
}

func (p *DescribeSqlConcurrencyControlRulesResp) GetRuleDetails() (v []*RuleDetail) {
	return p.RuleDetails
}

func (p *DescribeSqlConcurrencyControlRulesResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeSqlConcurrencyControlRulesResp) SetRuleDetails(val []*RuleDetail) {
	p.RuleDetails = val
}
func (p *DescribeSqlConcurrencyControlRulesResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeSqlConcurrencyControlRulesResp = map[int16]string{
	1: "RuleDetails",
	2: "Total",
}

func (p *DescribeSqlConcurrencyControlRulesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlConcurrencyControlRulesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRuleDetails bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRuleDetails = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRuleDetails {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlConcurrencyControlRulesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlConcurrencyControlRulesResp[fieldId]))
}

func (p *DescribeSqlConcurrencyControlRulesResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*RuleDetail, 0, size)
	values := make([]RuleDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.RuleDetails = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRulesResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeSqlConcurrencyControlRulesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlConcurrencyControlRulesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlConcurrencyControlRulesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RuleDetails", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RuleDetails)); err != nil {
		return err
	}
	for _, v := range p.RuleDetails {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRulesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlConcurrencyControlRulesResp(%+v)", *p)

}

func (p *DescribeSqlConcurrencyControlRulesResp) DeepEqual(ano *DescribeSqlConcurrencyControlRulesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RuleDetails) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeSqlConcurrencyControlRulesResp) Field1DeepEqual(src []*RuleDetail) bool {

	if len(p.RuleDetails) != len(src) {
		return false
	}
	for i, v := range p.RuleDetails {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRulesResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type DescribeSqlKeywordsReq struct {
	Sql          *string   `thrift:"Sql,1,optional" frugal:"1,optional,string" json:"Sql,omitempty"`
	InstanceType *DSType   `thrift:"InstanceType,2,optional" frugal:"2,optional,DSType" json:"InstanceType,omitempty"`
	LinkType     *LinkType `thrift:"LinkType,3,optional" frugal:"3,optional,LinkType" json:"LinkType,omitempty"`
}

func NewDescribeSqlKeywordsReq() *DescribeSqlKeywordsReq {
	return &DescribeSqlKeywordsReq{}
}

func (p *DescribeSqlKeywordsReq) InitDefault() {
}

var DescribeSqlKeywordsReq_Sql_DEFAULT string

func (p *DescribeSqlKeywordsReq) GetSql() (v string) {
	if !p.IsSetSql() {
		return DescribeSqlKeywordsReq_Sql_DEFAULT
	}
	return *p.Sql
}

var DescribeSqlKeywordsReq_InstanceType_DEFAULT DSType

func (p *DescribeSqlKeywordsReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeSqlKeywordsReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeSqlKeywordsReq_LinkType_DEFAULT LinkType

func (p *DescribeSqlKeywordsReq) GetLinkType() (v LinkType) {
	if !p.IsSetLinkType() {
		return DescribeSqlKeywordsReq_LinkType_DEFAULT
	}
	return *p.LinkType
}
func (p *DescribeSqlKeywordsReq) SetSql(val *string) {
	p.Sql = val
}
func (p *DescribeSqlKeywordsReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DescribeSqlKeywordsReq) SetLinkType(val *LinkType) {
	p.LinkType = val
}

var fieldIDToName_DescribeSqlKeywordsReq = map[int16]string{
	1: "Sql",
	2: "InstanceType",
	3: "LinkType",
}

func (p *DescribeSqlKeywordsReq) IsSetSql() bool {
	return p.Sql != nil
}

func (p *DescribeSqlKeywordsReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeSqlKeywordsReq) IsSetLinkType() bool {
	return p.LinkType != nil
}

func (p *DescribeSqlKeywordsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlKeywordsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlKeywordsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeSqlKeywordsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Sql = _field
	return nil
}
func (p *DescribeSqlKeywordsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeSqlKeywordsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return nil
}

func (p *DescribeSqlKeywordsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlKeywordsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlKeywordsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlKeywordsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSql() {
		if err = oprot.WriteFieldBegin("Sql", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Sql); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlKeywordsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlKeywordsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinkType() {
		if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlKeywordsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlKeywordsReq(%+v)", *p)

}

func (p *DescribeSqlKeywordsReq) DeepEqual(ano *DescribeSqlKeywordsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Sql) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.LinkType) {
		return false
	}
	return true
}

func (p *DescribeSqlKeywordsReq) Field1DeepEqual(src *string) bool {

	if p.Sql == src {
		return true
	} else if p.Sql == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Sql, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlKeywordsReq) Field2DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeSqlKeywordsReq) Field3DeepEqual(src *LinkType) bool {

	if p.LinkType == src {
		return true
	} else if p.LinkType == nil || src == nil {
		return false
	}
	if *p.LinkType != *src {
		return false
	}
	return true
}

type DescribeSqlKeywordsResp struct {
	KeyWords string `thrift:"KeyWords,1,required" frugal:"1,required,string" json:"KeyWords"`
}

func NewDescribeSqlKeywordsResp() *DescribeSqlKeywordsResp {
	return &DescribeSqlKeywordsResp{}
}

func (p *DescribeSqlKeywordsResp) InitDefault() {
}

func (p *DescribeSqlKeywordsResp) GetKeyWords() (v string) {
	return p.KeyWords
}
func (p *DescribeSqlKeywordsResp) SetKeyWords(val string) {
	p.KeyWords = val
}

var fieldIDToName_DescribeSqlKeywordsResp = map[int16]string{
	1: "KeyWords",
}

func (p *DescribeSqlKeywordsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlKeywordsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetKeyWords bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetKeyWords = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetKeyWords {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlKeywordsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlKeywordsResp[fieldId]))
}

func (p *DescribeSqlKeywordsResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.KeyWords = _field
	return nil
}

func (p *DescribeSqlKeywordsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlKeywordsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlKeywordsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlKeywordsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("KeyWords", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.KeyWords); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlKeywordsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlKeywordsResp(%+v)", *p)

}

func (p *DescribeSqlKeywordsResp) DeepEqual(ano *DescribeSqlKeywordsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.KeyWords) {
		return false
	}
	return true
}

func (p *DescribeSqlKeywordsResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.KeyWords, src) != 0 {
		return false
	}
	return true
}

type DescribeSqlFingerPrintReq struct {
	Sql          string    `thrift:"Sql,1,required" frugal:"1,required,string" json:"Sql"`
	InstanceType *DSType   `thrift:"InstanceType,2,optional" frugal:"2,optional,DSType" json:"InstanceType,omitempty"`
	LinkType     *LinkType `thrift:"LinkType,3,optional" frugal:"3,optional,LinkType" json:"LinkType,omitempty"`
}

func NewDescribeSqlFingerPrintReq() *DescribeSqlFingerPrintReq {
	return &DescribeSqlFingerPrintReq{}
}

func (p *DescribeSqlFingerPrintReq) InitDefault() {
}

func (p *DescribeSqlFingerPrintReq) GetSql() (v string) {
	return p.Sql
}

var DescribeSqlFingerPrintReq_InstanceType_DEFAULT DSType

func (p *DescribeSqlFingerPrintReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeSqlFingerPrintReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var DescribeSqlFingerPrintReq_LinkType_DEFAULT LinkType

func (p *DescribeSqlFingerPrintReq) GetLinkType() (v LinkType) {
	if !p.IsSetLinkType() {
		return DescribeSqlFingerPrintReq_LinkType_DEFAULT
	}
	return *p.LinkType
}
func (p *DescribeSqlFingerPrintReq) SetSql(val string) {
	p.Sql = val
}
func (p *DescribeSqlFingerPrintReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *DescribeSqlFingerPrintReq) SetLinkType(val *LinkType) {
	p.LinkType = val
}

var fieldIDToName_DescribeSqlFingerPrintReq = map[int16]string{
	1: "Sql",
	2: "InstanceType",
	3: "LinkType",
}

func (p *DescribeSqlFingerPrintReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeSqlFingerPrintReq) IsSetLinkType() bool {
	return p.LinkType != nil
}

func (p *DescribeSqlFingerPrintReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlFingerPrintReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSql bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSql = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSql {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlFingerPrintReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlFingerPrintReq[fieldId]))
}

func (p *DescribeSqlFingerPrintReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Sql = _field
	return nil
}
func (p *DescribeSqlFingerPrintReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeSqlFingerPrintReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return nil
}

func (p *DescribeSqlFingerPrintReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlFingerPrintReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlFingerPrintReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlFingerPrintReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Sql", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Sql); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlFingerPrintReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlFingerPrintReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinkType() {
		if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlFingerPrintReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlFingerPrintReq(%+v)", *p)

}

func (p *DescribeSqlFingerPrintReq) DeepEqual(ano *DescribeSqlFingerPrintReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Sql) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.LinkType) {
		return false
	}
	return true
}

func (p *DescribeSqlFingerPrintReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Sql, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlFingerPrintReq) Field2DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *DescribeSqlFingerPrintReq) Field3DeepEqual(src *LinkType) bool {

	if p.LinkType == src {
		return true
	} else if p.LinkType == nil || src == nil {
		return false
	}
	if *p.LinkType != *src {
		return false
	}
	return true
}

type DescribeSqlFingerPrintResp struct {
	SqlFingerPrint string `thrift:"SqlFingerPrint,1,required" frugal:"1,required,string" json:"SqlFingerPrint"`
}

func NewDescribeSqlFingerPrintResp() *DescribeSqlFingerPrintResp {
	return &DescribeSqlFingerPrintResp{}
}

func (p *DescribeSqlFingerPrintResp) InitDefault() {
}

func (p *DescribeSqlFingerPrintResp) GetSqlFingerPrint() (v string) {
	return p.SqlFingerPrint
}
func (p *DescribeSqlFingerPrintResp) SetSqlFingerPrint(val string) {
	p.SqlFingerPrint = val
}

var fieldIDToName_DescribeSqlFingerPrintResp = map[int16]string{
	1: "SqlFingerPrint",
}

func (p *DescribeSqlFingerPrintResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlFingerPrintResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlFingerPrint bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlFingerPrint = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlFingerPrint {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlFingerPrintResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlFingerPrintResp[fieldId]))
}

func (p *DescribeSqlFingerPrintResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlFingerPrint = _field
	return nil
}

func (p *DescribeSqlFingerPrintResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlFingerPrintResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlFingerPrintResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlFingerPrintResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlFingerPrint", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlFingerPrint); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlFingerPrintResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlFingerPrintResp(%+v)", *p)

}

func (p *DescribeSqlFingerPrintResp) DeepEqual(ano *DescribeSqlFingerPrintResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlFingerPrint) {
		return false
	}
	return true
}

func (p *DescribeSqlFingerPrintResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SqlFingerPrint, src) != 0 {
		return false
	}
	return true
}

type DescribeSqlTypeReq struct {
	SqlText      string  `thrift:"SqlText,1,required" frugal:"1,required,string" json:"SqlText"`
	InstanceType *DSType `thrift:"InstanceType,2,optional" frugal:"2,optional,DSType" json:"InstanceType,omitempty"`
}

func NewDescribeSqlTypeReq() *DescribeSqlTypeReq {
	return &DescribeSqlTypeReq{}
}

func (p *DescribeSqlTypeReq) InitDefault() {
}

func (p *DescribeSqlTypeReq) GetSqlText() (v string) {
	return p.SqlText
}

var DescribeSqlTypeReq_InstanceType_DEFAULT DSType

func (p *DescribeSqlTypeReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeSqlTypeReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}
func (p *DescribeSqlTypeReq) SetSqlText(val string) {
	p.SqlText = val
}
func (p *DescribeSqlTypeReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}

var fieldIDToName_DescribeSqlTypeReq = map[int16]string{
	1: "SqlText",
	2: "InstanceType",
}

func (p *DescribeSqlTypeReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeSqlTypeReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTypeReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlText bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlText = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlText {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlTypeReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlTypeReq[fieldId]))
}

func (p *DescribeSqlTypeReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlText = _field
	return nil
}
func (p *DescribeSqlTypeReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}

func (p *DescribeSqlTypeReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTypeReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlTypeReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlTypeReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlText", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlText); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlTypeReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlTypeReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlTypeReq(%+v)", *p)

}

func (p *DescribeSqlTypeReq) DeepEqual(ano *DescribeSqlTypeReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlText) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DescribeSqlTypeReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SqlText, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlTypeReq) Field2DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}

type DescribeSqlTypeResp struct {
	SqlType string `thrift:"SqlType,1,required" frugal:"1,required,string" json:"SqlType"`
}

func NewDescribeSqlTypeResp() *DescribeSqlTypeResp {
	return &DescribeSqlTypeResp{}
}

func (p *DescribeSqlTypeResp) InitDefault() {
}

func (p *DescribeSqlTypeResp) GetSqlType() (v string) {
	return p.SqlType
}
func (p *DescribeSqlTypeResp) SetSqlType(val string) {
	p.SqlType = val
}

var fieldIDToName_DescribeSqlTypeResp = map[int16]string{
	1: "SqlType",
}

func (p *DescribeSqlTypeResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTypeResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSqlType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSqlType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlTypeResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlTypeResp[fieldId]))
}

func (p *DescribeSqlTypeResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlType = _field
	return nil
}

func (p *DescribeSqlTypeResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlTypeResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlTypeResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlTypeResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlType", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlType); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlTypeResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlTypeResp(%+v)", *p)

}

func (p *DescribeSqlTypeResp) DeepEqual(ano *DescribeSqlTypeResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlType) {
		return false
	}
	return true
}

func (p *DescribeSqlTypeResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SqlType, src) != 0 {
		return false
	}
	return true
}

type ModifySqlConcurrencyControlRuleReq struct {
	InstanceId   string        `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	TaskId       string        `thrift:"TaskId,2,required" frugal:"2,required,string" json:"TaskId"`
	InstanceType *DSType       `thrift:"InstanceType,3,optional" frugal:"3,optional,DSType" json:"InstanceType,omitempty"`
	LinkType     *LinkType     `thrift:"LinkType,4,optional" frugal:"4,optional,LinkType" json:"LinkType,omitempty"`
	Threshold    int32         `thrift:"Threshold,5,required" frugal:"5,required,i32" json:"Threshold"`
	ThrottleMode *ThrottleMode `thrift:"ThrottleMode,6,optional" frugal:"6,optional,ThrottleMode" json:"ThrottleMode,omitempty"`
	RegionId     *string       `thrift:"regionId,7,optional" frugal:"7,optional,string" json:"regionId,omitempty"`
}

func NewModifySqlConcurrencyControlRuleReq() *ModifySqlConcurrencyControlRuleReq {
	return &ModifySqlConcurrencyControlRuleReq{}
}

func (p *ModifySqlConcurrencyControlRuleReq) InitDefault() {
}

func (p *ModifySqlConcurrencyControlRuleReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *ModifySqlConcurrencyControlRuleReq) GetTaskId() (v string) {
	return p.TaskId
}

var ModifySqlConcurrencyControlRuleReq_InstanceType_DEFAULT DSType

func (p *ModifySqlConcurrencyControlRuleReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return ModifySqlConcurrencyControlRuleReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var ModifySqlConcurrencyControlRuleReq_LinkType_DEFAULT LinkType

func (p *ModifySqlConcurrencyControlRuleReq) GetLinkType() (v LinkType) {
	if !p.IsSetLinkType() {
		return ModifySqlConcurrencyControlRuleReq_LinkType_DEFAULT
	}
	return *p.LinkType
}

func (p *ModifySqlConcurrencyControlRuleReq) GetThreshold() (v int32) {
	return p.Threshold
}

var ModifySqlConcurrencyControlRuleReq_ThrottleMode_DEFAULT ThrottleMode

func (p *ModifySqlConcurrencyControlRuleReq) GetThrottleMode() (v ThrottleMode) {
	if !p.IsSetThrottleMode() {
		return ModifySqlConcurrencyControlRuleReq_ThrottleMode_DEFAULT
	}
	return *p.ThrottleMode
}

var ModifySqlConcurrencyControlRuleReq_RegionId_DEFAULT string

func (p *ModifySqlConcurrencyControlRuleReq) GetRegionId() (v string) {
	if !p.IsSetRegionId() {
		return ModifySqlConcurrencyControlRuleReq_RegionId_DEFAULT
	}
	return *p.RegionId
}
func (p *ModifySqlConcurrencyControlRuleReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *ModifySqlConcurrencyControlRuleReq) SetTaskId(val string) {
	p.TaskId = val
}
func (p *ModifySqlConcurrencyControlRuleReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}
func (p *ModifySqlConcurrencyControlRuleReq) SetLinkType(val *LinkType) {
	p.LinkType = val
}
func (p *ModifySqlConcurrencyControlRuleReq) SetThreshold(val int32) {
	p.Threshold = val
}
func (p *ModifySqlConcurrencyControlRuleReq) SetThrottleMode(val *ThrottleMode) {
	p.ThrottleMode = val
}
func (p *ModifySqlConcurrencyControlRuleReq) SetRegionId(val *string) {
	p.RegionId = val
}

var fieldIDToName_ModifySqlConcurrencyControlRuleReq = map[int16]string{
	1: "InstanceId",
	2: "TaskId",
	3: "InstanceType",
	4: "LinkType",
	5: "Threshold",
	6: "ThrottleMode",
	7: "regionId",
}

func (p *ModifySqlConcurrencyControlRuleReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *ModifySqlConcurrencyControlRuleReq) IsSetLinkType() bool {
	return p.LinkType != nil
}

func (p *ModifySqlConcurrencyControlRuleReq) IsSetThrottleMode() bool {
	return p.ThrottleMode != nil
}

func (p *ModifySqlConcurrencyControlRuleReq) IsSetRegionId() bool {
	return p.RegionId != nil
}

func (p *ModifySqlConcurrencyControlRuleReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifySqlConcurrencyControlRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetTaskId bool = false
	var issetThreshold bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetThreshold = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTaskId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetThreshold {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifySqlConcurrencyControlRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifySqlConcurrencyControlRuleReq[fieldId]))
}

func (p *ModifySqlConcurrencyControlRuleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifySqlConcurrencyControlRuleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *ModifySqlConcurrencyControlRuleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *ModifySqlConcurrencyControlRuleReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *LinkType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := LinkType(v)
		_field = &tmp
	}
	p.LinkType = _field
	return nil
}
func (p *ModifySqlConcurrencyControlRuleReq) ReadField5(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Threshold = _field
	return nil
}
func (p *ModifySqlConcurrencyControlRuleReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *ThrottleMode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ThrottleMode(v)
		_field = &tmp
	}
	p.ThrottleMode = _field
	return nil
}
func (p *ModifySqlConcurrencyControlRuleReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RegionId = _field
	return nil
}

func (p *ModifySqlConcurrencyControlRuleReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifySqlConcurrencyControlRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifySqlConcurrencyControlRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifySqlConcurrencyControlRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifySqlConcurrencyControlRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifySqlConcurrencyControlRuleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifySqlConcurrencyControlRuleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetLinkType() {
		if err = oprot.WriteFieldBegin("LinkType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.LinkType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifySqlConcurrencyControlRuleReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Threshold", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Threshold); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifySqlConcurrencyControlRuleReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetThrottleMode() {
		if err = oprot.WriteFieldBegin("ThrottleMode", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ThrottleMode)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ModifySqlConcurrencyControlRuleReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionId() {
		if err = oprot.WriteFieldBegin("regionId", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.RegionId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *ModifySqlConcurrencyControlRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifySqlConcurrencyControlRuleReq(%+v)", *p)

}

func (p *ModifySqlConcurrencyControlRuleReq) DeepEqual(ano *ModifySqlConcurrencyControlRuleReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field4DeepEqual(ano.LinkType) {
		return false
	}
	if !p.Field5DeepEqual(ano.Threshold) {
		return false
	}
	if !p.Field6DeepEqual(ano.ThrottleMode) {
		return false
	}
	if !p.Field7DeepEqual(ano.RegionId) {
		return false
	}
	return true
}

func (p *ModifySqlConcurrencyControlRuleReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifySqlConcurrencyControlRuleReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifySqlConcurrencyControlRuleReq) Field3DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *ModifySqlConcurrencyControlRuleReq) Field4DeepEqual(src *LinkType) bool {

	if p.LinkType == src {
		return true
	} else if p.LinkType == nil || src == nil {
		return false
	}
	if *p.LinkType != *src {
		return false
	}
	return true
}
func (p *ModifySqlConcurrencyControlRuleReq) Field5DeepEqual(src int32) bool {

	if p.Threshold != src {
		return false
	}
	return true
}
func (p *ModifySqlConcurrencyControlRuleReq) Field6DeepEqual(src *ThrottleMode) bool {

	if p.ThrottleMode == src {
		return true
	} else if p.ThrottleMode == nil || src == nil {
		return false
	}
	if *p.ThrottleMode != *src {
		return false
	}
	return true
}
func (p *ModifySqlConcurrencyControlRuleReq) Field7DeepEqual(src *string) bool {

	if p.RegionId == src {
		return true
	} else if p.RegionId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.RegionId, *src) != 0 {
		return false
	}
	return true
}

type ModifySqlConcurrencyControlRuleResp struct {
}

func NewModifySqlConcurrencyControlRuleResp() *ModifySqlConcurrencyControlRuleResp {
	return &ModifySqlConcurrencyControlRuleResp{}
}

func (p *ModifySqlConcurrencyControlRuleResp) InitDefault() {
}

var fieldIDToName_ModifySqlConcurrencyControlRuleResp = map[int16]string{}

func (p *ModifySqlConcurrencyControlRuleResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifySqlConcurrencyControlRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifySqlConcurrencyControlRuleResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifySqlConcurrencyControlRuleResp")

	if err = oprot.WriteStructBegin("ModifySqlConcurrencyControlRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifySqlConcurrencyControlRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifySqlConcurrencyControlRuleResp(%+v)", *p)

}

func (p *ModifySqlConcurrencyControlRuleResp) DeepEqual(ano *ModifySqlConcurrencyControlRuleResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeSqlConcurrencyControlRuleDetailReq struct {
	TaskId       string  `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
	InstanceId   *string `thrift:"InstanceId,2,optional" frugal:"2,optional,string" json:"InstanceId,omitempty"`
	InstanceType *DSType `thrift:"InstanceType,3,optional" frugal:"3,optional,DSType" json:"InstanceType,omitempty"`
}

func NewDescribeSqlConcurrencyControlRuleDetailReq() *DescribeSqlConcurrencyControlRuleDetailReq {
	return &DescribeSqlConcurrencyControlRuleDetailReq{}
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) InitDefault() {
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) GetTaskId() (v string) {
	return p.TaskId
}

var DescribeSqlConcurrencyControlRuleDetailReq_InstanceId_DEFAULT string

func (p *DescribeSqlConcurrencyControlRuleDetailReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeSqlConcurrencyControlRuleDetailReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeSqlConcurrencyControlRuleDetailReq_InstanceType_DEFAULT DSType

func (p *DescribeSqlConcurrencyControlRuleDetailReq) GetInstanceType() (v DSType) {
	if !p.IsSetInstanceType() {
		return DescribeSqlConcurrencyControlRuleDetailReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}
func (p *DescribeSqlConcurrencyControlRuleDetailReq) SetTaskId(val string) {
	p.TaskId = val
}
func (p *DescribeSqlConcurrencyControlRuleDetailReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeSqlConcurrencyControlRuleDetailReq) SetInstanceType(val *DSType) {
	p.InstanceType = val
}

var fieldIDToName_DescribeSqlConcurrencyControlRuleDetailReq = map[int16]string{
	1: "TaskId",
	2: "InstanceId",
	3: "InstanceType",
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlConcurrencyControlRuleDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlConcurrencyControlRuleDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlConcurrencyControlRuleDetailReq[fieldId]))
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRuleDetailReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRuleDetailReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *DSType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := DSType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlConcurrencyControlRuleDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlConcurrencyControlRuleDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlConcurrencyControlRuleDetailReq(%+v)", *p)

}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) DeepEqual(ano *DescribeSqlConcurrencyControlRuleDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DescribeSqlConcurrencyControlRuleDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRuleDetailReq) Field2DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRuleDetailReq) Field3DeepEqual(src *DSType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}

type DescribeSqlConcurrencyControlRuleDetailResp struct {
	RejectCount int64  `thrift:"RejectCount,1,required" frugal:"1,required,i64" json:"RejectCount"`
	StartTime   string `thrift:"StartTime,2,required" frugal:"2,required,string" json:"StartTime"`
	EndTime     string `thrift:"EndTime,3,required" frugal:"3,required,string" json:"EndTime"`
	Duration    int32  `thrift:"Duration,4,required" frugal:"4,required,i32" json:"Duration"`
}

func NewDescribeSqlConcurrencyControlRuleDetailResp() *DescribeSqlConcurrencyControlRuleDetailResp {
	return &DescribeSqlConcurrencyControlRuleDetailResp{}
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) InitDefault() {
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) GetRejectCount() (v int64) {
	return p.RejectCount
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) GetStartTime() (v string) {
	return p.StartTime
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) GetEndTime() (v string) {
	return p.EndTime
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) GetDuration() (v int32) {
	return p.Duration
}
func (p *DescribeSqlConcurrencyControlRuleDetailResp) SetRejectCount(val int64) {
	p.RejectCount = val
}
func (p *DescribeSqlConcurrencyControlRuleDetailResp) SetStartTime(val string) {
	p.StartTime = val
}
func (p *DescribeSqlConcurrencyControlRuleDetailResp) SetEndTime(val string) {
	p.EndTime = val
}
func (p *DescribeSqlConcurrencyControlRuleDetailResp) SetDuration(val int32) {
	p.Duration = val
}

var fieldIDToName_DescribeSqlConcurrencyControlRuleDetailResp = map[int16]string{
	1: "RejectCount",
	2: "StartTime",
	3: "EndTime",
	4: "Duration",
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlConcurrencyControlRuleDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetRejectCount bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetDuration bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetRejectCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetDuration = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetRejectCount {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetStartTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetDuration {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlConcurrencyControlRuleDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlConcurrencyControlRuleDetailResp[fieldId]))
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RejectCount = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRuleDetailResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.StartTime = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRuleDetailResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *DescribeSqlConcurrencyControlRuleDetailResp) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Duration = _field
	return nil
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlConcurrencyControlRuleDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlConcurrencyControlRuleDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RejectCount", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.RejectCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("StartTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.StartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Duration", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Duration); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlConcurrencyControlRuleDetailResp(%+v)", *p)

}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) DeepEqual(ano *DescribeSqlConcurrencyControlRuleDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.RejectCount) {
		return false
	}
	if !p.Field2DeepEqual(ano.StartTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.Duration) {
		return false
	}
	return true
}

func (p *DescribeSqlConcurrencyControlRuleDetailResp) Field1DeepEqual(src int64) bool {

	if p.RejectCount != src {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRuleDetailResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.StartTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRuleDetailResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlConcurrencyControlRuleDetailResp) Field4DeepEqual(src int32) bool {

	if p.Duration != src {
		return false
	}
	return true
}
