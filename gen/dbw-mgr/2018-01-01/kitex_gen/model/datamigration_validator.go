// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *AdvancedOptions) IsValid() error {
	return nil
}
func (p *DBTaskInfo) IsValid() error {
	return nil
}
func (p *DbTaskSearchParam) IsValid() error {
	return nil
}
func (p *CreateDbImportTaskReq) IsValid() error {
	if p.InstanceType == nil {
		return fmt.Errorf("field InstanceType not_nil rule failed")
	}
	if len(p.DbName) > int(64) {
		return fmt.Errorf("field DbName max_len rule failed, current value: %d", len(p.DbName))
	}
	if len(p.SourceName) > int(128) {
		return fmt.Errorf("field SourceName max_len rule failed, current value: %d", len(p.SourceName))
	}
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	if p.UserName == nil {
		return fmt.Errorf("field UserName not_nil rule failed")
	}
	return nil
}
func (p *CreateDbImportTaskResp) IsValid() error {
	return nil
}
func (p *CreateDbExportTaskReq) IsValid() error {
	if p.InstanceType == nil {
		return fmt.Errorf("field InstanceType not_nil rule failed")
	}
	if len(p.DbName) > int(64) {
		return fmt.Errorf("field DbName max_len rule failed, current value: %d", len(p.DbName))
	}
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	if p.AdvancedOptions != nil {
		if err := p.AdvancedOptions.IsValid(); err != nil {
			return fmt.Errorf("field AdvancedOptions not valid, %w", err)
		}
	}
	if p.UserName == nil {
		return fmt.Errorf("field UserName not_nil rule failed")
	}
	return nil
}
func (p *CreateDbExportTaskResp) IsValid() error {
	return nil
}
func (p *MigrationTicketInfo) IsValid() error {
	return nil
}
func (p *DeleteDbTasksReq) IsValid() error {
	return nil
}
func (p *DeleteDbTasksResp) IsValid() error {
	return nil
}
func (p *DescribeDbExportDownloadUrlReq) IsValid() error {
	return nil
}
func (p *DescribeDbExportDownloadUrlResp) IsValid() error {
	return nil
}
func (p *DescribeDbMigrationTasksReq) IsValid() error {
	if p.SearchParam != nil {
		if err := p.SearchParam.IsValid(); err != nil {
			return fmt.Errorf("field SearchParam not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeDbMigrationTasksResp) IsValid() error {
	return nil
}
func (p *DescribeDbMigrationTaskDetailReq) IsValid() error {
	return nil
}
func (p *ProgressDetail) IsValid() error {
	return nil
}
func (p *DescribeDbMigrationTaskDetailResp) IsValid() error {
	return nil
}
func (p *DescribeTempCredentialsReq) IsValid() error {
	if p.InstanceType == nil {
		return fmt.Errorf("field InstanceType not_nil rule failed")
	}
	return nil
}
func (p *DescribeTempCredentialsResp) IsValid() error {
	return nil
}
func (p *MigrationTicketFlowActionReq) IsValid() error {
	return nil
}
func (p *MigrationTicketFlowActionResp) IsValid() error {
	return nil
}
func (p *CreateMigrationTicketReq) IsValid() error {
	if len(p.InstanceId) > int(64) {
		return fmt.Errorf("field InstanceId max_len rule failed, current value: %d", len(p.InstanceId))
	}
	if p.DBExportParam != nil {
		if err := p.DBExportParam.IsValid(); err != nil {
			return fmt.Errorf("field DBExportParam not valid, %w", err)
		}
	}
	if p.SqlResultExportParam != nil {
		if err := p.SqlResultExportParam.IsValid(); err != nil {
			return fmt.Errorf("field SqlResultExportParam not valid, %w", err)
		}
	}
	if p.ImportParam != nil {
		if err := p.ImportParam.IsValid(); err != nil {
			return fmt.Errorf("field ImportParam not valid, %w", err)
		}
	}
	return nil
}
func (p *DBExportParam) IsValid() error {
	if p.AdvancedOptions != nil {
		if err := p.AdvancedOptions.IsValid(); err != nil {
			return fmt.Errorf("field AdvancedOptions not valid, %w", err)
		}
	}
	return nil
}
func (p *SqlResultExportParam) IsValid() error {
	return nil
}
func (p *ImportParam) IsValid() error {
	if len(p.SourceName) > int(128) {
		return fmt.Errorf("field SourceName max_len rule failed, current value: %d", len(p.SourceName))
	}
	return nil
}
func (p *MigrationTicketConfig) IsValid() error {
	if p.DBExportParam != nil {
		if err := p.DBExportParam.IsValid(); err != nil {
			return fmt.Errorf("field DBExportParam not valid, %w", err)
		}
	}
	if p.SqlResultExportParam != nil {
		if err := p.SqlResultExportParam.IsValid(); err != nil {
			return fmt.Errorf("field SqlResultExportParam not valid, %w", err)
		}
	}
	if p.ImportParam != nil {
		if err := p.ImportParam.IsValid(); err != nil {
			return fmt.Errorf("field ImportParam not valid, %w", err)
		}
	}
	return nil
}
func (p *CreateMigrationTicketResp) IsValid() error {
	return nil
}
func (p *PreCheckMigrationTicketReq) IsValid() error {
	return nil
}
func (p *PreCheckMigrationTicketResp) IsValid() error {
	return nil
}
func (p *ExecuteMigrationTicketReq) IsValid() error {
	return nil
}
func (p *ExecuteMigrationTicketResp) IsValid() error {
	return nil
}
func (p *SubmitMigrationTicketReq) IsValid() error {
	return nil
}
func (p *SubmitMigrationTicketResp) IsValid() error {
	return nil
}
func (p *DescribeMigrationTicketsReq) IsValid() error {
	if p.SearchParam != nil {
		if err := p.SearchParam.IsValid(); err != nil {
			return fmt.Errorf("field SearchParam not valid, %w", err)
		}
	}
	if p.PageNumber != nil {
		if *p.PageNumber <= int32(0) {
			return fmt.Errorf("field PageNumber gt rule failed, current value: %v", *p.PageNumber)
		}
	}
	if p.PageSize != nil {
		if *p.PageSize <= int32(0) {
			return fmt.Errorf("field PageSize gt rule failed, current value: %v", *p.PageSize)
		}
	}
	return nil
}
func (p *DescribeMigrationTicketsResp) IsValid() error {
	return nil
}
func (p *DescribeMigrationTicketDetailReq) IsValid() error {
	return nil
}
func (p *DescribeMigrationTicketDetailResp) IsValid() error {
	if p.CreateUser != nil {
		if err := p.CreateUser.IsValid(); err != nil {
			return fmt.Errorf("field CreateUser not valid, %w", err)
		}
	}
	if p.CurrentUser != nil {
		if err := p.CurrentUser.IsValid(); err != nil {
			return fmt.Errorf("field CurrentUser not valid, %w", err)
		}
	}
	if p.MigrationTicketConfig != nil {
		if err := p.MigrationTicketConfig.IsValid(); err != nil {
			return fmt.Errorf("field MigrationTicketConfig not valid, %w", err)
		}
	}
	return nil
}
func (p *DescribeMigrationPreCheckDetailReq) IsValid() error {
	return nil
}
func (p *DescribeMigrationPreCheckDetailResp) IsValid() error {
	return nil
}
func (p *MigrationPrecheckDetail) IsValid() error {
	return nil
}
func (p *CancelMigrationTicketReq) IsValid() error {
	return nil
}
func (p *CancelMigrationTicketResp) IsValid() error {
	return nil
}
func (p *StopMigrationTicketReq) IsValid() error {
	return nil
}
func (p *StopMigrationTicketResp) IsValid() error {
	return nil
}
