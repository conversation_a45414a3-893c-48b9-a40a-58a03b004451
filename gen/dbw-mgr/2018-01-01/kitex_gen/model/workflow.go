// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type ChangeTableStrategy int64

const (
	ChangeTableStrategy_DELETE    ChangeTableStrategy = 0
	ChangeTableStrategy_DoNothing ChangeTableStrategy = 1
)

func (p ChangeTableStrategy) String() string {
	switch p {
	case ChangeTableStrategy_DELETE:
		return "DELETE"
	case ChangeTableStrategy_DoNothing:
		return "DoNothing"
	}
	return "<UNSET>"
}

func ChangeTableStrategyFromString(s string) (ChangeTableStrategy, error) {
	switch s {
	case "DELETE":
		return ChangeTableStrategy_DELETE, nil
	case "DoNothing":
		return ChangeTableStrategy_DoNothing, nil
	}
	return ChangeTableStrategy(0), fmt.Errorf("not a valid ChangeTableStrategy string")
}

func ChangeTableStrategyPtr(v ChangeTableStrategy) *ChangeTableStrategy { return &v }

func (p ChangeTableStrategy) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ChangeTableStrategy) UnmarshalText(text []byte) error {
	q, err := ChangeTableStrategyFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type IncrementalDataVerification int64

const (
	IncrementalDataVerification_ALL    IncrementalDataVerification = 0
	IncrementalDataVerification_Part50 IncrementalDataVerification = 1
	IncrementalDataVerification_No     IncrementalDataVerification = 2
)

func (p IncrementalDataVerification) String() string {
	switch p {
	case IncrementalDataVerification_ALL:
		return "ALL"
	case IncrementalDataVerification_Part50:
		return "Part50"
	case IncrementalDataVerification_No:
		return "No"
	}
	return "<UNSET>"
}

func IncrementalDataVerificationFromString(s string) (IncrementalDataVerification, error) {
	switch s {
	case "ALL":
		return IncrementalDataVerification_ALL, nil
	case "Part50":
		return IncrementalDataVerification_Part50, nil
	case "No":
		return IncrementalDataVerification_No, nil
	}
	return IncrementalDataVerification(0), fmt.Errorf("not a valid IncrementalDataVerification string")
}

func IncrementalDataVerificationPtr(v IncrementalDataVerification) *IncrementalDataVerification {
	return &v
}

func (p IncrementalDataVerification) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *IncrementalDataVerification) UnmarshalText(text []byte) error {
	q, err := IncrementalDataVerificationFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ExecuteTicketAction int64

const (
	ExecuteTicketAction_Started         ExecuteTicketAction = 0
	ExecuteTicketAction_ReceiveCommand  ExecuteTicketAction = 1
	ExecuteTicketAction_CreateSession   ExecuteTicketAction = 2
	ExecuteTicketAction_ExecuteCommand  ExecuteTicketAction = 3
	ExecuteTicketAction_ExecuteFailed   ExecuteTicketAction = 4
	ExecuteTicketAction_ExecuteFinished ExecuteTicketAction = 5
)

func (p ExecuteTicketAction) String() string {
	switch p {
	case ExecuteTicketAction_Started:
		return "Started"
	case ExecuteTicketAction_ReceiveCommand:
		return "ReceiveCommand"
	case ExecuteTicketAction_CreateSession:
		return "CreateSession"
	case ExecuteTicketAction_ExecuteCommand:
		return "ExecuteCommand"
	case ExecuteTicketAction_ExecuteFailed:
		return "ExecuteFailed"
	case ExecuteTicketAction_ExecuteFinished:
		return "ExecuteFinished"
	}
	return "<UNSET>"
}

func ExecuteTicketActionFromString(s string) (ExecuteTicketAction, error) {
	switch s {
	case "Started":
		return ExecuteTicketAction_Started, nil
	case "ReceiveCommand":
		return ExecuteTicketAction_ReceiveCommand, nil
	case "CreateSession":
		return ExecuteTicketAction_CreateSession, nil
	case "ExecuteCommand":
		return ExecuteTicketAction_ExecuteCommand, nil
	case "ExecuteFailed":
		return ExecuteTicketAction_ExecuteFailed, nil
	case "ExecuteFinished":
		return ExecuteTicketAction_ExecuteFinished, nil
	}
	return ExecuteTicketAction(0), fmt.Errorf("not a valid ExecuteTicketAction string")
}

func ExecuteTicketActionPtr(v ExecuteTicketAction) *ExecuteTicketAction { return &v }

func (p ExecuteTicketAction) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ExecuteTicketAction) UnmarshalText(text []byte) error {
	q, err := ExecuteTicketActionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ErrCode int64

const (
	ErrCode_Success ErrCode = 0
	ErrCode_Error   ErrCode = -1
)

func (p ErrCode) String() string {
	switch p {
	case ErrCode_Success:
		return "Success"
	case ErrCode_Error:
		return "Error"
	}
	return "<UNSET>"
}

func ErrCodeFromString(s string) (ErrCode, error) {
	switch s {
	case "Success":
		return ErrCode_Success, nil
	case "Error":
		return ErrCode_Error, nil
	}
	return ErrCode(0), fmt.Errorf("not a valid ErrCode string")
}

func ErrCodePtr(v ErrCode) *ErrCode { return &v }

func (p ErrCode) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ErrCode) UnmarshalText(text []byte) error {
	q, err := ErrCodeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type OrderByForTicket int64

const (
	OrderByForTicket_CreateTime OrderByForTicket = 0
	OrderByForTicket_UpdateTime OrderByForTicket = 1
)

func (p OrderByForTicket) String() string {
	switch p {
	case OrderByForTicket_CreateTime:
		return "CreateTime"
	case OrderByForTicket_UpdateTime:
		return "UpdateTime"
	}
	return "<UNSET>"
}

func OrderByForTicketFromString(s string) (OrderByForTicket, error) {
	switch s {
	case "CreateTime":
		return OrderByForTicket_CreateTime, nil
	case "UpdateTime":
		return OrderByForTicket_UpdateTime, nil
	}
	return OrderByForTicket(0), fmt.Errorf("not a valid OrderByForTicket string")
}

func OrderByForTicketPtr(v OrderByForTicket) *OrderByForTicket { return &v }

func (p OrderByForTicket) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *OrderByForTicket) UnmarshalText(text []byte) error {
	q, err := OrderByForTicketFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type TicketListType int64

const (
	TicketListType_CreatedByMe  TicketListType = 0
	TicketListType_ApprovedByMe TicketListType = 1
	TicketListType_All          TicketListType = 2
)

func (p TicketListType) String() string {
	switch p {
	case TicketListType_CreatedByMe:
		return "CreatedByMe"
	case TicketListType_ApprovedByMe:
		return "ApprovedByMe"
	case TicketListType_All:
		return "All"
	}
	return "<UNSET>"
}

func TicketListTypeFromString(s string) (TicketListType, error) {
	switch s {
	case "CreatedByMe":
		return TicketListType_CreatedByMe, nil
	case "ApprovedByMe":
		return TicketListType_ApprovedByMe, nil
	case "All":
		return TicketListType_All, nil
	}
	return TicketListType(0), fmt.Errorf("not a valid TicketListType string")
}

func TicketListTypePtr(v TicketListType) *TicketListType { return &v }

func (p TicketListType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TicketListType) UnmarshalText(text []byte) error {
	q, err := TicketListTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type CurrentUserRole int64

const (
	CurrentUserRole_InstanceManager CurrentUserRole = 0
	CurrentUserRole_InstanceNormal  CurrentUserRole = 1
	CurrentUserRole_InstanceOwner   CurrentUserRole = 2
	CurrentUserRole_InstanceDBA     CurrentUserRole = 3
)

func (p CurrentUserRole) String() string {
	switch p {
	case CurrentUserRole_InstanceManager:
		return "InstanceManager"
	case CurrentUserRole_InstanceNormal:
		return "InstanceNormal"
	case CurrentUserRole_InstanceOwner:
		return "InstanceOwner"
	case CurrentUserRole_InstanceDBA:
		return "InstanceDBA"
	}
	return "<UNSET>"
}

func CurrentUserRoleFromString(s string) (CurrentUserRole, error) {
	switch s {
	case "InstanceManager":
		return CurrentUserRole_InstanceManager, nil
	case "InstanceNormal":
		return CurrentUserRole_InstanceNormal, nil
	case "InstanceOwner":
		return CurrentUserRole_InstanceOwner, nil
	case "InstanceDBA":
		return CurrentUserRole_InstanceDBA, nil
	}
	return CurrentUserRole(0), fmt.Errorf("not a valid CurrentUserRole string")
}

func CurrentUserRolePtr(v CurrentUserRole) *CurrentUserRole { return &v }

func (p CurrentUserRole) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *CurrentUserRole) UnmarshalText(text []byte) error {
	q, err := CurrentUserRoleFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type TicketStatus int64

const (
	TicketStatus_TicketUndo          TicketStatus = 0
	TicketStatus_TicketPreCheck      TicketStatus = 1
	TicketStatus_TicketPreCheckError TicketStatus = 2
	TicketStatus_TicketExamine       TicketStatus = 3
	TicketStatus_TicketCancel        TicketStatus = 4
	TicketStatus_TicketReject        TicketStatus = 5
	TicketStatus_TicketWaitExecute   TicketStatus = 6
	TicketStatus_TicketExecute       TicketStatus = 7
	TicketStatus_TicketFinished      TicketStatus = 8
	TicketStatus_TicketError         TicketStatus = 9
	TicketStatus_TicketTermination   TicketStatus = 10
)

func (p TicketStatus) String() string {
	switch p {
	case TicketStatus_TicketUndo:
		return "TicketUndo"
	case TicketStatus_TicketPreCheck:
		return "TicketPreCheck"
	case TicketStatus_TicketPreCheckError:
		return "TicketPreCheckError"
	case TicketStatus_TicketExamine:
		return "TicketExamine"
	case TicketStatus_TicketCancel:
		return "TicketCancel"
	case TicketStatus_TicketReject:
		return "TicketReject"
	case TicketStatus_TicketWaitExecute:
		return "TicketWaitExecute"
	case TicketStatus_TicketExecute:
		return "TicketExecute"
	case TicketStatus_TicketFinished:
		return "TicketFinished"
	case TicketStatus_TicketError:
		return "TicketError"
	case TicketStatus_TicketTermination:
		return "TicketTermination"
	}
	return "<UNSET>"
}

func TicketStatusFromString(s string) (TicketStatus, error) {
	switch s {
	case "TicketUndo":
		return TicketStatus_TicketUndo, nil
	case "TicketPreCheck":
		return TicketStatus_TicketPreCheck, nil
	case "TicketPreCheckError":
		return TicketStatus_TicketPreCheckError, nil
	case "TicketExamine":
		return TicketStatus_TicketExamine, nil
	case "TicketCancel":
		return TicketStatus_TicketCancel, nil
	case "TicketReject":
		return TicketStatus_TicketReject, nil
	case "TicketWaitExecute":
		return TicketStatus_TicketWaitExecute, nil
	case "TicketExecute":
		return TicketStatus_TicketExecute, nil
	case "TicketFinished":
		return TicketStatus_TicketFinished, nil
	case "TicketError":
		return TicketStatus_TicketError, nil
	case "TicketTermination":
		return TicketStatus_TicketTermination, nil
	}
	return TicketStatus(0), fmt.Errorf("not a valid TicketStatus string")
}

func TicketStatusPtr(v TicketStatus) *TicketStatus { return &v }

func (p TicketStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TicketStatus) UnmarshalText(text []byte) error {
	q, err := TicketStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type PreCheckStatus int64

const (
	PreCheckStatus_Undo  PreCheckStatus = 0
	PreCheckStatus_Pass  PreCheckStatus = 1
	PreCheckStatus_Error PreCheckStatus = 2
)

func (p PreCheckStatus) String() string {
	switch p {
	case PreCheckStatus_Undo:
		return "Undo"
	case PreCheckStatus_Pass:
		return "Pass"
	case PreCheckStatus_Error:
		return "Error"
	}
	return "<UNSET>"
}

func PreCheckStatusFromString(s string) (PreCheckStatus, error) {
	switch s {
	case "Undo":
		return PreCheckStatus_Undo, nil
	case "Pass":
		return PreCheckStatus_Pass, nil
	case "Error":
		return PreCheckStatus_Error, nil
	}
	return PreCheckStatus(0), fmt.Errorf("not a valid PreCheckStatus string")
}

func PreCheckStatusPtr(v PreCheckStatus) *PreCheckStatus { return &v }

func (p PreCheckStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *PreCheckStatus) UnmarshalText(text []byte) error {
	q, err := PreCheckStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ItemType int64

const (
	ItemType_PreCheckSyntax       ItemType = 0
	ItemType_PreCheckPermission   ItemType = 1
	ItemType_PreCheckExplain      ItemType = 2
	ItemType_PreCheckSecurityRule ItemType = 3
)

func (p ItemType) String() string {
	switch p {
	case ItemType_PreCheckSyntax:
		return "PreCheckSyntax"
	case ItemType_PreCheckPermission:
		return "PreCheckPermission"
	case ItemType_PreCheckExplain:
		return "PreCheckExplain"
	case ItemType_PreCheckSecurityRule:
		return "PreCheckSecurityRule"
	}
	return "<UNSET>"
}

func ItemTypeFromString(s string) (ItemType, error) {
	switch s {
	case "PreCheckSyntax":
		return ItemType_PreCheckSyntax, nil
	case "PreCheckPermission":
		return ItemType_PreCheckPermission, nil
	case "PreCheckExplain":
		return ItemType_PreCheckExplain, nil
	case "PreCheckSecurityRule":
		return ItemType_PreCheckSecurityRule, nil
	}
	return ItemType(0), fmt.Errorf("not a valid ItemType string")
}

func ItemTypePtr(v ItemType) *ItemType { return &v }

func (p ItemType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ItemType) UnmarshalText(text []byte) error {
	q, err := ItemTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ExecuteType int64

const (
	ExecuteType_Auto   ExecuteType = 0
	ExecuteType_Manual ExecuteType = 1
	ExecuteType_Cron   ExecuteType = 2
)

func (p ExecuteType) String() string {
	switch p {
	case ExecuteType_Auto:
		return "Auto"
	case ExecuteType_Manual:
		return "Manual"
	case ExecuteType_Cron:
		return "Cron"
	}
	return "<UNSET>"
}

func ExecuteTypeFromString(s string) (ExecuteType, error) {
	switch s {
	case "Auto":
		return ExecuteType_Auto, nil
	case "Manual":
		return ExecuteType_Manual, nil
	case "Cron":
		return ExecuteType_Cron, nil
	}
	return ExecuteType(0), fmt.Errorf("not a valid ExecuteType string")
}

func ExecuteTypePtr(v ExecuteType) *ExecuteType { return &v }

func (p ExecuteType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ExecuteType) UnmarshalText(text []byte) error {
	q, err := ExecuteTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type TicketType int64

const (
	TicketType_NormalSqlChange              TicketType = 0
	TicketType_FreeLockStructChange         TicketType = 1
	TicketType_FreeLockSqlChange            TicketType = 2
	TicketType_DataMigrationImport          TicketType = 3
	TicketType_DataMigrationExportDB        TicketType = 4
	TicketType_DataMigrationExportSqlResult TicketType = 5
)

func (p TicketType) String() string {
	switch p {
	case TicketType_NormalSqlChange:
		return "NormalSqlChange"
	case TicketType_FreeLockStructChange:
		return "FreeLockStructChange"
	case TicketType_FreeLockSqlChange:
		return "FreeLockSqlChange"
	case TicketType_DataMigrationImport:
		return "DataMigrationImport"
	case TicketType_DataMigrationExportDB:
		return "DataMigrationExportDB"
	case TicketType_DataMigrationExportSqlResult:
		return "DataMigrationExportSqlResult"
	}
	return "<UNSET>"
}

func TicketTypeFromString(s string) (TicketType, error) {
	switch s {
	case "NormalSqlChange":
		return TicketType_NormalSqlChange, nil
	case "FreeLockStructChange":
		return TicketType_FreeLockStructChange, nil
	case "FreeLockSqlChange":
		return TicketType_FreeLockSqlChange, nil
	case "DataMigrationImport":
		return TicketType_DataMigrationImport, nil
	case "DataMigrationExportDB":
		return TicketType_DataMigrationExportDB, nil
	case "DataMigrationExportSqlResult":
		return TicketType_DataMigrationExportSqlResult, nil
	}
	return TicketType(0), fmt.Errorf("not a valid TicketType string")
}

func TicketTypePtr(v TicketType) *TicketType { return &v }

func (p TicketType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *TicketType) UnmarshalText(text []byte) error {
	q, err := TicketTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type AllTicketType int64

const (
	AllTicketType_NormalSqlChange      AllTicketType = 0
	AllTicketType_FreeLockStructChange AllTicketType = 1
	AllTicketType_FreeLockSqlChange    AllTicketType = 2
	AllTicketType_DataArchive          AllTicketType = 3
	AllTicketType_DataMigration        AllTicketType = 4
)

func (p AllTicketType) String() string {
	switch p {
	case AllTicketType_NormalSqlChange:
		return "NormalSqlChange"
	case AllTicketType_FreeLockStructChange:
		return "FreeLockStructChange"
	case AllTicketType_FreeLockSqlChange:
		return "FreeLockSqlChange"
	case AllTicketType_DataArchive:
		return "DataArchive"
	case AllTicketType_DataMigration:
		return "DataMigration"
	}
	return "<UNSET>"
}

func AllTicketTypeFromString(s string) (AllTicketType, error) {
	switch s {
	case "NormalSqlChange":
		return AllTicketType_NormalSqlChange, nil
	case "FreeLockStructChange":
		return AllTicketType_FreeLockStructChange, nil
	case "FreeLockSqlChange":
		return AllTicketType_FreeLockSqlChange, nil
	case "DataArchive":
		return AllTicketType_DataArchive, nil
	case "DataMigration":
		return AllTicketType_DataMigration, nil
	}
	return AllTicketType(0), fmt.Errorf("not a valid AllTicketType string")
}

func AllTicketTypePtr(v AllTicketType) *AllTicketType { return &v }

func (p AllTicketType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *AllTicketType) UnmarshalText(text []byte) error {
	q, err := AllTicketTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type FlowActionType int64

const (
	FlowActionType_Pass   FlowActionType = 0
	FlowActionType_Reject FlowActionType = 1
)

func (p FlowActionType) String() string {
	switch p {
	case FlowActionType_Pass:
		return "Pass"
	case FlowActionType_Reject:
		return "Reject"
	}
	return "<UNSET>"
}

func FlowActionTypeFromString(s string) (FlowActionType, error) {
	switch s {
	case "Pass":
		return FlowActionType_Pass, nil
	case "Reject":
		return FlowActionType_Reject, nil
	}
	return FlowActionType(0), fmt.Errorf("not a valid FlowActionType string")
}

func FlowActionTypePtr(v FlowActionType) *FlowActionType { return &v }

func (p FlowActionType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *FlowActionType) UnmarshalText(text []byte) error {
	q, err := FlowActionTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type ScenesType int64

const (
	ScenesType_Ticket ScenesType = 0
)

func (p ScenesType) String() string {
	switch p {
	case ScenesType_Ticket:
		return "Ticket"
	}
	return "<UNSET>"
}

func ScenesTypeFromString(s string) (ScenesType, error) {
	switch s {
	case "Ticket":
		return ScenesType_Ticket, nil
	}
	return ScenesType(0), fmt.Errorf("not a valid ScenesType string")
}

func ScenesTypePtr(v ScenesType) *ScenesType { return &v }

func (p ScenesType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *ScenesType) UnmarshalText(text []byte) error {
	q, err := ScenesTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type FlowNodeStatus int64

const (
	FlowNodeStatus_Undo     FlowNodeStatus = 0
	FlowNodeStatus_Approval FlowNodeStatus = 1
	FlowNodeStatus_Pass     FlowNodeStatus = 2
	FlowNodeStatus_Reject   FlowNodeStatus = 3
	FlowNodeStatus_Cancel   FlowNodeStatus = 4
)

func (p FlowNodeStatus) String() string {
	switch p {
	case FlowNodeStatus_Undo:
		return "Undo"
	case FlowNodeStatus_Approval:
		return "Approval"
	case FlowNodeStatus_Pass:
		return "Pass"
	case FlowNodeStatus_Reject:
		return "Reject"
	case FlowNodeStatus_Cancel:
		return "Cancel"
	}
	return "<UNSET>"
}

func FlowNodeStatusFromString(s string) (FlowNodeStatus, error) {
	switch s {
	case "Undo":
		return FlowNodeStatus_Undo, nil
	case "Approval":
		return FlowNodeStatus_Approval, nil
	case "Pass":
		return FlowNodeStatus_Pass, nil
	case "Reject":
		return FlowNodeStatus_Reject, nil
	case "Cancel":
		return FlowNodeStatus_Cancel, nil
	}
	return FlowNodeStatus(0), fmt.Errorf("not a valid FlowNodeStatus string")
}

func FlowNodeStatusPtr(v FlowNodeStatus) *FlowNodeStatus { return &v }

func (p FlowNodeStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *FlowNodeStatus) UnmarshalText(text []byte) error {
	q, err := FlowNodeStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type FreeLockDMLTicketAction int64

const (
	FreeLockDMLTicketAction_Started                FreeLockDMLTicketAction = 0
	FreeLockDMLTicketAction_ReceiveCommand         FreeLockDMLTicketAction = 1
	FreeLockDMLTicketAction_GetMinMaxBoundaryValue FreeLockDMLTicketAction = 2
	FreeLockDMLTicketAction_GetBatchBoundaryValue  FreeLockDMLTicketAction = 3
	FreeLockDMLTicketAction_CreateSession          FreeLockDMLTicketAction = 4
	FreeLockDMLTicketAction_ExecuteCommand         FreeLockDMLTicketAction = 5
	FreeLockDMLTicketAction_ExecuteFailed          FreeLockDMLTicketAction = 6
	FreeLockDMLTicketAction_ExecuteFinished        FreeLockDMLTicketAction = 7
)

func (p FreeLockDMLTicketAction) String() string {
	switch p {
	case FreeLockDMLTicketAction_Started:
		return "Started"
	case FreeLockDMLTicketAction_ReceiveCommand:
		return "ReceiveCommand"
	case FreeLockDMLTicketAction_GetMinMaxBoundaryValue:
		return "GetMinMaxBoundaryValue"
	case FreeLockDMLTicketAction_GetBatchBoundaryValue:
		return "GetBatchBoundaryValue"
	case FreeLockDMLTicketAction_CreateSession:
		return "CreateSession"
	case FreeLockDMLTicketAction_ExecuteCommand:
		return "ExecuteCommand"
	case FreeLockDMLTicketAction_ExecuteFailed:
		return "ExecuteFailed"
	case FreeLockDMLTicketAction_ExecuteFinished:
		return "ExecuteFinished"
	}
	return "<UNSET>"
}

func FreeLockDMLTicketActionFromString(s string) (FreeLockDMLTicketAction, error) {
	switch s {
	case "Started":
		return FreeLockDMLTicketAction_Started, nil
	case "ReceiveCommand":
		return FreeLockDMLTicketAction_ReceiveCommand, nil
	case "GetMinMaxBoundaryValue":
		return FreeLockDMLTicketAction_GetMinMaxBoundaryValue, nil
	case "GetBatchBoundaryValue":
		return FreeLockDMLTicketAction_GetBatchBoundaryValue, nil
	case "CreateSession":
		return FreeLockDMLTicketAction_CreateSession, nil
	case "ExecuteCommand":
		return FreeLockDMLTicketAction_ExecuteCommand, nil
	case "ExecuteFailed":
		return FreeLockDMLTicketAction_ExecuteFailed, nil
	case "ExecuteFinished":
		return FreeLockDMLTicketAction_ExecuteFinished, nil
	}
	return FreeLockDMLTicketAction(0), fmt.Errorf("not a valid FreeLockDMLTicketAction string")
}

func FreeLockDMLTicketActionPtr(v FreeLockDMLTicketAction) *FreeLockDMLTicketAction { return &v }

func (p FreeLockDMLTicketAction) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *FreeLockDMLTicketAction) UnmarshalText(text []byte) error {
	q, err := FreeLockDMLTicketActionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type VeDBDDLTicketAction int64

const (
	VeDBDDLTicketAction_Started         VeDBDDLTicketAction = 0
	VeDBDDLTicketAction_ReceiveCommand  VeDBDDLTicketAction = 1
	VeDBDDLTicketAction_CreateSession   VeDBDDLTicketAction = 2
	VeDBDDLTicketAction_ExecuteCommand  VeDBDDLTicketAction = 3
	VeDBDDLTicketAction_ExecuteDDL      VeDBDDLTicketAction = 4
	VeDBDDLTicketAction_ExecuteFailed   VeDBDDLTicketAction = 5
	VeDBDDLTicketAction_ExecuteFinished VeDBDDLTicketAction = 6
)

func (p VeDBDDLTicketAction) String() string {
	switch p {
	case VeDBDDLTicketAction_Started:
		return "Started"
	case VeDBDDLTicketAction_ReceiveCommand:
		return "ReceiveCommand"
	case VeDBDDLTicketAction_CreateSession:
		return "CreateSession"
	case VeDBDDLTicketAction_ExecuteCommand:
		return "ExecuteCommand"
	case VeDBDDLTicketAction_ExecuteDDL:
		return "ExecuteDDL"
	case VeDBDDLTicketAction_ExecuteFailed:
		return "ExecuteFailed"
	case VeDBDDLTicketAction_ExecuteFinished:
		return "ExecuteFinished"
	}
	return "<UNSET>"
}

func VeDBDDLTicketActionFromString(s string) (VeDBDDLTicketAction, error) {
	switch s {
	case "Started":
		return VeDBDDLTicketAction_Started, nil
	case "ReceiveCommand":
		return VeDBDDLTicketAction_ReceiveCommand, nil
	case "CreateSession":
		return VeDBDDLTicketAction_CreateSession, nil
	case "ExecuteCommand":
		return VeDBDDLTicketAction_ExecuteCommand, nil
	case "ExecuteDDL":
		return VeDBDDLTicketAction_ExecuteDDL, nil
	case "ExecuteFailed":
		return VeDBDDLTicketAction_ExecuteFailed, nil
	case "ExecuteFinished":
		return VeDBDDLTicketAction_ExecuteFinished, nil
	}
	return VeDBDDLTicketAction(0), fmt.Errorf("not a valid VeDBDDLTicketAction string")
}

func VeDBDDLTicketActionPtr(v VeDBDDLTicketAction) *VeDBDDLTicketAction { return &v }

func (p VeDBDDLTicketAction) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *VeDBDDLTicketAction) UnmarshalText(text []byte) error {
	q, err := VeDBDDLTicketActionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type OnlineDDLTicketAction int64

const (
	OnlineDDLTicketAction_Started         OnlineDDLTicketAction = 0
	OnlineDDLTicketAction_ReceiveCommand  OnlineDDLTicketAction = 1
	OnlineDDLTicketAction_ExecuteDDL      OnlineDDLTicketAction = 2
	OnlineDDLTicketAction_ExecuteFailed   OnlineDDLTicketAction = 3
	OnlineDDLTicketAction_ExecuteFinished OnlineDDLTicketAction = 4
)

func (p OnlineDDLTicketAction) String() string {
	switch p {
	case OnlineDDLTicketAction_Started:
		return "Started"
	case OnlineDDLTicketAction_ReceiveCommand:
		return "ReceiveCommand"
	case OnlineDDLTicketAction_ExecuteDDL:
		return "ExecuteDDL"
	case OnlineDDLTicketAction_ExecuteFailed:
		return "ExecuteFailed"
	case OnlineDDLTicketAction_ExecuteFinished:
		return "ExecuteFinished"
	}
	return "<UNSET>"
}

func OnlineDDLTicketActionFromString(s string) (OnlineDDLTicketAction, error) {
	switch s {
	case "Started":
		return OnlineDDLTicketAction_Started, nil
	case "ReceiveCommand":
		return OnlineDDLTicketAction_ReceiveCommand, nil
	case "ExecuteDDL":
		return OnlineDDLTicketAction_ExecuteDDL, nil
	case "ExecuteFailed":
		return OnlineDDLTicketAction_ExecuteFailed, nil
	case "ExecuteFinished":
		return OnlineDDLTicketAction_ExecuteFinished, nil
	}
	return OnlineDDLTicketAction(0), fmt.Errorf("not a valid OnlineDDLTicketAction string")
}

func OnlineDDLTicketActionPtr(v OnlineDDLTicketAction) *OnlineDDLTicketAction { return &v }

func (p OnlineDDLTicketAction) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *OnlineDDLTicketAction) UnmarshalText(text []byte) error {
	q, err := OnlineDDLTicketActionFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type PreCheckState int64

const (
	PreCheckState_Success PreCheckState = 0
	PreCheckState_Warn    PreCheckState = 1
	PreCheckState_Error   PreCheckState = 2
)

func (p PreCheckState) String() string {
	switch p {
	case PreCheckState_Success:
		return "Success"
	case PreCheckState_Warn:
		return "Warn"
	case PreCheckState_Error:
		return "Error"
	}
	return "<UNSET>"
}

func PreCheckStateFromString(s string) (PreCheckState, error) {
	switch s {
	case "Success":
		return PreCheckState_Success, nil
	case "Warn":
		return PreCheckState_Warn, nil
	case "Error":
		return PreCheckState_Error, nil
	}
	return PreCheckState(0), fmt.Errorf("not a valid PreCheckState string")
}

func PreCheckStatePtr(v PreCheckState) *PreCheckState { return &v }

func (p PreCheckState) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *PreCheckState) UnmarshalText(text []byte) error {
	q, err := PreCheckStateFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type PreCheckItem int64

const (
	PreCheckItem_Syntax                 PreCheckItem = 0
	PreCheckItem_Permission             PreCheckItem = 1
	PreCheckItem_Explain                PreCheckItem = 2
	PreCheckItem_SecurityRule           PreCheckItem = 3
	PreCheckItem_OnlineDDLDry           PreCheckItem = 4
	PreCheckItem_OnlineDDLOriginalTable PreCheckItem = 5
	PreCheckItem_OnlineDDLSpace         PreCheckItem = 8
	PreCheckItem_DtsTask                PreCheckItem = 6
	PreCheckItem_UniqueKey              PreCheckItem = 7
	PreCheckItem_FreeLockParam          PreCheckItem = 9
)

func (p PreCheckItem) String() string {
	switch p {
	case PreCheckItem_Syntax:
		return "Syntax"
	case PreCheckItem_Permission:
		return "Permission"
	case PreCheckItem_Explain:
		return "Explain"
	case PreCheckItem_SecurityRule:
		return "SecurityRule"
	case PreCheckItem_OnlineDDLDry:
		return "OnlineDDLDry"
	case PreCheckItem_OnlineDDLOriginalTable:
		return "OnlineDDLOriginalTable"
	case PreCheckItem_OnlineDDLSpace:
		return "OnlineDDLSpace"
	case PreCheckItem_DtsTask:
		return "DtsTask"
	case PreCheckItem_UniqueKey:
		return "UniqueKey"
	case PreCheckItem_FreeLockParam:
		return "FreeLockParam"
	}
	return "<UNSET>"
}

func PreCheckItemFromString(s string) (PreCheckItem, error) {
	switch s {
	case "Syntax":
		return PreCheckItem_Syntax, nil
	case "Permission":
		return PreCheckItem_Permission, nil
	case "Explain":
		return PreCheckItem_Explain, nil
	case "SecurityRule":
		return PreCheckItem_SecurityRule, nil
	case "OnlineDDLDry":
		return PreCheckItem_OnlineDDLDry, nil
	case "OnlineDDLOriginalTable":
		return PreCheckItem_OnlineDDLOriginalTable, nil
	case "OnlineDDLSpace":
		return PreCheckItem_OnlineDDLSpace, nil
	case "DtsTask":
		return PreCheckItem_DtsTask, nil
	case "UniqueKey":
		return PreCheckItem_UniqueKey, nil
	case "FreeLockParam":
		return PreCheckItem_FreeLockParam, nil
	}
	return PreCheckItem(0), fmt.Errorf("not a valid PreCheckItem string")
}

func PreCheckItemPtr(v PreCheckItem) *PreCheckItem { return &v }

func (p PreCheckItem) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *PreCheckItem) UnmarshalText(text []byte) error {
	q, err := PreCheckItemFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type CreateTicketReq struct {
	InstanceType      InstanceType       `thrift:"InstanceType,1,required" frugal:"1,required,InstanceType" json:"InstanceType"`
	InstanceId        string             `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
	SqlText           *string            `thrift:"SqlText,3,optional" frugal:"3,optional,string" json:"SqlText,omitempty"`
	TicketExecuteType *ExecuteType       `thrift:"TicketExecuteType,4,optional" frugal:"4,optional,ExecuteType" json:"TicketExecuteType,omitempty"`
	TicketType        TicketType         `thrift:"TicketType,5,required" frugal:"5,required,TicketType" json:"TicketType"`
	CreateUser        string             `thrift:"CreateUser,6,required" frugal:"6,required,string" json:"CreateUser"`
	DatabaseName      string             `thrift:"DatabaseName,7,required" frugal:"7,required,string" json:"DatabaseName"`
	ExecStartTime     *int32             `thrift:"ExecStartTime,8,optional" frugal:"8,optional,i32" json:"ExecStartTime,omitempty"`
	ExecEndTime       *int32             `thrift:"ExecEndTime,9,optional" frugal:"9,optional,i32" json:"ExecEndTime,omitempty"`
	BatchConfig       *BatchConfig       `thrift:"BatchConfig,10,optional" frugal:"10,optional,BatchConfig" json:"BatchConfig,omitempty"`
	ArchiveConfig     *DataArchiveConfig `thrift:"ArchiveConfig,11,optional" frugal:"11,optional,DataArchiveConfig" json:"ArchiveConfig,omitempty"`
	CreateFrom        *string            `thrift:"CreateFrom,13,optional" frugal:"13,optional,string" json:"CreateFrom,omitempty"`
	Memo              *string            `thrift:"Memo,14,optional" frugal:"14,optional,string" json:"Memo,omitempty"`
	Title             *string            `thrift:"Title,15,optional" frugal:"15,optional,string" json:"Title,omitempty"`
	CreateUserName    *string            `thrift:"CreateUserName,16,optional" frugal:"16,optional,string" json:"CreateUserName,omitempty"`
}

func NewCreateTicketReq() *CreateTicketReq {
	return &CreateTicketReq{}
}

func (p *CreateTicketReq) InitDefault() {
}

func (p *CreateTicketReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *CreateTicketReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var CreateTicketReq_SqlText_DEFAULT string

func (p *CreateTicketReq) GetSqlText() (v string) {
	if !p.IsSetSqlText() {
		return CreateTicketReq_SqlText_DEFAULT
	}
	return *p.SqlText
}

var CreateTicketReq_TicketExecuteType_DEFAULT ExecuteType

func (p *CreateTicketReq) GetTicketExecuteType() (v ExecuteType) {
	if !p.IsSetTicketExecuteType() {
		return CreateTicketReq_TicketExecuteType_DEFAULT
	}
	return *p.TicketExecuteType
}

func (p *CreateTicketReq) GetTicketType() (v TicketType) {
	return p.TicketType
}

func (p *CreateTicketReq) GetCreateUser() (v string) {
	return p.CreateUser
}

func (p *CreateTicketReq) GetDatabaseName() (v string) {
	return p.DatabaseName
}

var CreateTicketReq_ExecStartTime_DEFAULT int32

func (p *CreateTicketReq) GetExecStartTime() (v int32) {
	if !p.IsSetExecStartTime() {
		return CreateTicketReq_ExecStartTime_DEFAULT
	}
	return *p.ExecStartTime
}

var CreateTicketReq_ExecEndTime_DEFAULT int32

func (p *CreateTicketReq) GetExecEndTime() (v int32) {
	if !p.IsSetExecEndTime() {
		return CreateTicketReq_ExecEndTime_DEFAULT
	}
	return *p.ExecEndTime
}

var CreateTicketReq_BatchConfig_DEFAULT *BatchConfig

func (p *CreateTicketReq) GetBatchConfig() (v *BatchConfig) {
	if !p.IsSetBatchConfig() {
		return CreateTicketReq_BatchConfig_DEFAULT
	}
	return p.BatchConfig
}

var CreateTicketReq_ArchiveConfig_DEFAULT *DataArchiveConfig

func (p *CreateTicketReq) GetArchiveConfig() (v *DataArchiveConfig) {
	if !p.IsSetArchiveConfig() {
		return CreateTicketReq_ArchiveConfig_DEFAULT
	}
	return p.ArchiveConfig
}

var CreateTicketReq_CreateFrom_DEFAULT string

func (p *CreateTicketReq) GetCreateFrom() (v string) {
	if !p.IsSetCreateFrom() {
		return CreateTicketReq_CreateFrom_DEFAULT
	}
	return *p.CreateFrom
}

var CreateTicketReq_Memo_DEFAULT string

func (p *CreateTicketReq) GetMemo() (v string) {
	if !p.IsSetMemo() {
		return CreateTicketReq_Memo_DEFAULT
	}
	return *p.Memo
}

var CreateTicketReq_Title_DEFAULT string

func (p *CreateTicketReq) GetTitle() (v string) {
	if !p.IsSetTitle() {
		return CreateTicketReq_Title_DEFAULT
	}
	return *p.Title
}

var CreateTicketReq_CreateUserName_DEFAULT string

func (p *CreateTicketReq) GetCreateUserName() (v string) {
	if !p.IsSetCreateUserName() {
		return CreateTicketReq_CreateUserName_DEFAULT
	}
	return *p.CreateUserName
}
func (p *CreateTicketReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *CreateTicketReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateTicketReq) SetSqlText(val *string) {
	p.SqlText = val
}
func (p *CreateTicketReq) SetTicketExecuteType(val *ExecuteType) {
	p.TicketExecuteType = val
}
func (p *CreateTicketReq) SetTicketType(val TicketType) {
	p.TicketType = val
}
func (p *CreateTicketReq) SetCreateUser(val string) {
	p.CreateUser = val
}
func (p *CreateTicketReq) SetDatabaseName(val string) {
	p.DatabaseName = val
}
func (p *CreateTicketReq) SetExecStartTime(val *int32) {
	p.ExecStartTime = val
}
func (p *CreateTicketReq) SetExecEndTime(val *int32) {
	p.ExecEndTime = val
}
func (p *CreateTicketReq) SetBatchConfig(val *BatchConfig) {
	p.BatchConfig = val
}
func (p *CreateTicketReq) SetArchiveConfig(val *DataArchiveConfig) {
	p.ArchiveConfig = val
}
func (p *CreateTicketReq) SetCreateFrom(val *string) {
	p.CreateFrom = val
}
func (p *CreateTicketReq) SetMemo(val *string) {
	p.Memo = val
}
func (p *CreateTicketReq) SetTitle(val *string) {
	p.Title = val
}
func (p *CreateTicketReq) SetCreateUserName(val *string) {
	p.CreateUserName = val
}

var fieldIDToName_CreateTicketReq = map[int16]string{
	1:  "InstanceType",
	2:  "InstanceId",
	3:  "SqlText",
	4:  "TicketExecuteType",
	5:  "TicketType",
	6:  "CreateUser",
	7:  "DatabaseName",
	8:  "ExecStartTime",
	9:  "ExecEndTime",
	10: "BatchConfig",
	11: "ArchiveConfig",
	13: "CreateFrom",
	14: "Memo",
	15: "Title",
	16: "CreateUserName",
}

func (p *CreateTicketReq) IsSetSqlText() bool {
	return p.SqlText != nil
}

func (p *CreateTicketReq) IsSetTicketExecuteType() bool {
	return p.TicketExecuteType != nil
}

func (p *CreateTicketReq) IsSetExecStartTime() bool {
	return p.ExecStartTime != nil
}

func (p *CreateTicketReq) IsSetExecEndTime() bool {
	return p.ExecEndTime != nil
}

func (p *CreateTicketReq) IsSetBatchConfig() bool {
	return p.BatchConfig != nil
}

func (p *CreateTicketReq) IsSetArchiveConfig() bool {
	return p.ArchiveConfig != nil
}

func (p *CreateTicketReq) IsSetCreateFrom() bool {
	return p.CreateFrom != nil
}

func (p *CreateTicketReq) IsSetMemo() bool {
	return p.Memo != nil
}

func (p *CreateTicketReq) IsSetTitle() bool {
	return p.Title != nil
}

func (p *CreateTicketReq) IsSetCreateUserName() bool {
	return p.CreateUserName != nil
}

func (p *CreateTicketReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTicketReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetTicketType bool = false
	var issetCreateUser bool = false
	var issetDatabaseName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetDatabaseName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceType {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTicketType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetCreateUser {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetDatabaseName {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateTicketReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateTicketReq[fieldId]))
}

func (p *CreateTicketReq) ReadField1(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *CreateTicketReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateTicketReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SqlText = _field
	return nil
}
func (p *CreateTicketReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *ExecuteType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := ExecuteType(v)
		_field = &tmp
	}
	p.TicketExecuteType = _field
	return nil
}
func (p *CreateTicketReq) ReadField5(iprot thrift.TProtocol) error {

	var _field TicketType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TicketType(v)
	}
	p.TicketType = _field
	return nil
}
func (p *CreateTicketReq) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateUser = _field
	return nil
}
func (p *CreateTicketReq) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DatabaseName = _field
	return nil
}
func (p *CreateTicketReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecStartTime = _field
	return nil
}
func (p *CreateTicketReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ExecEndTime = _field
	return nil
}
func (p *CreateTicketReq) ReadField10(iprot thrift.TProtocol) error {
	_field := NewBatchConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BatchConfig = _field
	return nil
}
func (p *CreateTicketReq) ReadField11(iprot thrift.TProtocol) error {
	_field := NewDataArchiveConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ArchiveConfig = _field
	return nil
}
func (p *CreateTicketReq) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateFrom = _field
	return nil
}
func (p *CreateTicketReq) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Memo = _field
	return nil
}
func (p *CreateTicketReq) ReadField15(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Title = _field
	return nil
}
func (p *CreateTicketReq) ReadField16(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUserName = _field
	return nil
}

func (p *CreateTicketReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTicketReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateTicketReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateTicketReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateTicketReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateTicketReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlText() {
		if err = oprot.WriteFieldBegin("SqlText", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SqlText); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateTicketReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTicketExecuteType() {
		if err = oprot.WriteFieldBegin("TicketExecuteType", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TicketExecuteType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateTicketReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TicketType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateTicketReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateUser", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateUser); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateTicketReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DatabaseName", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DatabaseName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateTicketReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecStartTime() {
		if err = oprot.WriteFieldBegin("ExecStartTime", thrift.I32, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ExecStartTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateTicketReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetExecEndTime() {
		if err = oprot.WriteFieldBegin("ExecEndTime", thrift.I32, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ExecEndTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *CreateTicketReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetBatchConfig() {
		if err = oprot.WriteFieldBegin("BatchConfig", thrift.STRUCT, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BatchConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *CreateTicketReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetArchiveConfig() {
		if err = oprot.WriteFieldBegin("ArchiveConfig", thrift.STRUCT, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ArchiveConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *CreateTicketReq) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateFrom() {
		if err = oprot.WriteFieldBegin("CreateFrom", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateFrom); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *CreateTicketReq) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetMemo() {
		if err = oprot.WriteFieldBegin("Memo", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Memo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *CreateTicketReq) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetTitle() {
		if err = oprot.WriteFieldBegin("Title", thrift.STRING, 15); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Title); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *CreateTicketReq) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUserName() {
		if err = oprot.WriteFieldBegin("CreateUserName", thrift.STRING, 16); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUserName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *CreateTicketReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTicketReq(%+v)", *p)

}

func (p *CreateTicketReq) DeepEqual(ano *CreateTicketReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field3DeepEqual(ano.SqlText) {
		return false
	}
	if !p.Field4DeepEqual(ano.TicketExecuteType) {
		return false
	}
	if !p.Field5DeepEqual(ano.TicketType) {
		return false
	}
	if !p.Field6DeepEqual(ano.CreateUser) {
		return false
	}
	if !p.Field7DeepEqual(ano.DatabaseName) {
		return false
	}
	if !p.Field8DeepEqual(ano.ExecStartTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.ExecEndTime) {
		return false
	}
	if !p.Field10DeepEqual(ano.BatchConfig) {
		return false
	}
	if !p.Field11DeepEqual(ano.ArchiveConfig) {
		return false
	}
	if !p.Field13DeepEqual(ano.CreateFrom) {
		return false
	}
	if !p.Field14DeepEqual(ano.Memo) {
		return false
	}
	if !p.Field15DeepEqual(ano.Title) {
		return false
	}
	if !p.Field16DeepEqual(ano.CreateUserName) {
		return false
	}
	return true
}

func (p *CreateTicketReq) Field1DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field3DeepEqual(src *string) bool {

	if p.SqlText == src {
		return true
	} else if p.SqlText == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SqlText, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field4DeepEqual(src *ExecuteType) bool {

	if p.TicketExecuteType == src {
		return true
	} else if p.TicketExecuteType == nil || src == nil {
		return false
	}
	if *p.TicketExecuteType != *src {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field5DeepEqual(src TicketType) bool {

	if p.TicketType != src {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field6DeepEqual(src string) bool {

	if strings.Compare(p.CreateUser, src) != 0 {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field7DeepEqual(src string) bool {

	if strings.Compare(p.DatabaseName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field8DeepEqual(src *int32) bool {

	if p.ExecStartTime == src {
		return true
	} else if p.ExecStartTime == nil || src == nil {
		return false
	}
	if *p.ExecStartTime != *src {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field9DeepEqual(src *int32) bool {

	if p.ExecEndTime == src {
		return true
	} else if p.ExecEndTime == nil || src == nil {
		return false
	}
	if *p.ExecEndTime != *src {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field10DeepEqual(src *BatchConfig) bool {

	if !p.BatchConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field11DeepEqual(src *DataArchiveConfig) bool {

	if !p.ArchiveConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field13DeepEqual(src *string) bool {

	if p.CreateFrom == src {
		return true
	} else if p.CreateFrom == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateFrom, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field14DeepEqual(src *string) bool {

	if p.Memo == src {
		return true
	} else if p.Memo == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Memo, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field15DeepEqual(src *string) bool {

	if p.Title == src {
		return true
	} else if p.Title == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Title, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateTicketReq) Field16DeepEqual(src *string) bool {

	if p.CreateUserName == src {
		return true
	} else if p.CreateUserName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUserName, *src) != 0 {
		return false
	}
	return true
}

type GetArchiveNextSqlResp struct {
	DeleteSql string `thrift:"DeleteSql,1,required" frugal:"1,required,string" json:"DeleteSql"`
}

func NewGetArchiveNextSqlResp() *GetArchiveNextSqlResp {
	return &GetArchiveNextSqlResp{}
}

func (p *GetArchiveNextSqlResp) InitDefault() {
}

func (p *GetArchiveNextSqlResp) GetDeleteSql() (v string) {
	return p.DeleteSql
}
func (p *GetArchiveNextSqlResp) SetDeleteSql(val string) {
	p.DeleteSql = val
}

var fieldIDToName_GetArchiveNextSqlResp = map[int16]string{
	1: "DeleteSql",
}

func (p *GetArchiveNextSqlResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetArchiveNextSqlResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetDeleteSql bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetDeleteSql = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetDeleteSql {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetArchiveNextSqlResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_GetArchiveNextSqlResp[fieldId]))
}

func (p *GetArchiveNextSqlResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DeleteSql = _field
	return nil
}

func (p *GetArchiveNextSqlResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("GetArchiveNextSqlResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("GetArchiveNextSqlResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetArchiveNextSqlResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DeleteSql", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DeleteSql); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *GetArchiveNextSqlResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArchiveNextSqlResp(%+v)", *p)

}

func (p *GetArchiveNextSqlResp) DeepEqual(ano *GetArchiveNextSqlResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.DeleteSql) {
		return false
	}
	return true
}

func (p *GetArchiveNextSqlResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.DeleteSql, src) != 0 {
		return false
	}
	return true
}

type DataArchiveConfig struct {
	IsBackUp         bool              `thrift:"IsBackUp,1,required" frugal:"1,required,bool" json:"IsBackUp"`
	TableName        string            `thrift:"TableName,2,required" frugal:"2,required,string" json:"TableName"`
	OtherCase        *string           `thrift:"OtherCase,3,optional" frugal:"3,optional,string" json:"OtherCase,omitempty"`
	ArchiveType      ArchiveType       `thrift:"ArchiveType,5,required" frugal:"5,required,ArchiveType" json:"ArchiveType"`
	ArchiveCycleInfo *ArchiveCycleInfo `thrift:"ArchiveCycleInfo,6,optional" frugal:"6,optional,ArchiveCycleInfo" json:"ArchiveCycleInfo,omitempty"`
	TimeInfos        []*TimeInfo       `thrift:"TimeInfos,7,required" frugal:"7,required,list<TimeInfo>" json:"TimeInfos"`
	BackUpConfig     *BackUpConfig     `thrift:"BackUpConfig,8,optional" frugal:"8,optional,BackUpConfig" json:"BackUpConfig,omitempty"`
}

func NewDataArchiveConfig() *DataArchiveConfig {
	return &DataArchiveConfig{}
}

func (p *DataArchiveConfig) InitDefault() {
}

func (p *DataArchiveConfig) GetIsBackUp() (v bool) {
	return p.IsBackUp
}

func (p *DataArchiveConfig) GetTableName() (v string) {
	return p.TableName
}

var DataArchiveConfig_OtherCase_DEFAULT string

func (p *DataArchiveConfig) GetOtherCase() (v string) {
	if !p.IsSetOtherCase() {
		return DataArchiveConfig_OtherCase_DEFAULT
	}
	return *p.OtherCase
}

func (p *DataArchiveConfig) GetArchiveType() (v ArchiveType) {
	return p.ArchiveType
}

var DataArchiveConfig_ArchiveCycleInfo_DEFAULT *ArchiveCycleInfo

func (p *DataArchiveConfig) GetArchiveCycleInfo() (v *ArchiveCycleInfo) {
	if !p.IsSetArchiveCycleInfo() {
		return DataArchiveConfig_ArchiveCycleInfo_DEFAULT
	}
	return p.ArchiveCycleInfo
}

func (p *DataArchiveConfig) GetTimeInfos() (v []*TimeInfo) {
	return p.TimeInfos
}

var DataArchiveConfig_BackUpConfig_DEFAULT *BackUpConfig

func (p *DataArchiveConfig) GetBackUpConfig() (v *BackUpConfig) {
	if !p.IsSetBackUpConfig() {
		return DataArchiveConfig_BackUpConfig_DEFAULT
	}
	return p.BackUpConfig
}
func (p *DataArchiveConfig) SetIsBackUp(val bool) {
	p.IsBackUp = val
}
func (p *DataArchiveConfig) SetTableName(val string) {
	p.TableName = val
}
func (p *DataArchiveConfig) SetOtherCase(val *string) {
	p.OtherCase = val
}
func (p *DataArchiveConfig) SetArchiveType(val ArchiveType) {
	p.ArchiveType = val
}
func (p *DataArchiveConfig) SetArchiveCycleInfo(val *ArchiveCycleInfo) {
	p.ArchiveCycleInfo = val
}
func (p *DataArchiveConfig) SetTimeInfos(val []*TimeInfo) {
	p.TimeInfos = val
}
func (p *DataArchiveConfig) SetBackUpConfig(val *BackUpConfig) {
	p.BackUpConfig = val
}

var fieldIDToName_DataArchiveConfig = map[int16]string{
	1: "IsBackUp",
	2: "TableName",
	3: "OtherCase",
	5: "ArchiveType",
	6: "ArchiveCycleInfo",
	7: "TimeInfos",
	8: "BackUpConfig",
}

func (p *DataArchiveConfig) IsSetOtherCase() bool {
	return p.OtherCase != nil
}

func (p *DataArchiveConfig) IsSetArchiveCycleInfo() bool {
	return p.ArchiveCycleInfo != nil
}

func (p *DataArchiveConfig) IsSetBackUpConfig() bool {
	return p.BackUpConfig != nil
}

func (p *DataArchiveConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataArchiveConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetIsBackUp bool = false
	var issetTableName bool = false
	var issetArchiveType bool = false
	var issetTimeInfos bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsBackUp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTableName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetArchiveType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimeInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetIsBackUp {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTableName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetArchiveType {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetTimeInfos {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DataArchiveConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DataArchiveConfig[fieldId]))
}

func (p *DataArchiveConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsBackUp = _field
	return nil
}
func (p *DataArchiveConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableName = _field
	return nil
}
func (p *DataArchiveConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.OtherCase = _field
	return nil
}
func (p *DataArchiveConfig) ReadField5(iprot thrift.TProtocol) error {

	var _field ArchiveType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ArchiveType(v)
	}
	p.ArchiveType = _field
	return nil
}
func (p *DataArchiveConfig) ReadField6(iprot thrift.TProtocol) error {
	_field := NewArchiveCycleInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ArchiveCycleInfo = _field
	return nil
}
func (p *DataArchiveConfig) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TimeInfo, 0, size)
	values := make([]TimeInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TimeInfos = _field
	return nil
}
func (p *DataArchiveConfig) ReadField8(iprot thrift.TProtocol) error {
	_field := NewBackUpConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BackUpConfig = _field
	return nil
}

func (p *DataArchiveConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DataArchiveConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("DataArchiveConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DataArchiveConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsBackUp", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsBackUp); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DataArchiveConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TableName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TableName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DataArchiveConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetOtherCase() {
		if err = oprot.WriteFieldBegin("OtherCase", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.OtherCase); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DataArchiveConfig) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ArchiveType", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ArchiveType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DataArchiveConfig) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetArchiveCycleInfo() {
		if err = oprot.WriteFieldBegin("ArchiveCycleInfo", thrift.STRUCT, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ArchiveCycleInfo.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DataArchiveConfig) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TimeInfos", thrift.LIST, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TimeInfos)); err != nil {
		return err
	}
	for _, v := range p.TimeInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DataArchiveConfig) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetBackUpConfig() {
		if err = oprot.WriteFieldBegin("BackUpConfig", thrift.STRUCT, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BackUpConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DataArchiveConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataArchiveConfig(%+v)", *p)

}

func (p *DataArchiveConfig) DeepEqual(ano *DataArchiveConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.IsBackUp) {
		return false
	}
	if !p.Field2DeepEqual(ano.TableName) {
		return false
	}
	if !p.Field3DeepEqual(ano.OtherCase) {
		return false
	}
	if !p.Field5DeepEqual(ano.ArchiveType) {
		return false
	}
	if !p.Field6DeepEqual(ano.ArchiveCycleInfo) {
		return false
	}
	if !p.Field7DeepEqual(ano.TimeInfos) {
		return false
	}
	if !p.Field8DeepEqual(ano.BackUpConfig) {
		return false
	}
	return true
}

func (p *DataArchiveConfig) Field1DeepEqual(src bool) bool {

	if p.IsBackUp != src {
		return false
	}
	return true
}
func (p *DataArchiveConfig) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TableName, src) != 0 {
		return false
	}
	return true
}
func (p *DataArchiveConfig) Field3DeepEqual(src *string) bool {

	if p.OtherCase == src {
		return true
	} else if p.OtherCase == nil || src == nil {
		return false
	}
	if strings.Compare(*p.OtherCase, *src) != 0 {
		return false
	}
	return true
}
func (p *DataArchiveConfig) Field5DeepEqual(src ArchiveType) bool {

	if p.ArchiveType != src {
		return false
	}
	return true
}
func (p *DataArchiveConfig) Field6DeepEqual(src *ArchiveCycleInfo) bool {

	if !p.ArchiveCycleInfo.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DataArchiveConfig) Field7DeepEqual(src []*TimeInfo) bool {

	if len(p.TimeInfos) != len(src) {
		return false
	}
	for i, v := range p.TimeInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DataArchiveConfig) Field8DeepEqual(src *BackUpConfig) bool {

	if !p.BackUpConfig.DeepEqual(src) {
		return false
	}
	return true
}

type TimeInfo struct {
	TimeColumn string `thrift:"TimeColumn,1,required" frugal:"1,required,string" json:"TimeColumn"`
	TimeFormat string `thrift:"TimeFormat,2,required" frugal:"2,required,string" json:"TimeFormat"`
	OffsetTime int32  `thrift:"OffsetTime,3,required" frugal:"3,required,i32" json:"OffsetTime"`
}

func NewTimeInfo() *TimeInfo {
	return &TimeInfo{}
}

func (p *TimeInfo) InitDefault() {
}

func (p *TimeInfo) GetTimeColumn() (v string) {
	return p.TimeColumn
}

func (p *TimeInfo) GetTimeFormat() (v string) {
	return p.TimeFormat
}

func (p *TimeInfo) GetOffsetTime() (v int32) {
	return p.OffsetTime
}
func (p *TimeInfo) SetTimeColumn(val string) {
	p.TimeColumn = val
}
func (p *TimeInfo) SetTimeFormat(val string) {
	p.TimeFormat = val
}
func (p *TimeInfo) SetOffsetTime(val int32) {
	p.OffsetTime = val
}

var fieldIDToName_TimeInfo = map[int16]string{
	1: "TimeColumn",
	2: "TimeFormat",
	3: "OffsetTime",
}

func (p *TimeInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TimeInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTimeColumn bool = false
	var issetTimeFormat bool = false
	var issetOffsetTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimeColumn = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTimeFormat = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetOffsetTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTimeColumn {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTimeFormat {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOffsetTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TimeInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TimeInfo[fieldId]))
}

func (p *TimeInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TimeColumn = _field
	return nil
}
func (p *TimeInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TimeFormat = _field
	return nil
}
func (p *TimeInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OffsetTime = _field
	return nil
}

func (p *TimeInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TimeInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("TimeInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TimeInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TimeColumn", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TimeColumn); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TimeInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TimeFormat", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TimeFormat); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TimeInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("OffsetTime", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.OffsetTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TimeInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TimeInfo(%+v)", *p)

}

func (p *TimeInfo) DeepEqual(ano *TimeInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TimeColumn) {
		return false
	}
	if !p.Field2DeepEqual(ano.TimeFormat) {
		return false
	}
	if !p.Field3DeepEqual(ano.OffsetTime) {
		return false
	}
	return true
}

func (p *TimeInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TimeColumn, src) != 0 {
		return false
	}
	return true
}
func (p *TimeInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.TimeFormat, src) != 0 {
		return false
	}
	return true
}
func (p *TimeInfo) Field3DeepEqual(src int32) bool {

	if p.OffsetTime != src {
		return false
	}
	return true
}

type CreateTicketResp struct {
	Code     ErrCode `thrift:"Code,1,required" frugal:"1,required,ErrCode" json:"Code"`
	ErrMsg   string  `thrift:"ErrMsg,2,required" frugal:"2,required,string" json:"ErrMsg"`
	TicketId string  `thrift:"TicketId,3,required" frugal:"3,required,string" json:"TicketId"`
}

func NewCreateTicketResp() *CreateTicketResp {
	return &CreateTicketResp{}
}

func (p *CreateTicketResp) InitDefault() {
}

func (p *CreateTicketResp) GetCode() (v ErrCode) {
	return p.Code
}

func (p *CreateTicketResp) GetErrMsg() (v string) {
	return p.ErrMsg
}

func (p *CreateTicketResp) GetTicketId() (v string) {
	return p.TicketId
}
func (p *CreateTicketResp) SetCode(val ErrCode) {
	p.Code = val
}
func (p *CreateTicketResp) SetErrMsg(val string) {
	p.ErrMsg = val
}
func (p *CreateTicketResp) SetTicketId(val string) {
	p.TicketId = val
}

var fieldIDToName_CreateTicketResp = map[int16]string{
	1: "Code",
	2: "ErrMsg",
	3: "TicketId",
}

func (p *CreateTicketResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTicketResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetErrMsg bool = false
	var issetTicketId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetErrMsg {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTicketId {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateTicketResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateTicketResp[fieldId]))
}

func (p *CreateTicketResp) ReadField1(iprot thrift.TProtocol) error {

	var _field ErrCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ErrCode(v)
	}
	p.Code = _field
	return nil
}
func (p *CreateTicketResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrMsg = _field
	return nil
}
func (p *CreateTicketResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}

func (p *CreateTicketResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateTicketResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateTicketResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateTicketResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Code)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateTicketResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrMsg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateTicketResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateTicketResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTicketResp(%+v)", *p)

}

func (p *CreateTicketResp) DeepEqual(ano *CreateTicketResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Code) {
		return false
	}
	if !p.Field2DeepEqual(ano.ErrMsg) {
		return false
	}
	if !p.Field3DeepEqual(ano.TicketId) {
		return false
	}
	return true
}

func (p *CreateTicketResp) Field1DeepEqual(src ErrCode) bool {

	if p.Code != src {
		return false
	}
	return true
}
func (p *CreateTicketResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ErrMsg, src) != 0 {
		return false
	}
	return true
}
func (p *CreateTicketResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}

type BatchConfig struct {
	SleepTimeMs         *int32 `thrift:"SleepTimeMs,1,optional" frugal:"1,optional,i32" json:"SleepTimeMs,omitempty"`
	IsEnableDelayCheck  *bool  `thrift:"IsEnableDelayCheck,2,optional" frugal:"2,optional,bool" json:"IsEnableDelayCheck,omitempty"`
	ReplicaDelaySeconds *int32 `thrift:"ReplicaDelaySeconds,3,optional" frugal:"3,optional,i32" json:"ReplicaDelaySeconds,omitempty"`
	BatchSize           *int32 `thrift:"BatchSize,4,optional" frugal:"4,optional,i32" json:"BatchSize,omitempty"`
	DBBatchNum          *int32 `thrift:"DBBatchNum,5,optional" frugal:"5,optional,i32" json:"DBBatchNum,omitempty"`
}

func NewBatchConfig() *BatchConfig {
	return &BatchConfig{}
}

func (p *BatchConfig) InitDefault() {
}

var BatchConfig_SleepTimeMs_DEFAULT int32

func (p *BatchConfig) GetSleepTimeMs() (v int32) {
	if !p.IsSetSleepTimeMs() {
		return BatchConfig_SleepTimeMs_DEFAULT
	}
	return *p.SleepTimeMs
}

var BatchConfig_IsEnableDelayCheck_DEFAULT bool

func (p *BatchConfig) GetIsEnableDelayCheck() (v bool) {
	if !p.IsSetIsEnableDelayCheck() {
		return BatchConfig_IsEnableDelayCheck_DEFAULT
	}
	return *p.IsEnableDelayCheck
}

var BatchConfig_ReplicaDelaySeconds_DEFAULT int32

func (p *BatchConfig) GetReplicaDelaySeconds() (v int32) {
	if !p.IsSetReplicaDelaySeconds() {
		return BatchConfig_ReplicaDelaySeconds_DEFAULT
	}
	return *p.ReplicaDelaySeconds
}

var BatchConfig_BatchSize_DEFAULT int32

func (p *BatchConfig) GetBatchSize() (v int32) {
	if !p.IsSetBatchSize() {
		return BatchConfig_BatchSize_DEFAULT
	}
	return *p.BatchSize
}

var BatchConfig_DBBatchNum_DEFAULT int32

func (p *BatchConfig) GetDBBatchNum() (v int32) {
	if !p.IsSetDBBatchNum() {
		return BatchConfig_DBBatchNum_DEFAULT
	}
	return *p.DBBatchNum
}
func (p *BatchConfig) SetSleepTimeMs(val *int32) {
	p.SleepTimeMs = val
}
func (p *BatchConfig) SetIsEnableDelayCheck(val *bool) {
	p.IsEnableDelayCheck = val
}
func (p *BatchConfig) SetReplicaDelaySeconds(val *int32) {
	p.ReplicaDelaySeconds = val
}
func (p *BatchConfig) SetBatchSize(val *int32) {
	p.BatchSize = val
}
func (p *BatchConfig) SetDBBatchNum(val *int32) {
	p.DBBatchNum = val
}

var fieldIDToName_BatchConfig = map[int16]string{
	1: "SleepTimeMs",
	2: "IsEnableDelayCheck",
	3: "ReplicaDelaySeconds",
	4: "BatchSize",
	5: "DBBatchNum",
}

func (p *BatchConfig) IsSetSleepTimeMs() bool {
	return p.SleepTimeMs != nil
}

func (p *BatchConfig) IsSetIsEnableDelayCheck() bool {
	return p.IsEnableDelayCheck != nil
}

func (p *BatchConfig) IsSetReplicaDelaySeconds() bool {
	return p.ReplicaDelaySeconds != nil
}

func (p *BatchConfig) IsSetBatchSize() bool {
	return p.BatchSize != nil
}

func (p *BatchConfig) IsSetDBBatchNum() bool {
	return p.DBBatchNum != nil
}

func (p *BatchConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("BatchConfig")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_BatchConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *BatchConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SleepTimeMs = _field
	return nil
}
func (p *BatchConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsEnableDelayCheck = _field
	return nil
}
func (p *BatchConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ReplicaDelaySeconds = _field
	return nil
}
func (p *BatchConfig) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.BatchSize = _field
	return nil
}
func (p *BatchConfig) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DBBatchNum = _field
	return nil
}

func (p *BatchConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("BatchConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("BatchConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *BatchConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSleepTimeMs() {
		if err = oprot.WriteFieldBegin("SleepTimeMs", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.SleepTimeMs); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *BatchConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsEnableDelayCheck() {
		if err = oprot.WriteFieldBegin("IsEnableDelayCheck", thrift.BOOL, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsEnableDelayCheck); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *BatchConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetReplicaDelaySeconds() {
		if err = oprot.WriteFieldBegin("ReplicaDelaySeconds", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.ReplicaDelaySeconds); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *BatchConfig) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetBatchSize() {
		if err = oprot.WriteFieldBegin("BatchSize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.BatchSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *BatchConfig) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetDBBatchNum() {
		if err = oprot.WriteFieldBegin("DBBatchNum", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.DBBatchNum); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *BatchConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchConfig(%+v)", *p)

}

func (p *BatchConfig) DeepEqual(ano *BatchConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SleepTimeMs) {
		return false
	}
	if !p.Field2DeepEqual(ano.IsEnableDelayCheck) {
		return false
	}
	if !p.Field3DeepEqual(ano.ReplicaDelaySeconds) {
		return false
	}
	if !p.Field4DeepEqual(ano.BatchSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.DBBatchNum) {
		return false
	}
	return true
}

func (p *BatchConfig) Field1DeepEqual(src *int32) bool {

	if p.SleepTimeMs == src {
		return true
	} else if p.SleepTimeMs == nil || src == nil {
		return false
	}
	if *p.SleepTimeMs != *src {
		return false
	}
	return true
}
func (p *BatchConfig) Field2DeepEqual(src *bool) bool {

	if p.IsEnableDelayCheck == src {
		return true
	} else if p.IsEnableDelayCheck == nil || src == nil {
		return false
	}
	if *p.IsEnableDelayCheck != *src {
		return false
	}
	return true
}
func (p *BatchConfig) Field3DeepEqual(src *int32) bool {

	if p.ReplicaDelaySeconds == src {
		return true
	} else if p.ReplicaDelaySeconds == nil || src == nil {
		return false
	}
	if *p.ReplicaDelaySeconds != *src {
		return false
	}
	return true
}
func (p *BatchConfig) Field4DeepEqual(src *int32) bool {

	if p.BatchSize == src {
		return true
	} else if p.BatchSize == nil || src == nil {
		return false
	}
	if *p.BatchSize != *src {
		return false
	}
	return true
}
func (p *BatchConfig) Field5DeepEqual(src *int32) bool {

	if p.DBBatchNum == src {
		return true
	} else if p.DBBatchNum == nil || src == nil {
		return false
	}
	if *p.DBBatchNum != *src {
		return false
	}
	return true
}

type ModifyTicketReq struct {
	TicketId      string             `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
	SqlText       string             `thrift:"SqlText,2,required" frugal:"2,required,string" json:"SqlText"`
	BatchSize     *BatchConfig       `thrift:"BatchSize,3,optional" frugal:"3,optional,BatchConfig" json:"BatchSize,omitempty"`
	ArchiveConfig *DataArchiveConfig `thrift:"ArchiveConfig,4,optional" frugal:"4,optional,DataArchiveConfig" json:"ArchiveConfig,omitempty"`
}

func NewModifyTicketReq() *ModifyTicketReq {
	return &ModifyTicketReq{}
}

func (p *ModifyTicketReq) InitDefault() {
}

func (p *ModifyTicketReq) GetTicketId() (v string) {
	return p.TicketId
}

func (p *ModifyTicketReq) GetSqlText() (v string) {
	return p.SqlText
}

var ModifyTicketReq_BatchSize_DEFAULT *BatchConfig

func (p *ModifyTicketReq) GetBatchSize() (v *BatchConfig) {
	if !p.IsSetBatchSize() {
		return ModifyTicketReq_BatchSize_DEFAULT
	}
	return p.BatchSize
}

var ModifyTicketReq_ArchiveConfig_DEFAULT *DataArchiveConfig

func (p *ModifyTicketReq) GetArchiveConfig() (v *DataArchiveConfig) {
	if !p.IsSetArchiveConfig() {
		return ModifyTicketReq_ArchiveConfig_DEFAULT
	}
	return p.ArchiveConfig
}
func (p *ModifyTicketReq) SetTicketId(val string) {
	p.TicketId = val
}
func (p *ModifyTicketReq) SetSqlText(val string) {
	p.SqlText = val
}
func (p *ModifyTicketReq) SetBatchSize(val *BatchConfig) {
	p.BatchSize = val
}
func (p *ModifyTicketReq) SetArchiveConfig(val *DataArchiveConfig) {
	p.ArchiveConfig = val
}

var fieldIDToName_ModifyTicketReq = map[int16]string{
	1: "TicketId",
	2: "SqlText",
	3: "BatchSize",
	4: "ArchiveConfig",
}

func (p *ModifyTicketReq) IsSetBatchSize() bool {
	return p.BatchSize != nil
}

func (p *ModifyTicketReq) IsSetArchiveConfig() bool {
	return p.ArchiveConfig != nil
}

func (p *ModifyTicketReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyTicketReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false
	var issetSqlText bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlText = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetSqlText {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyTicketReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyTicketReq[fieldId]))
}

func (p *ModifyTicketReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}
func (p *ModifyTicketReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlText = _field
	return nil
}
func (p *ModifyTicketReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewBatchConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BatchSize = _field
	return nil
}
func (p *ModifyTicketReq) ReadField4(iprot thrift.TProtocol) error {
	_field := NewDataArchiveConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ArchiveConfig = _field
	return nil
}

func (p *ModifyTicketReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyTicketReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyTicketReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyTicketReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyTicketReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlText", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlText); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyTicketReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetBatchSize() {
		if err = oprot.WriteFieldBegin("BatchSize", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BatchSize.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyTicketReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetArchiveConfig() {
		if err = oprot.WriteFieldBegin("ArchiveConfig", thrift.STRUCT, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ArchiveConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyTicketReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyTicketReq(%+v)", *p)

}

func (p *ModifyTicketReq) DeepEqual(ano *ModifyTicketReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SqlText) {
		return false
	}
	if !p.Field3DeepEqual(ano.BatchSize) {
		return false
	}
	if !p.Field4DeepEqual(ano.ArchiveConfig) {
		return false
	}
	return true
}

func (p *ModifyTicketReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyTicketReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.SqlText, src) != 0 {
		return false
	}
	return true
}
func (p *ModifyTicketReq) Field3DeepEqual(src *BatchConfig) bool {

	if !p.BatchSize.DeepEqual(src) {
		return false
	}
	return true
}
func (p *ModifyTicketReq) Field4DeepEqual(src *DataArchiveConfig) bool {

	if !p.ArchiveConfig.DeepEqual(src) {
		return false
	}
	return true
}

type ModifyTicketResp struct {
	Code ErrCode `thrift:"Code,1,required" frugal:"1,required,ErrCode" json:"Code"`
}

func NewModifyTicketResp() *ModifyTicketResp {
	return &ModifyTicketResp{}
}

func (p *ModifyTicketResp) InitDefault() {
}

func (p *ModifyTicketResp) GetCode() (v ErrCode) {
	return p.Code
}
func (p *ModifyTicketResp) SetCode(val ErrCode) {
	p.Code = val
}

var fieldIDToName_ModifyTicketResp = map[int16]string{
	1: "Code",
}

func (p *ModifyTicketResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyTicketResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyTicketResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ModifyTicketResp[fieldId]))
}

func (p *ModifyTicketResp) ReadField1(iprot thrift.TProtocol) error {

	var _field ErrCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ErrCode(v)
	}
	p.Code = _field
	return nil
}

func (p *ModifyTicketResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyTicketResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyTicketResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyTicketResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Code)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyTicketResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyTicketResp(%+v)", *p)

}

func (p *ModifyTicketResp) DeepEqual(ano *ModifyTicketResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Code) {
		return false
	}
	return true
}

func (p *ModifyTicketResp) Field1DeepEqual(src ErrCode) bool {

	if p.Code != src {
		return false
	}
	return true
}

type ExecuteTicketReq struct {
	TicketId string `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
}

func NewExecuteTicketReq() *ExecuteTicketReq {
	return &ExecuteTicketReq{}
}

func (p *ExecuteTicketReq) InitDefault() {
}

func (p *ExecuteTicketReq) GetTicketId() (v string) {
	return p.TicketId
}
func (p *ExecuteTicketReq) SetTicketId(val string) {
	p.TicketId = val
}

var fieldIDToName_ExecuteTicketReq = map[int16]string{
	1: "TicketId",
}

func (p *ExecuteTicketReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteTicketReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteTicketReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExecuteTicketReq[fieldId]))
}

func (p *ExecuteTicketReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}

func (p *ExecuteTicketReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteTicketReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteTicketReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteTicketReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExecuteTicketReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteTicketReq(%+v)", *p)

}

func (p *ExecuteTicketReq) DeepEqual(ano *ExecuteTicketReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	return true
}

func (p *ExecuteTicketReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}

type ExecuteTicketResp struct {
	AllPass    bool   `thrift:"AllPass,1,required" frugal:"1,required,bool" json:"AllPass"`
	ErrMessage string `thrift:"ErrMessage,2,required" frugal:"2,required,string" json:"ErrMessage"`
}

func NewExecuteTicketResp() *ExecuteTicketResp {
	return &ExecuteTicketResp{}
}

func (p *ExecuteTicketResp) InitDefault() {
}

func (p *ExecuteTicketResp) GetAllPass() (v bool) {
	return p.AllPass
}

func (p *ExecuteTicketResp) GetErrMessage() (v string) {
	return p.ErrMessage
}
func (p *ExecuteTicketResp) SetAllPass(val bool) {
	p.AllPass = val
}
func (p *ExecuteTicketResp) SetErrMessage(val string) {
	p.ErrMessage = val
}

var fieldIDToName_ExecuteTicketResp = map[int16]string{
	1: "AllPass",
	2: "ErrMessage",
}

func (p *ExecuteTicketResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteTicketResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllPass bool = false
	var issetErrMessage bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllPass = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllPass {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetErrMessage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ExecuteTicketResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ExecuteTicketResp[fieldId]))
}

func (p *ExecuteTicketResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllPass = _field
	return nil
}
func (p *ExecuteTicketResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrMessage = _field
	return nil
}

func (p *ExecuteTicketResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ExecuteTicketResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("ExecuteTicketResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ExecuteTicketResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllPass", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.AllPass); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ExecuteTicketResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrMessage", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrMessage); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ExecuteTicketResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecuteTicketResp(%+v)", *p)

}

func (p *ExecuteTicketResp) DeepEqual(ano *ExecuteTicketResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllPass) {
		return false
	}
	if !p.Field2DeepEqual(ano.ErrMessage) {
		return false
	}
	return true
}

func (p *ExecuteTicketResp) Field1DeepEqual(src bool) bool {

	if p.AllPass != src {
		return false
	}
	return true
}
func (p *ExecuteTicketResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ErrMessage, src) != 0 {
		return false
	}
	return true
}

type DescribeTicketsReq struct {
	SearchParam *TicketSearchParam `thrift:"SearchParam,1,optional" frugal:"1,optional,TicketSearchParam" json:"SearchParam,omitempty"`
	PageNumber  *int32             `thrift:"PageNumber,2,optional" frugal:"2,optional,i32" json:"PageNumber,omitempty"`
	PageSize    *int32             `thrift:"PageSize,3,optional" frugal:"3,optional,i32" json:"PageSize,omitempty"`
	SortBy      *SortBy            `thrift:"SortBy,4,optional" frugal:"4,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy     *OrderByForTicket  `thrift:"OrderBy,5,optional" frugal:"5,optional,OrderByForTicket" json:"OrderBy,omitempty"`
	ListType    *TicketListType    `thrift:"ListType,6,optional" frugal:"6,optional,TicketListType" json:"ListType,omitempty"`
	CreateFrom  *string            `thrift:"CreateFrom,7,optional" frugal:"7,optional,string" json:"CreateFrom,omitempty"`
}

func NewDescribeTicketsReq() *DescribeTicketsReq {
	return &DescribeTicketsReq{}
}

func (p *DescribeTicketsReq) InitDefault() {
}

var DescribeTicketsReq_SearchParam_DEFAULT *TicketSearchParam

func (p *DescribeTicketsReq) GetSearchParam() (v *TicketSearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeTicketsReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeTicketsReq_PageNumber_DEFAULT int32

func (p *DescribeTicketsReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeTicketsReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeTicketsReq_PageSize_DEFAULT int32

func (p *DescribeTicketsReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeTicketsReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeTicketsReq_SortBy_DEFAULT SortBy

func (p *DescribeTicketsReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeTicketsReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeTicketsReq_OrderBy_DEFAULT OrderByForTicket

func (p *DescribeTicketsReq) GetOrderBy() (v OrderByForTicket) {
	if !p.IsSetOrderBy() {
		return DescribeTicketsReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeTicketsReq_ListType_DEFAULT TicketListType

func (p *DescribeTicketsReq) GetListType() (v TicketListType) {
	if !p.IsSetListType() {
		return DescribeTicketsReq_ListType_DEFAULT
	}
	return *p.ListType
}

var DescribeTicketsReq_CreateFrom_DEFAULT string

func (p *DescribeTicketsReq) GetCreateFrom() (v string) {
	if !p.IsSetCreateFrom() {
		return DescribeTicketsReq_CreateFrom_DEFAULT
	}
	return *p.CreateFrom
}
func (p *DescribeTicketsReq) SetSearchParam(val *TicketSearchParam) {
	p.SearchParam = val
}
func (p *DescribeTicketsReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeTicketsReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeTicketsReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeTicketsReq) SetOrderBy(val *OrderByForTicket) {
	p.OrderBy = val
}
func (p *DescribeTicketsReq) SetListType(val *TicketListType) {
	p.ListType = val
}
func (p *DescribeTicketsReq) SetCreateFrom(val *string) {
	p.CreateFrom = val
}

var fieldIDToName_DescribeTicketsReq = map[int16]string{
	1: "SearchParam",
	2: "PageNumber",
	3: "PageSize",
	4: "SortBy",
	5: "OrderBy",
	6: "ListType",
	7: "CreateFrom",
}

func (p *DescribeTicketsReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeTicketsReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeTicketsReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeTicketsReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeTicketsReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeTicketsReq) IsSetListType() bool {
	return p.ListType != nil
}

func (p *DescribeTicketsReq) IsSetCreateFrom() bool {
	return p.CreateFrom != nil
}

func (p *DescribeTicketsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketsReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTicketsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeTicketsReq) ReadField1(iprot thrift.TProtocol) error {
	_field := NewTicketSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeTicketsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeTicketsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeTicketsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeTicketsReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *OrderByForTicket
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForTicket(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeTicketsReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *TicketListType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TicketListType(v)
		_field = &tmp
	}
	p.ListType = _field
	return nil
}
func (p *DescribeTicketsReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateFrom = _field
	return nil
}

func (p *DescribeTicketsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTicketsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTicketsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTicketsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTicketsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTicketsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeTicketsReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeTicketsReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetListType() {
		if err = oprot.WriteFieldBegin("ListType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.ListType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeTicketsReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateFrom() {
		if err = oprot.WriteFieldBegin("CreateFrom", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateFrom); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeTicketsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTicketsReq(%+v)", *p)

}

func (p *DescribeTicketsReq) DeepEqual(ano *DescribeTicketsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field2DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field4DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field5DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field6DeepEqual(ano.ListType) {
		return false
	}
	if !p.Field7DeepEqual(ano.CreateFrom) {
		return false
	}
	return true
}

func (p *DescribeTicketsReq) Field1DeepEqual(src *TicketSearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeTicketsReq) Field2DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeTicketsReq) Field3DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeTicketsReq) Field4DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeTicketsReq) Field5DeepEqual(src *OrderByForTicket) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeTicketsReq) Field6DeepEqual(src *TicketListType) bool {

	if p.ListType == src {
		return true
	} else if p.ListType == nil || src == nil {
		return false
	}
	if *p.ListType != *src {
		return false
	}
	return true
}
func (p *DescribeTicketsReq) Field7DeepEqual(src *string) bool {

	if p.CreateFrom == src {
		return true
	} else if p.CreateFrom == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateFrom, *src) != 0 {
		return false
	}
	return true
}

type TicketSearchParam struct {
	TicketId     *string       `thrift:"TicketId,1,optional" frugal:"1,optional,string" json:"TicketId,omitempty"`
	TicketType   *TicketType   `thrift:"TicketType,2,optional" frugal:"2,optional,TicketType" json:"TicketType,omitempty"`
	TicketStatus *TicketStatus `thrift:"TicketStatus,3,optional" frugal:"3,optional,TicketStatus" json:"TicketStatus,omitempty"`
	CreateUserId *string       `thrift:"CreateUserId,4,optional" frugal:"4,optional,string" json:"CreateUserId,omitempty"`
	CreateUser   *string       `thrift:"CreateUser,5,optional" frugal:"5,optional,string" json:"CreateUser,omitempty"`
	InstanceId   *string       `thrift:"InstanceId,6,optional" frugal:"6,optional,string" json:"InstanceId,omitempty"`
	InstanceType *InstanceType `thrift:"InstanceType,7,optional" frugal:"7,optional,InstanceType" json:"InstanceType,omitempty"`
	Memo         *string       `thrift:"Memo,8,optional" frugal:"8,optional,string" json:"Memo,omitempty"`
	Title        *string       `thrift:"Title,9,optional" frugal:"9,optional,string" json:"Title,omitempty"`
}

func NewTicketSearchParam() *TicketSearchParam {
	return &TicketSearchParam{}
}

func (p *TicketSearchParam) InitDefault() {
}

var TicketSearchParam_TicketId_DEFAULT string

func (p *TicketSearchParam) GetTicketId() (v string) {
	if !p.IsSetTicketId() {
		return TicketSearchParam_TicketId_DEFAULT
	}
	return *p.TicketId
}

var TicketSearchParam_TicketType_DEFAULT TicketType

func (p *TicketSearchParam) GetTicketType() (v TicketType) {
	if !p.IsSetTicketType() {
		return TicketSearchParam_TicketType_DEFAULT
	}
	return *p.TicketType
}

var TicketSearchParam_TicketStatus_DEFAULT TicketStatus

func (p *TicketSearchParam) GetTicketStatus() (v TicketStatus) {
	if !p.IsSetTicketStatus() {
		return TicketSearchParam_TicketStatus_DEFAULT
	}
	return *p.TicketStatus
}

var TicketSearchParam_CreateUserId_DEFAULT string

func (p *TicketSearchParam) GetCreateUserId() (v string) {
	if !p.IsSetCreateUserId() {
		return TicketSearchParam_CreateUserId_DEFAULT
	}
	return *p.CreateUserId
}

var TicketSearchParam_CreateUser_DEFAULT string

func (p *TicketSearchParam) GetCreateUser() (v string) {
	if !p.IsSetCreateUser() {
		return TicketSearchParam_CreateUser_DEFAULT
	}
	return *p.CreateUser
}

var TicketSearchParam_InstanceId_DEFAULT string

func (p *TicketSearchParam) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return TicketSearchParam_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var TicketSearchParam_InstanceType_DEFAULT InstanceType

func (p *TicketSearchParam) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return TicketSearchParam_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var TicketSearchParam_Memo_DEFAULT string

func (p *TicketSearchParam) GetMemo() (v string) {
	if !p.IsSetMemo() {
		return TicketSearchParam_Memo_DEFAULT
	}
	return *p.Memo
}

var TicketSearchParam_Title_DEFAULT string

func (p *TicketSearchParam) GetTitle() (v string) {
	if !p.IsSetTitle() {
		return TicketSearchParam_Title_DEFAULT
	}
	return *p.Title
}
func (p *TicketSearchParam) SetTicketId(val *string) {
	p.TicketId = val
}
func (p *TicketSearchParam) SetTicketType(val *TicketType) {
	p.TicketType = val
}
func (p *TicketSearchParam) SetTicketStatus(val *TicketStatus) {
	p.TicketStatus = val
}
func (p *TicketSearchParam) SetCreateUserId(val *string) {
	p.CreateUserId = val
}
func (p *TicketSearchParam) SetCreateUser(val *string) {
	p.CreateUser = val
}
func (p *TicketSearchParam) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *TicketSearchParam) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *TicketSearchParam) SetMemo(val *string) {
	p.Memo = val
}
func (p *TicketSearchParam) SetTitle(val *string) {
	p.Title = val
}

var fieldIDToName_TicketSearchParam = map[int16]string{
	1: "TicketId",
	2: "TicketType",
	3: "TicketStatus",
	4: "CreateUserId",
	5: "CreateUser",
	6: "InstanceId",
	7: "InstanceType",
	8: "Memo",
	9: "Title",
}

func (p *TicketSearchParam) IsSetTicketId() bool {
	return p.TicketId != nil
}

func (p *TicketSearchParam) IsSetTicketType() bool {
	return p.TicketType != nil
}

func (p *TicketSearchParam) IsSetTicketStatus() bool {
	return p.TicketStatus != nil
}

func (p *TicketSearchParam) IsSetCreateUserId() bool {
	return p.CreateUserId != nil
}

func (p *TicketSearchParam) IsSetCreateUser() bool {
	return p.CreateUser != nil
}

func (p *TicketSearchParam) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *TicketSearchParam) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *TicketSearchParam) IsSetMemo() bool {
	return p.Memo != nil
}

func (p *TicketSearchParam) IsSetTitle() bool {
	return p.Title != nil
}

func (p *TicketSearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TicketSearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TicketSearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TicketSearchParam) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TicketId = _field
	return nil
}
func (p *TicketSearchParam) ReadField2(iprot thrift.TProtocol) error {

	var _field *TicketType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TicketType(v)
		_field = &tmp
	}
	p.TicketType = _field
	return nil
}
func (p *TicketSearchParam) ReadField3(iprot thrift.TProtocol) error {

	var _field *TicketStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TicketStatus(v)
		_field = &tmp
	}
	p.TicketStatus = _field
	return nil
}
func (p *TicketSearchParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUserId = _field
	return nil
}
func (p *TicketSearchParam) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreateUser = _field
	return nil
}
func (p *TicketSearchParam) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *TicketSearchParam) ReadField7(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *TicketSearchParam) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Memo = _field
	return nil
}
func (p *TicketSearchParam) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Title = _field
	return nil
}

func (p *TicketSearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TicketSearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("TicketSearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TicketSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTicketId() {
		if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TicketId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TicketSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTicketType() {
		if err = oprot.WriteFieldBegin("TicketType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TicketType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TicketSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTicketStatus() {
		if err = oprot.WriteFieldBegin("TicketStatus", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TicketStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TicketSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUserId() {
		if err = oprot.WriteFieldBegin("CreateUserId", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUserId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *TicketSearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateUser() {
		if err = oprot.WriteFieldBegin("CreateUser", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CreateUser); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *TicketSearchParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *TicketSearchParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *TicketSearchParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetMemo() {
		if err = oprot.WriteFieldBegin("Memo", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Memo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *TicketSearchParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetTitle() {
		if err = oprot.WriteFieldBegin("Title", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Title); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *TicketSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TicketSearchParam(%+v)", *p)

}

func (p *TicketSearchParam) DeepEqual(ano *TicketSearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TicketType) {
		return false
	}
	if !p.Field3DeepEqual(ano.TicketStatus) {
		return false
	}
	if !p.Field4DeepEqual(ano.CreateUserId) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateUser) {
		return false
	}
	if !p.Field6DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field7DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field8DeepEqual(ano.Memo) {
		return false
	}
	if !p.Field9DeepEqual(ano.Title) {
		return false
	}
	return true
}

func (p *TicketSearchParam) Field1DeepEqual(src *string) bool {

	if p.TicketId == src {
		return true
	} else if p.TicketId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TicketId, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketSearchParam) Field2DeepEqual(src *TicketType) bool {

	if p.TicketType == src {
		return true
	} else if p.TicketType == nil || src == nil {
		return false
	}
	if *p.TicketType != *src {
		return false
	}
	return true
}
func (p *TicketSearchParam) Field3DeepEqual(src *TicketStatus) bool {

	if p.TicketStatus == src {
		return true
	} else if p.TicketStatus == nil || src == nil {
		return false
	}
	if *p.TicketStatus != *src {
		return false
	}
	return true
}
func (p *TicketSearchParam) Field4DeepEqual(src *string) bool {

	if p.CreateUserId == src {
		return true
	} else if p.CreateUserId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUserId, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketSearchParam) Field5DeepEqual(src *string) bool {

	if p.CreateUser == src {
		return true
	} else if p.CreateUser == nil || src == nil {
		return false
	}
	if strings.Compare(*p.CreateUser, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketSearchParam) Field6DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketSearchParam) Field7DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *TicketSearchParam) Field8DeepEqual(src *string) bool {

	if p.Memo == src {
		return true
	} else if p.Memo == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Memo, *src) != 0 {
		return false
	}
	return true
}
func (p *TicketSearchParam) Field9DeepEqual(src *string) bool {

	if p.Title == src {
		return true
	} else if p.Title == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Title, *src) != 0 {
		return false
	}
	return true
}

type DescribeTicketsResp struct {
	Tickets []*TicketItem `thrift:"Tickets,1,required" frugal:"1,required,list<TicketItem>" json:"Tickets"`
	Total   int32         `thrift:"total,2,required" frugal:"2,required,i32" json:"total"`
}

func NewDescribeTicketsResp() *DescribeTicketsResp {
	return &DescribeTicketsResp{}
}

func (p *DescribeTicketsResp) InitDefault() {
}

func (p *DescribeTicketsResp) GetTickets() (v []*TicketItem) {
	return p.Tickets
}

func (p *DescribeTicketsResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeTicketsResp) SetTickets(val []*TicketItem) {
	p.Tickets = val
}
func (p *DescribeTicketsResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeTicketsResp = map[int16]string{
	1: "Tickets",
	2: "total",
}

func (p *DescribeTicketsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTickets bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTickets = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTickets {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTicketsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTicketsResp[fieldId]))
}

func (p *DescribeTicketsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TicketItem, 0, size)
	values := make([]TicketItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tickets = _field
	return nil
}
func (p *DescribeTicketsResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeTicketsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTicketsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTicketsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Tickets", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tickets)); err != nil {
		return err
	}
	for _, v := range p.Tickets {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTicketsResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTicketsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTicketsResp(%+v)", *p)

}

func (p *DescribeTicketsResp) DeepEqual(ano *DescribeTicketsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Tickets) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeTicketsResp) Field1DeepEqual(src []*TicketItem) bool {

	if len(p.Tickets) != len(src) {
		return false
	}
	for i, v := range p.Tickets {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeTicketsResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}

type TicketItem struct {
	TicketId          string             `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
	TicketType        TicketType         `thrift:"TicketType,2,required" frugal:"2,required,TicketType" json:"TicketType"`
	TicketStatus      TicketStatus       `thrift:"TicketStatus,3,required" frugal:"3,required,TicketStatus" json:"TicketStatus"`
	TicketExecuteType ExecuteType        `thrift:"TicketExecuteType,4,required" frugal:"4,required,ExecuteType" json:"TicketExecuteType"`
	CreateUser        *CreateUserInfo    `thrift:"CreateUser,5,required" frugal:"5,required,CreateUserInfo" json:"CreateUser"`
	CurrentUserRole   string             `thrift:"CurrentUserRole,6,required" frugal:"6,required,string" json:"CurrentUserRole"`
	CurrentUser       *CurrentUserInfo   `thrift:"CurrentUser,7,required" frugal:"7,required,CurrentUserInfo" json:"CurrentUser"`
	CreateTime        string             `thrift:"CreateTime,8,required" frugal:"8,required,string" json:"CreateTime"`
	UpdateTime        string             `thrift:"UpdateTime,9,required" frugal:"9,required,string" json:"UpdateTime"`
	Description       string             `thrift:"Description,10,required" frugal:"10,required,string" json:"Description"`
	InstanceType      InstanceType       `thrift:"InstanceType,11,required" frugal:"11,required,InstanceType" json:"InstanceType"`
	InstanceId        string             `thrift:"InstanceId,12,required" frugal:"12,required,string" json:"InstanceId"`
	Memo              string             `thrift:"Memo,13,required" frugal:"13,required,string" json:"Memo"`
	Title             string             `thrift:"Title,14,required" frugal:"14,required,string" json:"Title"`
	DBName            string             `thrift:"DBName,15,required" frugal:"15,required,string" json:"DBName"`
	DataArchiveConfig *DataArchiveConfig `thrift:"DataArchiveConfig,16,required" frugal:"16,required,DataArchiveConfig" json:"DataArchiveConfig"`
	AffectedRows      string             `thrift:"AffectedRows,17,required" frugal:"17,required,string" json:"AffectedRows"`
}

func NewTicketItem() *TicketItem {
	return &TicketItem{}
}

func (p *TicketItem) InitDefault() {
}

func (p *TicketItem) GetTicketId() (v string) {
	return p.TicketId
}

func (p *TicketItem) GetTicketType() (v TicketType) {
	return p.TicketType
}

func (p *TicketItem) GetTicketStatus() (v TicketStatus) {
	return p.TicketStatus
}

func (p *TicketItem) GetTicketExecuteType() (v ExecuteType) {
	return p.TicketExecuteType
}

var TicketItem_CreateUser_DEFAULT *CreateUserInfo

func (p *TicketItem) GetCreateUser() (v *CreateUserInfo) {
	if !p.IsSetCreateUser() {
		return TicketItem_CreateUser_DEFAULT
	}
	return p.CreateUser
}

func (p *TicketItem) GetCurrentUserRole() (v string) {
	return p.CurrentUserRole
}

var TicketItem_CurrentUser_DEFAULT *CurrentUserInfo

func (p *TicketItem) GetCurrentUser() (v *CurrentUserInfo) {
	if !p.IsSetCurrentUser() {
		return TicketItem_CurrentUser_DEFAULT
	}
	return p.CurrentUser
}

func (p *TicketItem) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *TicketItem) GetUpdateTime() (v string) {
	return p.UpdateTime
}

func (p *TicketItem) GetDescription() (v string) {
	return p.Description
}

func (p *TicketItem) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *TicketItem) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *TicketItem) GetMemo() (v string) {
	return p.Memo
}

func (p *TicketItem) GetTitle() (v string) {
	return p.Title
}

func (p *TicketItem) GetDBName() (v string) {
	return p.DBName
}

var TicketItem_DataArchiveConfig_DEFAULT *DataArchiveConfig

func (p *TicketItem) GetDataArchiveConfig() (v *DataArchiveConfig) {
	if !p.IsSetDataArchiveConfig() {
		return TicketItem_DataArchiveConfig_DEFAULT
	}
	return p.DataArchiveConfig
}

func (p *TicketItem) GetAffectedRows() (v string) {
	return p.AffectedRows
}
func (p *TicketItem) SetTicketId(val string) {
	p.TicketId = val
}
func (p *TicketItem) SetTicketType(val TicketType) {
	p.TicketType = val
}
func (p *TicketItem) SetTicketStatus(val TicketStatus) {
	p.TicketStatus = val
}
func (p *TicketItem) SetTicketExecuteType(val ExecuteType) {
	p.TicketExecuteType = val
}
func (p *TicketItem) SetCreateUser(val *CreateUserInfo) {
	p.CreateUser = val
}
func (p *TicketItem) SetCurrentUserRole(val string) {
	p.CurrentUserRole = val
}
func (p *TicketItem) SetCurrentUser(val *CurrentUserInfo) {
	p.CurrentUser = val
}
func (p *TicketItem) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *TicketItem) SetUpdateTime(val string) {
	p.UpdateTime = val
}
func (p *TicketItem) SetDescription(val string) {
	p.Description = val
}
func (p *TicketItem) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *TicketItem) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *TicketItem) SetMemo(val string) {
	p.Memo = val
}
func (p *TicketItem) SetTitle(val string) {
	p.Title = val
}
func (p *TicketItem) SetDBName(val string) {
	p.DBName = val
}
func (p *TicketItem) SetDataArchiveConfig(val *DataArchiveConfig) {
	p.DataArchiveConfig = val
}
func (p *TicketItem) SetAffectedRows(val string) {
	p.AffectedRows = val
}

var fieldIDToName_TicketItem = map[int16]string{
	1:  "TicketId",
	2:  "TicketType",
	3:  "TicketStatus",
	4:  "TicketExecuteType",
	5:  "CreateUser",
	6:  "CurrentUserRole",
	7:  "CurrentUser",
	8:  "CreateTime",
	9:  "UpdateTime",
	10: "Description",
	11: "InstanceType",
	12: "InstanceId",
	13: "Memo",
	14: "Title",
	15: "DBName",
	16: "DataArchiveConfig",
	17: "AffectedRows",
}

func (p *TicketItem) IsSetCreateUser() bool {
	return p.CreateUser != nil
}

func (p *TicketItem) IsSetCurrentUser() bool {
	return p.CurrentUser != nil
}

func (p *TicketItem) IsSetDataArchiveConfig() bool {
	return p.DataArchiveConfig != nil
}

func (p *TicketItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TicketItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false
	var issetTicketType bool = false
	var issetTicketStatus bool = false
	var issetTicketExecuteType bool = false
	var issetCreateUser bool = false
	var issetCurrentUserRole bool = false
	var issetCurrentUser bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false
	var issetDescription bool = false
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetMemo bool = false
	var issetTitle bool = false
	var issetDBName bool = false
	var issetDataArchiveConfig bool = false
	var issetAffectedRows bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketExecuteType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetCurrentUserRole = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetCurrentUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetMemo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetTitle = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetDBName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
				issetDataArchiveConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
				issetAffectedRows = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTicketType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTicketStatus {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTicketExecuteType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetCreateUser {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetCurrentUserRole {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetCurrentUser {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetDescription {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetMemo {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetTitle {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetDBName {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetDataArchiveConfig {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetAffectedRows {
		fieldId = 17
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TicketItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TicketItem[fieldId]))
}

func (p *TicketItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}
func (p *TicketItem) ReadField2(iprot thrift.TProtocol) error {

	var _field TicketType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TicketType(v)
	}
	p.TicketType = _field
	return nil
}
func (p *TicketItem) ReadField3(iprot thrift.TProtocol) error {

	var _field TicketStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TicketStatus(v)
	}
	p.TicketStatus = _field
	return nil
}
func (p *TicketItem) ReadField4(iprot thrift.TProtocol) error {

	var _field ExecuteType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ExecuteType(v)
	}
	p.TicketExecuteType = _field
	return nil
}
func (p *TicketItem) ReadField5(iprot thrift.TProtocol) error {
	_field := NewCreateUserInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CreateUser = _field
	return nil
}
func (p *TicketItem) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CurrentUserRole = _field
	return nil
}
func (p *TicketItem) ReadField7(iprot thrift.TProtocol) error {
	_field := NewCurrentUserInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CurrentUser = _field
	return nil
}
func (p *TicketItem) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *TicketItem) ReadField9(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *TicketItem) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *TicketItem) ReadField11(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *TicketItem) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *TicketItem) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Memo = _field
	return nil
}
func (p *TicketItem) ReadField14(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Title = _field
	return nil
}
func (p *TicketItem) ReadField15(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DBName = _field
	return nil
}
func (p *TicketItem) ReadField16(iprot thrift.TProtocol) error {
	_field := NewDataArchiveConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.DataArchiveConfig = _field
	return nil
}
func (p *TicketItem) ReadField17(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AffectedRows = _field
	return nil
}

func (p *TicketItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("TicketItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("TicketItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TicketItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TicketItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TicketType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *TicketItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketStatus", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TicketStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *TicketItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketExecuteType", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TicketExecuteType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *TicketItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateUser", thrift.STRUCT, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CreateUser.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *TicketItem) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CurrentUserRole", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CurrentUserRole); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *TicketItem) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CurrentUser", thrift.STRUCT, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CurrentUser.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *TicketItem) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *TicketItem) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *TicketItem) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *TicketItem) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *TicketItem) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *TicketItem) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Memo", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Memo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *TicketItem) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Title", thrift.STRING, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Title); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *TicketItem) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DBName", thrift.STRING, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DBName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *TicketItem) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DataArchiveConfig", thrift.STRUCT, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.DataArchiveConfig.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *TicketItem) writeField17(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AffectedRows", thrift.STRING, 17); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AffectedRows); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *TicketItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TicketItem(%+v)", *p)

}

func (p *TicketItem) DeepEqual(ano *TicketItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TicketType) {
		return false
	}
	if !p.Field3DeepEqual(ano.TicketStatus) {
		return false
	}
	if !p.Field4DeepEqual(ano.TicketExecuteType) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateUser) {
		return false
	}
	if !p.Field6DeepEqual(ano.CurrentUserRole) {
		return false
	}
	if !p.Field7DeepEqual(ano.CurrentUser) {
		return false
	}
	if !p.Field8DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.UpdateTime) {
		return false
	}
	if !p.Field10DeepEqual(ano.Description) {
		return false
	}
	if !p.Field11DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field12DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field13DeepEqual(ano.Memo) {
		return false
	}
	if !p.Field14DeepEqual(ano.Title) {
		return false
	}
	if !p.Field15DeepEqual(ano.DBName) {
		return false
	}
	if !p.Field16DeepEqual(ano.DataArchiveConfig) {
		return false
	}
	if !p.Field17DeepEqual(ano.AffectedRows) {
		return false
	}
	return true
}

func (p *TicketItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}
func (p *TicketItem) Field2DeepEqual(src TicketType) bool {

	if p.TicketType != src {
		return false
	}
	return true
}
func (p *TicketItem) Field3DeepEqual(src TicketStatus) bool {

	if p.TicketStatus != src {
		return false
	}
	return true
}
func (p *TicketItem) Field4DeepEqual(src ExecuteType) bool {

	if p.TicketExecuteType != src {
		return false
	}
	return true
}
func (p *TicketItem) Field5DeepEqual(src *CreateUserInfo) bool {

	if !p.CreateUser.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TicketItem) Field6DeepEqual(src string) bool {

	if strings.Compare(p.CurrentUserRole, src) != 0 {
		return false
	}
	return true
}
func (p *TicketItem) Field7DeepEqual(src *CurrentUserInfo) bool {

	if !p.CurrentUser.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TicketItem) Field8DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *TicketItem) Field9DeepEqual(src string) bool {

	if strings.Compare(p.UpdateTime, src) != 0 {
		return false
	}
	return true
}
func (p *TicketItem) Field10DeepEqual(src string) bool {

	if strings.Compare(p.Description, src) != 0 {
		return false
	}
	return true
}
func (p *TicketItem) Field11DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *TicketItem) Field12DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *TicketItem) Field13DeepEqual(src string) bool {

	if strings.Compare(p.Memo, src) != 0 {
		return false
	}
	return true
}
func (p *TicketItem) Field14DeepEqual(src string) bool {

	if strings.Compare(p.Title, src) != 0 {
		return false
	}
	return true
}
func (p *TicketItem) Field15DeepEqual(src string) bool {

	if strings.Compare(p.DBName, src) != 0 {
		return false
	}
	return true
}
func (p *TicketItem) Field16DeepEqual(src *DataArchiveConfig) bool {

	if !p.DataArchiveConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *TicketItem) Field17DeepEqual(src string) bool {

	if strings.Compare(p.AffectedRows, src) != 0 {
		return false
	}
	return true
}

type CreateUserInfo struct {
	CreateUserName string `thrift:"CreateUserName,1,required" frugal:"1,required,string" json:"CreateUserName"`
	CreateUserId   string `thrift:"CreateUserId,2,required" frugal:"2,required,string" json:"CreateUserId"`
}

func NewCreateUserInfo() *CreateUserInfo {
	return &CreateUserInfo{}
}

func (p *CreateUserInfo) InitDefault() {
}

func (p *CreateUserInfo) GetCreateUserName() (v string) {
	return p.CreateUserName
}

func (p *CreateUserInfo) GetCreateUserId() (v string) {
	return p.CreateUserId
}
func (p *CreateUserInfo) SetCreateUserName(val string) {
	p.CreateUserName = val
}
func (p *CreateUserInfo) SetCreateUserId(val string) {
	p.CreateUserId = val
}

var fieldIDToName_CreateUserInfo = map[int16]string{
	1: "CreateUserName",
	2: "CreateUserId",
}

func (p *CreateUserInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateUserInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCreateUserName bool = false
	var issetCreateUserId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateUserName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateUserId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCreateUserName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCreateUserId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateUserInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateUserInfo[fieldId]))
}

func (p *CreateUserInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateUserName = _field
	return nil
}
func (p *CreateUserInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateUserId = _field
	return nil
}

func (p *CreateUserInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateUserInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateUserInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateUserInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateUserName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateUserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateUserInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateUserId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateUserId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateUserInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateUserInfo(%+v)", *p)

}

func (p *CreateUserInfo) DeepEqual(ano *CreateUserInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CreateUserName) {
		return false
	}
	if !p.Field2DeepEqual(ano.CreateUserId) {
		return false
	}
	return true
}

func (p *CreateUserInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.CreateUserName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateUserInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CreateUserId, src) != 0 {
		return false
	}
	return true
}

type CurrentUserInfo struct {
	CurrentUserName string `thrift:"CurrentUserName,1,required" frugal:"1,required,string" json:"CurrentUserName"`
	CurrentUserId   string `thrift:"CurrentUserId,2,required" frugal:"2,required,string" json:"CurrentUserId"`
}

func NewCurrentUserInfo() *CurrentUserInfo {
	return &CurrentUserInfo{}
}

func (p *CurrentUserInfo) InitDefault() {
}

func (p *CurrentUserInfo) GetCurrentUserName() (v string) {
	return p.CurrentUserName
}

func (p *CurrentUserInfo) GetCurrentUserId() (v string) {
	return p.CurrentUserId
}
func (p *CurrentUserInfo) SetCurrentUserName(val string) {
	p.CurrentUserName = val
}
func (p *CurrentUserInfo) SetCurrentUserId(val string) {
	p.CurrentUserId = val
}

var fieldIDToName_CurrentUserInfo = map[int16]string{
	1: "CurrentUserName",
	2: "CurrentUserId",
}

func (p *CurrentUserInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CurrentUserInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCurrentUserName bool = false
	var issetCurrentUserId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCurrentUserName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCurrentUserId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCurrentUserName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCurrentUserId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CurrentUserInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CurrentUserInfo[fieldId]))
}

func (p *CurrentUserInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CurrentUserName = _field
	return nil
}
func (p *CurrentUserInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CurrentUserId = _field
	return nil
}

func (p *CurrentUserInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CurrentUserInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("CurrentUserInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CurrentUserInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CurrentUserName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CurrentUserName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CurrentUserInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CurrentUserId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CurrentUserId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CurrentUserInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CurrentUserInfo(%+v)", *p)

}

func (p *CurrentUserInfo) DeepEqual(ano *CurrentUserInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.CurrentUserName) {
		return false
	}
	if !p.Field2DeepEqual(ano.CurrentUserId) {
		return false
	}
	return true
}

func (p *CurrentUserInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.CurrentUserName, src) != 0 {
		return false
	}
	return true
}
func (p *CurrentUserInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CurrentUserId, src) != 0 {
		return false
	}
	return true
}

type DescribeTicketDetailReq struct {
	TicketId string `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
}

func NewDescribeTicketDetailReq() *DescribeTicketDetailReq {
	return &DescribeTicketDetailReq{}
}

func (p *DescribeTicketDetailReq) InitDefault() {
}

func (p *DescribeTicketDetailReq) GetTicketId() (v string) {
	return p.TicketId
}
func (p *DescribeTicketDetailReq) SetTicketId(val string) {
	p.TicketId = val
}

var fieldIDToName_DescribeTicketDetailReq = map[int16]string{
	1: "TicketId",
}

func (p *DescribeTicketDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTicketDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTicketDetailReq[fieldId]))
}

func (p *DescribeTicketDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}

func (p *DescribeTicketDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTicketDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTicketDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTicketDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTicketDetailReq(%+v)", *p)

}

func (p *DescribeTicketDetailReq) DeepEqual(ano *DescribeTicketDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	return true
}

func (p *DescribeTicketDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}

type DescribeTicketDetailResp struct {
	TicketId          string             `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
	TicketType        TicketType         `thrift:"TicketType,2,required" frugal:"2,required,TicketType" json:"TicketType"`
	TicketStatus      TicketStatus       `thrift:"TicketStatus,3,required" frugal:"3,required,TicketStatus" json:"TicketStatus"`
	TicketExecuteType ExecuteType        `thrift:"TicketExecuteType,4,required" frugal:"4,required,ExecuteType" json:"TicketExecuteType"`
	CreateUser        *CreateUserInfo    `thrift:"CreateUser,5,required" frugal:"5,required,CreateUserInfo" json:"CreateUser"`
	CurrentUser       *CurrentUserInfo   `thrift:"CurrentUser,6,required" frugal:"6,required,CurrentUserInfo" json:"CurrentUser"`
	CreateTime        string             `thrift:"CreateTime,7,required" frugal:"7,required,string" json:"CreateTime"`
	UpdateTime        string             `thrift:"UpdateTime,8,required" frugal:"8,required,string" json:"UpdateTime"`
	InstanceType      InstanceType       `thrift:"InstanceType,9,required" frugal:"9,required,InstanceType" json:"InstanceType"`
	InstanceId        string             `thrift:"InstanceId,10,required" frugal:"10,required,string" json:"InstanceId"`
	DbName            string             `thrift:"DbName,11,required" frugal:"11,required,string" json:"DbName"`
	SqlText           string             `thrift:"SqlText,12,required" frugal:"12,required,string" json:"SqlText"`
	Description       string             `thrift:"Description,13,required" frugal:"13,required,string" json:"Description"`
	ExecStartTime     int32              `thrift:"ExecStartTime,14,required" frugal:"14,required,i32" json:"ExecStartTime"`
	ExecEndTime       int32              `thrift:"ExecEndTime,15,required" frugal:"15,required,i32" json:"ExecEndTime"`
	Progress          int32              `thrift:"Progress,16,required" frugal:"16,required,i32" json:"Progress"`
	BatchConfig       *BatchConfig       `thrift:"BatchConfig,17,optional" frugal:"17,optional,BatchConfig" json:"BatchConfig,omitempty"`
	ArchiveConfig     *DataArchiveConfig `thrift:"ArchiveConfig,18,optional" frugal:"18,optional,DataArchiveConfig" json:"ArchiveConfig,omitempty"`
	NextArchiveTime   *string            `thrift:"NextArchiveTime,19,optional" frugal:"19,optional,string" json:"NextArchiveTime,omitempty"`
	Memo              *string            `thrift:"Memo,20,optional" frugal:"20,optional,string" json:"Memo,omitempty"`
	Title             *string            `thrift:"Title,21,optional" frugal:"21,optional,string" json:"Title,omitempty"`
	WorkflowId        string             `thrift:"WorkflowId,22,required" frugal:"22,required,string" json:"WorkflowId"`
	TicketConfig      string             `thrift:"TicketConfig,23,required" frugal:"23,required,string" json:"TicketConfig"`
	TaskId            string             `thrift:"TaskId,24,required" frugal:"24,required,string" json:"TaskId"`
	Submitted         int32              `thrift:"Submitted,25,required" frugal:"25,required,i32" json:"Submitted"`
}

func NewDescribeTicketDetailResp() *DescribeTicketDetailResp {
	return &DescribeTicketDetailResp{}
}

func (p *DescribeTicketDetailResp) InitDefault() {
}

func (p *DescribeTicketDetailResp) GetTicketId() (v string) {
	return p.TicketId
}

func (p *DescribeTicketDetailResp) GetTicketType() (v TicketType) {
	return p.TicketType
}

func (p *DescribeTicketDetailResp) GetTicketStatus() (v TicketStatus) {
	return p.TicketStatus
}

func (p *DescribeTicketDetailResp) GetTicketExecuteType() (v ExecuteType) {
	return p.TicketExecuteType
}

var DescribeTicketDetailResp_CreateUser_DEFAULT *CreateUserInfo

func (p *DescribeTicketDetailResp) GetCreateUser() (v *CreateUserInfo) {
	if !p.IsSetCreateUser() {
		return DescribeTicketDetailResp_CreateUser_DEFAULT
	}
	return p.CreateUser
}

var DescribeTicketDetailResp_CurrentUser_DEFAULT *CurrentUserInfo

func (p *DescribeTicketDetailResp) GetCurrentUser() (v *CurrentUserInfo) {
	if !p.IsSetCurrentUser() {
		return DescribeTicketDetailResp_CurrentUser_DEFAULT
	}
	return p.CurrentUser
}

func (p *DescribeTicketDetailResp) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *DescribeTicketDetailResp) GetUpdateTime() (v string) {
	return p.UpdateTime
}

func (p *DescribeTicketDetailResp) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeTicketDetailResp) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeTicketDetailResp) GetDbName() (v string) {
	return p.DbName
}

func (p *DescribeTicketDetailResp) GetSqlText() (v string) {
	return p.SqlText
}

func (p *DescribeTicketDetailResp) GetDescription() (v string) {
	return p.Description
}

func (p *DescribeTicketDetailResp) GetExecStartTime() (v int32) {
	return p.ExecStartTime
}

func (p *DescribeTicketDetailResp) GetExecEndTime() (v int32) {
	return p.ExecEndTime
}

func (p *DescribeTicketDetailResp) GetProgress() (v int32) {
	return p.Progress
}

var DescribeTicketDetailResp_BatchConfig_DEFAULT *BatchConfig

func (p *DescribeTicketDetailResp) GetBatchConfig() (v *BatchConfig) {
	if !p.IsSetBatchConfig() {
		return DescribeTicketDetailResp_BatchConfig_DEFAULT
	}
	return p.BatchConfig
}

var DescribeTicketDetailResp_ArchiveConfig_DEFAULT *DataArchiveConfig

func (p *DescribeTicketDetailResp) GetArchiveConfig() (v *DataArchiveConfig) {
	if !p.IsSetArchiveConfig() {
		return DescribeTicketDetailResp_ArchiveConfig_DEFAULT
	}
	return p.ArchiveConfig
}

var DescribeTicketDetailResp_NextArchiveTime_DEFAULT string

func (p *DescribeTicketDetailResp) GetNextArchiveTime() (v string) {
	if !p.IsSetNextArchiveTime() {
		return DescribeTicketDetailResp_NextArchiveTime_DEFAULT
	}
	return *p.NextArchiveTime
}

var DescribeTicketDetailResp_Memo_DEFAULT string

func (p *DescribeTicketDetailResp) GetMemo() (v string) {
	if !p.IsSetMemo() {
		return DescribeTicketDetailResp_Memo_DEFAULT
	}
	return *p.Memo
}

var DescribeTicketDetailResp_Title_DEFAULT string

func (p *DescribeTicketDetailResp) GetTitle() (v string) {
	if !p.IsSetTitle() {
		return DescribeTicketDetailResp_Title_DEFAULT
	}
	return *p.Title
}

func (p *DescribeTicketDetailResp) GetWorkflowId() (v string) {
	return p.WorkflowId
}

func (p *DescribeTicketDetailResp) GetTicketConfig() (v string) {
	return p.TicketConfig
}

func (p *DescribeTicketDetailResp) GetTaskId() (v string) {
	return p.TaskId
}

func (p *DescribeTicketDetailResp) GetSubmitted() (v int32) {
	return p.Submitted
}
func (p *DescribeTicketDetailResp) SetTicketId(val string) {
	p.TicketId = val
}
func (p *DescribeTicketDetailResp) SetTicketType(val TicketType) {
	p.TicketType = val
}
func (p *DescribeTicketDetailResp) SetTicketStatus(val TicketStatus) {
	p.TicketStatus = val
}
func (p *DescribeTicketDetailResp) SetTicketExecuteType(val ExecuteType) {
	p.TicketExecuteType = val
}
func (p *DescribeTicketDetailResp) SetCreateUser(val *CreateUserInfo) {
	p.CreateUser = val
}
func (p *DescribeTicketDetailResp) SetCurrentUser(val *CurrentUserInfo) {
	p.CurrentUser = val
}
func (p *DescribeTicketDetailResp) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *DescribeTicketDetailResp) SetUpdateTime(val string) {
	p.UpdateTime = val
}
func (p *DescribeTicketDetailResp) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeTicketDetailResp) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeTicketDetailResp) SetDbName(val string) {
	p.DbName = val
}
func (p *DescribeTicketDetailResp) SetSqlText(val string) {
	p.SqlText = val
}
func (p *DescribeTicketDetailResp) SetDescription(val string) {
	p.Description = val
}
func (p *DescribeTicketDetailResp) SetExecStartTime(val int32) {
	p.ExecStartTime = val
}
func (p *DescribeTicketDetailResp) SetExecEndTime(val int32) {
	p.ExecEndTime = val
}
func (p *DescribeTicketDetailResp) SetProgress(val int32) {
	p.Progress = val
}
func (p *DescribeTicketDetailResp) SetBatchConfig(val *BatchConfig) {
	p.BatchConfig = val
}
func (p *DescribeTicketDetailResp) SetArchiveConfig(val *DataArchiveConfig) {
	p.ArchiveConfig = val
}
func (p *DescribeTicketDetailResp) SetNextArchiveTime(val *string) {
	p.NextArchiveTime = val
}
func (p *DescribeTicketDetailResp) SetMemo(val *string) {
	p.Memo = val
}
func (p *DescribeTicketDetailResp) SetTitle(val *string) {
	p.Title = val
}
func (p *DescribeTicketDetailResp) SetWorkflowId(val string) {
	p.WorkflowId = val
}
func (p *DescribeTicketDetailResp) SetTicketConfig(val string) {
	p.TicketConfig = val
}
func (p *DescribeTicketDetailResp) SetTaskId(val string) {
	p.TaskId = val
}
func (p *DescribeTicketDetailResp) SetSubmitted(val int32) {
	p.Submitted = val
}

var fieldIDToName_DescribeTicketDetailResp = map[int16]string{
	1:  "TicketId",
	2:  "TicketType",
	3:  "TicketStatus",
	4:  "TicketExecuteType",
	5:  "CreateUser",
	6:  "CurrentUser",
	7:  "CreateTime",
	8:  "UpdateTime",
	9:  "InstanceType",
	10: "InstanceId",
	11: "DbName",
	12: "SqlText",
	13: "Description",
	14: "ExecStartTime",
	15: "ExecEndTime",
	16: "Progress",
	17: "BatchConfig",
	18: "ArchiveConfig",
	19: "NextArchiveTime",
	20: "Memo",
	21: "Title",
	22: "WorkflowId",
	23: "TicketConfig",
	24: "TaskId",
	25: "Submitted",
}

func (p *DescribeTicketDetailResp) IsSetCreateUser() bool {
	return p.CreateUser != nil
}

func (p *DescribeTicketDetailResp) IsSetCurrentUser() bool {
	return p.CurrentUser != nil
}

func (p *DescribeTicketDetailResp) IsSetBatchConfig() bool {
	return p.BatchConfig != nil
}

func (p *DescribeTicketDetailResp) IsSetArchiveConfig() bool {
	return p.ArchiveConfig != nil
}

func (p *DescribeTicketDetailResp) IsSetNextArchiveTime() bool {
	return p.NextArchiveTime != nil
}

func (p *DescribeTicketDetailResp) IsSetMemo() bool {
	return p.Memo != nil
}

func (p *DescribeTicketDetailResp) IsSetTitle() bool {
	return p.Title != nil
}

func (p *DescribeTicketDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false
	var issetTicketType bool = false
	var issetTicketStatus bool = false
	var issetTicketExecuteType bool = false
	var issetCreateUser bool = false
	var issetCurrentUser bool = false
	var issetCreateTime bool = false
	var issetUpdateTime bool = false
	var issetInstanceType bool = false
	var issetInstanceId bool = false
	var issetDbName bool = false
	var issetSqlText bool = false
	var issetDescription bool = false
	var issetExecStartTime bool = false
	var issetExecEndTime bool = false
	var issetProgress bool = false
	var issetWorkflowId bool = false
	var issetTicketConfig bool = false
	var issetTaskId bool = false
	var issetSubmitted bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketExecuteType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetCurrentUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
				issetDbName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
				issetSqlText = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
				issetDescription = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecStartTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField15(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField16(iprot); err != nil {
					goto ReadFieldError
				}
				issetProgress = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 17:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField17(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 18:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField18(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField19(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField20(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField21(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField22(iprot); err != nil {
					goto ReadFieldError
				}
				issetWorkflowId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField23(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketConfig = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField24(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField25(iprot); err != nil {
					goto ReadFieldError
				}
				issetSubmitted = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTicketType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetTicketStatus {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTicketExecuteType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetCreateUser {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetCurrentUser {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 8
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 9
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 10
		goto RequiredFieldNotSetError
	}

	if !issetDbName {
		fieldId = 11
		goto RequiredFieldNotSetError
	}

	if !issetSqlText {
		fieldId = 12
		goto RequiredFieldNotSetError
	}

	if !issetDescription {
		fieldId = 13
		goto RequiredFieldNotSetError
	}

	if !issetExecStartTime {
		fieldId = 14
		goto RequiredFieldNotSetError
	}

	if !issetExecEndTime {
		fieldId = 15
		goto RequiredFieldNotSetError
	}

	if !issetProgress {
		fieldId = 16
		goto RequiredFieldNotSetError
	}

	if !issetWorkflowId {
		fieldId = 22
		goto RequiredFieldNotSetError
	}

	if !issetTicketConfig {
		fieldId = 23
		goto RequiredFieldNotSetError
	}

	if !issetTaskId {
		fieldId = 24
		goto RequiredFieldNotSetError
	}

	if !issetSubmitted {
		fieldId = 25
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTicketDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTicketDetailResp[fieldId]))
}

func (p *DescribeTicketDetailResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField2(iprot thrift.TProtocol) error {

	var _field TicketType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TicketType(v)
	}
	p.TicketType = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField3(iprot thrift.TProtocol) error {

	var _field TicketStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = TicketStatus(v)
	}
	p.TicketStatus = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField4(iprot thrift.TProtocol) error {

	var _field ExecuteType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ExecuteType(v)
	}
	p.TicketExecuteType = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField5(iprot thrift.TProtocol) error {
	_field := NewCreateUserInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CreateUser = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField6(iprot thrift.TProtocol) error {
	_field := NewCurrentUserInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CurrentUser = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField7(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField8(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField9(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField11(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.DbName = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField12(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SqlText = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField13(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Description = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField14(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecStartTime = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField15(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecEndTime = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField16(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Progress = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField17(iprot thrift.TProtocol) error {
	_field := NewBatchConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BatchConfig = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField18(iprot thrift.TProtocol) error {
	_field := NewDataArchiveConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.ArchiveConfig = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField19(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NextArchiveTime = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField20(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Memo = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField21(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Title = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField22(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.WorkflowId = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField23(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketConfig = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField24(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *DescribeTicketDetailResp) ReadField25(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Submitted = _field
	return nil
}

func (p *DescribeTicketDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTicketDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
		if err = p.writeField15(oprot); err != nil {
			fieldId = 15
			goto WriteFieldError
		}
		if err = p.writeField16(oprot); err != nil {
			fieldId = 16
			goto WriteFieldError
		}
		if err = p.writeField17(oprot); err != nil {
			fieldId = 17
			goto WriteFieldError
		}
		if err = p.writeField18(oprot); err != nil {
			fieldId = 18
			goto WriteFieldError
		}
		if err = p.writeField19(oprot); err != nil {
			fieldId = 19
			goto WriteFieldError
		}
		if err = p.writeField20(oprot); err != nil {
			fieldId = 20
			goto WriteFieldError
		}
		if err = p.writeField21(oprot); err != nil {
			fieldId = 21
			goto WriteFieldError
		}
		if err = p.writeField22(oprot); err != nil {
			fieldId = 22
			goto WriteFieldError
		}
		if err = p.writeField23(oprot); err != nil {
			fieldId = 23
			goto WriteFieldError
		}
		if err = p.writeField24(oprot); err != nil {
			fieldId = 24
			goto WriteFieldError
		}
		if err = p.writeField25(oprot); err != nil {
			fieldId = 25
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TicketType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketStatus", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TicketStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketExecuteType", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.TicketExecuteType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateUser", thrift.STRUCT, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CreateUser.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CurrentUser", thrift.STRUCT, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.CurrentUser.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField9(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 9); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField11(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("DbName", thrift.STRING, 11); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.DbName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField12(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SqlText", thrift.STRING, 12); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SqlText); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField13(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Description", thrift.STRING, 13); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Description); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField14(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecStartTime", thrift.I32, 14); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ExecStartTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField15(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecEndTime", thrift.I32, 15); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ExecEndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 15 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField16(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Progress", thrift.I32, 16); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Progress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 16 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField17(oprot thrift.TProtocol) (err error) {
	if p.IsSetBatchConfig() {
		if err = oprot.WriteFieldBegin("BatchConfig", thrift.STRUCT, 17); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BatchConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 17 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField18(oprot thrift.TProtocol) (err error) {
	if p.IsSetArchiveConfig() {
		if err = oprot.WriteFieldBegin("ArchiveConfig", thrift.STRUCT, 18); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.ArchiveConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 18 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetNextArchiveTime() {
		if err = oprot.WriteFieldBegin("NextArchiveTime", thrift.STRING, 19); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NextArchiveTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 19 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetMemo() {
		if err = oprot.WriteFieldBegin("Memo", thrift.STRING, 20); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Memo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 20 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetTitle() {
		if err = oprot.WriteFieldBegin("Title", thrift.STRING, 21); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Title); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 21 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField22(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("WorkflowId", thrift.STRING, 22); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.WorkflowId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 22 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField23(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketConfig", thrift.STRING, 23); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketConfig); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 23 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField24(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 24); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 24 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) writeField25(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Submitted", thrift.I32, 25); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Submitted); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 25 end error: ", p), err)
}

func (p *DescribeTicketDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTicketDetailResp(%+v)", *p)

}

func (p *DescribeTicketDetailResp) DeepEqual(ano *DescribeTicketDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TicketType) {
		return false
	}
	if !p.Field3DeepEqual(ano.TicketStatus) {
		return false
	}
	if !p.Field4DeepEqual(ano.TicketExecuteType) {
		return false
	}
	if !p.Field5DeepEqual(ano.CreateUser) {
		return false
	}
	if !p.Field6DeepEqual(ano.CurrentUser) {
		return false
	}
	if !p.Field7DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.UpdateTime) {
		return false
	}
	if !p.Field9DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field10DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field11DeepEqual(ano.DbName) {
		return false
	}
	if !p.Field12DeepEqual(ano.SqlText) {
		return false
	}
	if !p.Field13DeepEqual(ano.Description) {
		return false
	}
	if !p.Field14DeepEqual(ano.ExecStartTime) {
		return false
	}
	if !p.Field15DeepEqual(ano.ExecEndTime) {
		return false
	}
	if !p.Field16DeepEqual(ano.Progress) {
		return false
	}
	if !p.Field17DeepEqual(ano.BatchConfig) {
		return false
	}
	if !p.Field18DeepEqual(ano.ArchiveConfig) {
		return false
	}
	if !p.Field19DeepEqual(ano.NextArchiveTime) {
		return false
	}
	if !p.Field20DeepEqual(ano.Memo) {
		return false
	}
	if !p.Field21DeepEqual(ano.Title) {
		return false
	}
	if !p.Field22DeepEqual(ano.WorkflowId) {
		return false
	}
	if !p.Field23DeepEqual(ano.TicketConfig) {
		return false
	}
	if !p.Field24DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field25DeepEqual(ano.Submitted) {
		return false
	}
	return true
}

func (p *DescribeTicketDetailResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field2DeepEqual(src TicketType) bool {

	if p.TicketType != src {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field3DeepEqual(src TicketStatus) bool {

	if p.TicketStatus != src {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field4DeepEqual(src ExecuteType) bool {

	if p.TicketExecuteType != src {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field5DeepEqual(src *CreateUserInfo) bool {

	if !p.CreateUser.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field6DeepEqual(src *CurrentUserInfo) bool {

	if !p.CurrentUser.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field7DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field8DeepEqual(src string) bool {

	if strings.Compare(p.UpdateTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field9DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field10DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field11DeepEqual(src string) bool {

	if strings.Compare(p.DbName, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field12DeepEqual(src string) bool {

	if strings.Compare(p.SqlText, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field13DeepEqual(src string) bool {

	if strings.Compare(p.Description, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field14DeepEqual(src int32) bool {

	if p.ExecStartTime != src {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field15DeepEqual(src int32) bool {

	if p.ExecEndTime != src {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field16DeepEqual(src int32) bool {

	if p.Progress != src {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field17DeepEqual(src *BatchConfig) bool {

	if !p.BatchConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field18DeepEqual(src *DataArchiveConfig) bool {

	if !p.ArchiveConfig.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field19DeepEqual(src *string) bool {

	if p.NextArchiveTime == src {
		return true
	} else if p.NextArchiveTime == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NextArchiveTime, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field20DeepEqual(src *string) bool {

	if p.Memo == src {
		return true
	} else if p.Memo == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Memo, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field21DeepEqual(src *string) bool {

	if p.Title == src {
		return true
	} else if p.Title == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Title, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field22DeepEqual(src string) bool {

	if strings.Compare(p.WorkflowId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field23DeepEqual(src string) bool {

	if strings.Compare(p.TicketConfig, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field24DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketDetailResp) Field25DeepEqual(src int32) bool {

	if p.Submitted != src {
		return false
	}
	return true
}

type PreCheckTicketReq struct {
	TicketId string `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
}

func NewPreCheckTicketReq() *PreCheckTicketReq {
	return &PreCheckTicketReq{}
}

func (p *PreCheckTicketReq) InitDefault() {
}

func (p *PreCheckTicketReq) GetTicketId() (v string) {
	return p.TicketId
}
func (p *PreCheckTicketReq) SetTicketId(val string) {
	p.TicketId = val
}

var fieldIDToName_PreCheckTicketReq = map[int16]string{
	1: "TicketId",
}

func (p *PreCheckTicketReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("PreCheckTicketReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PreCheckTicketReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PreCheckTicketReq[fieldId]))
}

func (p *PreCheckTicketReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}

func (p *PreCheckTicketReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("PreCheckTicketReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("PreCheckTicketReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PreCheckTicketReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PreCheckTicketReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PreCheckTicketReq(%+v)", *p)

}

func (p *PreCheckTicketReq) DeepEqual(ano *PreCheckTicketReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	return true
}

func (p *PreCheckTicketReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}

type PreCheckTicketResp struct {
	AllPass            bool         `thrift:"AllPass,1,required" frugal:"1,required,bool" json:"AllPass"`
	CheckItems         []*CheckItem `thrift:"CheckItems,2,required" frugal:"2,required,list<CheckItem>" json:"CheckItems"`
	EnableSubmitTicket bool         `thrift:"EnableSubmitTicket,3,required" frugal:"3,required,bool" json:"EnableSubmitTicket"`
	Step               int32        `thrift:"Step,4,required" frugal:"4,required,i32" json:"Step"`
	PrecheckFinished   bool         `thrift:"PrecheckFinished,5,required" frugal:"5,required,bool" json:"PrecheckFinished"`
	IsContainDDL       bool         `thrift:"IsContainDDL,6,required" frugal:"6,required,bool" json:"IsContainDDL"`
}

func NewPreCheckTicketResp() *PreCheckTicketResp {
	return &PreCheckTicketResp{}
}

func (p *PreCheckTicketResp) InitDefault() {
}

func (p *PreCheckTicketResp) GetAllPass() (v bool) {
	return p.AllPass
}

func (p *PreCheckTicketResp) GetCheckItems() (v []*CheckItem) {
	return p.CheckItems
}

func (p *PreCheckTicketResp) GetEnableSubmitTicket() (v bool) {
	return p.EnableSubmitTicket
}

func (p *PreCheckTicketResp) GetStep() (v int32) {
	return p.Step
}

func (p *PreCheckTicketResp) GetPrecheckFinished() (v bool) {
	return p.PrecheckFinished
}

func (p *PreCheckTicketResp) GetIsContainDDL() (v bool) {
	return p.IsContainDDL
}
func (p *PreCheckTicketResp) SetAllPass(val bool) {
	p.AllPass = val
}
func (p *PreCheckTicketResp) SetCheckItems(val []*CheckItem) {
	p.CheckItems = val
}
func (p *PreCheckTicketResp) SetEnableSubmitTicket(val bool) {
	p.EnableSubmitTicket = val
}
func (p *PreCheckTicketResp) SetStep(val int32) {
	p.Step = val
}
func (p *PreCheckTicketResp) SetPrecheckFinished(val bool) {
	p.PrecheckFinished = val
}
func (p *PreCheckTicketResp) SetIsContainDDL(val bool) {
	p.IsContainDDL = val
}

var fieldIDToName_PreCheckTicketResp = map[int16]string{
	1: "AllPass",
	2: "CheckItems",
	3: "EnableSubmitTicket",
	4: "Step",
	5: "PrecheckFinished",
	6: "IsContainDDL",
}

func (p *PreCheckTicketResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("PreCheckTicketResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetAllPass bool = false
	var issetCheckItems bool = false
	var issetEnableSubmitTicket bool = false
	var issetStep bool = false
	var issetPrecheckFinished bool = false
	var issetIsContainDDL bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllPass = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCheckItems = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableSubmitTicket = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetStep = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetPrecheckFinished = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsContainDDL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetAllPass {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCheckItems {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEnableSubmitTicket {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetStep {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPrecheckFinished {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetIsContainDDL {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PreCheckTicketResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PreCheckTicketResp[fieldId]))
}

func (p *PreCheckTicketResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllPass = _field
	return nil
}
func (p *PreCheckTicketResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*CheckItem, 0, size)
	values := make([]CheckItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CheckItems = _field
	return nil
}
func (p *PreCheckTicketResp) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EnableSubmitTicket = _field
	return nil
}
func (p *PreCheckTicketResp) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Step = _field
	return nil
}
func (p *PreCheckTicketResp) ReadField5(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PrecheckFinished = _field
	return nil
}
func (p *PreCheckTicketResp) ReadField6(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsContainDDL = _field
	return nil
}

func (p *PreCheckTicketResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("PreCheckTicketResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("PreCheckTicketResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PreCheckTicketResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllPass", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.AllPass); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PreCheckTicketResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CheckItems", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CheckItems)); err != nil {
		return err
	}
	for _, v := range p.CheckItems {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *PreCheckTicketResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableSubmitTicket", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.EnableSubmitTicket); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *PreCheckTicketResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Step", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Step); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *PreCheckTicketResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PrecheckFinished", thrift.BOOL, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.PrecheckFinished); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *PreCheckTicketResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsContainDDL", thrift.BOOL, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsContainDDL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *PreCheckTicketResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PreCheckTicketResp(%+v)", *p)

}

func (p *PreCheckTicketResp) DeepEqual(ano *PreCheckTicketResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.AllPass) {
		return false
	}
	if !p.Field2DeepEqual(ano.CheckItems) {
		return false
	}
	if !p.Field3DeepEqual(ano.EnableSubmitTicket) {
		return false
	}
	if !p.Field4DeepEqual(ano.Step) {
		return false
	}
	if !p.Field5DeepEqual(ano.PrecheckFinished) {
		return false
	}
	if !p.Field6DeepEqual(ano.IsContainDDL) {
		return false
	}
	return true
}

func (p *PreCheckTicketResp) Field1DeepEqual(src bool) bool {

	if p.AllPass != src {
		return false
	}
	return true
}
func (p *PreCheckTicketResp) Field2DeepEqual(src []*CheckItem) bool {

	if len(p.CheckItems) != len(src) {
		return false
	}
	for i, v := range p.CheckItems {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *PreCheckTicketResp) Field3DeepEqual(src bool) bool {

	if p.EnableSubmitTicket != src {
		return false
	}
	return true
}
func (p *PreCheckTicketResp) Field4DeepEqual(src int32) bool {

	if p.Step != src {
		return false
	}
	return true
}
func (p *PreCheckTicketResp) Field5DeepEqual(src bool) bool {

	if p.PrecheckFinished != src {
		return false
	}
	return true
}
func (p *PreCheckTicketResp) Field6DeepEqual(src bool) bool {

	if p.IsContainDDL != src {
		return false
	}
	return true
}

type CheckItem struct {
	Item     string         `thrift:"Item,1,required" frugal:"1,required,string" json:"Item"`
	Memo     string         `thrift:"Memo,2,required" frugal:"2,required,string" json:"Memo"`
	Status   PreCheckStatus `thrift:"Status,3,required" frugal:"3,required,PreCheckStatus" json:"Status"`
	ItemType ItemType       `thrift:"ItemType,4,required" frugal:"4,required,ItemType" json:"ItemType"`
}

func NewCheckItem() *CheckItem {
	return &CheckItem{}
}

func (p *CheckItem) InitDefault() {
}

func (p *CheckItem) GetItem() (v string) {
	return p.Item
}

func (p *CheckItem) GetMemo() (v string) {
	return p.Memo
}

func (p *CheckItem) GetStatus() (v PreCheckStatus) {
	return p.Status
}

func (p *CheckItem) GetItemType() (v ItemType) {
	return p.ItemType
}
func (p *CheckItem) SetItem(val string) {
	p.Item = val
}
func (p *CheckItem) SetMemo(val string) {
	p.Memo = val
}
func (p *CheckItem) SetStatus(val PreCheckStatus) {
	p.Status = val
}
func (p *CheckItem) SetItemType(val ItemType) {
	p.ItemType = val
}

var fieldIDToName_CheckItem = map[int16]string{
	1: "Item",
	2: "Memo",
	3: "Status",
	4: "ItemType",
}

func (p *CheckItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetItem bool = false
	var issetMemo bool = false
	var issetStatus bool = false
	var issetItemType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetItem = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMemo = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetItemType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetItem {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMemo {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetStatus {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetItemType {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CheckItem[fieldId]))
}

func (p *CheckItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Item = _field
	return nil
}
func (p *CheckItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Memo = _field
	return nil
}
func (p *CheckItem) ReadField3(iprot thrift.TProtocol) error {

	var _field PreCheckStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = PreCheckStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *CheckItem) ReadField4(iprot thrift.TProtocol) error {

	var _field ItemType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ItemType(v)
	}
	p.ItemType = _field
	return nil
}

func (p *CheckItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CheckItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("CheckItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Item", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Item); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CheckItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Memo", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Memo); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CheckItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CheckItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ItemType", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ItemType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CheckItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckItem(%+v)", *p)

}

func (p *CheckItem) DeepEqual(ano *CheckItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Item) {
		return false
	}
	if !p.Field2DeepEqual(ano.Memo) {
		return false
	}
	if !p.Field3DeepEqual(ano.Status) {
		return false
	}
	if !p.Field4DeepEqual(ano.ItemType) {
		return false
	}
	return true
}

func (p *CheckItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Item, src) != 0 {
		return false
	}
	return true
}
func (p *CheckItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Memo, src) != 0 {
		return false
	}
	return true
}
func (p *CheckItem) Field3DeepEqual(src PreCheckStatus) bool {

	if p.Status != src {
		return false
	}
	return true
}
func (p *CheckItem) Field4DeepEqual(src ItemType) bool {

	if p.ItemType != src {
		return false
	}
	return true
}

type WorkflowActionReq struct {
	TicketId   string         `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
	ActionType FlowActionType `thrift:"ActionType,2,required" frugal:"2,required,FlowActionType" json:"ActionType"`
	Memo       *string        `thrift:"Memo,3,optional" frugal:"3,optional,string" json:"Memo,omitempty"`
}

func NewWorkflowActionReq() *WorkflowActionReq {
	return &WorkflowActionReq{}
}

func (p *WorkflowActionReq) InitDefault() {
}

func (p *WorkflowActionReq) GetTicketId() (v string) {
	return p.TicketId
}

func (p *WorkflowActionReq) GetActionType() (v FlowActionType) {
	return p.ActionType
}

var WorkflowActionReq_Memo_DEFAULT string

func (p *WorkflowActionReq) GetMemo() (v string) {
	if !p.IsSetMemo() {
		return WorkflowActionReq_Memo_DEFAULT
	}
	return *p.Memo
}
func (p *WorkflowActionReq) SetTicketId(val string) {
	p.TicketId = val
}
func (p *WorkflowActionReq) SetActionType(val FlowActionType) {
	p.ActionType = val
}
func (p *WorkflowActionReq) SetMemo(val *string) {
	p.Memo = val
}

var fieldIDToName_WorkflowActionReq = map[int16]string{
	1: "TicketId",
	2: "ActionType",
	3: "Memo",
}

func (p *WorkflowActionReq) IsSetMemo() bool {
	return p.Memo != nil
}

func (p *WorkflowActionReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WorkflowActionReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false
	var issetActionType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetActionType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetActionType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WorkflowActionReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_WorkflowActionReq[fieldId]))
}

func (p *WorkflowActionReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}
func (p *WorkflowActionReq) ReadField2(iprot thrift.TProtocol) error {

	var _field FlowActionType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = FlowActionType(v)
	}
	p.ActionType = _field
	return nil
}
func (p *WorkflowActionReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Memo = _field
	return nil
}

func (p *WorkflowActionReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WorkflowActionReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("WorkflowActionReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WorkflowActionReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *WorkflowActionReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ActionType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ActionType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *WorkflowActionReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMemo() {
		if err = oprot.WriteFieldBegin("Memo", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Memo); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *WorkflowActionReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WorkflowActionReq(%+v)", *p)

}

func (p *WorkflowActionReq) DeepEqual(ano *WorkflowActionReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	if !p.Field2DeepEqual(ano.ActionType) {
		return false
	}
	if !p.Field3DeepEqual(ano.Memo) {
		return false
	}
	return true
}

func (p *WorkflowActionReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}
func (p *WorkflowActionReq) Field2DeepEqual(src FlowActionType) bool {

	if p.ActionType != src {
		return false
	}
	return true
}
func (p *WorkflowActionReq) Field3DeepEqual(src *string) bool {

	if p.Memo == src {
		return true
	} else if p.Memo == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Memo, *src) != 0 {
		return false
	}
	return true
}

type CancelTicketReq struct {
	TicketId string `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
}

func NewCancelTicketReq() *CancelTicketReq {
	return &CancelTicketReq{}
}

func (p *CancelTicketReq) InitDefault() {
}

func (p *CancelTicketReq) GetTicketId() (v string) {
	return p.TicketId
}
func (p *CancelTicketReq) SetTicketId(val string) {
	p.TicketId = val
}

var fieldIDToName_CancelTicketReq = map[int16]string{
	1: "TicketId",
}

func (p *CancelTicketReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CancelTicketReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CancelTicketReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CancelTicketReq[fieldId]))
}

func (p *CancelTicketReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}

func (p *CancelTicketReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CancelTicketReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CancelTicketReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CancelTicketReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CancelTicketReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CancelTicketReq(%+v)", *p)

}

func (p *CancelTicketReq) DeepEqual(ano *CancelTicketReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	return true
}

func (p *CancelTicketReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}

type CancelTicketResp struct {
}

func NewCancelTicketResp() *CancelTicketResp {
	return &CancelTicketResp{}
}

func (p *CancelTicketResp) InitDefault() {
}

var fieldIDToName_CancelTicketResp = map[int16]string{}

func (p *CancelTicketResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CancelTicketResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CancelTicketResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CancelTicketResp")

	if err = oprot.WriteStructBegin("CancelTicketResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CancelTicketResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CancelTicketResp(%+v)", *p)

}

func (p *CancelTicketResp) DeepEqual(ano *CancelTicketResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeWorkflowReq struct {
	TicketId string `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
}

func NewDescribeWorkflowReq() *DescribeWorkflowReq {
	return &DescribeWorkflowReq{}
}

func (p *DescribeWorkflowReq) InitDefault() {
}

func (p *DescribeWorkflowReq) GetTicketId() (v string) {
	return p.TicketId
}
func (p *DescribeWorkflowReq) SetTicketId(val string) {
	p.TicketId = val
}

var fieldIDToName_DescribeWorkflowReq = map[int16]string{
	1: "TicketId",
}

func (p *DescribeWorkflowReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeWorkflowReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeWorkflowReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeWorkflowReq[fieldId]))
}

func (p *DescribeWorkflowReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}

func (p *DescribeWorkflowReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeWorkflowReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeWorkflowReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeWorkflowReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeWorkflowReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeWorkflowReq(%+v)", *p)

}

func (p *DescribeWorkflowReq) DeepEqual(ano *DescribeWorkflowReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	return true
}

func (p *DescribeWorkflowReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}

type DescribeWorkflowResp struct {
	FlowNodes []*WorkflowNode `thrift:"FlowNodes,1,required" frugal:"1,required,list<WorkflowNode>" json:"FlowNodes"`
	Code      ErrCode         `thrift:"Code,2,required" frugal:"2,required,ErrCode" json:"Code"`
	ErrMsg    string          `thrift:"ErrMsg,3,required" frugal:"3,required,string" json:"ErrMsg"`
}

func NewDescribeWorkflowResp() *DescribeWorkflowResp {
	return &DescribeWorkflowResp{}
}

func (p *DescribeWorkflowResp) InitDefault() {
}

func (p *DescribeWorkflowResp) GetFlowNodes() (v []*WorkflowNode) {
	return p.FlowNodes
}

func (p *DescribeWorkflowResp) GetCode() (v ErrCode) {
	return p.Code
}

func (p *DescribeWorkflowResp) GetErrMsg() (v string) {
	return p.ErrMsg
}
func (p *DescribeWorkflowResp) SetFlowNodes(val []*WorkflowNode) {
	p.FlowNodes = val
}
func (p *DescribeWorkflowResp) SetCode(val ErrCode) {
	p.Code = val
}
func (p *DescribeWorkflowResp) SetErrMsg(val string) {
	p.ErrMsg = val
}

var fieldIDToName_DescribeWorkflowResp = map[int16]string{
	1: "FlowNodes",
	2: "Code",
	3: "ErrMsg",
}

func (p *DescribeWorkflowResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeWorkflowResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFlowNodes bool = false
	var issetCode bool = false
	var issetErrMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFlowNodes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFlowNodes {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCode {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetErrMsg {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeWorkflowResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeWorkflowResp[fieldId]))
}

func (p *DescribeWorkflowResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*WorkflowNode, 0, size)
	values := make([]WorkflowNode, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FlowNodes = _field
	return nil
}
func (p *DescribeWorkflowResp) ReadField2(iprot thrift.TProtocol) error {

	var _field ErrCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ErrCode(v)
	}
	p.Code = _field
	return nil
}
func (p *DescribeWorkflowResp) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrMsg = _field
	return nil
}

func (p *DescribeWorkflowResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeWorkflowResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeWorkflowResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeWorkflowResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FlowNodes", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FlowNodes)); err != nil {
		return err
	}
	for _, v := range p.FlowNodes {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeWorkflowResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Code", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Code)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeWorkflowResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrMsg", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeWorkflowResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeWorkflowResp(%+v)", *p)

}

func (p *DescribeWorkflowResp) DeepEqual(ano *DescribeWorkflowResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FlowNodes) {
		return false
	}
	if !p.Field2DeepEqual(ano.Code) {
		return false
	}
	if !p.Field3DeepEqual(ano.ErrMsg) {
		return false
	}
	return true
}

func (p *DescribeWorkflowResp) Field1DeepEqual(src []*WorkflowNode) bool {

	if len(p.FlowNodes) != len(src) {
		return false
	}
	for i, v := range p.FlowNodes {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeWorkflowResp) Field2DeepEqual(src ErrCode) bool {

	if p.Code != src {
		return false
	}
	return true
}
func (p *DescribeWorkflowResp) Field3DeepEqual(src string) bool {

	if strings.Compare(p.ErrMsg, src) != 0 {
		return false
	}
	return true
}

type DescribeApprovalFlowLogsReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	ScenesType   ScenesType   `thrift:"ScenesType,3,required" frugal:"3,required,ScenesType" json:"ScenesType"`
}

func NewDescribeApprovalFlowLogsReq() *DescribeApprovalFlowLogsReq {
	return &DescribeApprovalFlowLogsReq{}
}

func (p *DescribeApprovalFlowLogsReq) InitDefault() {
}

func (p *DescribeApprovalFlowLogsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeApprovalFlowLogsReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *DescribeApprovalFlowLogsReq) GetScenesType() (v ScenesType) {
	return p.ScenesType
}
func (p *DescribeApprovalFlowLogsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeApprovalFlowLogsReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeApprovalFlowLogsReq) SetScenesType(val ScenesType) {
	p.ScenesType = val
}

var fieldIDToName_DescribeApprovalFlowLogsReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "ScenesType",
}

func (p *DescribeApprovalFlowLogsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeApprovalFlowLogsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetScenesType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetScenesType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetScenesType {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeApprovalFlowLogsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeApprovalFlowLogsReq[fieldId]))
}

func (p *DescribeApprovalFlowLogsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeApprovalFlowLogsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeApprovalFlowLogsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field ScenesType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ScenesType(v)
	}
	p.ScenesType = _field
	return nil
}

func (p *DescribeApprovalFlowLogsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeApprovalFlowLogsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeApprovalFlowLogsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeApprovalFlowLogsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeApprovalFlowLogsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeApprovalFlowLogsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ScenesType", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ScenesType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeApprovalFlowLogsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeApprovalFlowLogsReq(%+v)", *p)

}

func (p *DescribeApprovalFlowLogsReq) DeepEqual(ano *DescribeApprovalFlowLogsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.ScenesType) {
		return false
	}
	return true
}

func (p *DescribeApprovalFlowLogsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeApprovalFlowLogsReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeApprovalFlowLogsReq) Field3DeepEqual(src ScenesType) bool {

	if p.ScenesType != src {
		return false
	}
	return true
}

type DescribeApprovalFlowLogsResp struct {
	FlowNodes []*WorkflowNode `thrift:"FlowNodes,1,required" frugal:"1,required,list<WorkflowNode>" json:"FlowNodes"`
}

func NewDescribeApprovalFlowLogsResp() *DescribeApprovalFlowLogsResp {
	return &DescribeApprovalFlowLogsResp{}
}

func (p *DescribeApprovalFlowLogsResp) InitDefault() {
}

func (p *DescribeApprovalFlowLogsResp) GetFlowNodes() (v []*WorkflowNode) {
	return p.FlowNodes
}
func (p *DescribeApprovalFlowLogsResp) SetFlowNodes(val []*WorkflowNode) {
	p.FlowNodes = val
}

var fieldIDToName_DescribeApprovalFlowLogsResp = map[int16]string{
	1: "FlowNodes",
}

func (p *DescribeApprovalFlowLogsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeApprovalFlowLogsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetFlowNodes bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetFlowNodes = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetFlowNodes {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeApprovalFlowLogsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeApprovalFlowLogsResp[fieldId]))
}

func (p *DescribeApprovalFlowLogsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*WorkflowNode, 0, size)
	values := make([]WorkflowNode, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FlowNodes = _field
	return nil
}

func (p *DescribeApprovalFlowLogsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeApprovalFlowLogsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeApprovalFlowLogsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeApprovalFlowLogsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("FlowNodes", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FlowNodes)); err != nil {
		return err
	}
	for _, v := range p.FlowNodes {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeApprovalFlowLogsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeApprovalFlowLogsResp(%+v)", *p)

}

func (p *DescribeApprovalFlowLogsResp) DeepEqual(ano *DescribeApprovalFlowLogsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.FlowNodes) {
		return false
	}
	return true
}

func (p *DescribeApprovalFlowLogsResp) Field1DeepEqual(src []*WorkflowNode) bool {

	if len(p.FlowNodes) != len(src) {
		return false
	}
	for i, v := range p.FlowNodes {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type WorkflowNode struct {
	Status     FlowNodeStatus `thrift:"Status,1,required" frugal:"1,required,FlowNodeStatus" json:"Status"`
	UpdateTime string         `thrift:"UpdateTime,2,required" frugal:"2,required,string" json:"UpdateTime"`
	Operator   string         `thrift:"Operator,3,required" frugal:"3,required,string" json:"Operator"`
	NodeName   string         `thrift:"NodeName,4,required" frugal:"4,required,string" json:"NodeName"`
	Step       int64          `thrift:"step,5,required" frugal:"5,required,i64" json:"step"`
}

func NewWorkflowNode() *WorkflowNode {
	return &WorkflowNode{}
}

func (p *WorkflowNode) InitDefault() {
}

func (p *WorkflowNode) GetStatus() (v FlowNodeStatus) {
	return p.Status
}

func (p *WorkflowNode) GetUpdateTime() (v string) {
	return p.UpdateTime
}

func (p *WorkflowNode) GetOperator() (v string) {
	return p.Operator
}

func (p *WorkflowNode) GetNodeName() (v string) {
	return p.NodeName
}

func (p *WorkflowNode) GetStep() (v int64) {
	return p.Step
}
func (p *WorkflowNode) SetStatus(val FlowNodeStatus) {
	p.Status = val
}
func (p *WorkflowNode) SetUpdateTime(val string) {
	p.UpdateTime = val
}
func (p *WorkflowNode) SetOperator(val string) {
	p.Operator = val
}
func (p *WorkflowNode) SetNodeName(val string) {
	p.NodeName = val
}
func (p *WorkflowNode) SetStep(val int64) {
	p.Step = val
}

var fieldIDToName_WorkflowNode = map[int16]string{
	1: "Status",
	2: "UpdateTime",
	3: "Operator",
	4: "NodeName",
	5: "step",
}

func (p *WorkflowNode) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WorkflowNode")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetStatus bool = false
	var issetUpdateTime bool = false
	var issetOperator bool = false
	var issetNodeName bool = false
	var issetStep bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetUpdateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetOperator = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetNodeName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetStep = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetStatus {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetUpdateTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetOperator {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetNodeName {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetStep {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WorkflowNode[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_WorkflowNode[fieldId]))
}

func (p *WorkflowNode) ReadField1(iprot thrift.TProtocol) error {

	var _field FlowNodeStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = FlowNodeStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *WorkflowNode) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UpdateTime = _field
	return nil
}
func (p *WorkflowNode) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Operator = _field
	return nil
}
func (p *WorkflowNode) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.NodeName = _field
	return nil
}
func (p *WorkflowNode) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Step = _field
	return nil
}

func (p *WorkflowNode) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WorkflowNode")

	var fieldId int16
	if err = oprot.WriteStructBegin("WorkflowNode"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WorkflowNode) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Status", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *WorkflowNode) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("UpdateTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UpdateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *WorkflowNode) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Operator", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Operator); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *WorkflowNode) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("NodeName", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.NodeName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *WorkflowNode) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("step", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Step); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *WorkflowNode) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WorkflowNode(%+v)", *p)

}

func (p *WorkflowNode) DeepEqual(ano *WorkflowNode) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Status) {
		return false
	}
	if !p.Field2DeepEqual(ano.UpdateTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.Operator) {
		return false
	}
	if !p.Field4DeepEqual(ano.NodeName) {
		return false
	}
	if !p.Field5DeepEqual(ano.Step) {
		return false
	}
	return true
}

func (p *WorkflowNode) Field1DeepEqual(src FlowNodeStatus) bool {

	if p.Status != src {
		return false
	}
	return true
}
func (p *WorkflowNode) Field2DeepEqual(src string) bool {

	if strings.Compare(p.UpdateTime, src) != 0 {
		return false
	}
	return true
}
func (p *WorkflowNode) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Operator, src) != 0 {
		return false
	}
	return true
}
func (p *WorkflowNode) Field4DeepEqual(src string) bool {

	if strings.Compare(p.NodeName, src) != 0 {
		return false
	}
	return true
}
func (p *WorkflowNode) Field5DeepEqual(src int64) bool {

	if p.Step != src {
		return false
	}
	return true
}

type WorkflowActionResp struct {
	Code   ErrCode `thrift:"Code,1,required" frugal:"1,required,ErrCode" json:"Code"`
	ErrMsg string  `thrift:"ErrMsg,2,required" frugal:"2,required,string" json:"ErrMsg"`
}

func NewWorkflowActionResp() *WorkflowActionResp {
	return &WorkflowActionResp{}
}

func (p *WorkflowActionResp) InitDefault() {
}

func (p *WorkflowActionResp) GetCode() (v ErrCode) {
	return p.Code
}

func (p *WorkflowActionResp) GetErrMsg() (v string) {
	return p.ErrMsg
}
func (p *WorkflowActionResp) SetCode(val ErrCode) {
	p.Code = val
}
func (p *WorkflowActionResp) SetErrMsg(val string) {
	p.ErrMsg = val
}

var fieldIDToName_WorkflowActionResp = map[int16]string{
	1: "Code",
	2: "ErrMsg",
}

func (p *WorkflowActionResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WorkflowActionResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetErrMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetErrMsg {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_WorkflowActionResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_WorkflowActionResp[fieldId]))
}

func (p *WorkflowActionResp) ReadField1(iprot thrift.TProtocol) error {

	var _field ErrCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ErrCode(v)
	}
	p.Code = _field
	return nil
}
func (p *WorkflowActionResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrMsg = _field
	return nil
}

func (p *WorkflowActionResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("WorkflowActionResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("WorkflowActionResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *WorkflowActionResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Code)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *WorkflowActionResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrMsg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *WorkflowActionResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WorkflowActionResp(%+v)", *p)

}

func (p *WorkflowActionResp) DeepEqual(ano *WorkflowActionResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Code) {
		return false
	}
	if !p.Field2DeepEqual(ano.ErrMsg) {
		return false
	}
	return true
}

func (p *WorkflowActionResp) Field1DeepEqual(src ErrCode) bool {

	if p.Code != src {
		return false
	}
	return true
}
func (p *WorkflowActionResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ErrMsg, src) != 0 {
		return false
	}
	return true
}

type StopTicketReq struct {
	TicketId string `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
}

func NewStopTicketReq() *StopTicketReq {
	return &StopTicketReq{}
}

func (p *StopTicketReq) InitDefault() {
}

func (p *StopTicketReq) GetTicketId() (v string) {
	return p.TicketId
}
func (p *StopTicketReq) SetTicketId(val string) {
	p.TicketId = val
}

var fieldIDToName_StopTicketReq = map[int16]string{
	1: "TicketId",
}

func (p *StopTicketReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopTicketReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StopTicketReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_StopTicketReq[fieldId]))
}

func (p *StopTicketReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}

func (p *StopTicketReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopTicketReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("StopTicketReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StopTicketReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *StopTicketReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopTicketReq(%+v)", *p)

}

func (p *StopTicketReq) DeepEqual(ano *StopTicketReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	return true
}

func (p *StopTicketReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}

type StopTicketResp struct {
	Code    ErrCode `thrift:"Code,1,required" frugal:"1,required,ErrCode" json:"Code"`
	Message string  `thrift:"Message,2,required" frugal:"2,required,string" json:"Message"`
}

func NewStopTicketResp() *StopTicketResp {
	return &StopTicketResp{}
}

func (p *StopTicketResp) InitDefault() {
}

func (p *StopTicketResp) GetCode() (v ErrCode) {
	return p.Code
}

func (p *StopTicketResp) GetMessage() (v string) {
	return p.Message
}
func (p *StopTicketResp) SetCode(val ErrCode) {
	p.Code = val
}
func (p *StopTicketResp) SetMessage(val string) {
	p.Message = val
}

var fieldIDToName_StopTicketResp = map[int16]string{
	1: "Code",
	2: "Message",
}

func (p *StopTicketResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopTicketResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetMessage bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMessage {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StopTicketResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_StopTicketResp[fieldId]))
}

func (p *StopTicketResp) ReadField1(iprot thrift.TProtocol) error {

	var _field ErrCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ErrCode(v)
	}
	p.Code = _field
	return nil
}
func (p *StopTicketResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}

func (p *StopTicketResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopTicketResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("StopTicketResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StopTicketResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Code)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *StopTicketResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *StopTicketResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopTicketResp(%+v)", *p)

}

func (p *StopTicketResp) DeepEqual(ano *StopTicketResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Code) {
		return false
	}
	if !p.Field2DeepEqual(ano.Message) {
		return false
	}
	return true
}

func (p *StopTicketResp) Field1DeepEqual(src ErrCode) bool {

	if p.Code != src {
		return false
	}
	return true
}
func (p *StopTicketResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}

type DescribeTicketLogDetailReq struct {
	TicketId string `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
}

func NewDescribeTicketLogDetailReq() *DescribeTicketLogDetailReq {
	return &DescribeTicketLogDetailReq{}
}

func (p *DescribeTicketLogDetailReq) InitDefault() {
}

func (p *DescribeTicketLogDetailReq) GetTicketId() (v string) {
	return p.TicketId
}
func (p *DescribeTicketLogDetailReq) SetTicketId(val string) {
	p.TicketId = val
}

var fieldIDToName_DescribeTicketLogDetailReq = map[int16]string{
	1: "TicketId",
}

func (p *DescribeTicketLogDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketLogDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTicketLogDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTicketLogDetailReq[fieldId]))
}

func (p *DescribeTicketLogDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}

func (p *DescribeTicketLogDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketLogDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTicketLogDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTicketLogDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTicketLogDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTicketLogDetailReq(%+v)", *p)

}

func (p *DescribeTicketLogDetailReq) DeepEqual(ano *DescribeTicketLogDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	return true
}

func (p *DescribeTicketLogDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}

type DescribeTicketLogDetailResp struct {
	TicketExecLogs []*DescribeTicketLogItem `thrift:"TicketExecLogs,1,required" frugal:"1,required,list<DescribeTicketLogItem>" json:"TicketExecLogs"`
}

func NewDescribeTicketLogDetailResp() *DescribeTicketLogDetailResp {
	return &DescribeTicketLogDetailResp{}
}

func (p *DescribeTicketLogDetailResp) InitDefault() {
}

func (p *DescribeTicketLogDetailResp) GetTicketExecLogs() (v []*DescribeTicketLogItem) {
	return p.TicketExecLogs
}
func (p *DescribeTicketLogDetailResp) SetTicketExecLogs(val []*DescribeTicketLogItem) {
	p.TicketExecLogs = val
}

var fieldIDToName_DescribeTicketLogDetailResp = map[int16]string{
	1: "TicketExecLogs",
}

func (p *DescribeTicketLogDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketLogDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketExecLogs bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketExecLogs = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketExecLogs {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTicketLogDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTicketLogDetailResp[fieldId]))
}

func (p *DescribeTicketLogDetailResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*DescribeTicketLogItem, 0, size)
	values := make([]DescribeTicketLogItem, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TicketExecLogs = _field
	return nil
}

func (p *DescribeTicketLogDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketLogDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTicketLogDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTicketLogDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketExecLogs", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TicketExecLogs)); err != nil {
		return err
	}
	for _, v := range p.TicketExecLogs {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTicketLogDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTicketLogDetailResp(%+v)", *p)

}

func (p *DescribeTicketLogDetailResp) DeepEqual(ano *DescribeTicketLogDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketExecLogs) {
		return false
	}
	return true
}

func (p *DescribeTicketLogDetailResp) Field1DeepEqual(src []*DescribeTicketLogItem) bool {

	if len(p.TicketExecLogs) != len(src) {
		return false
	}
	for i, v := range p.TicketExecLogs {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type DescribeTicketLogItem struct {
	Message    string `thrift:"Message,1,required" frugal:"1,required,string" json:"Message"`
	CreateTime string `thrift:"CreateTime,2,required" frugal:"2,required,string" json:"CreateTime"`
	Progress   string `thrift:"Progress,3,required" frugal:"3,required,string" json:"Progress"`
	TenantId   string `thrift:"TenantId,4,required" frugal:"4,required,string" json:"TenantId"`
	InstanceId string `thrift:"InstanceId,5,required" frugal:"5,required,string" json:"InstanceId"`
	TicketId   string `thrift:"TicketId,6,required" frugal:"6,required,string" json:"TicketId"`
}

func NewDescribeTicketLogItem() *DescribeTicketLogItem {
	return &DescribeTicketLogItem{}
}

func (p *DescribeTicketLogItem) InitDefault() {
}

func (p *DescribeTicketLogItem) GetMessage() (v string) {
	return p.Message
}

func (p *DescribeTicketLogItem) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *DescribeTicketLogItem) GetProgress() (v string) {
	return p.Progress
}

func (p *DescribeTicketLogItem) GetTenantId() (v string) {
	return p.TenantId
}

func (p *DescribeTicketLogItem) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeTicketLogItem) GetTicketId() (v string) {
	return p.TicketId
}
func (p *DescribeTicketLogItem) SetMessage(val string) {
	p.Message = val
}
func (p *DescribeTicketLogItem) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *DescribeTicketLogItem) SetProgress(val string) {
	p.Progress = val
}
func (p *DescribeTicketLogItem) SetTenantId(val string) {
	p.TenantId = val
}
func (p *DescribeTicketLogItem) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeTicketLogItem) SetTicketId(val string) {
	p.TicketId = val
}

var fieldIDToName_DescribeTicketLogItem = map[int16]string{
	1: "Message",
	2: "CreateTime",
	3: "Progress",
	4: "TenantId",
	5: "InstanceId",
	6: "TicketId",
}

func (p *DescribeTicketLogItem) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketLogItem")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetMessage bool = false
	var issetCreateTime bool = false
	var issetProgress bool = false
	var issetTenantId bool = false
	var issetInstanceId bool = false
	var issetTicketId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetMessage = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetProgress = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetTenantId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetMessage {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetProgress {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetTenantId {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetTicketId {
		fieldId = 6
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeTicketLogItem[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeTicketLogItem[fieldId]))
}

func (p *DescribeTicketLogItem) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Message = _field
	return nil
}
func (p *DescribeTicketLogItem) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *DescribeTicketLogItem) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Progress = _field
	return nil
}
func (p *DescribeTicketLogItem) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TenantId = _field
	return nil
}
func (p *DescribeTicketLogItem) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeTicketLogItem) ReadField6(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}

func (p *DescribeTicketLogItem) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeTicketLogItem")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeTicketLogItem"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeTicketLogItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Message", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Message); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeTicketLogItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeTicketLogItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Progress", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Progress); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeTicketLogItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TenantId", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TenantId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeTicketLogItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeTicketLogItem) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeTicketLogItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeTicketLogItem(%+v)", *p)

}

func (p *DescribeTicketLogItem) DeepEqual(ano *DescribeTicketLogItem) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Message) {
		return false
	}
	if !p.Field2DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.Progress) {
		return false
	}
	if !p.Field4DeepEqual(ano.TenantId) {
		return false
	}
	if !p.Field5DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field6DeepEqual(ano.TicketId) {
		return false
	}
	return true
}

func (p *DescribeTicketLogItem) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Message, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketLogItem) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketLogItem) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Progress, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketLogItem) Field4DeepEqual(src string) bool {

	if strings.Compare(p.TenantId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketLogItem) Field5DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeTicketLogItem) Field6DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}

type SubmitTicketReq struct {
	TicketId        string           `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
	IsUnlock        bool             `thrift:"isUnlock,2,required" frugal:"2,required,bool" json:"isUnlock"`
	OnlineDDlConfig *OnlineDDlConfig `thrift:"OnlineDDlConfig,3,optional" frugal:"3,optional,OnlineDDlConfig" json:"OnlineDDlConfig,omitempty"`
}

func NewSubmitTicketReq() *SubmitTicketReq {
	return &SubmitTicketReq{}
}

func (p *SubmitTicketReq) InitDefault() {
}

func (p *SubmitTicketReq) GetTicketId() (v string) {
	return p.TicketId
}

func (p *SubmitTicketReq) GetIsUnlock() (v bool) {
	return p.IsUnlock
}

var SubmitTicketReq_OnlineDDlConfig_DEFAULT *OnlineDDlConfig

func (p *SubmitTicketReq) GetOnlineDDlConfig() (v *OnlineDDlConfig) {
	if !p.IsSetOnlineDDlConfig() {
		return SubmitTicketReq_OnlineDDlConfig_DEFAULT
	}
	return p.OnlineDDlConfig
}
func (p *SubmitTicketReq) SetTicketId(val string) {
	p.TicketId = val
}
func (p *SubmitTicketReq) SetIsUnlock(val bool) {
	p.IsUnlock = val
}
func (p *SubmitTicketReq) SetOnlineDDlConfig(val *OnlineDDlConfig) {
	p.OnlineDDlConfig = val
}

var fieldIDToName_SubmitTicketReq = map[int16]string{
	1: "TicketId",
	2: "isUnlock",
	3: "OnlineDDlConfig",
}

func (p *SubmitTicketReq) IsSetOnlineDDlConfig() bool {
	return p.OnlineDDlConfig != nil
}

func (p *SubmitTicketReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SubmitTicketReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false
	var issetIsUnlock bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsUnlock = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetIsUnlock {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SubmitTicketReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SubmitTicketReq[fieldId]))
}

func (p *SubmitTicketReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}
func (p *SubmitTicketReq) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsUnlock = _field
	return nil
}
func (p *SubmitTicketReq) ReadField3(iprot thrift.TProtocol) error {
	_field := NewOnlineDDlConfig()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.OnlineDDlConfig = _field
	return nil
}

func (p *SubmitTicketReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SubmitTicketReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SubmitTicketReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SubmitTicketReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SubmitTicketReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("isUnlock", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsUnlock); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SubmitTicketReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetOnlineDDlConfig() {
		if err = oprot.WriteFieldBegin("OnlineDDlConfig", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.OnlineDDlConfig.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SubmitTicketReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitTicketReq(%+v)", *p)

}

func (p *SubmitTicketReq) DeepEqual(ano *SubmitTicketReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	if !p.Field2DeepEqual(ano.IsUnlock) {
		return false
	}
	if !p.Field3DeepEqual(ano.OnlineDDlConfig) {
		return false
	}
	return true
}

func (p *SubmitTicketReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}
func (p *SubmitTicketReq) Field2DeepEqual(src bool) bool {

	if p.IsUnlock != src {
		return false
	}
	return true
}
func (p *SubmitTicketReq) Field3DeepEqual(src *OnlineDDlConfig) bool {

	if !p.OnlineDDlConfig.DeepEqual(src) {
		return false
	}
	return true
}

type OnlineDDlConfig struct {
	ChangeTableLockTimeout int32               `thrift:"ChangeTableLockTimeout,1,required" frugal:"1,required,i32" json:"ChangeTableLockTimeout"`
	ChangeTableRetryCount  int32               `thrift:"ChangeTableRetryCount,2,required" frugal:"2,required,i32" json:"ChangeTableRetryCount"`
	IsTableCopyDMSAuto     bool                `thrift:"IsTableCopyDMSAuto,3,required" frugal:"3,required,bool" json:"IsTableCopyDMSAuto"`
	TableCopySize          int32               `thrift:"TableCopySize,4,optional" frugal:"4,optional,i32" json:"TableCopySize,omitempty"`
	ChangeTableStrategy    ChangeTableStrategy `thrift:"ChangeTableStrategy,5,required" frugal:"5,required,ChangeTableStrategy" json:"ChangeTableStrategy"`
	IsExecuteImmediately   bool                `thrift:"IsExecuteImmediately,6,required" frugal:"6,required,bool" json:"IsExecuteImmediately"`
	ExecuteTime            int32               `thrift:"ExecuteTime,7,required" frugal:"7,required,i32" json:"ExecuteTime"`
}

func NewOnlineDDlConfig() *OnlineDDlConfig {
	return &OnlineDDlConfig{

		ChangeTableLockTimeout: 3,
		ChangeTableRetryCount:  5,
		IsTableCopyDMSAuto:     true,
		TableCopySize:          0,
		ChangeTableStrategy:    ChangeTableStrategy_DELETE,
		IsExecuteImmediately:   true,
		ExecuteTime:            0,
	}
}

func (p *OnlineDDlConfig) InitDefault() {
	p.ChangeTableLockTimeout = 3
	p.ChangeTableRetryCount = 5
	p.IsTableCopyDMSAuto = true
	p.TableCopySize = 0
	p.ChangeTableStrategy = ChangeTableStrategy_DELETE
	p.IsExecuteImmediately = true
	p.ExecuteTime = 0
}

func (p *OnlineDDlConfig) GetChangeTableLockTimeout() (v int32) {
	return p.ChangeTableLockTimeout
}

func (p *OnlineDDlConfig) GetChangeTableRetryCount() (v int32) {
	return p.ChangeTableRetryCount
}

func (p *OnlineDDlConfig) GetIsTableCopyDMSAuto() (v bool) {
	return p.IsTableCopyDMSAuto
}

var OnlineDDlConfig_TableCopySize_DEFAULT int32 = 0

func (p *OnlineDDlConfig) GetTableCopySize() (v int32) {
	if !p.IsSetTableCopySize() {
		return OnlineDDlConfig_TableCopySize_DEFAULT
	}
	return p.TableCopySize
}

func (p *OnlineDDlConfig) GetChangeTableStrategy() (v ChangeTableStrategy) {
	return p.ChangeTableStrategy
}

func (p *OnlineDDlConfig) GetIsExecuteImmediately() (v bool) {
	return p.IsExecuteImmediately
}

func (p *OnlineDDlConfig) GetExecuteTime() (v int32) {
	return p.ExecuteTime
}
func (p *OnlineDDlConfig) SetChangeTableLockTimeout(val int32) {
	p.ChangeTableLockTimeout = val
}
func (p *OnlineDDlConfig) SetChangeTableRetryCount(val int32) {
	p.ChangeTableRetryCount = val
}
func (p *OnlineDDlConfig) SetIsTableCopyDMSAuto(val bool) {
	p.IsTableCopyDMSAuto = val
}
func (p *OnlineDDlConfig) SetTableCopySize(val int32) {
	p.TableCopySize = val
}
func (p *OnlineDDlConfig) SetChangeTableStrategy(val ChangeTableStrategy) {
	p.ChangeTableStrategy = val
}
func (p *OnlineDDlConfig) SetIsExecuteImmediately(val bool) {
	p.IsExecuteImmediately = val
}
func (p *OnlineDDlConfig) SetExecuteTime(val int32) {
	p.ExecuteTime = val
}

var fieldIDToName_OnlineDDlConfig = map[int16]string{
	1: "ChangeTableLockTimeout",
	2: "ChangeTableRetryCount",
	3: "IsTableCopyDMSAuto",
	4: "TableCopySize",
	5: "ChangeTableStrategy",
	6: "IsExecuteImmediately",
	7: "ExecuteTime",
}

func (p *OnlineDDlConfig) IsSetTableCopySize() bool {
	return p.TableCopySize != OnlineDDlConfig_TableCopySize_DEFAULT
}

func (p *OnlineDDlConfig) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("OnlineDDlConfig")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetChangeTableLockTimeout bool = false
	var issetChangeTableRetryCount bool = false
	var issetIsTableCopyDMSAuto bool = false
	var issetChangeTableStrategy bool = false
	var issetIsExecuteImmediately bool = false
	var issetExecuteTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetChangeTableLockTimeout = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetChangeTableRetryCount = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsTableCopyDMSAuto = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetChangeTableStrategy = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsExecuteImmediately = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetExecuteTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetChangeTableLockTimeout {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetChangeTableRetryCount {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetIsTableCopyDMSAuto {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetChangeTableStrategy {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetIsExecuteImmediately {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetExecuteTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_OnlineDDlConfig[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_OnlineDDlConfig[fieldId]))
}

func (p *OnlineDDlConfig) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChangeTableLockTimeout = _field
	return nil
}
func (p *OnlineDDlConfig) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ChangeTableRetryCount = _field
	return nil
}
func (p *OnlineDDlConfig) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsTableCopyDMSAuto = _field
	return nil
}
func (p *OnlineDDlConfig) ReadField4(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TableCopySize = _field
	return nil
}
func (p *OnlineDDlConfig) ReadField5(iprot thrift.TProtocol) error {

	var _field ChangeTableStrategy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ChangeTableStrategy(v)
	}
	p.ChangeTableStrategy = _field
	return nil
}
func (p *OnlineDDlConfig) ReadField6(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsExecuteImmediately = _field
	return nil
}
func (p *OnlineDDlConfig) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ExecuteTime = _field
	return nil
}

func (p *OnlineDDlConfig) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("OnlineDDlConfig")

	var fieldId int16
	if err = oprot.WriteStructBegin("OnlineDDlConfig"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *OnlineDDlConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChangeTableLockTimeout", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ChangeTableLockTimeout); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *OnlineDDlConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChangeTableRetryCount", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ChangeTableRetryCount); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *OnlineDDlConfig) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsTableCopyDMSAuto", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsTableCopyDMSAuto); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *OnlineDDlConfig) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTableCopySize() {
		if err = oprot.WriteFieldBegin("TableCopySize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(p.TableCopySize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *OnlineDDlConfig) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ChangeTableStrategy", thrift.I32, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ChangeTableStrategy)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *OnlineDDlConfig) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsExecuteImmediately", thrift.BOOL, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsExecuteImmediately); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *OnlineDDlConfig) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ExecuteTime", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.ExecuteTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *OnlineDDlConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OnlineDDlConfig(%+v)", *p)

}

func (p *OnlineDDlConfig) DeepEqual(ano *OnlineDDlConfig) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ChangeTableLockTimeout) {
		return false
	}
	if !p.Field2DeepEqual(ano.ChangeTableRetryCount) {
		return false
	}
	if !p.Field3DeepEqual(ano.IsTableCopyDMSAuto) {
		return false
	}
	if !p.Field4DeepEqual(ano.TableCopySize) {
		return false
	}
	if !p.Field5DeepEqual(ano.ChangeTableStrategy) {
		return false
	}
	if !p.Field6DeepEqual(ano.IsExecuteImmediately) {
		return false
	}
	if !p.Field7DeepEqual(ano.ExecuteTime) {
		return false
	}
	return true
}

func (p *OnlineDDlConfig) Field1DeepEqual(src int32) bool {

	if p.ChangeTableLockTimeout != src {
		return false
	}
	return true
}
func (p *OnlineDDlConfig) Field2DeepEqual(src int32) bool {

	if p.ChangeTableRetryCount != src {
		return false
	}
	return true
}
func (p *OnlineDDlConfig) Field3DeepEqual(src bool) bool {

	if p.IsTableCopyDMSAuto != src {
		return false
	}
	return true
}
func (p *OnlineDDlConfig) Field4DeepEqual(src int32) bool {

	if p.TableCopySize != src {
		return false
	}
	return true
}
func (p *OnlineDDlConfig) Field5DeepEqual(src ChangeTableStrategy) bool {

	if p.ChangeTableStrategy != src {
		return false
	}
	return true
}
func (p *OnlineDDlConfig) Field6DeepEqual(src bool) bool {

	if p.IsExecuteImmediately != src {
		return false
	}
	return true
}
func (p *OnlineDDlConfig) Field7DeepEqual(src int32) bool {

	if p.ExecuteTime != src {
		return false
	}
	return true
}

type SubmitTicketResp struct {
	Code   ErrCode `thrift:"Code,1,required" frugal:"1,required,ErrCode" json:"Code"`
	ErrMsg string  `thrift:"ErrMsg,2,required" frugal:"2,required,string" json:"ErrMsg"`
}

func NewSubmitTicketResp() *SubmitTicketResp {
	return &SubmitTicketResp{}
}

func (p *SubmitTicketResp) InitDefault() {
}

func (p *SubmitTicketResp) GetCode() (v ErrCode) {
	return p.Code
}

func (p *SubmitTicketResp) GetErrMsg() (v string) {
	return p.ErrMsg
}
func (p *SubmitTicketResp) SetCode(val ErrCode) {
	p.Code = val
}
func (p *SubmitTicketResp) SetErrMsg(val string) {
	p.ErrMsg = val
}

var fieldIDToName_SubmitTicketResp = map[int16]string{
	1: "Code",
	2: "ErrMsg",
}

func (p *SubmitTicketResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SubmitTicketResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false
	var issetErrMsg bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetErrMsg = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetErrMsg {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SubmitTicketResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SubmitTicketResp[fieldId]))
}

func (p *SubmitTicketResp) ReadField1(iprot thrift.TProtocol) error {

	var _field ErrCode
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ErrCode(v)
	}
	p.Code = _field
	return nil
}
func (p *SubmitTicketResp) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ErrMsg = _field
	return nil
}

func (p *SubmitTicketResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SubmitTicketResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("SubmitTicketResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SubmitTicketResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Code)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SubmitTicketResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ErrMsg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ErrMsg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SubmitTicketResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitTicketResp(%+v)", *p)

}

func (p *SubmitTicketResp) DeepEqual(ano *SubmitTicketResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Code) {
		return false
	}
	if !p.Field2DeepEqual(ano.ErrMsg) {
		return false
	}
	return true
}

func (p *SubmitTicketResp) Field1DeepEqual(src ErrCode) bool {

	if p.Code != src {
		return false
	}
	return true
}
func (p *SubmitTicketResp) Field2DeepEqual(src string) bool {

	if strings.Compare(p.ErrMsg, src) != 0 {
		return false
	}
	return true
}

type DescribePreCheckDetailReq struct {
	TicketId string `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
}

func NewDescribePreCheckDetailReq() *DescribePreCheckDetailReq {
	return &DescribePreCheckDetailReq{}
}

func (p *DescribePreCheckDetailReq) InitDefault() {
}

func (p *DescribePreCheckDetailReq) GetTicketId() (v string) {
	return p.TicketId
}
func (p *DescribePreCheckDetailReq) SetTicketId(val string) {
	p.TicketId = val
}

var fieldIDToName_DescribePreCheckDetailReq = map[int16]string{
	1: "TicketId",
}

func (p *DescribePreCheckDetailReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribePreCheckDetailReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribePreCheckDetailReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribePreCheckDetailReq[fieldId]))
}

func (p *DescribePreCheckDetailReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}

func (p *DescribePreCheckDetailReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribePreCheckDetailReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribePreCheckDetailReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribePreCheckDetailReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribePreCheckDetailReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribePreCheckDetailReq(%+v)", *p)

}

func (p *DescribePreCheckDetailReq) DeepEqual(ano *DescribePreCheckDetailReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	return true
}

func (p *DescribePreCheckDetailReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}

type DescribePreCheckDetailResp struct {
	TicketId           string            `thrift:"TicketId,1,required" frugal:"1,required,string" json:"TicketId"`
	PreCheckDetails    []*PreCheckDetail `thrift:"PreCheckDetails,2,required" frugal:"2,required,list<PreCheckDetail>" json:"PreCheckDetails"`
	AllPass            bool              `thrift:"AllPass,3,required" frugal:"3,required,bool" json:"AllPass"`
	EnableSubmitTicket bool              `thrift:"EnableSubmitTicket,4,required" frugal:"4,required,bool" json:"EnableSubmitTicket"`
	PrecheckFinished   bool              `thrift:"PrecheckFinished,5,required" frugal:"5,required,bool" json:"PrecheckFinished"`
	Step               int32             `thrift:"Step,6,required" frugal:"6,required,i32" json:"Step"`
	TotalNeedCheck     int32             `thrift:"TotalNeedCheck,7,required" frugal:"7,required,i32" json:"TotalNeedCheck"`
	IsContainDDL       bool              `thrift:"IsContainDDL,8,required" frugal:"8,required,bool" json:"IsContainDDL"`
}

func NewDescribePreCheckDetailResp() *DescribePreCheckDetailResp {
	return &DescribePreCheckDetailResp{}
}

func (p *DescribePreCheckDetailResp) InitDefault() {
}

func (p *DescribePreCheckDetailResp) GetTicketId() (v string) {
	return p.TicketId
}

func (p *DescribePreCheckDetailResp) GetPreCheckDetails() (v []*PreCheckDetail) {
	return p.PreCheckDetails
}

func (p *DescribePreCheckDetailResp) GetAllPass() (v bool) {
	return p.AllPass
}

func (p *DescribePreCheckDetailResp) GetEnableSubmitTicket() (v bool) {
	return p.EnableSubmitTicket
}

func (p *DescribePreCheckDetailResp) GetPrecheckFinished() (v bool) {
	return p.PrecheckFinished
}

func (p *DescribePreCheckDetailResp) GetStep() (v int32) {
	return p.Step
}

func (p *DescribePreCheckDetailResp) GetTotalNeedCheck() (v int32) {
	return p.TotalNeedCheck
}

func (p *DescribePreCheckDetailResp) GetIsContainDDL() (v bool) {
	return p.IsContainDDL
}
func (p *DescribePreCheckDetailResp) SetTicketId(val string) {
	p.TicketId = val
}
func (p *DescribePreCheckDetailResp) SetPreCheckDetails(val []*PreCheckDetail) {
	p.PreCheckDetails = val
}
func (p *DescribePreCheckDetailResp) SetAllPass(val bool) {
	p.AllPass = val
}
func (p *DescribePreCheckDetailResp) SetEnableSubmitTicket(val bool) {
	p.EnableSubmitTicket = val
}
func (p *DescribePreCheckDetailResp) SetPrecheckFinished(val bool) {
	p.PrecheckFinished = val
}
func (p *DescribePreCheckDetailResp) SetStep(val int32) {
	p.Step = val
}
func (p *DescribePreCheckDetailResp) SetTotalNeedCheck(val int32) {
	p.TotalNeedCheck = val
}
func (p *DescribePreCheckDetailResp) SetIsContainDDL(val bool) {
	p.IsContainDDL = val
}

var fieldIDToName_DescribePreCheckDetailResp = map[int16]string{
	1: "TicketId",
	2: "PreCheckDetails",
	3: "AllPass",
	4: "EnableSubmitTicket",
	5: "PrecheckFinished",
	6: "Step",
	7: "TotalNeedCheck",
	8: "IsContainDDL",
}

func (p *DescribePreCheckDetailResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribePreCheckDetailResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTicketId bool = false
	var issetPreCheckDetails bool = false
	var issetAllPass bool = false
	var issetEnableSubmitTicket bool = false
	var issetPrecheckFinished bool = false
	var issetStep bool = false
	var issetTotalNeedCheck bool = false
	var issetIsContainDDL bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTicketId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetPreCheckDetails = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAllPass = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnableSubmitTicket = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetPrecheckFinished = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetStep = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotalNeedCheck = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
				issetIsContainDDL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTicketId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetPreCheckDetails {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAllPass {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEnableSubmitTicket {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetPrecheckFinished {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetStep {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetTotalNeedCheck {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetIsContainDDL {
		fieldId = 8
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribePreCheckDetailResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribePreCheckDetailResp[fieldId]))
}

func (p *DescribePreCheckDetailResp) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TicketId = _field
	return nil
}
func (p *DescribePreCheckDetailResp) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*PreCheckDetail, 0, size)
	values := make([]PreCheckDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.PreCheckDetails = _field
	return nil
}
func (p *DescribePreCheckDetailResp) ReadField3(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AllPass = _field
	return nil
}
func (p *DescribePreCheckDetailResp) ReadField4(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EnableSubmitTicket = _field
	return nil
}
func (p *DescribePreCheckDetailResp) ReadField5(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PrecheckFinished = _field
	return nil
}
func (p *DescribePreCheckDetailResp) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Step = _field
	return nil
}
func (p *DescribePreCheckDetailResp) ReadField7(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TotalNeedCheck = _field
	return nil
}
func (p *DescribePreCheckDetailResp) ReadField8(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IsContainDDL = _field
	return nil
}

func (p *DescribePreCheckDetailResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribePreCheckDetailResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribePreCheckDetailResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribePreCheckDetailResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TicketId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TicketId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribePreCheckDetailResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PreCheckDetails", thrift.LIST, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PreCheckDetails)); err != nil {
		return err
	}
	for _, v := range p.PreCheckDetails {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribePreCheckDetailResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AllPass", thrift.BOOL, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.AllPass); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribePreCheckDetailResp) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EnableSubmitTicket", thrift.BOOL, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.EnableSubmitTicket); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribePreCheckDetailResp) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PrecheckFinished", thrift.BOOL, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.PrecheckFinished); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribePreCheckDetailResp) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Step", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Step); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribePreCheckDetailResp) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TotalNeedCheck", thrift.I32, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.TotalNeedCheck); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribePreCheckDetailResp) writeField8(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("IsContainDDL", thrift.BOOL, 8); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.IsContainDDL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *DescribePreCheckDetailResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribePreCheckDetailResp(%+v)", *p)

}

func (p *DescribePreCheckDetailResp) DeepEqual(ano *DescribePreCheckDetailResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TicketId) {
		return false
	}
	if !p.Field2DeepEqual(ano.PreCheckDetails) {
		return false
	}
	if !p.Field3DeepEqual(ano.AllPass) {
		return false
	}
	if !p.Field4DeepEqual(ano.EnableSubmitTicket) {
		return false
	}
	if !p.Field5DeepEqual(ano.PrecheckFinished) {
		return false
	}
	if !p.Field6DeepEqual(ano.Step) {
		return false
	}
	if !p.Field7DeepEqual(ano.TotalNeedCheck) {
		return false
	}
	if !p.Field8DeepEqual(ano.IsContainDDL) {
		return false
	}
	return true
}

func (p *DescribePreCheckDetailResp) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TicketId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribePreCheckDetailResp) Field2DeepEqual(src []*PreCheckDetail) bool {

	if len(p.PreCheckDetails) != len(src) {
		return false
	}
	for i, v := range p.PreCheckDetails {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribePreCheckDetailResp) Field3DeepEqual(src bool) bool {

	if p.AllPass != src {
		return false
	}
	return true
}
func (p *DescribePreCheckDetailResp) Field4DeepEqual(src bool) bool {

	if p.EnableSubmitTicket != src {
		return false
	}
	return true
}
func (p *DescribePreCheckDetailResp) Field5DeepEqual(src bool) bool {

	if p.PrecheckFinished != src {
		return false
	}
	return true
}
func (p *DescribePreCheckDetailResp) Field6DeepEqual(src int32) bool {

	if p.Step != src {
		return false
	}
	return true
}
func (p *DescribePreCheckDetailResp) Field7DeepEqual(src int32) bool {

	if p.TotalNeedCheck != src {
		return false
	}
	return true
}
func (p *DescribePreCheckDetailResp) Field8DeepEqual(src bool) bool {

	if p.IsContainDDL != src {
		return false
	}
	return true
}

type PreCheckDetail struct {
	ItemName      string        `thrift:"ItemName,1,required" frugal:"1,required,string" json:"ItemName"`
	PreCheckItem  PreCheckItem  `thrift:"PreCheckItem,2,required" frugal:"2,required,PreCheckItem" json:"PreCheckItem"`
	PreCheckState PreCheckState `thrift:"PreCheckState,3,required" frugal:"3,required,PreCheckState" json:"PreCheckState"`
	Result_       string        `thrift:"Result,4,required" frugal:"4,required,string" json:"Result"`
	ItemDetails   []*ItemDetail `thrift:"ItemDetails,5,required" frugal:"5,required,list<ItemDetail>" json:"ItemDetails"`
}

func NewPreCheckDetail() *PreCheckDetail {
	return &PreCheckDetail{}
}

func (p *PreCheckDetail) InitDefault() {
}

func (p *PreCheckDetail) GetItemName() (v string) {
	return p.ItemName
}

func (p *PreCheckDetail) GetPreCheckItem() (v PreCheckItem) {
	return p.PreCheckItem
}

func (p *PreCheckDetail) GetPreCheckState() (v PreCheckState) {
	return p.PreCheckState
}

func (p *PreCheckDetail) GetResult_() (v string) {
	return p.Result_
}

func (p *PreCheckDetail) GetItemDetails() (v []*ItemDetail) {
	return p.ItemDetails
}
func (p *PreCheckDetail) SetItemName(val string) {
	p.ItemName = val
}
func (p *PreCheckDetail) SetPreCheckItem(val PreCheckItem) {
	p.PreCheckItem = val
}
func (p *PreCheckDetail) SetPreCheckState(val PreCheckState) {
	p.PreCheckState = val
}
func (p *PreCheckDetail) SetResult_(val string) {
	p.Result_ = val
}
func (p *PreCheckDetail) SetItemDetails(val []*ItemDetail) {
	p.ItemDetails = val
}

var fieldIDToName_PreCheckDetail = map[int16]string{
	1: "ItemName",
	2: "PreCheckItem",
	3: "PreCheckState",
	4: "Result",
	5: "ItemDetails",
}

func (p *PreCheckDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("PreCheckDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetItemName bool = false
	var issetPreCheckItem bool = false
	var issetPreCheckState bool = false
	var issetResult_ bool = false
	var issetItemDetails bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetItemName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetPreCheckItem = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetPreCheckState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetResult_ = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetItemDetails = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetItemName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetPreCheckItem {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetPreCheckState {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetResult_ {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetItemDetails {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_PreCheckDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_PreCheckDetail[fieldId]))
}

func (p *PreCheckDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ItemName = _field
	return nil
}
func (p *PreCheckDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field PreCheckItem
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = PreCheckItem(v)
	}
	p.PreCheckItem = _field
	return nil
}
func (p *PreCheckDetail) ReadField3(iprot thrift.TProtocol) error {

	var _field PreCheckState
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = PreCheckState(v)
	}
	p.PreCheckState = _field
	return nil
}
func (p *PreCheckDetail) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Result_ = _field
	return nil
}
func (p *PreCheckDetail) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ItemDetail, 0, size)
	values := make([]ItemDetail, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ItemDetails = _field
	return nil
}

func (p *PreCheckDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("PreCheckDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("PreCheckDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *PreCheckDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ItemName", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ItemName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *PreCheckDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PreCheckItem", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.PreCheckItem)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *PreCheckDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("PreCheckState", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.PreCheckState)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *PreCheckDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Result", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Result_); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *PreCheckDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ItemDetails", thrift.LIST, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ItemDetails)); err != nil {
		return err
	}
	for _, v := range p.ItemDetails {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *PreCheckDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PreCheckDetail(%+v)", *p)

}

func (p *PreCheckDetail) DeepEqual(ano *PreCheckDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ItemName) {
		return false
	}
	if !p.Field2DeepEqual(ano.PreCheckItem) {
		return false
	}
	if !p.Field3DeepEqual(ano.PreCheckState) {
		return false
	}
	if !p.Field4DeepEqual(ano.Result_) {
		return false
	}
	if !p.Field5DeepEqual(ano.ItemDetails) {
		return false
	}
	return true
}

func (p *PreCheckDetail) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ItemName, src) != 0 {
		return false
	}
	return true
}
func (p *PreCheckDetail) Field2DeepEqual(src PreCheckItem) bool {

	if p.PreCheckItem != src {
		return false
	}
	return true
}
func (p *PreCheckDetail) Field3DeepEqual(src PreCheckState) bool {

	if p.PreCheckState != src {
		return false
	}
	return true
}
func (p *PreCheckDetail) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Result_, src) != 0 {
		return false
	}
	return true
}
func (p *PreCheckDetail) Field5DeepEqual(src []*ItemDetail) bool {

	if len(p.ItemDetails) != len(src) {
		return false
	}
	for i, v := range p.ItemDetails {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ItemDetail struct {
	SQL     string `thrift:"SQL,1,required" frugal:"1,required,string" json:"SQL"`
	Result_ string `thrift:"Result,2,required" frugal:"2,required,string" json:"Result"`
}

func NewItemDetail() *ItemDetail {
	return &ItemDetail{}
}

func (p *ItemDetail) InitDefault() {
}

func (p *ItemDetail) GetSQL() (v string) {
	return p.SQL
}

func (p *ItemDetail) GetResult_() (v string) {
	return p.Result_
}
func (p *ItemDetail) SetSQL(val string) {
	p.SQL = val
}
func (p *ItemDetail) SetResult_(val string) {
	p.Result_ = val
}

var fieldIDToName_ItemDetail = map[int16]string{
	1: "SQL",
	2: "Result",
}

func (p *ItemDetail) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ItemDetail")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSQL bool = false
	var issetResult_ bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQL = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetResult_ = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSQL {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetResult_ {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ItemDetail[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ItemDetail[fieldId]))
}

func (p *ItemDetail) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SQL = _field
	return nil
}
func (p *ItemDetail) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Result_ = _field
	return nil
}

func (p *ItemDetail) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ItemDetail")

	var fieldId int16
	if err = oprot.WriteStructBegin("ItemDetail"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ItemDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQL", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.SQL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ItemDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Result", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Result_); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ItemDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ItemDetail(%+v)", *p)

}

func (p *ItemDetail) DeepEqual(ano *ItemDetail) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SQL) {
		return false
	}
	if !p.Field2DeepEqual(ano.Result_) {
		return false
	}
	return true
}

func (p *ItemDetail) Field1DeepEqual(src string) bool {

	if strings.Compare(p.SQL, src) != 0 {
		return false
	}
	return true
}
func (p *ItemDetail) Field2DeepEqual(src string) bool {

	if strings.Compare(p.Result_, src) != 0 {
		return false
	}
	return true
}
