// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SupportedKillSqlType int64

const (
	SupportedKillSqlType_select SupportedKillSqlType = 0
	SupportedKillSqlType_update SupportedKillSqlType = 1
	SupportedKillSqlType_delete SupportedKillSqlType = 2
	SupportedKillSqlType_insert SupportedKillSqlType = 3
)

func (p SupportedKillSqlType) String() string {
	switch p {
	case SupportedKillSqlType_select:
		return "select"
	case SupportedKillSqlType_update:
		return "update"
	case SupportedKillSqlType_delete:
		return "delete"
	case SupportedKillSqlType_insert:
		return "insert"
	}
	return "<UNSET>"
}

func SupportedKillSqlTypeFromString(s string) (SupportedKillSqlType, error) {
	switch s {
	case "select":
		return SupportedKillSqlType_select, nil
	case "update":
		return SupportedKillSqlType_update, nil
	case "delete":
		return SupportedKillSqlType_delete, nil
	case "insert":
		return SupportedKillSqlType_insert, nil
	}
	return SupportedKillSqlType(0), fmt.Errorf("not a valid SupportedKillSqlType string")
}

func SupportedKillSqlTypePtr(v SupportedKillSqlType) *SupportedKillSqlType { return &v }

func (p SupportedKillSqlType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *SupportedKillSqlType) UnmarshalText(text []byte) error {
	q, err := SupportedKillSqlTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type KillRuleState int64

const (
	KillRuleState_ACTIVE        KillRuleState = 0
	KillRuleState_STOPPED       KillRuleState = 1
	KillRuleState_NONE          KillRuleState = 2
	KillRuleState_DONE          KillRuleState = 3
	KillRuleState_STOP_FAILED   KillRuleState = 4
	KillRuleState_DELETE_FAILED KillRuleState = 5
)

func (p KillRuleState) String() string {
	switch p {
	case KillRuleState_ACTIVE:
		return "ACTIVE"
	case KillRuleState_STOPPED:
		return "STOPPED"
	case KillRuleState_NONE:
		return "NONE"
	case KillRuleState_DONE:
		return "DONE"
	case KillRuleState_STOP_FAILED:
		return "STOP_FAILED"
	case KillRuleState_DELETE_FAILED:
		return "DELETE_FAILED"
	}
	return "<UNSET>"
}

func KillRuleStateFromString(s string) (KillRuleState, error) {
	switch s {
	case "ACTIVE":
		return KillRuleState_ACTIVE, nil
	case "STOPPED":
		return KillRuleState_STOPPED, nil
	case "NONE":
		return KillRuleState_NONE, nil
	case "DONE":
		return KillRuleState_DONE, nil
	case "STOP_FAILED":
		return KillRuleState_STOP_FAILED, nil
	case "DELETE_FAILED":
		return KillRuleState_DELETE_FAILED, nil
	}
	return KillRuleState(0), fmt.Errorf("not a valid KillRuleState string")
}

func KillRuleStatePtr(v KillRuleState) *KillRuleState { return &v }

func (p KillRuleState) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *KillRuleState) UnmarshalText(text []byte) error {
	q, err := KillRuleStateFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type KillSqlEventType int64

const (
	KillSqlEventType_Add    KillSqlEventType = 0
	KillSqlEventType_Delete KillSqlEventType = 1
	KillSqlEventType_Stop   KillSqlEventType = 2
	KillSqlEventType_Modify KillSqlEventType = 3
)

func (p KillSqlEventType) String() string {
	switch p {
	case KillSqlEventType_Add:
		return "Add"
	case KillSqlEventType_Delete:
		return "Delete"
	case KillSqlEventType_Stop:
		return "Stop"
	case KillSqlEventType_Modify:
		return "Modify"
	}
	return "<UNSET>"
}

func KillSqlEventTypeFromString(s string) (KillSqlEventType, error) {
	switch s {
	case "Add":
		return KillSqlEventType_Add, nil
	case "Delete":
		return KillSqlEventType_Delete, nil
	case "Stop":
		return KillSqlEventType_Stop, nil
	case "Modify":
		return KillSqlEventType_Modify, nil
	}
	return KillSqlEventType(0), fmt.Errorf("not a valid KillSqlEventType string")
}

func KillSqlEventTypePtr(v KillSqlEventType) *KillSqlEventType { return &v }

func (p KillSqlEventType) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *KillSqlEventType) UnmarshalText(text []byte) error {
	q, err := KillSqlEventTypeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

type SQLKillRule struct {
	TaskId          string  `thrift:"TaskId,1,required" frugal:"1,required,string" json:"TaskId"`
	CreateTime      string  `thrift:"CreateTime,2,required" frugal:"2,required,string" json:"CreateTime"`
	RemainingTime   int32   `thrift:"RemainingTime,3,required" frugal:"3,required,i32" json:"RemainingTime"`
	EndTime         string  `thrift:"EndTime,4,required" frugal:"4,required,string" json:"EndTime"`
	User            string  `thrift:"User,5,required" frugal:"5,required,string" json:"User"`
	EffectiveTime   int32   `thrift:"EffectiveTime,6,required" frugal:"6,required,i32" json:"EffectiveTime"`
	MaxExecTime     int64   `thrift:"MaxExecTime,7,required" frugal:"7,required,i64" json:"MaxExecTime"`
	SqlType         *string `thrift:"SqlType,8,optional" frugal:"8,optional,string" json:"SqlType,omitempty"`
	NodeType        *string `thrift:"NodeType,9,optional" frugal:"9,optional,string" json:"NodeType,omitempty"`
	State           string  `thrift:"State,10,required" frugal:"10,required,string" json:"State"`
	Host            *string `thrift:"Host,11,optional" frugal:"11,optional,string" json:"Host,omitempty"`
	KeyWords        *string `thrift:"KeyWords,12,optional" frugal:"12,optional,string" json:"KeyWords,omitempty"`
	FingerPrint     *string `thrift:"FingerPrint,13,optional" frugal:"13,optional,string" json:"FingerPrint,omitempty"`
	TerminationType *string `thrift:"TerminationType,14,optional" frugal:"14,optional,string" json:"TerminationType,omitempty"`
}

func NewSQLKillRule() *SQLKillRule {
	return &SQLKillRule{}
}

func (p *SQLKillRule) InitDefault() {
}

func (p *SQLKillRule) GetTaskId() (v string) {
	return p.TaskId
}

func (p *SQLKillRule) GetCreateTime() (v string) {
	return p.CreateTime
}

func (p *SQLKillRule) GetRemainingTime() (v int32) {
	return p.RemainingTime
}

func (p *SQLKillRule) GetEndTime() (v string) {
	return p.EndTime
}

func (p *SQLKillRule) GetUser() (v string) {
	return p.User
}

func (p *SQLKillRule) GetEffectiveTime() (v int32) {
	return p.EffectiveTime
}

func (p *SQLKillRule) GetMaxExecTime() (v int64) {
	return p.MaxExecTime
}

var SQLKillRule_SqlType_DEFAULT string

func (p *SQLKillRule) GetSqlType() (v string) {
	if !p.IsSetSqlType() {
		return SQLKillRule_SqlType_DEFAULT
	}
	return *p.SqlType
}

var SQLKillRule_NodeType_DEFAULT string

func (p *SQLKillRule) GetNodeType() (v string) {
	if !p.IsSetNodeType() {
		return SQLKillRule_NodeType_DEFAULT
	}
	return *p.NodeType
}

func (p *SQLKillRule) GetState() (v string) {
	return p.State
}

var SQLKillRule_Host_DEFAULT string

func (p *SQLKillRule) GetHost() (v string) {
	if !p.IsSetHost() {
		return SQLKillRule_Host_DEFAULT
	}
	return *p.Host
}

var SQLKillRule_KeyWords_DEFAULT string

func (p *SQLKillRule) GetKeyWords() (v string) {
	if !p.IsSetKeyWords() {
		return SQLKillRule_KeyWords_DEFAULT
	}
	return *p.KeyWords
}

var SQLKillRule_FingerPrint_DEFAULT string

func (p *SQLKillRule) GetFingerPrint() (v string) {
	if !p.IsSetFingerPrint() {
		return SQLKillRule_FingerPrint_DEFAULT
	}
	return *p.FingerPrint
}

var SQLKillRule_TerminationType_DEFAULT string

func (p *SQLKillRule) GetTerminationType() (v string) {
	if !p.IsSetTerminationType() {
		return SQLKillRule_TerminationType_DEFAULT
	}
	return *p.TerminationType
}
func (p *SQLKillRule) SetTaskId(val string) {
	p.TaskId = val
}
func (p *SQLKillRule) SetCreateTime(val string) {
	p.CreateTime = val
}
func (p *SQLKillRule) SetRemainingTime(val int32) {
	p.RemainingTime = val
}
func (p *SQLKillRule) SetEndTime(val string) {
	p.EndTime = val
}
func (p *SQLKillRule) SetUser(val string) {
	p.User = val
}
func (p *SQLKillRule) SetEffectiveTime(val int32) {
	p.EffectiveTime = val
}
func (p *SQLKillRule) SetMaxExecTime(val int64) {
	p.MaxExecTime = val
}
func (p *SQLKillRule) SetSqlType(val *string) {
	p.SqlType = val
}
func (p *SQLKillRule) SetNodeType(val *string) {
	p.NodeType = val
}
func (p *SQLKillRule) SetState(val string) {
	p.State = val
}
func (p *SQLKillRule) SetHost(val *string) {
	p.Host = val
}
func (p *SQLKillRule) SetKeyWords(val *string) {
	p.KeyWords = val
}
func (p *SQLKillRule) SetFingerPrint(val *string) {
	p.FingerPrint = val
}
func (p *SQLKillRule) SetTerminationType(val *string) {
	p.TerminationType = val
}

var fieldIDToName_SQLKillRule = map[int16]string{
	1:  "TaskId",
	2:  "CreateTime",
	3:  "RemainingTime",
	4:  "EndTime",
	5:  "User",
	6:  "EffectiveTime",
	7:  "MaxExecTime",
	8:  "SqlType",
	9:  "NodeType",
	10: "State",
	11: "Host",
	12: "KeyWords",
	13: "FingerPrint",
	14: "TerminationType",
}

func (p *SQLKillRule) IsSetSqlType() bool {
	return p.SqlType != nil
}

func (p *SQLKillRule) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *SQLKillRule) IsSetHost() bool {
	return p.Host != nil
}

func (p *SQLKillRule) IsSetKeyWords() bool {
	return p.KeyWords != nil
}

func (p *SQLKillRule) IsSetFingerPrint() bool {
	return p.FingerPrint != nil
}

func (p *SQLKillRule) IsSetTerminationType() bool {
	return p.TerminationType != nil
}

func (p *SQLKillRule) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLKillRule")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetTaskId bool = false
	var issetCreateTime bool = false
	var issetRemainingTime bool = false
	var issetEndTime bool = false
	var issetUser bool = false
	var issetEffectiveTime bool = false
	var issetMaxExecTime bool = false
	var issetState bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetTaskId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetCreateTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetRemainingTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEndTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetUser = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
				issetEffectiveTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxExecTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
				issetState = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField12(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField13(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField14(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetTaskId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetCreateTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetRemainingTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEndTime {
		fieldId = 4
		goto RequiredFieldNotSetError
	}

	if !issetUser {
		fieldId = 5
		goto RequiredFieldNotSetError
	}

	if !issetEffectiveTime {
		fieldId = 6
		goto RequiredFieldNotSetError
	}

	if !issetMaxExecTime {
		fieldId = 7
		goto RequiredFieldNotSetError
	}

	if !issetState {
		fieldId = 10
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SQLKillRule[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SQLKillRule[fieldId]))
}

func (p *SQLKillRule) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskId = _field
	return nil
}
func (p *SQLKillRule) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CreateTime = _field
	return nil
}
func (p *SQLKillRule) ReadField3(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.RemainingTime = _field
	return nil
}
func (p *SQLKillRule) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EndTime = _field
	return nil
}
func (p *SQLKillRule) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.User = _field
	return nil
}
func (p *SQLKillRule) ReadField6(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EffectiveTime = _field
	return nil
}
func (p *SQLKillRule) ReadField7(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxExecTime = _field
	return nil
}
func (p *SQLKillRule) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SqlType = _field
	return nil
}
func (p *SQLKillRule) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.NodeType = _field
	return nil
}
func (p *SQLKillRule) ReadField10(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.State = _field
	return nil
}
func (p *SQLKillRule) ReadField11(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Host = _field
	return nil
}
func (p *SQLKillRule) ReadField12(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.KeyWords = _field
	return nil
}
func (p *SQLKillRule) ReadField13(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FingerPrint = _field
	return nil
}
func (p *SQLKillRule) ReadField14(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TerminationType = _field
	return nil
}

func (p *SQLKillRule) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SQLKillRule")

	var fieldId int16
	if err = oprot.WriteStructBegin("SQLKillRule"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField12(oprot); err != nil {
			fieldId = 12
			goto WriteFieldError
		}
		if err = p.writeField13(oprot); err != nil {
			fieldId = 13
			goto WriteFieldError
		}
		if err = p.writeField14(oprot); err != nil {
			fieldId = 14
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SQLKillRule) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SQLKillRule) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("CreateTime", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.CreateTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SQLKillRule) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("RemainingTime", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.RemainingTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SQLKillRule) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EndTime", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.EndTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SQLKillRule) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("User", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.User); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SQLKillRule) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EffectiveTime", thrift.I32, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EffectiveTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SQLKillRule) writeField7(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxExecTime", thrift.I64, 7); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MaxExecTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SQLKillRule) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlType() {
		if err = oprot.WriteFieldBegin("SqlType", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SqlType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SQLKillRule) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.NodeType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *SQLKillRule) writeField10(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("State", thrift.STRING, 10); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.State); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *SQLKillRule) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetHost() {
		if err = oprot.WriteFieldBegin("Host", thrift.STRING, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Host); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *SQLKillRule) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeyWords() {
		if err = oprot.WriteFieldBegin("KeyWords", thrift.STRING, 12); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.KeyWords); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 12 end error: ", p), err)
}

func (p *SQLKillRule) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetFingerPrint() {
		if err = oprot.WriteFieldBegin("FingerPrint", thrift.STRING, 13); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FingerPrint); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 13 end error: ", p), err)
}

func (p *SQLKillRule) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetTerminationType() {
		if err = oprot.WriteFieldBegin("TerminationType", thrift.STRING, 14); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TerminationType); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 14 end error: ", p), err)
}

func (p *SQLKillRule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SQLKillRule(%+v)", *p)

}

func (p *SQLKillRule) DeepEqual(ano *SQLKillRule) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field2DeepEqual(ano.CreateTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.RemainingTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.EndTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.User) {
		return false
	}
	if !p.Field6DeepEqual(ano.EffectiveTime) {
		return false
	}
	if !p.Field7DeepEqual(ano.MaxExecTime) {
		return false
	}
	if !p.Field8DeepEqual(ano.SqlType) {
		return false
	}
	if !p.Field9DeepEqual(ano.NodeType) {
		return false
	}
	if !p.Field10DeepEqual(ano.State) {
		return false
	}
	if !p.Field11DeepEqual(ano.Host) {
		return false
	}
	if !p.Field12DeepEqual(ano.KeyWords) {
		return false
	}
	if !p.Field13DeepEqual(ano.FingerPrint) {
		return false
	}
	if !p.Field14DeepEqual(ano.TerminationType) {
		return false
	}
	return true
}

func (p *SQLKillRule) Field1DeepEqual(src string) bool {

	if strings.Compare(p.TaskId, src) != 0 {
		return false
	}
	return true
}
func (p *SQLKillRule) Field2DeepEqual(src string) bool {

	if strings.Compare(p.CreateTime, src) != 0 {
		return false
	}
	return true
}
func (p *SQLKillRule) Field3DeepEqual(src int32) bool {

	if p.RemainingTime != src {
		return false
	}
	return true
}
func (p *SQLKillRule) Field4DeepEqual(src string) bool {

	if strings.Compare(p.EndTime, src) != 0 {
		return false
	}
	return true
}
func (p *SQLKillRule) Field5DeepEqual(src string) bool {

	if strings.Compare(p.User, src) != 0 {
		return false
	}
	return true
}
func (p *SQLKillRule) Field6DeepEqual(src int32) bool {

	if p.EffectiveTime != src {
		return false
	}
	return true
}
func (p *SQLKillRule) Field7DeepEqual(src int64) bool {

	if p.MaxExecTime != src {
		return false
	}
	return true
}
func (p *SQLKillRule) Field8DeepEqual(src *string) bool {

	if p.SqlType == src {
		return true
	} else if p.SqlType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.SqlType, *src) != 0 {
		return false
	}
	return true
}
func (p *SQLKillRule) Field9DeepEqual(src *string) bool {

	if p.NodeType == src {
		return true
	} else if p.NodeType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.NodeType, *src) != 0 {
		return false
	}
	return true
}
func (p *SQLKillRule) Field10DeepEqual(src string) bool {

	if strings.Compare(p.State, src) != 0 {
		return false
	}
	return true
}
func (p *SQLKillRule) Field11DeepEqual(src *string) bool {

	if p.Host == src {
		return true
	} else if p.Host == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Host, *src) != 0 {
		return false
	}
	return true
}
func (p *SQLKillRule) Field12DeepEqual(src *string) bool {

	if p.KeyWords == src {
		return true
	} else if p.KeyWords == nil || src == nil {
		return false
	}
	if strings.Compare(*p.KeyWords, *src) != 0 {
		return false
	}
	return true
}
func (p *SQLKillRule) Field13DeepEqual(src *string) bool {

	if p.FingerPrint == src {
		return true
	} else if p.FingerPrint == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FingerPrint, *src) != 0 {
		return false
	}
	return true
}
func (p *SQLKillRule) Field14DeepEqual(src *string) bool {

	if p.TerminationType == src {
		return true
	} else if p.TerminationType == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TerminationType, *src) != 0 {
		return false
	}
	return true
}

type SearchParam struct {
	SqlType     []SupportedKillSqlType `thrift:"SqlType,1,optional" frugal:"1,optional,list<SupportedKillSqlType>" json:"SqlType,omitempty"`
	State       []KillRuleState        `thrift:"State,2,optional" frugal:"2,optional,list<KillRuleState>" json:"State,omitempty"`
	NodeType    []string               `thrift:"NodeType,3,optional" frugal:"3,optional,list<string>" json:"NodeType,omitempty"`
	User        *string                `thrift:"User,4,optional" frugal:"4,optional,string" json:"User,omitempty"`
	TaskId      *string                `thrift:"TaskId,5,optional" frugal:"5,optional,string" json:"TaskId,omitempty"`
	Host        *string                `thrift:"Host,6,optional" frugal:"6,optional,string" json:"Host,omitempty"`
	KeyWords    *string                `thrift:"KeyWords,7,optional" frugal:"7,optional,string" json:"KeyWords,omitempty"`
	FingerPrint *string                `thrift:"FingerPrint,8,optional" frugal:"8,optional,string" json:"FingerPrint,omitempty"`
}

func NewSearchParam() *SearchParam {
	return &SearchParam{}
}

func (p *SearchParam) InitDefault() {
}

var SearchParam_SqlType_DEFAULT []SupportedKillSqlType

func (p *SearchParam) GetSqlType() (v []SupportedKillSqlType) {
	if !p.IsSetSqlType() {
		return SearchParam_SqlType_DEFAULT
	}
	return p.SqlType
}

var SearchParam_State_DEFAULT []KillRuleState

func (p *SearchParam) GetState() (v []KillRuleState) {
	if !p.IsSetState() {
		return SearchParam_State_DEFAULT
	}
	return p.State
}

var SearchParam_NodeType_DEFAULT []string

func (p *SearchParam) GetNodeType() (v []string) {
	if !p.IsSetNodeType() {
		return SearchParam_NodeType_DEFAULT
	}
	return p.NodeType
}

var SearchParam_User_DEFAULT string

func (p *SearchParam) GetUser() (v string) {
	if !p.IsSetUser() {
		return SearchParam_User_DEFAULT
	}
	return *p.User
}

var SearchParam_TaskId_DEFAULT string

func (p *SearchParam) GetTaskId() (v string) {
	if !p.IsSetTaskId() {
		return SearchParam_TaskId_DEFAULT
	}
	return *p.TaskId
}

var SearchParam_Host_DEFAULT string

func (p *SearchParam) GetHost() (v string) {
	if !p.IsSetHost() {
		return SearchParam_Host_DEFAULT
	}
	return *p.Host
}

var SearchParam_KeyWords_DEFAULT string

func (p *SearchParam) GetKeyWords() (v string) {
	if !p.IsSetKeyWords() {
		return SearchParam_KeyWords_DEFAULT
	}
	return *p.KeyWords
}

var SearchParam_FingerPrint_DEFAULT string

func (p *SearchParam) GetFingerPrint() (v string) {
	if !p.IsSetFingerPrint() {
		return SearchParam_FingerPrint_DEFAULT
	}
	return *p.FingerPrint
}
func (p *SearchParam) SetSqlType(val []SupportedKillSqlType) {
	p.SqlType = val
}
func (p *SearchParam) SetState(val []KillRuleState) {
	p.State = val
}
func (p *SearchParam) SetNodeType(val []string) {
	p.NodeType = val
}
func (p *SearchParam) SetUser(val *string) {
	p.User = val
}
func (p *SearchParam) SetTaskId(val *string) {
	p.TaskId = val
}
func (p *SearchParam) SetHost(val *string) {
	p.Host = val
}
func (p *SearchParam) SetKeyWords(val *string) {
	p.KeyWords = val
}
func (p *SearchParam) SetFingerPrint(val *string) {
	p.FingerPrint = val
}

var fieldIDToName_SearchParam = map[int16]string{
	1: "SqlType",
	2: "State",
	3: "NodeType",
	4: "User",
	5: "TaskId",
	6: "Host",
	7: "KeyWords",
	8: "FingerPrint",
}

func (p *SearchParam) IsSetSqlType() bool {
	return p.SqlType != nil
}

func (p *SearchParam) IsSetState() bool {
	return p.State != nil
}

func (p *SearchParam) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *SearchParam) IsSetUser() bool {
	return p.User != nil
}

func (p *SearchParam) IsSetTaskId() bool {
	return p.TaskId != nil
}

func (p *SearchParam) IsSetHost() bool {
	return p.Host != nil
}

func (p *SearchParam) IsSetKeyWords() bool {
	return p.KeyWords != nil
}

func (p *SearchParam) IsSetFingerPrint() bool {
	return p.FingerPrint != nil
}

func (p *SearchParam) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SearchParam")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SearchParam[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SearchParam) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]SupportedKillSqlType, 0, size)
	for i := 0; i < size; i++ {

		var _elem SupportedKillSqlType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = SupportedKillSqlType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SqlType = _field
	return nil
}
func (p *SearchParam) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]KillRuleState, 0, size)
	for i := 0; i < size; i++ {

		var _elem KillRuleState
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = KillRuleState(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.State = _field
	return nil
}
func (p *SearchParam) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeType = _field
	return nil
}
func (p *SearchParam) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.User = _field
	return nil
}
func (p *SearchParam) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TaskId = _field
	return nil
}
func (p *SearchParam) ReadField6(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Host = _field
	return nil
}
func (p *SearchParam) ReadField7(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.KeyWords = _field
	return nil
}
func (p *SearchParam) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FingerPrint = _field
	return nil
}

func (p *SearchParam) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SearchParam")

	var fieldId int16
	if err = oprot.WriteStructBegin("SearchParam"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlType() {
		if err = oprot.WriteFieldBegin("SqlType", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SqlType)); err != nil {
			return err
		}
		for _, v := range p.SqlType {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetState() {
		if err = oprot.WriteFieldBegin("State", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.State)); err != nil {
			return err
		}
		for _, v := range p.State {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeType)); err != nil {
			return err
		}
		for _, v := range p.NodeType {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetUser() {
		if err = oprot.WriteFieldBegin("User", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.User); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskId() {
		if err = oprot.WriteFieldBegin("TaskId", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TaskId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *SearchParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetHost() {
		if err = oprot.WriteFieldBegin("Host", thrift.STRING, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Host); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *SearchParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeyWords() {
		if err = oprot.WriteFieldBegin("KeyWords", thrift.STRING, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.KeyWords); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *SearchParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetFingerPrint() {
		if err = oprot.WriteFieldBegin("FingerPrint", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FingerPrint); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *SearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchParam(%+v)", *p)

}

func (p *SearchParam) DeepEqual(ano *SearchParam) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SqlType) {
		return false
	}
	if !p.Field2DeepEqual(ano.State) {
		return false
	}
	if !p.Field3DeepEqual(ano.NodeType) {
		return false
	}
	if !p.Field4DeepEqual(ano.User) {
		return false
	}
	if !p.Field5DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field6DeepEqual(ano.Host) {
		return false
	}
	if !p.Field7DeepEqual(ano.KeyWords) {
		return false
	}
	if !p.Field8DeepEqual(ano.FingerPrint) {
		return false
	}
	return true
}

func (p *SearchParam) Field1DeepEqual(src []SupportedKillSqlType) bool {

	if len(p.SqlType) != len(src) {
		return false
	}
	for i, v := range p.SqlType {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *SearchParam) Field2DeepEqual(src []KillRuleState) bool {

	if len(p.State) != len(src) {
		return false
	}
	for i, v := range p.State {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *SearchParam) Field3DeepEqual(src []string) bool {

	if len(p.NodeType) != len(src) {
		return false
	}
	for i, v := range p.NodeType {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *SearchParam) Field4DeepEqual(src *string) bool {

	if p.User == src {
		return true
	} else if p.User == nil || src == nil {
		return false
	}
	if strings.Compare(*p.User, *src) != 0 {
		return false
	}
	return true
}
func (p *SearchParam) Field5DeepEqual(src *string) bool {

	if p.TaskId == src {
		return true
	} else if p.TaskId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.TaskId, *src) != 0 {
		return false
	}
	return true
}
func (p *SearchParam) Field6DeepEqual(src *string) bool {

	if p.Host == src {
		return true
	} else if p.Host == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Host, *src) != 0 {
		return false
	}
	return true
}
func (p *SearchParam) Field7DeepEqual(src *string) bool {

	if p.KeyWords == src {
		return true
	} else if p.KeyWords == nil || src == nil {
		return false
	}
	if strings.Compare(*p.KeyWords, *src) != 0 {
		return false
	}
	return true
}
func (p *SearchParam) Field8DeepEqual(src *string) bool {

	if p.FingerPrint == src {
		return true
	} else if p.FingerPrint == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FingerPrint, *src) != 0 {
		return false
	}
	return true
}

type DescribeAutoKillSessionConfigReq struct {
	InstanceId   *string       `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	InstanceType *InstanceType `thrift:"InstanceType,2,optional" frugal:"2,optional,InstanceType" json:"InstanceType,omitempty"`
}

func NewDescribeAutoKillSessionConfigReq() *DescribeAutoKillSessionConfigReq {
	return &DescribeAutoKillSessionConfigReq{}
}

func (p *DescribeAutoKillSessionConfigReq) InitDefault() {
}

var DescribeAutoKillSessionConfigReq_InstanceId_DEFAULT string

func (p *DescribeAutoKillSessionConfigReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeAutoKillSessionConfigReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeAutoKillSessionConfigReq_InstanceType_DEFAULT InstanceType

func (p *DescribeAutoKillSessionConfigReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return DescribeAutoKillSessionConfigReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}
func (p *DescribeAutoKillSessionConfigReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeAutoKillSessionConfigReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}

var fieldIDToName_DescribeAutoKillSessionConfigReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
}

func (p *DescribeAutoKillSessionConfigReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeAutoKillSessionConfigReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeAutoKillSessionConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoKillSessionConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAutoKillSessionConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeAutoKillSessionConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeAutoKillSessionConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}

func (p *DescribeAutoKillSessionConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoKillSessionConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAutoKillSessionConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAutoKillSessionConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAutoKillSessionConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAutoKillSessionConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAutoKillSessionConfigReq(%+v)", *p)

}

func (p *DescribeAutoKillSessionConfigReq) DeepEqual(ano *DescribeAutoKillSessionConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DescribeAutoKillSessionConfigReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeAutoKillSessionConfigReq) Field2DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}

type DescribeAutoKillSessionConfigResp struct {
	SQLKillStatus  bool     `thrift:"SQLKillStatus,1,required" frugal:"1,required,bool" json:"SQLKillStatus"`
	MaxExecTime    int64    `thrift:"MaxExecTime,2,required" frugal:"2,required,i64" json:"MaxExecTime"`
	ProtectedUsers []string `thrift:"ProtectedUsers,3,required" frugal:"3,required,list<string>" json:"ProtectedUsers"`
}

func NewDescribeAutoKillSessionConfigResp() *DescribeAutoKillSessionConfigResp {
	return &DescribeAutoKillSessionConfigResp{}
}

func (p *DescribeAutoKillSessionConfigResp) InitDefault() {
}

func (p *DescribeAutoKillSessionConfigResp) GetSQLKillStatus() (v bool) {
	return p.SQLKillStatus
}

func (p *DescribeAutoKillSessionConfigResp) GetMaxExecTime() (v int64) {
	return p.MaxExecTime
}

func (p *DescribeAutoKillSessionConfigResp) GetProtectedUsers() (v []string) {
	return p.ProtectedUsers
}
func (p *DescribeAutoKillSessionConfigResp) SetSQLKillStatus(val bool) {
	p.SQLKillStatus = val
}
func (p *DescribeAutoKillSessionConfigResp) SetMaxExecTime(val int64) {
	p.MaxExecTime = val
}
func (p *DescribeAutoKillSessionConfigResp) SetProtectedUsers(val []string) {
	p.ProtectedUsers = val
}

var fieldIDToName_DescribeAutoKillSessionConfigResp = map[int16]string{
	1: "SQLKillStatus",
	2: "MaxExecTime",
	3: "ProtectedUsers",
}

func (p *DescribeAutoKillSessionConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoKillSessionConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSQLKillStatus bool = false
	var issetMaxExecTime bool = false
	var issetProtectedUsers bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLKillStatus = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxExecTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetProtectedUsers = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSQLKillStatus {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetMaxExecTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetProtectedUsers {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeAutoKillSessionConfigResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeAutoKillSessionConfigResp[fieldId]))
}

func (p *DescribeAutoKillSessionConfigResp) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SQLKillStatus = _field
	return nil
}
func (p *DescribeAutoKillSessionConfigResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxExecTime = _field
	return nil
}
func (p *DescribeAutoKillSessionConfigResp) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProtectedUsers = _field
	return nil
}

func (p *DescribeAutoKillSessionConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeAutoKillSessionConfigResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeAutoKillSessionConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeAutoKillSessionConfigResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLKillStatus", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.SQLKillStatus); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeAutoKillSessionConfigResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxExecTime", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MaxExecTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeAutoKillSessionConfigResp) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ProtectedUsers", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRING, len(p.ProtectedUsers)); err != nil {
		return err
	}
	for _, v := range p.ProtectedUsers {
		if err := oprot.WriteString(v); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeAutoKillSessionConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeAutoKillSessionConfigResp(%+v)", *p)

}

func (p *DescribeAutoKillSessionConfigResp) DeepEqual(ano *DescribeAutoKillSessionConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SQLKillStatus) {
		return false
	}
	if !p.Field2DeepEqual(ano.MaxExecTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.ProtectedUsers) {
		return false
	}
	return true
}

func (p *DescribeAutoKillSessionConfigResp) Field1DeepEqual(src bool) bool {

	if p.SQLKillStatus != src {
		return false
	}
	return true
}
func (p *DescribeAutoKillSessionConfigResp) Field2DeepEqual(src int64) bool {

	if p.MaxExecTime != src {
		return false
	}
	return true
}
func (p *DescribeAutoKillSessionConfigResp) Field3DeepEqual(src []string) bool {

	if len(p.ProtectedUsers) != len(src) {
		return false
	}
	for i, v := range p.ProtectedUsers {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type ModifyAutoKillSessionConfigReq struct {
	InstanceId     *string       `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	InstanceType   *InstanceType `thrift:"InstanceType,2,optional" frugal:"2,optional,InstanceType" json:"InstanceType,omitempty"`
	EnableSQLKill  *bool         `thrift:"EnableSQLKill,3,optional" frugal:"3,optional,bool" json:"EnableSQLKill,omitempty"`
	MaxExecTime    *int64        `thrift:"MaxExecTime,4,optional" frugal:"4,optional,i64" json:"MaxExecTime,omitempty"`
	ProtectedUsers []string      `thrift:"ProtectedUsers,5,optional" frugal:"5,optional,list<string>" json:"ProtectedUsers,omitempty"`
}

func NewModifyAutoKillSessionConfigReq() *ModifyAutoKillSessionConfigReq {
	return &ModifyAutoKillSessionConfigReq{}
}

func (p *ModifyAutoKillSessionConfigReq) InitDefault() {
}

var ModifyAutoKillSessionConfigReq_InstanceId_DEFAULT string

func (p *ModifyAutoKillSessionConfigReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return ModifyAutoKillSessionConfigReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var ModifyAutoKillSessionConfigReq_InstanceType_DEFAULT InstanceType

func (p *ModifyAutoKillSessionConfigReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return ModifyAutoKillSessionConfigReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var ModifyAutoKillSessionConfigReq_EnableSQLKill_DEFAULT bool

func (p *ModifyAutoKillSessionConfigReq) GetEnableSQLKill() (v bool) {
	if !p.IsSetEnableSQLKill() {
		return ModifyAutoKillSessionConfigReq_EnableSQLKill_DEFAULT
	}
	return *p.EnableSQLKill
}

var ModifyAutoKillSessionConfigReq_MaxExecTime_DEFAULT int64

func (p *ModifyAutoKillSessionConfigReq) GetMaxExecTime() (v int64) {
	if !p.IsSetMaxExecTime() {
		return ModifyAutoKillSessionConfigReq_MaxExecTime_DEFAULT
	}
	return *p.MaxExecTime
}

var ModifyAutoKillSessionConfigReq_ProtectedUsers_DEFAULT []string

func (p *ModifyAutoKillSessionConfigReq) GetProtectedUsers() (v []string) {
	if !p.IsSetProtectedUsers() {
		return ModifyAutoKillSessionConfigReq_ProtectedUsers_DEFAULT
	}
	return p.ProtectedUsers
}
func (p *ModifyAutoKillSessionConfigReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *ModifyAutoKillSessionConfigReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *ModifyAutoKillSessionConfigReq) SetEnableSQLKill(val *bool) {
	p.EnableSQLKill = val
}
func (p *ModifyAutoKillSessionConfigReq) SetMaxExecTime(val *int64) {
	p.MaxExecTime = val
}
func (p *ModifyAutoKillSessionConfigReq) SetProtectedUsers(val []string) {
	p.ProtectedUsers = val
}

var fieldIDToName_ModifyAutoKillSessionConfigReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "EnableSQLKill",
	4: "MaxExecTime",
	5: "ProtectedUsers",
}

func (p *ModifyAutoKillSessionConfigReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *ModifyAutoKillSessionConfigReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *ModifyAutoKillSessionConfigReq) IsSetEnableSQLKill() bool {
	return p.EnableSQLKill != nil
}

func (p *ModifyAutoKillSessionConfigReq) IsSetMaxExecTime() bool {
	return p.MaxExecTime != nil
}

func (p *ModifyAutoKillSessionConfigReq) IsSetProtectedUsers() bool {
	return p.ProtectedUsers != nil
}

func (p *ModifyAutoKillSessionConfigReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAutoKillSessionConfigReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ModifyAutoKillSessionConfigReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyAutoKillSessionConfigReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *ModifyAutoKillSessionConfigReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *ModifyAutoKillSessionConfigReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.EnableSQLKill = _field
	return nil
}
func (p *ModifyAutoKillSessionConfigReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.MaxExecTime = _field
	return nil
}
func (p *ModifyAutoKillSessionConfigReq) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProtectedUsers = _field
	return nil
}

func (p *ModifyAutoKillSessionConfigReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAutoKillSessionConfigReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ModifyAutoKillSessionConfigReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAutoKillSessionConfigReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ModifyAutoKillSessionConfigReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ModifyAutoKillSessionConfigReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnableSQLKill() {
		if err = oprot.WriteFieldBegin("EnableSQLKill", thrift.BOOL, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.EnableSQLKill); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ModifyAutoKillSessionConfigReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxExecTime() {
		if err = oprot.WriteFieldBegin("MaxExecTime", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.MaxExecTime); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *ModifyAutoKillSessionConfigReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetProtectedUsers() {
		if err = oprot.WriteFieldBegin("ProtectedUsers", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ProtectedUsers)); err != nil {
			return err
		}
		for _, v := range p.ProtectedUsers {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ModifyAutoKillSessionConfigReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAutoKillSessionConfigReq(%+v)", *p)

}

func (p *ModifyAutoKillSessionConfigReq) DeepEqual(ano *ModifyAutoKillSessionConfigReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.EnableSQLKill) {
		return false
	}
	if !p.Field4DeepEqual(ano.MaxExecTime) {
		return false
	}
	if !p.Field5DeepEqual(ano.ProtectedUsers) {
		return false
	}
	return true
}

func (p *ModifyAutoKillSessionConfigReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *ModifyAutoKillSessionConfigReq) Field2DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *ModifyAutoKillSessionConfigReq) Field3DeepEqual(src *bool) bool {

	if p.EnableSQLKill == src {
		return true
	} else if p.EnableSQLKill == nil || src == nil {
		return false
	}
	if *p.EnableSQLKill != *src {
		return false
	}
	return true
}
func (p *ModifyAutoKillSessionConfigReq) Field4DeepEqual(src *int64) bool {

	if p.MaxExecTime == src {
		return true
	} else if p.MaxExecTime == nil || src == nil {
		return false
	}
	if *p.MaxExecTime != *src {
		return false
	}
	return true
}
func (p *ModifyAutoKillSessionConfigReq) Field5DeepEqual(src []string) bool {

	if len(p.ProtectedUsers) != len(src) {
		return false
	}
	for i, v := range p.ProtectedUsers {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}

type ModifyAutoKillSessionConfigResp struct {
}

func NewModifyAutoKillSessionConfigResp() *ModifyAutoKillSessionConfigResp {
	return &ModifyAutoKillSessionConfigResp{}
}

func (p *ModifyAutoKillSessionConfigResp) InitDefault() {
}

var fieldIDToName_ModifyAutoKillSessionConfigResp = map[int16]string{}

func (p *ModifyAutoKillSessionConfigResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAutoKillSessionConfigResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ModifyAutoKillSessionConfigResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ModifyAutoKillSessionConfigResp")

	if err = oprot.WriteStructBegin("ModifyAutoKillSessionConfigResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ModifyAutoKillSessionConfigResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ModifyAutoKillSessionConfigResp(%+v)", *p)

}

func (p *ModifyAutoKillSessionConfigResp) DeepEqual(ano *ModifyAutoKillSessionConfigResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type CreateSqlKillRuleReq struct {
	InstanceId      string                 `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	EffectiveTime   int32                  `thrift:"EffectiveTime,2,required" frugal:"2,required,i32" json:"EffectiveTime"`
	MaxExecTime     int64                  `thrift:"MaxExecTime,3,required" frugal:"3,required,i64" json:"MaxExecTime"`
	SqlType         []SupportedKillSqlType `thrift:"SqlType,4,optional" frugal:"4,optional,list<SupportedKillSqlType>" json:"SqlType,omitempty"`
	NodeType        []string               `thrift:"NodeType,5,optional" frugal:"5,optional,list<string>" json:"NodeType,omitempty"`
	InstanceType    *InstanceType          `thrift:"InstanceType,6,optional" frugal:"6,optional,InstanceType" json:"InstanceType,omitempty"`
	ProtectedUsers  []string               `thrift:"ProtectedUsers,7,optional" frugal:"7,optional,list<string>" json:"ProtectedUsers,omitempty"`
	Host            *string                `thrift:"Host,8,optional" frugal:"8,optional,string" json:"Host,omitempty"`
	KeyWords        *string                `thrift:"KeyWords,9,optional" frugal:"9,optional,string" json:"KeyWords,omitempty"`
	FingerPrint     *string                `thrift:"FingerPrint,10,optional" frugal:"10,optional,string" json:"FingerPrint,omitempty"`
	TerminationType *TerminationType       `thrift:"TerminationType,11,optional" frugal:"11,optional,TerminationType" json:"TerminationType,omitempty"`
}

func NewCreateSqlKillRuleReq() *CreateSqlKillRuleReq {
	return &CreateSqlKillRuleReq{}
}

func (p *CreateSqlKillRuleReq) InitDefault() {
}

func (p *CreateSqlKillRuleReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateSqlKillRuleReq) GetEffectiveTime() (v int32) {
	return p.EffectiveTime
}

func (p *CreateSqlKillRuleReq) GetMaxExecTime() (v int64) {
	return p.MaxExecTime
}

var CreateSqlKillRuleReq_SqlType_DEFAULT []SupportedKillSqlType

func (p *CreateSqlKillRuleReq) GetSqlType() (v []SupportedKillSqlType) {
	if !p.IsSetSqlType() {
		return CreateSqlKillRuleReq_SqlType_DEFAULT
	}
	return p.SqlType
}

var CreateSqlKillRuleReq_NodeType_DEFAULT []string

func (p *CreateSqlKillRuleReq) GetNodeType() (v []string) {
	if !p.IsSetNodeType() {
		return CreateSqlKillRuleReq_NodeType_DEFAULT
	}
	return p.NodeType
}

var CreateSqlKillRuleReq_InstanceType_DEFAULT InstanceType

func (p *CreateSqlKillRuleReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return CreateSqlKillRuleReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}

var CreateSqlKillRuleReq_ProtectedUsers_DEFAULT []string

func (p *CreateSqlKillRuleReq) GetProtectedUsers() (v []string) {
	if !p.IsSetProtectedUsers() {
		return CreateSqlKillRuleReq_ProtectedUsers_DEFAULT
	}
	return p.ProtectedUsers
}

var CreateSqlKillRuleReq_Host_DEFAULT string

func (p *CreateSqlKillRuleReq) GetHost() (v string) {
	if !p.IsSetHost() {
		return CreateSqlKillRuleReq_Host_DEFAULT
	}
	return *p.Host
}

var CreateSqlKillRuleReq_KeyWords_DEFAULT string

func (p *CreateSqlKillRuleReq) GetKeyWords() (v string) {
	if !p.IsSetKeyWords() {
		return CreateSqlKillRuleReq_KeyWords_DEFAULT
	}
	return *p.KeyWords
}

var CreateSqlKillRuleReq_FingerPrint_DEFAULT string

func (p *CreateSqlKillRuleReq) GetFingerPrint() (v string) {
	if !p.IsSetFingerPrint() {
		return CreateSqlKillRuleReq_FingerPrint_DEFAULT
	}
	return *p.FingerPrint
}

var CreateSqlKillRuleReq_TerminationType_DEFAULT TerminationType

func (p *CreateSqlKillRuleReq) GetTerminationType() (v TerminationType) {
	if !p.IsSetTerminationType() {
		return CreateSqlKillRuleReq_TerminationType_DEFAULT
	}
	return *p.TerminationType
}
func (p *CreateSqlKillRuleReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateSqlKillRuleReq) SetEffectiveTime(val int32) {
	p.EffectiveTime = val
}
func (p *CreateSqlKillRuleReq) SetMaxExecTime(val int64) {
	p.MaxExecTime = val
}
func (p *CreateSqlKillRuleReq) SetSqlType(val []SupportedKillSqlType) {
	p.SqlType = val
}
func (p *CreateSqlKillRuleReq) SetNodeType(val []string) {
	p.NodeType = val
}
func (p *CreateSqlKillRuleReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}
func (p *CreateSqlKillRuleReq) SetProtectedUsers(val []string) {
	p.ProtectedUsers = val
}
func (p *CreateSqlKillRuleReq) SetHost(val *string) {
	p.Host = val
}
func (p *CreateSqlKillRuleReq) SetKeyWords(val *string) {
	p.KeyWords = val
}
func (p *CreateSqlKillRuleReq) SetFingerPrint(val *string) {
	p.FingerPrint = val
}
func (p *CreateSqlKillRuleReq) SetTerminationType(val *TerminationType) {
	p.TerminationType = val
}

var fieldIDToName_CreateSqlKillRuleReq = map[int16]string{
	1:  "InstanceId",
	2:  "EffectiveTime",
	3:  "MaxExecTime",
	4:  "SqlType",
	5:  "NodeType",
	6:  "InstanceType",
	7:  "ProtectedUsers",
	8:  "Host",
	9:  "KeyWords",
	10: "FingerPrint",
	11: "TerminationType",
}

func (p *CreateSqlKillRuleReq) IsSetSqlType() bool {
	return p.SqlType != nil
}

func (p *CreateSqlKillRuleReq) IsSetNodeType() bool {
	return p.NodeType != nil
}

func (p *CreateSqlKillRuleReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *CreateSqlKillRuleReq) IsSetProtectedUsers() bool {
	return p.ProtectedUsers != nil
}

func (p *CreateSqlKillRuleReq) IsSetHost() bool {
	return p.Host != nil
}

func (p *CreateSqlKillRuleReq) IsSetKeyWords() bool {
	return p.KeyWords != nil
}

func (p *CreateSqlKillRuleReq) IsSetFingerPrint() bool {
	return p.FingerPrint != nil
}

func (p *CreateSqlKillRuleReq) IsSetTerminationType() bool {
	return p.TerminationType != nil
}

func (p *CreateSqlKillRuleReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlKillRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetEffectiveTime bool = false
	var issetMaxExecTime bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetEffectiveTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetMaxExecTime = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetEffectiveTime {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetMaxExecTime {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateSqlKillRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateSqlKillRuleReq[fieldId]))
}

func (p *CreateSqlKillRuleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateSqlKillRuleReq) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.EffectiveTime = _field
	return nil
}
func (p *CreateSqlKillRuleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.MaxExecTime = _field
	return nil
}
func (p *CreateSqlKillRuleReq) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]SupportedKillSqlType, 0, size)
	for i := 0; i < size; i++ {

		var _elem SupportedKillSqlType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = SupportedKillSqlType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SqlType = _field
	return nil
}
func (p *CreateSqlKillRuleReq) ReadField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.NodeType = _field
	return nil
}
func (p *CreateSqlKillRuleReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}
func (p *CreateSqlKillRuleReq) ReadField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ProtectedUsers = _field
	return nil
}
func (p *CreateSqlKillRuleReq) ReadField8(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Host = _field
	return nil
}
func (p *CreateSqlKillRuleReq) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.KeyWords = _field
	return nil
}
func (p *CreateSqlKillRuleReq) ReadField10(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FingerPrint = _field
	return nil
}
func (p *CreateSqlKillRuleReq) ReadField11(iprot thrift.TProtocol) error {

	var _field *TerminationType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TerminationType(v)
		_field = &tmp
	}
	p.TerminationType = _field
	return nil
}

func (p *CreateSqlKillRuleReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlKillRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSqlKillRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("EffectiveTime", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.EffectiveTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("MaxExecTime", thrift.I64, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.MaxExecTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSqlType() {
		if err = oprot.WriteFieldBegin("SqlType", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SqlType)); err != nil {
			return err
		}
		for _, v := range p.SqlType {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetNodeType() {
		if err = oprot.WriteFieldBegin("NodeType", thrift.LIST, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NodeType)); err != nil {
			return err
		}
		for _, v := range p.NodeType {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetProtectedUsers() {
		if err = oprot.WriteFieldBegin("ProtectedUsers", thrift.LIST, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ProtectedUsers)); err != nil {
			return err
		}
		for _, v := range p.ProtectedUsers {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetHost() {
		if err = oprot.WriteFieldBegin("Host", thrift.STRING, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Host); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetKeyWords() {
		if err = oprot.WriteFieldBegin("KeyWords", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.KeyWords); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetFingerPrint() {
		if err = oprot.WriteFieldBegin("FingerPrint", thrift.STRING, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FingerPrint); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetTerminationType() {
		if err = oprot.WriteFieldBegin("TerminationType", thrift.I32, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.TerminationType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *CreateSqlKillRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSqlKillRuleReq(%+v)", *p)

}

func (p *CreateSqlKillRuleReq) DeepEqual(ano *CreateSqlKillRuleReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.EffectiveTime) {
		return false
	}
	if !p.Field3DeepEqual(ano.MaxExecTime) {
		return false
	}
	if !p.Field4DeepEqual(ano.SqlType) {
		return false
	}
	if !p.Field5DeepEqual(ano.NodeType) {
		return false
	}
	if !p.Field6DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field7DeepEqual(ano.ProtectedUsers) {
		return false
	}
	if !p.Field8DeepEqual(ano.Host) {
		return false
	}
	if !p.Field9DeepEqual(ano.KeyWords) {
		return false
	}
	if !p.Field10DeepEqual(ano.FingerPrint) {
		return false
	}
	if !p.Field11DeepEqual(ano.TerminationType) {
		return false
	}
	return true
}

func (p *CreateSqlKillRuleReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlKillRuleReq) Field2DeepEqual(src int32) bool {

	if p.EffectiveTime != src {
		return false
	}
	return true
}
func (p *CreateSqlKillRuleReq) Field3DeepEqual(src int64) bool {

	if p.MaxExecTime != src {
		return false
	}
	return true
}
func (p *CreateSqlKillRuleReq) Field4DeepEqual(src []SupportedKillSqlType) bool {

	if len(p.SqlType) != len(src) {
		return false
	}
	for i, v := range p.SqlType {
		_src := src[i]
		if v != _src {
			return false
		}
	}
	return true
}
func (p *CreateSqlKillRuleReq) Field5DeepEqual(src []string) bool {

	if len(p.NodeType) != len(src) {
		return false
	}
	for i, v := range p.NodeType {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *CreateSqlKillRuleReq) Field6DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}
func (p *CreateSqlKillRuleReq) Field7DeepEqual(src []string) bool {

	if len(p.ProtectedUsers) != len(src) {
		return false
	}
	for i, v := range p.ProtectedUsers {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *CreateSqlKillRuleReq) Field8DeepEqual(src *string) bool {

	if p.Host == src {
		return true
	} else if p.Host == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Host, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlKillRuleReq) Field9DeepEqual(src *string) bool {

	if p.KeyWords == src {
		return true
	} else if p.KeyWords == nil || src == nil {
		return false
	}
	if strings.Compare(*p.KeyWords, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlKillRuleReq) Field10DeepEqual(src *string) bool {

	if p.FingerPrint == src {
		return true
	} else if p.FingerPrint == nil || src == nil {
		return false
	}
	if strings.Compare(*p.FingerPrint, *src) != 0 {
		return false
	}
	return true
}
func (p *CreateSqlKillRuleReq) Field11DeepEqual(src *TerminationType) bool {

	if p.TerminationType == src {
		return true
	} else if p.TerminationType == nil || src == nil {
		return false
	}
	if *p.TerminationType != *src {
		return false
	}
	return true
}

type CreateSqlKillRuleResp struct {
}

func NewCreateSqlKillRuleResp() *CreateSqlKillRuleResp {
	return &CreateSqlKillRuleResp{}
}

func (p *CreateSqlKillRuleResp) InitDefault() {
}

var fieldIDToName_CreateSqlKillRuleResp = map[int16]string{}

func (p *CreateSqlKillRuleResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlKillRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateSqlKillRuleResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateSqlKillRuleResp")

	if err = oprot.WriteStructBegin("CreateSqlKillRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateSqlKillRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateSqlKillRuleResp(%+v)", *p)

}

func (p *CreateSqlKillRuleResp) DeepEqual(ano *CreateSqlKillRuleResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DeleteSqlKillRuleReq struct {
	InstanceId   string        `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	TaskId       []string      `thrift:"TaskId,2,optional" frugal:"2,optional,list<string>" json:"TaskId,omitempty"`
	InstanceType *InstanceType `thrift:"InstanceType,3,optional" frugal:"3,optional,InstanceType" json:"InstanceType,omitempty"`
}

func NewDeleteSqlKillRuleReq() *DeleteSqlKillRuleReq {
	return &DeleteSqlKillRuleReq{}
}

func (p *DeleteSqlKillRuleReq) InitDefault() {
}

func (p *DeleteSqlKillRuleReq) GetInstanceId() (v string) {
	return p.InstanceId
}

var DeleteSqlKillRuleReq_TaskId_DEFAULT []string

func (p *DeleteSqlKillRuleReq) GetTaskId() (v []string) {
	if !p.IsSetTaskId() {
		return DeleteSqlKillRuleReq_TaskId_DEFAULT
	}
	return p.TaskId
}

var DeleteSqlKillRuleReq_InstanceType_DEFAULT InstanceType

func (p *DeleteSqlKillRuleReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return DeleteSqlKillRuleReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}
func (p *DeleteSqlKillRuleReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DeleteSqlKillRuleReq) SetTaskId(val []string) {
	p.TaskId = val
}
func (p *DeleteSqlKillRuleReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}

var fieldIDToName_DeleteSqlKillRuleReq = map[int16]string{
	1: "InstanceId",
	2: "TaskId",
	3: "InstanceType",
}

func (p *DeleteSqlKillRuleReq) IsSetTaskId() bool {
	return p.TaskId != nil
}

func (p *DeleteSqlKillRuleReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DeleteSqlKillRuleReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteSqlKillRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteSqlKillRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteSqlKillRuleReq[fieldId]))
}

func (p *DeleteSqlKillRuleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DeleteSqlKillRuleReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TaskId = _field
	return nil
}
func (p *DeleteSqlKillRuleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}

func (p *DeleteSqlKillRuleReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteSqlKillRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteSqlKillRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteSqlKillRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteSqlKillRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskId() {
		if err = oprot.WriteFieldBegin("TaskId", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TaskId)); err != nil {
			return err
		}
		for _, v := range p.TaskId {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteSqlKillRuleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DeleteSqlKillRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteSqlKillRuleReq(%+v)", *p)

}

func (p *DeleteSqlKillRuleReq) DeepEqual(ano *DeleteSqlKillRuleReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DeleteSqlKillRuleReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DeleteSqlKillRuleReq) Field2DeepEqual(src []string) bool {

	if len(p.TaskId) != len(src) {
		return false
	}
	for i, v := range p.TaskId {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *DeleteSqlKillRuleReq) Field3DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}

type DeleteSqlKillRuleResp struct {
}

func NewDeleteSqlKillRuleResp() *DeleteSqlKillRuleResp {
	return &DeleteSqlKillRuleResp{}
}

func (p *DeleteSqlKillRuleResp) InitDefault() {
}

var fieldIDToName_DeleteSqlKillRuleResp = map[int16]string{}

func (p *DeleteSqlKillRuleResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteSqlKillRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteSqlKillRuleResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DeleteSqlKillRuleResp")

	if err = oprot.WriteStructBegin("DeleteSqlKillRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteSqlKillRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteSqlKillRuleResp(%+v)", *p)

}

func (p *DeleteSqlKillRuleResp) DeepEqual(ano *DeleteSqlKillRuleResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type StopSqlKillRuleReq struct {
	InstanceId   *string       `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	TaskId       []string      `thrift:"TaskId,2,optional" frugal:"2,optional,list<string>" json:"TaskId,omitempty"`
	InstanceType *InstanceType `thrift:"InstanceType,3,optional" frugal:"3,optional,InstanceType" json:"InstanceType,omitempty"`
}

func NewStopSqlKillRuleReq() *StopSqlKillRuleReq {
	return &StopSqlKillRuleReq{}
}

func (p *StopSqlKillRuleReq) InitDefault() {
}

var StopSqlKillRuleReq_InstanceId_DEFAULT string

func (p *StopSqlKillRuleReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return StopSqlKillRuleReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var StopSqlKillRuleReq_TaskId_DEFAULT []string

func (p *StopSqlKillRuleReq) GetTaskId() (v []string) {
	if !p.IsSetTaskId() {
		return StopSqlKillRuleReq_TaskId_DEFAULT
	}
	return p.TaskId
}

var StopSqlKillRuleReq_InstanceType_DEFAULT InstanceType

func (p *StopSqlKillRuleReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return StopSqlKillRuleReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}
func (p *StopSqlKillRuleReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *StopSqlKillRuleReq) SetTaskId(val []string) {
	p.TaskId = val
}
func (p *StopSqlKillRuleReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}

var fieldIDToName_StopSqlKillRuleReq = map[int16]string{
	1: "InstanceId",
	2: "TaskId",
	3: "InstanceType",
}

func (p *StopSqlKillRuleReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *StopSqlKillRuleReq) IsSetTaskId() bool {
	return p.TaskId != nil
}

func (p *StopSqlKillRuleReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *StopSqlKillRuleReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopSqlKillRuleReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_StopSqlKillRuleReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *StopSqlKillRuleReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *StopSqlKillRuleReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.TaskId = _field
	return nil
}
func (p *StopSqlKillRuleReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}

func (p *StopSqlKillRuleReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopSqlKillRuleReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("StopSqlKillRuleReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StopSqlKillRuleReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *StopSqlKillRuleReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskId() {
		if err = oprot.WriteFieldBegin("TaskId", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TaskId)); err != nil {
			return err
		}
		for _, v := range p.TaskId {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *StopSqlKillRuleReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *StopSqlKillRuleReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopSqlKillRuleReq(%+v)", *p)

}

func (p *StopSqlKillRuleReq) DeepEqual(ano *StopSqlKillRuleReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.TaskId) {
		return false
	}
	if !p.Field3DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *StopSqlKillRuleReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *StopSqlKillRuleReq) Field2DeepEqual(src []string) bool {

	if len(p.TaskId) != len(src) {
		return false
	}
	for i, v := range p.TaskId {
		_src := src[i]
		if strings.Compare(v, _src) != 0 {
			return false
		}
	}
	return true
}
func (p *StopSqlKillRuleReq) Field3DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}

type StopSqlKillRuleResp struct {
}

func NewStopSqlKillRuleResp() *StopSqlKillRuleResp {
	return &StopSqlKillRuleResp{}
}

func (p *StopSqlKillRuleResp) InitDefault() {
}

var fieldIDToName_StopSqlKillRuleResp = map[int16]string{}

func (p *StopSqlKillRuleResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopSqlKillRuleResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *StopSqlKillRuleResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("StopSqlKillRuleResp")

	if err = oprot.WriteStructBegin("StopSqlKillRuleResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *StopSqlKillRuleResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StopSqlKillRuleResp(%+v)", *p)

}

func (p *StopSqlKillRuleResp) DeepEqual(ano *StopSqlKillRuleResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeSqlKillRulesReq struct {
	InstanceId   *string           `thrift:"InstanceId,1,optional" frugal:"1,optional,string" json:"InstanceId,omitempty"`
	SearchParam  *SearchParam      `thrift:"SearchParam,2,optional" frugal:"2,optional,SearchParam" json:"SearchParam,omitempty"`
	PageNumber   *int32            `thrift:"PageNumber,3,optional" frugal:"3,optional,i32" json:"PageNumber,omitempty"`
	PageSize     *int32            `thrift:"PageSize,4,optional" frugal:"4,optional,i32" json:"PageSize,omitempty"`
	SortBy       *SortBy           `thrift:"SortBy,5,optional" frugal:"5,optional,SortBy" json:"SortBy,omitempty"`
	OrderBy      *OrderByForDBRule `thrift:"OrderBy,6,optional" frugal:"6,optional,OrderByForDBRule" json:"OrderBy,omitempty"`
	InstanceType *InstanceType     `thrift:"InstanceType,7,optional" frugal:"7,optional,InstanceType" json:"InstanceType,omitempty"`
}

func NewDescribeSqlKillRulesReq() *DescribeSqlKillRulesReq {
	return &DescribeSqlKillRulesReq{}
}

func (p *DescribeSqlKillRulesReq) InitDefault() {
}

var DescribeSqlKillRulesReq_InstanceId_DEFAULT string

func (p *DescribeSqlKillRulesReq) GetInstanceId() (v string) {
	if !p.IsSetInstanceId() {
		return DescribeSqlKillRulesReq_InstanceId_DEFAULT
	}
	return *p.InstanceId
}

var DescribeSqlKillRulesReq_SearchParam_DEFAULT *SearchParam

func (p *DescribeSqlKillRulesReq) GetSearchParam() (v *SearchParam) {
	if !p.IsSetSearchParam() {
		return DescribeSqlKillRulesReq_SearchParam_DEFAULT
	}
	return p.SearchParam
}

var DescribeSqlKillRulesReq_PageNumber_DEFAULT int32

func (p *DescribeSqlKillRulesReq) GetPageNumber() (v int32) {
	if !p.IsSetPageNumber() {
		return DescribeSqlKillRulesReq_PageNumber_DEFAULT
	}
	return *p.PageNumber
}

var DescribeSqlKillRulesReq_PageSize_DEFAULT int32

func (p *DescribeSqlKillRulesReq) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return DescribeSqlKillRulesReq_PageSize_DEFAULT
	}
	return *p.PageSize
}

var DescribeSqlKillRulesReq_SortBy_DEFAULT SortBy

func (p *DescribeSqlKillRulesReq) GetSortBy() (v SortBy) {
	if !p.IsSetSortBy() {
		return DescribeSqlKillRulesReq_SortBy_DEFAULT
	}
	return *p.SortBy
}

var DescribeSqlKillRulesReq_OrderBy_DEFAULT OrderByForDBRule

func (p *DescribeSqlKillRulesReq) GetOrderBy() (v OrderByForDBRule) {
	if !p.IsSetOrderBy() {
		return DescribeSqlKillRulesReq_OrderBy_DEFAULT
	}
	return *p.OrderBy
}

var DescribeSqlKillRulesReq_InstanceType_DEFAULT InstanceType

func (p *DescribeSqlKillRulesReq) GetInstanceType() (v InstanceType) {
	if !p.IsSetInstanceType() {
		return DescribeSqlKillRulesReq_InstanceType_DEFAULT
	}
	return *p.InstanceType
}
func (p *DescribeSqlKillRulesReq) SetInstanceId(val *string) {
	p.InstanceId = val
}
func (p *DescribeSqlKillRulesReq) SetSearchParam(val *SearchParam) {
	p.SearchParam = val
}
func (p *DescribeSqlKillRulesReq) SetPageNumber(val *int32) {
	p.PageNumber = val
}
func (p *DescribeSqlKillRulesReq) SetPageSize(val *int32) {
	p.PageSize = val
}
func (p *DescribeSqlKillRulesReq) SetSortBy(val *SortBy) {
	p.SortBy = val
}
func (p *DescribeSqlKillRulesReq) SetOrderBy(val *OrderByForDBRule) {
	p.OrderBy = val
}
func (p *DescribeSqlKillRulesReq) SetInstanceType(val *InstanceType) {
	p.InstanceType = val
}

var fieldIDToName_DescribeSqlKillRulesReq = map[int16]string{
	1: "InstanceId",
	2: "SearchParam",
	3: "PageNumber",
	4: "PageSize",
	5: "SortBy",
	6: "OrderBy",
	7: "InstanceType",
}

func (p *DescribeSqlKillRulesReq) IsSetInstanceId() bool {
	return p.InstanceId != nil
}

func (p *DescribeSqlKillRulesReq) IsSetSearchParam() bool {
	return p.SearchParam != nil
}

func (p *DescribeSqlKillRulesReq) IsSetPageNumber() bool {
	return p.PageNumber != nil
}

func (p *DescribeSqlKillRulesReq) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *DescribeSqlKillRulesReq) IsSetSortBy() bool {
	return p.SortBy != nil
}

func (p *DescribeSqlKillRulesReq) IsSetOrderBy() bool {
	return p.OrderBy != nil
}

func (p *DescribeSqlKillRulesReq) IsSetInstanceType() bool {
	return p.InstanceType != nil
}

func (p *DescribeSqlKillRulesReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlKillRulesReq")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlKillRulesReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DescribeSqlKillRulesReq) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeSqlKillRulesReq) ReadField2(iprot thrift.TProtocol) error {
	_field := NewSearchParam()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.SearchParam = _field
	return nil
}
func (p *DescribeSqlKillRulesReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageNumber = _field
	return nil
}
func (p *DescribeSqlKillRulesReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}
func (p *DescribeSqlKillRulesReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *SortBy
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := SortBy(v)
		_field = &tmp
	}
	p.SortBy = _field
	return nil
}
func (p *DescribeSqlKillRulesReq) ReadField6(iprot thrift.TProtocol) error {

	var _field *OrderByForDBRule
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := OrderByForDBRule(v)
		_field = &tmp
	}
	p.OrderBy = _field
	return nil
}
func (p *DescribeSqlKillRulesReq) ReadField7(iprot thrift.TProtocol) error {

	var _field *InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := InstanceType(v)
		_field = &tmp
	}
	p.InstanceType = _field
	return nil
}

func (p *DescribeSqlKillRulesReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlKillRulesReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlKillRulesReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlKillRulesReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceId() {
		if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.InstanceId); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlKillRulesReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchParam() {
		if err = oprot.WriteFieldBegin("SearchParam", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.SearchParam.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlKillRulesReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageNumber() {
		if err = oprot.WriteFieldBegin("PageNumber", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageNumber); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeSqlKillRulesReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("PageSize", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeSqlKillRulesReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSortBy() {
		if err = oprot.WriteFieldBegin("SortBy", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.SortBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *DescribeSqlKillRulesReq) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderBy() {
		if err = oprot.WriteFieldBegin("OrderBy", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.OrderBy)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *DescribeSqlKillRulesReq) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetInstanceType() {
		if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.InstanceType)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}

func (p *DescribeSqlKillRulesReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlKillRulesReq(%+v)", *p)

}

func (p *DescribeSqlKillRulesReq) DeepEqual(ano *DescribeSqlKillRulesReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.SearchParam) {
		return false
	}
	if !p.Field3DeepEqual(ano.PageNumber) {
		return false
	}
	if !p.Field4DeepEqual(ano.PageSize) {
		return false
	}
	if !p.Field5DeepEqual(ano.SortBy) {
		return false
	}
	if !p.Field6DeepEqual(ano.OrderBy) {
		return false
	}
	if !p.Field7DeepEqual(ano.InstanceType) {
		return false
	}
	return true
}

func (p *DescribeSqlKillRulesReq) Field1DeepEqual(src *string) bool {

	if p.InstanceId == src {
		return true
	} else if p.InstanceId == nil || src == nil {
		return false
	}
	if strings.Compare(*p.InstanceId, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeSqlKillRulesReq) Field2DeepEqual(src *SearchParam) bool {

	if !p.SearchParam.DeepEqual(src) {
		return false
	}
	return true
}
func (p *DescribeSqlKillRulesReq) Field3DeepEqual(src *int32) bool {

	if p.PageNumber == src {
		return true
	} else if p.PageNumber == nil || src == nil {
		return false
	}
	if *p.PageNumber != *src {
		return false
	}
	return true
}
func (p *DescribeSqlKillRulesReq) Field4DeepEqual(src *int32) bool {

	if p.PageSize == src {
		return true
	} else if p.PageSize == nil || src == nil {
		return false
	}
	if *p.PageSize != *src {
		return false
	}
	return true
}
func (p *DescribeSqlKillRulesReq) Field5DeepEqual(src *SortBy) bool {

	if p.SortBy == src {
		return true
	} else if p.SortBy == nil || src == nil {
		return false
	}
	if *p.SortBy != *src {
		return false
	}
	return true
}
func (p *DescribeSqlKillRulesReq) Field6DeepEqual(src *OrderByForDBRule) bool {

	if p.OrderBy == src {
		return true
	} else if p.OrderBy == nil || src == nil {
		return false
	}
	if *p.OrderBy != *src {
		return false
	}
	return true
}
func (p *DescribeSqlKillRulesReq) Field7DeepEqual(src *InstanceType) bool {

	if p.InstanceType == src {
		return true
	} else if p.InstanceType == nil || src == nil {
		return false
	}
	if *p.InstanceType != *src {
		return false
	}
	return true
}

type DescribeSqlKillRulesResp struct {
	SQLKillRules []*SQLKillRule `thrift:"SQLKillRules,1,required" frugal:"1,required,list<SQLKillRule>" json:"SQLKillRules"`
	Total        int32          `thrift:"Total,2,required" frugal:"2,required,i32" json:"Total"`
}

func NewDescribeSqlKillRulesResp() *DescribeSqlKillRulesResp {
	return &DescribeSqlKillRulesResp{}
}

func (p *DescribeSqlKillRulesResp) InitDefault() {
}

func (p *DescribeSqlKillRulesResp) GetSQLKillRules() (v []*SQLKillRule) {
	return p.SQLKillRules
}

func (p *DescribeSqlKillRulesResp) GetTotal() (v int32) {
	return p.Total
}
func (p *DescribeSqlKillRulesResp) SetSQLKillRules(val []*SQLKillRule) {
	p.SQLKillRules = val
}
func (p *DescribeSqlKillRulesResp) SetTotal(val int32) {
	p.Total = val
}

var fieldIDToName_DescribeSqlKillRulesResp = map[int16]string{
	1: "SQLKillRules",
	2: "Total",
}

func (p *DescribeSqlKillRulesResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlKillRulesResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSQLKillRules bool = false
	var issetTotal bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetSQLKillRules = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetTotal = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSQLKillRules {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetTotal {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeSqlKillRulesResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeSqlKillRulesResp[fieldId]))
}

func (p *DescribeSqlKillRulesResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*SQLKillRule, 0, size)
	values := make([]SQLKillRule, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SQLKillRules = _field
	return nil
}
func (p *DescribeSqlKillRulesResp) ReadField2(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Total = _field
	return nil
}

func (p *DescribeSqlKillRulesResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeSqlKillRulesResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeSqlKillRulesResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeSqlKillRulesResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("SQLKillRules", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SQLKillRules)); err != nil {
		return err
	}
	for _, v := range p.SQLKillRules {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeSqlKillRulesResp) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Total", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Total); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeSqlKillRulesResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeSqlKillRulesResp(%+v)", *p)

}

func (p *DescribeSqlKillRulesResp) DeepEqual(ano *DescribeSqlKillRulesResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.SQLKillRules) {
		return false
	}
	if !p.Field2DeepEqual(ano.Total) {
		return false
	}
	return true
}

func (p *DescribeSqlKillRulesResp) Field1DeepEqual(src []*SQLKillRule) bool {

	if len(p.SQLKillRules) != len(src) {
		return false
	}
	for i, v := range p.SQLKillRules {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}
func (p *DescribeSqlKillRulesResp) Field2DeepEqual(src int32) bool {

	if p.Total != src {
		return false
	}
	return true
}
