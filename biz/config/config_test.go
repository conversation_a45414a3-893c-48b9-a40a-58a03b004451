package config

import (
	"container/list"
	"fmt"
	"os"
	"reflect"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	//"code.byted.org/infcs/ds-lib/framework/util"
)

func TestConfig_GetTlsEndpointRegion(t *testing.T) {
	type fields struct {
		MaxConnectionPerDataSource       int64
		MaxCommandResultCount            int64
		MaxCommandResultBytes            int64
		MaxCommandResultCellBytes        int64
		CommandResultTimeout             int64
		SessionTimeout                   int64
		ConnectionConnectTimeout         int64
		ConnectionReadTimeout            int64
		ConnectionWriteTimeout           int64
		ConnectionIdleTimeout            int64
		MaxCommandLength                 int64
		MaxLoginFailCount                int64
		MaxLoginFailCDSeconds            int64
		EnableMySQLDelimiter             bool
		EnableVeDB                       bool
		RDSMgrAddress                    string
		MetaRDSMgrAddress                string
		VeDBMgrAddress                   string
		RedisMgrAddress                  string
		MongoMgrAddress                  string
		InfraMgrAddress                  string
		UpgradeMgrAddress                string
		MgrSentryDSN                     string
		ConnectionsSentryDSN             string
		CollectorDSN                     string
		BlackRegions                     []string
		DeployInfo                       []*shared.ClusterInfo
		ConnectionsReplica               int64
		ConnectionsVersionSetID          int
		ConnectionsImage                 string
		MonitorReplicas                  int64
		MonitorImage                     string
		MonitorVersionSetID              int
		UserEnv                          map[string]string
		EnableVeDBNewWhiteList           bool
		IAMProfile                       string
		ServiceLinkRule                  string
		ServiceLinkRuleAudit             string
		ServiceLinkRuleDataMigration     string
		TempServiceLinkRuleDataMigration string
		NodePool                         string
		SessionPoolMaxIdleSeconds        int64
		SessionPoolMaxUsedSeconds        int64
		SessionMgrCleanIntervalSeconds   int64
		VPCServiceEndpoint               string
		TOPServiceAccessKey              string
		TOPServiceSecretKey              string
		CloudMonitorEndpoint             string
		EnableVeDBNewOpenAPI             bool
		EnableRDSNewWhiteList            bool
		EnableRemoveWhiteList            bool
		EnableRDSMySQLNewOpenAPI         bool
		ShuttleMgrTimeoutSeconds         uint64
		ShuttleExpiredSeconds            int64
		DialogCollectionIntervalSeconds  int64
		TLSDialogDetailTopic             string
		TLSEngineStatusTopic             string
		ComponentsDeployInfo             *shared.Components
		AuditPodInitContainerImage       string
		AuditPodPacketbeatImage          string
		InternalIPs                      string
		InternalUsers                    string
		AuditListenPorts                 string
		InternalAccounts                 []string
		AuditCheckIntervalMinutes        int64
		AuditJobMaxRetryTimes            int64
		AuditPodMaxPendingMinutes        int64
		ByteBrainServerReplicas          int64
		ByteBrainImage                   string
		ByteBrainDeployInfo              []*shared.ClusterInfo
		ByteBrainAddress                 string
		VolcProfile                      string
		VolcServiceAccountAK             string
		VolcServiceAccountSK             string
		EnableBillingTenantIDList        []string
		DisableBillingTenantIDList       []string
		BillingAK                        string
		BillingSK                        string
		BillingCluster                   string
		BillingTopic                     string
		BillingConsumerGroup             string
		BillingReleaseInstance           bool
		EnableOnlineDDLTenantIDList      []string
		PublishEventBlackList            []string
		EnableDescribeDBProxy            bool
		DataMigToolContainerImage        string
		DataMigToolBatchNum              int
		S3MaxBandWidthMb                 int
		DisableImportSqlStatementList    string
		IsOpenAccountSysDB               bool
		BpmDefaultConfigId               int64
		BpmFlowUrl                       string
		BpmBasicAuthUser                 string
		BpmBasicAuthPassword             string
		DbwUserMgmtForIAMRole            string
		MgrPodCIDR                       string
		TlsEndpoint                      string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name:   "",
			fields: fields{TlsEndpoint: "https://tls-cn-guilin-boe-inner.ivolces.com"},
			want:   "cn-guilin-boe",
		},
	}
	os.Setenv("BDC_REGION_ID", "cn-guilin-boe")
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Config{
				MaxConnectionPerDataSource:       tt.fields.MaxConnectionPerDataSource,
				MaxCommandResultCount:            tt.fields.MaxCommandResultCount,
				MaxCommandResultBytes:            tt.fields.MaxCommandResultBytes,
				MaxCommandResultCellBytes:        tt.fields.MaxCommandResultCellBytes,
				CommandResultTimeout:             tt.fields.CommandResultTimeout,
				SessionTimeout:                   tt.fields.SessionTimeout,
				ConnectionConnectTimeout:         tt.fields.ConnectionConnectTimeout,
				ConnectionReadTimeout:            tt.fields.ConnectionReadTimeout,
				ConnectionWriteTimeout:           tt.fields.ConnectionWriteTimeout,
				ConnectionIdleTimeout:            tt.fields.ConnectionIdleTimeout,
				MaxCommandLength:                 tt.fields.MaxCommandLength,
				MaxLoginFailCount:                tt.fields.MaxLoginFailCount,
				MaxLoginFailCDSeconds:            tt.fields.MaxLoginFailCDSeconds,
				EnableMySQLDelimiter:             tt.fields.EnableMySQLDelimiter,
				EnableVeDB:                       tt.fields.EnableVeDB,
				RDSMgrAddress:                    tt.fields.RDSMgrAddress,
				MetaRDSMgrAddress:                tt.fields.MetaRDSMgrAddress,
				VeDBMgrAddress:                   tt.fields.VeDBMgrAddress,
				RedisMgrAddress:                  tt.fields.RedisMgrAddress,
				MongoMgrAddress:                  tt.fields.MongoMgrAddress,
				InfraMgrAddress:                  tt.fields.InfraMgrAddress,
				UpgradeMgrAddress:                tt.fields.UpgradeMgrAddress,
				MgrSentryDSN:                     tt.fields.MgrSentryDSN,
				ConnectionsSentryDSN:             tt.fields.ConnectionsSentryDSN,
				CollectorDSN:                     tt.fields.CollectorDSN,
				BlackRegions:                     tt.fields.BlackRegions,
				DeployInfo:                       tt.fields.DeployInfo,
				ConnectionsReplica:               tt.fields.ConnectionsReplica,
				ConnectionsVersionSetID:          tt.fields.ConnectionsVersionSetID,
				ConnectionsImage:                 tt.fields.ConnectionsImage,
				MonitorReplicas:                  tt.fields.MonitorReplicas,
				MonitorImage:                     tt.fields.MonitorImage,
				MonitorVersionSetID:              tt.fields.MonitorVersionSetID,
				UserEnv:                          tt.fields.UserEnv,
				EnableVeDBNewWhiteList:           tt.fields.EnableVeDBNewWhiteList,
				IAMProfile:                       tt.fields.IAMProfile,
				ServiceLinkRule:                  tt.fields.ServiceLinkRule,
				ServiceLinkRuleAudit:             tt.fields.ServiceLinkRuleAudit,
				ServiceLinkRuleDataMigration:     tt.fields.ServiceLinkRuleDataMigration,
				TempServiceLinkRuleDataMigration: tt.fields.TempServiceLinkRuleDataMigration,
				NodePool:                         tt.fields.NodePool,
				SessionPoolMaxIdleSeconds:        tt.fields.SessionPoolMaxIdleSeconds,
				SessionPoolMaxUsedSeconds:        tt.fields.SessionPoolMaxUsedSeconds,
				SessionMgrCleanIntervalSeconds:   tt.fields.SessionMgrCleanIntervalSeconds,
				VPCServiceEndpoint:               tt.fields.VPCServiceEndpoint,
				TOPServiceAccessKey:              tt.fields.TOPServiceAccessKey,
				TOPServiceSecretKey:              tt.fields.TOPServiceSecretKey,
				CloudMonitorEndpoint:             tt.fields.CloudMonitorEndpoint,
				EnableVeDBNewOpenAPI:             tt.fields.EnableVeDBNewOpenAPI,
				EnableRDSNewWhiteList:            tt.fields.EnableRDSNewWhiteList,
				EnableRemoveWhiteList:            tt.fields.EnableRemoveWhiteList,
				EnableRDSMySQLNewOpenAPI:         tt.fields.EnableRDSMySQLNewOpenAPI,
				ShuttleMgrTimeoutSeconds:         tt.fields.ShuttleMgrTimeoutSeconds,
				ShuttleExpiredSeconds:            tt.fields.ShuttleExpiredSeconds,
				DialogCollectionIntervalSeconds:  tt.fields.DialogCollectionIntervalSeconds,
				TLSDialogDetailTopic:             tt.fields.TLSDialogDetailTopic,
				TLSEngineStatusTopic:             tt.fields.TLSEngineStatusTopic,
				ComponentsDeployInfo:             tt.fields.ComponentsDeployInfo,
				AuditPodInitContainerImage:       tt.fields.AuditPodInitContainerImage,
				AuditPodPacketbeatImage:          tt.fields.AuditPodPacketbeatImage,
				InternalIPs:                      tt.fields.InternalIPs,
				AuditListenPorts:                 tt.fields.AuditListenPorts,
				InternalAccounts:                 tt.fields.InternalAccounts,
				AuditCheckIntervalMinutes:        tt.fields.AuditCheckIntervalMinutes,
				AuditJobMaxRetryTimes:            tt.fields.AuditJobMaxRetryTimes,
				AuditPodMaxPendingMinutes:        tt.fields.AuditPodMaxPendingMinutes,
				ByteBrainServerReplicas:          tt.fields.ByteBrainServerReplicas,
				ByteBrainImage:                   tt.fields.ByteBrainImage,
				ByteBrainDeployInfo:              tt.fields.ByteBrainDeployInfo,
				ByteBrainAddress:                 tt.fields.ByteBrainAddress,
				VolcProfile:                      tt.fields.VolcProfile,
				VolcServiceAccountAK:             tt.fields.VolcServiceAccountAK,
				VolcServiceAccountSK:             tt.fields.VolcServiceAccountSK,
				EnableBillingTenantIDList:        tt.fields.EnableBillingTenantIDList,
				DisableBillingTenantIDList:       tt.fields.DisableBillingTenantIDList,
				BillingAK:                        tt.fields.BillingAK,
				BillingSK:                        tt.fields.BillingSK,
				BillingCluster:                   tt.fields.BillingCluster,
				BillingTopic:                     tt.fields.BillingTopic,
				BillingConsumerGroup:             tt.fields.BillingConsumerGroup,
				BillingReleaseInstance:           tt.fields.BillingReleaseInstance,
				EnableOnlineDDLTenantIDList:      tt.fields.EnableOnlineDDLTenantIDList,
				PublishEventBlackList:            tt.fields.PublishEventBlackList,
				EnableDescribeDBProxy:            tt.fields.EnableDescribeDBProxy,
				DataMigToolContainerImage:        tt.fields.DataMigToolContainerImage,
				DataMigToolBatchNum:              tt.fields.DataMigToolBatchNum,
				S3MaxBandWidthMb:                 tt.fields.S3MaxBandWidthMb,
				DisableImportSqlStatementList:    tt.fields.DisableImportSqlStatementList,
				IsOpenAccountSysDB:               tt.fields.IsOpenAccountSysDB,
				BpmDefaultConfigId:               tt.fields.BpmDefaultConfigId,
				BpmFlowUrl:                       tt.fields.BpmFlowUrl,
				BpmBasicAuthUser:                 tt.fields.BpmBasicAuthUser,
				BpmBasicAuthPassword:             tt.fields.BpmBasicAuthPassword,
				DbwUserMgmtForIAMRole:            tt.fields.DbwUserMgmtForIAMRole,
				MgrPodCIDR:                       tt.fields.MgrPodCIDR,
				TlsServiceEndpoint:               tt.fields.TlsEndpoint,
			}
			if got, err := c.GetTLSRegion("VolcanoEngine"); got != tt.want && err == nil {
				t.Errorf("GetTLSRegion() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConfig_GetTlsRegionEndpoint(t *testing.T) {
	type fields struct {
		TlsServiceEndpoint string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "",
			fields: fields{
				TlsServiceEndpoint: "",
			},
			want: "https://tls-cn-shanghai-inner.ivolces.com",
		},
		{
			name: "",
			fields: fields{
				TlsServiceEndpoint: "https://tls-cn-shanghai-inner.ivolces.com",
			},
			want: "https://tls-cn-shanghai-inner.ivolces.com",
		},
		{
			name: "",
			fields: fields{
				TlsServiceEndpoint: "http://tls-cn-shanghai-inner.ivolces.com",
			},
			want: "http://tls-cn-shanghai-inner.ivolces.com",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Config{
				TlsServiceEndpoint: tt.fields.TlsServiceEndpoint,
			}
			os.Setenv("BDC_REGION_ID", "cn-shanghai")
			got := c.GetTlsRegionEndpoint("VolcanoEngine")
			if got != tt.want {
				t.Errorf("GetTlsRegionEndpoint() = %v, want %v", got, tt.want)
			}
			got1, err := c.GetTLSRegion("VolcanoEngine")
			if err != nil || got1 != "cn-shanghai" {
				t.Errorf("GetTlsRegionEndpoint() = %v, want %v", got, "cn-shanghai")
			}
		})
	}
}

func TestTCCConfig(t *testing.T) {
	handler := func(field reflect.StructField, v reflect.Value) (stepIn bool) {
		var tccValExist bool
		//var tccValue string
		if k := field.Tag.Get("tcc"); k != "" {
			fmt.Println("failed is no tcc tag", field)
		//} else if tccValue, tccValExist = getTccValue(k); !tccValExist {
		//} else if util.UpdateStructField(v, tccValue) {
		//	fmt.Println("tcc config change: ", k, v.Interface(), tccValue)

		}
		return v.IsValid() && v.Type().Kind() == reflect.Struct && !tccValExist}

	data := &Config{}
	v := reflect.ValueOf(data)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() != reflect.Struct {
		panic("input not a struct")
	}
	l := list.New()
	l.PushBack(v)
	for l.Len() > 0 {
		e := l.Remove(l.Back()).(reflect.Value)
		t := e.Type()
		for i := 0; i < e.NumField(); i++ {
			f := t.Field(i)
			//fmt.Println("filed: ", f)
			fv := e.Field(i)
			if fv.Kind() == reflect.Ptr {
				fv = reflect.Indirect(fv)
			}
			if handler(f, fv) {
				l.PushBack(fv)
			}

		}
	}
}
