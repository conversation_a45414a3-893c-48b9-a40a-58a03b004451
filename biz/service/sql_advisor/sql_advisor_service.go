package sql_advisor

import (
	"bytes"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/service/sql_indexadvice/advice"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/dig"
	"net/http"
	"net/http/httputil"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	svc_parser "code.byted.org/infcs/dbw-mgr/biz/service/parser"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
)

const RDSMySQLDefaultDBName = "information_schema"
const SQLBrainKeyWord = "advised by sqlbrain"
const DBWKeyWord = "advised by dbw"
const SQLAdvisorTimeOut = 20

type sqlAdvisorService struct {
	source        datasource.DataSourceService
	idSvc         idgen.Service
	c3Cnf         c3.ConfigProvider
	conf          config.ConfigProvider
	sqlAdvisorDal dal.SQLAdvisorDAL
	commandParser svc_parser.CommandParser
	indexAdvice   advice.IndexAdvice
	ActorClient   cli.ActorClient
	ds            datasource.DataSourceService
}

type SqlAdvisorServiceIn struct {
	dig.In
	Source        datasource.DataSourceService
	C3Cnf         c3.ConfigProvider
	Conf          config.ConfigProvider
	ActorClient   cli.ActorClient
	IdSvc         idgen.Service
	SqlAdvisorDal dal.SQLAdvisorDAL
	CommandParser svc_parser.CommandParser
	DataSource    datasource.DataSourceService
}

type SqlAdvisorService interface {
	DescribeSQLAdvisorTableMeta(ctx context.Context, req *model.DescribeSQLAdvisorTableMetaReq) (*model.DescribeSQLAdvisorTableMetaResp, error)
	DescribeInstanceVariables(ctx context.Context, req *model.DescribeInstanceVariablesReq) (*model.DescribeInstanceVariablesResp, error)
	DescribePrimaryKeyRange(ctx context.Context, req *model.DescribePrimaryKeyRangeReq) (*model.DescribePrimaryKeyRangeResp, error)
	DescribeSampleData(ctx context.Context, req *model.DescribeSampleDataReq) (*model.DescribeSampleDataResp, error)

	CreateSQLAdvisorTask(ctx context.Context, req *model.CreateSQLAdvisorTaskReq) (*model.CreateSQLAdvisorTaskResp, error)
	DescribeSQLAdvisorTask(ctx context.Context, req *model.DescribeSQLAdvisorTaskReq) (*model.DescribeSQLAdvisorTaskResp, error)
	// GetSQLAdvisorResult 和SQLAdvisor交互
	GetSQLAdvisorResult(ctx context.Context, req *model.DescribeSQLAdvisorTaskReq) (*model.DescribeSQLAdvisorTaskResp, error)
}

func NewSqlAdvisorService(d SqlAdvisorServiceIn) SqlAdvisorService {
	return &sqlAdvisorService{
		source:        d.Source,
		c3Cnf:         d.C3Cnf,
		idSvc:         d.IdSvc,
		ActorClient:   d.ActorClient,
		conf:          d.Conf,
		sqlAdvisorDal: d.SqlAdvisorDal,
		commandParser: d.CommandParser,
		ds:            d.DataSource,
	}
}

func getDefaultDbName(typ shared.DataSourceType) string {
	switch typ {
	case shared.MySQL:
		fallthrough
	case shared.VeDBMySQL:
		return RDSMySQLDefaultDBName
	}
	return "Unknown"
}

func (s *sqlAdvisorService) DescribeInstanceVariables(ctx context.Context, req *model.DescribeInstanceVariablesReq) (
	*model.DescribeInstanceVariablesResp, error) {
	// 1、生成一个ds
	ds := s.generateDatasource(ctx, shared.DataSourceType(req.InstanceType), req.InstanceID, getDefaultDbName(shared.DataSourceType(req.InstanceType)))
	// 2、获取secondary节点
	fwctx.GetBizContext(ctx).TenantID = req.TenantID
	nodeID, err := s.getSecondaryNodeID(ctx, req.InstanceID, shared.DataSourceType(req.InstanceType))
	if err != nil || nodeID == "" {
		log.Warn(ctx, "get slave nodeID error")
		return nil, consts.ErrorOf(model.ErrorCode_DescribeSQLAdvisorTaskError)
	}
	ds.NodeId = nodeID
	// 3、利用提供的账号密码创建一个连接
	if err = s.source.EnsureAccount(ctx, &datasource.EnsureAccountReq{
		Source: ds,
	}); err != nil {
		log.Warn(ctx, "EnsureAccount error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_CheckConnectionFailed)
	}
	rreq := &datasource.DescribeInstanceVariablesReq{
		Source:     ds,
		InstanceID: req.InstanceID,
		TenantID:   req.TenantID,
		Region:     req.Region,
		Variables:  req.Variables,
	}
	variables, err := s.source.DescribeInstanceVariables(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "DescribeInstanceVariables error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}

	return &model.DescribeInstanceVariablesResp{
		Variables: variables.Variables,
	}, nil
}

func (s *sqlAdvisorService) DescribePrimaryKeyRange(ctx context.Context, req *model.DescribePrimaryKeyRangeReq) (
	*model.DescribePrimaryKeyRangeResp, error) {
	// 1、生成一个ds
	ds := s.generateDatasource(ctx, shared.DataSourceType(req.InstanceType), req.InstanceID, req.DbName)
	// 2、获取secondary节点
	fwctx.GetBizContext(ctx).TenantID = req.TenantID
	nodeID, err := s.getSecondaryNodeID(ctx, req.InstanceID, shared.DataSourceType(req.InstanceType))
	if err != nil || nodeID == "" {
		log.Warn(ctx, "get slave nodeID error")
		return nil, consts.ErrorOf(model.ErrorCode_DescribeSQLAdvisorTaskError)
	}
	ds.NodeId = nodeID
	// 3、利用提供的账号密码创建一个连接
	if err = s.source.EnsureAccount(ctx, &datasource.EnsureAccountReq{
		Source: ds,
	}); err != nil {
		log.Warn(ctx, "EnsureAccount error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_CheckConnectionFailed)
	}
	rreq := &datasource.DescribePrimaryKeyRangeReq{
		Source:     ds,
		InstanceID: req.InstanceID,
		TenantID:   req.TenantID,
		Region:     req.Region,
		DBName:     req.DbName,
		TableName:  req.TableName,
		Columns:    req.Columns,
	}
	primaryKeyInfo, err := s.source.DescribePrimaryKeyRange(ctx, rreq)
	if err != nil {
		return nil, err
	}

	return &model.DescribePrimaryKeyRangeResp{
		Success:        true,
		PrimaryKeyInfo: primaryKeyInfoDataSourceTOModel(primaryKeyInfo.PrimaryKeyInfo),
	}, nil
}

func (s *sqlAdvisorService) DescribeSQLAdvisorTableMeta(ctx context.Context, req *model.DescribeSQLAdvisorTableMetaReq) (*model.DescribeSQLAdvisorTableMetaResp, error) {
	// 1、生成一个ds
	ds := s.generateDatasource(ctx, shared.DataSourceType(req.InstanceType), req.InstanceID, req.DbName)
	// 2、获取secondary节点
	fwctx.GetBizContext(ctx).TenantID = req.TenantID
	nodeID, err := s.getSecondaryNodeID(ctx, req.InstanceID, shared.DataSourceType(req.InstanceType))
	if err != nil || nodeID == "" {
		log.Warn(ctx, "get slave nodeID error")
		return nil, consts.ErrorOf(model.ErrorCode_DescribeSQLAdvisorTaskError)
	}
	ds.NodeId = nodeID
	log.Info(ctx, "ds info is [%v],[%v],[%v],[%v]", ds.NodeId, ds.InstanceId, ds.Db, ds.User)
	// 3、利用提供的账号密码创建一个连接
	if err = s.source.EnsureAccount(ctx, &datasource.EnsureAccountReq{
		Source: ds,
	}); err != nil {
		log.Warn(ctx, "EnsureAccount error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_CheckConnectionFailed)
	}

	rreq := &datasource.DescribeSQLAdvisorTableMetaReq{
		Source:     ds,
		InstanceID: req.InstanceID,
		TenantID:   req.TenantID,
		Region:     req.Region,
		DBName:     req.DbName,
		TableList:  req.TableList,
	}
	tableMetaInfo, err := s.source.DescribeSQLAdvisorTableMeta(ctx, rreq)
	if err != nil {
		return nil, err
	}

	return &model.DescribeSQLAdvisorTableMetaResp{
		Success: true,
		Data:    tableMetaInfoDatasourceTOModel(tableMetaInfo.Data),
	}, nil
}

func (s *sqlAdvisorService) DescribeSampleData(ctx context.Context, req *model.DescribeSampleDataReq) (
	*model.DescribeSampleDataResp, error) {
	// 1、生成一个ds
	ds := s.generateDatasource(ctx, shared.DataSourceType(req.InstanceType), req.InstanceID, req.DbName)
	// 2、获取secondary节点
	fwctx.GetBizContext(ctx).TenantID = req.TenantID
	nodeID, err := s.getSecondaryNodeID(ctx, req.InstanceID, shared.DataSourceType(req.InstanceType))
	if err != nil || nodeID == "" {
		log.Warn(ctx, "get slave nodeID error")
		return nil, consts.ErrorOf(model.ErrorCode_DescribeSQLAdvisorTaskError)
	}
	ds.NodeId = nodeID

	// 3、利用提供的账号密码创建一个连接
	if err = s.source.EnsureAccount(ctx, &datasource.EnsureAccountReq{
		Source: ds,
	}); err != nil {
		log.Warn(ctx, "EnsureAccount error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_CheckConnectionFailed)
	}

	rreq := &datasource.DescribeSampleDataReq{
		Source:     ds,
		InstanceID: req.InstanceID,
		Region:     req.Region,
		DbName:     req.DbName,
		TableName:  req.TableName,
		PrimaryKey: req.PrimaryKey,
		Columns:    req.Columns,
		MinNum:     primaryKeyValueModelToDataSource(req.MinNum),
		MaxNum:     primaryKeyValueModelToDataSource(req.MaxNum),
		OrderBy:    req.OrderBy,
		Limit:      req.Limit,
	}
	sampleDataInfo, err := s.source.DescribeSampleData(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "DescribeSampleData error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}
	log.Info(ctx, "result is %v", sampleDataInfo.Records)
	return &model.DescribeSampleDataResp{
		Success: true,
		Total:   int32(len(sampleDataInfo.Records)),
		Records: sampleDataInfo.Records,
	}, nil
}

func (s *sqlAdvisorService) CreateSQLAdvisorTask(ctx context.Context, req *model.CreateSQLAdvisorTaskReq) (
	*model.CreateSQLAdvisorTaskResp, error) {
	instanceFlag := s.ds.IsMyOwnInstance(ctx, req.InstanceID, shared.DataSourceType(req.InstanceType))
	log.Info(ctx, "instance flag is %v", instanceFlag)
	if !instanceFlag {
		return &model.CreateSQLAdvisorTaskResp{
			Success: false,
			Message: "This SQL does not contain table info or SQL parse failed",
			TaskID:  "",
		}, nil
	}
	// 自己生成一个taskId
	taskId, _ := s.idSvc.NextID(ctx)
	if !s.IsSQLContainTables(ctx, req.SQLList) {
		// 这里不向上报错,转为打印一个日志,然后报执行成功,然后返回一个自己生成的SqlTaskID
		log.Error(ctx, "This SQL does not contain table info or SQL parse error, please check")
		s.sqlAdvisorDal.Create(ctx, &dao.SqlAdvisor{
			TaskID:       utils.Int64ToStr(taskId),
			InstanceID:   req.InstanceID,
			InstanceType: req.InstanceType.String(),
			TenantID:     fwctx.GetTenantID(ctx),
			DbName:       req.DbName,
			TaskStatus:   model.SQLAdvisorTaskStatus_SUBMITTED.String(),
			SqlText:      "{}",
			Memo:         "",
			Deleted:      0,
			Result:       "{}",
			CreateTime:   time.Now().Unix(),
			ModifyTime:   time.Now().Unix(),
		})
		return &model.CreateSQLAdvisorTaskResp{
			Success: true,
			Message: "This SQL does not contain table info or SQL parse failed",
			TaskID:  utils.Int64ToStr(taskId),
		}, nil
	}
	resp, err := s.doCreateRequest(ctx, req)
	if err != nil {
		log.Error(ctx, "request sql advisor api error %s", err.Error())
		return &model.CreateSQLAdvisorTaskResp{
			Success: true,
			Message: "request sql advisor api failed",
			TaskID:  utils.Int64ToStr(taskId),
		}, nil
	}
	//resp, err := s.doCreateRequest(ctx, req)
	//resp := &model.CreateSQLAdvisorTaskResp{Success: true, Message: "describe sql advisor success", TaskID: uuid.New().String()}
	log.Info(ctx, "create sql advisor task resp is %v", resp)
	if !resp.Success || resp.TaskID == "" {
		return &model.CreateSQLAdvisorTaskResp{
			Success: true,
			Message: "create sql advisor task failed",
			TaskID:  utils.Int64ToStr(taskId),
		}, nil
	}
	// 这里需要落库一下
	jsonData, err := json.Marshal(req.SQLList)
	if err != nil {
		log.Warn(ctx, "sql list marshal error:%s", err.Error())
		return &model.CreateSQLAdvisorTaskResp{
			Success: true,
			Message: "sql list marshal error",
			TaskID:  utils.Int64ToStr(taskId),
		}, nil
	}
	if err = s.sqlAdvisorDal.Create(ctx, &dao.SqlAdvisor{
		TaskID:       resp.TaskID,
		InstanceID:   req.InstanceID,
		InstanceType: req.InstanceType.String(),
		TenantID:     fwctx.GetTenantID(ctx),
		DbName:       req.DbName,
		TaskStatus:   model.SQLAdvisorTaskStatus_SUBMITTED.String(),
		SqlText:      string(jsonData),
		Memo:         resp.Message,
		Deleted:      0,
		Result:       "{}",
		CreateTime:   time.Now().Unix(),
		ModifyTime:   time.Now().Unix(),
	}); err != nil {
		log.Warn(ctx, "create sql advisor task error:%s", err.Error())
		// 这块是由于bytebrain对一天内重复的sql做了优化,返回相同的TaskID,所以这里如果发现了相同的TaskID,就更新一下对应的记录
		if strings.Contains(err.Error(), "Duplicate entry") {
			if err = s.sqlAdvisorDal.UpdateTaskByID(ctx, resp.TaskID, model.SQLAdvisorTaskStatus_SUBMITTED.String(), resp.Message, "{}"); err != nil {
				log.Warn(ctx, "update task status error:%v", err)
				return &model.CreateSQLAdvisorTaskResp{
					Success: true,
					Message: "update task status failed",
					TaskID:  utils.Int64ToStr(taskId),
				}, nil
			}
			log.Info(ctx, "update task status success,resp is %v", resp)
			return resp, nil
		}
		return &model.CreateSQLAdvisorTaskResp{
			Success: true,
			Message: "create sql advisor task failed",
			TaskID:  utils.Int64ToStr(taskId),
		}, nil
	}
	return resp, nil
}

func (s *sqlAdvisorService) DescribeSQLAdvisorTask(ctx context.Context, req *model.DescribeSQLAdvisorTaskReq) (
	*model.DescribeSQLAdvisorTaskResp, error) {
	instanceFlag := s.ds.IsMyOwnInstance(ctx, req.InstanceID, shared.DataSourceType(GetDBType(req.InstanceID)))
	log.Info(ctx, "instance flag is %v", instanceFlag)
	if !instanceFlag {
		return nil, consts.ErrorOf(model.ErrorCode_DescribeSQLAdvisorTaskError)
	}
	result, err := s.GetSQLAdvisorResult(ctx, req)
	if err != nil {
		log.Warn(ctx, "get sql advisor task  %s error", req.TaskID)
		return nil, err
	}
	log.Info(ctx, "result is %v,result state is %v,err is %v", result, result.State, err)
	if result.State == model.SQLAdvisorTaskStatus_EXCEPTION {
		log.Warn(ctx, "sql advisor task  %s exception", req.TaskID)
		return result, consts.ErrorWithParam(model.ErrorCode_SystemError, result.Message)
	}
	if result.State == model.SQLAdvisorTaskStatus_SUCCESS {
		log.Info(ctx, "sql advisor task  %s finished", req.TaskID)
	}
	return result, nil
}

func (s *sqlAdvisorService) doCreateRequest(ctx context.Context, req *model.CreateSQLAdvisorTaskReq) (*model.CreateSQLAdvisorTaskResp, error) {
	// 1、构建请求
	var baseURL string
	if req.GetRegionId() == "boe" || req.GetRegionId() == "boe2" {
		baseURL = "https://api-bytebrain-boe.byted.org"
	} else if req.GetRegionId() == "cn" {
		baseURL = "https://api-bytebrain.byted.org"
	} else {
		baseURL = s.conf.Get(ctx).SQLAdvisorPlbEndPoint
	}
	body, err := generateBody(req, fwctx.GetTenantID(ctx))
	if err != nil {
		return nil, err
	}
	log.Info(ctx, "body is %s", body)
	uri := fmt.Sprintf("%s/openapi/sqlbrain_mysql/task_submit", baseURL)
	log.Info(ctx, "describe sql advisor task uri is %v", uri)
	request, err := http.NewRequest(http.MethodPost, uri, bytes.NewBuffer(body))
	if err != nil {
		log.Warn(ctx, "sqlAdvisor: build request error:%s", err.Error())
		return nil, err
	}

	// 2、包装请求
	requestRaw, err := httputil.DumpRequest(request, true)
	if err != nil {
		return nil, fmt.Errorf("dump request err: %w", err)
	}
	log.Info(ctx, "sqlAdvisor: request:\n%s\n", string(requestRaw))

	// 3、执行请求
	request.Header.Set("Content-Type", "application/json; charset=utf-8")
	response, err := http.DefaultClient.Do(request)
	if err != nil {
		log.Warn(ctx, "sqlAdvisor: do request error:%s", err.Error())
		return nil, fmt.Errorf("do request err: %w", err)
	}

	// 4、 打印响应
	responseRaw, err := httputil.DumpResponse(response, true)
	if err != nil {
		log.Warn(ctx, "sqlAdvisor: DumpResponse error:%s", err.Error())
		return nil, fmt.Errorf("dump response err: %w", err)
	}
	log.Info(ctx, "sqlAdvisor: response:\n%s\n", string(responseRaw))

	// 5、解析返回结构体
	respBody, err := GetBody(response)
	if err != nil {
		return nil, err
	}
	var rsp = &createSQLAdvisorTaskResp{}
	if err = json.Unmarshal(respBody, rsp); err != nil {
		log.Warn(ctx, "sqlAdvisor: uri: %s unmarshal  result fail %v, result: %s", uri, err, string(respBody))
		return nil, err
	}
	// 6、判断请求状态
	if response.StatusCode == 200 {
		log.Info(ctx, "sqlAdvisor: 请求成功")
	} else {
		log.Info(ctx, "sqlAdvisor: 请求失败")
		return nil, fmt.Errorf("call %s err", uri)
	}
	return &model.CreateSQLAdvisorTaskResp{
		Success: rsp.Success,
		Message: rsp.Message,
		TaskID:  rsp.TaskID,
	}, nil
}

func (s *sqlAdvisorService) GetSQLAdvisorResult(ctx context.Context, req *model.DescribeSQLAdvisorTaskReq) (
	*model.DescribeSQLAdvisorTaskResp, error) {
	// 构建一个标准的输出,当没有获取到索引优化建议的时候,直接返回这个标准的输出,不给用户报错
	var result = &model.DescribeSQLAdvisorTaskResp{
		Success: true,
		Message: "describe sql advisor success",
		State:   model.SQLAdvisorTaskStatus_SUCCESS,
		Data: &model.SQLAdvisorTaskInfo{
			TaskId: req.TaskID,
			IndexAdvices: []*model.IndexAdvice{
				{
					DDL: "This SQL has no index advice",
				},
			},
			OptimizeAdvice: []*model.OptimizeAdvice{
				{
					AdviceContent: "This SQL has no optimize advice",
				},
			},
			OptimizeStatus:  "",
			AdviceTimestamp: "",
		},
	}

	task, err := s.sqlAdvisorDal.GetTaskByID(ctx, req.TaskID)
	if err != nil {
		log.Warn(ctx, "get sql advisor task %v error %v ", req.TaskID, err)
		return result, nil
	}

	//FixMe 这里暂定20s之后,直接返回无建议,否则用户会有疑问,优化时间太长
	if time.Now().Unix()-task.CreateTime > SQLAdvisorTimeOut {
		log.Info(ctx, "get sql advisor task %v result timeout，over %v seconds", req.TaskID, SQLAdvisorTimeOut)
		return result, nil
	}

	resp, err := s.doDescribeRequest(ctx, req)
	if err != nil {
		log.Warn(ctx, "request sql advisor api error %s", err.Error())
		return result, nil
	}
	log.Info(ctx, "describe sql advisor task resp is %v", resp)
	// 如果调用SQLBrain失败了
	if resp.State == model.SQLAdvisorTaskStatus_EXCEPTION {
		log.Error(ctx, "get sql brain task %v result exception", req.TaskID)
		return result, nil
	}
	if resp.State == model.SQLAdvisorTaskStatus_SUBMITTED ||
		resp.State == model.SQLAdvisorTaskStatus_FETCHED ||
		resp.Data == nil {
		if err = s.sqlAdvisorDal.UpdateTaskByID(ctx, req.TaskID, resp.State.String(), resp.Message, "{}"); err != nil {
			log.Warn(ctx, "update sqlAdvisor task error:%s", err.Error())
			return result, nil
		}
		return &model.DescribeSQLAdvisorTaskResp{
			Success: resp.Success,
			Message: resp.Message,
			State:   resp.State,
			Data:    nil,
		}, nil
	}
	if resp.State == model.SQLAdvisorTaskStatus_SUCCESS {
		// 替换返回结果中的advise字样
		if resp.Data != nil && resp.Data.IndexAdvices != nil && len(resp.Data.IndexAdvices) > 0 {
			for idx, val := range resp.Data.IndexAdvices {
				if val != nil && strings.Contains(val.DDL, SQLBrainKeyWord) {
					resp.Data.IndexAdvices[idx].DDL = strings.ReplaceAll(val.DDL, SQLBrainKeyWord, DBWKeyWord)
				}
			}
		}
		if resp.Data != nil && resp.Data.IndexAdvices == nil && resp.Data.OptimizeAdvice == nil {
			log.Warn(ctx, "get resp error,resp is %v", utils.Show(resp))
			return result, nil
		}
	}
	// 这里需要落库一下
	jsonData, err := json.Marshal(resp.Data)
	if err != nil {
		log.Warn(ctx, "sql list marshal error:%s", err.Error())
		return result, nil
	}
	if err = s.sqlAdvisorDal.UpdateTaskByID(ctx, resp.Data.TaskId, resp.State.String(), resp.Message, string(jsonData)); err != nil {
		log.Warn(ctx, "describe sqlAdvisor task error:%s", err.Error())
		return result, nil
	}
	return resp, nil
}

func (s *sqlAdvisorService) handleSQLAdvice(ctx context.Context, req *model.DescribeSQLAdvisorTaskReq, task *dao.SqlAdvisor) (*model.DescribeSQLAdvisorTaskResp, error) {
	// 构建一个标准的输出
	result := &model.DescribeSQLAdvisorTaskResp{
		Success: true,
		Message: "describe sql advisor success",
		State:   model.SQLAdvisorTaskStatus_SUCCESS,
		Data: &model.SQLAdvisorTaskInfo{
			TaskId: req.TaskID,
			IndexAdvices: []*model.IndexAdvice{
				{
					DDL: "This SQL has no index advice",
				},
			},
			OptimizeAdvice: []*model.OptimizeAdvice{
				{
					AdviceContent: "This SQL has no optimize advice",
				},
			},
			OptimizeStatus:  "",
			AdviceTimestamp: "",
		},
	}

	// 调用 s.DescribeIndexAdvice 获取 SQL 建议
	adviceResp, err := s.DescribeIndexAdvice(ctx, req, task)
	if err != nil {
		log.Warn(ctx, "DescribeIndexAdvice error:%s", err.Error())
		return result, nil
	}

	if len(adviceResp) > 0 {
		result.Data.IndexAdvices = adviceResp
	}

	jsonData, err := json.Marshal(result.Data)
	if err != nil {
		log.Warn(ctx, "sql list marshal error:%s", err.Error())
		return result, nil
	}

	resultState := "DBW_SUCCESS"
	if err = s.sqlAdvisorDal.UpdateTaskByID(ctx, result.Data.TaskId, resultState, result.Message, string(jsonData)); err != nil {
		log.Warn(ctx, "describe sqlAdvisor task error:%s", err.Error())
	}

	return result, nil
}

func (s *sqlAdvisorService) doDescribeRequest(ctx context.Context, req *model.DescribeSQLAdvisorTaskReq) (*model.DescribeSQLAdvisorTaskResp, error) {
	// 1、构建请求
	var baseURL string
	// 内场的是boe、boe2或者cn,其他的都是火山region
	if req.GetRegionId() == "boe" || req.GetRegionId() == "boe2" {
		baseURL = "https://api-bytebrain-boe.byted.org"
	} else if req.GetRegionId() == "cn" {
		baseURL = "https://api-bytebrain.byted.org"
	} else {
		baseURL = s.conf.Get(ctx).SQLAdvisorPlbEndPoint
	}

	var body []byte
	uri := fmt.Sprintf("%s/openapi/sqlbrain_mysql/task_result?taskId=%s&tenantId=%s",
		baseURL, req.TaskID, fwctx.GetTenantID(ctx))
	log.Info(ctx, "describe sql advisor task uri is %v", uri)
	request, err := http.NewRequest(http.MethodGet, uri, bytes.NewBuffer(body))
	if err != nil {
		log.Warn(ctx, "sqlAdvisor: build request error:%s", err.Error())
		return nil, err
	}

	// 2、包装请求
	requestRaw, err := httputil.DumpRequest(request, true)
	if err != nil {
		return nil, fmt.Errorf("dump request err: %w", err)
	}
	log.Info(ctx, "sqlAdvisor: request:\n%s\n", string(requestRaw))

	// 3、执行请求
	request.Header.Set("Content-Type", "application/json; charset=utf-8")
	response, err := http.DefaultClient.Do(request)
	if err != nil {
		log.Warn(ctx, "sqlAdvisor: do request error:%s", err.Error())
		return nil, fmt.Errorf("do request err: %w", err)
	}

	// 4、 打印响应
	responseRaw, err := httputil.DumpResponse(response, true)
	if err != nil {
		log.Warn(ctx, "sqlAdvisor: DumpResponse error:%s", err.Error())
		return nil, fmt.Errorf("dump response err: %w", err)
	}
	log.Info(ctx, "sqlAdvisor: response:\n%s\n", string(responseRaw))

	// 5、解析返回结构体
	respBody, err := GetBody(response)
	if err != nil {
		return nil, err
	}
	var resp = &describeSQLAdvisorTaskResp{}
	if err = json.Unmarshal(respBody, resp); err != nil {
		log.Warn(ctx, "sqlAdvisor: uri: %s unmarshal  result fail %v, result: %s", uri, err, string(respBody))
		return nil, err
	}
	// 6、判断请求状态
	if response.StatusCode == 200 {
		log.Info(ctx, "sqlAdvisor: 请求成功")
	} else {
		log.Info(ctx, "sqlAdvisor: 请求失败")
		return nil, fmt.Errorf("call %s err", uri)
	}
	return DescribeSQLAdvisorTaskRespToModel(resp), nil
}

func (s *sqlAdvisorService) generateDatasource(ctx context.Context, InstanceType shared.DataSourceType, InstanceID string, DBName string) *shared.DataSource {
	c3Cfg := s.c3Cnf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	cnf := s.conf.Get(ctx)

	ds := &shared.DataSource{
		Type:             InstanceType,
		LinkType:         shared.Volc,
		User:             c3Cfg.DBWAccountName,
		Password:         getAccountPassword(c3Cfg.DbwAccountPasswordGenKey, InstanceID),
		ConnectTimeoutMs: cnf.ConnectionConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.ConnectionReadTimeout * 1000,
		WriteTimeoutMs:   cnf.ConnectionWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.ConnectionIdleTimeout * 1000,
		InstanceId:       InstanceID,
		Db:               DBName,
	}
	return ds
}

func (s *sqlAdvisorService) generateDatasourceForDB(ctx context.Context,
	InstaceID string, DBName string, InstanceType string) *shared.DataSource {
	c3Cfg := s.c3Cnf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	cnf := s.conf.Get(ctx)
	dsType, _ := toDataSourceType(InstanceType)
	ds := &shared.DataSource{
		Type:             dsType,
		LinkType:         shared.Volc,
		User:             c3Cfg.DBWAccountName,
		Password:         getAccountPassword(c3Cfg.DbwAccountPasswordGenKey, InstaceID),
		ConnectTimeoutMs: cnf.ConnectionConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.ConnectionReadTimeout * 1000,
		WriteTimeoutMs:   cnf.ConnectionWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.ConnectionIdleTimeout * 1000,
		InstanceId:       InstaceID,
		Db:               DBName,
	}
	return ds
}

func (s *sqlAdvisorService) getSecondaryNodeID(ctx context.Context, instanceID string, dsType shared.DataSourceType) (string, error) {
	nodes, err := s.source.ListInstanceNodes(ctx, &datasource.ListInstanceNodesReq{
		DSType:     dsType,
		InstanceId: instanceID,
	})
	if err != nil {
		log.Warn(ctx, "get instance node error %s", err.Error())
		return "", err
	}
	for _, val := range nodes.Nodes {
		if val.NodeType == model.NodeType_Secondary || val.NodeType == model.NodeType_ReadOnly {
			return val.NodeId, nil
		}
	}
	return "", fmt.Errorf("no secondary find")
}

func (s *sqlAdvisorService) IsSQLContainTables(ctx context.Context, sqllist []string) bool {
	if len(sqllist) == 0 {
		log.Warn(ctx, "empty sql list")
		return false
	}
	p := parser.New()
	stmts, _, err := p.Parse(sqllist[0], "utf8", "")
	if err != nil {
		log.Warn(ctx, "sql parse error %s", err.Error())
		return false
	}
	if len(stmts) == 0 {
		log.Warn(ctx, "sql parse success,empty stmts")
		return false
	}
	//res := s.getTables(stmts[0])
	//if len(res) == 0 {
	//	log.Warn(ctx, "sql parse success, sql not contain table")
	//	return false
	//}
	return s.getTableUsingParserInterface(ctx, &shared.DataSource{Type: shared.MySQL}, sqllist[0])
}

func (s *sqlAdvisorService) getTableUsingParserInterface(ctx context.Context, ds *shared.DataSource, sql string) bool {
	columns, err := s.commandParser.GetAllColumns(ctx, ds, sql)
	if err != nil {
		log.Warn(ctx, "get all columns error %s", err.Error())
		return false
	}
	if len(columns) > 0 {
		return true
	}
	return false
}

func (s *sqlAdvisorService) getTables(node ast.StmtNode) []*ast.TableName {
	var lefSetNode ast.ResultSetNode
	var tables []*ast.TableName
	// 先把右侧取到，因为sql解析的leftNode是靠右侧的点，之后我们一直往左侧走即可
	switch stmt := node.(type) {
	case *ast.SelectStmt:
		if stmt.From == nil {
			return tables
		}
		lefSetNode = stmt.From.TableRefs.Left
		if stmt.From.TableRefs.Right != nil {
			tables = append(tables, stmt.From.TableRefs.Right.(*ast.TableSource).Source.(*ast.TableName))
		}
	case *ast.UpdateStmt:
		lefSetNode = stmt.TableRefs.TableRefs.Left
		if stmt.TableRefs.TableRefs.Right != nil {
			tables = append(tables, stmt.TableRefs.TableRefs.Right.(*ast.TableSource).Source.(*ast.TableName))
		}
	case *ast.DeleteStmt:
		lefSetNode = stmt.TableRefs.TableRefs.Left
		if stmt.TableRefs.TableRefs.Right != nil {
			tables = append(tables, stmt.TableRefs.TableRefs.Right.(*ast.TableSource).Source.(*ast.TableName))
		}
	case *ast.InsertStmt:
		if stmt.Table.TableRefs.Right != nil {
			tables = append(tables, stmt.Table.TableRefs.Right.(*ast.TableSource).Source.(*ast.TableName))
		}
		lefSetNode = stmt.Table.TableRefs.Left
	case *ast.AlterTableStmt:
		if stmt.Table != nil {
			tables = append(tables, stmt.Table)
		}
	case *ast.CreateTableStmt:
		if stmt.Table != nil {
			tables = append(tables, stmt.Table)
		}
		if stmt.ReferTable != nil {
			tables = append(tables, stmt.ReferTable)
		}
	case *ast.CreateIndexStmt:
		if stmt.Table != nil {
			tables = append(tables, stmt.Table)
		}
	case *ast.DropTableStmt:
		for _, val := range stmt.Tables {
			if stmt.Tables != nil {
				tables = append(tables, val)
			}
		}
	case *ast.DropIndexStmt:
		if stmt.Table != nil {
			tables = append(tables, stmt.Table)
		}
	case *ast.TruncateTableStmt:
		if stmt.Table != nil {
			tables = append(tables, stmt.Table)
		}
	case *ast.ShowStmt:
		if stmt.Table != nil {
			tables = append(tables, stmt.Table)
		}
	}

	// 然后拿左侧的信息
	isUnFinished := true
	for isUnFinished {
		switch lefSetNode.(type) {
		case *ast.TableSource:
			tables = append(tables, lefSetNode.(*ast.TableSource).Source.(*ast.TableName))
			isUnFinished = false
		case *ast.Join:
			tables = append(tables, lefSetNode.(*ast.Join).Right.(*ast.TableSource).Source.(*ast.TableName))
			lefSetNode = lefSetNode.(*ast.Join).Left
		default:
			isUnFinished = false
		}
	}
	return tables
}

func (s *sqlAdvisorService) DescribeIndexAdvice(ctx context.Context, req *model.DescribeSQLAdvisorTaskReq, sa *dao.SqlAdvisor) ([]*model.IndexAdvice, error) {

	// 1、生成一个ds
	//ds := s.generateDatasource(ctx, req.InstanceID, sa.DbName)
	ds := s.generateDatasourceForDB(ctx, req.InstanceID, sa.DbName, sa.InstanceType)
	// 2、获取secondary节点l
	fwctx.GetBizContext(ctx).TenantID = sa.TenantID
	dsType, err := toDataSourceType(sa.InstanceType)
	if err != nil {
		log.Warn(ctx, "toDataSourceType error")
		return nil, err
	}
	nodeID, err := s.getSecondaryNodeID(ctx, req.InstanceID, dsType)
	if err != nil || nodeID == "" {
		log.Warn(ctx, "getSecondaryNodeID error")
		return nil, consts.ErrorOf(model.ErrorCode_DescribeSQLAdvisorTaskError)
	}
	ds.NodeId = nodeID

	var parsedSQLList []string
	err = json.Unmarshal([]byte(sa.SqlText), &parsedSQLList)
	if err != nil {
		log.Warn(ctx, "json.Unmarshal error:%s", err.Error())
		return nil, err
	}

	switch sa.InstanceType {
	case "MySQL", "VeDBMySQL":
		// 3、利用提供的账号密码创建一个连接
		if err = s.source.EnsureAccount(ctx, &datasource.EnsureAccountReq{
			Source: ds,
		}); err != nil {
			log.Warn(ctx, "EnsureAccount error:%s", err.Error())
			return nil, consts.ErrorOf(model.ErrorCode_CheckConnectionFailed)
		}
	case "Postgres":
		ds, err = s.GetDataSource(ctx, sa)
		if err != nil {
			log.Warn(ctx, "GetDataSource error:%s", err.Error())
			return nil, err
		}
		ds.Db = sa.DbName
		ds.NodeId = nodeID
	}

	var indexAdvices []*model.IndexAdvice
	for _, sql := range parsedSQLList {
		// 调用 DescribeIndexAdvice 获取索引建议
		adviceIndex := s.indexAdvice.DescribeIndexAdvice(ctx, sql, *ds)
		// 适配返回值，转换为 IndexAdvice 类型
		for _, advice := range adviceIndex {
			indexAdvices = append(indexAdvices, &model.IndexAdvice{
				DbName:    sa.DbName,
				TableName: advice.TableName,
				DDL:       strings.Join(advice.DDLs, "; "),
				Columns:   convertToSQLAdvisorColumns(advice.AdviceIndexColumns),
			})
		}
	}
	return indexAdvices, nil
}

// convertToSQLAdvisorColumns: helper 函数，将字符串数组转换为 SQLAdvisorColumns
func convertToSQLAdvisorColumns(columns []string) []*model.SQLAdvisorColumns {
	var advisorColumns []*model.SQLAdvisorColumns
	for _, col := range columns {
		advisorColumns = append(advisorColumns, &model.SQLAdvisorColumns{Name: col})
	}
	return advisorColumns
}

// Mapping function to convert string to DataSourceType
func toDataSourceType(instanceType string) (shared.DataSourceType, error) {
	switch instanceType {
	case "NoneType":
		return shared.NoneType, nil
	case "MySQL":
		return shared.MySQL, nil
	case "Postgres":
		return shared.Postgres, nil
	case "Mongo":
		return shared.Mongo, nil
	case "Redis":
		return shared.Redis, nil
	case "VeDBMySQL":
		return shared.VeDBMySQL, nil
	case "MetaRDS":
		return shared.MetaRDS, nil
	case "MSSQL":
		return shared.MSSQL, nil
	case "MySQLSharding":
		return shared.MySQLSharding, nil
	default:
		return shared.NoneType, fmt.Errorf("unknown instance type: %s", instanceType)
	}
}

func (self *sqlAdvisorService) GetDataSource(ctx context.Context, sa *dao.SqlAdvisor) (*shared.DataSource, error) {
	cnf := self.conf.Get(ctx)
	dsType, _ := toDataSourceType(sa.InstanceType)
	ds := &shared.DataSource{
		Type:             dsType,
		LinkType:         shared.Volc,
		ConnectTimeoutMs: cnf.TicketConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.TicketReadTimeout * 1000,
		WriteTimeoutMs:   cnf.TicketWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.TicketIdleTimeout * 1000,
		InstanceId:       sa.InstanceID,
		Db:               sa.DbName,
	}

	// 获取默认终端的连接地址
	_, err := self.getDBAddress(ctx, ds)
	if err != nil {
		log.Warn(ctx, "get db address error:%s", err.Error())
		return nil, err
	}

	// 从连接池获取链接
	sessionID, err := self.GetInstanceSession(ctx, ds)
	if err != nil {
		log.Warn(ctx, "sqlReviewService GetInstanceSession error:%s", err.Error())
		return nil, err
	}
	// 释放连接
	defer self.GiveBackInstanceSession(ctx, ds.InstanceId, sessionID)

	resp, err := self.ActorClient.KindOf(consts.SessionActorKind).Call(ctx, sessionID, &shared.CheckSession{})
	if err != nil {
		return nil, err
	}
	ck, ok := resp.(*shared.CheckSessionSuccess)
	if !ok {
		return nil, err
	}
	return ck.Source, nil
}

func (self *sqlAdvisorService) GetInstanceSession(ctx context.Context, ds *shared.DataSource) (string, error) {
	now := time.Now().Unix()
	name := ds.InstanceId + conv.Int64ToStr(now)
	resp, err := self.ActorClient.KindOf(consts.SessionMgrActorKind).Call(ctx, name, &shared.GetSession{
		InstanceId:      ds.InstanceId,
		DSType:          ds.Type,
		LinkType:        shared.Volc,
		UserAccountName: ds.User,
		UserPassword:    ds.Password,
	})
	if err != nil {
		log.Warn(ctx, "failed to get session, err=%v", err)
		return "", err
	}
	switch rsp := resp.(type) {
	case *shared.SessionInfo:
		log.Info(ctx, "succeed to get session, sessionId=%s", rsp.GetSessionId())
		return rsp.GetSessionId(), nil
	case *shared.GetSessionFailed:
		err = consts.ErrorOf(model.ErrorCode_InternalError)
		log.Warn(ctx, "failed to get session, resp=%#v", resp)
	default:
		err = consts.ErrorOf(model.ErrorCode_InternalError)
		log.Warn(ctx, "unknown response of GetSession, resp=%#v", resp)
	}
	return "", err
}

func (self *sqlAdvisorService) GiveBackInstanceSession(ctx context.Context, instanceId string, sessionId string) {
	err := self.ActorClient.KindOf(consts.SessionMgrActorKind).
		Send(ctx, instanceId, &shared.GiveBackSession{
			InstanceId: instanceId,
			SessionId:  sessionId,
		})
	if err != nil {
		log.Warn(ctx, "give back session error:%s", err.Error())
		return
	}
	log.Info(ctx, "give back session %s", sessionId)
}

func (self *sqlAdvisorService) getDBAddress(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error) {

	rreq := &datasource.GetDBInnerAddressReq{
		Source: ds,
	}
	rresp, err := self.ds.GetDBInnerAddress(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "sqlReview: get db address error:", err.Error())
		return nil, err
	}
	if rresp.Source.Address == "" {
		log.Warn(ctx, "sqlReview: get empty db address ")
		return nil, errors.New("get empty db address")
	}
	return ds, nil
}
