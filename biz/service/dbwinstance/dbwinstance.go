package dbwinstance

import (
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	mgrUtils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
	"gorm.io/gorm"
	"math"
	"strconv"
	"strings"
	"time"
)

const MaxVeDBPageSize = 500

type dbwInstanceService struct {
	dbwInstance     dal.DbwInstanceDAL
	dbwUser         dal.DbwUserDAL
	instanceHistory dal.InstanceHistoryDAL
	loc             location.Location
	dsSvc           datasource.DataSourceService
	idg             idgen.Service
}

type DbwInstanceInterface interface {
	SyncInstances(ctx context.Context, sourceType shared.DataSourceType) error
	GetManagedInstance(ctx context.Context, instanceId string, instanceType shared.DataSourceType) (*entity.ManagedInstance, error)
	DescribeInstances(ctx context.Context, req *model.DescribeInstancesReq) ([]*model.InstanceInfo, error)
	CreateInstanceHistory(ctx context.Context, req *model.AddRecentlyUsedDBInstanceReq) error
	ListInstanceHistory(ctx context.Context, req *model.DescribeRecentlyUsedDBInstancesReq) (*model.DescribeRecentlyUsedDBInstancesResp, error)
	DeleteInstanceHistory(ctx context.Context, req *model.DeleteRecentlyUsedDBInstancesReq) error
}

type NewDbwInstanceServiceIn struct {
	dig.In
	DbwInstance dal.DbwInstanceDAL
	DbwUser     dal.DbwUserDAL
	InstanceHis dal.InstanceHistoryDAL
	Loc         location.Location
	DsSvc       datasource.DataSourceService
	Idg         idgen.Service
}

func NewDbwInstanceService(d NewDbwInstanceServiceIn) DbwInstanceInterface {
	h := &dbwInstanceService{
		dbwInstance:     d.DbwInstance,
		dbwUser:         d.DbwUser,
		instanceHistory: d.InstanceHis,
		loc:             d.Loc,
		dsSvc:           d.DsSvc,
		idg:             d.Idg,
	}
	return h
}

// SyncInstances 查询所有的实例信息，然后对已经无效或者删除的实例，从dbw_instance表里进行剔除
func (selfSvc *dbwInstanceService) SyncInstances(ctx context.Context, sourceType shared.DataSourceType) error {
	return nil
}

func (selfSvc *dbwInstanceService) getAllInstances(ctx context.Context, sourceType shared.DataSourceType) ([]*model.InstanceInfo, error) {
	var instanceList []*model.InstanceInfo
	var pageNumber int32 = 1
	var pageSize = int32(math.MaxInt32)
	if sourceType == shared.VeDBMySQL || sourceType == shared.MSSQL {
		pageSize = MaxVeDBPageSize
	}
	query := &datasource.ListInstanceReq{
		Type:       sourceType,
		LinkType:   shared.Volc,
		RegionId:   selfSvc.loc.RegionID(),
		TenantId:   fwctx.GetTenantID(ctx),
		PageNumber: pageNumber,
		PageSize:   pageSize,
	}
	for {
		resp, err := selfSvc.dsSvc.ListInstance(ctx, query)
		if err != nil {
			log.Warn(ctx, "failed to list instance, err=%v", err)
			return nil, err
		}
		instanceList = append(instanceList, resp.InstanceList...)

		if int64(query.PageSize*query.PageNumber) >= resp.Total {
			break
		}
		query.PageNumber += 1
	}

	return instanceList, nil
}

// TODO add repository layer
func (selfSvc *dbwInstanceService) GetManagedInstance(ctx context.Context, instanceId string, instanceType shared.DataSourceType) (*entity.ManagedInstance, error) {
	instance, err := selfSvc.dbwInstance.Get(ctx, instanceId, instanceType.String(), shared.Volc.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		return nil, err
	}
	return entity.ManagedInstanceDaoToEntity(instance), nil
}

func (selfSvc *dbwInstanceService) DescribeInstances(ctx context.Context, req *model.DescribeInstancesReq) ([]*model.InstanceInfo, error) {
	var (
		instanceList []*model.InstanceInfo
		orderBy      string
		sortBy       string
		controlMode  int
	)
	controlMode = -1
	tenantId := fwctx.GetTenantID(ctx)
	if req.IsSetSearchControlMode() {
		controlMode = int(req.GetSearchControlMode()[0])
	}
	query := &datasource.ListInstanceReq{
		Type:            conv.ToSharedType(req.GetDSType()),
		RegionId:        selfSvc.loc.RegionID(),
		InstanceName:    req.GetInstanceName(),
		InstanceId:      req.GetInstanceId(),
		InstanceStatus:  req.GetInstanceStatus(),
		DbwStatus:       req.GetDbwStatus(),
		DBEngineVersion: req.GetDBEngineVersion(),
		ProjectName:     req.GetProjectName(),
		SecurityGroupId: req.GetSecurityRuleId(),
		Query:           req.GetQuery(),
		ControlMode:     controlMode,
	}
	if req.OrderBy != nil {
		orderBy = req.OrderBy.String()
	}
	if req.SortBy != nil {
		sortBy = req.SortBy.String()
	}
	dbwInstance, err := selfSvc.dbwInstance.ListAll(ctx, tenantId, query, orderBy, sortBy)
	if err != nil {
		log.Warn(ctx, "failed to list local instance, err=%v", err)
		return nil, err
	}
	instances := entity.ManagedInstancesDaoToEntity(dbwInstance.Items)
	for _, instance := range instances {
		linkType, _ := model.LinkTypeFromString(instance.Source)
		fromString, _ := model.SubInstanceTypeFromString(instance.SubInstanceType)
		instanceType, _ := model.InstanceTypeFromString(instance.InstanceType)
		noAuthMode, _ := model.NoAuthModeFromString(instance.NoAuthMode)
		instanceList = append(instanceList, &model.InstanceInfo{
			InstanceId:      utils.StringRef(instance.InstanceId),
			InstanceName:    utils.StringRef(instance.InstanceName),
			InstanceStatus:  instance.Status,
			InstanceSpec:    genInstanceSpec(instance),
			InstanceType:    instanceType,
			Zone:            instance.Zone,
			DBEngineVersion: instance.DBEngineVersion,
			ProjectName:     utils.StringRef(instance.ProjectName),
			SubInstanceType: &fromString,
			Tags:            instance.Tags,
			LinkType:        linkType,
			AccountId:       utils.StringRef(instance.TenantId),
			ControlMode:     model.ControlModePtr(model.ControlMode(instance.ControlMode)),
			NoAuthMode:      model.NoAuthModePtr(noAuthMode),
			SecurityRuleId:  utils.StringRef(strconv.FormatInt(instance.SecurityGroupId, 10)),
			CreateTime:      utils.StringRef(instance.InstanceCreateTime),
		})
	}
	return instanceList, nil
}

func genInstanceSpec(instance *entity.ManagedInstance) *model.InstanceSpec {
	return &model.InstanceSpec{
		CpuNum:      instance.CpuNum,
		MemInGiB:    instance.MemInGiB,
		NodeNumber:  instance.NodeNumber,
		ShardNumber: instance.ShardNumber,
	}
}

func (selfSvc *dbwInstanceService) CreateInstanceHistory(ctx context.Context, req *model.AddRecentlyUsedDBInstanceReq) error {
	if !mgrUtils.IsByteCloud() {
		return nil
	}
	var err error
	log.Info(ctx, "CreateInstanceHistory, req=%v", req)
	now := time.Now().Unix()
	history, err := selfSvc.instanceHistory.Get(ctx, &dao.HistoryQuery{
		InstanceId:   req.GetInstanceId(),
		InstanceType: req.GetInstanceType().String(),
		RegionId:     req.GetRegionId(),
	})
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			id, err := selfSvc.idg.NextIDStr(ctx)
			if err != nil {
				log.Warn(ctx, "dbwInstanceService failed to generate id, err=%v", err)
				return err
			}
			history = &dao.InstanceHistory{
				ID:           id,
				InstanceId:   req.GetInstanceId(),
				InstanceType: req.GetInstanceType().String(),
				InstanceName: req.GetInstanceName(),
				Psm:          strings.Join(req.GetPsmList(), ","),
				RegionId:     req.GetRegionId(),
				TenantId:     fwctx.GetTenantID(ctx),
				UserId:       fwctx.GetUserID(ctx),
				CreateTime:   now,
				UpdateTime:   now,
				LoginTime:    now,
			}
			err = selfSvc.instanceHistory.Create(ctx, history)
			if err != nil {
				log.Warn(ctx, "dbwInstanceService failed to create instance history, err=%v", err)
				return err
			}
			return nil
		}
		return err
	}
	history.UpdateTime = now
	history.LoginTime = now
	history.InstanceName = req.GetInstanceName()
	err = selfSvc.instanceHistory.Update(ctx, history)
	if err != nil {
		log.Warn(ctx, "dbwInstanceService failed to update instance history, err=%v", err)
		return err
	}
	return nil
}

func (selfSvc *dbwInstanceService) ListInstanceHistory(ctx context.Context, req *model.DescribeRecentlyUsedDBInstancesReq) (*model.DescribeRecentlyUsedDBInstancesResp, error) {
	var items []*model.UsedInstanceInfo
	limit := req.GetPageSize()
	offset := (req.GetPageNumber() - 1) * req.GetPageSize()
	query := &dao.HistoryQuery{
		InstanceType: req.GetInstanceType().String(),
		Keyword:      req.GetKeyword(),
		RegionId:     req.GetRegionId(),
		SortBy:       req.GetSortBy().String(),
	}
	historyList, err := selfSvc.instanceHistory.List(ctx, query, limit, offset)
	if err != nil {
		log.Warn(ctx, "dbwInstanceService failed to list instance history, err=%v", err)
		return nil, err
	}
	for _, history := range historyList.Items {
		instanceType, err := model.InstanceTypeFromString(history.InstanceType)
		if err != nil {
			log.Warn(ctx, "dbwInstanceService failed to parse instance type, err=%v", err)
		}
		items = append(items, &model.UsedInstanceInfo{
			InstanceId:   history.InstanceId,
			InstanceName: history.InstanceName,
			InstanceType: instanceType,
			PsmList:      strings.Split(history.Psm, ","),
			LastTime:     strconv.FormatInt(history.LoginTime, 10),
			UUID:         history.ID,
		})
	}
	return &model.DescribeRecentlyUsedDBInstancesResp{
		Instances: items,
		Total:     historyList.Total,
	}, nil
}

func (selfSvc *dbwInstanceService) DeleteInstanceHistory(ctx context.Context, req *model.DeleteRecentlyUsedDBInstancesReq) error {
	if req.GetIsAll() {
		err := selfSvc.instanceHistory.DeleteAll(ctx)
		if err != nil {
			log.Warn(ctx, "dbwInstanceService failed to delete all instance history, err=%v", err)
			return err
		}
	} else {
		err := selfSvc.instanceHistory.Delete(ctx, req.GetUUID())
		if err != nil {
			log.Warn(ctx, "dbwInstanceService failed to delete instance history, err=%v", err)
			return err
		}
	}
	return nil
}
