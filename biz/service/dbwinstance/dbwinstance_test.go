package dbwinstance

import (
	"code.byted.org/gopkg/gorm"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"errors"
	"os"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/dal"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
)

func getDescribeInstancesService() *dbwInstanceService {
	return &dbwInstanceService{
		dbwInstance:     &mocks.MockDbwInstanceDAL{},
		dbwUser:         &mocks.MockDbwUserDAL{},
		instanceHistory: &dal.MockInstanceHistoryDAL{},
		loc:             &mocks.MockLocation{},
		dsSvc:           &mocks.MockDataSourceService{},
		idg:             &mocks.MockService{},
	}
}

func Test_DescribeInstances(t *testing.T) {
	ctx := context.Background()
	req := &model.DescribeInstancesReq{}
	selfSvc := getDescribeInstancesService()
	dbwInst := &dao.DbwInstance{
		ID:                 1,
		InstanceId:         "instance-id-1",
		InstanceType:       model.InstanceType_MySQL.String(),
		InstanceName:       "instance-1",
		TenantId:           "tenant-1",
		Region:             "region-1",
		Source:             model.LinkType_Volc.String(),
		Status:             "running",
		Zone:               "zone-1",
		DBEngineVersion:    "5.7",
		SubInstanceType:    model.SubInstanceType_Unknown.String(),
		StorageType:        "ssd",
		InstanceCreateTime: "2023-01-01T00:00:00Z",
		CpuNum:             2,
		MemInGiB:           4.0,
		NodeNumber:         1,
		ShardNumber:        1,
		ProjectName:        "project-1",
		Tags:               "",
		ControlMode:        1,
		SecurityGroupId:    123,
		CreatedAt:          1672531200,
		UpdatedAt:          1672531200,
		DeletedAt:          0,
		Deleted:            0,
	}
	dbwInsts := &dao.DbwInstances{
		Total: 1,
		Items: []*dao.DbwInstance{dbwInst},
	}
	Mock((*mocks.MockLocation).RegionID).Return("region-1").Build()

	PatchConvey("Test ListAll failed", t, func() {
		Mock((*mocks.MockDbwInstanceDAL).ListAll).Return(nil, errors.New("list all failed")).Build()
		actual, err := selfSvc.DescribeInstances(ctx, req)
		So(actual, ShouldBeNil)
		So(err, ShouldNotBeNil)
	})
	PatchConvey("Test ListAll success", t, func() {
		Mock((*mocks.MockDbwInstanceDAL).ListAll).Return(dbwInsts, nil).Build()
		_, err := selfSvc.DescribeInstances(ctx, req)
		//expectedResp := []*model.InstanceInfo{
		//	{
		//		InstanceId:      utils.StringRef("instance-id-1"),
		//		InstanceName:    utils.StringRef("instance-1"),
		//		InstanceStatus:  "running",
		//		InstanceSpec:    &model.InstanceSpec{CpuNum: 2, MemInGiB: 4.0, NodeNumber: 1, ShardNumber: 1},
		//		InstanceType:    model.InstanceType_MySQL,
		//		Zone:            "zone-1",
		//		DBEngineVersion: "5.7",
		//		ProjectName:     utils.StringRef("project-1"),
		//		SubInstanceType: nil,
		//		Tags:            nil,
		//		LinkType:        model.LinkType_Volc,
		//		AccountId:       utils.StringRef("tenant-1"),
		//		ControlMode:     model.ControlModePtr(model.ControlMode(1)),
		//		SecurityRuleId:  utils.StringRef("123"),
		//		CreateTime:      utils.StringRef("2023-01-01T00:00:00Z"),
		//	},
		//}
		So(err, ShouldBeNil)
	})
	PatchConvey("Test ListAll DbwStatus_SyncError", t, func() {
		Mock((*mocks.MockDbwInstanceDAL).ListAll).Return(dbwInsts, nil).Build()
		statu1 := model.DbwStatus_SyncError
		req1 := &model.DescribeInstancesReq{DbwStatus: &statu1}
		_, err := selfSvc.DescribeInstances(ctx, req1)
		So(err, ShouldBeNil)
	})
	PatchConvey("Test ListAll DbwStatus_Normal", t, func() {
		Mock((*mocks.MockDbwInstanceDAL).ListAll).Return(dbwInsts, nil).Build()
		statu2 := model.DbwStatus_Normal
		req2 := &model.DescribeInstancesReq{DbwStatus: &statu2}
		_, err := selfSvc.DescribeInstances(ctx, req2)
		So(err, ShouldBeNil)
	})
}

func Test_CreateInstanceHistory(t *testing.T) {
	ctx := context.Background()
	req := &model.AddRecentlyUsedDBInstanceReq{
		InstanceId:   "instance-123",
		InstanceName: "instance-123",
		InstanceType: model.InstanceType_ByteRDS,
		LinkType:     model.LinkTypePtr(model.LinkType_Volc),
		RegionId:     utils.StringRef("region-1"),
		PsmList:      []string{"a.b.c"},
	}
	selfSvc := getDescribeInstancesService()
	PatchConvey("Test IsByteCloud returns false", t, func() {
		Mock(os.LookupEnv).Return("FALSE", true).Build()
		err := selfSvc.CreateInstanceHistory(ctx, req)
		So(err, ShouldBeNil)
	})

	PatchConvey("Test IsByteCloud returns true", t, func() {
		Mock(os.LookupEnv).Return("TRUE", true).Build()
		PatchConvey("Test GetInstanceHistory returns ErrRecordNotFound", func() {
			Mock(GetMethod(selfSvc.instanceHistory, "Get")).Return(nil, gorm.ErrRecordNotFound).Build()
			PatchConvey("Test NextIDStr fails", func() {
				Mock(GetMethod(selfSvc.idg, "NextIDStr")).Return("", errors.New("id generation failed")).Build()
				err := selfSvc.CreateInstanceHistory(ctx, req)
				So(err, ShouldNotBeNil)
			})
			PatchConvey("Test NextIDStr succeeds", func() {
				Mock(GetMethod(selfSvc.idg, "NextIDStr")).Return("history-123", nil).Build()
				PatchConvey("Test Create fails", func() {
					Mock(GetMethod(selfSvc.instanceHistory, "Create")).Return(errors.New("create failed")).Build()
					err := selfSvc.CreateInstanceHistory(ctx, req)
					So(err, ShouldNotBeNil)
				})
				PatchConvey("Test Create succeeds", func() {
					Mock(GetMethod(selfSvc.instanceHistory, "Create")).Return(nil).Build()
					err := selfSvc.CreateInstanceHistory(ctx, req)
					So(err, ShouldBeNil)
				})
			})
		})
		PatchConvey("Test GetInstanceHistory returns other error", func() {
			Mock(GetMethod(selfSvc.instanceHistory, "Get")).Return(nil, errors.New("get failed")).Build()
			err := selfSvc.CreateInstanceHistory(ctx, req)
			So(err, ShouldNotBeNil)
		})
		PatchConvey("Test GetInstanceHistory returns existing history", func() {
			existingHistory := &dao.InstanceHistory{
				ID:           "history-123",
				InstanceId:   req.GetInstanceId(),
				InstanceType: req.GetInstanceType().String(),
				RegionId:     req.GetRegionId(),
				TenantId:     "tenant-123",
				UserId:       "user-123",
				CreateTime:   1234567890,
				UpdateTime:   1234567890,
				LoginTime:    1234567890,
			}
			Mock(GetMethod(selfSvc.instanceHistory, "Get")).Return(existingHistory, nil).Build()
			PatchConvey("Test Update fails", func() {
				Mock(GetMethod(selfSvc.instanceHistory, "Update")).Return(errors.New("update failed")).Build()
				err := selfSvc.CreateInstanceHistory(ctx, req)
				So(err, ShouldNotBeNil)
			})
			PatchConvey("Test Update succeeds", func() {
				Mock(GetMethod(selfSvc.instanceHistory, "Update")).Return(nil).Build()
				err := selfSvc.CreateInstanceHistory(ctx, req)
				So(err, ShouldBeNil)
			})
		})
	})
}

func Test_ListInstanceHistory(t *testing.T) {
	ctx := context.Background()
	req := &model.DescribeRecentlyUsedDBInstancesReq{
		InstanceType: model.InstanceTypePtr(model.InstanceType_ByteRDS), // Mock instance type, fill with actual value later
		Keyword:      utils.StringRef("test"),                           // Mock keyword, fill with actual value later
		RegionId:     utils.StringRef("us-west-1"),                      // Mock region ID, fill with actual value later
		SortBy:       model.SortByPtr(model.SortBy_ASC),                 // Mock sort by, fill with actual value later
	}
	selfSvc := getDescribeInstancesService()

	PatchConvey("Test ListInstanceHistory", t, func() {
		PatchConvey("Test ListInstanceHistory success", func() {
			mockHistoryList := &dao.HistoryList{
				Total: 2,
				Items: []*dao.InstanceHistory{
					{
						ID:           "1",
						InstanceId:   "instance-1",
						InstanceType: "type-1",
						LoginTime:    1234567890,
					},
					{
						ID:           "2",
						InstanceId:   "instance-2",
						InstanceType: "type-2",
						LoginTime:    1234567891,
					},
				},
			}
			Mock(GetMethod(selfSvc.instanceHistory, "List")).Return(mockHistoryList, nil).Build()

			resp, err := selfSvc.ListInstanceHistory(ctx, req)
			So(err, ShouldBeNil)
			So(resp.Total, ShouldEqual, 2)
			So(len(resp.Instances), ShouldEqual, 2)
			So(resp.Instances[0].InstanceId, ShouldEqual, "instance-1")
			So(resp.Instances[1].InstanceId, ShouldEqual, "instance-2")
		})

		PatchConvey("Test ListInstanceHistory failed to list instance history", func() {
			Mock(GetMethod(selfSvc.instanceHistory, "List")).Return(nil, errors.New("list instance history failed")).Build()

			resp, err := selfSvc.ListInstanceHistory(ctx, req)
			So(resp, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})

		PatchConvey("Test ListInstanceHistory failed to parse instance type", func() {
			mockHistoryList := &dao.HistoryList{
				Total: 1,
				Items: []*dao.InstanceHistory{
					{
						ID:           "1",
						InstanceId:   "instance-1",
						InstanceType: "invalid-type",
						LoginTime:    1234567890,
					},
				},
			}
			Mock(GetMethod(selfSvc.instanceHistory, "List")).Return(mockHistoryList, nil).Build()

			resp, err := selfSvc.ListInstanceHistory(ctx, req)
			So(resp, ShouldNotBeNil)
			So(err, ShouldBeNil)
		})
	})
}

func Test_DeleteInstanceHistory(t *testing.T) {
	ctx := context.Background()
	selfSvc := getDescribeInstancesService()

	PatchConvey("Test DeleteInstanceHistory with IsAll true", t, func() {
		req := &model.DeleteRecentlyUsedDBInstancesReq{
			IsAll: func() *bool { b := true; return &b }(),
		}

		PatchConvey("Test DeleteAll success", func() {
			Mock(GetMethod(selfSvc.instanceHistory, "DeleteAll")).Return(nil).Build()
			err := selfSvc.DeleteInstanceHistory(ctx, req)
			So(err, ShouldBeNil)
		})

		PatchConvey("Test DeleteAll failed", func() {
			Mock(GetMethod(selfSvc.instanceHistory, "DeleteAll")).Return(errors.New("delete all failed")).Build()
			err := selfSvc.DeleteInstanceHistory(ctx, req)
			So(err, ShouldNotBeNil)
		})
	})

	PatchConvey("Test DeleteInstanceHistory with IsAll false", t, func() {
		req := &model.DeleteRecentlyUsedDBInstancesReq{
			UUID: utils.StringRef("test-uuid"),
		}

		PatchConvey("Test Delete success", func() {
			Mock(GetMethod(selfSvc.instanceHistory, "Delete")).Return(nil).Build()
			err := selfSvc.DeleteInstanceHistory(ctx, req)
			So(err, ShouldBeNil)
		})

		PatchConvey("Test Delete failed", func() {
			Mock(GetMethod(selfSvc.instanceHistory, "Delete")).Return(errors.New("delete failed")).Build()
			err := selfSvc.DeleteInstanceHistory(ctx, req)
			So(err, ShouldNotBeNil)
		})
	})

	PatchConvey("Test DeleteInstanceHistory with IsAll nil", t, func() {
		req := &model.DeleteRecentlyUsedDBInstancesReq{
			UUID: utils.StringRef("test-uuid"),
		}

		PatchConvey("Test Delete success", func() {
			Mock(GetMethod(selfSvc.instanceHistory, "Delete")).Return(nil).Build()
			err := selfSvc.DeleteInstanceHistory(ctx, req)
			So(err, ShouldBeNil)
		})

		PatchConvey("Test Delete failed", func() {
			Mock(GetMethod(selfSvc.instanceHistory, "Delete")).Return(errors.New("delete failed")).Build()
			err := selfSvc.DeleteInstanceHistory(ctx, req)
			So(err, ShouldNotBeNil)
		})
	})
}
