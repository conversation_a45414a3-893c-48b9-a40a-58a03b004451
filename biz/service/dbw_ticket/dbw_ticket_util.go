package dbw_ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	pg_parser "github.com/pganalyze/pg_query_go/v6"
)

const (
	SubTaskUndo    = 0
	SubTaskDoing   = 1
	SubTaskSuccess = 2
	SubTaskError   = 3

	MaxTableLength = 52
)

type TicketBasicInfo struct {
	InstanceId   string
	InstanceType shared.DataSourceType
	SqlText      string
	DbName       string
	TicketId     int64
	TenantId     string
}

type StopDbwSubTask struct {
	SubTaskId  string
	TicketType shared.DbwTicketType
}

type CreateDbwSubTask struct {
	TicketType shared.DbwTicketType
	TicketId   string
	SqlText    string
	TableName  string
}

type OnlineDDlTaskInfo struct {
}

type RunningInfo struct {
	TicketType           shared.DbwTicketType
	TicketId             string
	OnlineDDlRunningInfo *OnlineDDlRunningInfo
	DMlRunningInfo       *DMlRunningInfo

	// 如果有其他类型的，就在这里往后加，按需使用
}

type DMlRunningInfo struct {
}

type OnlineDDlRunningInfo struct {
	TaskInfos  []*OnlineTaskInfo
	CurrentPos int
	Process    float64
}

type OnlineTaskInfo struct {
	SqlText   string
	SubTaskId string
	Status    int
	TableName string
}

type InitRunningInfo struct {
	Ticket     *entity.Ticket
	TicketType shared.DbwTicketType
}

func getPgSQLText(ctx context.Context, parse *pg_parser.ParseResult, st *pg_parser.RawStmt) string {
	r1 := &pg_parser.ParseResult{
		Version: parse.Version,
		Stmts:   []*pg_parser.RawStmt{st},
	}
	sqlText, err := pg_parser.Deparse(r1)
	if err != nil {
		// 理论上不会有异常，因为我们用到到RawStmt就是通过sql解析出来的，反向解析必然是正确的
		// 假设真的出现了异常我们这里直接抛panic，由最上面捕获
		log.Warn(ctx, "pg Deparse error: %s", err.Error())
		panic(err)
	}
	return sqlText
}
