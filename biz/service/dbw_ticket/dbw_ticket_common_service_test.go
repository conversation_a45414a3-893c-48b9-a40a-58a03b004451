package dbw_ticket

import (
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestIsModifyIndexSqlWithoutAlter(t *testing.T) {
	service := &ticketCommonService{}
	sql := "CREATE UNIQUE INDEX `id_name` ON `t_test_time` (`id`, `name`);"
	assert.True(t, service.IsModifyIndexSqlWithoutAlter(sql))
	sql = "CREATE INDEX `id_name` ON `t_test_time` (`id`, `name`);"
	assert.True(t, service.IsModifyIndexSqlWithoutAlter(sql))
	sql = "DROP INDEX `time_idx` ON `t_test_time_2`;\n"
	assert.True(t, service.IsModifyIndexSqlWithoutAlter(sql))
	sql = "alter table `t_test_time` add  UNIQUE KEY `id_name` (`id`, `name`);"
	assert.False(t, service.IsModifyIndexSqlWithoutAlter(sql))
}

func TestIsInGhostDDLWhite(t *testing.T) {
	service := &ticketCommonService{}

	mock1 := mockey.Mock((*ticketCommonService).IsTenantInWhite).Return(false).Build()
	res := service.IsInGhostDDLWhite(context.Background(), "1", model.InstanceType_MySQL.String())
	assert.False(t, res)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*ticketCommonService).IsTenantInWhite).Return(true).Build()
	defer mock2.UnPatch()
	res = service.IsInGhostDDLWhite(context.Background(), "1", model.InstanceType_MySQL.String())
	assert.True(t, res)
}

func TestValidateOneAddColumnSql(t *testing.T) {
	service := &ticketCommonService{}

	sql := "alter table t_test add key `name` (`name`);"
	stmts, success, _ := service.CheckSqlFormat(sql)
	assert.True(t, success)
	assert.Equal(t, 1, len(stmts))
	assert.Nil(t, service.ValidateOneAddColumnSql(stmts[0]))

	sql = "alter table t_test add column `c1` varchar(50) NOT NULL;"
	stmts, success, _ = service.CheckSqlFormat(sql)
	assert.True(t, success)
	assert.Equal(t, 1, len(stmts))
	assert.NotNil(t, service.ValidateOneAddColumnSql(stmts[0]))

	sql = "alter table t_test add column `c1` varchar(50) NOT NULL DEFAULT '';"
	stmts, success, _ = service.CheckSqlFormat(sql)
	assert.True(t, success)
	assert.Equal(t, 1, len(stmts))
	assert.Nil(t, service.ValidateOneAddColumnSql(stmts[0]))

	sql = "alter table t_test add column `c1` varchar(50);"
	stmts, success, _ = service.CheckSqlFormat(sql)
	assert.True(t, success)
	assert.Equal(t, 1, len(stmts))
	assert.Nil(t, service.ValidateOneAddColumnSql(stmts[0]))

	sql = "CREATE TABLE `t_test` (`id` varchar(32)) ENGINE=InnoDB;"
	stmts, success, _ = service.CheckSqlFormat(sql)
	assert.True(t, success)
	assert.Equal(t, 1, len(stmts))
	assert.Nil(t, service.ValidateOneAddColumnSql(stmts[0]))

}
