package dbw_ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/biz/service/message"
	parserService "code.byted.org/infcs/dbw-mgr/biz/service/parser"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	pg_parser "github.com/pganalyze/pg_query_go/v6"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
	"runtime/debug"
	"strings"

	"code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
	_ "github.com/pingcap/tidb/types/parser_driver"
)

/**
一些各种工单都会用到的公共方法，避免重复编写代码
*/

type ticketCommonService struct {
	dbwInstance    dal.DbwInstanceDAL
	dbwUser        dal.DbwUserDAL
	workflowDal    dal.WorkflowDAL
	ds             datasource.DataSourceService
	actorClient    cli.ActorClient
	conf           config.ConfigProvider
	priSvc         usermgmt.PrivilegeServiceInterface
	pgPriSvc       usermgmt.PostgresPrivilegeServiceInterface
	c3ConfProvider c3.ConfigProvider
	location       location.Location
	i18nSvc        i18n.I18nServiceInterface
	ps             parserService.CommandParser
	flowRepo       repository.ApprovalFlowRepo
	archiveRepo    repository.DataArchiveRepo
	msgService     message.MsgService
}

type TicketCommonService interface {
	CheckSqlFormat(sqlText string) ([]ast.StmtNode, bool, string)
	SplitSqls(ctx context.Context, sqlText string) []*SqlInfo
	SplitSqlsByDBType(ctx context.Context, dbType shared.DataSourceType, sqlText string) []*SqlInfo
	GetDBDataSource(ctx context.Context, ticketInfo TicketBasicInfo) (*shared.DataSource, error)
	GetDbwAdminDatasource(ctx context.Context, ticketInfo TicketBasicInfo) (*shared.DataSource, error)

	GetSessionId(ctx context.Context, ticketInfo TicketBasicInfo) (string, error)
	GiveBackInstanceSession(ctx context.Context, instanceId string, sessionId string)

	CheckTablePermission(ctx context.Context, table *SqlInfo, allUserPrivilege *UserPrivilege, preCheckMsg *shared.PreCheckDbwTicket) error
	GetUserAllInstancePrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*UserPrivilege, error)

	IsCreateTableSql(sql string) bool
	IsDDLSql(sql string) bool
	IsModifyIndexSqlWithoutAlter(sql string) bool

	IsDirectExecuteDDLSql(sql string) bool
	Generate6MD5(input string) string
	IsInGhostDDLWhite(ctx context.Context, tenantId string, instanceType string) bool
	GetAdminAccountPassword(key string, instanceId string) string
	ValidateOneAddColumnSql(stmtNode ast.StmtNode) error
}

type NewTicketCommonServiceIn struct {
	dig.In
	DbwInstance    dal.DbwInstanceDAL
	DbwUser        dal.DbwUserDAL
	WorkflowDal    dal.WorkflowDAL
	Ds             datasource.DataSourceService
	ActorClient    cli.ActorClient
	Conf           config.ConfigProvider
	PriSvc         usermgmt.PrivilegeServiceInterface
	PgPriSvc       usermgmt.PostgresPrivilegeServiceInterface
	C3ConfProvider c3.ConfigProvider
	Location       location.Location
	I18nSvc        i18n.I18nServiceInterface
	Ps             parserService.CommandParser
	FlowRepo       repository.ApprovalFlowRepo
	ArchiveRepo    repository.DataArchiveRepo
	MsgService     message.MsgService
}

func NewTicketCommonService(d NewTicketCommonServiceIn) TicketCommonService {
	h := &ticketCommonService{
		dbwInstance:    d.DbwInstance,
		dbwUser:        d.DbwUser,
		workflowDal:    d.WorkflowDal,
		ds:             d.Ds,
		actorClient:    d.ActorClient,
		conf:           d.Conf,
		priSvc:         d.PriSvc,
		pgPriSvc:       d.PgPriSvc,
		c3ConfProvider: d.C3ConfProvider,
		location:       d.Location,
		i18nSvc:        d.I18nSvc,
		ps:             d.Ps,
		flowRepo:       d.FlowRepo,
		archiveRepo:    d.ArchiveRepo,
		msgService:     d.MsgService,
	}
	return h
}

func (selfService *ticketCommonService) CheckSqlFormat(sqlText string) ([]ast.StmtNode, bool, string) {
	p := parser.New()
	stmts, _, err := p.Parse(sqlText, "utf8", "")
	if err != nil {
		errMsg := fmt.Sprintf("SQL statement error or unsupported, reason: %v", err)
		return stmts, false, errMsg
	}
	return stmts, true, ""
}

func (selfService *ticketCommonService) CheckSqlFormatForPostGreSQL(ctx context.Context, sqlText string) ([]string, bool, string) {
	var errMsg string
	var res = make([]string, 0)

	defer func() {
		if r := recover(); r != nil {
			log.Info(ctx, "parse pg sql(%s) fail %v %s", sqlText, errMsg, string(debug.Stack()))
			errMsg = fmt.Sprintf("parse pg sql(%s) fail %v %s", sqlText, errMsg, string(debug.Stack()))
		}
	}()
	parse, err := pg_parser.Parse(sqlText)
	if err != nil {
		errMsg = fmt.Sprintf("parse pg [%v] error [%v]", sqlText, err)
		log.Warn(ctx, errMsg)
		return res, false, errMsg
	}

	for idx, val := range parse.Stmts {
		log.Info(ctx, "parse pg sql (%s) node %d %s", sqlText, idx, val.String())
		res = append(res, getPgSQLText(ctx, parse, val))
	}
	return res, true, ""
}

func (selfService *ticketCommonService) ValidateOneAddColumnSql(stmtNode ast.StmtNode) error {
	alterTableStmt, ok := stmtNode.(*ast.AlterTableStmt)
	if !ok {
		// 非alter table语句
		return nil
	}
	for _, spec := range alterTableStmt.Specs {
		if spec.Tp != ast.AlterTableAddColumns {
			continue // 跳过非ADD COLUMN操作
		}

		for _, colDef := range spec.NewColumns {
			if selfService.hasNotNullConstraint(colDef) && !selfService.hasDefaultValue(colDef) {
				return fmt.Errorf("sql: %s, column '%s' set 'NOT NULL' but no default value ", alterTableStmt.Text(), colDef.Name.Name.O)
			}
		}
	}
	return nil
}

func (selfService *ticketCommonService) hasNotNullConstraint(colDef *ast.ColumnDef) bool {
	for _, option := range colDef.Options {
		if option.Tp == ast.ColumnOptionNotNull {
			return true
		}
	}
	return false
}

// 检查列定义是否包含默认值
func (selfService *ticketCommonService) hasDefaultValue(colDef *ast.ColumnDef) bool {
	for _, option := range colDef.Options {
		switch option.Tp {
		case ast.ColumnOptionDefaultValue, // DEFAULT 值
			ast.ColumnOptionAutoIncrement, // AUTO_INCREMENT
			ast.ColumnOptionOnUpdate,      // ON UPDATE
			ast.ColumnOptionGenerated:     // GENERATED ALWAYS AS
			return true
		}
	}
	return false
}

func (selfService *ticketCommonService) GetSessionId(ctx context.Context, ticketInfo TicketBasicInfo) (string, error) {
	// 从连接池获取链接
	ds, err := selfService.GetDBDataSource(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "GetDBDataSource error:%v", err.Error())
		return "", err
	}
	sessionID, err := selfService.GetInstanceSession(ctx, ds)
	if err != nil {
		log.Warn(ctx, "explain ticket get session error:%s", err.Error())
		return "", err
	}
	return sessionID, nil
}

func (selfService *ticketCommonService) GiveBackInstanceSession(ctx context.Context, instanceId string, sessionId string) {
	err := selfService.actorClient.KindOf(consts.SessionMgrActorKind).Send(ctx, instanceId, &shared.GiveBackSession{InstanceId: instanceId, SessionId: sessionId})
	if err != nil {
		log.Warn(ctx, "give back session error:%s", err.Error())
		return
	}
	log.Info(ctx, "give back session %s", sessionId)
}

func (selfService *ticketCommonService) GetInstanceSession(ctx context.Context, ds *shared.DataSource) (string, error) {
	resp, err := selfService.actorClient.KindOf(consts.SessionMgrActorKind).Call(ctx, ds.InstanceId, &shared.GetSession{
		InstanceId:      ds.InstanceId,
		DSType:          ds.Type,
		LinkType:        shared.Volc,
		UserAccountName: ds.User,
		UserPassword:    ds.Password,
		Database:        ds.Db,
	})
	if err != nil {
		log.Warn(ctx, "failed to get session, err=%v", err)
		return "", err
	}
	switch rsp := resp.(type) {
	case *shared.SessionInfo:
		log.Info(ctx, "succeed to get session, sessionId=%s", rsp.GetSessionId())
		return rsp.GetSessionId(), nil
	case *shared.GetSessionFailed:
		log.Warn(ctx, "failed to get session, resp=%#v", resp)
		return "", fmt.Errorf(rsp.ErrorMessage)
	default:
		err = consts.ErrorOf(model.ErrorCode_InternalError)
		log.Warn(ctx, "unknown response of GetSession, resp=%#v", resp)
		return "", err
	}
}

func (selfService *ticketCommonService) GetDBDataSource(ctx context.Context, ticketInfo TicketBasicInfo) (*shared.DataSource, error) {
	c := fwctx.GetBizContext(ctx)
	if c != nil {
		c.TenantID = ticketInfo.TenantId
	}

	log.Info(ctx, "cx-pos-test tenantId:%s", fwctx.GetTenantID(ctx))
	if ticketInfo.InstanceType == shared.VeDBMySQL && (tenant.IsRDSMultiCloudPlatform(ctx, selfService.conf) || ticketInfo.TenantId == "2100067216") {
		return selfService.GetDbwAdminDatasource(ctx, ticketInfo)
	}

	return selfService.getNormalDBDataSource(ctx, ticketInfo)
}

func (selfService *ticketCommonService) getNormalDBDataSource(ctx context.Context, ticketInfo TicketBasicInfo) (*shared.DataSource, error) {
	cnf := selfService.conf.Get(ctx)
	ds := &shared.DataSource{
		Type:             ticketInfo.InstanceType,
		ConnectTimeoutMs: cnf.TicketConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.TicketReadTimeout * 1000,
		WriteTimeoutMs:   cnf.TicketWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.TicketIdleTimeout * 1000,
		InstanceId:       ticketInfo.InstanceId,
		Db:               ticketInfo.DbName,
	}
	// 这里获取安全管控的账号密码
	_, err := selfService.GetDBAccount(ctx, ds)
	if err != nil {
		log.Warn(ctx, "get db account error:%s", err.Error())
		return nil, err
	}
	// 获取默认终端的连接地址
	_, err = selfService.GetDBAddress(ctx, ds)
	if err != nil {
		log.Warn(ctx, "get db address error:%s", err.Error())
		return nil, err
	}
	return ds, nil
}

func (selfService *ticketCommonService) GetDBAccount(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error) {
	// 这块需要实例管理dbw_instance的表记录
	instanceInfo, err := selfService.dbwInstance.Get(ctx, ds.InstanceId, ds.Type.String(), "", fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		log.Warn(ctx, "pos-xxxxx  %s, %s, %s", ds.InstanceId, ds.Type.String(), fwctx.GetTenantID(ctx))
		log.Info(ctx, "ticket： get Instance %s info error:%s", ds.InstanceId, err.Error())
		return nil, err
	}
	// 这里需要把密码进行解密
	ds.Password = utils.DecryptData(instanceInfo.DatabasePassword, instanceInfo.InstanceId)
	ds.User = instanceInfo.DatabaseUser
	return ds, nil
}

func (selfService *ticketCommonService) GetDBAddress(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error) {
	rreq := &datasource.GetDBInnerAddressReq{
		Source: ds,
	}
	rresp, err := selfService.ds.GetDBInnerAddress(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "ticket: get db address error:", err.Error())
		return nil, err
	}
	if rresp.Source.Address == "" {
		log.Warn(ctx, "ticket: get empty db address ")
		return nil, errors.New("get empty db address")
	}
	return ds, nil
}

func (selfService *ticketCommonService) SplitSqls(ctx context.Context, sqlText string) []*SqlInfo {
	stmts, success, _ := selfService.CheckSqlFormat(sqlText)
	if !success {
		return []*SqlInfo{}
	}
	var sqls []*SqlInfo
	// 循环所有的语句执行命令,求explain的语句
	for _, val := range stmts {
		sqlInfo := &SqlInfo{Sql: val.Text(), TableNames: []string{}, DbNames: []string{}}
		tablesNames := selfService.getTables(ctx, val)
		for _, tableName := range tablesNames {
			sqlInfo.TableNames = append(sqlInfo.TableNames, tableName.Name.String())
			sqlInfo.DbNames = append(sqlInfo.DbNames, tableName.Schema.String())
		}
		sqls = append(sqls, sqlInfo)
	}
	return sqls
}

func (selfService *ticketCommonService) SplitSqlsByDBType(ctx context.Context, dbType shared.DataSourceType, sqlText string) []*SqlInfo {
	switch dbType {
	case shared.MySQL, shared.VeDBMySQL:
		stmts, success, _ := selfService.CheckSqlFormat(sqlText)
		if !success {
			return []*SqlInfo{}
		}
		var sqls []*SqlInfo
		// 循环所有的语句执行命令,求explain的语句
		for _, val := range stmts {
			sqlInfo := &SqlInfo{Sql: val.Text(), TableNames: []string{}, DbNames: []string{}}
			tablesNames := selfService.getTables(ctx, val)
			for _, tableName := range tablesNames {
				sqlInfo.TableNames = append(sqlInfo.TableNames, tableName.Name.String())
				sqlInfo.DbNames = append(sqlInfo.DbNames, tableName.Schema.String())
			}
			sqls = append(sqls, sqlInfo)
		}
		return sqls
	case shared.Postgres:
		stmts, success, _ := selfService.CheckSqlFormatForPostGreSQL(ctx, sqlText)
		if !success {
			return []*SqlInfo{}
		}
		var sqls []*SqlInfo
		// 循环所有的语句执行命令,求explain的语句
		for _, val := range stmts {
			sqlInfo := &SqlInfo{Sql: val, TableNames: []string{}, DbNames: []string{}}
			// postGreSQL暂时不解析库表名字,不好解析
			//tablesNames := selfService.getTables(ctx, val)
			//for _, tableName := range tablesNames {
			//	sqlInfo.TableNames = append(sqlInfo.TableNames, tableName.Name.String())
			//	sqlInfo.DbNames = append(sqlInfo.DbNames, tableName.Schema.String())
			//}
			sqls = append(sqls, sqlInfo)
		}
		return sqls
	default:
		log.Warn(ctx, "this is a unknown instance type:%v", dbType)
		return make([]*SqlInfo, 0)
	}
}

func (selfService *ticketCommonService) getTables(ctx context.Context, node ast.StmtNode) []*ast.TableName {
	var visitor = &GetTableNameVisitor{
		Tables: []*ast.TableName{},
	}
	// 先把右侧取到，因为sql解析的leftNode是靠右侧的点，之后我们一直往左侧走即可
	switch stmt := node.(type) {
	default:
		stmt.Accept(visitor)
		// 去重一下
		var res []*ast.TableName
		var resMap = make(map[string]bool, 0)
		for _, val := range visitor.Tables {
			if _, ok := resMap[fmt.Sprintf(val.Schema.String(), val.Name.String())]; !ok {
				res = append(res, val)
				resMap[fmt.Sprintf(val.Schema.String(), val.Name.String())] = true
			}
		}
		//for _, val := range res {
		//	log.Info(ctx, fmt.Sprintf("get db from sql is %v,get table from sql is %v", val.Schema.String(), val.Name.String()))
		//}
		return res
	}
}

func (selfService *ticketCommonService) CheckTablePermission(ctx context.Context, table *SqlInfo, allUserPrivilege *UserPrivilege, preCheckMsg *shared.PreCheckDbwTicket) error {
	// 如果sql存在库名且库名和执行db不一致，则不允许执行
	for _, dbName := range table.DbNames {
		if dbName != "" && dbName != preCheckMsg.Db {
			return fmt.Errorf("执行db:%s, sql db:%s 不一致，不允许执行", preCheckMsg.Db, dbName)
		}
	}
	isUpperAccount, err := selfService.workflowDal.IsUpperAccount(ctx, preCheckMsg.TenantId, preCheckMsg.CreateUser, preCheckMsg.InstanceId)
	if err != nil {
		log.Warn(ctx, "failed to get and create user role，err：%v", err)
		return fmt.Errorf("inner error: failed to get and create user role")
	}
	if isUpperAccount {
		return nil
	}
	for _, tbName := range table.TableNames {
		// 按从高到低的权限进行检查，如果前面通过了，后面则在不需要进行检查
		if selfService.checkInstancePrivilege(preCheckMsg.InstanceId, allUserPrivilege.instancePrivilege) {
			continue
		}
		if selfService.checkDatabasePrivilege(preCheckMsg.Db, allUserPrivilege.databasePrivilege) {
			continue
		}
		if selfService.checkTablePrivilege(preCheckMsg.Db, tbName, allUserPrivilege.tablePrivilege) {
			continue
		}
		return fmt.Errorf("do not have database/table：%s.%s permission", preCheckMsg.Db, tbName)
	}
	return nil
}

func (selfService *ticketCommonService) GetUserAllInstancePrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*UserPrivilege, error) {
	allUserPrivilege := &UserPrivilege{}
	// instance
	instancePrivilege, err := selfService.workflowDal.GetUserInstancePrivilege(ctx, userId, instanceId, tenantId, privilegeType)
	if err != nil {
		return nil, err
	}
	allUserPrivilege.instancePrivilege = *instancePrivilege
	// database
	databasePrivilege, err := selfService.workflowDal.GetUserDatabasePrivilege(ctx, userId, instanceId, tenantId, privilegeType)
	if err != nil {
		return nil, err
	}
	allUserPrivilege.databasePrivilege = *databasePrivilege
	// table
	tablePrivilege, err := selfService.workflowDal.GetUserTablePrivilege(ctx, userId, instanceId, tenantId, privilegeType)
	if err != nil {
		return nil, err
	}
	allUserPrivilege.tablePrivilege = *tablePrivilege
	// column
	columnPrivilege, err := selfService.workflowDal.GetUserColumnPrivilege(ctx, userId, instanceId, tenantId, privilegeType)
	if err != nil {
		return nil, err
	}
	allUserPrivilege.columnPrivilege = *columnPrivilege
	return allUserPrivilege, nil
}

func (selfService *ticketCommonService) GetDbwAdminDatasource(ctx context.Context, ticketInfo TicketBasicInfo) (*shared.DataSource, error) {
	veDBDatasource, err := selfService.getNormalDBDataSource(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "instance:%s get datasource error:%s", ticketInfo.InstanceId, err.Error())
		return nil, err
	}
	c3Cfg := selfService.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	// 更换账号为dbw_admin的账号
	veDBDatasource.User = c3Cfg.DBWAccountName
	veDBDatasource.Password = selfService.GetAdminAccountPassword(c3Cfg.DbwAccountPasswordGenKey, ticketInfo.InstanceId)
	return veDBDatasource, nil
}

func (selfService *ticketCommonService) GetAdminAccountPassword(key string, instanceId string) string {
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(instanceId))
	expectedMac := hex.EncodeToString(mac.Sum(nil))
	return "Dbw_" + expectedMac[:26]
}

//func (selfService *ticketCommonService) GetInstanceSession(ctx context.Context, ds *shared.DataSource) (string, error) {
//	// now := time.Now().Unix()
//	//name := ds.InstanceId + conv.Int64ToStr(now)
//	resp, err := o.actorClient.KindOf(consts.SessionMgrActorKind).Call(ctx, ds.InstanceId, &shared.GetSession{
//		InstanceId:      ds.InstanceId,
//		DSType:          ds.Type,
//		LinkType:        shared.Volc,
//		UserAccountName: ds.User,
//		UserPassword:    ds.Password,
//		Database:        ds.Db,
//	})
//	if err != nil {
//		log.Warn(ctx, "failed to get session, err=%v", err)
//		return "", err
//	}
//	switch rsp := resp.(type) {
//	case *shared.SessionInfo:
//		log.Info(ctx, "succeed to get session, sessionId=%s", rsp.GetSessionId())
//		return rsp.GetSessionId(), nil
//	case *shared.GetSessionFailed:
//		log.Warn(ctx, "failed to get session, resp=%#v", resp)
//		return "", fmt.Errorf(rsp.ErrorMessage)
//	default:
//		err = consts.ErrorOf(model.ErrorCode_InternalError)
//		log.Warn(ctx, "unknown response of GetSession, resp=%#v", resp)
//		return "", err
//	}
//}

func (selfService *ticketCommonService) IsCreateTableSql(sql string) bool {
	stmts, checkSuccess, _ := selfService.CheckSqlFormat(sql)
	if !checkSuccess {
		return false
	}
	for _, stmt := range stmts {
		// 检查语句类型是否为DDL
		switch stmt.(type) {
		case
			*ast.CreateTableStmt:
			return true
		default:
			continue
		}
	}
	return false
}

func (selfService *ticketCommonService) IsDDLSql(sql string) bool {
	stmts, checkSuccess, _ := selfService.CheckSqlFormat(sql)
	if !checkSuccess {
		return false
	}

	for _, stmt := range stmts {
		// 检查语句类型是否为DDL
		switch stmt.(type) {
		case
			*ast.AlterTableStmt, // online ddl，需用户确认的，唯一索引和主键

			*ast.AlterDatabaseStmt,  // 直接执行 TODO ？
			*ast.CreateDatabaseStmt, // 直接执行
			*ast.CreateTableStmt,    // 直接执行
			*ast.DropTableStmt,      // 直接执行
			*ast.TruncateTableStmt:  // 直接执行
			continue
		default:
			return false
		}
	}
	return true
}

func (selfService *ticketCommonService) IsModifyIndexSqlWithoutAlter(sql string) bool {
	stmts, checkSuccess, _ := selfService.CheckSqlFormat(sql)
	if !checkSuccess {
		return false
	}

	for _, stmt := range stmts {
		// 检查语句类型是否为DDL
		switch stmt.(type) {
		case
			*ast.CreateIndexStmt,
			*ast.DropIndexStmt:
			continue
		default:
			return false
		}
	}
	return true
}

func (selfService *ticketCommonService) IsDirectExecuteDDLSql(sql string) bool {
	stmts, checkSuccess, _ := selfService.CheckSqlFormat(sql)
	if !checkSuccess {
		return false
	}
	for _, stmt := range stmts {
		// 检查语句类型是否为DDL
		switch stmt.(type) {
		case
			*ast.AlterDatabaseStmt,  // 直接执行 TODO ？
			*ast.CreateDatabaseStmt, // 直接执行
			*ast.CreateTableStmt,    // 直接执行
			*ast.DropTableStmt,      // 直接执行
			*ast.TruncateTableStmt:  // 直接执行
			continue
		default:
			return false
		}
	}
	return true
}

func (selfService *ticketCommonService) checkInstancePrivilege(instanceID string, instancePrivilege []*dao.UserInstancePrivilege) bool {
	return fp.StreamOf(instancePrivilege).Filter(func(instancePrivilege *dao.UserInstancePrivilege) bool {
		return instancePrivilege.InstanceId == instanceID
	}).Exists()
}

func (selfService *ticketCommonService) checkDatabasePrivilege(dbName string, databasePrivilege []*dao.UserDatabasePrivilege) bool {
	return fp.StreamOf(databasePrivilege).Filter(func(dbPrivilege *dao.UserDatabasePrivilege) bool {
		return strings.EqualFold(dbName, dbPrivilege.DbName)
	}).Exists()
}

func (selfService *ticketCommonService) checkTablePrivilege(dbName string, tbName string, tablePrivilege []*dao.UserTablePrivilege) bool {
	return fp.StreamOf(tablePrivilege).Filter(func(tbPrivilege *dao.UserTablePrivilege) bool {
		return strings.EqualFold(dbName, tbPrivilege.DbName) && strings.EqualFold(tbName, tbPrivilege.TbName)
	}).Exists()
}

func (selfService *ticketCommonService) Generate6MD5(input string) string {
	// 计算 MD5 哈希
	hash := md5.Sum([]byte(input))
	// 转换为十六进制字符串
	hashString := hex.EncodeToString(hash[:])
	// 返回前 6 位
	return hashString[:6]
}

func (selfService *ticketCommonService) IsInGhostDDLWhite(ctx context.Context, tenantId string, instanceType string) bool {
	return selfService.IsTenantInWhite(ctx, tenantId)

	// TODO	暂时不做数据库类型检查
	//if !selfService.IsTenantInWhite(ctx, tenantId) {
	//	return false
	//}
	//switch instanceType {
	//case model.InstanceType_Postgres.String(), model.InstanceType_Mongo.String(),
	//	model.InstanceType_Redis.String(), model.InstanceType_VeDBMySQL.String(), model.InstanceType_MSSQL.String():
	//	return false
	//}
	//return true
}

func (selfService *ticketCommonService) IsTenantInWhite(ctx context.Context, tenantId string) bool {
	cnf := selfService.conf.Get(ctx)
	if cnf.TurnOnAllNewOnlineDDL {
		return true
	}
	if cnf.NewOnlineDDLWhiteList == nil {
		return false
	}
	for _, whiteTenant := range cnf.NewOnlineDDLWhiteList {
		if tenantId == strings.TrimSpace(whiteTenant) {
			return true
		}
	}
	return false
}

// GetTableNameVisitor 获取表名
type GetTableNameVisitor struct {
	Tables []*ast.TableName
}

func (d *GetTableNameVisitor) Enter(in ast.Node) (out ast.Node, skipChildren bool) {
	switch node := in.(type) {
	case *ast.TableName: // 所有表的集合
		d.Tables = append(d.Tables, node)
	}
	return in, false
}

func (d *GetTableNameVisitor) Leave(in ast.Node) (out ast.Node, ok bool) {
	return in, true
}

type SqlInfo struct {
	Sql        string
	TableNames []string
	DbNames    []string
}

type UserPrivilege struct {
	instancePrivilege []*dao.UserInstancePrivilege
	databasePrivilege []*dao.UserDatabasePrivilege
	tablePrivilege    []*dao.UserTablePrivilege
	columnPrivilege   []*dao.UserColumnPrivilege
}
