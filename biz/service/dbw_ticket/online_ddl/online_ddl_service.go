package online_ddl

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	dtsModel "code.byted.org/infcs/dbw-mgr/gen/dts-mgr/2022-10-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	dslibutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	"code.byted.org/infcs/ds-sql-parser/ast"
	"context"
	"fmt"
	_ "github.com/pingcap/tidb/types/parser_driver"
	"go.uber.org/dig"
)

type NewOnlineDDlServiceIn struct {
	dig.In
	Conf                config.ConfigProvider
	TicketCommonService dbw_ticket.TicketCommonService
	IDSvc               idgen.Service
	ActorClient         cli.ActorClient
	TicketRepo          repository.TicketRepo
	Datasource          datasource.DataSourceService
	PriSvc              usermgmt.PrivilegeServiceInterface
	DtsMgr              mgr.Provider `name:"dts"`
	C3Conf              c3.ConfigProvider
}

type NewOnlineDDlServiceOut struct {
	dig.Out
	Source dbw_ticket.DbwTicketService `group:"dbw_ticket"`
}

func NewOnlineDDlService(p NewOnlineDDlServiceIn) NewOnlineDDlServiceOut {
	return NewOnlineDDlServiceOut{
		Source: &onlineDDlImpl{
			DbwTicketService:    dbw_ticket.NewDbwTicketServiceDecorator(nil).Export(),
			cnf:                 p.Conf,
			ticketCommonService: p.TicketCommonService,
			idSvc:               p.IDSvc,
			actorClient:         p.ActorClient,
			ticketRepo:          p.TicketRepo,
			datasource:          p.Datasource,
			priSvc:              p.PriSvc,
			dtsMgr:              p.DtsMgr,
			c3Conf:              p.C3Conf,
		},
	}
}

type onlineDDlImpl struct {
	dbw_ticket.DbwTicketService
	cnf                 config.ConfigProvider
	ticketCommonService dbw_ticket.TicketCommonService
	idSvc               idgen.Service
	actorClient         cli.ActorClient
	ticketRepo          repository.TicketRepo
	datasource          datasource.DataSourceService
	priSvc              usermgmt.PrivilegeServiceInterface
	dtsMgr              mgr.Provider
	c3Conf              c3.ConfigProvider
}

func (o *onlineDDlImpl) Type() shared.DbwTicketType {
	return shared.OnlineDDL
}

func (o *onlineDDlImpl) PreCheck(ctx context.Context, preCheckMsg *shared.PreCheckDbwTicket) bool {
	ticketInfo := dbw_ticket.TicketBasicInfo{
		InstanceId:   preCheckMsg.InstanceId,
		InstanceType: preCheckMsg.InstanceType,
		SqlText:      preCheckMsg.SqlText,
		DbName:       preCheckMsg.Db,
		TenantId:     preCheckMsg.TenantId,
	}
	success := true
	// 给账号做授权
	// o.GrantAccount(ctx, ticketInfo)
	// 检查sql语法正确性
	if !o.checkSqlFormat(ctx, ticketInfo, preCheckMsg) {
		success = false
	}
	// 安全规则检查
	if !o.validateSecurityRule(ctx, ticketInfo, preCheckMsg) {
		success = false
	}
	// 用户权限检查
	if !o.checkUserPermission(ctx, preCheckMsg) {
		success = false
	}
	// DryRun
	if !o.validateDryRun(ctx, preCheckMsg, ticketInfo) {
		success = false
	}
	// 库表检查
	if !o.validateTableInfo(ctx, preCheckMsg, ticketInfo) {
		success = false
	}
	// explain 检查
	if !o.checkExplain(ctx, ticketInfo, preCheckMsg) {
		success = false
	}
	// 检查空间占用情况，是否够执行online ddl
	if !o.validateTableSpace(ctx, preCheckMsg, ticketInfo) {
		success = false
	}
	// 添加唯一主键检查
	if !o.validateUniqKey(ctx, preCheckMsg) {
		success = false
	}
	// 是否存在dts双向同步任务，或在dts目标端做变更
	if !o.validateDTSTask(ctx, preCheckMsg) {
		success = false
	}
	return success
}

func (o *onlineDDlImpl) GrantAccount(ctx context.Context, ticketInfo dbw_ticket.TicketBasicInfo) {

	if ticketInfo.InstanceType != shared.VeDBMySQL {
		return
	}

	if !tenant.IsRDSMultiCloudPlatform(ctx, o.cnf) && fwctx.GetTenantID(ctx) != "**********" {
		return
	}

	// 目前仅给VeDB的，多云用户的账号做复制权限的授权
	dbwAdminDatasource, err := o.ticketCommonService.GetDbwAdminDatasource(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "instance:%s get datasource error:%s", ticketInfo.InstanceId, err.Error())
		return
	}
	// 检查DbwAdmin账号
	o.checkAndSetDbwAdmin(ctx, dbwAdminDatasource)
	// 给用户账号授权
	o.grantReplicaToUserAccount(ctx, dbwAdminDatasource, ticketInfo)
}

func (o *onlineDDlImpl) checkAndSetDbwAdmin(ctx context.Context, dbwAdminDatasource *shared.DataSource) {
	err := o.datasource.EnsureAccount(ctx, &datasource.EnsureAccountReq{Source: dbwAdminDatasource})
	if err != nil {
		log.Warn(ctx, "instance:%s ensure account error:%s", dbwAdminDatasource.InstanceId, err.Error())
		return
	}
	checkReq := &datasource.CheckDBWAccountReq{
		DSType:      dbwAdminDatasource.Type,
		InstanceId:  dbwAdminDatasource.InstanceId,
		AccountName: dbwAdminDatasource.User,
	}
	isSuper, err := o.datasource.CheckAccountPrivilege(ctx, checkReq)
	if err != nil || !isSuper {
		_ = o.datasource.ResetAccount(ctx, dbwAdminDatasource.InstanceId, dbwAdminDatasource.Type)
	}
}

func (o *onlineDDlImpl) grantReplicaToUserAccount(ctx context.Context, dbwAdminDatasource *shared.DataSource, ticketInfo dbw_ticket.TicketBasicInfo) {
	userDatasource, err := o.ticketCommonService.GetDBDataSource(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "instance:%s get datasource error:%s", ticketInfo.InstanceId, err.Error())
		return
	}
	err = o.datasource.GrantReplicationPrivilege(ctx, dbwAdminDatasource, userDatasource.User)
	if err != nil {
		log.Warn(ctx, "instance:%s grant replication privilege error:%s", ticketInfo.InstanceId, err.Error())
	}
}

func (o *onlineDDlImpl) validateSecurityRule(ctx context.Context, ticketInfo dbw_ticket.TicketBasicInfo, preCheckMsg *shared.PreCheckDbwTicket) bool {
	detail := o.initPreCheckDetail(ctx, preCheckMsg, model.PreCheckItem_Syntax.String(), "安全检查")
	ds, err := o.ticketCommonService.GetDBDataSource(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "GetDBDataSource error:%v", err.Error())
		detail.Result_ = "inner error: get datasource error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	sqlInfos := o.ticketCommonService.SplitSqls(ctx, preCheckMsg.SqlText)
	if len(sqlInfos) == 0 {
		detail.Result_ = "sql format error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	success := true
	for _, value := range sqlInfos {
		// val sql语句
		if err = o.priSvc.SecurityCheckForTicket(ctx, value.Sql, ds, model.SqlExecutionType_SqlTicket, preCheckMsg.TicketId); err != nil {
			success = false
		}
	}
	if !success {
		detail.Result_ = "check error"
		detail.PreCheckState = model.PreCheckState_Error
	}
	_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
	return detail.PreCheckState == model.PreCheckState_Success
}

func (o *onlineDDlImpl) checkSqlFormat(ctx context.Context, ticketInfo dbw_ticket.TicketBasicInfo, preCheckMsg *shared.PreCheckDbwTicket) bool {
	detail := o.initPreCheckDetail(ctx, preCheckMsg, model.PreCheckItem_Syntax.String(), "格式检查")
	stmtNodes, success, errMsg := o.ticketCommonService.CheckSqlFormat(ticketInfo.SqlText)
	if !success {
		detail.Result_ = errMsg
		detail.PreCheckState = model.PreCheckState_Error
	} else {
		o.ValidateAddColumnSql(stmtNodes, detail)
	}

	_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)

	return success
}

func (o *onlineDDlImpl) ValidateAddColumnSql(stmtNodes []ast.StmtNode, detail *entity.PreCheckDetail) {
	detail.ItemDetails = []*model.ItemDetail{}
	isSuccess := true
	for _, stmtNode := range stmtNodes {
		itemDetail := &model.ItemDetail{SQL: stmtNode.Text(), Result_: "Success"}
		err := o.ticketCommonService.ValidateOneAddColumnSql(stmtNode)
		if err != nil {
			itemDetail.Result_ = err.Error()
			isSuccess = false
		}
		detail.ItemDetails = append(detail.ItemDetails, itemDetail)
	}
	if !isSuccess {
		detail.Result_ = "column  set 'NOT NULL' but no default value"
		detail.PreCheckState = model.PreCheckState_Error
	}
}

func (o *onlineDDlImpl) checkExplain(ctx context.Context, ticketInfo dbw_ticket.TicketBasicInfo, preCheckMsg *shared.PreCheckDbwTicket) bool {
	detail := o.initPreCheckDetail(ctx, preCheckMsg, model.PreCheckItem_Explain.String(), "Explain 检查")
	sessionID, err := o.ticketCommonService.GetSessionId(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "ticket get session error:%s", err.Error())
		detail.Result_ = "inner error: get session error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	// 释放连接
	defer o.ticketCommonService.GiveBackInstanceSession(ctx, ticketInfo.InstanceId, sessionID)
	sqlInfos := o.ticketCommonService.SplitSqls(ctx, preCheckMsg.SqlText)
	if len(sqlInfos) == 0 {
		detail.Result_ = "sql format error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	detail.ItemDetails = []*model.ItemDetail{}
	success := true
	rowAffects := int64(0)
	for _, sqlInfo := range sqlInfos {
		itemDetail := &model.ItemDetail{SQL: sqlInfo.Sql}
		detail.ItemDetails = append(detail.ItemDetails, itemDetail)
		result, err := o.explainOneTable(ctx, sessionID, sqlInfo, ticketInfo.DbName)
		if err != nil {
			success = false
			log.Warn(ctx, "explainOneTable error:%s", err.Error())
			itemDetail.Result_ = err.Error()
		} else {
			itemDetail.Result_ = fmt.Sprintf("%d", result)
			rowAffects += result
		}
	}
	detail.Result_ = fmt.Sprintf("%d", rowAffects)
	if !success {
		detail.PreCheckState = model.PreCheckState_Error
	}
	_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
	return success
}

func (o *onlineDDlImpl) explainOneTable(ctx context.Context, sessionID string, sqlInfo *dbw_ticket.SqlInfo, db string) (int64, error) {
	if !o.ticketCommonService.IsDDLSql(sqlInfo.Sql) {
		if !o.ticketCommonService.IsModifyIndexSqlWithoutAlter(sqlInfo.Sql) {
			return 0, fmt.Errorf("not support sql, you can use alter table to modify index")
		}
		return 0, fmt.Errorf("sql is not support ddl sql")
	}

	if o.ticketCommonService.IsCreateTableSql(sqlInfo.Sql) {
		return 0, nil
	}
	// 因为是ddl，取一个表名就行了
	var tableName string
	if len(sqlInfo.TableNames) > 0 {
		tableName = sqlInfo.TableNames[0]
	} else {
		return 0, nil
	}
	explainSql := fmt.Sprintf("select count(*) from %s.%s", db, tableName)

	resp, err := o.actorClient.KindOf(consts.SessionActorKind).Call(ctx, sessionID, &shared.ExplainCommandReq{SQLText: explainSql, DB: db})
	if err != nil {
		log.Warn(ctx, "get explain ticket sqlText error:%s ", err.Error())
		return 0, fmt.Errorf("inner error: call session error")
	}
	rowsAffect := int64(0)
	switch rsp := resp.(type) {
	case *shared.ExplainCommandResp:
		for _, val := range rsp.CommandRes {
			rowsAffect += dslibutils.MustStrToInt64(val.Rows)
		}
	case *shared.DataSourceOpFailed:
		log.Warn(ctx, "failed to obtain the SQL statement for the ticket that affects the number of rows:%s", rsp.ErrorMessage)
		return 0, fmt.Errorf("failed to obtain the SQL statement for the ticket that affects the number of rows")
	default:
		log.Warn(ctx, "failed to obtain the SQL statement for the ticket that affects the number of rows")
		return 0, fmt.Errorf("failed to obtain the SQL statement for the ticket that affects the number of rows")
	}
	return rowsAffect, nil
}

func (o *onlineDDlImpl) checkUserPermission(ctx context.Context, preCheckMsg *shared.PreCheckDbwTicket) bool {
	detail := o.initPreCheckDetail(ctx, preCheckMsg, model.PreCheckItem_Permission.String(), "权限检查")
	sqlInfos := o.ticketCommonService.SplitSqls(ctx, preCheckMsg.SqlText)
	if len(sqlInfos) == 0 {
		detail.Result_ = "sql format error"
		detail.PreCheckState = model.PreCheckState_Error
	}
	userPrivileges, err := o.ticketCommonService.GetUserAllInstancePrivilege(ctx, preCheckMsg.CreateUser, preCheckMsg.InstanceId, preCheckMsg.TenantId, model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
	if err != nil {
		detail.Result_ = "inner error: get user privilege error "
		detail.PreCheckState = model.PreCheckState_Error
	}
	if detail.PreCheckState == model.PreCheckState_Error {
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}

	var itemDetails []*model.ItemDetail
	hasError := false
	for _, value := range sqlInfos {
		itemDetail := &model.ItemDetail{SQL: value.Sql, Result_: "success"}
		if err = o.ticketCommonService.CheckTablePermission(ctx, value, userPrivileges, preCheckMsg); err != nil {
			itemDetail.Result_ = err.Error()
			hasError = true
		}
		itemDetails = append(itemDetails, itemDetail)
	}
	detail.ItemDetails = itemDetails
	if hasError {
		detail.Result_ = "check error"
		detail.PreCheckState = model.PreCheckState_Error
	}
	_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
	return detail.PreCheckState == model.PreCheckState_Success
}

func (o *onlineDDlImpl) validateDryRun(ctx context.Context, preCheckMsg *shared.PreCheckDbwTicket, ticketInfo dbw_ticket.TicketBasicInfo) bool {
	detail := o.initPreCheckDetail(ctx, preCheckMsg, model.PreCheckItem_OnlineDDLDry.String(), "Online DDL DryRun检查")
	detail.ItemDetails = []*model.ItemDetail{{SQL: "-", Result_: "success"}}
	sessionID, err := o.ticketCommonService.GetSessionId(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "ticket get session error:%s", err.Error())
		detail.Result_ = "inner error: get session error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	// 释放连接
	defer o.ticketCommonService.GiveBackInstanceSession(ctx, ticketInfo.InstanceId, sessionID)

	sqlInfos := o.ticketCommonService.SplitSqls(ctx, preCheckMsg.SqlText)
	if len(sqlInfos) == 0 {
		detail.Result_ = "sql format error"
		detail.ItemDetails[0].Result_ = "sql format error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	var sqls []*shared.SqlInfo
	for _, value := range sqlInfos {
		if o.ticketCommonService.IsCreateTableSql(value.Sql) {
			itemDetail := &model.ItemDetail{SQL: value.Sql, Result_: "success"}
			detail.ItemDetails = append(detail.ItemDetails, itemDetail)
			continue
		}
		sql := &shared.SqlInfo{SqlText: value.Sql, TableNames: value.TableNames}
		sqls = append(sqls, sql)
	}

	resp, err := o.actorClient.KindOf(consts.SessionActorKind).Call(ctx, sessionID, &shared.ValidateDryRun{InstanceType: preCheckMsg.InstanceType, SqlList: sqls})
	if err != nil {
		log.Warn(ctx, "failed to get session, err=%v", err)
		detail.Result_ = "inner error: request session error"
		detail.PreCheckState = model.PreCheckState_Error
		detail.ItemDetails[0].Result_ = "inner error: request session error"
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	switch rsp := resp.(type) {
	case *shared.ValidateResponse:
		if rsp.Success == datasource.ValidateError {
			detail.Result_ = rsp.Result
			detail.PreCheckState = model.PreCheckState_Error
		}
		for _, value := range rsp.ItemDetail {
			itemDetail := &model.ItemDetail{SQL: value.SqlText, Result_: value.Result}
			detail.ItemDetails = append(detail.ItemDetails, itemDetail)
		}
	default:
		log.Warn(ctx, "unknown response of GetSession, resp=%#v", resp)
		detail.Result_ = "inner error: request session error"
		detail.ItemDetails[0].Result_ = "inner error: request session error"
		detail.PreCheckState = model.PreCheckState_Error
	}
	_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
	return detail.PreCheckState == model.PreCheckState_Success
}

func (o *onlineDDlImpl) validateTableInfo(ctx context.Context, preCheckMsg *shared.PreCheckDbwTicket, ticketInfo dbw_ticket.TicketBasicInfo) bool {
	detail := o.initPreCheckDetail(ctx, preCheckMsg, model.PreCheckItem_OnlineDDLOriginalTable.String(), "Online DDL 库表检查")
	detail.ItemDetails = []*model.ItemDetail{}
	sessionID, err := o.ticketCommonService.GetSessionId(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "ticket get session error:%s", err.Error())
		detail.Result_ = "inner error: get session error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	// 释放连接
	defer o.ticketCommonService.GiveBackInstanceSession(ctx, ticketInfo.InstanceId, sessionID)

	sqlInfos := o.ticketCommonService.SplitSqls(ctx, preCheckMsg.SqlText)
	if len(sqlInfos) == 0 {
		detail.Result_ = "sql format error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	var sqls []*shared.SqlInfo
	for _, value := range sqlInfos {
		if o.ticketCommonService.IsDirectExecuteDDLSql(value.Sql) {
			itemDetail := &model.ItemDetail{SQL: value.Sql, Result_: "success"}
			detail.ItemDetails = append(detail.ItemDetails, itemDetail)
			continue
		}
		sql := &shared.SqlInfo{SqlText: value.Sql, TableNames: value.TableNames}
		sqls = append(sqls, sql)
	}
	req := &shared.ValidateOriginalTable{InstanceType: ticketInfo.InstanceType, SqlList: sqls}
	resp, err := o.actorClient.KindOf(consts.SessionActorKind).Call(ctx, sessionID, req)
	if err != nil {
		log.Warn(ctx, "failed to get session, err=%v", err)
		detail.Result_ = "inner error: request session error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	switch rsp := resp.(type) {
	case *shared.ValidateResponse:
		if rsp.Success == datasource.ValidateError {
			detail.Result_ = "check error"
			detail.PreCheckState = model.PreCheckState_Error
		}
		for _, value := range rsp.ItemDetail {
			itemDetail := &model.ItemDetail{SQL: value.SqlText, Result_: value.Result}
			detail.ItemDetails = append(detail.ItemDetails, itemDetail)
		}
	default:
		log.Warn(ctx, "unknown response of GetSession, resp=%#v", resp)
		detail.Result_ = "inner error: request session error"
		detail.PreCheckState = model.PreCheckState_Error
	}
	_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
	return detail.PreCheckState == model.PreCheckState_Success
}

func (o *onlineDDlImpl) validateUniqKey(ctx context.Context, preCheckMsg *shared.PreCheckDbwTicket) bool {
	detail := o.initPreCheckDetail(ctx, preCheckMsg, model.PreCheckItem_UniqueKey.String(), "Online DDL 添加唯一键检查")
	sqlInfos, success, _ := o.ticketCommonService.CheckSqlFormat(preCheckMsg.SqlText)
	if !success {
		detail.Result_ = "sql format error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	hasUniqIdx := o.hasUniqIdx(sqlInfos)
	if hasUniqIdx {
		detail.Result_ = "ddl has modify unique key"
		detail.PreCheckState = model.PreCheckState_Warn
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return true
	}
	_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
	return detail.PreCheckState == model.PreCheckState_Success
}

func (o *onlineDDlImpl) checkDropIdx(ctx context.Context, indexInfos []*shared.PreCheckIndexInfo, ticketInfo dbw_ticket.TicketBasicInfo, detail *entity.PreCheckDetail) {
	sessionID, err := o.ticketCommonService.GetSessionId(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "ticket get session error:%s", err.Error())
		detail.Result_ = "inner error: get session error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return
	}
	// 释放连接
	defer o.ticketCommonService.GiveBackInstanceSession(ctx, ticketInfo.InstanceId, sessionID)

	resp, err := o.actorClient.KindOf(consts.SessionActorKind).Call(ctx, sessionID, &shared.ValidateUniqIndex{InstanceType: ticketInfo.InstanceType, IndexInfos: indexInfos})
	if err != nil {
		log.Warn(ctx, "failed to get session, err=%v", err)
		detail.Result_ = "inner error: request session error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return
	}
	switch rsp := resp.(type) {
	case *shared.ValidateResponse:
		for _, value := range rsp.ItemDetail {
			itemDetail := &model.ItemDetail{SQL: value.SqlText, Result_: value.Result}
			detail.ItemDetails = append(detail.ItemDetails, itemDetail)
		}
		detail.PreCheckState = model.PreCheckState(rsp.Success)
		detail.Result_ = rsp.Result
	default:
		log.Warn(ctx, "unknown response of GetSession, resp=%#v", resp)
		detail.Result_ = "inner error: request session error"
		detail.PreCheckState = model.PreCheckState_Error
	}
	_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
	return
}

func (o *onlineDDlImpl) hasUniqIdx(sqlInfos []ast.StmtNode) bool {
	// Iterate over the parsed statement nodes
	for _, stmtNode := range sqlInfos {
		switch node := stmtNode.(type) {
		case *ast.AlterTableStmt:
			for _, spec := range node.Specs {
				if spec.Tp == ast.AlterTableAddConstraint {
					if spec.Constraint.Tp == ast.ConstraintUniq || spec.Constraint.Tp == ast.ConstraintPrimaryKey {
						return true
					}
				}
			}
		case *ast.CreateIndexStmt:
			if node.KeyType == ast.IndexKeyTypeUnique {
				return true
			}
		}
	}
	return false
}

func (o *onlineDDlImpl) validateDTSTask(ctx context.Context, preCheckMsg *shared.PreCheckDbwTicket) bool {
	detail := o.initPreCheckDetail(ctx, preCheckMsg, model.PreCheckItem_DtsTask.String(), "DTS任务检查")
	detail.ItemDetails = []*model.ItemDetail{{SQL: "-", Result_: "success"}}

	pageNum := int32(1)
	pageSize := int32(10)
	status := dtsModel.TaskStatus_Running
	taskType := dtsModel.TaskType_DataSynchronization
	describeDtsReq := &dtsModel.DescribeTransmissionTasksReq{PageNumber: &pageNum, PageSize: &pageSize, TaskStatus: &status, TaskType: &taskType}
	describeDtsResp := &dtsModel.DescribeTransmissionTasksResp{}
	hasMore := true

	for hasMore {
		if !o.validateBatchDTSTask(ctx, detail, describeDtsReq, describeDtsResp) {
			return true
		}
		size := int32(0)
		for _, value := range describeDtsResp.Tasks {
			if value.TaskId != nil {
				// 主任务，因为存在双向任务，所以tasks的列表长度不一定等于size
				size++
			}
			if value.DestConfig != nil && value.DestConfig.VolcMySQLSettings != nil && value.DestConfig.VolcMySQLSettings.GetDBInstanceId() == preCheckMsg.InstanceId {
				detail.Result_ = "There is a running instance of the Dest DTS one-way task. Or the current instance has a running two-way DTS task"
				detail.ItemDetails[0].Result_ = "There is a running instance of the Dest DTS one-way task. Or the current instance has a running two-way DTS task"
				detail.PreCheckState = model.PreCheckState_Warn
				break
			}
			if value.DestConfig != nil && value.DestConfig.VolcveDBMySQLSettings != nil && value.DestConfig.VolcveDBMySQLSettings.GetDBInstanceId() == preCheckMsg.InstanceId {
				detail.Result_ = "There is a running instance of the Dest DTS one-way task. Or the current instance has a running two-way DTS task"
				detail.ItemDetails[0].Result_ = "There is a running instance of the Dest DTS one-way task. Or the current instance has a running two-way DTS task"
				detail.PreCheckState = model.PreCheckState_Warn
				break
			}
		}
		pageNum++
		describeDtsReq.PageNumber = &pageNum
		if size != pageSize {
			hasMore = false
		}
	}
	_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
	return true
}

func (o *onlineDDlImpl) validateBatchDTSTask(ctx context.Context, detail *entity.PreCheckDetail, req *dtsModel.DescribeTransmissionTasksReq, resp *dtsModel.DescribeTransmissionTasksResp) bool {
	err := o.dtsMgr.Get().Call(ctx, dtsModel.Action_DescribeTransmissionTasks.String(), req, resp, client.WithVersion(consts.Dts_Version_V2))
	if err != nil {
		log.Warn(ctx, "call dts Action_DescribeTransmissionTasks error:%s", err.Error())
		detail.Result_ = "inner error: describe dts task error"
		detail.ItemDetails[0].Result_ = "inner error: describe dts task error"
		detail.PreCheckState = model.PreCheckState_Warn
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	return true
}

func (o *onlineDDlImpl) validateTableSpace(ctx context.Context, preCheckMsg *shared.PreCheckDbwTicket, ticketInfo dbw_ticket.TicketBasicInfo) bool {
	detail := o.initPreCheckDetail(ctx, preCheckMsg, model.PreCheckItem_OnlineDDLSpace.String(), "Online DDL 剩余空间检查")
	detail.ItemDetails = []*model.ItemDetail{{SQL: "-", Result_: "success"}}
	sqlInfos := o.ticketCommonService.SplitSqls(ctx, preCheckMsg.SqlText)
	if len(sqlInfos) == 0 {
		detail.ItemDetails[0].Result_ = "sql format error"
		detail.Result_ = "sql format error"
		detail.PreCheckState = model.PreCheckState_Error
		_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
		return false
	}
	success, errMsg := o.checkSpaceCapacity(ctx, ticketInfo, sqlInfos)
	if !success {
		detail.Result_ = errMsg
		detail.ItemDetails[0].Result_ = errMsg
		detail.PreCheckState = model.PreCheckState_Warn
	}
	_ = o.ticketRepo.CreateItemPreCheck(ctx, detail)
	return detail.PreCheckState == model.PreCheckState_Success
}

func (o *onlineDDlImpl) checkSpaceCapacity(ctx context.Context, ticketInfo dbw_ticket.TicketBasicInfo, sqlInfos []*dbw_ticket.SqlInfo) (bool, string) {
	if ticketInfo.InstanceType == shared.VeDBMySQL {
		// VeDB 理论空间无上限，不存在总空间的概念
		return true, ""
	}
	spaceSum, err := o.getTableSpace(ctx, ticketInfo, sqlInfos)
	if err != nil {
		log.Warn(ctx, "getTableSpace error:%v", err.Error())
		return false, "inner error，get total table space error"
	}
	// 查当前空间使用率, TODO 只有mysql，其他的后面加转换
	usedSize, err := o.datasource.GetUsedSize(ctx, &datasource.GetDiskSizeReq{InstanceType: ticketInfo.InstanceType, InstanceId: ticketInfo.InstanceId})
	if err != nil {
		log.Warn(ctx, "GetUsedSize error:%v", err.Error())
		return false, "inner error，get space usage error"
	}
	// 查总空间大小
	instanceDetail, err := o.datasource.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{Type: ticketInfo.InstanceType, InstanceId: ticketInfo.InstanceId})
	if err != nil {
		log.Warn(ctx, "DescribeDBInstanceDetail error:%v", err.Error())
		return false, "inner error，get space usage error"
	}
	storageSpace := instanceDetail.StorageSpace * 1024 * 1024 * 1024

	// StorageSpace 单位 GiB  1024*1024*1024 B
	// usedSize,totalSum 单位 B
	log.Info(ctx, "InstanceId:%s, spaceInfo, total:%d, tableTotal:%d, used:%d ", ticketInfo.InstanceId, storageSpace, spaceSum, usedSize)
	if float64(storageSpace-usedSize)-float64(spaceSum)*2 < 0 {
		// 剩余空间小于这次变更的表的两倍
		return false, fmt.Sprintf("the remaining space is smaller than the tablespace of this change sum, remainSpace: %dB", storageSpace-usedSize)
	}
	return true, ""
}

func (o *onlineDDlImpl) getTableSpace(ctx context.Context, ticketInfo dbw_ticket.TicketBasicInfo, sqlInfos []*dbw_ticket.SqlInfo) (int64, error) {
	sessionID, err := o.ticketCommonService.GetSessionId(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "ticket get session error:%s", err.Error())
		return 0, err
	}
	// 释放连接
	defer o.ticketCommonService.GiveBackInstanceSession(ctx, ticketInfo.InstanceId, sessionID)
	getTableSpaceMsg := &shared.GetTableSpace{InstanceId: ticketInfo.InstanceId, InstanceType: ticketInfo.InstanceType}
	for _, value := range sqlInfos {
		getTableSpaceMsg.TableNames = append(getTableSpaceMsg.TableNames, value.TableNames...)
	}
	resp, err := o.actorClient.KindOf(consts.SessionActorKind).Call(ctx, sessionID, getTableSpaceMsg)
	if err != nil {
		log.Warn(ctx, "failed to get session, err=%v", err)
		return 0, fmt.Errorf("inner error: request session error")
	}
	switch rsp := resp.(type) {
	case *shared.GetTableSpaceResp:
		if !rsp.Success {
			return 0, fmt.Errorf(rsp.ErrMsg)
		}
		return rsp.TotalSpace, nil
	default:
		log.Warn(ctx, "unknown response of GetSession, resp=%#v", resp)
		return 0, fmt.Errorf("inner error: unknown response, request session error")
	}
}

func (o *onlineDDlImpl) StopTask(ctx context.Context, runningInfo *dbw_ticket.RunningInfo) error {
	if runningInfo.OnlineDDlRunningInfo.CurrentPos >= len(runningInfo.OnlineDDlRunningInfo.TaskInfos) {
		log.Info(ctx, "%s ticket is finished, needn't stop", runningInfo.TicketId)
		return nil
	}

	err := o.actorClient.KindOf(consts.OnlineDDLActorKind).Send(ctx, runningInfo.OnlineDDlRunningInfo.TaskInfos[runningInfo.OnlineDDlRunningInfo.CurrentPos].SubTaskId, &shared.StopDbwTicket{})
	if err != nil {
		log.Warn(ctx, "ticket: %s, stop %s task error:%v", runningInfo.TicketId, runningInfo.OnlineDDlRunningInfo.TaskInfos[runningInfo.OnlineDDlRunningInfo.CurrentPos].SubTaskId, err)
		return err
	}
	return nil
}

func (o *onlineDDlImpl) RunNextTask(ctx context.Context, runningInfo *dbw_ticket.RunningInfo) (bool, error) {
	o.updateLastTaskRunningInfo(ctx, runningInfo)
	runningInfo.OnlineDDlRunningInfo.CurrentPos++
	if runningInfo.OnlineDDlRunningInfo.CurrentPos >= len(runningInfo.OnlineDDlRunningInfo.TaskInfos) {
		// 任务都执行完了，不需要再执行下一批次了
		return false, nil
	}
	sqlText := runningInfo.OnlineDDlRunningInfo.TaskInfos[runningInfo.OnlineDDlRunningInfo.CurrentPos].SqlText
	tableName := runningInfo.OnlineDDlRunningInfo.TaskInfos[runningInfo.OnlineDDlRunningInfo.CurrentPos].TableName
	// 执行下一个批次
	subTaskId, err := o.CreateTask(ctx, &dbw_ticket.CreateDbwSubTask{TicketId: runningInfo.TicketId, SqlText: sqlText, TableName: tableName})
	if err != nil {
		log.Warn(ctx, "ticket:%s create next task error:%v", runningInfo.TicketId, err)
		return false, err
	}
	runningInfo.OnlineDDlRunningInfo.TaskInfos[runningInfo.OnlineDDlRunningInfo.CurrentPos].SubTaskId = subTaskId
	runningInfo.OnlineDDlRunningInfo.TaskInfos[runningInfo.OnlineDDlRunningInfo.CurrentPos].Status = dbw_ticket.SubTaskDoing
	runningInfo.OnlineDDlRunningInfo.Process = float64(runningInfo.OnlineDDlRunningInfo.CurrentPos / len(runningInfo.OnlineDDlRunningInfo.TaskInfos))
	return true, nil
}

func (o *onlineDDlImpl) updateLastTaskRunningInfo(ctx context.Context, runningInfo *dbw_ticket.RunningInfo) {
	if runningInfo.OnlineDDlRunningInfo.CurrentPos < 0 {
		// 第一个任务，前面没有任务
		return
	}
	lastPos := runningInfo.OnlineDDlRunningInfo.CurrentPos
	runningInfo.OnlineDDlRunningInfo.TaskInfos[lastPos].Status = dbw_ticket.SubTaskSuccess

	proces := (lastPos + 1) * 100 / len(runningInfo.OnlineDDlRunningInfo.TaskInfos)
	err := o.ticketRepo.UpdateRunningInfo(ctx, &entity.TicketRunningInfo{TicketId: runningInfo.TicketId, TicketStatus: int(model.TicketStatus_TicketExecute), Description: "", Process: proces})
	if err != nil {
		log.Warn(ctx, "UpdateRunningInfo error:%s", err.Error())
	}
}

func (o *onlineDDlImpl) CreateTask(ctx context.Context, createTaskMsg *dbw_ticket.CreateDbwSubTask) (string, error) {
	subTaskId, err := o.idSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "gen id error:%v", err)
		return "", fmt.Errorf("get sub task id error")
	}

	err = o.actorClient.KindOf(consts.OnlineDDLActorKind).Send(ctx, fmt.Sprintf("%d", subTaskId), &shared.ExecuteDbwTicket{TicketId: createTaskMsg.TicketId, SqlText: createTaskMsg.SqlText, TableName: createTaskMsg.TableName})
	if err != nil {
		log.Warn(ctx, "create online ddl sub task error:%v", err)
		return "", err
	}
	return fmt.Sprintf("%d", subTaskId), nil
}

func (o *onlineDDlImpl) InitRunningInfo(ctx context.Context, msg *dbw_ticket.InitRunningInfo) *dbw_ticket.RunningInfo {
	return &dbw_ticket.RunningInfo{
		TicketType: msg.TicketType,
		TicketId:   msg.Ticket.TicketId,
		OnlineDDlRunningInfo: &dbw_ticket.OnlineDDlRunningInfo{
			TaskInfos:  o.initSubTask(ctx, msg),
			CurrentPos: -1,
		},
	}
}

func (o *onlineDDlImpl) initSubTask(ctx context.Context, msg *dbw_ticket.InitRunningInfo) []*dbw_ticket.OnlineTaskInfo {
	var OnlineTaskInfo []*dbw_ticket.OnlineTaskInfo
	sqlInfos := o.ticketCommonService.SplitSqls(ctx, msg.Ticket.SqlText)
	for _, sqlInfo := range sqlInfos {
		// 因为是ddl，所以只取第一条就行了
		tableName := ""
		if len(sqlInfo.TableNames) > 0 {
			tableName = sqlInfo.TableNames[0]
		}
		OnlineTaskInfo = append(OnlineTaskInfo, &dbw_ticket.OnlineTaskInfo{SqlText: sqlInfo.Sql, Status: dbw_ticket.SubTaskUndo, TableName: tableName})
	}
	return OnlineTaskInfo
}

func (o *onlineDDlImpl) initPreCheckDetail(ctx context.Context, preCheckMsg *shared.PreCheckDbwTicket, ItemEn string, ItemCn string) *entity.PreCheckDetail {
	id, _ := o.idSvc.NextID(ctx)
	return &entity.PreCheckDetail{
		Id:            id,
		TicketId:      preCheckMsg.TicketId,
		TenantId:      preCheckMsg.TenantId,
		ItemNameEN:    ItemEn,
		ItemNameCN:    ItemCn,
		PreCheckState: model.PreCheckState_Success,
		Result_:       "success",
	}
}
