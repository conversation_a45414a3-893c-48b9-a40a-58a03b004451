package online_ddl

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/repository"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/mock_dbw_ticket"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func mockOnlineDDlService() *onlineDDlImpl {
	return &onlineDDlImpl{
		cnf:                 &config.MockConfigProvider{},
		ticketCommonService: &mock_dbw_ticket.MockTicketCommonService{},
		idSvc:               &mocks.MockService{},
		actorClient:         &mocks.MockActorClient{},
		ticketRepo:          &repository.MockTicketRepo{},
		datasource:          &mocks.MockDataSourceService{},
		priSvc:              &mocks.MockPrivilegeServiceInterface{},
	}
}

func TestValidateSecurityRule(t *testing.T) {
	impl := mockOnlineDDlService()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetDBDataSource).Return(&shared.DataSource{}, fmt.Errorf("test")).Build()

	res := impl.validateSecurityRule(context.Background(), dbw_ticket.TicketBasicInfo{}, &shared.PreCheckDbwTicket{})
	assert.False(t, res)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetDBDataSource).Return(&shared.DataSource{}, nil).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{}).Build()
	res = impl.validateSecurityRule(context.Background(), dbw_ticket.TicketBasicInfo{}, &shared.PreCheckDbwTicket{})
	assert.False(t, res)
	mock5.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock6 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{{}}).Build()
	defer mock6.UnPatch()
	mock7 := mockey.Mock((*mocks.MockPrivilegeServiceInterface).SecurityCheckForTicket).Return(nil).Build()
	defer mock7.UnPatch()
	res = impl.validateSecurityRule(context.Background(), dbw_ticket.TicketBasicInfo{}, &shared.PreCheckDbwTicket{})
	assert.False(t, res)
}

func TestCheckUserPermission(t *testing.T) {
	impl := mockOnlineDDlService()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	// Case 1: 正常权限检查
	t.Run("success_case", func(t *testing.T) {
		mockDetail := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
		mockSplit := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{{Sql: "pos-sample-sql"}}).Build()
		mockPrivilege := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).CheckTablePermission).Return(nil).Build()
		mockPrivilege2 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetUserAllInstancePrivilege).Return(nil, nil).Build()
		mock2 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
		defer func() {
			mockDetail.UnPatch()
			mockSplit.UnPatch()
			mockPrivilege.UnPatch()
			mockPrivilege2.UnPatch()
			defer mock2.UnPatch()
		}()

		res := impl.checkUserPermission(context.Background(), &shared.PreCheckDbwTicket{})
		assert.True(t, res)
	})

	// Case 2: 权限检查失败
	t.Run("permission_denied", func(t *testing.T) {
		mockDetail := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
		mockSplit := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{{Sql: "pos-sample-sql"}}).Build()
		mockPrivilege := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).CheckTablePermission).Return(fmt.Errorf("access denied")).Build()
		mockPrivilege2 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetUserAllInstancePrivilege).Return(nil, nil).Build()
		mock2 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
		defer func() {
			mockDetail.UnPatch()
			mockSplit.UnPatch()
			mockPrivilege.UnPatch()
			mockPrivilege2.UnPatch()
			defer mock2.UnPatch()
		}()

		res := impl.checkUserPermission(context.Background(), &shared.PreCheckDbwTicket{})
		assert.False(t, res)
	})
}

func TestCheckSqlFormat(t *testing.T) {
	impl := mockOnlineDDlService()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	// Case 1: SQL格式正确
	t.Run("selcet 1", func(t *testing.T) {
		mockDetail := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
		mockCheck := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).CheckSqlFormat).
			Return(nil, true, nil).Build()
		mockRepo := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
		defer func() {
			mockDetail.UnPatch()
			mockRepo.UnPatch()
			mockCheck.UnPatch()
		}()

		res := impl.checkSqlFormat(context.Background(), dbw_ticket.TicketBasicInfo{}, &shared.PreCheckDbwTicket{})
		assert.True(t, res)
	})

	// Case 2: SQL语法错误
	t.Run("sql_invalid", func(t *testing.T) {
		mockDetail := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
		mockCheck := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).CheckSqlFormat).
			Return(nil, false, "syntax error").Build()
		mockRepo := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
		defer func() {
			mockDetail.UnPatch()
			mockCheck.UnPatch()
			mockRepo.UnPatch()
		}()

		res := impl.checkSqlFormat(context.Background(), dbw_ticket.TicketBasicInfo{}, &shared.PreCheckDbwTicket{})
		assert.False(t, res)
	})
}

func TestCheckExplain(t *testing.T) {
	impl := mockOnlineDDlService()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	giveBackMock := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GiveBackInstanceSession).Return().Build()
	defer giveBackMock.UnPatch()

	// 测试获取session失败
	mock1 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock2 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock3 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("", fmt.Errorf("session error")).Build()

	res := impl.checkExplain(context.Background(), dbw_ticket.TicketBasicInfo{}, &shared.PreCheckDbwTicket{})
	assert.False(t, res)
	mock1.UnPatch()
	mock2.UnPatch()
	mock3.UnPatch()

	// 测试SQL分割失败
	mock4 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock5 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock6 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("session1", nil).Build()
	mock7 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{}).Build()

	res = impl.checkExplain(context.Background(), dbw_ticket.TicketBasicInfo{}, &shared.PreCheckDbwTicket{})
	assert.False(t, res)
	mock4.UnPatch()
	mock5.UnPatch()
	mock6.UnPatch()
	mock7.UnPatch()

	// 测试explain执行成功
	mock8 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock9 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock10 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("session1", nil).Build()
	mock11 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{{Sql: "test", TableNames: []string{"table1"}}}).Build()
	mock12 := mockey.Mock((*onlineDDlImpl).explainOneTable).Return(int64(1), nil).Build()

	res = impl.checkExplain(context.Background(), dbw_ticket.TicketBasicInfo{}, &shared.PreCheckDbwTicket{})
	assert.True(t, res)
	mock8.UnPatch()
	mock9.UnPatch()
	mock10.UnPatch()
	mock11.UnPatch()
	mock12.UnPatch()

	// 测试explain执行失败
	mock13 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock14 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock15 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("session1", nil).Build()
	mock16 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{{Sql: "test", TableNames: []string{"table1"}}}).Build()
	mock17 := mockey.Mock((*onlineDDlImpl).explainOneTable).Return(int64(0), fmt.Errorf("explain error")).Build()

	res = impl.checkExplain(context.Background(), dbw_ticket.TicketBasicInfo{}, &shared.PreCheckDbwTicket{})
	assert.False(t, res)
	mock13.UnPatch()
	mock14.UnPatch()
	mock15.UnPatch()
	mock16.UnPatch()
	mock17.UnPatch()
}

func TestExplainOneTable(t *testing.T) {
	impl := mockOnlineDDlService()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	giveBackMock := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GiveBackInstanceSession).Return().Build()
	defer giveBackMock.UnPatch()
	mockClient := mocks.NewMockKindClient(&gomock.Controller{})
	mockx := mockey.Mock((*mocks.MockActorClient).KindOf).Return(mockClient).Build()
	defer mockx.UnPatch()
	mock8 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.ExplainCommandResp{
		CommandRes: []*shared.ExplainCommandResult{{Rows: "10"}},
	}, nil).Build()

	// 测试非DDL SQL
	mock1 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsDDLSql).Return(false).Build()
	mockx1 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsModifyIndexSqlWithoutAlter).Return(true).Build()
	defer mockx1.UnPatch()
	res, err := impl.explainOneTable(context.Background(), "session1", &dbw_ticket.SqlInfo{Sql: "select 1"}, "db1")
	assert.Error(t, err)
	assert.Equal(t, int64(0), res)
	mock1.UnPatch()

	// 测试创建表SQL
	mock2 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsDDLSql).Return(true).Build()
	mock3 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsCreateTableSql).Return(true).Build()
	res, err = impl.explainOneTable(context.Background(), "session1", &dbw_ticket.SqlInfo{Sql: "create table"}, "db1")
	assert.NoError(t, err)
	assert.Equal(t, int64(0), res)
	mock2.UnPatch()
	mock3.UnPatch()

	// 测试无表名情况
	mock4 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsDDLSql).Return(true).Build()
	mock5 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsCreateTableSql).Return(false).Build()
	_, err = impl.explainOneTable(context.Background(), "session1", &dbw_ticket.SqlInfo{Sql: "alter table", TableNames: []string{""}}, "db1")
	assert.NoError(t, err)
	mock4.UnPatch()
	mock5.UnPatch()

	// 测试正常执行情况
	mock6 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsDDLSql).Return(true).Build()
	mock7 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsCreateTableSql).Return(false).Build()

	res, err = impl.explainOneTable(context.Background(), "session1", &dbw_ticket.SqlInfo{
		Sql:        "alter table",
		TableNames: []string{"table1"},
	}, "db1")
	assert.NoError(t, err)
	//	assert.Equal(t, int64(10), res)
	mock6.UnPatch()
	mock7.UnPatch()
	mock8.UnPatch()

	// 测试执行失败情况
	mock9 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsDDLSql).Return(true).Build()
	mock10 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsCreateTableSql).Return(false).Build()
	mock11 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("call error")).Build()

	res, err = impl.explainOneTable(context.Background(), "session1", &dbw_ticket.SqlInfo{
		Sql:        "alter table",
		TableNames: []string{"table1"},
	}, "db1")
	assert.Error(t, err)
	assert.Equal(t, int64(0), res)
	mock9.UnPatch()
	mock10.UnPatch()
	mock11.UnPatch()
}

func TestValidateDryRun(t *testing.T) {
	impl := mockOnlineDDlService()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	mockClient := mocks.NewMockKindClient(&gomock.Controller{})
	mockx := mockey.Mock((*mocks.MockActorClient).KindOf).Return(mockClient).Build()
	defer mockx.UnPatch()
	giveBackMock := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GiveBackInstanceSession).Return().Build()
	defer giveBackMock.UnPatch()

	// 测试获取session失败
	mock1 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock2 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock3 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("", fmt.Errorf("session error")).Build()

	res := impl.validateDryRun(context.Background(), &shared.PreCheckDbwTicket{}, dbw_ticket.TicketBasicInfo{})
	assert.False(t, res)
	mock1.UnPatch()
	mock2.UnPatch()
	mock3.UnPatch()

	// 测试调用失败
	mock4 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock5 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock6 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("session1", nil).Build()
	mock7 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("call error")).Build()
	mock16 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{}).Build()
	defer mock16.UnPatch()

	res = impl.validateDryRun(context.Background(), &shared.PreCheckDbwTicket{}, dbw_ticket.TicketBasicInfo{})
	assert.False(t, res)
	mock4.UnPatch()
	mock5.UnPatch()
	mock6.UnPatch()
	mock7.UnPatch()

	// 测试验证失败
	mock8 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock9 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock10 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("session1", nil).Build()
	mock11 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.ValidateResponse{
		Success: datasource.ValidateError,
		Result:  "dry run failed",
	}, nil).Build()

	res = impl.validateDryRun(context.Background(), &shared.PreCheckDbwTicket{}, dbw_ticket.TicketBasicInfo{})
	assert.False(t, res)
	mock8.UnPatch()
	mock9.UnPatch()
	mock10.UnPatch()
	mock11.UnPatch()

	// 测试验证成功
	mock12 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock13 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock14 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("session1", nil).Build()
	mock15 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.ValidateResponse{
		Success: datasource.ValidateSuccess,
	}, nil).Build()

	_ = impl.validateDryRun(context.Background(), &shared.PreCheckDbwTicket{}, dbw_ticket.TicketBasicInfo{})
	mock12.UnPatch()
	mock13.UnPatch()
	mock14.UnPatch()
	mock15.UnPatch()
}

func TestValidateTableInfo(t *testing.T) {
	impl := mockOnlineDDlService()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	mockClient := mocks.NewMockKindClient(&gomock.Controller{})
	mockx := mockey.Mock((*mocks.MockActorClient).KindOf).Return(mockClient).Build()
	defer mockx.UnPatch()
	giveBackMock := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GiveBackInstanceSession).Return().Build()
	defer giveBackMock.UnPatch()

	// 测试获取session失败
	mock1 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock2 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock3 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("", fmt.Errorf("session error")).Build()

	res := impl.validateTableInfo(context.Background(), &shared.PreCheckDbwTicket{}, dbw_ticket.TicketBasicInfo{})
	assert.False(t, res)
	mock1.UnPatch()
	mock2.UnPatch()
	mock3.UnPatch()

	// 测试SQL分割失败
	mock4 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock5 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock6 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("session1", nil).Build()
	mock7 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{}).Build()

	res = impl.validateTableInfo(context.Background(), &shared.PreCheckDbwTicket{}, dbw_ticket.TicketBasicInfo{})
	assert.False(t, res)
	mock4.UnPatch()
	mock5.UnPatch()
	mock6.UnPatch()
	mock7.UnPatch()

	// 测试调用失败
	mock8 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock9 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock10 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("session1", nil).Build()
	mock11 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{{Sql: "test", TableNames: []string{"table1"}}}).Build()
	mock12 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("call error")).Build()
	mock18 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).IsDirectExecuteDDLSql).Return(true).Build()
	defer mock18.UnPatch()

	res = impl.validateTableInfo(context.Background(), &shared.PreCheckDbwTicket{SqlText: ""}, dbw_ticket.TicketBasicInfo{})
	assert.False(t, res)
	mock8.UnPatch()
	mock9.UnPatch()
	mock10.UnPatch()
	mock11.UnPatch()
	mock12.UnPatch()

	// 测试验证成功
	mock13 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	mock14 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	mock15 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetSessionId).Return("session1", nil).Build()
	mock16 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{{Sql: "test", TableNames: []string{"table1"}}}).Build()
	mock17 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.ValidateResponse{
		Success: datasource.ValidateSuccess,
	}, nil).Build()

	res = impl.validateTableInfo(context.Background(), &shared.PreCheckDbwTicket{}, dbw_ticket.TicketBasicInfo{})
	assert.True(t, res)
	mock13.UnPatch()
	mock14.UnPatch()
	mock15.UnPatch()
	mock16.UnPatch()
	mock17.UnPatch()
}

func TestValidateTableSpace(t *testing.T) {
	impl := mockOnlineDDlService()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqls).Return([]*dbw_ticket.SqlInfo{{}}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*repository.MockTicketRepo).CreateItemPreCheck).Return(nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*onlineDDlImpl).checkSpaceCapacity).Return(false, "").Build()
	defer mock4.UnPatch()

	impl.validateTableSpace(context.Background(), &shared.PreCheckDbwTicket{}, dbw_ticket.TicketBasicInfo{})

}

func TestValidateDTSTask(t *testing.T) {
	impl := mockOnlineDDlService()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*onlineDDlImpl).initPreCheckDetail).Return(&entity.PreCheckDetail{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*onlineDDlImpl).validateBatchDTSTask).Return(false).Build()
	defer mock2.UnPatch()

	impl.validateDTSTask(context.Background(), &shared.PreCheckDbwTicket{})
}
