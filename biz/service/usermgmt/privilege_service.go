package usermgmt

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/biz/service/parser"
	"code.byted.org/infcs/dbw-mgr/biz/service/securityrule"
	"code.byted.org/infcs/dbw-mgr/biz/service/securityrule/system_rule"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	dbwutils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	mysqlparse "code.byted.org/infcs/ds-sql-parser"
	pgparse "github.com/pganalyze/pg_query_go/v6"
	_ "github.com/pingcap/tidb/types/parser_driver"
	"go.uber.org/dig"
	"gorm.io/gorm"
)

type privilegeService struct {
	dbwInstance             dal.DbwInstanceDAL
	dbwUser                 dal.DbwUserDAL
	ds                      datasource.DataSourceService
	parser                  parser.ColumnNode
	userSvc                 UserService
	sp                      parser.CommandParser
	workflowDal             dal.WorkflowDAL
	sec                     securityrule.SecRulesValidationService
	conf                    config.ConfigProvider
	mysqlPri                MysqlPrivilegeServiceInterface
	postgresPri             PostgresPrivilegeServiceInterface
	mssqlPri                MssqlPrivilegeServiceInterface
	cli                     cli.ActorClient
	i18nSvc                 i18n.I18nServiceInterface
	secRuleExecuteRecordDAL dal.SecRuleExecuteRecordDAL
	IdgenSvc                idgen.Service
}

type PrivilegeServiceInterface interface {
	SecurityCheck(ctx context.Context, sql string, source *shared.DataSource, IgnoreSecurityCheck bool, ExecutionType model.SqlExecutionType) error
	SecurityCheckForTicket(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType, orderId int64) error
	SecurityRulesCheckForReview(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType) ([]*system_rule.CheckResult, error)
	CheckDatabasePrivilegeBySession(ctx context.Context, Schema string, source *shared.DataSource, Full bool, priType model.DbwPrivilegeType) string
	CheckTablePrivilegeBySession(ctx context.Context, Schema, Table string, source *shared.DataSource, Full bool, priType model.DbwPrivilegeType) string
	DatabasePrivilegeFilter(ctx context.Context, source *shared.DataSource, req *model.DescribeDatabasesReq, ret *model.DescribeDatabasesResp, Full bool, priType model.DbwPrivilegeType) (*model.DescribeDatabasesResp, error)
	SchemaPrivilegeFilter(ctx context.Context, req *model.DescribeSchemasReq, source *shared.DataSource, ret *model.DescribeSchemasResp, Full bool, priType model.DbwPrivilegeType) (*model.DescribeSchemasResp, error)
	TablePrivilegeFilter(ctx context.Context, req *model.DescribeTablesReq, source *shared.DataSource, ret *model.DescribeTablesResp, Full bool, priType model.DbwPrivilegeType) (*model.DescribeTablesResp, error)
	SqlExecuteCheck(ctx context.Context) error
	SqlResultCheck(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType) error
	UpdateCurExecuteCount(ctx context.Context, source *shared.DataSource) error
	UpdateCurResultCount(ctx context.Context, len int, source *shared.DataSource) error
	SqlPrivilegeCheck(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType) error
	SecurityRuleCheck(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType) error
}

type NewPrivilegeServiceIn struct {
	dig.In
	DbwInstance             dal.DbwInstanceDAL
	DbwUser                 dal.DbwUserDAL
	Ds                      datasource.DataSourceService
	UserSvc                 UserService
	Sp                      parser.CommandParser
	WorkflowDal             dal.WorkflowDAL
	Sec                     securityrule.SecRulesValidationService
	Conf                    config.ConfigProvider
	MysqlPri                MysqlPrivilegeServiceInterface
	PostgresPri             PostgresPrivilegeServiceInterface
	MssqlPri                MssqlPrivilegeServiceInterface
	Cli                     cli.ActorClient
	I18nSvc                 i18n.I18nServiceInterface
	SecRuleExecuteRecordDAL dal.SecRuleExecuteRecordDAL
	IdgenSvc                idgen.Service
}

func NewPrivilegeService(d NewPrivilegeServiceIn) PrivilegeServiceInterface {
	h := &privilegeService{
		dbwInstance:             d.DbwInstance,
		dbwUser:                 d.DbwUser,
		ds:                      d.Ds,
		userSvc:                 d.UserSvc,
		sp:                      d.Sp,
		workflowDal:             d.WorkflowDal,
		sec:                     d.Sec,
		conf:                    d.Conf,
		mysqlPri:                d.MysqlPri,
		postgresPri:             d.PostgresPri,
		mssqlPri:                d.MssqlPri,
		cli:                     d.Cli,
		i18nSvc:                 d.I18nSvc,
		secRuleExecuteRecordDAL: d.SecRuleExecuteRecordDAL,
		IdgenSvc:                d.IdgenSvc,
	}
	return h
}

func (selfSvc *privilegeService) SecurityCheck(ctx context.Context, sql string, source *shared.DataSource, IgnoreSecurityCheck bool, ExecutionType model.SqlExecutionType) error {
	if !dbwutils.IsInstanceTypeAllowPrivilegeCheck(source.Type) {
		return nil
	}
	if IgnoreSecurityCheck {
		return nil
	}
	// 是否开启了安全管控
	_, err := selfSvc.dbwInstance.Get(ctx, source.InstanceId, source.Type.String(), source.LinkType.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		} else {
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
	}
	// sql执行次数校验
	err = selfSvc.SqlExecuteCheck(ctx)
	if err != nil {
		return err
	}
	// sql返回行数校验
	err = selfSvc.SqlResultCheck(ctx, sql, source, ExecutionType)
	if err != nil {
		return err
	}
	// 安全规则校验
	err = selfSvc.SecurityRuleCheck(ctx, sql, source, ExecutionType)
	if err != nil {
		return err
	}
	// 权限校验
	err = selfSvc.SqlPrivilegeCheck(ctx, sql, source, ExecutionType)
	if err != nil {
		return err
	}
	return nil
}

func (selfSvc *privilegeService) SecurityCheckForTicket(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType, orderId int64) error {
	//首先先校验当前数据库类型，能否进行工单的安全规则检查
	if !dbwutils.IsInstanceTypeAllowPrivilegeCheck(source.Type) {
		return nil
	}
	_, err := selfSvc.dbwInstance.Get(ctx, source.InstanceId, source.Type.String(), source.LinkType.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		} else {
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
	}

	//并行调用安全规则的校验
	CheckReq := datasource.SecurityRulesCheckReq{
		InstanceType:     source.Type,
		InstanceId:       source.InstanceId,
		TenantId:         fwctx.GetTenantID(ctx),
		Source:           source.LinkType.String(),
		ParsedSql:        sql,
		SqlExecutionType: ExecutionType,
		DataSource:       source,
	}

	checkResultList, err := selfSvc.sec.ParallelValidate(ctx, &CheckReq)
	if err != nil {
		return err
	}

	//由于返回结果是对所有的true和false都有返回，但是我们只需要false的结果集，所以进行二次处理
	var ruleList []*model.CheckResult_
	for _, value := range checkResultList {
		if !value.Result {
			level := value.Level.String()
			errorMessage := ""
			if value.ErrorMsg != nil {
				errorMessage = value.ErrorMsg.Error()
			}
			ruleList = append(ruleList, &model.CheckResult_{
				ErrorMsg:   &errorMessage,
				ActionName: &value.Action,
				Level:      &level,
			})
		}
	}

	//处理完成数据之后，对数据进行保存落库，先将安全规则的等级进行计数
	var (
		HighRiskCount   int8 = 0
		MiddleRiskCount int8 = 0
		LowRiskCount    int8 = 0
	)
	for _, rule := range ruleList {
		switch *rule.Level {
		case model.RuleLevel_High.String():
			HighRiskCount++
		case model.RuleLevel_Medium.String():
			MiddleRiskCount++
		case model.RuleLevel_Low.String():
			LowRiskCount++
		}
	}

	detailID, err := selfSvc.IdgenSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "generate id error %s", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	content := ""
	if len(ruleList) > 0 {
		content = utils.Show(ruleList)
	}
	//构建完成结果之后，将数据保存落库
	record := &dao.SecurityRuleExecuteRecord{
		ID:                   strconv.FormatInt(detailID, 10),
		TenantId:             fwctx.GetTenantID(ctx),
		UserId:               fwctx.GetUserID(ctx),
		OrderType:            int8(model.SqlExecutionType_SqlTicket),
		OrderId:              orderId,
		SqlStatement:         sql,
		HighRiskCount:        HighRiskCount,
		MiddleRiskCount:      MiddleRiskCount,
		LowRiskCount:         LowRiskCount,
		ExecuteDetailContent: content,
		CreateTime:           time.Now().UnixMilli(),
		UpdateTime:           time.Now().UnixMilli(),
	}
	err = selfSvc.secRuleExecuteRecordDAL.Create(ctx, record)
	if err != nil {
		log.Warn(ctx, "secRuleExecuteRecordDAL Create record error %s", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return nil
}

func (selfSvc *privilegeService) SecurityRulesCheckForReview(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType) ([]*system_rule.CheckResult, error) {
	CheckReq := datasource.SecurityRulesCheckReq{
		InstanceType:     source.Type,
		InstanceId:       source.InstanceId,
		TenantId:         fwctx.GetTenantID(ctx),
		Source:           model.LinkType_Volc.String(),
		ParsedSql:        sql,
		SqlExecutionType: ExecutionType,
		DataSource:       source,
	}

	checkResultList, err := selfSvc.sec.ValidateBySqlReview(ctx, &CheckReq)
	if err != nil {
		return nil, err
	}

	//由于返回结果是对所有的true和false都有返回，但是我们只需要false的结果集，所以进行二次处理
	var ruleList []*system_rule.CheckResult
	// 遍历每个 map 中的键值对
	for _, value := range checkResultList {
		if !value.Result {
			ruleList = append(ruleList, value)
		}
	}

	return ruleList, nil
}

func (selfSvc *privilegeService) SqlPrivilegeCheck(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType) error {
	site := selfSvc.i18nSvc.GetLanguage(ctx)
	// 1.根据安全规则判断是否需要进行校验权限
	checkResult := selfSvc.IsSecurityRulePassedByName(ctx, sql, source, "IsCheckDbOrTablePrivilege", "check_sql_access_permission", ExecutionType)
	if checkResult != nil {
		return checkResult
	}

	// 2.根据账号类型判断是否需要进行校验权限，DBA\OWNER\ROOT账号不校验权限
	var userId string
	if fwctx.GetUserID(ctx) == "" {
		userId = fwctx.GetTenantID(ctx)
	} else {
		userId = fwctx.GetUserID(ctx)
	}
	isUpperAccount, err := selfSvc.workflowDal.IsUpperAccount(ctx, fwctx.GetTenantID(ctx), userId, source.InstanceId)
	if err != nil {
		log.Warn(ctx, "check super account error %s", err)
	}
	if isUpperAccount {
		return nil
	}

	// 3.根据安全规则判断无法解析的sql是否放行
	err = selfSvc.ParseBySourceType(ctx, source, sql)
	if err != nil {
		result := selfSvc.IsSecurityRulePassedByName(ctx, sql, source, "IsAllowUnknownSqlExec", "reject_execute", ExecutionType)
		if result != nil {
			return result
		}
		return nil
	}

	// 4.不支持的sql类型直接拒绝
	parseType := dbwutils.GetCommandTypeBySourceType(source, sql)
	if parseType.PrivilegeType == dbwutils.None && parseType.Granularity == dbwutils.None {
		return errors.New("security control instance not support this type of SQL")
	}

	// 5.有实例权限直接放行（也可为一些特殊sql无法准确解析的情况兜底）
	priType, _ := model.DbwPrivilegeTypeFromString(parseType.PrivilegeType)
	if selfSvc.userSvc.CheckInstancePrivilege(ctx, source.InstanceId, source.Type.String(), userId, fwctx.GetTenantID(ctx), priType) {
		return nil
	}

	// 6.内场和多云用户仅校验库权限
	if tenant.IsRDSMultiCloudPlatform(ctx, selfSvc.conf) && parseType.Granularity != dbwutils.Database {
		return nil
	}

	// 7.权限校验, pri为缺少的权限字段
	pri, parseErr := selfSvc.checkByCommandTypeBySourceType(ctx, sql, source, parseType, ExecutionType)
	if pri != "" {
		return fmt.Errorf(consts.ErrorWithParamBySite(site, model.ErrorCode_SqlPrivilegeCheckFailed).GetMessage(), pri)
	}
	if parseErr != nil {
		return parseErr
	}
	return nil
}

func (selfSvc *privilegeService) checkByCommandTypeBySourceType(ctx context.Context, sql string, source *shared.DataSource, parseType dbwutils.ParseType, ExecutionType model.SqlExecutionType) (pri string, err error) {
	switch source.Type {
	case shared.MySQL, shared.VeDBMySQL, shared.MySQLSharding, shared.ByteRDS:
		pri, err = selfSvc.mysqlPri.CheckByCommandTypeForMysql(ctx, sql, source, parseType)
	case shared.Postgres:
		pri, err = selfSvc.postgresPri.CheckByCommandTypeForPostgres(ctx, sql, source, parseType)
	case shared.MSSQL:
		pri, err = selfSvc.mssqlPri.CheckByCommandTypeForMssql(ctx, sql, source, parseType)
	}
	switch err {
	case nil:
		break
	// 权限解析失败
	case consts.ErrorOf(model.ErrorCode_SqlPrivilegeParseCheckFailed):
		result := selfSvc.IsSecurityRulePassedByName(ctx, sql, source, "IsAllowUnknownSqlPermissionsExec", "reject_execute", ExecutionType)
		if result != nil {
			return "", nil
		} else {
			// todo:对解析权限失败的先放行
			return "", nil
		}
	default:
		return "", err
	}
	return pri, nil
}

func (selfSvc *privilegeService) SqlExecuteCheck(ctx context.Context) error {
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	if userId == "" {
		userId = tenantId
	}
	IsRootAccount, err := selfSvc.workflowDal.IsRootAccount(ctx, fwctx.GetTenantID(ctx), userId)
	if IsRootAccount {
		return nil
	}
	if err != nil {
		log.Warn(ctx, "SqlResultCheck failed to get metadata, err=%v", err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// i18n
	site := selfSvc.i18nSvc.GetLanguage(ctx)
	//user, err := selfSvc.dbwUser.Get(ctx, userId, tenantId)
	user, err := selfSvc.userSvc.GetMaxUserConfig(ctx, tenantId, userId)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// 校验规则是否到期
	if user.MaxExecuteExpiredTime <= int32(time.Now().Unix()) {
		if user.MaxExecuteDuration != model.DurationType_Forever.String() {
			return fmt.Errorf(consts.ErrorBySite(site, model.ErrorCode_OverMaxQueriesExpirationTime).GetMessage())
		}
	}
	// 校验查询次数
	if user.MaxExecuteCount <= user.CurExecuteCount {
		return fmt.Errorf(consts.ErrorBySite(site, model.ErrorCode_OverMaxQueries).GetMessage())
	}
	return nil
}

func (selfSvc *privilegeService) SecurityRuleCheck(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType) error {

	CheckReq := datasource.SecurityRulesCheckReq{
		InstanceType:     source.Type,
		InstanceId:       source.InstanceId,
		TenantId:         fwctx.GetTenantID(ctx),
		Source:           source.LinkType.String(),
		ParsedSql:        sql,
		SqlExecutionType: ExecutionType,
		DataSource:       source,
	}
	CheckResults, err := selfSvc.sec.Validate(ctx, &CheckReq)
	if err != nil {
		return err
	}
	for _, result := range CheckResults {
		if result.Result == false {
			return result.ErrorMsg
		}
	}
	return nil
}

func (selfSvc *privilegeService) SecurityRuleCheckForReview(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType) ([]*system_rule.CheckResult, error) {

	CheckReq := datasource.SecurityRulesCheckReq{
		InstanceType:     source.Type,
		InstanceId:       source.InstanceId,
		TenantId:         fwctx.GetTenantID(ctx),
		Source:           source.LinkType.String(),
		ParsedSql:        sql,
		SqlExecutionType: ExecutionType,
		DataSource:       source,
	}
	return selfSvc.sec.ValidateBySqlReview(ctx, &CheckReq)
}

func (selfSvc *privilegeService) IsSecurityRulePassedByName(ctx context.Context, sql string, source *shared.DataSource, name string, action string, ExecutionType model.SqlExecutionType) error {
	CheckReq := datasource.SecurityRulesCheckReq{
		InstanceType:     source.Type,
		InstanceId:       source.InstanceId,
		TenantId:         fwctx.GetTenantID(ctx),
		Source:           source.LinkType.String(),
		ParsedSql:        sql,
		SqlExecutionType: ExecutionType,
		DataSource:       source,
		RuleFunc:         name,
	}
	CheckResults, err := selfSvc.sec.Validate(ctx, &CheckReq)
	if err != nil {
		return err
	}
	for key, result := range CheckResults {
		if key == name && result.Action == action && !result.Result {
			return result.ErrorMsg
		}
	}
	return nil
}

func (selfSvc *privilegeService) SqlResultCheck(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType) error {
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	if userId == "" {
		userId = tenantId
	}
	IsRootAccount, err := selfSvc.workflowDal.IsRootAccount(ctx, fwctx.GetTenantID(ctx), userId)
	if IsRootAccount {
		return nil
	}
	if err != nil {
		log.Warn(ctx, "SqlResultCheck failed to get metadata, err=%v", err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if dbwutils.IsSelect(sql, source) {
		// i18n
		site := selfSvc.i18nSvc.GetLanguage(ctx)
		//user, err := selfSvc.dbwUser.Get(ctx, userId, tenantId)
		user, err := selfSvc.userSvc.GetMaxUserConfig(ctx, tenantId, userId)
		if err != nil {
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
		// 校验规则是否到期
		if user.MaxResultExpiredTime <= int32(time.Now().Unix()) {
			if user.MaxResultDuration != model.DurationType_Forever.String() {
				return fmt.Errorf(consts.ErrorBySite(site, model.ErrorCode_OverMaxRowsExpirationTime).GetMessage())
			}
		}
		// 校验返回行数限制
		rowsAffect, err := selfSvc.ExplainCommandRowsBySourceType(ctx, sql, source, ExecutionType)
		if err != nil {
			return err
		}
		maxCommandResultCount := int32(selfSvc.conf.Get(ctx).MaxCommandResultCount)
		if rowsAffect > maxCommandResultCount {
			rowsAffect = maxCommandResultCount
		}
		if user.CurResultCount+rowsAffect > user.MaxResultCount {
			if user.MaxResultDuration != model.DurationType_Forever.String() {
				return fmt.Errorf(consts.ErrorBySite(site, model.ErrorCode_OverMaxRows).GetMessage())
			}
		}
		return nil
	}
	return nil
}

func (selfSvc *privilegeService) UpdateCurExecuteCount(ctx context.Context, source *shared.DataSource) error {
	_, err := selfSvc.dbwInstance.Get(ctx, source.InstanceId, source.Type.String(), source.LinkType.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		} else {
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
	}
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	if userId == "" {
		userId = tenantId
	}
	IsRootAccount, err := selfSvc.workflowDal.IsRootAccount(ctx, fwctx.GetTenantID(ctx), userId)
	if IsRootAccount {
		return nil
	}
	if err != nil {
		log.Warn(ctx, "SqlResultCheck failed to get metadata, err=%v", err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	user, err := selfSvc.dbwUser.Get(ctx, userId, tenantId)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}

	now := time.Now().Unix()
	if !IsSameDay(now, user.UpdatedAt) {
		err := selfSvc.dbwUser.ResetCurrentCount(ctx, 0, 0, user.UserID, user.TenantID)
		if err != nil {
			log.Warn(ctx, "Reset user %s failed:%+v", user.UserID, err.Error())
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
		user.CurExecuteCount = 0
		user.CurResultCount = 0
	}
	err = selfSvc.dbwUser.UpdateCurCount(ctx, userId, tenantId, user.CurExecuteCount+1, user.CurResultCount)
	if err != nil {
		log.Warn(ctx, "SqlResultCheck failed to update metadata, err=%v", err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return nil
}

func (selfSvc *privilegeService) UpdateCurResultCount(ctx context.Context, len int, source *shared.DataSource) error {
	_, err := selfSvc.dbwInstance.Get(ctx, source.InstanceId, source.Type.String(), source.LinkType.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		} else {
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
	}
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	if userId == "" {
		userId = tenantId
	}
	IsRootAccount, err := selfSvc.workflowDal.IsRootAccount(ctx, fwctx.GetTenantID(ctx), userId)
	if IsRootAccount {
		return nil
	}
	if err != nil {
		log.Warn(ctx, "SqlResultCheck failed to get metadata, err=%v", err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	user, err := selfSvc.dbwUser.Get(ctx, userId, tenantId)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	userMax, err := selfSvc.userSvc.GetMaxUserConfig(ctx, tenantId, userId)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	rowsAffect := int32(len)
	now := time.Now().Unix()
	if !IsSameDay(now, user.UpdatedAt) {
		err := selfSvc.dbwUser.ResetCurrentCount(ctx, 0, 0, user.UserID, user.TenantID)
		if err != nil {
			log.Warn(ctx, "Reset user %s failed:%+v", user.UserID, err.Error())
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
		user.CurExecuteCount = 0
		user.CurResultCount = 0
	}
	nextResultCount := user.CurResultCount + rowsAffect
	if nextResultCount > user.MaxResultCount && nextResultCount > userMax.MaxResultCount {
		nextResultCount = user.MaxResultCount
	}
	err = selfSvc.dbwUser.UpdateCurCount(ctx, userId, tenantId, user.CurExecuteCount, nextResultCount)
	if err != nil {
		log.Warn(ctx, "SqlResultCheck failed to update metadata, err=%v", err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return nil
}

func (selfSvc *privilegeService) ParseBySourceType(ctx context.Context, source *shared.DataSource, sql string) error {
	switch source.Type {
	case shared.MySQL, shared.VeDBMySQL, shared.MySQLSharding, shared.MetaMySQL, shared.ByteRDS:
		_, err := mysqlparse.New().ParseOneStmt(sql, "", "")
		return err
	case shared.Postgres:
		_, err := pgparse.Parse(sql)
		return err
	default:
		msg := fmt.Sprintf("Parse Command Error: Unsupported DataSourceType %s", source.Type.String())
		log.Warn(ctx, msg)
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
}

func (selfSvc *privilegeService) CheckTablePrivilegeBySession(ctx context.Context, Schema, Table string, source *shared.DataSource, Full bool, priType model.DbwPrivilegeType) string {
	_, err := selfSvc.dbwInstance.Get(ctx, source.InstanceId, source.Type.String(), source.LinkType.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		return ""
	}
	var userId string
	if fwctx.GetUserID(ctx) == "" {
		userId = fwctx.GetTenantID(ctx)
	} else {
		userId = fwctx.GetUserID(ctx)
	}
	isUpperAccount, err := selfSvc.workflowDal.IsUpperAccount(ctx, fwctx.GetTenantID(ctx), userId, source.InstanceId)
	if isUpperAccount {
		return ""
	}
	inst := selfSvc.userSvc.CheckInstancePrivilege(ctx, source.InstanceId, source.Type.String(), userId, fwctx.GetTenantID(ctx), priType)
	if inst {
		return ""
	}
	db := selfSvc.userSvc.CheckDatabasePrivilege(ctx, source.InstanceId, Schema, userId, fwctx.GetTenantID(ctx), priType, true)
	if db {
		return ""
	}
	column := parser.ColumnNode{
		Schema: Schema,
		Table:  Table,
	}
	// 如果有权限,返回为空,如果没有权限，返回对应的缺少权限的对象
	pri := selfSvc.userSvc.CheckSqlTablePrivilege(ctx, source.InstanceId, userId, fwctx.GetTenantID(ctx), column, Full, priType)
	if pri != "" {
		return pri
	}
	return ""
}

func (selfSvc *privilegeService) CheckDatabasePrivilegeBySession(ctx context.Context, Database string, source *shared.DataSource, Full bool, priType model.DbwPrivilegeType) string {
	_, err := selfSvc.dbwInstance.Get(ctx, source.InstanceId, source.Type.String(), source.LinkType.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		return ""
	}
	var userId string
	if fwctx.GetUserID(ctx) == "" {
		userId = fwctx.GetTenantID(ctx)
	} else {
		userId = fwctx.GetUserID(ctx)
	}
	isUpperAccount, err := selfSvc.workflowDal.IsUpperAccount(ctx, fwctx.GetTenantID(ctx), userId, source.InstanceId)
	if isUpperAccount {
		return ""
	}
	inst := selfSvc.userSvc.CheckInstancePrivilege(ctx, source.InstanceId, source.Type.String(), userId, fwctx.GetTenantID(ctx), priType)
	if inst {
		return ""
	}
	inst = selfSvc.userSvc.CheckInstancePrivilege(ctx, source.InstanceId, source.Type.String(), userId, fwctx.GetTenantID(ctx), model.DbwPrivilegeType_QUERY)
	if inst {
		return ""
	}
	column := parser.ColumnNode{
		Database: Database,
	}
	pri := selfSvc.userSvc.CheckSqlDatabasePrivilege(ctx, source.InstanceId, userId, fwctx.GetTenantID(ctx), column, Full, priType)
	if pri != "" {
		return pri
	}
	return ""
}

func (selfSvc *privilegeService) DatabasePrivilegeFilter(ctx context.Context, source *shared.DataSource, req *model.DescribeDatabasesReq, ret *model.DescribeDatabasesResp, Full bool, priType model.DbwPrivilegeType) (*model.DescribeDatabasesResp, error) {
	var userId string
	if fwctx.GetUserID(ctx) == "" {
		userId = fwctx.GetTenantID(ctx)
	} else {
		userId = fwctx.GetUserID(ctx)
	}
	tenantId := fwctx.GetTenantID(ctx)
	begin := (req.GetPageNumber() - 1) * req.GetPageSize()
	end := req.GetPageNumber() * req.GetPageSize()

	isUpperAccount, err := selfSvc.workflowDal.IsUpperAccount(ctx, tenantId, userId, source.InstanceId)
	inst := selfSvc.userSvc.CheckInstancePrivilege(ctx, source.InstanceId, source.Type.String(), userId, fwctx.GetTenantID(ctx), priType)
	log.Info(ctx, "is super account is %v,inst is %v,begin is %v,end is %v", isUpperAccount, inst, begin, end)
	if isUpperAccount || inst {
		if end > int32(len(ret.Items)) {
			end = int32(len(ret.Items))
		}
		ret.Items = ret.Items[begin:end]
		return ret, nil
	}

	filedRet := model.DescribeDatabasesResp{}
	for _, Item := range ret.Items {
		if selfSvc.userSvc.CheckDatabasePrivilege(ctx, source.InstanceId, Item.Name, userId, tenantId, priType, Full) {
			filedRet.Items = append(filedRet.Items, Item)
		}
	}
	if end > int32(len(filedRet.Items)) {
		end = int32(len(filedRet.Items))
	}
	log.Info(ctx, "DatabasePrivilegeFilter res: %v,%v,%v", begin, end, filedRet)
	ret.Items = filedRet.Items[begin:end]
	ret.Total = int32(len(filedRet.Items))
	return ret, err
}

func (selfSvc *privilegeService) SchemaPrivilegeFilter(ctx context.Context, req *model.DescribeSchemasReq, source *shared.DataSource, ret *model.DescribeSchemasResp, Full bool, priType model.DbwPrivilegeType) (*model.DescribeSchemasResp, error) {
	var userId string
	if fwctx.GetUserID(ctx) == "" {
		userId = fwctx.GetTenantID(ctx)
	} else {
		userId = fwctx.GetUserID(ctx)
	}
	tenantId := fwctx.GetTenantID(ctx)

	isUpperAccount, err := selfSvc.workflowDal.IsUpperAccount(ctx, tenantId, userId, source.InstanceId)
	inst := selfSvc.userSvc.CheckInstancePrivilege(ctx, source.InstanceId, source.Type.String(), userId, fwctx.GetTenantID(ctx), priType)
	db := selfSvc.userSvc.CheckDatabasePrivilege(ctx, source.InstanceId, req.GetDB(), userId, fwctx.GetTenantID(ctx), priType, true)
	if isUpperAccount || inst || db {
		return ret, nil
	}
	var priItme []string
	for _, Item := range ret.Items {
		msg := &shared.ListTable{
			Db:     req.GetDB(),
			Schema: Item,
		}
		resp, err := selfSvc.cli.KindOf(consts.SessionActorKind).Call(ctx, req.GetSessionId(), msg)
		if err != nil {
			log.Warn(ctx, "describe tables fail %v", err)
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
		tableRet := &model.DescribeTablesResp{}
		switch rsp := resp.(type) {
		case *shared.TablesInfo:
			tableRet.Total = utils.Int32Ref(int32(rsp.Total))
			tableRet.Items = rsp.Name
		default:
			log.Info(ctx, "describe tables fail %#v", resp)
			return nil, consts.ErrorOf(model.ErrorCode_DataSourceOpFail)
		}
		for _, table := range tableRet.Items {
			columnNode := parser.ColumnNode{
				Database: req.GetDB(),
				Table:    Item + "." + table,
			}
			pri := selfSvc.userSvc.CheckSqlTablePrivilege(ctx, source.InstanceId, userId, fwctx.GetTenantID(ctx), columnNode, Full, priType)
			if pri == "" {
				priItme = append(priItme, Item)
				break
			}
		}
	}
	ret.Items = priItme
	ret.Total = int32(len(ret.Items))
	return ret, err
}

func (selfSvc *privilegeService) TablePrivilegeFilter(ctx context.Context, req *model.DescribeTablesReq, source *shared.DataSource, ret *model.DescribeTablesResp, Full bool, priType model.DbwPrivilegeType) (*model.DescribeTablesResp, error) {
	var userId string
	if fwctx.GetUserID(ctx) == "" {
		userId = fwctx.GetTenantID(ctx)
	} else {
		userId = fwctx.GetUserID(ctx)
	}
	tenantId := fwctx.GetTenantID(ctx)
	begin := (req.GetPageNumber() - 1) * req.GetPageSize()
	end := req.GetPageNumber() * req.GetPageSize()

	isUpperAccount, err := selfSvc.workflowDal.IsUpperAccount(ctx, tenantId, userId, source.InstanceId)
	inst := selfSvc.userSvc.CheckInstancePrivilege(ctx, source.InstanceId, source.Type.String(), userId, fwctx.GetTenantID(ctx), priType)
	db := selfSvc.userSvc.CheckDatabasePrivilege(ctx, source.InstanceId, req.GetDB(), userId, fwctx.GetTenantID(ctx), priType, true)
	if isUpperAccount || inst || db {
		if end > int32(len(ret.Items)) {
			end = int32(len(ret.Items))
		}
		ret.Items = ret.Items[begin:end]
		return ret, nil
	}
	filedRet := model.DescribeTablesResp{}
	for _, Item := range ret.Items {
		ItemWithSchema := Item
		if source.Type == shared.Postgres {
			ItemWithSchema = req.GetSchema() + "." + Item
		}
		if selfSvc.userSvc.CheckTablePrivilege(ctx, source.InstanceId, req.GetDB(), ItemWithSchema, userId, tenantId, priType, false) {
			filedRet.Items = append(filedRet.Items, Item)
		}
	}
	if end > int32(len(filedRet.Items)) {
		end = int32(len(filedRet.Items))
	}
	log.Info(ctx, "TablePrivilegeFilter res: %v,%v,%v", begin, end, filedRet)
	ret.Items = filedRet.Items[begin:end]
	ret.Total = utils.Int32Ref(int32(len(filedRet.Items)))
	return ret, err
}

func (selfSvc *privilegeService) ExplainCommandRowsBySourceType(ctx context.Context, sql string, source *shared.DataSource, ExecutionType model.SqlExecutionType) (int32, error) {
	switch source.Type {
	case shared.MySQL, shared.VeDBMySQL, shared.MySQLSharding, shared.ByteRDS:
		return selfSvc.mysqlPri.ExplainCommandRows(ctx, source, sql, ExecutionType)
	case shared.Postgres:
		return selfSvc.postgresPri.ExplainCommandRows(ctx, source, sql, ExecutionType)
	case shared.MSSQL:
		return selfSvc.mssqlPri.ExplainCommandRows(ctx, source, sql, ExecutionType)
	}
	return 0, nil
}
