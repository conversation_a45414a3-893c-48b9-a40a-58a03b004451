package data_migration

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
)

func TestNewDataMigrationService(t *testing.T) {
	NewDataMigrationService(NewDataMigrationServiceIn{})
}

func getSvc() *dataMigrationService {
	return &dataMigrationService{
		dbwInstance:       &mocks.MockDbwInstanceDAL{},
		workflowDal:       &mocks.MockWorkflowDAL{},
		cnf:               &mocks.MockConfigProvider{},
		c3cnf:             &mocks.MockC3ConfigProvider{},
		actorClient:       &mocks.MockActorClient{},
		dsSvc:             &mocks.MockDataSourceService{},
		crossAuthSvc:      &mocks.MockCrossServiceAuthorizationService{},
		ticketService:     &mocks.MockTicketService{},
		preCheckDetailDal: &mocks.MockPreCheckDetailDAL{},
		i18nSvc:           &mocks.MockI18nServiceInterface{},
		userSvc:           &mocks.MockUserService{},
	}
}

func TestIsUserExists(t *testing.T) {
	svc := getSvc()
	ctx := context.Background()
	err := fmt.Errorf("")
	m1 := mockey.Mock((*mocks.MockWorkflowDAL).IsUserExists).Return(false, err).Build()
	svc.IsUserExists(ctx, "1", "1")
	m1.UnPatch()

	m2 := mockey.Mock((*mocks.MockWorkflowDAL).IsUserExists).Return(true, nil).Build()
	svc.IsUserExists(ctx, "1", "1")
	m2.UnPatch()
}

func TestIsInstanceAvailable(t *testing.T) {
	svc := getSvc()
	ctx := context.Background()
	err := fmt.Errorf("")
	m1 := mockey.Mock((*mocks.MockWorkflowDAL).IsInstanceAvailable).Return(false, err).Build()
	svc.IsInstanceAvailable(ctx, "1", "1")
	m1.UnPatch()

	m2 := mockey.Mock((*mocks.MockWorkflowDAL).IsInstanceAvailable).Return(true, nil).Build()
	svc.IsInstanceAvailable(ctx, "1", "1")
	m2.UnPatch()
}

func TestGetPreCheckResult(t *testing.T) {
	svc := getSvc()
	ctx := context.Background()
	err := fmt.Errorf("")

	m1 := mockey.Mock((*mocks.MockWorkflowDAL).GetPreCheckResult).Return(nil, err).Build()
	svc.GetPreCheckResult(ctx, &dao.Ticket{})
	m1.UnPatch()

	m2 := mockey.Mock((*mocks.MockWorkflowDAL).GetPreCheckResult).Return([]*dao.TicketPreCheckResult{
		{
			TicketId: 0,
			Item:     workflow.PreCheckSyntax,
			Status:   int8(model.PreCheckStatus_Error),
			Memo:     "",
		},
	}, nil).Build()
	defer m2.UnPatch()

	m3 := mockey.Mock((*mocks.MockI18nServiceInterface).GetLanguage).Return("BytePlus").Build()
	defer m3.UnPatch()
	m4 := mockey.Mock((*mocks.MockI18nServiceInterface).TranslateByConf).Return("BytePlus").Build()
	defer m4.UnPatch()
	svc.GetPreCheckResult(ctx, &dao.Ticket{})

}

func TestCreateMigrationTicket(t *testing.T) {
	svc := getSvc()
	ctx := context.Background()
	err := fmt.Errorf("")

	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()

	m1 := mockey.Mock((*dataMigrationService).GetUserRole).Return(nil, err).Build()
	svc.CreateMigrationTicket(ctx, &model.CreateMigrationTicketReq{}, &model.MigrationTicketInfo{
		TicketId: "1",
	})
	m1.UnPatch()

	m2 := mockey.Mock((*dataMigrationService).GetUserRole).Return("ADMIN", nil).Build()
	defer m2.UnPatch()

	m3 := mockey.Mock((*mocks.MockWorkflowDAL).CreateTicket).Return(err).Build()
	svc.CreateMigrationTicket(ctx, &model.CreateMigrationTicketReq{
		SqlResultExportParam: &model.SqlResultExportParam{
			SqlText: utils.StringRef("select 1;"),
		},
	}, &model.MigrationTicketInfo{
		ApprovalFlowId:     "1",
		ApprovalTemplateId: "1",
		WorkflowId:         "1",
		TicketId:           "1",
		TenantId:           "1",
		UserId:             "1",
	})
	m3.UnPatch()

}

func TestGetUserRole(t *testing.T) {
	svc := getSvc()
	ctx := context.Background()
	err := fmt.Errorf("")

	m1 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserRoles).Return(nil, err).Build()

	svc.GetUserRole(ctx, "1", "1", "1")
	m1.UnPatch()

	m2 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserRoles).Return(&[]string{
		"1", "2",
	}, err).Build()
	defer m2.UnPatch()
	svc.GetUserRole(ctx, "1", "1", "1")

}

func TestSubmitMigrationTicket(t *testing.T) {
	svc := getSvc()
	ctx := context.Background()
	err := fmt.Errorf("")

	m1 := mockey.Mock((*mocks.MockTicketService).SubmitTicket).Return(nil, err).Build()
	svc.SubmitMigrationTicket(ctx, &model.SubmitMigrationTicketReq{
		TicketId: "1",
	})
	m1.UnPatch()

	m2 := mockey.Mock((*mocks.MockTicketService).SubmitTicket).Return(&model.SubmitTicketResp{
		Code:   0,
		ErrMsg: "",
	}, nil).Build()
	defer m2.UnPatch()
	svc.SubmitMigrationTicket(ctx, &model.SubmitMigrationTicketReq{
		TicketId: "1",
	})

}

func TestCheckSqlText(t *testing.T) {
	svc := getSvc()
	ctx := context.Background()
	//err := fmt.Errorf("")
	m1 := mockey.Mock((*dataMigrationService).ExplainSQL).Return(nil).Build()
	defer m1.UnPatch()
	svc.checkSqlText(ctx, &dao.Ticket{
		InstanceType: model.InstanceType_MySQL.String(),
		SqlText:      "select 1;",
	})

	svc.checkSqlText(ctx, &dao.Ticket{
		InstanceType: model.InstanceType_Postgres.String(),
		SqlText:      "select 1;",
	})

	svc.checkSqlText(ctx, &dao.Ticket{
		InstanceType: "test",
		SqlText:      "select 1;",
	})
}

func TestDescribeMigrationTicketDetail(t *testing.T) {
	svc := getSvc()
	ctx := context.Background()
	err := fmt.Errorf("")

	m1 := mockey.Mock((*mocks.MockTicketService).DescribeTicketDetail).Return(nil, err).Build()
	svc.DescribeMigrationTicketDetail(ctx, &model.DescribeMigrationTicketDetailReq{
		TicketId: "1",
	})
	m1.UnPatch()

	m2 := mockey.Mock((*mocks.MockTicketService).DescribeTicketDetail).Return(&model.DescribeTicketDetailResp{
		TicketConfig: "{}",
	}, nil).Build()
	defer m2.UnPatch()
	svc.DescribeMigrationTicketDetail(ctx, &model.DescribeMigrationTicketDetailReq{
		TicketId: "1",
	})
}

func TestDescribeMigrationTickets(t *testing.T) {
	svc := getSvc()
	ctx := context.Background()
	err := fmt.Errorf("")
	m1 := mockey.Mock((*mocks.MockTicketService).DescribeTickets).Return(nil, err).Build()
	svc.DescribeMigrationTickets(ctx, &model.DescribeMigrationTicketsReq{})
	m1.UnPatch()

	m2 := mockey.Mock((*mocks.MockTicketService).DescribeTickets).Return(nil, nil).Build()
	svc.DescribeMigrationTickets(ctx, &model.DescribeMigrationTicketsReq{})
	m2.UnPatch()

	m3 := mockey.Mock((*mocks.MockTicketService).DescribeTickets).Return(&model.DescribeTicketsResp{
		Tickets: []*model.TicketItem{{}},
		Total:   0,
	}, nil).Build()
	svc.DescribeMigrationTickets(ctx, &model.DescribeMigrationTicketsReq{})
	m3.UnPatch()

}

func TestDescribeMigrationPreCheckDetail(t *testing.T) {
	service := &dataMigrationService{ticketService: &mocks.MockTicketService{}}

	mockPreCheckDetail := &model.DescribePreCheckDetailResp{
		PreCheckDetails: []*model.PreCheckDetail{{
			PreCheckItem: model.PreCheckItem_Syntax,
			ItemDetails:  []*model.ItemDetail{{SQL: "", Result_: "aaa"}},
		}, {
			PreCheckItem: model.PreCheckItem_Permission,
			ItemDetails:  []*model.ItemDetail{{SQL: "", Result_: "bbb"}},
		}},
	}

	mock1 := mockey.Mock((*mocks.MockTicketService).DescribePreCheckDetail).Return(mockPreCheckDetail, nil).Build()
	defer mock1.UnPatch()

	_, _ = service.DescribeMigrationPreCheckDetail(context.Background(), &model.DescribeMigrationPreCheckDetailReq{TicketId: "1"})
}

func TestCheckPerMission(t *testing.T) {
	ctx := context.Background()
	svc := getSvc()
	m1 := mockey.Mock((*mocks.MockWorkflowDAL).IsUpperAccount).Return(false, nil).Build()
	defer m1.UnPatch()
	m2 := mockey.Mock((*mocks.MockUserService).CheckInstancePrivilege).Return(false).Build()
	defer m2.UnPatch()
	m3 := mockey.Mock((*mocks.MockUserService).CheckDatabasePrivilege).Return(false).Build()
	defer m3.UnPatch()
	svc.CheckPermission(ctx, &dao.Ticket{TicketType: int8(model.TicketType_DataMigrationExportDB)})
	svc.CheckPermission(ctx, &dao.Ticket{TicketType: int8(model.TicketType_DataMigrationImport)})

}

//func TestPreCheckMigrationTicket(t *testing.T) {
//	svc := getSvc()
//	ctx := context.Background()
//	err := fmt.Errorf("")
//
//	m1 := mockey.Mock((*dataMigrationService).GetTicket).Return(&dao.Ticket{
//		TicketId:     1,
//		TicketStatus: int8(model.TicketStatus_TicketUndo),
//		SqlText:      "",
//	}, nil).Build()
//	m2 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(nil).Build()
//	m3 := mockey.Mock((*mocks.MockPreCheckDetailDAL).CreatePreCheckDetail).Return(nil).Build()
//	m4 := mockey.Mock((*mocks.MockWorkflowDAL).ReplaceIntoPreCheckResult).Return(nil).Build()
//	m5 := mockey.Mock((*mocks.MockTicketService).ChangePreCheckTicketStatusAndOperator).Return(nil).Build()
//	svc.PreCheckMigrationTicket(ctx, &model.PreCheckMigrationTicketReq{
//		TicketId: "1",
//	})
//	m1.UnPatch()
//
//	m11 := mockey.Mock((*dataMigrationService).GetTicket).Return(&dao.Ticket{
//		TicketId:     1,
//		TicketStatus: int8(model.TicketStatus_TicketUndo),
//		SqlText:      "update t set id=1",
//	}, nil).Build()
//	m12 := mockey.Mock((*dataMigrationService).checkSqlText).Return(err).Build()
//	m13 := mockey.Mock((*mocks.MockTicketService).ChangeTicketStatus).Return(nil).Build()
//	svc.PreCheckMigrationTicket(ctx, &model.PreCheckMigrationTicketReq{
//		TicketId: "1",
//	})
//	m11.UnPatch()
//	m12.UnPatch()
//	m13.UnPatch()
//
//	m111 := mockey.Mock((*dataMigrationService).GetTicket).Return(&dao.Ticket{
//		TicketId:     1,
//		TicketStatus: int8(model.TicketStatus_TicketUndo),
//		SqlText:      "update t set id=1",
//	}, nil).Build()
//	m121 := mockey.Mock((*dataMigrationService).checkSqlText).Return(nil).Build()
//	svc.PreCheckMigrationTicket(ctx, &model.PreCheckMigrationTicketReq{
//		TicketId: "1",
//	})
//	m111.UnPatch()
//	m121.UnPatch()
//
//	m2.UnPatch()
//	m3.UnPatch()
//	m4.UnPatch()
//	m5.UnPatch()
//}
