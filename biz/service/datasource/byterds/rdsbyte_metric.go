package byterds

import (
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"fmt"
	"math"
	"net"
	"os"
	"sort"
	"strings"
	"sync"
	"time"

	mq "code.byted.org/inf/metrics-query"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/slowquery"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/qjpcpu/fp"
)

const (
	percent  = "%"
	noUnit   = ""
	Byte     = "B"
	MegaByte = "MiB"
	Gibibyte = "GiB" // 1024

	CpuUsage                       = "CPU使用率"
	MemUsage                       = "内存使用率"
	DiskUsage                      = "磁盘使用率"
	innodbBufferPoolUsage          = "innodbBufferPool使用率"
	innodbBufferPoolHitRatio       = "innodbBufferPool命中率"
	innodbBufferPoolDirtyPageRatio = "innodbBufferPool脏页比率"

	avgCpuUsage     = "平均CPU使用率"
	maxCpuUsage     = "最大CPU使用率"
	minCpuUsage     = "最小CPU使用率"
	avgMemUsage     = "平均内存使用率"
	minMemUsage     = "最小内存使用率"
	maxMemUsage     = "最大内存使用率"
	avgDiskUsage    = "平均磁盘使用率"
	maxDiskUsage    = "最大磁盘使用率"
	minDiskUsage    = "最小磁盘使用率"
	avgDiskCapacity = "平均磁盘使用量"
	avgSlowQueries  = "平均慢SQL查询数"

	//maxDiskCapacity    = "最大磁盘使用量"
	//minDiskCapacity    = "最小磁盘使用量"
	avgQpsUsage        = "平均QPS"
	avgTpsUsage        = "平均TPS"
	avgConnectionUsage = "平均连接数使用率"
	maxConnectionUsage = "最大连接数使用率"
	minConnectionUsage = "最小连接数使用率"
	avgSessionUsage    = "平均活跃会话数"
	maxSessionUsage    = "最大活跃会话数"
	minSessionUsage    = "最小活跃会话数"

	// 磁盘详情
	binlogSize   = "binlog大小"
	relayLogSize = "relaylog大小"
	redoLogSize  = "redolog大小"
	undoLogSize  = "undolog大小"

	Connected      = "当前打开连接数"
	connRunning    = "活跃会话数"
	ConnectedRatio = "连接数使用率"
)

func (m *byterdsImpl) initHealthSummary() {
	// 初始化
	m.HealthSummaryMetric = make(map[string]func(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error))
	m.HealthSummaryMetric["cpuUsage"] = m.GetHealthSummaryCpu
	m.HealthSummaryMetric["memUsage"] = m.GetHealthSummaryMem
	m.HealthSummaryMetric["connectionUsage"] = m.GetHealthSummaryConnection
	m.HealthSummaryMetric["sessionUsage"] = m.GetHealthSummarySession
	m.HealthSummaryMetric["tpsUsage"] = m.GetHealthSummaryTps
	m.HealthSummaryMetric["qpsUsage_delete"] = m.GetHealthSummaryQps_delete
	m.HealthSummaryMetric["qpsUsage_select"] = m.GetHealthSummaryQps_select
	m.HealthSummaryMetric["qpsUsage_update"] = m.GetHealthSummaryQps_update
	m.HealthSummaryMetric["qpsUsage_insert"] = m.GetHealthSummaryQps_insert
	m.HealthSummaryMetric["innodb_rows_read"] = m.GetHealthSummaryInnodb_rows_read

	m.HealthSummaryMetric["slowQuery"] = m.GetHealthSummarySlowQuery
	//m.HealthSummaryMetric["BPHit"] = m.GetHealthSummaryBPHit
	m.HealthSummaryMetric["connected"] = m.GetHealthSummaryConnected
}

func (m *byterdsImpl) HealthSummary(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.Resource, error) {
	log.Info(ctx, fmt.Sprintf("DescribeHealthSummary-3-4 is %v", req))
	m.initHealthSummary()
	log.Info(ctx, fmt.Sprintf("DescribeHealthSummary-3-5 is %v", req))
	var resources []*model.Resource
	var lock sync.Mutex
	waitCh := make(chan struct{})
	wg := sync.WaitGroup{}
	go func() {
		for k, v := range m.HealthSummaryMetric {
			fn := v
			wg.Add(1)
			go func() {
				defer wg.Done()
				item, err := fn(ctx, req)
				if err != nil {
					log.Warn(ctx, fmt.Sprintf("Diag Metric %s is err: %s", k, err.Error()))
				}
				lock.Lock()
				defer lock.Unlock()
				resources = append(resources, item)
				// 按照字典序排序
				fp.StreamOf(resources).SortBy(func(a, b *model.Resource) bool {
					return a.Name < b.Name
				}).ToSlice(&resources)
			}()
		}
		wg.Wait()
		close(waitCh)
	}()

	select {
	case <-waitCh:
		return resources, nil
	}
}

func (m *byterdsImpl) GetAvgCpuUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	return m.GetCpuUsage(ctx, req)
}

func (m *byterdsImpl) GetMaxCpuUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	return m.GetCpuUsage(ctx, req)
}

func (m *byterdsImpl) GetCpuUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	log.Info(ctx, "DescribeDBDiagnosis-GetAvgCpuUsage-req:%s", req)
	metric := "inf.mysql.server.cpu"
	metricName := "inf.mysql.server.cpu"
	multivalue := "usage_idle"
	ret, err := m.GetUsageByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetAvgCpuUsage-err %s", err)
	}
	ret.Name = avgCpuUsage
	return ret, nil
}

func (m *byterdsImpl) GetAvgMemUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	return m.GetMemUsage(ctx, req)
}

func (m *byterdsImpl) GetMaxMemUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	return m.GetMemUsage(ctx, req)
}

func (m *byterdsImpl) GetMemUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	log.Info(ctx, "DescribeDBDiagnosis-GetAvgMemUsage req:%s", req)
	metric := "inf.mysql.server.mysql_system"
	metricName := "inf.mysql.server.mysql_system"
	multivalue := "mem_free_percent"
	ret, err := m.GetUsageByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetAvgMemUsage-err %s", err)
	}
	ret.Name = avgMemUsage
	return ret, nil
}

func (m *byterdsImpl) GetAvgConnectionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	return m.GetConnectionUsage(ctx, req)
}

func (m *byterdsImpl) GetMaxConnectionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	return m.GetConnectionUsage(ctx, req)
}

func (m *byterdsImpl) GetConnectionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	log.Info(ctx, "DescribeDBDiagnosis-GetAvgConnectionUsage req:%s", req)
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "ConnectionUsage"
	multivalue := "threads_connected"
	ret, err := m.GetUsageByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetAvgConnectionUsage-err %s", err)
	}
	if ret == nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetAvgConnectionUsage-ret-nil-err %s", err)
	}
	ret.Avg = ret.Avg * 100
	ret.Max = ret.Max * 100
	ret.Min = ret.Min * 100
	ret.Name = avgConnectionUsage
	return ret, nil
}

func (m *byterdsImpl) GetAvgDiskUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	return m.GetDiskUsage(ctx, req)
}

func (m *byterdsImpl) GetMaxDiskUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	return m.GetDiskUsage(ctx, req)
}

func (m *byterdsImpl) GetDiskUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	log.Info(ctx, "DescribeDBDiagnosis-GetAvgDiskUsage req:%s", req)
	/*metric := "inf.mysql.server.disk"
	metricName := "inf.mysql.server.disk"
	multivalue := "used_percent"*/
	metric := "inf.mysql.server.mysql_system"
	metricName := "inf.mysql.server.mysql_system"
	multivalue := "data_used_percent"
	ret, err := m.GetUsageByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetAvgDiskUsage-err %s", err)
	}
	ret.Name = avgDiskUsage
	return ret, nil
}

func (m *byterdsImpl) GetAvgSlowQueries(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	log.Info(ctx, "DescribeDBDiagnosis-GetAvgSlowQueries req:%s", req)
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue := "slow_queries"
	ret, err := m.GetUsageByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetAvgSlowQueries-err %s", err)
	}
	ret.Name = avgSlowQueries
	return ret, nil
}

func (m *byterdsImpl) GetUsageByteCloud(ctx context.Context,
	req *datasource.GetMetricUsageReq, metric string, metricName string, multivalue string) (*datasource.GetMetricUsageResp, error) {
	log.Info(ctx, fmt.Sprintf("DescribeHealthSummary-3-8-GetMetricList is InstanceId: %v,regionId: %v, metric: %s,multivalue: %s", req.InstanceId, req.RegionId, metric, multivalue))
	result, err := m.GetDataPointByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		return nil, err
	}
	avgValue := getAverageValue(result)
	minValue := getMinValue(result)
	maxValue := getMaxValue(result)
	log.Info(ctx, fmt.Sprintf("DescribeHealthSummary-3-8-GetMetricList is InstanceId: %v,regionId: %v, metric: %s,multivalue: %s", req.InstanceId, req.RegionId, metric, multivalue))

	return &datasource.GetMetricUsageResp{
		Avg:  avgValue,
		Min:  minValue,
		Max:  maxValue,
		Name: multivalue,
		Unit: percent,
	}, nil
}

func (m *byterdsImpl) GetDataPointCountSlowQueries(ctx context.Context, req *datasource.GetMetricUsageReq) (int, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue := "slow_queries"
	ret, err := m.GetDataPointCountByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetDataPointCountSlowQueries-err %s", err)
		fmt.Println("Error converting string to int64:", err)
	}
	return ret, nil
}

func (m *byterdsImpl) GetDataPointCountByteCloud(ctx context.Context,
	req *datasource.GetMetricUsageReq, metric string, metricName, multivalue string) (int, error) {
	result, err := m.GetDataPointByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		return 0, err
	}

	// 检查 firstItem 是否为空
	if len(result) == 0 {
		log.Warn(ctx, "No data points found")
		return 0, fmt.Errorf("no data points found")
	}

	// 计算最后一个点的值减去第一个点的值
	var sum float64 // 假设元素的 Value 是 float64 类型
	for _, item := range result {
		sum += item.Value
	}
	return int(sum), nil
}

func (m *byterdsImpl) GetDataPointByteCloud(ctx context.Context,
	req *datasource.GetMetricUsageReq, metric string, metricName, multivalue string) ([]*model.DataPoint, error) {
	addr := req.NodeIds[0]

	result := m.GetMetricList(
		ctx, req.InstanceId, req.RegionId, metric, metricName, multivalue, addr,
		"", "", req.StartTime, req.EndTime, req.Intervals,
	)
	_, ok := result[multivalue]

	// 检查 firstItem 是否为空
	if result == nil || !ok {
		log.Warn(ctx, "No data points found")
		return nil, fmt.Errorf("no data points found")
	}

	return result[multivalue], nil
}

// GetHealthSummaryCpu 健康概要 cpu使用率
func (m *byterdsImpl) GetHealthSummaryCpu(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	log.Info(ctx, fmt.Sprintf("DescribeHealthSummary-3-6 is %v", req))
	metric := "inf.mysql.server.cpu"
	metricName := "inf.mysql.server.cpu"
	multivalue := "usage_idle"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryCpu-err %s", err)
	}
	ret.Unit = percent
	ret.Name = "CPU利用率"
	// 保留两位小数
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummaryMem(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_system"
	metricName := "inf.mysql.server.mysql_system"
	multivalue := "mem_free_percent"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryMem-err %s", err)
	}
	ret.Unit = percent
	ret.Name = "内存利用率"
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummaryConnection(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "ConnectionUsage"
	multivalue := "threads_connected"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryConnection-err %s", err)
	}
	ret.Unit = percent
	ret.Name = "链接利用率"
	coefficient := 100.00
	roundResourceFields(ret, coefficient)
	ret.YoY = roundToTwoDecimals2(ret.YoY / coefficient)
	ret.MoM = roundToTwoDecimals2(ret.MoM / coefficient)
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummarySession(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue := "threads_running"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummarySession-err %s", err)
	}
	ret.Name = "活跃会话数量"
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummaryTps(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue := "com_commit"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryTps-err %s", err)
	}
	ret.Name = "TPS"
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	return ret, nil
}

/*func (m *byterdsImpl) GetHealthSummaryQps222(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue_com_select := "com_select"
	ret, _ := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_select)
	multivalue_com_insert := "com_insert"
	ret, _ = m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_insert)
	multivalue_com_update := "com_update"
	ret, _ = m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_update)
	multivalue_com_delete := "com_delete"
	ret, _ = m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_delete)
	return ret, nil
}*/

/*func (m *byterdsImpl) GetHealthSummaryQps_pral(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	var wg sync.WaitGroup
	results := make([]*model.Resource, 4) // 假设有 4 个查询
	errors := make(chan error, 4)         // 用于捕获错误

	metrics := []string{"com_select", "com_insert", "com_update", "com_delete"}
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"

	// 遍历每个 multivalue 并启动 goroutine
	for i, multivalue := range metrics {
		wg.Add(1) // 增加 WaitGroup 计数器
		go func(index int, mv string) {
			defer wg.Done() // 确保任务完成时减少计数器

			// 调用 GetHealthSummaryByteCloud
			ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, mv)
			if err != nil {
				errors <- err // 发送错误到通道
				return
			}
			results[index] = ret // 保存结果到对应的索引
		}(i, multivalue)
	}

	// 等待所有 goroutine 完成
	wg.Wait()
	close(errors) // 关闭错误通道

	// 检查是否有错误
	for err := range errors {
		if err != nil {
			return nil, err // 如果有错误，直接返回
		}
	}
	// 合并 DataPoints
	timeStampMap := make(map[int32]float64) // 用于存储 TimeStamp -> Value 累加
	for _, resource := range results {
		if resource == nil {
			continue
		}
		for _, dp := range resource.DataPoints {
			timeStampMap[dp.TimeStamp] += dp.Value
		}
	}

	// 构造新的 DataPoints 切片
	var mergedDataPoints []*model.DataPoint
	for ts, value := range timeStampMap {
		mergedDataPoints = append(mergedDataPoints, &model.DataPoint{
			TimeStamp: ts,
			Value:     value,
			Abnormal:  0, // 可根据需要设置默认值或特殊逻辑
		})
	}

	// 返回新的 Resource
	mergedResource := &model.Resource{
		Name:       "MergedResource", // 可根据实际需要设置名称
		Max:        0,                // 可根据实际需要计算最大值
		Min:        0,                // 可根据实际需要计算最小值
		Avg:        0,                // 可根据实际需要计算平均值
		Unit:       "qps",            // 示例单位，可修改
		DataPoints: mergedDataPoints,
		YoY:        0, // 可根据实际需要设置
		MoM:        0, // 可根据实际需要设置
	}

	return mergedResource, nil
	//return results[0], nil
}
*/
// roundResourceFields 保留 Resource 结构体中的所有浮点字段两位小数
func roundResourceFields(resource *model.Resource, coefficient float64) {

	if resource == nil {
		return
	}

	// 保留 Resource 顶层字段的两位小数
	resource.Max = roundToTwoDecimals2(resource.Max * coefficient)
	resource.Min = roundToTwoDecimals2(resource.Min * coefficient)
	resource.Avg = roundToTwoDecimals2(resource.Avg * coefficient)
	resource.YoY = roundToTwoDecimals2(resource.YoY * coefficient)
	resource.MoM = roundToTwoDecimals2(resource.MoM * coefficient)

	// 遍历 DataPoints 并处理 Value
	for _, dp := range resource.DataPoints {
		dp.Value = roundToTwoDecimals2(dp.Value * coefficient)
	}
}

// 保留两位小数
func roundToTwoDecimals2(value float64) float64 {
	return math.Round(value*100) / 100
}

func (m *byterdsImpl) GetHealthSummaryQps_select(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue_com_select := "com_select"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_select)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryQps_select-err %s", err)
	}
	// 保留两位小数
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	ret.Name = "com_select/s"
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummaryQps_insert(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue_com_insert := "com_insert"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_insert)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryQps_insert-err %s", err)
	}
	// 保留两位小数
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	ret.Name = "com_insert/s"
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummaryInnodb_rows_read(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue_com_insert := "innodb_rows_read"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_insert)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryInnodb_rows_read-err %s", err)
	}
	// 保留两位小数
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	ret.Name = "innodb_rows_read/s"
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummaryQps_update(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue_com_update := "com_update"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_update)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryQps_update-err %s", err)
	}
	// 保留两位小数
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	ret.Name = "com_update/s"
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummaryQps_delete(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue_com_delete := "com_delete"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_delete)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryQps_delete-err %s", err)
	}
	// 保留两位小数
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	ret.Name = "com_delete/s"
	return ret, nil
}

/*func (m *byterdsImpl) GetHealthSummaryQps(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "qps"
	multivalue_com_select := "com_select"
	ret, _ := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_select)
	multivalue_com_insert := "com_insert"
	ret, _ = m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_insert)
	multivalue_com_update := "com_update"
	ret, _ = m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_update)
	multivalue_com_delete := "com_delete"
	ret, _ = m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue_com_delete)
	return ret, nil
}*/

func (m *byterdsImpl) GetHealthSummarySlowQuery(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue := "slow_queries"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummarySlowQuery-err %s", err)
	}
	// 保留两位小数
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	ret.Name = "慢查询数量/30s"
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummaryBPHit(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status_core"
	metricName := "inf.mysql.server.mysql_global_status_core"
	//"innodb_buffer_pool_read_requests,innodb_buffer_pool_reads"
	multivalue := "innodb_buffer_pool_reads"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryBPHit-err %s", err)
		return nil, err
	}
	// 保留两位小数

	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	ret.Name = "BP Hit命中率"
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummaryConnected(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error) {
	metric := "inf.mysql.server.mysql_global_status"
	metricName := "inf.mysql.server.mysql_global_status"
	multivalue := "threads_connected"
	ret, err := m.GetHealthSummaryByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryConnected-err %s", err)
	}
	// 保留两位小数
	coefficient := 1.00
	roundResourceFields(ret, coefficient)
	ret.Name = "当前链接数量"
	return ret, nil
}

func (m *byterdsImpl) GetHealthSummaryByteCloud(ctx context.Context,
	req *datasource.GetMetricUsageReq, metric string, metricName string, multivalue string,
) (ret *model.Resource, err error) {
	ret = &model.Resource{
		Name: multivalue,
		Unit: noUnit,
	}
	log.Info(ctx, fmt.Sprintf("DescribeHealthSummary-3-8 is req: %v,metric: %s,multivalue: %s", req, metric, multivalue))

	var (
		firstItem, firstItemYoY, firstItemMoM, firstItemresultperiod []*model.DataPoint
		YoYReq, MoMReq, periodReq                                    *datasource.GetMetricUsageReq
	)
	firstItem, err = m.GetDataPointByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		return
	}

	//同比(前一天)
	YoYReq, err = m.TimeConvertion(ctx, req, "YoY", 60*24)
	if err != nil {
		return
	}

	//YoY
	firstItemYoY, err = m.GetDataPointByteCloud(ctx, YoYReq, metric, metricName, multivalue)
	if err != nil {
		return
	}

	//MoM
	//环比(上一个小时)
	MoMReq, err = m.TimeConvertion(ctx, req, "MoM", 60)
	if err != nil {
		return
	}
	firstItemMoM, err = m.GetDataPointByteCloud(ctx, MoMReq, metric, metricName, multivalue)
	if err != nil {
		return
	}

	periodReq, err = m.TimeConvertion(ctx, req, "PERIOD", 60)
	if err != nil {
		return
	}

	firstItemresultperiod, err = m.GetDataPointByteCloud(
		ctx, periodReq, metric, metricName, multivalue,
	)
	if err != nil {
		return
	}

	dataPointsPeriod := convertToDataPoints(firstItemresultperiod)

	// 计算最大值、最小值、平均值
	log.Info(ctx, fmt.Sprintf("DescribeHealthSummary-3-8 is firstItem: %v", len(firstItem)))

	log.Info(ctx, fmt.Sprintf("DescribeHealthSummary-3-8 is firstItemresultperiod: %v", len(firstItemresultperiod)))
	maxValue := getMaxValue(firstItem)
	minValue := getMinValue(firstItem)
	avgValue := getAverageValue(firstItem)
	avgValueYoY := getAverageValue(firstItemYoY)
	avgValueMoM := getAverageValue(firstItemMoM)

	yoy, mom := m.calculateYoYMoM(avgValue, avgValueYoY, avgValueMoM)
	ret.Max = maxValue
	ret.Min = minValue
	ret.Avg = avgValue
	ret.YoY = yoy
	ret.MoM = mom
	ret.DataPoints = dataPointsPeriod
	return ret, nil
}

func (m *byterdsImpl) calculateYoYMoM(avgSession, avgSessionYoY, avgSessionMoM float64) (float64, float64) {
	var yoy, mom float64

	if avgSessionYoY == 0 {
		yoy = -2
	} else {
		yoy = (avgSession - avgSessionYoY) / avgSessionYoY
	}

	if avgSessionMoM == 0 {
		mom = -2
	} else {
		mom = (avgSession - avgSessionMoM) / avgSessionMoM
	}

	// 保留两位小数
	yoy = math.Round(yoy*100*100) / 100
	mom = math.Round(mom*100*100) / 100

	return yoy, mom
}

func (m *byterdsImpl) GetMetricList(ctx context.Context, InstanceId string, regionId string, metric string,
	metricName string, multivalue string, ipaddress string, appName string, appSecret string, startT, endT time.Time, intervals int32) map[string][]*model.DataPoint {
	start := startT.Unix()
	end := endT.Unix()
	log.Info(ctx, fmt.Sprintf("DescribeHealthSummary-3-8-GetMetricList is InstanceId: %v,regionId: %v, metric:"+
		" %s,multivalue: %s, start %d, end %d", InstanceId, regionId, metric, multivalue, start, end))

	appName = os.Getenv("PSM")
	appSecret = os.Getenv("METRICS_SECRET")
	if appName == "" {
		appName = "dbw.bytecloud.mgr"
	}
	if appSecret == "" {
		appSecret = "60d677b7-398c-46a8-b9a7-7212a7201875"
	}
	// 初始化客户端
	client, err := mq.NewClient(appName, appSecret,
		mq.WithPeriodicTokenUpdate,                       // 自动更新 token
		mq.WithCluster(convertRegionToCluster(regionId)), // 控制面，与 Region 对应
	)
	if err != nil {
		log.Warn(ctx, "init byteMetric client failed %s", err)
		return map[string][]*model.DataPoint{}
	}
	defer client.Close()
	ipaddressNew, _, err := net.SplitHostPort(ipaddress)
	if err != nil {
		log.Warn(ctx, "SplitHostPort failed %s", err)
		return nil
	}

	// 构建查询
	interval := 30
	if intervals > 30 { // metric interval 只支持30s以上
		interval = int(intervals)
	}
	query := mq.NewQuery(mq.AggregatorAvg, metric).
		SetTenant("storage.mysql").
		SetDownsample(time.Duration(interval)*time.Second, mq.DownsampleAvg). // 设置降采样
		//SetTag("region", regionId).                                           // 设置 region 标签
		SetTag("dbname", InstanceId). // 设置实例名
		SetTag("ip", ipaddressNew).
		//SetMultivalue(multivalue). // 查询 usage_idle 指标
		SetMultivalue(multivalue). // 查询 usage_idle 指标
		SetTopK(1, mq.TopKMax)     // 仅获取 Top 1 数据

	// 创建请求，设置时间范围
	result := client.NewRequest(convertRegionToVRegion(regionId)).
		SetIntervalBetweenUnix(start, end).
		AddQuery(query).
		DoWithContext(ctx)

	// 检查查询结果
	if err := result.Err(); err != nil {
		log.Warn(ctx, "get cpu usage failed %s", err)
		return map[string][]*model.DataPoint{}
	}
	resultMap := make(map[string][]*model.DataPoint)

	// 处理返回的结果
	resp := result.First()
	if len(resp.Curves()) == 0 {
		// 如果没有曲线数据，返回空切片
		resultMap[multivalue] = []*model.DataPoint{}
		return resultMap
	}
	for _, curve := range resp.Curves() {
		// 创建一个结构体切片，将 TimeStamps 和 Values 对应起来
		type timestampValue struct {
			TimeStamp int64
			Value     float64
		}
		tvSlice := make([]timestampValue, len(curve.Timestamps))
		for i := range curve.Timestamps {
			tvSlice[i] = timestampValue{
				TimeStamp: curve.Timestamps[i],
				Value:     curve.Values[i],
			}
		}

		// 对 tvSlice 按照 Timestamps 升序排序
		sort.Slice(tvSlice, func(i, j int) bool {
			return tvSlice[i].TimeStamp < tvSlice[j].TimeStamp
		})

		// 将排序后的数据重新赋值回 curve.TimeStamps 和 curve.Values
		for i := range tvSlice {
			curve.Timestamps[i] = tvSlice[i].TimeStamp
			curve.Values[i] = tvSlice[i].Value
		}

		dataPoints := make([]*model.DataPoint, len(curve.Timestamps))
		var maxConnectionsDataPoints float64 = -1 // 默认值为 -1
		if metricName == "ConnectionUsage" {
			metricMaxConnection := "inf.mysql.server.mysql_global_variables"
			multivalueMaxConnection := "max_connections"
			result := m.GetLastestMetricList(ctx, InstanceId, regionId, metricMaxConnection, multivalueMaxConnection,
				ipaddress, appName, appSecret, start, end)
			// 检查 result 是否不为空且包含 "max_connections"
			if result != nil {
				if maxConnections, ok := result["max_connections"]; ok {
					maxConnectionsDataPoints = maxConnections // 赋值给 maxConnectionsDataPoints
				} else {
					log.Warn(ctx, "result does not contain max_connections")
				}
			} else {
				log.Warn(ctx, "result is nil")
			}
		}

		for i := range curve.Timestamps {
			if multivalue == "usage_idle" {
				dataPoints[i] = &model.DataPoint{
					TimeStamp: int32(curve.Timestamps[i]),
					Value:     100 - curve.Values[i],
				}
			} else if multivalue == "com_commit" || multivalue == "com_select" || multivalue == "com_update" ||
				multivalue == "com_insert" || multivalue == "com_delete" || multivalue == "slow_queries" || multivalue == "innodb_rows_read" {
				if i == 0 {
					// For the first data point, set Value to 0 as we cannot compute the rate
					dataPoints[i] = &model.DataPoint{
						TimeStamp: int32(curve.Timestamps[i]),
						Value:     0,
					}
				} else {
					deltaValue := curve.Values[i] - curve.Values[i-1]
					//deltaTime := float64(curve.TimeStamps[i]-curve.TimeStamps[i-1]) / 1000.0 // Assuming timestamps are in milliseconds
					if deltaValue >= 0 {
						//tps//qps
						if multivalue == "com_commit" || multivalue == "com_select" || multivalue == "com_update" ||
							multivalue == "com_insert" || multivalue == "com_delete" || multivalue == "innodb_rows_read" {
							rate := deltaValue / float64(interval)
							dataPoints[i] = &model.DataPoint{
								TimeStamp: int32(curve.Timestamps[i]),
								Value:     rate,
							}
						} else if multivalue == "slow_queries" {
							dataPoints[i] = &model.DataPoint{
								TimeStamp: int32(curve.Timestamps[i]),
								Value:     deltaValue,
							}
						}
					} else {
						// Handle counter reset or invalid data
						dataPoints[i] = &model.DataPoint{
							TimeStamp: int32(curve.Timestamps[i]),
							Value:     0,
						}
					}
				}
			} else {
				if metricName == "ConnectionUsage" {
					if maxConnectionsDataPoints > 0 {
						rate := curve.Values[i] / float64(maxConnectionsDataPoints)
						dataPoints[i] = &model.DataPoint{
							TimeStamp: int32(curve.Timestamps[i]),
							Value:     rate,
						}
					}
				} else {
					dataPoints[i] = &model.DataPoint{
						TimeStamp: int32(curve.Timestamps[i]),
						Value:     curve.Values[i],
					}
				}
			}
		}
		// 对数据副本按 Timestamps 升序排序
		sort.Slice(dataPoints, func(i, j int) bool {
			return dataPoints[i].TimeStamp < dataPoints[j].TimeStamp
		})
		resultMap[multivalue] = dataPoints
	}

	return resultMap
}

func (m *byterdsImpl) GetMultivalueMetricList(ctx context.Context, InstanceId string, regionId string, metric string, multivalue string,
	ipaddress string, appName string, appSecret string, startT, endT time.Time) map[string][]*model.DataPoint {
	start := startT.Unix()
	end := endT.Unix()
	log.Info(ctx, fmt.Sprintf("DescribeHealthSummary-3-8-GetMetricList is InstanceId: %v,regionId: %v, metric: %s,multivalue: %s", InstanceId, regionId, metric, multivalue))
	// 初始化客户端
	appName = os.Getenv("PSM")
	appSecret = os.Getenv("METRICS_SECRET")
	if appName == "" {
		appName = "dbw.bytecloud.mgr"
	}
	if appSecret == "" {
		appSecret = "60d677b7-398c-46a8-b9a7-7212a7201875"
	}
	client, err := mq.NewClient(appName, appSecret,
		mq.WithPeriodicTokenUpdate,                       // 自动更新 token
		mq.WithCluster(convertRegionToCluster(regionId)), // 控制面，与 Region 对应
	)
	if err != nil {
		log.Warn(ctx, "init byteMetric client failed %s", err)
		return map[string][]*model.DataPoint{}
	}
	defer client.Close()

	ipaddressNew, _, err := net.SplitHostPort(ipaddress)
	if err != nil {
		log.Warn(ctx, "SplitHostPort failed %s", err)
		return nil
	}

	// 构建查询
	interval := 30
	query := mq.NewQuery(mq.AggregatorAvg, metric).
		SetTenant("storage.mysql").
		SetDownsample(time.Duration(interval)*time.Second, mq.DownsampleAvg). // 设置降采样
		//SetTag("region", regionId).                                           // 设置 region 标签
		SetTag("dbname", InstanceId). // 设置实例名
		SetTag("ip", ipaddressNew).
		//SetMultivalue(multivalue). // 查询 usage_idle 指标
		SetMultivalue(multivalue). // 查询 usage_idle 指标
		SetTopK(1, mq.TopKMax)     // 仅获取 Top 1 数据

	// 创建请求，设置时间范围
	result := client.NewRequest(convertRegionToVRegion(regionId)).
		SetIntervalBetweenUnix(start, end).
		AddQuery(query).
		DoWithContext(ctx)

	// 检查查询结果
	if err := result.Err(); err != nil {
		log.Warn(ctx, "get cpu usage failed %s", err)
		return map[string][]*model.DataPoint{}
	}
	resultMap := make(map[string][]*model.DataPoint)

	// 处理返回的结果
	resp := result.First()
	if len(resp.Curves()) == 0 {
		// 如果没有曲线数据，返回空切片
		resultMap[multivalue] = []*model.DataPoint{}
		return resultMap
	}
	for _, curve := range resp.Curves() {

		fieldName, ok := curve.Tags["_field"]
		if multivalue == "usage_idle" || multivalue == "mem_free_percent" {
			fieldName = multivalue
		} else {
			if !ok {
				continue
			}
		}

		// 创建一个结构体切片，将 TimeStamps 和 Values 对应起来
		type timestampValue struct {
			TimeStamp int64
			Value     float64
		}
		tvSlice := make([]timestampValue, len(curve.Timestamps))
		for i := range curve.Timestamps {
			tvSlice[i] = timestampValue{
				TimeStamp: curve.Timestamps[i],
				Value:     curve.Values[i],
			}
		}

		// 对 tvSlice 按照 TimeStamps 升序排序
		sort.Slice(
			tvSlice, func(i, j int) bool {
				return tvSlice[i].TimeStamp < tvSlice[j].TimeStamp
			},
		)

		// 将排序后的数据重新赋值回 curve.TimeStamps 和 curve.Values
		for i := range tvSlice {
			curve.Timestamps[i] = tvSlice[i].TimeStamp
			curve.Values[i] = tvSlice[i].Value
		}

		// 创建新的数据副本
		dataPoints := make([]*model.DataPoint, len(curve.Timestamps))
		for i := range curve.Timestamps {

			if multivalue == "usage_idle" {
				dataPoints[i] = &model.DataPoint{
					TimeStamp: int32(curve.Timestamps[i]),
					Value:     100 - curve.Values[i],
				}
			} else if fieldName == "com_commit" || fieldName == "com_select" || fieldName == "com_update" ||
				fieldName == "com_insert" || fieldName == "com_delete" || fieldName == "slow_queries" || fieldName == "innodb_rows_read" {
				if i == 0 {
					// For the first data point, set Value to 0 as we cannot compute the rate
					dataPoints[i] = &model.DataPoint{
						TimeStamp: int32(curve.Timestamps[i]),
						Value:     0,
					}
				} else {
					deltaValue := curve.Values[i] - curve.Values[i-1]
					//deltaTime := float64(curve.TimeStamps[i]-curve.TimeStamps[i-1]) / 1000.0 // Assuming timestamps are in milliseconds
					if deltaValue >= 0 {
						if fieldName == "com_commit" || fieldName == "com_select" || fieldName == "com_update" ||
							fieldName == "com_insert" || fieldName == "com_delete" || fieldName == "innodb_rows_read" {
							rate := deltaValue / float64(interval)
							dataPoints[i] = &model.DataPoint{
								TimeStamp: int32(curve.Timestamps[i]),
								Value:     rate,
							}
						} else if fieldName == "slow_queries" {
							dataPoints[i] = &model.DataPoint{
								TimeStamp: int32(curve.Timestamps[i]),
								Value:     deltaValue,
							}
						}
					} else {
						// Handle counter reset or invalid data
						dataPoints[i] = &model.DataPoint{
							TimeStamp: int32(curve.Timestamps[i]),
							Value:     0,
						}
					}
				}
			} else {
				dataPoints[i] = &model.DataPoint{
					TimeStamp: int32(curve.Timestamps[i]),
					Value:     curve.Values[i],
				}
			}
		}
		// 对数据副本按 Timestamps 升序排序
		sort.Slice(dataPoints, func(i, j int) bool {
			return dataPoints[i].TimeStamp < dataPoints[j].TimeStamp
		})
		resultMap[fieldName] = dataPoints
	}

	// 对 Value 保留两位小数
	roundedMap := roundToTwoDecimals(resultMap)
	return roundedMap
}

func roundToTwoDecimals(inputMap map[string][]*model.DataPoint) map[string][]*model.DataPoint {
	for key, dataPoints := range inputMap {
		for i := range dataPoints {
			dataPoints[i].Value = math.Round(dataPoints[i].Value*100) / 100
		}
		inputMap[key] = dataPoints
	}
	return inputMap
}

func (m *byterdsImpl) GetLastestMetricList(ctx context.Context, InstanceId string, regionId string, metric string, multivalue string,
	ipaddress string, appName string, appSecret string, start, end int64) map[string]float64 {
	// 初始化客户端
	client, err := mq.NewClient(
		appName, appSecret,
		mq.WithPeriodicTokenUpdate,                       // 自动更新 token
		mq.WithCluster(convertRegionToCluster(regionId)), // 控制面，与 Region 对应
	)
	if err != nil {
		log.Warn(ctx, "init byteMetric client failed %s", err)
		return map[string]float64{
			InstanceId: 0,
		}
	}
	defer client.Close()

	ipaddressNew, _, err := net.SplitHostPort(ipaddress)
	if err != nil {
		log.Warn(ctx, "SplitHostPort failed %s", err)
		return nil
	}
	// 构建查询

	interval := 30
	query := mq.NewQuery(mq.AggregatorAvg, metric).
		SetTenant("storage.mysql").
		SetDownsample(time.Duration(interval)*time.Second, mq.DownsampleAvg). // 设置降采样
		//SetTag("region", regionId).                                           // 设置 region 标签
		SetTag("dbname", InstanceId). // 设置实例名
		SetTag("ip", ipaddressNew).   //todo 可能是IP V6呢
		SetMultivalue(multivalue).    // 查询 usage_idle 指标
		SetTopK(1, mq.TopKMax)        // 仅获取 Top 1 数据

	// 创建请求，设置时间范围
	result := client.NewRequest(convertRegionToVRegion(regionId)).
		SetIntervalBetweenUnix(start, end).
		AddQuery(query).
		DoWithContext(ctx)

	// 检查查询结果
	if err := result.Err(); err != nil {
		log.Warn(ctx, "get cpu usage failed %s", err)
		return map[string]float64{
			InstanceId: 0,
		}
	}

	// 处理返回的结果
	resp := result.First()
	var metricResults float64
	for _, curve := range resp.Curves() {
		// 对 Timestamps 和 Values 按 Timestamps 升序排序
		sort.Slice(curve.Values, func(i, j int) bool {
			return curve.Timestamps[i] < curve.Timestamps[j]
		})
		// 获取第一个数据点的值
		if len(curve.Values) > 0 {
			metricResults = curve.Values[0]
			break
		}
	}

	return map[string]float64{
		multivalue: metricResults, // 计算 CPU 使用率
	}
}

func convertRegionToCluster(region string) mq.ItemCluster {
	switch region {
	case "boe2":
		return mq.ClusterBOE2
	case "boe":
		return mq.ClusterBOE
	case "cn":
		return mq.ClusterCN
	default:
		return mq.ClusterCN
	}
}

func convertRegionToVRegion(region string) mq.ItemRegion {
	switch region {
	case "boe2":
		return mq.RegionChinaBOE2
	case "boe":
		return mq.RegionChinaBOE
	case "cn":
		return mq.RegionCN
	default:
		return mq.RegionCN
	}
}

func convertToDataPoints(firstItem []*model.DataPoint) []*model.DataPoint {
	dataPoints := make([]*model.DataPoint, 0)
	for _, item := range firstItem {
		dataPoint := &model.DataPoint{
			TimeStamp: int32(item.TimeStamp),
			Value:     item.Value,
			Abnormal:  0,
		}
		dataPoints = append(dataPoints, dataPoint)
	}
	return dataPoints
}

func getMaxValue(items []*model.DataPoint) float64 {
	if len(items) == 0 {
		return 0 // 返回 0 或者你想要的默认值
	}
	maxValue := items[0].Value
	for _, item := range items {
		if item.Value > maxValue {
			maxValue = item.Value
		}
	}
	log.Info(context.Background(), "maxValue is %s", maxValue)
	return maxValue
}

func getMinValue(items []*model.DataPoint) float64 {
	if len(items) == 0 {
		return 0 // 返回 0 或者你想要的默认值
	}
	minValue := items[0].Value
	for _, item := range items {
		if item.Value < minValue {
			minValue = item.Value
		}
	}
	return minValue
}

func getAverageValue(items []*model.DataPoint) float64 {
	if len(items) == 0 {
		return 0 // 返回 0 或者你想要的默认值
	}
	var sum float64
	for _, item := range items {
		sum += item.Value
	}
	return sum / float64(len(items))
}

func (m *byterdsImpl) TimeConvertion(
	ctx context.Context, req *datasource.GetMetricUsageReq, timeType string, period int) (*datasource.GetMetricUsageReq, error) {

	// 拷贝请求对象
	newReq := *req

	startTime := req.StartTime
	endTime := req.EndTime

	// 用于保存新计算的 StartTime 和 EndTime
	var newStartTime, newEndTime time.Time

	// 根据 timeType 来调整时间
	if timeType == "YoY" || timeType == "MoM" {
		// 同比 (YoY) 或 环比 (MoM) 处理：将时间减去 period 分钟
		newStartTime = startTime.Add(time.Duration(-period) * time.Minute)
		newEndTime = endTime.Add(time.Duration(-period) * time.Minute)
	} else {
		// 常规展示：StartTime 减去 period 分钟，EndTime 加上 period 分钟
		newStartTime = startTime.Add(time.Duration(-period) * time.Minute)
		newEndTime = endTime.Add(time.Duration(period) * time.Minute)
	}

	// Ensure newEndTime does not exceed the current time
	currentTime := time.Now()
	if newEndTime.After(currentTime) {
		newEndTime = currentTime
	}

	// 将调整后的时间转换为 Unix 时间戳（以纳秒为单位）并更新请求
	newReq.StartTime = newStartTime
	newReq.EndTime = newEndTime

	return &newReq, nil
}

/*func (m *byterdsImpl) DiagRootCause(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagRootCauseResp, error) {
	var RootCauseResp *model.DescribeDiagRootCauseResp
	var err error
	// 检查 req.DiagType 是否为空
	if req.DiagType != nil {
		switch *req.DiagType {
		case model.DiagType_DISK, model.DiagType_CPU, model.DiagType_SLOWQUERY,
			model.DiagType_SQLERROR, model.DiagType_REPLICATION:
			RootCauseResp, err = m.DiagRootCauseByDiagType(ctx, req)
		default:
			RootCauseResp, err = m.DiagRootCauseALL(ctx, req)
		}
	} else {
		RootCauseResp, err = m.DiagRootCauseALL(ctx, req)
	}

	if err != nil {
		fmt.Println("DiagRootCause-DiagRootCauseALL:", err)
		return nil, err
	}
	return RootCauseResp, nil
}*/

func (m *byterdsImpl) DiagRootCauseByDiagType(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagRootCauseResp, error) {
	RootCauseInfo, err := m.DiagRootCauseByRootCauseInfo(ctx, req)
	AdviceInfo, err := m.DiagRootCauseByRootAdviceInfo(ctx, req)
	OtherInfo, err := m.DiagRootCauseByRootOtherInfo(ctx, req)
	resp := &model.DescribeDiagRootCauseResp{
		RootCause:     "",
		RootCauseList: RootCauseInfo,
		AdviceList:    AdviceInfo,
		OtherInfoList: OtherInfo,
	}

	if err != nil {
		fmt.Println("DiagRootCause-DiagRootCauseALL:", err)
		return nil, err
	}
	return resp, nil
}

func (m *byterdsImpl) DiagRootCauseByRootCauseInfo(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.DescribeDiagRootItem, error) {
	//key： data_total,data_used,data_used_percent,data_free
	var RootCauseInfo []*model.DescribeDiagRootItem
	switch *req.DiagType {
	case model.DiagType_DISK:
		Info, err := m.DiagRootCauseByRootRootCauseInfoByDisk(ctx, req)
		if err != nil {
			fmt.Println("DiagRootCause-DiagRootCauseALL:", err)
			return nil, err
		}
		RootCauseInfo = Info
	case model.DiagType_CPU:
		Info, err := m.DiagRootCauseByCPU(ctx, req)
		if err != nil {
			fmt.Println("DiagRootCause-DiagRootCauseALL:", err)
			return nil, err
		}
		RootCauseInfo = Info
	case model.DiagType_SLOWQUERY:
		Info, err := m.DiagRootCauseByCPU(ctx, req)
		if err != nil {
			fmt.Println("DiagRootCause-DiagRootCauseALL:", err)
			return nil, err
		}
		RootCauseInfo = Info
		/*	case model.DiagType_SQLERROR:
			case model.DiagType_REPLICATION:*/
	default:

	}

	return RootCauseInfo, nil
}

func (m *byterdsImpl) DiagRootCauseByRootAdviceInfo(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.DescribeDiagRootItem, error) {
	//key： data_total,data_used,data_used_percent,data_free
	var RootAdviceInfo []*model.DescribeDiagRootItem
	switch *req.DiagType {
	case model.DiagType_DISK:
		Info, err := m.DiagRootCauseByRootAdviceInfoByDisk(ctx, req)
		if err != nil {
			fmt.Println("DiagRootCause-DiagRootCauseALL:", err)
			return nil, err
		}
		RootAdviceInfo = Info
	case model.DiagType_CPU:
	case model.DiagType_SLOWQUERY:
		/*	case model.DiagType_SQLERROR:
			case model.DiagType_REPLICATION:*/
	default:

	}

	return RootAdviceInfo, nil
}

func (m *byterdsImpl) DiagRootCauseByRootOtherInfo(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.DescribeDiagRootItem, error) {
	//key： data_total,data_used,data_used_percent,data_free
	var OtherInfo []*model.DescribeDiagRootItem
	switch *req.DiagType {
	case model.DiagType_DISK:
		Info, err := m.DiagRootCauseByRootOtherInfoByDisk(ctx, req)
		if err != nil {
			fmt.Println("DiagRootCause-DiagRootCauseALL:", err)
			return nil, err
		}
		OtherInfo = Info
	case model.DiagType_CPU:

	case model.DiagType_SLOWQUERY:
		/*	case model.DiagType_SQLERROR:
			case model.DiagType_REPLICATION:*/
	default:

	}

	return OtherInfo, nil
}

func (m *byterdsImpl) DiagRootCauseByRootRootCauseInfoByDisk(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.DescribeDiagRootItem, error) {
	/*	var req1 *shared.DescribeTableSpace
		{
		}
		space_analysis := space_analysis.SqlSpaceAnalysisService{}
		space_analysis.DescribeDataBaseTableSpace(ctx, req1)*/
	RootCauseList := []*model.DescribeDiagRootItem{
		{
			Content:  "用户表占xxxxGB",
			TopTable: "Space",
		},
		{
			Content:  "临时表占xxxxGB",
			TopTable: "TempSpace",
		},
		{
			Content:  "表空洞占xxxx",
			TopTable: "Space",
		},
	}
	return RootCauseList, nil
}

func (m *byterdsImpl) DiagRootCauseByRootAdviceInfoByDisk(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.DescribeDiagRootItem, error) {

	/*export enum TopTable {
		'SlowLog' = 'SlowLog', // TOP 慢SQL
		'SlowLogTemp' = 'SlowLogTemp', // TOP 慢SQL （内场 ByteRDS 专用）
		'Dialog' = 'Dialog', // TOP SQL（基于聚合会话）
		'FullSQL' = 'FullSQL', // TOP SQL（基于全量SQL洞察）
		'TrxAndLock' = 'TrxAndLock', // TOP SQL（基于事务与锁）
		'Space' = 'Space', // TOP 表（基于空间分析）
		'TempSpace' = 'TempSpace', // 临时表空间分析
	}*/
	RootCauseList := []*model.DescribeDiagRootItem{
		{
			Content:  "可进行临时表清理",
			TopTable: "TempSpace",
		},
		{
			Content:  "表碎片整理",
			TopTable: "Space",
		},
		/*{
			Content:  "无流量表清理",
			TopTable: "",
		},*/
		{
			Content:  "无流量用户表清理",
			TopTable: "Space",
		},
	}

	return RootCauseList, nil
}

func (m *byterdsImpl) DiagRootCauseByRootOtherInfoByDisk(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.DescribeDiagRootItem, error) {

	//key： data_total,data_used,data_used_percent,data_free
	DiskMetrics, err := m.DiagRootCauseDiskMetrics(ctx, req)
	RootCauseList := []*model.DescribeDiagRootItem{
		{
			Content: "总空间:" + DiskMetrics["data_total"] + "GB",
		},
		{
			Content: "已用空间:" + DiskMetrics["data_used"] + "GB",
			//TopTable: "Space",
		},
		{
			Content: "磁盘使用率:" + DiskMetrics["data_used_percent"] + "%",
		},
		{
			Content: "剩余空间:" + DiskMetrics["data_free"] + "GB",
		},
	}

	if err != nil {
		fmt.Println("DiagRootCause-DiagRootCauseALL:", err)
		return nil, err
	}
	return RootCauseList, nil
}

func (m *byterdsImpl) DiagRootCauseByCPU(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.DescribeDiagRootItem, error) {
	//key： data_total,data_used,data_used_percent,data_free
	DiskMetrics, err := m.DiagRootCauseYoYQoQ(ctx, req)
	if err != nil {
		fmt.Println("DiagRootCause-DiagRootCauseALL:", err)
		return nil, err
	}

	calcYoY := func(current, yoy float64) string {
		if yoy == 0 {
			return "（同比增长 N/A）"
		}
		ratio := (current - yoy) / yoy * 100
		return fmt.Sprintf("（同比增长 %.2f%%）", ratio)
	}
	RootCauseList := []*model.DescribeDiagRootItem{
		{
			Content: fmt.Sprintf(
				"com_select: %.0f %s", DiskMetrics["com_select"],
				calcYoY(DiskMetrics["com_select"], DiskMetrics["com_selectYoY"]),
			),
		},
		{
			Content: fmt.Sprintf(
				"com_commit: %.0f %s", DiskMetrics["com_commit"],
				calcYoY(DiskMetrics["com_commit"], DiskMetrics["com_commitYoY"]),
			),
		},
		{
			Content: fmt.Sprintf(
				"innodb_rows_read: %.0f %s", DiskMetrics["innodb_rows_read"],
				calcYoY(DiskMetrics["innodb_rows_read"], DiskMetrics["innodb_rows_readYoY"]),
			),
		},
	}

	return RootCauseList, nil
}

func (m *byterdsImpl) DiagRootCauseBySlowQuery(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.DescribeDiagRootItem, error) {

	//key： data_total,data_used,data_used_percent,data_free
	DiskMetrics, err := m.DiagRootCauseDiskMetrics(ctx, req)
	RootCauseList := []*model.DescribeDiagRootItem{
		{
			Content: "总空间:" + DiskMetrics["data_total"] + "GB",
		},
	}

	if err != nil {
		fmt.Println("DiagRootCause-DiagRootCauseALL:", err)
		return nil, err
	}
	return RootCauseList, nil
}

func (m *byterdsImpl) DiagRootCauseDiskMetrics(ctx context.Context, req *datasource.GetMetricUsageReq) (map[string]string, error) {
	metric := "inf.mysql.server.mysql_system"
	multivalue := "data_total,data_used,data_used_percent,data_free"
	ipaddress := req.NodeIds[0]

	result := m.GetMultivalueMetricList(
		ctx, req.InstanceId, req.RegionId,
		metric, multivalue, ipaddress, "", "", req.StartTime, req.EndTime,
	)

	diskMetrics := map[string]float64{}
	for key, dataPoints := range result {
		maxMean := 0.0
		if len(dataPoints) > 1 {
			for i := 0; i <= len(dataPoints)-1; i++ {
				value := dataPoints[i]
				if value.Value > maxMean {
					maxMean = value.Value
				}
			}
		}
		diskMetrics[key] = maxMean
	}

	convertedMetrics := make(map[string]string)
	for key, value := range diskMetrics {
		if key == "data_used_percent" {
			convertedMetrics[key] = fmt.Sprintf("%.2f", value) //
		} else {
			var convertedValue string
			if value < 1024 {
				// value in KB
				convertedValue = fmt.Sprintf("%.3f KB", value)
			} else if value < 1024*1024 {
				// value in MB
				convertedValue = fmt.Sprintf("%.3f MB", value/1024)
			} else if value < 1024*1024*1024 {
				// value in GB
				convertedValue = fmt.Sprintf("%.3f GB", value/1024/1024)
			} else {
				// value in TB
				convertedValue = fmt.Sprintf("%.3f TB", value/1024/1024/1024)
			}
			convertedMetrics[key] = convertedValue
		}
	}

	return convertedMetrics, nil
}

func (m *byterdsImpl) DiagRootCauseALL(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagRootCauseResp, error) {

	metric := "inf.mysql.server.mysql_global_status"
	multivalue := "com_commit,slow_queries,com_select,com_delete,com_insert,com_update,innodb_rows_read"
	ipaddress := req.NodeIds[0]

	result := m.GetMultivalueMetricList(
		ctx, req.InstanceId, req.RegionId,
		metric, multivalue, ipaddress, "", "", req.StartTime, req.EndTime,
	)

	anomalyDatal := map[string]float64{}
	for key, dataPoints := range result {
		maxMean := 0.0
		// 遍历连续的 3 个点计算平均值
		if len(dataPoints) > 3 {
			for i := 1; i <= len(dataPoints)-3; i++ {
				sum := dataPoints[i].Value + dataPoints[i+1].Value + dataPoints[i+2].Value
				mean := sum / 3.0
				if mean > maxMean {
					maxMean = mean
				}
			}
		} else if len(dataPoints) == 3 {
			for i := 1; i <= len(dataPoints)-2; i++ {
				sum := dataPoints[i].Value + dataPoints[i+1].Value
				mean := sum / 2.0
				if mean > maxMean {
					maxMean = mean
				}
			}
		} else if len(dataPoints) == 2 {
			for i := 1; i <= len(dataPoints)-1; i++ {
				sum := dataPoints[i].Value
				mean := sum / 1.0
				if mean > maxMean {
					maxMean = mean
				}
			}
		}
		anomalyDatal[key] = maxMean
		//fmt.Printf("Metric: %s, Highest Mean: %.2f, Points: %v\n", key, maxMean, dataPoints[startIndex:startIndex+3])
	}

	StartTimeIntYoY := req.StartTime.Add(-24 * time.Hour).Add(-3 * time.Hour) //1小时+用户选择的时间区间的数据
	EndTimeIntYoY := req.EndTime.Add(-24 * time.Hour).Add(-3 * time.Hour)
	resultYoY := m.GetMultivalueMetricList(
		ctx, req.InstanceId, req.RegionId,
		metric, multivalue, ipaddress, "", "", StartTimeIntYoY, EndTimeIntYoY,
	)

	// Initialize DescribeDiagRootCauseResp
	rootCauseResp := &model.DescribeDiagRootCauseResp{
		RootCause:     "", // RootCause is now the main field for concatenated results
		RootCauseList: []*model.DescribeDiagRootItem{},
		AdviceList:    []*model.DescribeDiagRootItem{},
		OtherInfoList: []*model.DescribeDiagRootItem{},
	}

	for key, dataPoints := range resultYoY {
		mean, stdDev := m.CalculateMeanStdDev(ctx, dataPoints)
		newValue := anomalyDatal[key]
		log.Info(ctx, fmt.Sprintf("IsAnomaly-key: %v,newValue: %v, mean: %v,stdDev: %v", key, newValue, mean, stdDev))
		if m.IsAnomaly(ctx, newValue, mean, stdDev) {
			log.Info(
				ctx, fmt.Sprintf("RealAnomaly-key: %v,newValue: %v, mean: %v,stdDev: %v", key, newValue, mean, stdDev),
			)
			switch key {
			case "com_update":
				rootCauseResp.RootCause += " 【update数量有波动】"
			case "com_delete":
				rootCauseResp.RootCause += " 【delete数量有波动】"
			case "com_insert":
				rootCauseResp.RootCause += " 【insert数量有波动】"
			case "com_select":
				rootCauseResp.RootCause += " 【select数量有波动】"
			case "com_commit":
				rootCauseResp.RootCause += " 【TPS数量有波动】"
			case "slow_queries":
				sl := slowquery.SlowQueryService{}
				slowQueryReq := model.DescribeAggregateDiagSlowQueryReq{}
				slowQueryResp, err := sl.DescribeAggregateDiagSlowQuery(ctx, &slowQueryReq, &slowquery.DescribeAggregateDiagSlowQueryOpt{
					MaxQueryCnt: 5000,
				})
				if err != nil {
					log.Warn(ctx, "SplitHostPort failed %s", err)
					return nil, err
				}

				// 提取 Count * AvgQueryTime 最大的 Top 3
				type slowLogWithMetric struct {
					logItem *model.AggregateDiagSlowLog
					metric  float64
				}
				var logsWithMetrics []slowLogWithMetric
				for _, logItem := range slowQueryResp.AggregateSlowLogs {
					if logItem != nil {
						currentValue := logItem.Count * logItem.AvgQueryTime
						logsWithMetrics = append(
							logsWithMetrics, slowLogWithMetric{
								logItem: logItem,
								metric:  currentValue,
							},
						)
					}
				}
				// 按 metric 从大到小排序
				sort.Slice(
					logsWithMetrics, func(i, j int) bool {
						return logsWithMetrics[i].metric > logsWithMetrics[j].metric
					},
				)

				// 获取 Top 3
				topCount := 3
				if len(logsWithMetrics) < topCount {
					topCount = len(logsWithMetrics)
				}
				topFingerprints := []string{}
				for i := 0; i < topCount; i++ {
					topFingerprints = append(topFingerprints, logsWithMetrics[i].logItem.Fingerprint)
				}
				rootCauseResp.RootCause += " 【慢查询数量有波动】 总耗时最长的Top 3 慢SQL模版是：" + strings.Join(
					topFingerprints, ", ",
				)
			}
		}
	}
	return rootCauseResp, nil
}

func (m *byterdsImpl) DiagRootCauseYoYQoQ(ctx context.Context, req *datasource.GetMetricUsageReq) (map[string]float64, error) {

	metric := "inf.mysql.server.mysql_global_status"
	multivalue := "com_select,com_commit,innodb_rows_read"
	ipaddress := req.NodeIds[0]

	result := m.GetMultivalueMetricList(
		ctx, req.InstanceId, req.RegionId,
		metric, multivalue, ipaddress, "", "", req.StartTime, req.EndTime,
	)

	convertedMetrics := make(map[string]float64)

	anomalyDatal := map[string]float64{}
	for key, dataPoints := range result {
		anomalyDatal[key] = CalculateMaxMean(dataPoints)
		convertedMetrics[key] = anomalyDatal[key]
	}

	StartTimeIntYoY := req.StartTime.Add(-24 * time.Hour) //1小时+用户选择的时间区间的数据
	EndTimeIntYoY := req.EndTime.Add(-24 * time.Hour)
	resultYoY := m.GetMultivalueMetricList(
		ctx, req.InstanceId, req.RegionId,
		metric, multivalue, ipaddress, "", "", StartTimeIntYoY, EndTimeIntYoY,
	)

	anomalyDatalYoY := map[string]float64{}
	for key, dataPoints := range resultYoY {
		anomalyDatalYoY[key] = CalculateMaxMean(dataPoints)
		convertedMetrics[key+"YoY"] = anomalyDatalYoY[key]
	}
	return convertedMetrics, nil
	// Initialize DescribeDiagRootCauseResp
	/*	rootCauseResp := &model.DescribeDiagRootCauseResp{
		RootCause:     "", // RootCause is now the main field for concatenated results
		RootCauseList: []*model.DescribeDiagRootItem{},
		AdviceList:    []*model.DescribeDiagRootItem{},
		OtherInfoList: []*model.DescribeDiagRootItem{},
	}*/

	/*	for key, dataPoints := range resultYoY {
		mean, stdDev := m.CalculateMeanStdDev(ctx, dataPoints)
		newValue := anomalyDatal[key]
		log.Info(ctx, fmt.Sprintf("IsAnomaly-key: %v,newValue: %v, mean: %v,stdDev: %v", key, newValue, mean, stdDev))

		switch key {
		case "com_select":
			convertedMetrics[key] = ""
		case "com_commit":
			convertedMetrics[key] = ""
		case "innodb_rows_read":
			convertedMetrics[key] = ""
		case "slow_queries":
			sl := slowquery.SlowQueryService{}
			log.Warn(ctx, "DescribeAggregateDiagSlowQuery-req %s", req)
			if req.RegionId == "boe" || req.RegionId == "boe2" {
				sl.BaseURL = "http://api-db-slowlog-boe.byted.org"
			} else if req.RegionId == "cn" {
				sl.BaseURL = "http://api-db-slowlog.byted.org"
			}
			slowQueryReq := model.DescribeAggregateDiagSlowQueryReq{}
			slowQueryResp, err := sl.ConvertToDescribeAggregateResp(ctx, &slowQueryReq)
			if err != nil {
				log.Warn(ctx, "SplitHostPort failed %s", err)
				return nil, err
			}

			// 提取 Count * AvgQueryTime 最大的 Top 3
			type slowLogWithMetric struct {
				logItem *model.AggregateDiagSlowLog
				metric  float64
			}
			var logsWithMetrics []slowLogWithMetric
			for _, logItem := range slowQueryResp.AggregateSlowLogs {
				if logItem != nil {
					currentValue := logItem.Count * logItem.AvgQueryTime
					logsWithMetrics = append(logsWithMetrics, slowLogWithMetric{
						logItem: logItem,
						metric:  currentValue,
					})
				}
			}
			// 按 metric 从大到小排序
			sort.Slice(logsWithMetrics, func(i, j int) bool {
				return logsWithMetrics[i].metric > logsWithMetrics[j].metric
			})

			// 获取 Top 3
			topCount := 3
			if len(logsWithMetrics) < topCount {
				topCount = len(logsWithMetrics)
			}
			topFingerprints := []string{}
			for i := 0; i < topCount; i++ {
				topFingerprints = append(topFingerprints, logsWithMetrics[i].logItem.Fingerprint)
			}
			rootCauseResp.RootCause += " 【慢查询数量有波动】 总耗时最长的Top 3 慢SQL模版是：" + strings.Join(topFingerprints, ", ")
		}
	}*/
}

func CalculateMaxMean(dataPoints []*model.DataPoint) float64 {
	maxMean := 0.0
	n := len(dataPoints)

	if n > 3 {
		for i := 1; i <= n-3; i++ {
			sum := dataPoints[i].Value + dataPoints[i+1].Value + dataPoints[i+2].Value
			mean := sum / 3.0
			if mean > maxMean {
				maxMean = mean
			}
		}
	} else if n == 3 {
		sum := dataPoints[1].Value + dataPoints[2].Value
		mean := sum / 2.0
		if mean > maxMean {
			maxMean = mean
		}
	} else if n == 2 {
		sum := dataPoints[1].Value
		mean := sum / 1.0
		if mean > maxMean {
			maxMean = mean
		}
	}

	return maxMean
}

func (m *byterdsImpl) initMonitorByMetric() {
	// 初始化
	m.MonitorMetric = make(map[string]func(ctx context.Context, req *datasource.GetMonitorByMetricReq) (*model.MetricResource, error))
	m.MonitorMetric["diskUsage"] = m.GetMetricDiskUsage
	m.MonitorMetric["diskCapacity"] = m.GetMetricDiskCapacity
}

func (m *byterdsImpl) GetMonitorByMetric(ctx context.Context, req *datasource.GetMonitorByMetricReq) ([]*model.MetricResource, error) {
	m.initMonitorByMetric()
	var resources []*model.MetricResource
	var lock sync.Mutex
	waitCh := make(chan struct{})
	errCh := make(chan error)
	wg := sync.WaitGroup{}
	go func() {
		for k, v := range m.MonitorMetric {
			fn := v
			wg.Add(1)
			k := k
			go func() {
				defer wg.Done()
				item, err := fn(ctx, req)
				if err != nil {
					log.Warn(ctx, fmt.Sprintf("Get Metric %s is err: %s", k, err.Error()))
					errCh <- err
					return
				}
				lock.Lock()
				defer lock.Unlock()
				resources = append(resources, item)
				// 按照字典序排序
				err = fp.StreamOf(resources).SortBy(func(a, b *model.MetricResource) bool {
					return a.Name < b.Name
				}).ToSlice(&resources)
				if err != nil {
					log.Warn(ctx, fmt.Sprintf("Get Metric %s is err: %s", k, err.Error()))
					errCh <- err
					return
				}
			}()
		}
		wg.Wait()
		close(waitCh)
	}()

	select {
	case err := <-errCh:
		log.Info(ctx, "GetMonitorByMetric error: %s", err)
		return nil, err
	case <-waitCh:
		return resources, nil
	}
}

func (m *byterdsImpl) GetMetricDiskUsage(ctx context.Context, req *datasource.GetMonitorByMetricReq) (*model.MetricResource, error) {
	metric := "inf.mysql.server.mysql_system"
	metricName := "inf.mysql.server.mysql_system"
	multivalue := "data_used_percent"
	ret, err := m.GetMetricByByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryCpu-err %s", err)
	}
	if ret == nil {
		return nil, err
	}
	ret.Unit = percent
	ret.Name = model.MetricType_DiskUsage
	// 保留两位小数
	//coefficient := 1.00
	//roundResourceFields(ret, coefficient)
	return ret, nil
}

func (m *byterdsImpl) GetMetricDiskCapacity(ctx context.Context, req *datasource.GetMonitorByMetricReq) (*model.MetricResource, error) {
	metric := "inf.mysql.server.mysql_system"
	metricName := "inf.mysql.server.mysql_system"
	multivalue := "data_used"
	ret, err := m.GetMetricByByteCloud(ctx, req, metric, metricName, multivalue)
	if err != nil {
		log.Warn(ctx, "DescribeDBDiagnosis-GetHealthSummaryCpu-err %s", err)
	}
	if ret == nil {
		return nil, err
	}
	ret.Unit = Byte
	ret.Name = model.MetricType_DiskCapacity
	// 保留两位小数
	//coefficient := 1.00
	//roundResourceFields(ret, coefficient)
	return ret, nil
}

func (m *byterdsImpl) GetMetricByByteCloud(ctx context.Context, req *datasource.GetMonitorByMetricReq, metric string, metricName string, multivalue string) (*model.MetricResource, error) {

	ipv4 := req.NodeIds[0]
	result := m.GetMetricList(ctx, req.InstanceId, req.RegionId, metric, metricName, multivalue, ipv4,
		"", "", req.StartTime, req.EndTime, req.Interval)
	firstItem := result[multivalue]
	dataPointsPeriod := convertToDataPoints(firstItem)

	// 构造返回结果
	ret := &model.MetricResource{
		DataPoints: dataPointsPeriod,
	}
	log.Info(ctx, "GetMetricByByteCloud resp is %+v", utils.Show(ret))
	return ret, nil
}
