package byterds

const (
	DBListAPI      = "/openapi/rds/v2/dbs/"                          // 实例列表
	DBDetailAPI    = "/openapi/rds/v2/dbs/%s"                        // 实例详情
	KillProcessAPI = "/openapi/rds/v2/dbs/%s/instance/kill_process/" // kill会话
	//DBInstanceEngineAPI     = "/openapi/rds/v2/topo/instance_list/"
	DBInstanceEngineAPI     = "/openapi/rds/v2/topo/%s/db_instances/"       // 实例db engine 拓扑信息
	DBInstanceMySQLProxyAPI = "/openapi/rds/mysql/v2/topo/proxy_instances/" // 实例proxy 拓扑信息
	DBInstanceNDBProxyAPI   = "/openapi/rds/ndb/v2/topo/proxy_instances/"
	DBByteTreeAPI           = "/openapi/rds/v2/dbs/%s/bytetree_mount/"            // 实例挂载的服务树信息
	PSMTicketAPI            = "/openapi/rds/v2/bpm_proxy/types/psm_auth/tickets/" // 创建PSM授权工单
	BPMTicketStatusAPI      = "/openapi/rds/v2/bpm_proxy/tickets/%d/status/"      // 查询工单状态
	DbwDefaultBizPsm        = "dbw.bytecloud.mgr"
	DbwBizAuthPsm           = "dbw.bytecloud.auth"
	ServiceAccount          = "volcstoragedbw"
	DBExistAPI              = "/openapi/rds/v2/dbs/%s/exists/"
	DBGetPsmAuth            = "/openapi/rds/v2/dbs/%s/psm_auth/"
	ThrottleCreateAPI       = "/openapi/rds/v2/proxy/mysql/throttle/create/" // 限流规则创建
	ThrottleStopAPI         = "/openapi/rds/v2/proxy/mysql/throttle/stop/"   // 限流规则删除

)
const jwtUrl = "https://cloud.bytedance.net/auth/api/v1/jwt"
const (
	FullSqlOpenAPI   = "/openapi/rds/v2/full_sql/%s/instance/open/"
	FullSqlCloseAPI  = "/openapi/rds/v2/full_sql/%s/instance/close/"
	FullSqlStatusAPI = "/openapi/rds/v2/full_sql/%s/instance/get_status/"

	ErrorLogCreateLogDownloadTask = "/openapi/rds/v2/create_log_download_task"
	ErrorLogGetLogDownloadList    = "/openapi/rds/v2/get_log_download_list"
)
const (
	//MySQL57TrxAndLockQuery = "/*+ DBW SQL CONSOLE DEFAULT*/ select t.trx_id AS TrxId, t.trx_state AS TrxState, t.trx_mysql_thread_id AS ProcessId," +
	//	" t.trx_started AS TrxStartTime,t.trx_wait_started AS TrxWaitStartTime, l.blocking_trx_id AS BlockTrxId," +
	//	"t.trx_query AS SqlBlocked,t.trx_isolation_level AS TrxIsoLevel,t.trx_tables_locked AS TrxTablesLocked,t.trx_rows_locked AS TrxRowsLocked," +
	//	"t.trx_lock_structs AS TrxLockStructs,t.trx_rows_modified AS TrxRowsModified " +
	//	"from information_schema.INNODB_TRX t " +
	//	"left join information_schema.INNODB_LOCK_WAITS l on t.trx_requested_lock_id = l.requested_lock_id "
	MySQL57TrxAndLockQuery = " /*+ DBW SQL CONSOLE DEFAULT*/ SELECT * FROM (select t.trx_id AS TrxId, t.trx_state AS TrxStatus, " +
		"t.trx_mysql_thread_id AS ProcessId, t.trx_started AS TrxStartTime,trx_requested_lock_id, t.trx_wait_started AS TrxWaitStartTime," +
		" l.blocking_trx_id AS BlockTrxId, t.trx_query AS SqlBlocked,t.trx_isolation_level AS TrxIsoLevel," +
		"TIMESTAMPDIFF(SECOND,trx_started,NOW()) as TrxExecTime," +
		"t.trx_tables_locked AS TrxTablesLocked,t.trx_rows_locked AS TrxRowsLocked,t.trx_lock_structs AS TrxLockStructs," +
		"t.trx_rows_modified AS TrxRowsModified from information_schema.INNODB_TRX t " +
		"left join information_schema.INNODB_LOCK_WAITS l on t.trx_requested_lock_id = l.requested_lock_id ) as subQuery "
	//MySQL80TrxAndLockQuery = "/*+ DBW SQL CONSOLE DEFAULT*/ select t.trx_id AS TrxId, t.trx_state AS TrxState, t.trx_mysql_thread_id AS ProcessId," +
	//	" t.trx_started AS TrxStartTime,t.trx_wait_started AS TrxWaitStartTime, l.BLOCKING_ENGINE_TRANSACTION_ID AS BlockTrxId," +
	//	"t.trx_query AS SqlBlocked,t.trx_isolation_level AS TrxIsoLevel,t.trx_tables_locked AS TrxTablesLocked,t.trx_rows_locked AS TrxRowsLocked," +
	//	"t.trx_lock_structs AS TrxLockStructs,t.trx_rows_modified AS TrxRowsModified " +
	//	"from information_schema.INNODB_TRX t " +
	//	"left join performance_schema.data_lock_waits l on t.trx_requested_lock_id = l.REQUESTING_ENGINE_LOCK_ID "
	MySQL80TrxAndLockQuery = "/*+ DBW SQL CONSOLE DEFAULT*/ SELECT * FROM (select t.trx_id AS TrxId, t.trx_state AS TrxStatus, " +
		"t.trx_mysql_thread_id AS ProcessId,t.trx_started AS TrxStartTime, trx_requested_lock_id,t.trx_wait_started AS TrxWaitStartTime, " +
		"l.BLOCKING_ENGINE_TRANSACTION_ID AS BlockTrxId,t.trx_query AS SqlBlocked,t.trx_isolation_level AS TrxIsoLevel," +
		"TIMESTAMPDIFF(SECOND,trx_started,NOW()) as TrxExecTime," +
		"t.trx_tables_locked AS TrxTablesLocked,t.trx_rows_locked AS TrxRowsLocked, t.trx_lock_structs AS TrxLockStructs," +
		"t.trx_rows_modified AS TrxRowsModified from information_schema.INNODB_TRX t " +
		"left join performance_schema.data_lock_waits l on t.trx_requested_lock_id = l.REQUESTING_ENGINE_LOCK_ID ) " +
		"as subQuery "
	MySQLCountQuery = "/*+ DBW SQL CONSOLE DEFAULT*/ select count(*) from information_schema.INNODB_TRX "
)
