package byterds

import (
	"context"
	"encoding/json"
	"fmt"
	"io"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"

	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

func (b *byterdsImpl) getDBDetail(ctx context.Context, req *GetDBDetailReq) (*GetDBDetailResp, error) {
	params := map[string]string{
		"db_name": req.DBName,
		"region":  req.Region,
	}
	log.Warn(ctx, "getDBDetail req : %s", utils.Show(req))
	path := fmt.Sprintf(DBDetailAPI, req.DBName)
	resp, err := b.byteRDSHttpClient.GET(ctx, path, params, nil, req.Region, false)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if resp == nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "response empty")
	}
	detail := &GetDBDetailResp{}
	respByte, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	log.Info(ctx, "get db detail resp %s", respByte)
	if err := json.Unmarshal(respByte, detail); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if detail.RDSAPIRespMeta.Code != 0 {
		log.Warn(ctx, "call rds DB detail error:%s", detail.RDSAPIRespMeta.Msg)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, detail.RDSAPIRespMeta.Msg)
	}
	return detail, nil
}

func (b *byterdsImpl) getPsmTktState(ctx context.Context, req *GetPsmTktStatusReq) (*PsmTktStatusResp, error) {
	params := map[string]string{
		"region": req.Region,
	}
	path := fmt.Sprintf(BPMTicketStatusAPI, req.BpmTktId)
	resp, err := b.byteRDSHttpClient.GET(ctx, path, params, nil, req.Region, false)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if resp == nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty response")
	}
	state := &PsmTktStatusResp{}
	respByte, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	log.Info(ctx, "get db tkt state resp %s", respByte)
	if err := json.Unmarshal(respByte, state); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if state.RDSAPIRespMeta.Code != 0 {
		log.Warn(ctx, "call rds DB detail error:%s", state.RDSAPIRespMeta.Msg)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, state.RDSAPIRespMeta.Msg)
	}
	return state, nil
}

func (b *byterdsImpl) getPsmAuth(ctx context.Context, req *GetDBAuthPsmReq) (*GetDBAuthPsmResp, error) {
	params := map[string]string{
		"region": req.Region,
	}
	path := fmt.Sprintf(DBGetPsmAuth, req.DB)
	resp := &GetDBAuthPsmResp{}
	if err := b.byteRDSHttpClient.HttpGet(ctx, path, req.Region, params, nil, resp); err != nil {
		log.Warn(ctx, "get psm auth error %s", err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	log.Info(ctx, "get psm auth resp %s", utils.Show(resp))
	if resp.RDSAPIRespMeta.Code != 0 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, resp.RDSAPIRespMeta.Error)
	}
	return resp, nil
}

func (b *byterdsImpl) grantPermToPsm(ctx context.Context, db, psm, region string) error {
	dbInfo, err := b.GetDBInstanceInfo(ctx, &datasource.GetDBInstanceInfoReq{InstanceId: db, Type: b.Type(), RegionId: region})
	if err != nil {
		log.Warn(ctx, "Get db %s consul list failed %s", db, err)
		return err
	}
	cnf := b.conf.Get(ctx)
	// 创建psm授权工单完成账号创建
	body := &CreatePsmTktBody{
		Region:         region,
		Creator:        cnf.PsmTicketCreator,
		DbName:         db,
		BackGround:     fmt.Sprintf("auto create dbw_admin account with %s", fwctx.GetLogID(ctx)),
		BizPsmList:     []string{psm},
		DbPsmList:      dbInfo.DBInfo.GetConsuleList(),
		Action:         "add",
		ReviewStrategy: "skip_all_review",
		//IdempotentId:   fmt.Sprintf("%s-%s-%s", ServiceAccount, region, db), // 幂等key
	}
	resp := &CreatePsmTktResp{}
	err = b.byteRDSHttpClient.HttpPost(ctx, PSMTicketAPI, region, nil, nil, body, resp)
	if err != nil {
		log.Warn(ctx, "create ticket error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if resp.RDSAPIRespMeta.Code != 0 {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, resp.RDSAPIRespMeta.Error)
	}
	return nil
}

func (b *byterdsImpl) createThrottleRule(ctx context.Context, regionId string, rule ThrottleRuleObject) error {
	body := &CreateThrottleBody{
		Region: regionId,
		Rule:   rule,
	}
	log.Info(ctx, "create throttle rule body is is %s", utils.Show(body))
	resp := &CreateThrottleResp{}
	err := b.byteRDSHttpClient.HttpPost(ctx, ThrottleCreateAPI, regionId, nil, nil, body, resp)
	if err != nil {
		log.Warn(ctx, "create throttle rule error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if resp.RDSAPIRespMeta.Code != 0 {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, resp.RDSAPIRespMeta.Error)
	}
	return nil
}
func (b *byterdsImpl) stopThrottleRule(ctx context.Context, regionId string, ruleList []string, DbName string) error {
	body := &StopThrottleBody{
		Region:  regionId,
		DB:      DbName,
		RuleIds: ruleList,
	}
	log.Info(ctx, "stop throttle rule body is %s", utils.Show(body))
	resp := &StopThrottleResp{}
	err := b.byteRDSHttpClient.HttpPost(ctx, ThrottleStopAPI, regionId, nil, nil, body, resp)
	if err != nil {
		log.Warn(ctx, "stop throttle rule error %s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if resp.RDSAPIRespMeta.Code != 0 {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, resp.RDSAPIRespMeta.Error)
	}
	return nil
}
func hasPermission(dbpsm string, permissions []AuthedPsm) bool {
	for _, perm := range permissions {
		if perm.DBConsul == dbpsm {
			for _, p := range perm.PsmList {
				if p == DbwBizAuthPsm {
					return true
				}
			}
		}
	}
	return false
}
