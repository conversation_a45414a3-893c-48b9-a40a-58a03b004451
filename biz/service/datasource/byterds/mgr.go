// ignore_security_alert_file SQL_INJECTION
package byterds

import (
	strings2 "code.byted.org/gopkg/lang/strings"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net"
	"regexp"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/lib/mysql"
	rdsModel_v2_new "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	commonUtils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-lib/framework/db"
	"github.com/qjpcpu/fp"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"go.uber.org/dig"

	dsmgrpb "code.byted.org/bytedts/ds-mgr/shared"
	mgr "code.byted.org/infcs/dbw-mgr/biz/infrastructure/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

const (
	DBW_CONSOLE_DEFAULT_HINT = " /*+ DBW SQL CONSOLE DEFAULT*/"
)

type NewByteRdsDataSourceIn struct {
	dig.In
	Conf              config.ConfigProvider
	DsMgrBuilder      mgr.DsMgrBuilder
	ByteRDSHttpClient ByteRDSClient
}

type NewByteRdsDataSourceOut struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

func NewByteRdsDataSource(in NewByteRdsDataSourceIn) NewByteRdsDataSourceOut {
	return NewByteRdsDataSourceOut{
		Source: datasource.RetryIfWhiteListNotReady(&byterdsImpl{
			dsMgrBuilder:      in.DsMgrBuilder,
			conf:              in.Conf,
			byteRDSHttpClient: in.ByteRDSHttpClient,
		}),
	}
}

type byterdsImpl struct {
	dsMgrBuilder        mgr.DsMgrBuilder
	conf                config.ConfigProvider
	byteRDSHttpClient   ByteRDSClient
	HealthSummaryMetric map[string]func(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.Resource, error)
	MonitorMetric       map[string]func(ctx context.Context, req *datasource.GetMonitorByMetricReq) (*model.MetricResource, error)
}

func (m *byterdsImpl) DescribeBigKeys(ctx context.Context, req *datasource.DescribeBigKeysReq) (*datasource.DescribeBigKeysResp, error) {
	//TODO implement me
	panic("implement me")
}

func (m *byterdsImpl) DescribeHotKeys(ctx context.Context, req *datasource.DescribeHotKeysReq) (*datasource.DescribeHotKeysResp, error) {
	//TODO implement me
	panic("implement me")
}

func (m *byterdsImpl) GetInspectionOutputMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	//TODO implement me
	panic("implement me")
}

func (m *byterdsImpl) GetInspectionInputMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	//TODO implement me
	panic("implement me")
}

func (m *byterdsImpl) DescribeDBAutoScalingConfig(ctx context.Context, req *datasource.DescribeDBAutoScalingConfigReq) (*datasource.DescribeDBAutoScalingConfigResp, error) {
	//TODO implement me
	panic("implement me")
}

func (m *byterdsImpl) ModifyDBAutoScalingConfig(ctx context.Context, req *datasource.ModifyDBAutoScalingConfigReq) (*datasource.ModifyDBAutoScalingConfigResp, error) {
	//TODO implement me
	panic("implement me")
}

func (m *byterdsImpl) DescribeDBAutoScaleEvents(ctx context.Context, req *datasource.DescribeDBAutoScaleEventsReq) (*datasource.DescribeDBAutoScaleEventsResp, error) {
	//TODO implement me
	panic("implement me")
}

func (m *byterdsImpl) ModifyDBLocalSpecManually(ctx context.Context, req *datasource.ModifyDBLocalSpecManuallyReq) (*datasource.ModifyDBLocalSpecManuallyResp, error) {
	//TODO implement me
	panic("implement me")
}

func (m *byterdsImpl) GetPreSecondMetricDataByInstance(ctx context.Context, req *datasource.GetMetricDatapointsReq) (*datasource.GetMetricDatapointsResp, error) {
	res, err := m.GetDataPointByteCloud(
		ctx, &datasource.GetMetricUsageReq{
			InstanceId: req.InstanceId,
			StartTime:  req.StartTime,
			EndTime:    req.EndTime,
			NodeIds:    []string{req.NodeId},
			Intervals:  req.Period,
		}, req.Measurement, req.MetricName, req.MultiValue,
	)
	if err != nil {
		return nil, err
	}
	return &datasource.GetMetricDatapointsResp{
		DataPoints: res,
	}, nil
}

func (m *byterdsImpl) DescribeDBInstanceDetailForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	detail, err := m.DescribeDBInstanceDetail(ctx, req)
	if err != nil {
		return "", err
	}
	return utils.Show(detail), nil
}

func (m *byterdsImpl) DescribeDBInstanceParametersForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	detail, err := m.DescribeDBInstanceDetail(ctx, req)
	if err != nil {
		return "", err
	}
	return utils.Show(detail), nil
}

func (b *byterdsImpl) GetInstancePrimaryNodeId(ctx context.Context, req *datasource.GetInstancePrimaryNodeIdReq) (*datasource.GetInstancePrimaryNodeIdResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) CheckAccountPrivilege(ctx context.Context, req *datasource.CheckDBWAccountReq) (bool, error) {
	panic("implement me")
}

func (b *byterdsImpl) ResetAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error {
	panic("implement me")
}
func (b *byterdsImpl) GrantReplicationPrivilege(ctx context.Context, ds *shared.DataSource, accountName string) error {
	panic("implement me")
}

func (b *byterdsImpl) DescribeInstancePodAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListSQLKillRules(ctx context.Context, req *datasource.ListSQLKillRulesReq) (*datasource.ListSQLKillRulesResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ModifySQLKillRule(ctx context.Context, req *datasource.ModifySQLKillRuleReq) (*datasource.ModifySQLKillRuleResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeInstanceDetail(ctx context.Context, req *datasource.GetInstanceDetailReq) (*rdsModel_v2_new.DescribeDBInstanceDetailResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeDBInstances(ctx context.Context, req *rdsModel_v2_new.DescribeDBInstancesReq) (*rdsModel_v2_new.DescribeDBInstancesResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) UpdateWhiteList(ctx context.Context, id string, ds *shared.DataSource, ip []string) error {
	panic("implement me")
}

func (b *byterdsImpl) RemoveWhiteList(ctx context.Context, id string, ds *shared.DataSource, wlID string) error {
	panic("implement me")
}

func (b *byterdsImpl) DescribeCurrentConn(ctx context.Context, req *datasource.DescribeCurrentConnsReq) (*datasource.DescribeCurrentConnsResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) FillDataSource(ctx context.Context, ds *shared.DataSource) error {
	detail, err := b.getDBDetail(ctx, &GetDBDetailReq{
		Region: ds.Region,
		DBName: ds.InstanceId,
	})
	if err != nil {
		return err
	}
	log.Info(ctx, "db detail is %s", utils.Show(detail))
	if detail.Data.IsSharding {
		return b.fillShardingDatasource(ctx, ds)
	}
	return b.fillRDSMysqlDataSource(ctx, ds)

	//ds.Tunnel = consts.ByteDanceGatewayProtocol.String()
}

func (b *byterdsImpl) fillRDSMysqlDataSource(ctx context.Context, ds *shared.DataSource) error {
	cli, err := b.dsMgrBuilder.GetByRegion(ctx, ds.Region)
	if err != nil {
		log.Warn(ctx, "get dsmgr client error %s", err)
		return err
	}
	listResp, err := cli.ListDataSource(ctx, &dsmgrpb.ListDataSourceReq{
		Type:   dsmgrpb.EndpointByteDanceMySQL,
		Limit:  20,
		Offset: 0,
		Fileter: &dsmgrpb.ListDataSourceReq_ByterdsFileter{
			ByterdsFileter: &dsmgrpb.ListByteRDSFilter{
				Region:  ds.Region,
				Keyword: ds.InstanceId,
			},
		},
	})
	if err != nil {
		log.Warn(ctx, "list datasource error %s", err)
		return err
	}
	log.Info(ctx, "list datasource result %s", utils.Show(listResp))
	var dsGDID string
	for _, item := range listResp.DataSources {
		if item.Gdid != "" && item.Settings != nil && item.GetBytedanceMysqlSettings() != nil && item.GetBytedanceMysqlSettings().DbName == ds.Db {
			dsGDID = item.Gdid
			break
		}
	}
	if dsGDID == "" {
		return fmt.Errorf("can't find %v gdid", ds.InstanceId)
	}

	resp, err := cli.GetDataSource(ctx, &dsmgrpb.GetDataSourceReq{Gdid: dsGDID})
	if err != nil {
		return err
	}
	log.Info(ctx, "get datasource result %s", utils.Show(resp))
	//ret := &ConnectionInfo{GDID: resp.DataSource.Gdid}
	settings := resp.DataSource.GetBytedanceMysqlSettings()
	var proxyHost string
	var proxyPort int32
	if resp.DataSource.GetBytedanceMysqlSettings() != nil {
		for _, conn := range resp.DataSource.GetBytedanceMysqlSettings().Connections {
			for _, attr := range conn.Attributes {
				if attr.Value == dsmgrpb.ConnectionTypeMySQLProxy.String() {
					proxyHost = conn.Ip
					proxyPort = conn.Port
				}
			}
		}
	}

	if proxyHost == "" || proxyPort == 0 {
		log.Info(ctx, "lost proxy of bytedance db %v", dsGDID)
		return fmt.Errorf("lost proxy of bytedance db %v", dsGDID)
	}
	ds.Address = fmt.Sprintf("%s:%d", proxyHost, proxyPort)
	ds.Gdid = resp.DataSource.Gdid
	if psm := settings.GetConnByType(dsmgrpb.ConnectionTypeByteRDSWritePSM); psm != nil {
		ds.Psm = psm.GetIp()
	}
	return nil
}

func (b *byterdsImpl) fillShardingDatasource(ctx context.Context, ds *shared.DataSource) error {
	cli, err := b.dsMgrBuilder.GetByRegion(ctx, ds.Region)
	if err != nil {
		log.Warn(ctx, "get dsmgr client error %s", err)
		return err
	}

	listResp, err := cli.ListDataSource(ctx, &dsmgrpb.ListDataSourceReq{
		Type:   dsmgrpb.EndpointByteDanceShardingMySQL,
		Limit:  20,
		Offset: 0,
		Fileter: &dsmgrpb.ListDataSourceReq_ByterdsShardingFileter{
			ByterdsShardingFileter: &dsmgrpb.ListByteRDSShardingFilter{
				Region:  ds.Region,
				Keyword: ds.InstanceId,
			},
		},
	})
	if err != nil {
		log.Warn(ctx, "list datasource error %s", err)
		return err
	}
	log.Info(ctx, "list datasource result %s", utils.Show(listResp))
	var dsGDID string
	for _, item := range listResp.DataSources {
		if item.Gdid != "" && item.Settings != nil && item.GetBytedanceShardingMysqlSettings() != nil && item.GetBytedanceShardingMysqlSettings().DbName == ds.Db {
			dsGDID = item.Gdid
			break
		}
	}
	if dsGDID == "" {
		return fmt.Errorf("can't find %v gdid", ds.InstanceId)
	}
	resp, err := cli.GetDataSource(ctx, &dsmgrpb.GetDataSourceReq{Gdid: dsGDID})
	if err != nil {
		return err
	}
	log.Info(ctx, "get datasource result %s", utils.Show(resp))
	//ret := &ConnectionInfo{GDID: resp.DataSource.Gdid}
	settings := resp.DataSource.GetBytedanceShardingMysqlSettings()
	for _, conn := range settings.Connections {
		for _, attr := range conn.Attributes {
			if attr.Value == dsmgrpb.ConnectionTypeMySQLProxy.String() {
				ds.Address = fmt.Sprintf("%s:%d", conn.Ip, conn.Port)
				break
			}
			if attr.Value == dsmgrpb.ConnectionTypeByteRDSWritePSM.String() {
				ds.Psm = conn.Ip
			}
		}
	}
	ds.Gdid = resp.DataSource.Gdid
	log.Info(ctx, "sharding datasource %s", utils.Show(ds))
	if ds.Address == "" {
		log.Info(ctx, "lost proxy of bytedance db %v", dsGDID)
		return fmt.Errorf("lost proxy of bytedance db %v", dsGDID)
	}
	return nil
}

func (b *byterdsImpl) FillInnerDataSource(ctx context.Context, ds *shared.DataSource) error {
	panic("implement me")
}

func (b *byterdsImpl) CheckConn(ctx context.Context, ds *shared.DataSource) error {
	return nil
}

func (b *byterdsImpl) CheckDataSource(ctx context.Context, ds *shared.DataSource) error {
	panic("implement me")
}

func (b *byterdsImpl) KillQuery(ctx context.Context, ds *shared.DataSource, conn *shared.ConnectionInfo) error {
	return nil
}

func (b *byterdsImpl) ListInstance(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, "ListInstances panic, err=%v, stack=%s", r, string(debug.Stack()))
		}
	}()
	params := map[string]string{
		"region":    req.RegionId,
		"page":      utils.Int32ToStr(req.PageNumber - 1),
		"page_size": utils.Int32ToStr(req.PageSize),
		//"only_belong_byted_platform": "true", // 默认只展示字节云的实例
	}
	if req.InstanceId != "" {
		params["keyword"] = req.InstanceId
	}
	if req.UserId != "" {
		params["username"] = req.UserId
	}
	if req.Favor {
		params["favor"] = "1"
	}
	if req.Owned {
		params["owned"] = "true"
	}
	if req.DBEngineVersion != "" {
		params["db_type"] = req.DBEngineVersion
	}
	response, err := b.byteRDSHttpClient.GET(ctx, DBListAPI, params, nil, req.RegionId, false)
	if err != nil {
		log.Warn(ctx, "Call rds openAPI failed,err %s", err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if response == nil {
		log.Warn(ctx, "rds response is nil")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
	}
	dbList, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	dbData := &DBList{}
	if err := json.Unmarshal(dbList, dbData); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if dbData.Code != 0 {
		log.Warn(ctx, "Call rds openAPI failed,code %d err is %s", dbData.Code, dbData.Error)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
	}
	ret := &datasource.ListInstanceResp{}
	if err := fp.Stream0Of(dbData.DBs.Result).Map(func(info *Result) *model.InstanceInfo {
		ins := &model.InstanceInfo{
			InstanceId:      utils.StringRef(info.Dbname),
			InstanceName:    utils.StringRef(info.Dbname),
			InstanceStatus:  "Running", // 返回结果中不包含运行状态，默认为running
			Dbas:            strings.Split(info.DbaOwners, ","),
			Owners:          info.Owners,
			RegionId:        utils.StringRef(info.Region),
			DBEngineVersion: info.Engine,
			LinkType:        model.LinkType_ByteInner,
			PsmList:         info.Consuls,
		}
		if info.VolcInstanceId != "" {
			ins.InstanceType = classifyInstanceType(info.VolcInstanceId)
			ins.InstanceId = utils.StringRef(info.VolcInstanceId)
		} else {
			ins.InstanceType = model.InstanceType_ByteRDS
		}
		return ins
	}).ToSlice(&ret.InstanceList); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	ret.Total = int64(dbData.DBs.Total)
	log.Info(ctx, "ListInstance is %s", utils.Show(ret))
	return ret, nil
}

func (b *byterdsImpl) ListDatabases(ctx context.Context, req *datasource.ListDatabasesReq) (*datasource.ListDatabasesResp, error) {
	return mysql.ListDatabases(ctx, req)
}

func (b *byterdsImpl) ListInstanceNodes(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesResp, error) {
	var nodeList []*model.NodeInfoObject
	if req.RegionId == "" {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	ipList, err := b.GetInstanceDBEngine(ctx, req.InstanceId, req.RegionId)
	if err != nil {
		return nil, err
	}
	if len(ipList) < 1 {
		log.Warn(ctx, "mysql ip ist is Null")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "mysql ip list is Null")
	}
	for _, instInfo := range ipList {
		for _, addr := range instInfo.IpList {
			tmp := &model.NodeInfoObject{}
			if addr.IPv4 != "" {
				tmp.NodeId = fmt.Sprintf("%s:%d", addr.IPv4, addr.Port)
			} else {
				tmp.NodeId = fmt.Sprintf("[%s]:%d", addr.IPv6, addr.Port)
			}
			switch addr.Role {
			case "master":
				tmp.NodeType = model.NodeType_Primary
			case "slave":
				tmp.NodeType = model.NodeType_Secondary
			default:
				tmp.NodeType = model.NodeType_Primary
			}
			nodeList = append(nodeList, tmp)
		}
	}
	return &datasource.ListInstanceNodesResp{Nodes: nodeList}, nil
}

func (b *byterdsImpl) ListInstancePods(ctx context.Context, req *datasource.ListInstancePodsReq) (*datasource.ListInstancePodsResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeDBInstanceDetail(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (*datasource.DescribeDBInstanceDetailResp, error) {
	detail, err := b.getDBDetail(ctx, &GetDBDetailReq{
		Region: req.RegionId,
		DBName: req.InstanceId,
	})
	if err != nil {
		log.Warn(ctx, "get db detail error:%s", err)
		return nil, err
	}

	//switch detail.Data.Engine {
	//case "ndb":
	//case "mysql":
	//}
	return &datasource.DescribeDBInstanceDetailResp{
		InstanceStatus:  model.InstanceStatus_Running.String(),
		DBEngine:        detail.Data.Engine,
		DBEngineVersion: detail.Data.Version,
		ImportanceLevel: detail.Data.ImportanceLevel,
		Shared:          detail.Data.IsShared,
	}, nil
}

func (b *byterdsImpl) DescribeDBInstanceEndpoints(ctx context.Context, req *datasource.DescribeDBInstanceEndpointsReq) (*datasource.DescribeDBInstanceEndpointsResp, error) {
	ret := &datasource.DescribeDBInstanceEndpointsResp{
		Endpoints: []*datasource.EndpointInfo{
			{
				EndpointID:    fmt.Sprintf("toutiao.mysql.%s_read", req.InstanceId),
				ReadWriteMode: model.EndpointType_ReadOnly.String(),
			},
			{
				EndpointID:    fmt.Sprintf("toutiao.mysql.%s_write", req.InstanceId),
				ReadWriteMode: model.EndpointType_ReadWrite.String(),
			},
			{
				EndpointID:    fmt.Sprintf("toutiao.mysql.%s_offline", req.InstanceId),
				ReadWriteMode: model.EndpointType_Offline.String(),
			},
		},
	}
	return ret, nil
}

func (b *byterdsImpl) DescribeDBInstanceCluster(ctx context.Context, req *datasource.DescribeDBInstanceClusterReq) (*datasource.DescribeDBInstanceClusterResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeDBInstanceAuditCollectedPod(ctx context.Context, req *datasource.DescribeDBInstanceAuditCollectedPodReq) (*datasource.DescribeDBInstanceAuditCollectedPodResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) OpenDBInstanceAuditLog(ctx context.Context, req *datasource.OpenDBInstanceAuditLogReq) (*datasource.OpenDBInstanceAuditLogResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) CloseDBInstanceAuditLog(ctx context.Context, req *datasource.CloseDBInstanceAuditLogReq) (*datasource.CloseDBInstanceAuditLogResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) CheckDBInstanceAuditLogStatus(ctx context.Context, req *datasource.CheckDBInstanceAuditLogStatusReq) (*datasource.CheckDBInstanceAuditLogStatusResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeDBProxyConfig(ctx context.Context, req *datasource.DescribeDBProxyConfigReq) (*datasource.DescribeDBProxyConfigResp, error) {
	// 内场rds都带proxy
	return &datasource.DescribeDBProxyConfigResp{IsProxyEnable: true}, nil
}

func (b *byterdsImpl) DescribeInstanceFeatures(ctx context.Context, req *datasource.DescribeInstanceFeaturesReq) (*datasource.DescribeInstanceFeaturesResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeDBInstanceSSL(ctx context.Context, req *datasource.DescribeDBInstanceSSLReq) (*datasource.DescribeDBInstanceSSLResp, error) {
	return &datasource.DescribeDBInstanceSSLResp{
		SSLEnable: false,
	}, nil
}

func (b *byterdsImpl) DescribeSQLCCLConfig(ctx context.Context, req *datasource.DescribeSQLCCLConfigReq) (*datasource.DescribeSQLCCLConfigResp, error) {
	return &datasource.DescribeSQLCCLConfigResp{
		SQLConcurrencyControlStatus: false,
	}, nil
}

func (b *byterdsImpl) ModifySQLCCLConfig(ctx context.Context, req *datasource.ModifySQLCCLConfigReq) (*datasource.ModifySQLCCLConfigResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) AddSQLCCLRule(ctx context.Context, req *datasource.AddSQLCCLRuleReq) (*datasource.AddSQLCCLRuleResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ModifyProxyThrottleRule(ctx context.Context, req *datasource.ModifyProxyThrottleRuleReq) (*datasource.ModifyProxyThrottleRuleResp, error) {
	switch req.Action {
	case "Add":
		throttleRule := ThrottleRuleObject{
			ObjectID:          req.ProxyThrottleRule.ThrottleObjId,
			DB:                req.InstanceId,
			ProxyIngressType:  convEndpointTypeToProxyIngressType(req.ProxyThrottleRule.EndpointType),
			ProxyThrottleType: convThrottleTargetToProxyThrottleType(req.ProxyThrottleRule.ThrottleTarget),
			Proposer:          req.ProxyThrottleRule.UserID,
			ExpiredAt:         time.Now().UnixMilli() + req.ProxyThrottleRule.Duration*60*1000,
		}
		switch req.ProxyThrottleRule.ThrottleTarget {
		case model.ThrottleTarget_SqlQPS.String():
			throttleRule.SQLThrottle = SQLThrottle{
				SQL: req.ProxyThrottleRule.ThrottleSqlText,
				QPS: int64(req.ProxyThrottleRule.ThrottleThreshold),
			}
		case model.ThrottleTarget_KeywordQPS.String():
			throttleRule.KeywordThrottle = KeywordThrottle{
				Keyword: req.ProxyThrottleRule.Keywords,
				QPS:     int64(req.ProxyThrottleRule.ThrottleThreshold),
			}
		case model.ThrottleTarget_FingerQPS.String():
			throttleRule.FingerThrottle = FingerThrottle{
				Fingers: []string{req.ProxyThrottleRule.ThrottleFingerPrint},
				QPS:     int64(req.ProxyThrottleRule.ThrottleThreshold),
			}
		case model.ThrottleTarget_PsmDbQPS.String():
			throttleRule.PSMThrottle = PSMThrottle{
				PSMList: strings.Split(req.ProxyThrottleRule.ThrottleHost, ","),
				QPS:     int64(req.ProxyThrottleRule.ThrottleThreshold),
			}
		case model.ThrottleTarget_GroupQPS.String():
			groupIds := stringToInt64Slice(strings.Split(req.ProxyThrottleRule.GroupIds, ","))
			throttleRule.ShardThrottle = ShardThrottle{
				GroupIDs: groupIds,
				QPS:      int64(req.ProxyThrottleRule.ThrottleThreshold),
			}
		case model.ThrottleTarget_FrontQPS.String():
			throttleRule.TotalQPSThrottles = TotalQPSThrottles{
				QPS: int64(req.ProxyThrottleRule.ThrottleThreshold),
			}
		case model.ThrottleTarget_MaxFrontConn.String():
			throttleRule.FrontThrottle = FrontThrottle{
				ConnectNum: int64(req.ProxyThrottleRule.ThrottleThreshold),
			}
		case model.ThrottleTarget_MaxEndConn.String():
			throttleRule.BackThrottle = BackThrottle{
				ConnectNum: int64(req.ProxyThrottleRule.ThrottleThreshold),
			}
		case model.ThrottleTarget_FingerProhibitionQPS.String():
			throttleRule.FingerProhibition = FingerProhibition{
				Fingers: []string{req.ProxyThrottleRule.ThrottleFingerPrint},
			}
		}
		log.Info(ctx, "throttleRule is %s", utils.Show(throttleRule))
		if err := b.createThrottleRule(ctx, req.RegionId, throttleRule); err != nil {
			return nil, err
		}
		return &datasource.ModifyProxyThrottleRuleResp{}, nil
	case "Delete":
		if err := b.stopThrottleRule(ctx, req.RegionId, []string{req.ProxyThrottleRule.ThrottleObjId}, req.InstanceId); err != nil {
			return nil, err
		}
		return &datasource.ModifyProxyThrottleRuleResp{}, nil
	default:
	}
	ret := &datasource.ModifyProxyThrottleRuleResp{}
	return ret, nil
}

func (b *byterdsImpl) DeleteSQLCCLRule(ctx context.Context, req *datasource.DeleteSQLCCLRuleReq) (*datasource.DeleteSQLCCLRuleResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) FlushSQLCCLRule(ctx context.Context, req *datasource.FlushSQLCCLRuleReq) (*datasource.FlushSQLCCLRuleResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListSQLCCLRules(ctx context.Context, req *datasource.ListSQLCCLRulesReq) (*datasource.ListSQLCCLRulesResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeSqlFingerPrintOrKeywords(ctx context.Context, req *datasource.DescribeSqlFingerPrintOrKeywordsReq) (*datasource.DescribeSqlFingerPrintOrKeywordsResp, error) {
	ret := &datasource.DescribeSqlFingerPrintOrKeywordsResp{}
	switch req.ObjectType {
	case "Keyword":
		ret.Keywords = strings.Join(Keywords(req.SqlText), " ")
	case "Fingerprint":
		ret.FingerPrint = Fingerprint(req.SqlText)
	default:
		ret.FingerPrint = Fingerprint(req.SqlText)
		ret.Keywords = strings.Join(Keywords(req.SqlText), " ")
	}
	return ret, nil
}

func (b *byterdsImpl) DescribeInstanceAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeInstanceAddressList(ctx context.Context, req *datasource.DescribeInstanceAddressReq) ([]*datasource.DescribeInstanceAddressResp, error) {
	//获取实例ip port
	var addressList []*datasource.DescribeInstanceAddressResp
	// 获取DBEngine ip port
	ipList, err := b.GetInstanceDBEngine(ctx, req.InstanceId, req.RegionId)
	if err != nil {
		return nil, err
	}
	if len(ipList) < 1 {
		log.Warn(ctx, "Mysql ip list is Null")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "mysql ip list is Null")
	}
	for _, instInfo := range ipList {
		for _, addr := range instInfo.IpList {
			ret := &datasource.DescribeInstanceAddressResp{}
			if addr.IPv4 != "" {
				ret.IP = addr.IPv4
				ret.Port = int32(addr.Port)
				ret.NodeId = fmt.Sprintf("%s:%d", addr.IPv4, addr.Port)
			} else {
				ret.IP = addr.IPv6
				ret.Port = int32(addr.Port)
				ret.NodeId = fmt.Sprintf("[%s]:%d", addr.IPv6, addr.Port)
			}
			ret.NodeType = addr.Role
			ret.Component = model.Component_DBEngine.String()
			addressList = append(addressList, ret)
		}
	}
	// 获取Proxy ip port
	ipProxyList, err := b.GetInstanceProxy(ctx, req.RegionId, "mysql", req.InstanceId)
	if err != nil {
		return nil, err
	}
	for _, instInfo := range ipProxyList {
		for _, addr := range instInfo.IpList {
			ret := &datasource.DescribeInstanceAddressResp{}
			if addr.IPv4 != "" {
				ret.IP = addr.IPv4
				ret.Port = int32(addr.Port)
				ret.NodeId = fmt.Sprintf("%s:%d", addr.IPv4, addr.Port)
			} else {
				ret.IP = addr.IPv6
				ret.Port = int32(addr.Port)
				ret.NodeId = fmt.Sprintf("[%s]:%d", addr.IPv6, addr.Port)
			}
			ret.NodeType = addr.Role
			ret.Component = model.Component_Proxy.String()
			addressList = append(addressList, ret)
		}
	}
	return addressList, nil
}

func (b *byterdsImpl) ListTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	return mysql.ListTables(ctx, req)
}

func (b *byterdsImpl) ListTablesInfo(ctx context.Context, req *datasource.ListTablesInfoReq) (*datasource.ListTablesInfoResp, error) {
	resp, err := mysql.ListTablesInfo(ctx, &datasource.ListTablesReq{
		Source: req.Source,
		DB:     req.Database,
		Offset: int64(0),
		Limit:  int64(1000),
	})
	if err != nil {
		return nil, err
	}
	var tablesInfo []*datasource.TableInfo
	columns, err := mysql.GetDBColumns(ctx, &datasource.DescribeTableReq{
		Source: req.Source,
		DB:     req.Database,
	})
	for _, table := range resp.Tables {
		tableInfo := &datasource.TableInfo{
			Name:    table.Name,
			Comment: table.Comment,
		}
		for _, col := range columns {
			if table.Name == col.TABLE_NAME {
				c := &shared.TableInfo_ColumnInfo{
					Name:    col.COLUMN_NAME,
					Comment: col.COLUMN_COMMENT,
				}
				tableInfo.Columns = append(tableInfo.Columns, c)
			}
		}
		tablesInfo = append(tablesInfo, tableInfo)
	}
	return &datasource.ListTablesInfoResp{
		Tables: tablesInfo,
	}, nil

}

func (b *byterdsImpl) DescribeTable(ctx context.Context, req *datasource.DescribeTableReq) (*datasource.DescribeTableResp, error) {
	return mysql.GetTableMeta(ctx, req)
}

func (b *byterdsImpl) DescribeAutoKillSessionConfig(ctx context.Context, req *datasource.DescribeAutoKillSessionConfigReq) (*datasource.DescribeAutoKillSessionConfigResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ModifyAutoKillSessionConfig(ctx context.Context, req *datasource.ModifyAutoKillSessionConfigReq) (*datasource.ModifyAutoKillSessionConfigResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeInstanceVersion(ctx context.Context, req *datasource.DescribeInstanceVersionReq) (*datasource.DescribeInstanceVersionResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListErrLogs(ctx context.Context, req *datasource.ListErrLogsReq) (*datasource.ListErrLogsResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribePgTable(ctx context.Context, req *datasource.DescribePgTableReq) (*datasource.DescribePgTableResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeInstanceReplicaDelay(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (int64, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListViews(ctx context.Context, req *datasource.ListViewsReq) (*datasource.ListViewsResp, error) {
	return mysql.ListViews(ctx, req)
}

func (b *byterdsImpl) DescribeView(ctx context.Context, req *datasource.DescribeViewReq) (*datasource.DescribeViewResp, error) {
	return mysql.DescribeView(ctx, req)
}

func (b *byterdsImpl) DescribeFunction(ctx context.Context, req *datasource.DescribeFunctionReq) (*datasource.DescribeFunctionResp, error) {
	return mysql.DescribeFunction(ctx, req)
}

func (b *byterdsImpl) DescribeProcedure(ctx context.Context, req *datasource.DescribeProcedureReq) (*datasource.DescribeProcedureResp, error) {
	return mysql.DescribeProcedure(ctx, req)
}

func (b *byterdsImpl) ListFunctions(ctx context.Context, req *datasource.ListFunctionsReq) (*datasource.ListFunctionsResp, error) {
	return mysql.ListFunctions(ctx, req)
}

func (b *byterdsImpl) ListProcedures(ctx context.Context, req *datasource.ListProceduresReq) (*datasource.ListProceduresResp, error) {
	return mysql.ListProcedures(ctx, req)
}

func (b *byterdsImpl) ListTriggers(ctx context.Context, req *datasource.ListTriggersReq) (*datasource.ListTriggersResp, error) {
	return mysql.ListTriggers(ctx, req)
}

func (b *byterdsImpl) DescribeTLSConnectionInfo(ctx context.Context, req *datasource.DescribeTLSConnectionInfoReq) (*datasource.DescribeTLSConnectionInfoResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListKeyNumbers(ctx context.Context, req *datasource.ListKeyNumbersReq) (*datasource.ListKeyNumbersResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListKeys(ctx context.Context, req *datasource.ListKeysReq) (*datasource.ListKeysResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetKey(ctx context.Context, req *datasource.GetKeyReq) (*datasource.GetKeyResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListKeyMembers(ctx context.Context, req *datasource.ListKeyMembersReq) (*datasource.ListKeyMembersResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListAlterKVsCommands(ctx context.Context, req *datasource.ListAlterKVsCommandsReq) (*datasource.ListAlterKVsCommandsResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) OpenTunnel(ctx context.Context, ds *shared.DataSource, TunnelID string) error {
	return nil
}

func (b *byterdsImpl) DescribeTrigger(ctx context.Context, req *datasource.DescribeTriggerReq) (*datasource.DescribeTriggerResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeEvent(ctx context.Context, req *datasource.DescribeEventReq) (*datasource.DescribeEventResp, error) {
	return mysql.DescribeEvent(ctx, req)
}

func (b *byterdsImpl) ListEvents(ctx context.Context, req *datasource.ListEventsReq) (*datasource.ListEventsResp, error) {
	return mysql.ListEvents(ctx, req)
}

func (b *byterdsImpl) CreateAccount(ctx context.Context, req *datasource.CreateAccountReq) error {
	panic("implement me")
}

func (b *byterdsImpl) CheckPrivilege(ctx context.Context, instanceId, dbName, accountName, priv string, dsType shared.DataSourceType) (bool, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeAccounts(ctx context.Context, req *datasource.DescribeAccountsReq) (*datasource.DescribeAccountResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeAccounts2(ctx context.Context, req *datasource.DescribeAccountsReq) (*datasource.DescribeAccountResp2, error) {
	panic("implement me")
}

func (b *byterdsImpl) CreateAccountAndGrant(ctx context.Context, instanceId, accountName, password, dbName, priv string, dsType shared.DataSourceType) error {
	panic("implement me")
}

func (b *byterdsImpl) ModifyAccountPrivilege(ctx context.Context, req *datasource.ModifyAccountPrivilegeReq) error {
	panic("implement me")
}

func (b *byterdsImpl) GrantAccountPrivilege(ctx context.Context, req *datasource.GrantAccountPrivilegeReq) error {
	panic("implement me")
}

func (b *byterdsImpl) DeleteAccount(ctx context.Context, req *datasource.DeleteAccountReq) error {
	panic("implement me")
}

func (b *byterdsImpl) ListDatabasesWithAccount(ctx context.Context, req *datasource.ListDatabasesWithAccountReq) (*datasource.ListDatabasesWithAccountResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetAdvice(ctx context.Context, req *datasource.GetAdviceReq) (*datasource.GetAdviceResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListCharsets(ctx context.Context, req *datasource.ListCharsetsReq) (*datasource.ListCharsetsResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListCollations(ctx context.Context, req *datasource.ListCollationsReq) (*datasource.ListCollationsResp, error) {
	return mysql.ListCollations(ctx, req)
}

func (b *byterdsImpl) ListSchema(ctx context.Context, req *datasource.ListSchemaReq) (*datasource.ListSchemaResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListSequence(ctx context.Context, req *datasource.ListSequenceReq) (*datasource.ListSequenceResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListPgCollations(ctx context.Context, req *datasource.ListPgCollationsReq) (*datasource.ListPgCollationsResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListPgUsers(ctx context.Context, req *datasource.ListPgUsersReq) (*datasource.ListPgUsersResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListTableSpaces(ctx context.Context, req *datasource.ListTableSpacesReq) (*datasource.ListTableSpacesResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeDialogDetails(ctx context.Context, req *datasource.DescribeDialogDetailsReq) (*datasource.DescribeDialogDetailsResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeDialogStatistics(ctx context.Context, req *datasource.DescribeDialogStatisticsReq) (*datasource.DescribeDialogStatisticsResp, error) {
	dialogInfos, err := b.getAllDialogInfos(ctx, &datasource.DescribeDialogInfosReq{
		Source:        req.Source,
		Component:     req.Component,
		QueryFilter:   req.QueryFilter,
		InternalUsers: req.InternalUsers,
	})
	if err != nil {
		return nil, err
	}
	// get aggregated info
	statistics := b.getDialogStatistics(ctx, dialogInfos, req.TopN)

	ret := &datasource.DescribeDialogStatisticsResp{
		DialogStatistics: statistics,
	}

	return ret, nil
}

func (b *byterdsImpl) DescribeEngineStatus(ctx context.Context, req *datasource.DescribeEngineStatusReq) (*datasource.DescribeEngineStatusResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) KillProcess(ctx context.Context, req *datasource.KillProcessReq) (*datasource.KillProcessResp, error) {
	if len(req.ProcessIDs) < 1 {
		return &datasource.KillProcessResp{}, nil
	}
	params := map[string]string{
		"dbname": req.Source.InstanceId,
	}
	var processList []int32
	for _, process := range req.ProcessIDs {
		procId, err := strconv.ParseInt(process, 10, 64)
		if err != nil {
			log.Warn(ctx, "convert %s str2int32 failed %s", process, err)
			continue
		}
		processList = append(processList, int32(procId))
	}
	if req.NodeId == "" {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	lastColonIndex := strings.LastIndex(req.NodeId, ":")
	ipPart := req.NodeId[:lastColonIndex]
	port := req.NodeId[lastColonIndex+1:]
	convertPort, err := strconv.ParseInt(port, 10, 64)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("Invalid Port %s", port))
	}
	if strings.HasPrefix(ipPart, "[") && strings.HasSuffix(ipPart, "]") {
		ipPart = ipPart[1 : len(ipPart)-1]
	}
	// 解析IP地址以处理可能的简写形式,合法的ipv6格式2605:340:cd50:2000:5b0f:e6de:9b7a:10db,需要手动将NodeId中的[]去掉
	ip := net.ParseIP(ipPart)
	if ip == nil {
		log.Warn(ctx, "Invalid IP address")
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("Invalid IP %s", ipPart))
	}
	body := &KillProcessBody{
		//: req.ProcessIDs,
		Region:       req.Region,
		ProcessList:  processList,
		InstanceIp:   ip.String(),
		InstancePort: int32(convertPort),
	}
	log.Info(ctx, "body is %s", utils.Show(body))
	apiPath := fmt.Sprintf(KillProcessAPI, req.Source.InstanceId)
	response, err := b.byteRDSHttpClient.POST(ctx, apiPath, params, nil, body, req.Region, false)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if response == nil {
		log.Warn(ctx, "response is nil")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "response is nil")
	}
	killProResp, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	log.Info(ctx, "killProResp is %s", string(killProResp))
	dbData := &KillProcessResp{}
	if err := json.Unmarshal(killProResp, dbData); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if dbData.Code != 0 {
		log.Warn(ctx, "Call rds openAPI failed,code %d err is %s", dbData.Code, dbData.Error)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
	}
	return &datasource.KillProcessResp{}, nil
}

func (b *byterdsImpl) DescribeTrxAndLocks(ctx context.Context, req *datasource.DescribeTrxAndLocksReq) (*datasource.DescribeTrxAndLocksResp, error) {
	// 直连主节点查询
	var (
		masterNode     []string
		TrxAndLockList []*shared.TrxAndLock
		querySql       string
	)
	//orderMap := map[string]string{
	//	"TrxStartTime":     "trx_started",
	//	"TrxWaitStartTime": "trx_wait_started",
	//	"TrxTablesLocked":  "trx_tables_locked",
	//	"TrxRowsLocked":    "trx_rows_locked",
	//	"TrxRowsModified":  "trx_rows_modified",
	//}
	rrSource := req.Source
	// 支持过滤节点Id(需要为主节点)
	if len(req.NodeIds) > 0 {
		masterNode = req.NodeIds
	} else {
		ipList, err := b.GetInstanceDBEngine(ctx, req.Source.InstanceId, req.Source.Region)
		if err != nil {
			return nil, err
		}
		for _, instInfo := range ipList {
			for _, addr := range instInfo.IpList {
				var (
					ipAddr string
					nodeId string
				)
				if addr.Role == "master" {
					if addr.IPv4 != "" {
						ipAddr = addr.IPv4
						nodeId = fmt.Sprintf("%s:%d", ipAddr, addr.Port)
					} else {
						ipAddr = addr.IPv6
						nodeId = fmt.Sprintf("[%s]:%d", ipAddr, addr.Port)
					}
					log.Info(ctx, "mysql master addr is %s", nodeId)
					masterNode = append(masterNode, nodeId)
					break
				}
			}
		}
	}
	ret := &datasource.DescribeTrxAndLocksResp{}

	whereConditions, args := generateTrxAndLockSearchWhereConditions(req.SearchParam)
	/* sort */
	if req.SortParam != "<UNSET>" {
		whereConditions += ` order by ` + req.SortParam + " " + req.Order
	}

	//sql += ` LIMIT ? OFFSET ?`
	//args = append(args, req.Limit, req.Offset)
	for _, nodeAddr := range masterNode {
		var tempTrxAndLockList []TrxAndLock
		rrSource.Address = nodeAddr
		conn, err := b.getConn(ctx, rrSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
			continue // 跳过
		}
		// 查询mysql版本
		rdsVersion := b.getRdsVersion(ctx, conn)
		switch rdsVersion {
		case "MySQL_5_7":
			querySql = MySQL57TrxAndLockQuery + whereConditions // ignore_security_alert
		case "MySQL_8_0":
			querySql = MySQL80TrxAndLockQuery + whereConditions // ignore_security_alert
		}
		log.Info(ctx, "execute trx sql is %s", querySql)
		log.Info(ctx, "rds_by_add_rrSource:%v", commonUtils.Show(rrSource))
		if err = conn.Raw(querySql, args...).Scan(&tempTrxAndLockList); err != nil {
			log.Warn(ctx, "execute trx_lock sql failed %s", err)
			conn.Close()
			continue
		}
		log.Info(ctx, "TrxAndLockList is %s", utils.Show(tempTrxAndLockList))
		/* add data */
		TrxAndLocklist2 := make([]*shared.TrxAndLock, 0)
		for _, TL := range tempTrxAndLockList {
			/* lockStatus */
			var lockStatus shared.LockStatus
			waitLockDetail := &shared.WaitLockDetail{}
			if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId != "" {
				lockStatus = shared.LockHoldAndWait
			} else if TL.TrxLockStructs == 0 && TL.TrxRequestedLockId != "" {
				lockStatus = shared.LockWait
			} else if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId == "" {
				lockStatus = shared.LockHold
			} else {
				lockStatus = shared.None
			}

			/* TrxWaitStartTime maybe NULL */
			var trxWaitStartTime string
			if TL.TrxWaitStartTime.Format("2006-01-02 15:04:05") == "0001-01-01 00:00:00" {
				trxWaitStartTime = ""
			} else {
				trxWaitStartTime = TL.TrxWaitStartTime.Format("2006-01-02 15:04:05")
			}
			// 查询等待锁的详情
			if TL.TrxRequestedLockId != "" {
				waitLockDetail.RequestedLockId = TL.TrxRequestedLockId
			}
			lockList, err := b.describeLock(ctx, TL.TrxId, conn)
			if err != nil {
				log.Warn(ctx, "get lock fail: %v", err)
				return nil, err
			}
			lockList2 := make([]*shared.Lock, 0)
			for _, l := range lockList {
				lockList2 = append(lockList2, l)
			}

			TrxAndLocklist2 = append(TrxAndLocklist2, &shared.TrxAndLock{
				ProcessId:        TL.ProcessId,
				TrxId:            TL.TrxId,
				TrxStatus:        TL.TrxState,
				TrxIsoLevel:      TL.TrxIsoLevel,
				TrxStartTime:     TL.TrxStartTime.Format("2006-01-02 15:04:05"),
				TrxWaitStartTime: trxWaitStartTime,
				SqlBlocked:       TL.SqlBlocked,
				TrxTablesLocked:  TL.TrxTablesLocked,
				TrxRowsLocked:    TL.TrxRowsLocked,
				TrxRowsModified:  TL.TrxRowsModified,
				LockStatus:       lockStatus,
				LockList:         lockList2,
				NodeType:         model.NodeType_Primary.String(),
				NodeId:           rrSource.Address,
				BlockTrxId:       TL.BlockTrxId,
				TrxExecTime:      TL.TrxExecTime,
			})
		}
		TrxAndLockList = append(TrxAndLockList, TrxAndLocklist2...)

	}
	sortTrx(TrxAndLockList, shared.DESC, "TrxExecTime")
	ret.Result = &shared.DescribeTrxAndLocksInfo{
		TrxAndLockList: TrxAndLockList,
		Total:          int32(len(TrxAndLockList)),
	}
	return ret, nil
}

func (b *byterdsImpl) getRdsVersion(ctx context.Context, conn db.Conn) string {
	// 查询mysql内核版本
	var versionSql = "/*+ DBW DAS DEFAULT*/ select version() as version;"
	var MysqlVersion = DBVersion{}
	if err := conn.Raw(versionSql).Scan(&MysqlVersion); err != nil {
		log.Warn(ctx, "get mysql version fail %v", err)
		return "MySQL_5_7"
	}
	//log.Info(ctx, "mysql version is %v", MysqlVersion.Version)
	/* version = 5.7x */
	if strings.Contains(MysqlVersion.Version, "5.7") {
		return "MySQL_5_7"
	}
	if strings.Contains(MysqlVersion.Version, "8.0") {
		return "MySQL_8_0"
	} else {
		return "MySQL_5_7" // 其他情况默认按5.7内核版本查询
	}
}
func (b *byterdsImpl) describeLock(ctx context.Context, TrxId string, conn db.Conn) ([]*shared.Lock, error) {
	ret := make([]*shared.Lock, 0)

	/* get mysql version info */
	var versionSql = "/*+ DBW DAS DEFAULT*/ select version() as version;"
	var MysqlVersion = DBVersion{}
	if err := conn.Raw(versionSql).Scan(&MysqlVersion); err != nil {
		log.Warn(ctx, "get mysql version of  fail %v", err)
		return nil, err
	}
	//log.Info(ctx, "mysql version is  %v", MysqlVersion.Version)

	/* version = 5.7x */
	if strings.Contains(MysqlVersion.Version, "5.7") {
		var Locklist []Lock57
		sql := fmt.Sprintf("/*+ DBW DAS DEFAULT*/ select * from information_schema.INNODB_LOCKS where lock_trx_id='%s';", TrxId)
		if err := conn.Raw(sql).Scan(&Locklist); err != nil {
			return nil, err
		}
		LockList2 := make([]*shared.Lock, 0)
		for _, L := range Locklist {
			var lockProperty string
			var Numlockwait int64
			sql1 := fmt.Sprintf("/*+ DBW DAS DEFAULT*/ select count(1) from information_schema.INNODB_LOCK_WAITS where requested_lock_id ='%s';", L.LockId)
			if err := conn.Raw(sql1).Scan(&Numlockwait); err != nil {
				return nil, err
			}
			if Numlockwait == 0 {
				lockProperty = model.Lockstatus_LockHold.String()
			} else {
				lockProperty = model.Lockstatus_LockWait.String()
			}
			LockList2 = append(LockList2, &shared.Lock{
				LockProperty:       lockProperty,
				LockId:             L.LockId,
				LockAssociateIndex: L.LockAssociateIndex,
				LockAssociateTable: L.LockAssociateTable,
				LockType:           L.LockType,
				LockModel:          L.LockModel,
			})
		}
		ret = LockList2
	}

	/* version = 8.0x */
	if strings.Contains(MysqlVersion.Version, "8.0") {
		var Locklist []Lock80
		sql := fmt.Sprintf("/*+ DBW DAS DEFAULT*/ select * from performance_schema.data_locks where ENGINE_TRANSACTION_ID='%s';", TrxId)
		if err := conn.Raw(sql).Scan(&Locklist); err != nil {
			return nil, err
		}
		LockList2 := make([]*shared.Lock, 0)
		for i := 0; i < len(Locklist); i++ {
			if i == len(Locklist)-1 || (Locklist[i].LockBegin != Locklist[i+1].LockBegin) {
				LockList2 = append(LockList2, &shared.Lock{
					LockProperty:       Locklist[i].LockProperty,
					LockId:             Locklist[i].LockId,
					LockAssociateIndex: Locklist[i].LockAssociateIndex,
					LockAssociateTable: Locklist[i].LockAssociateTable,
					LockType:           Locklist[i].LockType,
					LockModel:          Locklist[i].LockModel,
				})
			}
		}
		ret = LockList2
	}
	return ret, nil
}

func (b *byterdsImpl) DescribeDeadlock(ctx context.Context, req *datasource.DescribeDeadlockReq) (*datasource.DescribeDeadlockResp, error) {
	// 直连主节点查询
	var masterNode []string
	rrSource := req.Source
	// 支持过滤节点Id(需要为主节点)
	if len(req.NodeId) > 0 {
		masterNode = req.NodeId
	} else {
		ipList, err := b.GetInstanceDBEngine(ctx, req.Source.InstanceId, req.Source.Region)
		if err != nil {
			return nil, err
		}
		for _, instInfo := range ipList {
			for _, addr := range instInfo.IpList {
				var (
					ipAddr string
					nodeId string
				)
				if addr.Role == "master" {
					if addr.IPv4 != "" {
						ipAddr = addr.IPv4
						nodeId = fmt.Sprintf("%s:%d", ipAddr, addr.Port)
					} else {
						ipAddr = addr.IPv6
						nodeId = fmt.Sprintf("[%s]:%d", ipAddr, addr.Port)
					}
					log.Info(ctx, "mysql master addr is %s", nodeId)
					masterNode = append(masterNode, nodeId)
					break
				}
			}
		}
	}
	ret := &datasource.DescribeDeadlockResp{}
	var deadLockList []*shared.DeadlockInfo
	/* get engine innodb status */
	command := datasource.DbwDasHint + "show engine innodb status;"
	/* DeadLock Info Str */
	reg := regexp.MustCompile(`LATEST DETECTED DEADLOCK\n------------------------\n([\s\S]*)------------\nTRANSACTIONS\n`)
	for _, nodeAddr := range masterNode {
		rrSource.Address = nodeAddr
		conn, err := b.getConn(ctx, rrSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
			continue // 跳过
		}
		var statusinfo StatusInfo
		if err = conn.Raw(command).Scan(&statusinfo); err != nil {
			conn.Close()
			continue // 跳过
		}
		if len(reg.FindStringSubmatch(statusinfo.Status)) == 0 {
			log.Info(ctx, "there are no new Deadlock")
			res, err := b.getDeadLock(ctx, nil, time.Now().Unix(), req.InstanceId, req.TenantId, nodeAddr)
			if err != nil {
				log.Warn(ctx, "Current Node %s get Deadlock info fail:%v", nodeAddr, err)
				continue // 跳过
			}
			deadLockList = append(deadLockList, res...)
			continue
		}

		DLInfoStr := reg.FindStringSubmatch(statusinfo.Status)[len(reg.FindStringSubmatch(statusinfo.Status))-1]
		/* get two time */
		DeadlockCollectionTime := time.Now().Format("2006-01-02 15:04:05")
		reg = regexp.MustCompile(`(\d{4}-\d{2}-\d{2}.{9})`)
		DeadlockTime := reg.FindAllString(DLInfoStr, -1)[0]

		regs := []string{
			`MySQL thread id (\d*),`,
			`table (.*) trx id`,
			`LOCK TO BE GRANTED:\n(.*) space`,
			`LOCK TO BE GRANTED:\n.*index (.*) of`,
			`LOCK TO BE GRANTED:\n.* lock.mode (\w*)`,
			`HOLDS THE LOCK\(S\):\n(.*) space`,
			`HOLDS THE LOCK\(S\):\n.*index (.*) of`,
			`HOLDS THE LOCK\(S\):\n.* lock.mode (\w*)`,
			`MySQL.*\n([\s\S]*?)\*\*\* \([1-9]\)`,
		}

		/* get every trx and its lock */
		var DeadlockList2 []*shared.Deadlock
		reg = regexp.MustCompile(consts.DeadlokcReg)
		trxs := reg.FindAllString(DLInfoStr, -1)

		for i, trx := range trxs {
			dl := &shared.Deadlock{}
			results := GetRegResults(trx, regs)

			dl.TrxInfo = strconv.Itoa(i + 1)
			dl.ProcessId = results[0]
			dl.RelateTable = results[1]
			dl.WaitLock = results[2]
			dl.WaitIndex = results[3]
			dl.WaitLockMode = results[4]
			dl.HoldLock = results[5]
			dl.HoldLockIndex = results[6]
			dl.HoldLockMode = results[7]
			dl.Sql = results[8]
			dl.ReqType = GetSqlType(dl.Sql)
			reg = regexp.MustCompile(`WE (.*) TRANSACTION \((\d)\)`)
			if reg.FindStringSubmatch(DLInfoStr) != nil && len(reg.FindStringSubmatch(DLInfoStr)) == 3 {
				if trxnum := reg.FindStringSubmatch(DLInfoStr)[2]; trxnum == strconv.Itoa(i+1) {
					dl.TrxTreat = reg.FindStringSubmatch(DLInfoStr)[1] + " TRANSACTION " + trxnum
				} else {
					dl.TrxTreat = ""
				}
			} else {
				dl.TrxTreat = ""
			}
			DeadlockList2 = append(DeadlockList2, dl)
		}
		DeadlockInfo2 := &shared.DeadlockInfo{
			DeadlockCollectionTime: DeadlockCollectionTime,
			DeadlockTime:           DeadlockTime,
			DeadlockList:           DeadlockList2,
			NodeId:                 nodeAddr,
		}
		res, err := b.getDeadLock(ctx, DeadlockInfo2, time.Now().Unix(), req.InstanceId, req.TenantId, nodeAddr)
		if err != nil {
			log.Warn(ctx, "Current Node %s get Deadlock info fail:%v", nodeAddr, err)
			continue // 跳过
		}
		deadLockList = append(deadLockList, res...)
	}
	ret.DescribeDeadlockInfo = &shared.DescribeDeadlockInfo{
		DeadlockInfoList: deadLockList,
	}
	return ret, nil
}
func (b *byterdsImpl) getDeadLock(ctx context.Context, DeadlockInfo *shared.DeadlockInfo, timestamp int64, InstanceId string, TenantId string, NodeId string) ([]*shared.DeadlockInfo, error) {
	/* get tls client */
	var (
		topicId  string
		querySql string
	)

	c3Cfg := b.conf.Get(ctx).C3Config
	Ak := c3Cfg.TLSServiceAccessKey
	Sk := c3Cfg.TLSServiceSecretKey
	regionId := c3Cfg.ByteInnerTLSServiceRegion
	tlsEndpoint := c3Cfg.ByteInnerTLSServiceEndpoint
	tlsClient := tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)
	tlsDeadlockTopic := c3Cfg.TLSDeadLockTopicV2
	topicSet := &datasource.TLSDeadlockTopic{}
	if err := json.Unmarshal([]byte(tlsDeadlockTopic), topicSet); err != nil {
		log.Warn(ctx, " tlsDeadlockTopic unmarshal failed %v", err)
	}
	for _, topic := range topicSet.Topics {
		if topic.InstanceType == model.DSType_ByteRDS.String() {
			topicId = topic.TopicID
		}
	}
	/* get data from tls */
	EndTime := time.Now().Unix()
	StartTime := EndTime - 86400
	log.Info(ctx, "start search logs from tls")
	if NodeId != "" {
		querySql = fmt.Sprintf("* | select DeadlockTime, content where InstanceId='%s' and TenantId='%s' and NodeId='%s' and DeadlockCollectionTime>%s", InstanceId, TenantId, NodeId, strconv.FormatInt(timestamp-86400, 10))
	} else {
		querySql = fmt.Sprintf("* | select DeadlockTime, content where InstanceId='%s' and TenantId='%s' and DeadlockCollectionTime>%s", InstanceId, TenantId, strconv.FormatInt(timestamp-86400, 10))

	}
	retLog, err := tlsClient.SearchLogsV2(&tls.SearchLogsRequest{
		TopicID:   topicId,
		Query:     querySql,
		StartTime: StartTime,
		EndTime:   EndTime,
		Limit:     1000,
		HighLight: false,
		Context:   "",
		Sort:      "",
	})
	if err != nil {
		log.Warn(ctx, "SearchLogs fail:%v", err)
		return nil, err
	}
	/* add data to ret */
	//ret := &shared.DescribeDeadlockInfo{}
	var ret []*shared.DeadlockInfo
	if retLog != nil && len(retLog.AnalysisResult.Data) != 0 {
		tempmp := map[string]string{}
		for i := 0; i < len(retLog.AnalysisResult.Data); i++ {
			if _, ok := tempmp[retLog.AnalysisResult.Data[i]["DeadlockTime"].(string)]; !ok {
				tempmp[retLog.AnalysisResult.Data[i]["DeadlockTime"].(string)] = "ok"
				DeadlockInfo2 := &shared.DeadlockInfo{}
				err = json.Unmarshal([]byte(retLog.AnalysisResult.Data[i]["content"].(string)), &DeadlockInfo2)
				if err != nil {
					log.Warn(ctx, "Unmarshal fail:%v", err)
					return nil, err
				}
				DeadlockInfo2.NodeId = NodeId
				ret = append(ret, DeadlockInfo2)
			}
		}
		if DeadlockInfo != nil {
			if _, ok := tempmp[DeadlockInfo.DeadlockTime]; !ok {
				ret = append(ret, DeadlockInfo)
			}
		}
	} else {
		if DeadlockInfo != nil {
			ret = append(ret, DeadlockInfo)
		}
	}

	/* put log to tls */
	if DeadlockInfo != nil {
		js, _ := json.Marshal(DeadlockInfo)
		_, err = tlsClient.PutLogs(&tls.PutLogsRequest{
			TopicID:      topicId,
			HashKey:      "",
			CompressType: "lz4",
			LogBody: &pb.LogGroupList{
				LogGroups: []*pb.LogGroup{
					{
						Logs: []*pb.Log{
							{
								Contents: []*pb.LogContent{
									{
										Key:   "InstanceId",
										Value: InstanceId,
									},
									{
										Key:   "DeadlockCollectionTime",
										Value: strconv.FormatInt(timestamp, 10),
									},
									{
										Key:   "DeadlockTime",
										Value: DeadlockInfo.DeadlockTime,
									},
									{
										Key:   "content",
										Value: string(js),
									},
									{
										Key:   "TenantId",
										Value: TenantId,
									},
									{
										Key:   "NodeId",
										Value: NodeId,
									},
								},
							},
						},
						Source:      "",
						LogTags:     nil,
						FileName:    "",
						ContextFlow: "",
					},
				},
			},
		})
		if err != nil {
			log.Warn(ctx, "put log to tls fail: %s", err)
			return nil, err
		}
	}
	log.Info(ctx, "success handle deadlock request")
	return ret, nil
}
func (b *byterdsImpl) DescribeDeadlockDetect(ctx context.Context, req *datasource.DescribeDeadlockDetectReq) (*datasource.DescribeDeadlockDetectResp, error) {
	// 直连主节点查询
	rrSource := req.Source
	ipList, err := b.GetInstanceDBEngine(ctx, req.Source.InstanceId, req.Source.Region)
	if err != nil {
		return nil, err
	}
	for _, instInfo := range ipList {
		for _, addr := range instInfo.IpList {
			var (
				ipAddr string
			)
			if addr.Role == "master" {
				if addr.IPv4 != "" {
					ipAddr = addr.IPv4
					rrSource.Address = fmt.Sprintf("%s:%d", ipAddr, addr.Port)
				} else {
					ipAddr = addr.IPv6
					rrSource.Address = fmt.Sprintf("[%s]:%d", ipAddr, addr.Port)
				}
				log.Info(ctx, "mysql master addr is %s", rrSource.Address)
				break
			}
		}
	}
	conn, err := b.getConn(ctx, rrSource)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	ret := &datasource.DescribeDeadlockDetectResp{}
	type T struct {
		OnOff int `gorm:"column:@@innodb_deadlock_detect"`
	}
	var t T
	sql := "/*+ DBW DAS DEFAULT*/ select @@innodb_deadlock_detect;"
	if err = conn.Raw(sql).Scan(&t); err != nil {
		log.Warn(ctx, "get Deadlock Detect fail %v", err)
		return nil, err
	}
	ret.DescribeDeadlockDetectInfo = &shared.DescribeDeadlockDetectInfo{
		OnOff: t.OnOff == 1,
	}
	return ret, nil
}

func (b *byterdsImpl) DescribeDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) (*datasource.DescribeDialogInfosResp, error) {
	if req.Offset < 0 || req.Limit < 0 {
		return &datasource.DescribeDialogInfosResp{}, nil
	}
	ret := &datasource.DescribeDialogInfosResp{}
	dialogInfos, err := b.getAllDialogInfos(ctx, req)
	if err != nil {
		return nil, err
	}
	// get details info
	ret.DialogDetails = b.filterDialogDetails(ctx, dialogInfos, req)
	return ret, nil
}

func (b *byterdsImpl) getDialogStatistics(ctx context.Context, data []*datasource.DialogInfo, topN int32) *shared.DialogStatistics {
	userInfo := make(map[string]*datasource.UserAggregatedInfo)
	ipInfo := make(map[string]*datasource.IPAggregatedInfo)
	dbInfo := make(map[string]*datasource.DBAggregatedInfo)
	psmInfo := make(map[string]*datasource.PSMAggregatedInfo)
	var activeConn, totalConn int32 = 0, 0
	// fill NULL column, count active/total conns, aggregate user/ip/db info
	fp.StreamOf(data).Foreach(func(d *datasource.DialogInfo) {
		totalConn += 1
		// 提取psm
		psmItem := extractPsm(d.Info.String)
		if _, ok := psmInfo[psmItem.psm]; !ok {
			psmInfo[psmItem.psm] = &datasource.PSMAggregatedInfo{PSM: psmItem.psm}
		}
		psmInfo[psmItem.psm].TotalConn += 1

		if _, ok := userInfo[d.User]; !ok {
			userInfo[d.User] = &datasource.UserAggregatedInfo{User: d.User}
		}
		userInfo[d.User].TotalConn += 1
		ip := datasource.ExtractIP(d.Host)
		log.Info(ctx, "ExtractIP former: %s, after: %s", d.Host, ip)
		if _, ok := ipInfo[ip]; !ok {
			ipInfo[ip] = &datasource.IPAggregatedInfo{IP: ip}
		}
		ipInfo[ip].TotalConn += 1
		if _, ok := dbInfo[d.DB]; !ok {
			dbInfo[d.DB] = &datasource.DBAggregatedInfo{DB: d.DB}
		}
		dbInfo[d.DB].TotalConn += 1
		if strings.ToLower(d.Command) != "sleep" {
			activeConn += 1
			userInfo[d.User].ActiveConn += 1
			ipInfo[ip].ActiveConn += 1
			dbInfo[d.DB].ActiveConn += 1
			psmInfo[psmItem.psm].ActiveConn += 1
		}
	}).Run()

	var userList []*shared.UserAggregatedInfo
	var ipList []*shared.IPAggregatedInfo
	var dbList []*shared.DBAggregatedInfo
	var psmList []*shared.PSMAggregatedInfo
	// sort aggregate info, take top 5
	fp.KVStreamOf(userInfo).ZipMap(func(k string, v *datasource.UserAggregatedInfo) *shared.UserAggregatedInfo {
		return &shared.UserAggregatedInfo{
			User:              v.User,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&userList)
	fp.KVStreamOf(ipInfo).ZipMap(func(k string, v *datasource.IPAggregatedInfo) *shared.IPAggregatedInfo {
		return &shared.IPAggregatedInfo{
			IP:                v.IP,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&ipList)
	fp.KVStreamOf(dbInfo).ZipMap(func(k string, v *datasource.DBAggregatedInfo) *shared.DBAggregatedInfo {
		return &shared.DBAggregatedInfo{
			DB:                v.DB,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&dbList)
	fp.KVStreamOf(psmInfo).ZipMap(func(k string, v *datasource.PSMAggregatedInfo) *shared.PSMAggregatedInfo {
		return &shared.PSMAggregatedInfo{
			PSM:               v.PSM,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&psmList)

	sort.Slice(userList, func(i, j int) bool {
		if userList[i].TotalConnections > userList[j].TotalConnections {
			return true
		}
		if userList[i].TotalConnections == userList[j].TotalConnections &&
			userList[i].ActiveConnections == userList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(ipList, func(i, j int) bool {
		if ipList[i].TotalConnections > ipList[j].TotalConnections {
			return true
		}
		if ipList[i].TotalConnections == ipList[j].TotalConnections &&
			ipList[i].ActiveConnections == ipList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(dbList, func(i, j int) bool {
		if dbList[i].TotalConnections > dbList[j].TotalConnections {
			return true
		}
		if dbList[i].TotalConnections == dbList[j].TotalConnections &&
			dbList[i].ActiveConnections == dbList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(psmList, func(i, j int) bool {
		if psmList[i].TotalConnections > psmList[j].TotalConnections {
			return true
		}
		if psmList[i].TotalConnections == psmList[j].TotalConnections &&
			psmList[i].ActiveConnections == psmList[j].ActiveConnections {
			return true
		}
		return false
	})

	return &shared.DialogStatistics{
		DialogOver: &shared.DialogOverview{
			ActiveConnections: activeConn,
			TotalConnections:  totalConn,
		},
		UserAggregatedInfo: userList[:fp.MinInt(int(topN), len(userList))],
		IPAggregatedInfo:   ipList[:fp.MinInt(int(topN), len(ipList))],
		DBAggregatedInfo:   dbList[:fp.MinInt(int(topN), len(dbList))],
		PSMAggregatedInfo:  psmList[:fp.MinInt(int(topN), len(psmList))],
	}
}
func (b *byterdsImpl) getAllDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) ([]*datasource.DialogInfo, error) {
	type NodeInfoObj struct {
		NodeId   string
		NodeType string
	}
	var (
		dialogInfos            []*datasource.DialogInfo
		nodeList, fullAllNodes []*NodeInfoObj
	)
	if req.Component == model.Component_DBEngine.String() {
		// DB侧会话
		// 获取engine ip: port信息
		ipList, err := b.GetInstanceDBEngine(ctx, req.Source.InstanceId, req.Source.Region)
		if err != nil {
			return nil, err
		}
		for _, instInfo := range ipList {
			for _, addr := range instInfo.IpList {
				var (
					ipAddr string
					nodeId string
				)
				if addr.IPv4 != "" {
					ipAddr = addr.IPv4
					nodeId = fmt.Sprintf("%s:%d", ipAddr, addr.Port)
				} else {
					ipAddr = addr.IPv6
					nodeId = fmt.Sprintf("[%s]:%d", ipAddr, addr.Port)
				}
				log.Info(ctx, "mysql addr is %s", nodeId)
				fullAllNodes = append(fullAllNodes, &NodeInfoObj{
					NodeId:   nodeId,
					NodeType: addr.Role,
				})
				break
			}
		}
		if len(req.QueryFilter.GetNodeIds()) > 0 {
			for _, nodeObj := range fullAllNodes {
				for _, nodeId := range req.QueryFilter.GetNodeIds() {
					if nodeId == nodeObj.NodeId {
						nodeList = append(nodeList, &NodeInfoObj{
							NodeId:   nodeObj.NodeId,
							NodeType: nodeObj.NodeType,
						})
						break
					}
				}
			}
		} else {
			nodeList = fullAllNodes
		}
		for _, nodeAddr := range nodeList {
			var (
				tempDialog []*datasource.DialogInfo
			)
			rrSource := req.Source
			rrSource.Address = nodeAddr.NodeId
			//log.Info(ctx, "mysql addr is %s", rrSource.Address)
			conn, err := b.getConn(ctx, rrSource)
			if err != nil {
				log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
				if conn != nil {
					_ = conn.Close()
				}
				return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
			}
			sql := "/*+ DBW DAS DEFAULT*/ select * from information_schema.processlist "
			log.Info(ctx, "sql is %s", sql)
			if err = conn.Raw(sql).Scan(&tempDialog); err != nil {
				_ = conn.Close()
				return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
			}
			_ = conn.Close()
			for _, dialog := range tempDialog {
				if strings.Contains(strings.Join(req.InternalUsers, ","), dialog.User) {
					dialog.User = "system_user" // 统一命名为system_user
				}
				dialog.NodeType = nodeAddr.NodeType
				dialog.NodeId = rrSource.Address
			}
			dialogInfos = append(dialogInfos, tempDialog...)
		}
		return dialogInfos, nil
	} else {
		// Proxy侧会话
		ipList, err := b.GetInstanceProxy(ctx, req.Source.Region, "mysql", req.Source.InstanceId)
		if err != nil {
			return nil, err
		}
		var proxyDialList []*datasource.VeDBProxyDialog
		for _, instInfo := range ipList {
			// 跳过htap
			if instInfo.Role == "htap" {
				continue
			}
			for _, addr := range instInfo.IpList {
				var (
					tmpDialInfo []*datasource.VeDBProxyDialog
					ipAddr      string
				)
				rrSource := req.Source
				if addr.IPv4 != "" {
					ipAddr = addr.IPv4
					rrSource.Address = fmt.Sprintf("%s:%d", ipAddr, addr.Port)
				} else {
					ipAddr = addr.IPv6
					rrSource.Address = fmt.Sprintf("[%s]:%d", ipAddr, addr.Port)
				}
				//log.Info(ctx, " proxy addr is %s", rrSource.Address)
				conn, err := b.getConn(ctx, rrSource)
				if err != nil {
					log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
					log.Warn(ctx, "conn is %+v", conn)
					if conn != nil {
						_ = conn.Close()
					}
					return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
				}
				sqlStr := "dbatman show processlist"
				//var dialogInfos []*DialogInfo
				if err = conn.Raw(sqlStr).Scan(&tmpDialInfo); err != nil {
					_ = conn.Close()
					return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
				}
				_ = conn.Close()
				for _, dialInfo := range tmpDialInfo {
					dialInfo.Port = utils.IntToStr(addr.Port)
					dialInfo.ProxyId = rrSource.GetAddress()
					dialInfo.ProxyName = instInfo.Consul
				}
				proxyDialList = append(proxyDialList, tmpDialInfo...)
			}
		}
		if err := fp.StreamOf(proxyDialList).Map(func(db *datasource.VeDBProxyDialog) *datasource.DialogInfo {
			dur, _ := time.ParseDuration(db.Time)
			elapsed := dur.Seconds()
			item := &datasource.DialogInfo{
				ProcessID: db.ProcessID,
				DB:        db.DB,
				Host:      db.Host,
				Command:   db.Command,
				User:      db.User,
				Time:      fmt.Sprintf("%f", elapsed),
				State:     "", // proxy不支持
				NodeId:    db.ProxyId,
				NodeType:  "Proxy",
				Info: sql.NullString{
					String: db.Command,
					Valid:  true,
				},
				BlockingPid: sql.NullString{
					String: "NULL",
					Valid:  false,
				},
			}
			if strings.Contains(strings.Join(req.InternalUsers, ","), db.User) {
				item.User = "system_user" // 统一命名为system_user
			}
			item.EndpointId = db.ProxyId
			item.EndpointName = db.ProxyName
			return item
		}).ToSlice(&dialogInfos); err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		return dialogInfos, nil
	}
}

func (b *byterdsImpl) filterDialogDetails(ctx context.Context, data []*datasource.DialogInfo, req *datasource.DescribeDialogInfosReq) *shared.DialogDetails {
	queryFilter := req.QueryFilter
	offset := req.Offset
	limit := req.Limit
	internalUsers := strings.Join(req.InternalUsers, ",")
	tData := data
	// filter dialog details if desired
	if queryFilter != nil {
		if queryFilter.GetShowSleepConnection() == "false" {
			fp.StreamOf(tData).Reject(func(d *datasource.DialogInfo) bool {
				if strings.ToLower(d.Command) == "sleep" {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if pID := queryFilter.ProcessID; pID != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				if strings.Contains(d.ProcessID, pID) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if user := queryFilter.User; user != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				if strings.Contains(d.User, user) || (user == "system_user" && strings.Contains(internalUsers, d.User)) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if host := queryFilter.Host; host != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				if strings.Contains(d.Host, host) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if fDB := queryFilter.DB; fDB != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				if strings.Contains(d.DB, fDB) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if command := queryFilter.Command; command != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				if strings.Contains(d.Command, command) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if state := queryFilter.State; state != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				if strings.Contains(d.State, state) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if timeLimit := queryFilter.LowerExecTimeLimit; timeLimit != "" {
			//limitInt, er := strconv.Atoi(queryFilter.LowerExecTimeLimit)
			limitFloat, er := strconv.ParseFloat(queryFilter.LowerExecTimeLimit, 64)
			if er == nil {
				fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
					execTime, _ := strconv.ParseFloat(d.Time, 64)
					if execTime >= limitFloat {
						return true
					}
					return false
				}).ToSlice(&tData)
			}
		}
		if info := queryFilter.Info; info != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				if strings.Contains(d.Info.String, info) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if nodeId := queryFilter.NodeId; nodeId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				if strings.Contains(d.NodeId, nodeId) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}

		if nodeType := queryFilter.NodeType; nodeType != "" {
			switch nodeType {
			case model.NodeType_Primary.String():
				nodeType = "master"
			case model.NodeType_Secondary.String():
				nodeType = "slave"
			default:
				nodeType = "master"
			}
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				if strings.Contains(d.NodeType, nodeType) {
					return true
				}
				return false
			}).ToSlice(&tData)
		}
		if psm := queryFilter.PSM; psm != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				targetPSM := extractPsm(d.Info.String)
				return strings.Contains(targetPSM.psm, psm)
			}).ToSlice(&tData)
		}
		// 过滤多节点(支持shard)
		if nodeIdList := queryFilter.NodeIds; len(nodeIdList) > 0 {
			fp.StreamOf(tData).Filter(func(d *datasource.DialogInfo) bool {
				var existed bool
				for _, nodeId := range nodeIdList {
					if d.NodeId == nodeId {
						existed = true
						break
					}
				}
				return existed
			}).ToSlice(&tData)
		}
	}
	// 默认按Time倒序
	datasource.SortDialog(tData, shared.DESC, "Time")
	var details []*shared.DialogDetail
	fp.StreamOf(tData[offset:fp.MinInt64(offset+limit, int64(len(tData)))]).
		Map(func(d *datasource.DialogInfo) *shared.DialogDetail {
			psmItem := extractPsm(d.Info.String)
			return &shared.DialogDetail{
				ProcessID:    d.ProcessID,
				User:         d.User,
				Host:         d.Host,
				DB:           d.DB,
				Command:      d.Command,
				Time:         d.Time,
				State:        d.State,
				Info:         d.Info.String,
				BlockingPid:  d.BlockingPid.String,
				NodeId:       d.NodeId,
				NodeType:     d.NodeType,
				PSM:          psmItem.psm,
				EndpointName: d.EndpointName,
				EndpointId:   d.EndpointId,
			}
		}).ToSlice(&details)

	return &shared.DialogDetails{
		Details: details,
		Total:   int32(len(tData)),
	}
}

func (b *byterdsImpl) DescribeTableSpace(ctx context.Context, req *datasource.DescribeTableSpaceReq) (*shared.DescribeTableSpaceResp, error) {
	conn, err := b.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	// tableInfo
	whereCase := " WHERE tables.TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'byte_rds_meta') AND tables.TABLE_TYPE = 'BASE TABLE' "

	//过滤只获取临时表的逻辑
	if req.TableType == "TEMP" {
		whereCase += fmt.Sprintf(" and TABLE_NAME like '%s'", "\\_%_del")
	}

	log.Info(ctx, "DescribeTableSpace-whereCase %s ", whereCase)

	sumSpace, err := datasource.GetSumSpace(conn, whereCase)
	if err != nil {
		return nil, err
	}
	tableInfoList, err := datasource.GetInformationSchemaTablesInfo(ctx, conn, req, whereCase, sumSpace)
	if err != nil {
		return nil, err
	}
	// sum
	countSQl := "/*+ DBW SQL CONSOLE DEFAULT*/ SELECT COUNT(1) FROM information_schema.tables as tables" + whereCase // ignore_security_alert
	ret := &shared.DescribeTableSpaceResp{}
	if err = conn.Raw(countSQl).
		Scan(&ret.Total); err != nil {
		return nil, err
	}
	ret.TableStats = datasource.CovertTableSpaceInfo(tableInfoList)
	return ret, nil
}

func (b *byterdsImpl) ConvertTableSpaceToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableSpaceResp) *model.DescribeTableSpaceResp {
	var tableStats []*model.TableStat
	for _, value := range resp.TableStats {
		tableStat := &model.TableStat{
			Name:                   value.Name,
			DB:                     value.DB,
			TableSpace:             &value.TableSpace,
			TableSpaceRatio:        &value.TableSpaceRatio,
			Engine:                 &value.Engine,
			IndexLength:            &value.IndexLength,
			DataLength:             &value.DataLength,
			TableRows:              &value.TableRows,
			SpaceFragmentationRate: &value.SpaceFragmentationRate,
			AutoIdUsedRate:         &value.AutoIdUsedRate,
			AvgRowLength:           &value.AvgRowLength,
		}
		tableStats = append(tableStats, tableStat)
	}
	return &model.DescribeTableSpaceResp{
		Total:      resp.Total,
		TableStats: tableStats,
	}
}

func (b *byterdsImpl) DescribeTableColumn(ctx context.Context, req *datasource.DescribeTableInfoReq) (*shared.DescribeTableColumnResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ConvertTableColumnToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableColumnResp) *model.DescribeTableColumnResp {
	panic("implement me")
}

func (b *byterdsImpl) DescribeTableIndex(ctx context.Context, req *datasource.DescribeTableInfoReq) (*shared.DescribeTableIndexResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ConvertTableIndexToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableIndexResp) *model.DescribeTableIndexResp {
	panic("implement me")
}

func (b *byterdsImpl) FormatDescribeStorageCapacityResp(sourceType shared.DataSourceType, diskSize *datasource.GetDiskSizeResp, storageSpace float64) *model.DescribeStorageCapacityResp {
	panic("implement me")
}

func (b *byterdsImpl) ExecuteCCL(ctx context.Context, req *datasource.ExecuteCCLReq) (*datasource.ExecuteCCLResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) CCLShow(ctx context.Context, req *datasource.CCLShowReq) (*datasource.CCLShowResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetCpuMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMinCpuUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetCpuUsageMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMemMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMinMemUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMemUsageMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMinDiskUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetDiskAvailableDays(ctx context.Context, req *datasource.GetDiskAvailableDaysReq) (*datasource.GetDiskAvailableDaysResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetDiskFutureSize(ctx context.Context, req *datasource.GetDiskFutureSizeReq) (*datasource.GetDiskFutureSizeResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetDiskMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetDiskUsageMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetAvgQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMaxQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMinQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetQpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetAvgTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMaxTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMinTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetTpsUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMinConnectionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetConnectedRatioMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetSessionMetricDetail(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetAvgSessionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMaxSessionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMinSessionUsage(ctx context.Context, req *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetLatestDiskUsage(ctx context.Context, req *datasource.GetLatestDiskUsageReq) (float64, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetInspectionCpuMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetInspectionMemMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetInspectionDiskMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetInspectionQpsMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetInspectionTpsMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetInspectionConnectedMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetInspectionConnRatioMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetInspectionBpHitMetric(ctx context.Context, req *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	panic("implement me")
}

/*func (b *byterdsImpl) HealthSummary(ctx context.Context, req *datasource.GetMetricUsageReq) ([]*model.Resource, error) {
	panic("implement me")
}*/

func (b *byterdsImpl) ListCollections(ctx context.Context, req *datasource.ListCollectionsReq) (*datasource.ListCollectionsResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListIndexs(ctx context.Context, req *datasource.ListIndexesReq) (*datasource.ListIndexesResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) ListMongoDBs(ctx context.Context, req *datasource.ListMongoDBsReq) (*datasource.ListMongoDBsResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) CreateFreeLockCorrectOrder(ctx context.Context, c *datasource.CreateFreeLockCorrectOrderReq) (*datasource.CreateFreeLockCorrectOrderResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) CreateFreeLockCorrectOrderDryRun(ctx context.Context, c *datasource.CreateFreeLockCorrectOrderReq) (*datasource.CreateFreeLockCorrectOrderDryRunResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeFreeLockCorrectOrders(ctx context.Context, req *datasource.DescribeFreeLockCorrectOrdersReq) (*datasource.DescribeFreeLockCorrectOrdersResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) StopFreeLockCorrectOrders(ctx context.Context, req *datasource.StopFreeLockCorrectOrdersReq) error {
	panic("implement me")
}

func (b *byterdsImpl) PreCheckFreeLockCorrectOrders(ctx context.Context, req *datasource.PreCheckFreeLockCorrectOrdersReq) (*datasource.PreCheckFreeLockCorrectOrdersResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetDBInnerAddress(ctx context.Context, req *datasource.GetDBInnerAddressReq) (*datasource.GetDBInnerAddressResp, error) {
	panic("implement me")
}

//func (b *byterdsImpl) ExecuteCommands(ctx context.Context, req *datasource.ExecuteCommandsReq) (string, error) {
//	panic("implement me")
//}

func (b *byterdsImpl) ExplainCommand(ctx context.Context, req *datasource.ExplainCommandReq) (*datasource.ExplainCommandResp, error) {
	// 这里连接数据库执行命令
	conn, err := b.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	if err != nil {
		return nil, err
	}
	var res []*datasource.ExplainCommandResult
	command := "explain " + req.Command
	if err = conn.Raw(command).Scan(&res); err != nil { // 执行失败
		// 对于explainCommand报错的情况,仅捕获表不存在，如果表存在，则给影响行数直接赋值为0
		log.Warn(ctx, "execute cmd %s err:%s", command, err.Error())
		if !strings.Contains(strings.ToLower(err.Error()), "doesn't exist") {
			res = append(res, &datasource.ExplainCommandResult{
				Rows: "0",
			})
			return &datasource.ExplainCommandResp{Command: res}, nil
		}
		return nil, err
	}
	log.Info(ctx, "explain result is %#v", res[0])
	return &datasource.ExplainCommandResp{Command: res}, nil
}

func (b *byterdsImpl) IsMyOwnInstance(ctx context.Context, instanceId string, source shared.DataSourceType) bool {
	return true
}

func (b *byterdsImpl) GetTableIndexInfo(ctx context.Context, req *datasource.GetTableIndexInfoReq) (*datasource.GetTableInfoIndexResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetTableIndexValue(ctx context.Context, req *datasource.GetIndexValueReq) (*datasource.GetIndexValueResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMaxConnections(ctx context.Context, req *datasource.GetMaxConnectionsReq) (int, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetLatestUsedConnection(ctx context.Context, req *datasource.GetMetricUsageReq) (int, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetLatestActiveConnection(ctx context.Context, req *datasource.GetMetricUsageReq) (int, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetCurrentBandwidth(ctx context.Context, req *datasource.GetCurrentBandwidthReq) (*datasource.InstanceBandwidthInfo, error) {
	panic("implement me")
}

func (b *byterdsImpl) BandwidthScale(ctx context.Context, req *datasource.BandwidthScaleReq) error {
	panic("implement me")
}

func (b *byterdsImpl) GetMinBandwidth(ctx context.Context, req *datasource.GetMinMaxBandwidthReq) (int, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetMaxBandwidth(ctx context.Context, req *datasource.GetMinMaxBandwidthReq) (int, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetDiskSize(ctx context.Context, req *datasource.GetDiskSizeReq) (*datasource.GetDiskSizeResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) GetCurrentMetricData(ctx context.Context, req *datasource.GetMetricDatapointsReq) (*datasource.GetMetricDatapointsResp, error) {
	pointCountByteCloud, err := b.GetDataPointByteCloud(
		ctx, &datasource.GetMetricUsageReq{
			RegionId:   req.RegionId,
			DSType:     req.Type,
			InstanceId: req.InstanceId,
			StartTime:  req.StartTime,
			EndTime:    req.EndTime,
			NodeIds:    []string{req.NodeId},
			Intervals:  req.Period,
		}, req.Measurement, req.MetricName, req.MultiValue,
	)
	if err != nil {
		return nil, err
	}
	return &datasource.GetMetricDatapointsResp{
		DataPoints: pointCountByteCloud,
	}, nil
}

func (b *byterdsImpl) GetPreSecondMetricData(ctx context.Context, req *datasource.GetMetricDatapointsReq) (*datasource.GetMetricDatapointsResp, error) {
	pointCountByteCloud, err := b.GetDataPointByteCloud(
		ctx, &datasource.GetMetricUsageReq{
			DSType:     req.Type,
			InstanceId: req.InstanceId,
			StartTime:  req.StartTime,
			EndTime:    req.EndTime,
			NodeIds:    []string{req.NodeId},
			Intervals:  req.Period,
		}, req.Measurement, req.MetricName, req.MultiValue,
	)
	if err != nil {
		return nil, err
	}
	return &datasource.GetMetricDatapointsResp{
		DataPoints: pointCountByteCloud,
	}, nil
}

func (b *byterdsImpl) DescribeFullSQLLogConfig(ctx context.Context, req *datasource.DescribeFullSQLLogConfigReq) (*datasource.DescribeFullSQLLogConfigResp, error) {
	inst := strings.Split(req.InstanceID, ":")
	clientIp := strings.Join(inst[0:len(inst)-1], ":")
	clientPort := inst[len(inst)-1]
	params := map[string]string{
		"region":        req.RegionId,
		"dbname":        req.DBName,
		"instance_ip":   clientIp,
		"instance_port": clientPort,
	}
	log.Info(ctx, "fullsql describe req:%s", utils.Show(params))
	apiPath := fmt.Sprintf(FullSqlStatusAPI, req.DBName)
	response, err := b.byteRDSHttpClient.GET(ctx, apiPath, params, nil, req.RegionId, false)
	if err != nil {
		return nil, err
	}
	if response == nil {
		log.Warn(ctx, "response is nil")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
	}
	log.Info(ctx, "GET statusCode: %d", response.StatusCode)
	bodyData, err := ioutil.ReadAll(response.Body)
	if err != nil {
		log.Warn(ctx, "ReadAll error: %s", err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	log.Info(ctx, "response body: %s", bodyData)
	if response.StatusCode > 300 {
		log.Warn(ctx, "POST statusCode: %d", response.StatusCode)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, fmt.Sprintf("statusCose:%d, msg:%s", response.StatusCode, string(bodyData)))
	}
	dbData := &GetFullSqlStatusResp{}
	if err := json.Unmarshal(bodyData, dbData); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if dbData.Code != 0 {
		log.Warn(ctx, "Call rds openAPI failed,code %d err is %s", dbData.Code, dbData.Error)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
	}
	status := datasource.SQLCollectorStatus_Disabled
	if dbData.Data.IsOpen {
		status = datasource.SQLCollectorStatus_Enable
	}
	return &datasource.DescribeFullSQLLogConfigResp{
		SQLCollectorStatus: status,
		BmqCluster:         dbData.Data.Cluster,
		BmqTopic:           dbData.Data.Topic,
	}, nil
}

func (b *byterdsImpl) ModifyFullSQLLogConfig(ctx context.Context, req *datasource.ModifyFullSQLLogConfigReq) (*datasource.ModifyFullSQLLogConfigResp, error) {
	inst := strings.Split(req.InstanceID, ":")
	if len(inst) < 2 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "instance id is not {ip}:{port}")
	}
	clientIp := strings.Join(inst[0:len(inst)-1], ":")
	clientPort, err := strconv.ParseInt(inst[len(inst)-1], 10, 64)
	if err != nil {
		log.Warn(ctx, "parse port error:%s", err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if req.SQLCollectorStatus == datasource.SQLCollectorStatus_Enable {
		params := map[string]string{}
		body := map[string]interface{}{
			"region":        req.RegionId,
			"dbname":        req.DBName,
			"instance_ip":   clientIp,
			"instance_port": clientPort,
			"cluster":       req.BmqCluster,
			"topic":         req.BmqTopic,
			"dry_run":       req.DryRun,
		}
		log.Info(ctx, "fullsql open req:%s", utils.Show(body))
		apiPath := fmt.Sprintf(FullSqlOpenAPI, req.DBName)
		response, err := b.byteRDSHttpClient.POST(ctx, apiPath, params, nil, body, req.RegionId, false)
		if err != nil {
			log.Warn(ctx, "POST error: %s", err)
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		if response == nil {
			log.Warn(ctx, "response is nil")
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
		}
		log.Info(ctx, "POST statusCode: %d", response.StatusCode)
		bodyData, err := ioutil.ReadAll(response.Body)
		if err != nil {
			log.Warn(ctx, "ReadAll error: %s", err)
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		log.Info(ctx, "response body: %s", bodyData)
		if response.StatusCode > 300 {
			log.Warn(ctx, "POST statusCode: %d", response.StatusCode)
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, fmt.Sprintf("statusCose:%d, msg:%s", response.StatusCode, string(bodyData)))
		}
		dbData := &InnerRdsBaseResp{}
		if err := json.Unmarshal(bodyData, dbData); err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
		if dbData.Code != 0 {
			log.Warn(ctx, "Call rds openAPI failed,code %d err is %s", dbData.Code, dbData.Error)
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
		}
		return &datasource.ModifyFullSQLLogConfigResp{}, nil
	}
	if req.SQLCollectorStatus == datasource.SQLCollectorStatus_Disabled {
		params := map[string]string{}
		body := map[string]interface{}{
			"region":        req.RegionId,
			"dbname":        req.DBName,
			"instance_ip":   clientIp,
			"instance_port": clientPort,
		}
		log.Info(ctx, "fullsql close req:%s", utils.Show(body))
		apiPath := fmt.Sprintf(FullSqlCloseAPI, req.DBName)
		response, err := b.byteRDSHttpClient.POST(ctx, apiPath, params, nil, body, req.RegionId, false)
		if err != nil {
			log.Warn(ctx, "POST error: %s", err)
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		if response.StatusCode > 300 {
			log.Warn(ctx, "POST error: %s", err)
			if response.StatusCode == 404 {
				return nil, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
			}
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())

		}
		bodyData, err := ioutil.ReadAll(response.Body)
		if err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		log.Info(ctx, "response body: %s", bodyData)
		dbData := &InnerRdsBaseResp{}
		if err := json.Unmarshal(bodyData, dbData); err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
		if dbData.Code != 0 {
			log.Warn(ctx, "Call rds openAPI failed,code %d err is %s", dbData.Code, dbData.Error)
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
		}
		return &datasource.ModifyFullSQLLogConfigResp{}, nil
	}
	return nil, errors.New("unknown")
}

func (b *byterdsImpl) DescribeInstanceVariables(ctx context.Context, req *datasource.DescribeInstanceVariablesReq) (*datasource.DescribeInstanceVariablesResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribePrimaryKeyRange(ctx context.Context, req *datasource.DescribePrimaryKeyRangeReq) (*datasource.DescribePrimaryKeyRangeResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeSQLAdvisorTableMeta(ctx context.Context, req *datasource.DescribeSQLAdvisorTableMetaReq) (*datasource.DescribeSQLAdvisorTableMetaResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) DescribeSampleData(ctx context.Context, req *datasource.DescribeSampleDataReq) (*datasource.DescribeSampleDataResp, error) {
	panic("implement me")
}

func (b *byterdsImpl) EnsureAccount(ctx context.Context, req *datasource.EnsureAccountReq) error {
	log.Info(ctx, "Start to check account")
	// 检查DB侧dbw账号是否存在
	isAccountExisted, err := b.checkAccountExistInDbEngine(ctx, req)
	if err != nil && !strings.Contains(strings.ToLower(err.Error()), "access denied") {
		return err
	}
	// 检查proxy侧账号dbw账号是否存在
	isAccountProxyExisted, err := b.checkAccountExistInProxy(ctx, req)
	if err != nil && !(strings.Contains(strings.ToLower(err.Error()), "user [dbw_admin] do not exists") ||
		strings.Contains(strings.ToLower(err.Error()), "access denied")) {
		return err
	}
	region := req.Source.Region
	cnf := b.conf.Get(ctx)
	// 账号不存在,创建psm授权工单
	if !(isAccountExisted && isAccountProxyExisted) {
		log.Info(ctx, "account is not existed,and to create it")
		dbInfo, err := b.GetDBInstanceInfo(ctx, &datasource.GetDBInstanceInfoReq{InstanceId: req.Source.InstanceId, Type: shared.Postgres, RegionId: req.Source.Region})
		if err != nil {
			log.Warn(ctx, "Get db %s consul list failed %s", req.Source.InstanceId, err)
			return err
		}
		backGround := fmt.Sprintf("auto create dbw_admin account with %s", fwctx.GetLogID(ctx))
		// 创建psm授权工单完成账号创建
		body := &CreatePsmTktBody{
			Region:         region,
			Creator:        cnf.PsmTicketCreator,
			DbName:         req.Source.InstanceId,
			BackGround:     backGround,
			BizPsmList:     []string{DbwDefaultBizPsm},
			DbPsmList:      dbInfo.DBInfo.GetConsuleList(),
			Action:         "add",
			ReviewStrategy: "skip_all_review",
			//IdempotentId:   fmt.Sprintf("%s-%s-%s", ServiceAccount, region, req.Source.InstanceId), // 幂等key
		}
		log.Info(ctx, "body is %s", utils.Show(body))
		response := b.byteRDSHttpClient.Post(ctx, PSMTicketAPI, nil, nil, body, req.Source.Region, false)
		if response == nil {
			log.Warn(ctx, "response is nil")
			return consts.ErrorWithParam(model.ErrorCode_SystemError, "response is nil")
		}
		psmTkt, err := ioutil.ReadAll(response.Body)
		if err != nil {
			return consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		log.Info(ctx, "psmTkt is %s", string(psmTkt))
		dbData := &CreatePsmTktResp{}
		if err := json.Unmarshal(psmTkt, dbData); err != nil {
			return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
		if dbData.Code != 0 {
			log.Warn(ctx, "Call rds openAPI failed,code %d err is %s", dbData.Code, dbData.Error)
			return consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
		}
		// 查询工单状态,工单正常执行时间为20s左右
		maxRetry := 5
		currentCnt := 0
		for currentCnt < maxRetry {
			state, err := b.getPsmTktState(ctx, &GetPsmTktStatusReq{
				Region:   region,
				BpmTktId: dbData.Data.BpmTktID,
			})
			if err == nil && state.Data.Status == "FINISHED" {
				log.Info(ctx, "db %s psm ticket state is FINISHED", req.Source.InstanceId)
				break
			}
			currentCnt++
			time.Sleep(5 * time.Second)
		}
		if currentCnt == maxRetry {
			msg := fmt.Sprintf("db %s psm ticket is abnormal or not finished,please check it", req.Source.InstanceId)
			log.Warn(ctx, msg)
			return consts.ErrorWithParam(model.ErrorCode_SystemError, msg)
		}
	}
	return nil
}

func (b *byterdsImpl) checkAccountExistInDbEngine(ctx context.Context, req *datasource.EnsureAccountReq) (bool, error) {
	ipList, err := b.GetInstanceDBEngine(ctx, req.Source.InstanceId, req.Source.Region)
	if err != nil {
		return false, err
	}
	//任选一个实例测试账号存在性
	if len(ipList) < 1 {
		log.Warn(ctx, "Mysql ip list is Null")
		return false, consts.ErrorWithParam(model.ErrorCode_SystemError, "mysql ip list is Null")
	}
	if len(ipList[0].IpList) < 1 {
		log.Warn(ctx, "Mysql ip list is Null")
		return false, consts.ErrorWithParam(model.ErrorCode_SystemError, "mysql ip list is Null")
	}
	addr := ipList[0].IpList[0]
	rrSource := req.Source
	var ipAddr string
	if addr.IPv4 != "" {
		ipAddr = addr.IPv4
		rrSource.Address = fmt.Sprintf("%s:%d", ipAddr, addr.Port)
	} else {
		ipAddr = addr.IPv6
		rrSource.Address = fmt.Sprintf("[%s]:%d", ipAddr, addr.Port)
	}
	log.Info(ctx, "mysql addr is %s", rrSource.Address)
	conn, err := b.getConn(ctx, rrSource)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
		return false, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())

	}
	defer conn.Close()
	return true, nil
}

func (b *byterdsImpl) checkAccountExistInProxy(ctx context.Context, req *datasource.EnsureAccountReq) (bool, error) {
	ipList, err := b.GetInstanceProxy(ctx, req.Source.Region, "mysql", req.Source.InstanceId)
	if err != nil {
		return false, err
	}
	//任选一个proxy 测试账号存在性
	if len(ipList) < 1 {
		log.Warn(ctx, "proxy ip list is Null")
		return false, consts.ErrorWithParam(model.ErrorCode_SystemError, "proxy ip list is Null")

	}
	if len(ipList[0].IpList) < 1 {
		log.Warn(ctx, "Mysql ip list is Null")
		return false, consts.ErrorWithParam(model.ErrorCode_SystemError, "mysql ip list is Null")
	}
	addr := ipList[0].IpList[0]
	rrSource := req.Source
	var ipAddr string
	if addr.IPv4 != "" {
		ipAddr = addr.IPv4
		rrSource.Address = fmt.Sprintf("%s:%d", ipAddr, addr.Port)
	} else {
		ipAddr = addr.IPv6
		rrSource.Address = fmt.Sprintf("[%s]:%d", ipAddr, addr.Port)
	}
	log.Info(ctx, "proxy addr is %s", rrSource.Address)
	conn, err := b.getConn(ctx, rrSource)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
		return false, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	return true, nil
}
func (b *byterdsImpl) GetDatasourceAddress(ctx context.Context, ds *shared.DataSource) error {
	panic("implement me")
}

func (b *byterdsImpl) Type() shared.DataSourceType {
	return shared.ByteRDS
}

func (b *byterdsImpl) AddWhiteList(ctx context.Context, id string, ds *shared.DataSource) (string, error) {
	if !env.IsBoe() {
		req := &GetDBAuthPsmReq{
			Region: ds.Region,
			DB:     ds.Db,
		}
		resp, err := b.getPsmAuth(ctx, req)
		if err != nil {
			return "", err
		}
		if hasPermission(ds.Address, resp.Data) {
			return "", nil
		}
		if err = b.grantPermToPsm(ctx, ds.Db, DbwBizAuthPsm, ds.Region); err != nil {
			log.Warn(ctx, "grantPermToPsm error %s", err)
			return "", nil
		}
	}
	return "", nil
}

func (b *byterdsImpl) getConn(ctx context.Context, ds *shared.DataSource) (db.Conn, error) {
	return db.NewConn(&db.Options{
		Address:          ds.Address,
		DB:               ds.Db,
		User:             ds.User,
		Password:         ds.Password,
		Driver:           db.MysqlDriver,
		MaxOpenConns:     uint(ds.MaxOpenConns),
		MaxIdleConns:     uint(ds.MaxIdleConns),
		ParseTimeManualy: false,
		PSM:              ds.Psm,
		GDID:             db.GDID(ds.Gdid),
		ExtraDSN:         map[string]string{"psm": ds.Psm},
	})
}

func (b *byterdsImpl) GetInstanceProxy(ctx context.Context, regionId string, dbEngine string, dbName string) ([]*ProxyAddress, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, "GetInstanceDBEngine panic, err=%v, stack=%s", r, string(debug.Stack()))
		}
	}()
	params := map[string]string{
		"region": regionId,
		"dbname": dbName,
	}
	var dbData ProxyTopoResp
	switch dbEngine {
	case "ndb":
		response, err := b.byteRDSHttpClient.GET(ctx, DBInstanceNDBProxyAPI, params, nil, regionId, false)
		if err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		if response == nil {
			log.Warn(ctx, "response is nil")
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
		}
		proxyTopo, err := ioutil.ReadAll(response.Body)
		if err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		//log.Info(ctx, "proxyTopo is %s", string(proxyTopo))
		if err := json.Unmarshal(proxyTopo, &dbData); err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
		if dbData.Code != 0 {
			log.Warn(ctx, "Call rds openAPI failed,code %d msg is %s", dbData.Code, dbData.Error)
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
		}
	case "mysql":
		response, err := b.byteRDSHttpClient.GET(ctx, DBInstanceMySQLProxyAPI, params, nil, regionId, false)
		if err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		if response == nil {
			log.Warn(ctx, "response is nil")
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
		}
		proxyTopo, err := ioutil.ReadAll(response.Body)
		if err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		//log.Info(ctx, "proxyTopo is %s", string(proxyTopo))
		if err := json.Unmarshal(proxyTopo, &dbData); err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
		if dbData.Code != 0 {
			log.Warn(ctx, "Call rds openAPI failed,code %d msg is %s", dbData.Code, dbData.Error)
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
		}
	default:

	}
	addressList := make([]*ProxyAddress, 0)
	if err := fp.StreamOf(dbData.Data).Map(func(d *ProxyTopoData) *ProxyAddress {
		ipList := make([]*IpAddress, 0)
		for _, addr := range d.Proxies {
			ipList = append(ipList, &IpAddress{
				IPv4: addr.IPv4,
				IPv6: addr.IPv6,
				Port: addr.Port,
			})
		}
		return &ProxyAddress{
			Consul: d.Consul,
			Role:   d.Role,
			IpList: ipList,
		}
	}).ToSlice(&addressList); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	log.Info(ctx, "proxy addressList is %s", utils.Show(addressList))
	return addressList, nil
}

func (b *byterdsImpl) GetInstanceDBEngine(ctx context.Context, dbName string, regionId string) ([]*DBEngineAddress, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, "GetInstanceDBEngine panic, err=%v, stack=%s", r, string(debug.Stack()))
		}
	}()
	params := map[string]string{
		"region": regionId,
		"dbname": dbName,
	}
	apiPath := fmt.Sprintf(DBInstanceEngineAPI, dbName)
	response, err := b.byteRDSHttpClient.GET(ctx, apiPath, params, nil, regionId, false)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if response == nil {
		log.Warn(ctx, "response is nil")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
	}
	dbTopo, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	var dbData DBEngineTopoResp
	log.Info(ctx, "DBEngineTopo is %s", string(dbTopo))
	if err := json.Unmarshal(dbTopo, &dbData); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if !strings2.IsEmpty(dbData.Error) {
		log.Warn(ctx, "Call rds openAPI failed,code %d msg is %s", dbData.Code, dbData.Error)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
	}
	addressList := make([]*DBEngineAddress, 0)
	if err := fp.StreamOf(dbData.Data).Map(func(d *DBEngineTopoData) *DBEngineAddress {
		ipList := make([]*IpAddress, 0)
		ipList = append(ipList, &IpAddress{
			IPv4:       d.IPv4,
			IPv6:       d.IPv6,
			Port:       d.Port,
			SchemaName: d.SchemaName,
			Role:       d.Role, // master slave
		})
		// extract child
		extractChildrenNode(d.Children, &ipList)
		return &DBEngineAddress{
			InstanceId: d.NdbInstanceID,
			IpList:     ipList,
			ShardRange: d.ShardRange,
		}
	}).ToSlice(&addressList); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	log.Info(ctx, "dbengine addressList is %s", utils.Show(addressList))
	return addressList, nil

}
func (b *byterdsImpl) GetDBServiceTreeMountInfo(ctx context.Context, req *datasource.GetDBServiceTreeMountInfoReq) (*datasource.GetDBServiceTreeMountInfoResp, error) {
	params := map[string]string{
		"region": req.RegionId,
	}
	apiPath := fmt.Sprintf(DBByteTreeAPI, req.InstanceId)
	response, err := b.byteRDSHttpClient.GET(ctx, apiPath, params, nil, req.RegionId, false)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if response == nil {
		log.Warn(ctx, "response is nil")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
	}
	bodyData, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	dbData := &GetDBSTMountInfoResp{}
	if err := json.Unmarshal(bodyData, dbData); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if dbData.Code != 0 {
		log.Warn(ctx, "Call rds openAPI failed,code %d err is %s", dbData.Code, dbData.Error)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
	}
	ret := &datasource.GetDBServiceTreeMountInfoResp{}
	if err := fp.Stream0Of(dbData.Data).Map(func(info *STMountData) *model.ServiceTreeInfo {
		var allParentId []string
		for _, p := range info.AllParentIDList {
			allParentId = append(allParentId, utils.IntToStr(p))
		}
		return &model.ServiceTreeInfo{
			PSM:          info.Psm,
			NodeId:       utils.IntToStr(info.NodeID),
			CNPath:       info.Path,
			I18nPath:     info.I18nPath,
			ParentID:     utils.IntToStr(info.ParentID),
			AllParentIds: allParentId,
		}
	}).ToSlice(&ret.DBSTMountInfos); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	return ret, nil

}

func (b *byterdsImpl) GetDBInstanceInfo(ctx context.Context, req *datasource.GetDBInstanceInfoReq) (*datasource.GetDBInstanceInfoResp, error) {
	detail, err := b.getDBDetail(ctx, &GetDBDetailReq{
		Region: req.RegionId,
		DBName: req.InstanceId,
	})
	if err != nil {
		return nil, err
	}
	data := detail.Data
	ret := &datasource.GetDBInstanceInfoResp{
		DBInfo: &model.DbInstanceInfo{
			InstanceId:      data.DbName,
			InstanceName:    data.DbName,
			DBEngineVersion: fmt.Sprintf("%s %s", data.Engine, data.Version),
			IsSharding:      data.IsSharding,
			ConsuleList:     data.ConsulList,
			Dept:            data.Dept,
			IsShared:        data.IsShared,
		},
	}
	return ret, nil
}

func (b *byterdsImpl) ListSchemaTables(ctx context.Context, req *datasource.ListSchemaTablesReq) (*datasource.ListSchemaTablesResp, error) {
	//TODO implement me
	panic("implement me")
}

func (b *byterdsImpl) ExecuteDQL(ctx context.Context, req *datasource.ExecuteDQLReq) (*datasource.ExecuteDQLResp, error) {
	//TODO implement me
	panic("implement me")
}

func (b *byterdsImpl) InstanceIsExist(ctx context.Context, req *datasource.InstanceIsExistReq) (bool, error) {
	params := map[string]string{
		"region": req.RegionId,
	}
	log.Info(ctx, "InstanceIsExist req:%s", utils.Show(params))
	apiPath := fmt.Sprintf(DBExistAPI, req.DBName)
	response, err := b.byteRDSHttpClient.GET(ctx, apiPath, params, nil, req.RegionId, false)
	if err != nil {
		return false, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if response == nil {
		log.Warn(ctx, "response is nil")
		return false, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
	}
	bodyData, err := ioutil.ReadAll(response.Body)
	if err != nil {
		log.Warn(ctx, "ReadAll error: %s", err)
		return false, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	log.Info(ctx, "response body: %s", bodyData)
	dbData := &InstanceIsExistResp{}
	if err := json.Unmarshal(bodyData, dbData); err != nil {
		return false, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if dbData.Code != 0 {
		log.Warn(ctx, "Call rds openAPI failed,code %d err is %s", dbData.Code, dbData.Error)
		return false, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
	}

	return dbData.Data, nil
}

func (b *byterdsImpl) GetInstanceTopo(ctx context.Context, req *datasource.GetInstanceTopoReq) ([]*model.InnerRdsInstance, error) {

	params := map[string]string{
		"region": req.RegionId,
		"dbname": req.DBName,
	}
	apiPath := fmt.Sprintf(DBInstanceEngineAPI, req.DBName)
	response, err := b.byteRDSHttpClient.GET(ctx, apiPath, params, nil, req.RegionId, false)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if response == nil {
		log.Warn(ctx, "response is nil")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
	}
	dbTopo, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	var dbData DBEngineTopoResp
	log.Info(ctx, "GetInstanceTopo is %s", string(dbTopo))
	if err := json.Unmarshal(dbTopo, &dbData); err != nil {
		log.Warn(ctx, "json.Unmarshal error:%s", err)
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if !strings2.IsEmpty(dbData.Error) {
		log.Warn(ctx, "GetInstanceTopo error, error is:%v", dbData.Error)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Error)
	}

	var cts []*model.InnerRdsInstance
	for _, d := range dbData.Data {
		nodeID := ""
		if d.IPv6 != "" {
			nodeID = fmt.Sprintf("%s:%d", d.IPv6, d.Port)
		} else {
			nodeID = fmt.Sprintf("%s:%d", d.IPv4, d.Port)
		}
		ct := &model.InnerRdsInstance{
			IPv4:           d.IPv4,
			IPv6:           d.IPv6,
			Port:           strconv.FormatInt(int64(d.Port), 10),
			NodeID:         nodeID,
			Role:           d.Role,
			VDC:            d.VDC,
			VAU:            d.VAU,
			IDC:            d.IDC,
			ClusterType:    d.ClusterType,
			MySQLVersion:   d.MySQLVersion,
			InstanceStatus: d.InstanceStatus,
			ShardRange:     d.ShardRange,
		}
		ct.Children = SetChild(ct.Children, d.Children)
		cts = append(cts, ct)
	}

	return cts, nil
}

func (b *byterdsImpl) GetInstanceProxyTopo(ctx context.Context, req *datasource.GetInstanceTopoReq) ([]*model.InnerRdsInstance, error) {
	params := map[string]string{
		"region": req.RegionId,
		"dbname": req.DBName,
	}
	response, err := b.byteRDSHttpClient.GET(ctx, DBInstanceMySQLProxyAPI, params, nil, req.RegionId, false)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if response == nil {
		log.Warn(ctx, "response is nil")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
	}
	dbTopo, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	var dbData ProxyTopoResp
	log.Info(ctx, "DBEngineTopo is %s", string(dbTopo))
	if err := json.Unmarshal(dbTopo, &dbData); err != nil {
		log.Warn(ctx, "json.Unmarshal error:%s", err)
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	var cts []*model.InnerRdsInstance
	for _, d := range dbData.Data {
		for _, proxy := range d.Proxies {
			nodeID := ""
			if proxy.IPv6 != "" {
				nodeID = fmt.Sprintf("%s:%d", proxy.IPv6, proxy.Port)
			} else {
				nodeID = fmt.Sprintf("%s:%d", proxy.IPv4, proxy.Port)
			}
			ct := &model.InnerRdsInstance{
				IPv4:   proxy.IPv4,
				IPv6:   proxy.IPv6,
				Port:   strconv.FormatInt(int64(proxy.Port), 10),
				NodeID: nodeID,
				Role:   d.Role,
				VDC:    proxy.VDC,
			}
			cts = append(cts, ct)
		}
	}

	return cts, nil
}

func (b *byterdsImpl) CreateLogDownloadTask(ctx context.Context, req *datasource.CreateLogDownloadTaskReq) error {
	body := map[string]interface{}{
		"region":            req.Region,
		"dbname":            req.Dbname,
		"node_ip":           req.NodeIp,
		"node_port":         req.NodePort,
		"log_type":          req.LogType,
		"file_name":         req.FileName,
		"max_gen_file_size": req.MaxGenFileSize,
	}

	response, err := b.byteRDSHttpClient.POST(ctx, ErrorLogCreateLogDownloadTask, nil, nil, body, req.Region, false)
	if err != nil {
		log.Warn(ctx, "response error:%s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if response == nil {
		log.Warn(ctx, "response is nil")
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
	}
	dbTopo, err := ioutil.ReadAll(response.Body)
	if err != nil {
		log.Warn(ctx, "response ReadAll error:%s", err)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	var dbData datasource.CreateLogDownloadTaskResponse
	log.Info(ctx, "CreateLogDownloadTaskResponse is %s", string(dbTopo))
	if err := json.Unmarshal(dbTopo, &dbData); err != nil {
		log.Warn(ctx, "json.Unmarshal error:%s", err)
		return err
	}
	if dbData.Code != 0 {
		log.Warn(ctx, "Call rds openAPI failed,code %d err is %s", dbData.Code, dbData.Msg)
		return consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Msg)
	}
	return nil
}

func (b *byterdsImpl) GetLogDownloadList(ctx context.Context, req *datasource.GetLogDownloadListReq) (*datasource.GetLogDownloadListResp, error) {
	params := map[string]string{
		"region":    req.Region,
		"dbname":    req.Dbname,
		"node_ip":   req.NodeIp,
		"node_port": strconv.FormatInt(int64(req.NodePort), 10),
		"log_type":  req.LogType,
	}
	response, err := b.byteRDSHttpClient.GET(ctx, ErrorLogGetLogDownloadList, params, nil, req.Region, false)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if response == nil {
		log.Warn(ctx, "response is nil")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "empty rds response")
	}
	dbTopo, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	var dbData datasource.GetLogDownloadListResponse
	log.Info(ctx, "GetLogDownloadListResponse is %s", string(dbTopo))
	if err := json.Unmarshal(dbTopo, &dbData); err != nil {
		log.Warn(ctx, "json.Unmarshal error:%s", err)
		return nil, consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	}
	if dbData.Code != 0 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, dbData.Msg)
	}
	return &datasource.GetLogDownloadListResp{Data: dbData.Data}, nil
}

func SetChild(children []*model.InnerRdsInstance, children2 []Child) []*model.InnerRdsInstance {
	if len(children2) == 0 {
		return children
	}
	for _, child := range children2 {
		c := convToModelInnerInst(child)
		c.Children = SetChild(c.Children, child.Children)
		children = append(children, c)
	}
	return children
}

func convToModelInnerInst(child Child) *model.InnerRdsInstance {
	nodeID := ""
	if child.IPv6 != "" {
		nodeID = fmt.Sprintf("%s:%d", child.IPv6, child.Port)
	} else {
		nodeID = fmt.Sprintf("%s:%d", child.IPv4, child.Port)
	}
	return &model.InnerRdsInstance{
		IPv4:           child.IPv4,
		IPv6:           child.IPv6,
		Port:           strconv.FormatInt(int64(child.Port), 10),
		NodeID:         nodeID,
		Role:           child.Role,
		VDC:            child.VDC,
		VAU:            child.VAU,
		IDC:            child.IDC,
		ClusterType:    child.ClusterType,
		MySQLVersion:   child.MySQLVersion,
		InstanceStatus: child.InstanceStatus,
		ShardRange:     child.ShardRange,
	}
}

func convEndpointTypeToProxyIngressType(src string) int {
	switch src {
	case model.EndpointType_ReadOnly.String():
		return 1
	case model.EndpointType_ReadWrite.String():
		return 0
	case model.EndpointType_Offline.String():
		return 2
	default:
		return 0
	}
}

// ref link:https://cloud.bytedance.net/bam/rd/inf.rds.openapi_v2/api_doc/show_doc?version=1.0.130&endpoint_id=1882653&from=block&x-resource-account=public&api_branch=master
func convThrottleTargetToProxyThrottleType(src string) int {
	switch src {
	case model.ThrottleTarget_SqlQPS.String():
		return 0
	case model.ThrottleTarget_KeywordQPS.String():
		return 1
	case model.ThrottleTarget_FingerQPS.String():
		return 2
	case model.ThrottleTarget_PsmDbQPS.String():
		return 3
	case model.ThrottleTarget_GroupQPS.String():
		return 4
	case model.ThrottleTarget_FrontQPS.String():
		return 5
	case model.ThrottleTarget_MaxFrontConn.String():
		return 6
	case model.ThrottleTarget_MaxEndConn.String():
		return 9
	case model.ThrottleTarget_FingerProhibitionQPS.String():
		return 10
	default:
		return 0
	}
}

func stringToInt64Slice(input []string) []int64 {
	var ret []int64
	for _, value := range input {
		res, err := strconv.ParseInt(value, 10, 64)
		if err == nil {
			ret = append(ret, res)
		}
	}
	return ret
}

func (b *byterdsImpl) DescribeDBInstanceShardInfos(ctx context.Context, req *datasource.DescribeDBInstanceShardInfosReq) (*datasource.DescribeDBInstanceShardInfosResp, error) {
	ret := &datasource.DescribeDBInstanceShardInfosResp{}
	topo, err := b.GetInstanceDBEngine(ctx, req.DBName, req.RegionId)
	if err != nil {
		return nil, err
	}
	if len(topo) < 1 {
		log.Warn(ctx, "Mysql ip list is Null")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "mysql ip list is Null")
	}
	log.Info(ctx, "%s topo is %s", req.InstanceId, utils.Show(topo))
	// 计算groupId
	var shardRange []string
	for _, tp := range topo {
		if tp.ShardRange != "" {
			shardRange = append(shardRange, tp.ShardRange)
		}
	}
	shardRangeMapGroupId := shardingRangeMapGroupId(shardRange)
	log.Info(ctx, "%s shardRangeGroupId is %s", req.InstanceId, shardRangeMapGroupId)
	if err := fp.Stream0Of(topo).Map(func(t *DBEngineAddress) *datasource.ShardInfo {
		var nodeIds []*model.ShardNodeInfo
		item := &datasource.ShardInfo{
			GroupId: shardRangeMapGroupId[t.ShardRange],
			LogicShardIds: []*model.DBRangeObject{
				{
					DBName: req.InstanceId,
					Range:  t.ShardRange,
				},
			},
		}
		for _, addr := range t.IpList {
			// shardId为主实例的ip:port
			var nodeId string
			if addr.IPv4 != "" {
				nodeId = fmt.Sprintf("%s:%d", addr.IPv4, addr.Port)
			} else {
				nodeId = fmt.Sprintf("[%s]:%d", addr.IPv6, addr.Port)
			}
			if strings.ToLower(addr.Role) == "master" {
				item.ShardId = nodeId
				nodeIds = append(nodeIds, &model.ShardNodeInfo{
					NodeType: model.NodeType_Primary.String(),
					NodeId:   nodeId,
				})
			} else {
				nodeIds = append(nodeIds, &model.ShardNodeInfo{
					NodeType: model.NodeType_Secondary.String(),
					NodeId:   nodeId,
				})
			}
		}
		item.Nodes = nodeIds
		return item
	}).ToSlice(&ret.Shards); err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	return ret, nil
}

func shardingRangeMapGroupId(shardRange []string) map[string]int32 {
	result := make(map[string]int32)
	// 解析区间的起始数字
	type Range struct {
		key   string
		start int32
	}
	var ranges []Range

	for _, item := range shardRange {
		parts := strings.Split(item, "-")
		start, err := strconv.ParseInt(parts[0], 10, 64)
		if err == nil {
			ranges = append(ranges, Range{key: item, start: int32(start)})
		}
	}

	// 按起始数字排序
	sort.Slice(ranges, func(i, j int) bool {
		return ranges[i].start < ranges[j].start
	})

	for i, r := range ranges {
		result[r.key] = int32(i)
	}
	return result
}

func extractChildrenNode(children []Child, ret *[]*IpAddress) {
	// Add current node's details to the result slice
	for _, child := range children {
		*ret = append(*ret, &IpAddress{
			IPv4:       child.IPv4,
			IPv6:       child.IPv6,
			Role:       child.Role,
			Port:       child.Port,
			SchemaName: child.SchemaName,
		})
		extractChildrenNode(child.Children, ret)
	}
}
func generateTrxAndLockSearchWhereConditions(searchParam *model.TrxQueryFilter) (string, []interface{}) {
	args := []interface{}{}
	mp1 := map[string]string{
		"RUNNING":      "RUNNING",
		"LOCKWAIT":     "LOCK WAIT",
		"ROLLING_BACK": "ROLLING BACK",
		"COMMITTING":   "COMMITTING",
	}
	if searchParam == nil {
		return "", nil
	}
	// where条件
	whereConditions := " where 1 "
	// TrxStatus
	/* RUNNING, LOCK WAIT, ROLLING BACK, COMMITTING */
	if searchParam.GetTrxStatus() != "<UNSET>" {
		whereConditions += " and TrxStatus=?"
		args = append(args, mp1[searchParam.GetTrxStatus()])
	}
	// 最低事务执行时间
	if searchParam.GetTrxExecTime() != 0 {
		whereConditions += " and UNIX_TIMESTAMP(TrxStartTime)<(UNIX_TIMESTAMP(NOW())-?)"
		args = append(args, searchParam.GetTrxExecTime())
	}
	// 锁状态
	/* hold and wait lock,wait lock,hold lock */
	if searchParam.GetLockStatus().String() != "<UNSET>" {
		lockStatus := searchParam.GetLockStatus().String()
		if lockStatus == "LockHoldAndWait" {
			whereConditions += " and TrxLockStructs>0 and ISNULL(trx_requested_lock_id)=0"
		} else if lockStatus == "LockWait" {
			whereConditions += " and ISNULL(trx_requested_lock_id)=0"
		} else if lockStatus == "LockHold" {
			whereConditions += " and TrxLockStructs>0"
		} else if lockStatus == "None" {
			whereConditions += " and TrxLockStructs=0"
		}
	}
	// 事务id
	if searchParam.GetTrxId() != "" {
		whereConditions += " and TrxId=?"
		args = append(args, searchParam.GetTrxId())
	}
	// 会话id
	if searchParam.GetProcessId() != "" {
		whereConditions += " and ProcessID=?"
		args = append(args, searchParam.GetProcessId())
	}
	// 阻塞事务id
	if searchParam.GetBlockTrxId() != "" {
		whereConditions += " and BlockTrxId=?"
		args = append(args, searchParam.GetBlockTrxId())
	}
	//事务隔离级别
	if searchParam.GetTrxIsoLevel() != "" {
		whereConditions += " and TrxIsoLevel=?"
		args = append(args, searchParam.GetTrxIsoLevel())
	}
	//阻塞的sql
	if searchParam.GetSqlBlocked() != "" {
		whereConditions += " and SqlBlocked like ?"
		args = append(args, "%"+searchParam.GetSqlBlocked()+"%")
	}
	return whereConditions, args
}
func sortTrx(trxLogs []*shared.TrxAndLock, sortBy shared.SortBy, orderBy string) {
	switch orderBy {
	case "TrxExecTime":
		if sortBy == shared.ASC {
			sort.Slice(trxLogs, func(i, j int) bool {
				t0 := trxLogs[i].TrxExecTime
				t1 := trxLogs[j].TrxExecTime
				return t0 < t1
			})
		} else if sortBy == shared.DESC {
			sort.Slice(trxLogs, func(i, j int) bool {
				t0 := trxLogs[i].TrxExecTime
				t1 := trxLogs[j].TrxExecTime
				return t0 > t1
			})
		}
	}
}

func (b *byterdsImpl) ListAllTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {

	log.Info(ctx, "ListAllTables-enter %s", utils.Show(req))
	conn, err := b.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTablesResp{}
	sql := "select TABLE_NAME from information_schema.TABLES where TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys') and TABLE_TYPE='BASE TABLE'" // ignore_security_alert
	args := []interface{}{}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		sql += genListTableFilter(req.Filters)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	sql += DBW_CONSOLE_DEFAULT_HINT
	log.Info(ctx, "ListAllTables-sql:%s,args:%s", utils.Show(sql), utils.Show(args))
	if err = conn.Raw(sql, args...).Scan(&ret.Items); err != nil {
		log.Warn(ctx, "Scan to sql %s fail %v", sql, err)
		return nil, err
	}
	args = []interface{}{req.DB}
	/* get count */
	counterSQL := "/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE' " // ignore_security_alert
	if req.Keyword != "" {
		counterSQL += "and TABLE_NAME like ?"
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		counterSQL += genListTableFilter(req.Filters)
	}
	log.Info(ctx, "ListAllTables-counterSQL:%s,args:%s", utils.Show(counterSQL), utils.Show(args))
	if err = conn.Raw(counterSQL, args...).Scan(&ret.Total); err != nil {
		log.Warn(ctx, "Scan to counterSQL %s fail %v", counterSQL, err)
		return nil, err
	}
	return ret, nil
}

func (b *byterdsImpl) ListInstanceLightWeight(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	panic("not implement")
}

func (b *byterdsImpl) ListInstanceNodesOri(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesOriResp, error) {
	panic("not implement")
}
func (b *byterdsImpl) DescribeDBInstanceSpec(ctx context.Context, req *datasource.DescribeDBInstanceSpecReq) (*datasource.DescribeDBInstanceSpecResp, error) {
	panic("not implement")
}
func (b *byterdsImpl) DescribeInstanceProxyAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	panic("not implement")
}

func (b *byterdsImpl) CalculateSpecAfterScale(ctx context.Context, req *datasource.CalculateSpecAfterScaleReq) (*datasource.CalculateSpecAfterScaleResp, error) {
	panic("not implement")
}

func (b *byterdsImpl) DescribeLockCurrentWaits(ctx context.Context, req *datasource.DescribeLockCurrentWaitsReq) (*datasource.DescribeLockCurrentWaitsResp, error) {
	panic("not implement")
}

func (b *byterdsImpl) DescribeTableSpaceAutoIncr(ctx context.Context, req *datasource.DescribeTableSpaceReq) (*shared.DescribeTableSpaceAutoIncrResp, error) {
	panic("not implement")
}

func (b *byterdsImpl) GetManagedAccountAndPwd(ctx context.Context, req *shared.DataSource) (*datasource.GetManagedAccountAndPwdResp, error) {
	panic("not implement")
}

func (b *byterdsImpl) GetPartitionInfos(ctx context.Context, dbSource *shared.DataSource, dbName string) ([]*datasource.DbPartitionInfo, error) {
	panic("not implement")
}

func (b *byterdsImpl) GetShardingDbType(ctx context.Context, dbSource *shared.DataSource, dbName string, tableName string) (string, error) {
	panic("not implement")
}

func (b *byterdsImpl) ModifyDBInstanceSpec(ctx context.Context, req *datasource.ModifyDBInstanceSpecReq) (*datasource.ModifyDBInstanceSpecResp, error) {
	panic("not implement")
}

// skip
func (b *byterdsImpl) CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error {
	return nil
}

func (b *byterdsImpl) ExecuteSql(ctx context.Context, req *datasource.ExecuteReq) error {
	panic("not implement")
}

func (b *byterdsImpl) GetCreateTableInfo(ctx context.Context, ds *shared.DataSource, tableName string) (string, error) {
	panic("not implement")
}

func (b *byterdsImpl) GetCurrentMaxConnections(ctx context.Context, req *datasource.GetCurrentMaxConnectionsReq) (int, error) {
	panic("not implement")
}

func (b *byterdsImpl) GetInstanceSlaveAddress(ctx types.Context, req *datasource.GetInstanceSlaveAddressReq) (*datasource.GetInstanceSlaveAddressResp, error) {
	panic("not implement")
}

func (b *byterdsImpl) GetUsedSize(ctx context.Context, req *datasource.GetDiskSizeReq) (int64, error) {
	panic("not implement")
}

func (b *byterdsImpl) ValidateDryRun(ctx context.Context, req *datasource.ValidateDryRunReq) *shared.ValidateResponse {
	panic("not implement")
}

func (b *byterdsImpl) ValidateOriginalTable(ctx context.Context, req *datasource.ValidateOriginalTableReq) *shared.ValidateResponse {
	panic("not implement")
}

func (b *byterdsImpl) ValidateUniqIndex(ctx context.Context, req *datasource.ValidateUniqIndexReq) *shared.ValidateResponse {
	panic("not implement")
}
func (b *byterdsImpl) DescribeSqlType(ctx context.Context, req *datasource.DescribeSqlTypeReq) (*datasource.DescribeSqlTypeResp, error) {
	ret := &datasource.DescribeSqlTypeResp{}
	sqlType, err := datasource.GetRdsSqlType(ctx, req.SqlText)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_SQLReviewParserSqlError)
	}
	ret.SqlType = sqlType
	return ret, nil
}
func genListTableFilter(filters []string) string {
	var condition string
	for _, filter := range filters {
		condition += fmt.Sprintf(" and TABLE_NAME not like '%s'", filter)
	}
	return condition
}

func (b *byterdsImpl) ExecuteDMLAndGetAffectedRows(ctx context.Context, req *datasource.ExecuteDMLAndGetAffectedRowsReq) (int64, error) {
	panic("not implement")
}
