package byterds

import (
	"bytes"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"time"

	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	bconfig "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	dshttp "code.byted.org/infcs/ds-lib/common/http"
	"code.byted.org/infcs/ds-lib/common/log"
)

type ByteRDSClient interface {
	Call(context.Context, string, interface{}, interface{}, bool) error
	Get(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, regionId string, crossRegion bool) *http.Response
	Post(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, body interface{}, regionId string, crossRegion bool) *http.Response
	GET(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, regionId string, crossRegion bool) (*http.Response, error)
	POST(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, body interface{}, regionId string, crossRegion bool) (*http.Response, error)
	HttpGet(ctx context.Context, path, regionId string, param, headers map[string]string, resp interface{}) error
	HttpPost(ctx context.Context, apiPath, region string, params map[string]string, headers map[string]string, req, resp interface{}) error
}

func NewByteRDSClient(conf config.ConfigProvider) ByteRDSClient {
	return &client{
		conf: conf,
	}
}

type client struct {
	conf config.ConfigProvider
}

func (c *client) Call(ctx context.Context, action string, req interface{}, resp interface{}, crossRegion bool) error {
	conf := c.conf.Get(ctx)
	result := c.getClient(ctx, conf, crossRegion).AddBeforeHook(func(req *http.Request) {
		ts := time.Now().Unix()
		req.Header.Set(`ts`, strconv.FormatInt(ts, 10))
		req.Header.Set(`authtoken`, calcAuthToken(conf.ByteRDS.Secret, ts))
	}).PostJSON(ctx, fmt.Sprintf(`%s/rds/%s`, conf.ByteRDS.ProxyAddress, action), req)
	body, err := result.GetBody()
	if err != nil {
		log.Warn(ctx, "get result body failed,err = %s", err.Error())
	}
	log.Info(ctx, "result body = %s", string(body))
	return result.Unmarshal(resp)
}

func (c *client) Get(ctx context.Context, apiPath string, params, headers map[string]string, regionId string, crossRegion bool) *http.Response {
	conf := c.conf.Get(ctx)
	var apiAddress map[string]string
	// APIDomain why not a map
	apiDomain := conf.ByteRDS.APIDomain
	log.Info(ctx, "apiDomain is %s, path is %s", apiDomain, apiPath)
	if err := json.Unmarshal([]byte(apiDomain), &apiAddress); err != nil {
		log.Warn(ctx, "%s json unmarshal failed %s", apiDomain, err)
		return nil
	}
	if _, ok := apiAddress[regionId]; !ok {
		log.Warn(ctx, "apiAddress %s not config for region %s", apiDomain, regionId)
		return nil
	}
	apiUrl := fmt.Sprintf(`%s%s`, apiAddress[regionId], apiPath)
	//apiUrl := fmt.Sprintf(`%s%s`, "http://[2605:340:cd50:2000:27c5:e409:c3a3:2973]:11258", apiPath)
	// 创建 URL 对象
	u, err := url.Parse(apiUrl)
	if err != nil {
		log.Warn(ctx, "Error parsing URL:%s", err)
		return nil
	}
	// 设置查询参数
	param := url.Values{}
	for k, v := range params {
		param.Add(k, v)
	}
	u.RawQuery = param.Encode()
	log.Info(ctx, "request is %s", u.String())
	request, _ := http.NewRequest(http.MethodGet, u.String(), nil)
	// 设置header
	for k, v := range headers {
		request.Header.Set(k, v)
	}
	ts := time.Now().Unix()
	jwtToken, err := c.GetJwtToken(ctx, conf.ByteRDS.Secret)
	if err != nil {
		log.Warn(ctx, "get jwtToken failed %s", err)
		return nil
	}
	request.Header.Set(`X-Jwt-Token`, jwtToken)
	request.Header.Set(`ts`, strconv.FormatInt(ts, 10))
	request.Header.Set(`authtoken`, calcAuthToken(conf.ByteRDS.Secret, ts))
	result := c.getClient(ctx, conf, crossRegion).DoRequest(request)
	return result.Response
}

func (c *client) Post(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, body interface{}, regionId string, crossRegion bool) *http.Response {
	conf := c.conf.Get(ctx)
	var apiAddress map[string]string
	apiDomain := conf.ByteRDS.APIDomain
	if err := json.Unmarshal([]byte(apiDomain), &apiAddress); err != nil {
		log.Warn(ctx, "%s json unmarshal failed %s", apiDomain, err)
		return nil
	}
	if _, ok := apiAddress[regionId]; !ok {
		log.Warn(ctx, "apiAddress %s not config for region %s", apiDomain, regionId)
		return nil
	}
	apiUrl := fmt.Sprintf(`%s%s`, apiAddress[regionId], apiPath)
	// 创建 URL 对象
	u, err := url.Parse(apiUrl)
	if err != nil {
		log.Warn(ctx, "Error parsing URL:%s", err)
		return nil
	}
	// 设置查询参数
	param := url.Values{}
	for k, v := range params {
		param.Add(k, v)
	}
	u.RawQuery = param.Encode()
	sentData, _ := json.Marshal(body)
	// 创建请求的 Body
	data := bytes.NewBuffer(sentData)
	request, _ := http.NewRequest(http.MethodPost, u.String(), data)
	// 设置header
	for k, v := range headers {
		request.Header.Set(k, v)
	}
	ts := time.Now().Unix()
	jwtToken, err := c.GetJwtToken(ctx, conf.ByteRDS.Secret)
	if err != nil {
		log.Warn(ctx, "get jwtToken failed %s", err)
		return nil
	}
	request.Header.Set(`X-Jwt-Token`, jwtToken)
	request.Header.Set(`ts`, strconv.FormatInt(ts, 10))
	request.Header.Set(`Content-Type`, "application/json")
	request.Header.Set(`authtoken`, calcAuthToken(conf.ByteRDS.Secret, ts))
	result := c.getClient(ctx, conf, crossRegion).DoRequest(request)
	return result.Response
}

func (c *client) getClient(ctx context.Context, conf *bconfig.Config, crossRegion bool) dshttp.Client {
	//pc := &proxy.ProxyConfig{
	//	AuthUser:     conf.Tunnel.SocksUser,
	//	AuthPassword: conf.Tunnel.SocksPassword,
	//	EnableTLS:    conf.Tunnel.EnableTLS,
	//}
	cli := dshttp.NewClient().SetTimeout(40 * time.Second)
	if log.GetLevel() == log.Level_Debug {
		cli = cli.SetDebug(dshttp.DefaultLogger)
	}
	return cli
	//pc, ok := fwproxy.GetProxyConfig(consts.RuntimeByteDanceGatewayProtocol.String())
	//if !ok {
	//	log.Warn(ctx, "can't find bytedance proxy %s", consts.RuntimeByteDanceGatewayProtocol)
	//} else {
	//	log.Info(ctx, "use proxy [%s] for byte rds", pc.Encode())
	//	return cli.EnableSocks5Proxy(pc)
	//}
	//return cli.EnableSocks5Proxy(pc)
}

func calcAuthToken(base string, ts int64) string {
	return fmt.Sprintf(`%x`, md5.Sum([]byte(fmt.Sprintf("%s.%d", base, ts))))
}

func (c *client) GetJwtToken(ctx context.Context, secret string) (string, error) {
	conf := c.conf.Get(ctx)
	// 创建一个新的 HTTP请求
	req, err := http.NewRequest(http.MethodGet, jwtUrl, nil)
	if err != nil {
		log.Warn(ctx, "Error creating jwt request: %s", err)
		return "", err
	}
	// 设置自定义头部
	req.Header.Set("Authorization", "Bearer "+secret)
	result := c.getClient(ctx, conf, false).DoRequest(req)
	// 读取header
	if result == nil {
		return "", errors.New("get jwt Token failed")
	}
	if result.Response.StatusCode >= 300 {
		return "", errors.New(fmt.Sprintf("status code :%d, get jwt Token failed", result.Response.StatusCode))
	}
	header := result.Header
	if jwt, ok := header["X-Jwt-Token"]; ok {
		jwtToken := jwt[0]
		return jwtToken, nil
	}
	return "", errors.New("get jwt Token failed")
}

func (c *client) GET(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, regionId string, crossRegion bool) (*http.Response, error) {
	conf := c.conf.Get(ctx)
	var apiAddress map[string]string
	apiDomain := conf.ByteRDS.APIDomain

	log.Info(ctx, "apiDomain is %s", apiDomain)
	if err := json.Unmarshal([]byte(apiDomain), &apiAddress); err != nil {
		log.Warn(ctx, "%s json unmarshal failed %s", apiDomain, err)
		return nil, err
	}
	if _, ok := apiAddress[regionId]; !ok {
		log.Warn(ctx, "apiAddress %s not config for region %s", apiDomain, regionId)
		return nil, errors.New("api address not exist")
	}
	apiUrl := fmt.Sprintf(`%s%s`, apiAddress[regionId], apiPath)
	//apiUrl := fmt.Sprintf(`%s%s`, "[2605:340:cd50:2000:27c5:e409:c3a3:2973]:11258", apiPath)
	// 创建 URL 对象
	u, err := url.Parse(apiUrl)
	if err != nil {
		log.Warn(ctx, "Error parsing URL:%s", err)
		return nil, err
	}
	// 设置查询参数
	param := url.Values{}
	for k, v := range params {
		param.Add(k, v)
	}
	u.RawQuery = param.Encode()
	log.Info(ctx, "GET request url: %s", u.String())
	request, _ := http.NewRequest(http.MethodGet, u.String(), nil)
	// 设置header
	for k, v := range headers {
		request.Header.Set(k, v)
	}
	ts := time.Now().Unix()
	jwtToken, err := c.GetJwtToken(ctx, conf.ByteRDS.Secret)
	if err != nil {
		log.Warn(ctx, "get jwtToken failed %s", err)
		return nil, err
	}
	request.Header.Set(`X-Jwt-Token`, jwtToken)
	request.Header.Set(`ts`, strconv.FormatInt(ts, 10))
	request.Header.Set(`authtoken`, calcAuthToken(conf.ByteRDS.Secret, ts))
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("X-Tt-Logid", fwctx.GetLogID(ctx))
	result := c.getClient(ctx, conf, crossRegion).DoRequest(request)
	if result.Err != nil {
		log.Warn(ctx, "request error:%s", result.Err)
	}
	log.Info(ctx, "response stateCode is %d", result.Response.StatusCode)
	if result.Response.StatusCode != 200 {
		errMsg := result.Response.Body
		rspBody, err := ioutil.ReadAll(errMsg)
		if err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		log.Warn(ctx, "response error msg is %s", string(rspBody))
		return nil, errors.New(string(rspBody))
	}
	return result.Response, result.Err
}

func (c *client) POST(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, body interface{}, regionId string, crossRegion bool) (*http.Response, error) {
	conf := c.conf.Get(ctx)
	var apiAddress map[string]string
	apiDomain := conf.ByteRDS.APIDomain
	if err := json.Unmarshal([]byte(apiDomain), &apiAddress); err != nil {
		log.Warn(ctx, "%s json unmarshal failed %s", apiDomain, err)
		return nil, err
	}
	if _, ok := apiAddress[regionId]; !ok {
		log.Warn(ctx, "apiAddress %s not config for region %s", apiDomain, regionId)
		return nil, errors.New("api address not exist")
	}
	apiUrl := fmt.Sprintf(`%s%s`, apiAddress[regionId], apiPath)
	// 创建 URL 对象
	u, err := url.Parse(apiUrl)
	if err != nil {
		log.Warn(ctx, "Error parsing URL:%s", err)
		return nil, err
	}
	// 设置查询参数
	param := url.Values{}
	for k, v := range params {
		param.Add(k, v)
	}
	u.RawQuery = param.Encode()
	sentData, err := json.Marshal(body)
	if err != nil {
		log.Warn(ctx, "Error marshal body:%s", err)
		return nil, err
	}
	// 创建请求的 Body
	data := bytes.NewBuffer(sentData)
	log.Info(ctx, "POST request url is %s, send data: %s", u.String(), sentData)
	request, _ := http.NewRequest(http.MethodPost, u.String(), data)
	// 设置header
	for k, v := range headers {
		request.Header.Set(k, v)
	}
	ts := time.Now().Unix()
	jwtToken, err := c.GetJwtToken(ctx, conf.ByteRDS.Secret)
	if err != nil {
		log.Warn(ctx, "get jwtToken failed %s", err)
		return nil, err
	}
	request.Header.Set(`X-Jwt-Token`, jwtToken)
	request.Header.Set(`ts`, strconv.FormatInt(ts, 10))
	request.Header.Set(`authtoken`, calcAuthToken(conf.ByteRDS.Secret, ts))
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("X-Tt-Logid", fwctx.GetLogID(ctx))
	result := c.getClient(ctx, conf, crossRegion).DoRequest(request)
	if result.Err != nil {
		log.Warn(ctx, "request error:%s", result.Err)
	}
	log.Info(ctx, "response stateCode is %d", result.Response.StatusCode)
	if result.Response.StatusCode != 200 {
		errMsg := result.Response.Body
		rspBody, err := ioutil.ReadAll(errMsg)
		if err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		log.Warn(ctx, "response error msg is %s", string(rspBody))
		return nil, errors.New(string(rspBody))
	}
	return result.Response, result.Err
}

func (c *client) HttpGet(ctx context.Context, path, regionId string, params map[string]string, headers map[string]string, resp interface{}) error {
	conf := c.conf.Get(ctx)
	hc := c.getClient(ctx, conf, false)
	hc.SetHeaders(headers)
	ts := time.Now().Unix()
	jwtToken, err := c.GetJwtToken(ctx, conf.ByteRDS.Secret)
	if err != nil {
		log.Warn(ctx, "get jwtToken failed %s", err)
		return err
	}
	hc.SetHeader(`X-Jwt-Token`, jwtToken)
	hc.SetHeader(`ts`, strconv.FormatInt(ts, 10))
	hc.SetHeader(`authtoken`, calcAuthToken(conf.ByteRDS.Secret, ts))
	hc.SetHeader("Content-Type", "application/json")
	hc.SetHeader("X-Tt-Logid", fwctx.GetLogID(ctx))
	apiDomain := conf.ByteRDS.APIDomain
	var apiAddress map[string]string

	if err := json.Unmarshal([]byte(apiDomain), &apiAddress); err != nil {
		log.Warn(ctx, "%s json unmarshal failed %s", apiDomain, err)
		return err
	}
	if _, ok := apiAddress[regionId]; !ok {
		log.Warn(ctx, "apiAddress %s not config for region %s", apiDomain, regionId)
		return errors.New("api address not exist")
	}
	apiUrl := fmt.Sprintf(`%s%s`, apiAddress[regionId], path)
	// 创建 URL 对象
	u, err := url.Parse(apiUrl)
	if err != nil {
		return err
	}
	param := url.Values{}
	for k, v := range params {
		param.Add(k, v)
	}
	u.RawQuery = param.Encode()
	r := hc.Get(ctx, u.String())
	if r.Err != nil {
		return r.Err
	}
	return r.Unmarshal(resp)
}

func (c *client) HttpPost(ctx context.Context, apiPath, region string, params map[string]string, headers map[string]string, req, resp interface{}) error {
	conf := c.conf.Get(ctx)
	hc := c.getClient(ctx, conf, false)
	apiDomain := conf.ByteRDS.APIDomain
	var apiAddress map[string]string

	if err := json.Unmarshal([]byte(apiDomain), &apiAddress); err != nil {
		log.Warn(ctx, "%s json unmarshal failed %s", apiDomain, err)
		return err
	}
	if _, ok := apiAddress[region]; !ok {
		log.Warn(ctx, "apiAddress %s not config for region %s", apiDomain, region)
		return errors.New("api address not exist")
	}
	apiUrl := fmt.Sprintf(`%s%s`, apiAddress[region], apiPath)
	// 创建 URL 对象
	u, err := url.Parse(apiUrl)
	if err != nil {
		log.Warn(ctx, "Error parsing URL:%s", err)
		return err
	}
	// 设置查询参数
	param := url.Values{}
	for k, v := range params {
		param.Add(k, v)
	}
	u.RawQuery = param.Encode()
	hc.SetHeaders(headers)
	ts := time.Now().Unix()
	jwtToken, err := c.GetJwtToken(ctx, conf.ByteRDS.Secret)
	if err != nil {
		log.Warn(ctx, "get jwtToken failed %s", err)
		return err
	}
	hc.SetHeader(`X-Jwt-Token`, jwtToken)
	hc.SetHeader(`ts`, strconv.FormatInt(ts, 10))
	hc.SetHeader(`authtoken`, calcAuthToken(conf.ByteRDS.Secret, ts))
	hc.SetHeader("Content-Type", "application/json")
	hc.SetHeader("X-Tt-Logid", fwctx.GetLogID(ctx))
	r := hc.PostJSON(ctx, apiUrl, req)
	return r.Unmarshal(resp)
}
