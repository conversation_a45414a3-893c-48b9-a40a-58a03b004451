// AUTO GEN by decorator plugin v0.0.0, do not edit this file
// /var/folders/38/5vf6ht9574l63z1pnqndqh_h0000gn/T/go-build1496397901/b001/exe/main dec -f /Users/<USER>/go/src/code.byted.org/storage/dbw-mgr/biz/service/datasource/ds_service.go
package datasource

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"context"
	"reflect"
)

// DataSourceServiceDecorator for DataSourceService
type DataSourceServiceDecorator interface {
	// DecorateType decorate method Type
	DecorateType(func(TypeFunc) TypeFunc) DataSourceServiceDecorator
	// DecorateAddWhiteList decorate method AddWhiteList
	DecorateAddWhiteList(func(AddWhiteListFunc) AddWhiteListFunc) DataSourceServiceDecorator
	// DecorateUpdateWhiteList decorate method UpdateWhiteList
	DecorateUpdateWhiteList(func(UpdateWhiteListFunc) UpdateWhiteListFunc) DataSourceServiceDecorator
	// DecorateRemoveWhiteList decorate method RemoveWhiteList
	DecorateRemoveWhiteList(func(RemoveWhiteListFunc) RemoveWhiteListFunc) DataSourceServiceDecorator
	// DecorateFillDataSource decorate method FillDataSource
	DecorateFillDataSource(func(FillDataSourceFunc) FillDataSourceFunc) DataSourceServiceDecorator
	// DecorateFillInnerDataSource decorate method FillInnerDataSource
	DecorateFillInnerDataSource(func(FillInnerDataSourceFunc) FillInnerDataSourceFunc) DataSourceServiceDecorator
	// DecorateCheckConn decorate method CheckConn
	DecorateCheckConn(func(CheckConnFunc) CheckConnFunc) DataSourceServiceDecorator
	// DecorateCheckDataSource decorate method CheckDataSource
	DecorateCheckDataSource(func(CheckDataSourceFunc) CheckDataSourceFunc) DataSourceServiceDecorator
	// DecorateKillQuery decorate method KillQuery
	DecorateKillQuery(func(KillQueryFunc) KillQueryFunc) DataSourceServiceDecorator
	// DecorateListInstance decorate method ListInstance
	DecorateListInstance(func(ListInstanceFunc) ListInstanceFunc) DataSourceServiceDecorator
	// DecorateListInstanceLightWeight decorate method ListInstanceLightWeight
	DecorateListInstanceLightWeight(func(ListInstanceLightWeightFunc) ListInstanceLightWeightFunc) DataSourceServiceDecorator
	// DecorateListDatabases decorate method ListDatabases
	DecorateListDatabases(func(ListDatabasesFunc) ListDatabasesFunc) DataSourceServiceDecorator
	// DecorateListInstanceNodes decorate method ListInstanceNodes
	DecorateListInstanceNodes(func(ListInstanceNodesFunc) ListInstanceNodesFunc) DataSourceServiceDecorator
	// DecorateListInstanceNodesOri decorate method ListInstanceNodesOri
	DecorateListInstanceNodesOri(func(ListInstanceNodesOriFunc) ListInstanceNodesOriFunc) DataSourceServiceDecorator
	// DecorateListInstancePods decorate method ListInstancePods
	DecorateListInstancePods(func(ListInstancePodsFunc) ListInstancePodsFunc) DataSourceServiceDecorator
	// DecorateDescribeDBInstanceDetail decorate method DescribeDBInstanceDetail
	DecorateDescribeDBInstanceDetail(func(DescribeDBInstanceDetailFunc) DescribeDBInstanceDetailFunc) DataSourceServiceDecorator
	// DecorateDescribeDBInstanceSpec decorate method DescribeDBInstanceSpec
	DecorateDescribeDBInstanceSpec(func(DescribeDBInstanceSpecFunc) DescribeDBInstanceSpecFunc) DataSourceServiceDecorator
	// DecorateDescribeDBInstanceEndpoints decorate method DescribeDBInstanceEndpoints
	DecorateDescribeDBInstanceEndpoints(func(DescribeDBInstanceEndpointsFunc) DescribeDBInstanceEndpointsFunc) DataSourceServiceDecorator
	// DecorateDescribeDBInstanceShardInfos decorate method DescribeDBInstanceShardInfos
	DecorateDescribeDBInstanceShardInfos(func(DescribeDBInstanceShardInfosFunc) DescribeDBInstanceShardInfosFunc) DataSourceServiceDecorator
	// DecorateDescribeDBInstanceCluster decorate method DescribeDBInstanceCluster
	DecorateDescribeDBInstanceCluster(func(DescribeDBInstanceClusterFunc) DescribeDBInstanceClusterFunc) DataSourceServiceDecorator
	// DecorateDescribeDBInstanceAuditCollectedPod decorate method DescribeDBInstanceAuditCollectedPod
	DecorateDescribeDBInstanceAuditCollectedPod(func(DescribeDBInstanceAuditCollectedPodFunc) DescribeDBInstanceAuditCollectedPodFunc) DataSourceServiceDecorator
	// DecorateOpenDBInstanceAuditLog decorate method OpenDBInstanceAuditLog
	DecorateOpenDBInstanceAuditLog(func(OpenDBInstanceAuditLogFunc) OpenDBInstanceAuditLogFunc) DataSourceServiceDecorator
	// DecorateCloseDBInstanceAuditLog decorate method CloseDBInstanceAuditLog
	DecorateCloseDBInstanceAuditLog(func(CloseDBInstanceAuditLogFunc) CloseDBInstanceAuditLogFunc) DataSourceServiceDecorator
	// DecorateCheckDBInstanceAuditLogStatus decorate method CheckDBInstanceAuditLogStatus
	DecorateCheckDBInstanceAuditLogStatus(func(CheckDBInstanceAuditLogStatusFunc) CheckDBInstanceAuditLogStatusFunc) DataSourceServiceDecorator
	// DecorateDescribeDBProxyConfig decorate method DescribeDBProxyConfig
	DecorateDescribeDBProxyConfig(func(DescribeDBProxyConfigFunc) DescribeDBProxyConfigFunc) DataSourceServiceDecorator
	// DecorateDescribeInstanceFeatures decorate method DescribeInstanceFeatures
	DecorateDescribeInstanceFeatures(func(DescribeInstanceFeaturesFunc) DescribeInstanceFeaturesFunc) DataSourceServiceDecorator
	// DecorateDescribeDBInstanceSSL decorate method DescribeDBInstanceSSL
	DecorateDescribeDBInstanceSSL(func(DescribeDBInstanceSSLFunc) DescribeDBInstanceSSLFunc) DataSourceServiceDecorator
	// DecorateDescribeSQLCCLConfig decorate method DescribeSQLCCLConfig
	DecorateDescribeSQLCCLConfig(func(DescribeSQLCCLConfigFunc) DescribeSQLCCLConfigFunc) DataSourceServiceDecorator
	// DecorateModifySQLCCLConfig decorate method ModifySQLCCLConfig
	DecorateModifySQLCCLConfig(func(ModifySQLCCLConfigFunc) ModifySQLCCLConfigFunc) DataSourceServiceDecorator
	// DecorateAddSQLCCLRule decorate method AddSQLCCLRule
	DecorateAddSQLCCLRule(func(AddSQLCCLRuleFunc) AddSQLCCLRuleFunc) DataSourceServiceDecorator
	// DecorateModifyProxyThrottleRule decorate method ModifyProxyThrottleRule
	DecorateModifyProxyThrottleRule(func(ModifyProxyThrottleRuleFunc) ModifyProxyThrottleRuleFunc) DataSourceServiceDecorator
	// DecorateDeleteSQLCCLRule decorate method DeleteSQLCCLRule
	DecorateDeleteSQLCCLRule(func(DeleteSQLCCLRuleFunc) DeleteSQLCCLRuleFunc) DataSourceServiceDecorator
	// DecorateFlushSQLCCLRule decorate method FlushSQLCCLRule
	DecorateFlushSQLCCLRule(func(FlushSQLCCLRuleFunc) FlushSQLCCLRuleFunc) DataSourceServiceDecorator
	// DecorateListSQLCCLRules decorate method ListSQLCCLRules
	DecorateListSQLCCLRules(func(ListSQLCCLRulesFunc) ListSQLCCLRulesFunc) DataSourceServiceDecorator
	// DecorateDescribeSqlFingerPrintOrKeywords decorate method DescribeSqlFingerPrintOrKeywords
	DecorateDescribeSqlFingerPrintOrKeywords(func(DescribeSqlFingerPrintOrKeywordsFunc) DescribeSqlFingerPrintOrKeywordsFunc) DataSourceServiceDecorator
	// DecorateDescribeSqlType decorate method DescribeSqlType
	DecorateDescribeSqlType(func(DescribeSqlTypeFunc) DescribeSqlTypeFunc) DataSourceServiceDecorator
	// DecorateDescribeInstanceAddress decorate method DescribeInstanceAddress
	DecorateDescribeInstanceAddress(func(DescribeInstanceAddressFunc) DescribeInstanceAddressFunc) DataSourceServiceDecorator
	// DecorateDescribeInstanceProxyAddress decorate method DescribeInstanceProxyAddress
	DecorateDescribeInstanceProxyAddress(func(DescribeInstanceProxyAddressFunc) DescribeInstanceProxyAddressFunc) DataSourceServiceDecorator
	// DecorateDescribeInstanceAddressList decorate method DescribeInstanceAddressList
	DecorateDescribeInstanceAddressList(func(DescribeInstanceAddressListFunc) DescribeInstanceAddressListFunc) DataSourceServiceDecorator
	// DecorateListTables decorate method ListTables
	DecorateListTables(func(ListTablesFunc) ListTablesFunc) DataSourceServiceDecorator
	// DecorateListAllTables decorate method ListAllTables
	DecorateListAllTables(func(ListAllTablesFunc) ListAllTablesFunc) DataSourceServiceDecorator
	// DecorateListSchemaTables decorate method ListSchemaTables
	DecorateListSchemaTables(func(ListSchemaTablesFunc) ListSchemaTablesFunc) DataSourceServiceDecorator
	// DecorateListTablesInfo decorate method ListTablesInfo
	DecorateListTablesInfo(func(ListTablesInfoFunc) ListTablesInfoFunc) DataSourceServiceDecorator
	// DecorateDescribeTable decorate method DescribeTable
	DecorateDescribeTable(func(DescribeTableFunc) DescribeTableFunc) DataSourceServiceDecorator
	// DecorateDescribeAutoKillSessionConfig decorate method DescribeAutoKillSessionConfig
	DecorateDescribeAutoKillSessionConfig(func(DescribeAutoKillSessionConfigFunc) DescribeAutoKillSessionConfigFunc) DataSourceServiceDecorator
	// DecorateModifyAutoKillSessionConfig decorate method ModifyAutoKillSessionConfig
	DecorateModifyAutoKillSessionConfig(func(ModifyAutoKillSessionConfigFunc) ModifyAutoKillSessionConfigFunc) DataSourceServiceDecorator
	// DecorateDescribeInstanceVersion decorate method DescribeInstanceVersion
	DecorateDescribeInstanceVersion(func(DescribeInstanceVersionFunc) DescribeInstanceVersionFunc) DataSourceServiceDecorator
	// DecorateListErrLogs decorate method ListErrLogs
	DecorateListErrLogs(func(ListErrLogsFunc) ListErrLogsFunc) DataSourceServiceDecorator
	// DecorateDescribePgTable decorate method DescribePgTable
	DecorateDescribePgTable(func(DescribePgTableFunc) DescribePgTableFunc) DataSourceServiceDecorator
	// DecorateDescribeInstanceReplicaDelay decorate method DescribeInstanceReplicaDelay
	DecorateDescribeInstanceReplicaDelay(func(DescribeInstanceReplicaDelayFunc) DescribeInstanceReplicaDelayFunc) DataSourceServiceDecorator
	// DecorateListViews decorate method ListViews
	DecorateListViews(func(ListViewsFunc) ListViewsFunc) DataSourceServiceDecorator
	// DecorateDescribeView decorate method DescribeView
	DecorateDescribeView(func(DescribeViewFunc) DescribeViewFunc) DataSourceServiceDecorator
	// DecorateDescribeFunction decorate method DescribeFunction
	DecorateDescribeFunction(func(DescribeFunctionFunc) DescribeFunctionFunc) DataSourceServiceDecorator
	// DecorateDescribeProcedure decorate method DescribeProcedure
	DecorateDescribeProcedure(func(DescribeProcedureFunc) DescribeProcedureFunc) DataSourceServiceDecorator
	// DecorateListFunctions decorate method ListFunctions
	DecorateListFunctions(func(ListFunctionsFunc) ListFunctionsFunc) DataSourceServiceDecorator
	// DecorateListProcedures decorate method ListProcedures
	DecorateListProcedures(func(ListProceduresFunc) ListProceduresFunc) DataSourceServiceDecorator
	// DecorateListTriggers decorate method ListTriggers
	DecorateListTriggers(func(ListTriggersFunc) ListTriggersFunc) DataSourceServiceDecorator
	// DecorateDescribeTLSConnectionInfo decorate method DescribeTLSConnectionInfo
	DecorateDescribeTLSConnectionInfo(func(DescribeTLSConnectionInfoFunc) DescribeTLSConnectionInfoFunc) DataSourceServiceDecorator
	// DecorateListKeyNumbers decorate method ListKeyNumbers
	DecorateListKeyNumbers(func(ListKeyNumbersFunc) ListKeyNumbersFunc) DataSourceServiceDecorator
	// DecorateListKeys decorate method ListKeys
	DecorateListKeys(func(ListKeysFunc) ListKeysFunc) DataSourceServiceDecorator
	// DecorateGetKey decorate method GetKey
	DecorateGetKey(func(GetKeyFunc) GetKeyFunc) DataSourceServiceDecorator
	// DecorateListKeyMembers decorate method ListKeyMembers
	DecorateListKeyMembers(func(ListKeyMembersFunc) ListKeyMembersFunc) DataSourceServiceDecorator
	// DecorateListAlterKVsCommands decorate method ListAlterKVsCommands
	DecorateListAlterKVsCommands(func(ListAlterKVsCommandsFunc) ListAlterKVsCommandsFunc) DataSourceServiceDecorator
	// DecorateDescribeBigKeys decorate method DescribeBigKeys
	DecorateDescribeBigKeys(func(DescribeBigKeysFunc) DescribeBigKeysFunc) DataSourceServiceDecorator
	// DecorateDescribeHotKeys decorate method DescribeHotKeys
	DecorateDescribeHotKeys(func(DescribeHotKeysFunc) DescribeHotKeysFunc) DataSourceServiceDecorator
	// DecorateOpenTunnel decorate method OpenTunnel
	DecorateOpenTunnel(func(OpenTunnelFunc) OpenTunnelFunc) DataSourceServiceDecorator
	// DecorateDescribeTrigger decorate method DescribeTrigger
	DecorateDescribeTrigger(func(DescribeTriggerFunc) DescribeTriggerFunc) DataSourceServiceDecorator
	// DecorateDescribeEvent decorate method DescribeEvent
	DecorateDescribeEvent(func(DescribeEventFunc) DescribeEventFunc) DataSourceServiceDecorator
	// DecorateListEvents decorate method ListEvents
	DecorateListEvents(func(ListEventsFunc) ListEventsFunc) DataSourceServiceDecorator
	// DecorateCreateAccount decorate method CreateAccount
	DecorateCreateAccount(func(CreateAccountFunc) CreateAccountFunc) DataSourceServiceDecorator
	// DecorateCheckPrivilege decorate method CheckPrivilege
	DecorateCheckPrivilege(func(CheckPrivilegeFunc) CheckPrivilegeFunc) DataSourceServiceDecorator
	// DecorateDescribeAccounts decorate method DescribeAccounts
	DecorateDescribeAccounts(func(DescribeAccountsFunc) DescribeAccountsFunc) DataSourceServiceDecorator
	// DecorateDescribeAccounts2 decorate method DescribeAccounts2
	DecorateDescribeAccounts2(func(DescribeAccounts2Func) DescribeAccounts2Func) DataSourceServiceDecorator
	// DecorateCreateAccountAndGrant decorate method CreateAccountAndGrant
	DecorateCreateAccountAndGrant(func(CreateAccountAndGrantFunc) CreateAccountAndGrantFunc) DataSourceServiceDecorator
	// DecorateModifyAccountPrivilege decorate method ModifyAccountPrivilege
	DecorateModifyAccountPrivilege(func(ModifyAccountPrivilegeFunc) ModifyAccountPrivilegeFunc) DataSourceServiceDecorator
	// DecorateGrantAccountPrivilege decorate method GrantAccountPrivilege
	DecorateGrantAccountPrivilege(func(GrantAccountPrivilegeFunc) GrantAccountPrivilegeFunc) DataSourceServiceDecorator
	// DecorateDeleteAccount decorate method DeleteAccount
	DecorateDeleteAccount(func(DeleteAccountFunc) DeleteAccountFunc) DataSourceServiceDecorator
	// DecorateListDatabasesWithAccount decorate method ListDatabasesWithAccount
	DecorateListDatabasesWithAccount(func(ListDatabasesWithAccountFunc) ListDatabasesWithAccountFunc) DataSourceServiceDecorator
	// DecorateGetAdvice decorate method GetAdvice
	DecorateGetAdvice(func(GetAdviceFunc) GetAdviceFunc) DataSourceServiceDecorator
	// DecorateListCharsets decorate method ListCharsets
	DecorateListCharsets(func(ListCharsetsFunc) ListCharsetsFunc) DataSourceServiceDecorator
	// DecorateListCollations decorate method ListCollations
	DecorateListCollations(func(ListCollationsFunc) ListCollationsFunc) DataSourceServiceDecorator
	// DecorateListSchema decorate method ListSchema
	DecorateListSchema(func(ListSchemaFunc) ListSchemaFunc) DataSourceServiceDecorator
	// DecorateListSequence decorate method ListSequence
	DecorateListSequence(func(ListSequenceFunc) ListSequenceFunc) DataSourceServiceDecorator
	// DecorateListPgCollations decorate method ListPgCollations
	DecorateListPgCollations(func(ListPgCollationsFunc) ListPgCollationsFunc) DataSourceServiceDecorator
	// DecorateListPgUsers decorate method ListPgUsers
	DecorateListPgUsers(func(ListPgUsersFunc) ListPgUsersFunc) DataSourceServiceDecorator
	// DecorateListTableSpaces decorate method ListTableSpaces
	DecorateListTableSpaces(func(ListTableSpacesFunc) ListTableSpacesFunc) DataSourceServiceDecorator
	// DecorateDescribeDialogDetails decorate method DescribeDialogDetails
	DecorateDescribeDialogDetails(func(DescribeDialogDetailsFunc) DescribeDialogDetailsFunc) DataSourceServiceDecorator
	// DecorateDescribeDialogStatistics decorate method DescribeDialogStatistics
	DecorateDescribeDialogStatistics(func(DescribeDialogStatisticsFunc) DescribeDialogStatisticsFunc) DataSourceServiceDecorator
	// DecorateDescribeEngineStatus decorate method DescribeEngineStatus
	DecorateDescribeEngineStatus(func(DescribeEngineStatusFunc) DescribeEngineStatusFunc) DataSourceServiceDecorator
	// DecorateKillProcess decorate method KillProcess
	DecorateKillProcess(func(KillProcessFunc) KillProcessFunc) DataSourceServiceDecorator
	// DecorateDescribeTrxAndLocks decorate method DescribeTrxAndLocks
	DecorateDescribeTrxAndLocks(func(DescribeTrxAndLocksFunc) DescribeTrxAndLocksFunc) DataSourceServiceDecorator
	// DecorateDescribeLockCurrentWaits decorate method DescribeLockCurrentWaits
	DecorateDescribeLockCurrentWaits(func(DescribeLockCurrentWaitsFunc) DescribeLockCurrentWaitsFunc) DataSourceServiceDecorator
	// DecorateDescribeDeadlock decorate method DescribeDeadlock
	DecorateDescribeDeadlock(func(DescribeDeadlockFunc) DescribeDeadlockFunc) DataSourceServiceDecorator
	// DecorateDescribeDeadlockDetect decorate method DescribeDeadlockDetect
	DecorateDescribeDeadlockDetect(func(DescribeDeadlockDetectFunc) DescribeDeadlockDetectFunc) DataSourceServiceDecorator
	// DecorateDescribeDialogInfos decorate method DescribeDialogInfos
	DecorateDescribeDialogInfos(func(DescribeDialogInfosFunc) DescribeDialogInfosFunc) DataSourceServiceDecorator
	// DecorateDescribeCurrentConn decorate method DescribeCurrentConn
	DecorateDescribeCurrentConn(func(DescribeCurrentConnFunc) DescribeCurrentConnFunc) DataSourceServiceDecorator
	// DecorateDescribeTableSpace decorate method DescribeTableSpace
	DecorateDescribeTableSpace(func(DescribeTableSpaceFunc) DescribeTableSpaceFunc) DataSourceServiceDecorator
	// DecorateDescribeTableSpaceAutoIncr decorate method DescribeTableSpaceAutoIncr
	DecorateDescribeTableSpaceAutoIncr(func(DescribeTableSpaceAutoIncrFunc) DescribeTableSpaceAutoIncrFunc) DataSourceServiceDecorator
	// DecorateConvertTableSpaceToModel decorate method ConvertTableSpaceToModel
	DecorateConvertTableSpaceToModel(func(ConvertTableSpaceToModelFunc) ConvertTableSpaceToModelFunc) DataSourceServiceDecorator
	// DecorateDescribeTableColumn decorate method DescribeTableColumn
	DecorateDescribeTableColumn(func(DescribeTableColumnFunc) DescribeTableColumnFunc) DataSourceServiceDecorator
	// DecorateConvertTableColumnToModel decorate method ConvertTableColumnToModel
	DecorateConvertTableColumnToModel(func(ConvertTableColumnToModelFunc) ConvertTableColumnToModelFunc) DataSourceServiceDecorator
	// DecorateDescribeTableIndex decorate method DescribeTableIndex
	DecorateDescribeTableIndex(func(DescribeTableIndexFunc) DescribeTableIndexFunc) DataSourceServiceDecorator
	// DecorateConvertTableIndexToModel decorate method ConvertTableIndexToModel
	DecorateConvertTableIndexToModel(func(ConvertTableIndexToModelFunc) ConvertTableIndexToModelFunc) DataSourceServiceDecorator
	// DecorateFormatDescribeStorageCapacityResp decorate method FormatDescribeStorageCapacityResp
	DecorateFormatDescribeStorageCapacityResp(func(FormatDescribeStorageCapacityRespFunc) FormatDescribeStorageCapacityRespFunc) DataSourceServiceDecorator
	// DecorateExecuteCCL decorate method ExecuteCCL
	DecorateExecuteCCL(func(ExecuteCCLFunc) ExecuteCCLFunc) DataSourceServiceDecorator
	// DecorateCCLShow decorate method CCLShow
	DecorateCCLShow(func(CCLShowFunc) CCLShowFunc) DataSourceServiceDecorator
	// DecorateGetAvgSlowQueries decorate method GetAvgSlowQueries
	DecorateGetAvgSlowQueries(func(GetAvgSlowQueriesFunc) GetAvgSlowQueriesFunc) DataSourceServiceDecorator
	// DecorateGetDataPointCountSlowQueries decorate method GetDataPointCountSlowQueries
	DecorateGetDataPointCountSlowQueries(func(GetDataPointCountSlowQueriesFunc) GetDataPointCountSlowQueriesFunc) DataSourceServiceDecorator
	// DecorateGetCpuMetricDetail decorate method GetCpuMetricDetail
	DecorateGetCpuMetricDetail(func(GetCpuMetricDetailFunc) GetCpuMetricDetailFunc) DataSourceServiceDecorator
	// DecorateGetAvgCpuUsage decorate method GetAvgCpuUsage
	DecorateGetAvgCpuUsage(func(GetAvgCpuUsageFunc) GetAvgCpuUsageFunc) DataSourceServiceDecorator
	// DecorateGetMinCpuUsage decorate method GetMinCpuUsage
	DecorateGetMinCpuUsage(func(GetMinCpuUsageFunc) GetMinCpuUsageFunc) DataSourceServiceDecorator
	// DecorateGetMaxCpuUsage decorate method GetMaxCpuUsage
	DecorateGetMaxCpuUsage(func(GetMaxCpuUsageFunc) GetMaxCpuUsageFunc) DataSourceServiceDecorator
	// DecorateGetCpuUsageMetricDetail decorate method GetCpuUsageMetricDetail
	DecorateGetCpuUsageMetricDetail(func(GetCpuUsageMetricDetailFunc) GetCpuUsageMetricDetailFunc) DataSourceServiceDecorator
	// DecorateDiagRootCauseALL decorate method DiagRootCauseALL
	DecorateDiagRootCauseALL(func(DiagRootCauseALLFunc) DiagRootCauseALLFunc) DataSourceServiceDecorator
	// DecorateDiagRootCauseYoYQoQ decorate method DiagRootCauseYoYQoQ
	DecorateDiagRootCauseYoYQoQ(func(DiagRootCauseYoYQoQFunc) DiagRootCauseYoYQoQFunc) DataSourceServiceDecorator
	// DecorateDiagRootCauseDiskMetrics decorate method DiagRootCauseDiskMetrics
	DecorateDiagRootCauseDiskMetrics(func(DiagRootCauseDiskMetricsFunc) DiagRootCauseDiskMetricsFunc) DataSourceServiceDecorator
	// DecorateGetMemMetricDetail decorate method GetMemMetricDetail
	DecorateGetMemMetricDetail(func(GetMemMetricDetailFunc) GetMemMetricDetailFunc) DataSourceServiceDecorator
	// DecorateGetAvgMemUsage decorate method GetAvgMemUsage
	DecorateGetAvgMemUsage(func(GetAvgMemUsageFunc) GetAvgMemUsageFunc) DataSourceServiceDecorator
	// DecorateGetMinMemUsage decorate method GetMinMemUsage
	DecorateGetMinMemUsage(func(GetMinMemUsageFunc) GetMinMemUsageFunc) DataSourceServiceDecorator
	// DecorateGetMaxMemUsage decorate method GetMaxMemUsage
	DecorateGetMaxMemUsage(func(GetMaxMemUsageFunc) GetMaxMemUsageFunc) DataSourceServiceDecorator
	// DecorateGetMemUsageMetricDetail decorate method GetMemUsageMetricDetail
	DecorateGetMemUsageMetricDetail(func(GetMemUsageMetricDetailFunc) GetMemUsageMetricDetailFunc) DataSourceServiceDecorator
	// DecorateGetAvgDiskUsage decorate method GetAvgDiskUsage
	DecorateGetAvgDiskUsage(func(GetAvgDiskUsageFunc) GetAvgDiskUsageFunc) DataSourceServiceDecorator
	// DecorateGetMaxDiskUsage decorate method GetMaxDiskUsage
	DecorateGetMaxDiskUsage(func(GetMaxDiskUsageFunc) GetMaxDiskUsageFunc) DataSourceServiceDecorator
	// DecorateGetMinDiskUsage decorate method GetMinDiskUsage
	DecorateGetMinDiskUsage(func(GetMinDiskUsageFunc) GetMinDiskUsageFunc) DataSourceServiceDecorator
	// DecorateGetDiskAvailableDays decorate method GetDiskAvailableDays
	DecorateGetDiskAvailableDays(func(GetDiskAvailableDaysFunc) GetDiskAvailableDaysFunc) DataSourceServiceDecorator
	// DecorateGetDiskFutureSize decorate method GetDiskFutureSize
	DecorateGetDiskFutureSize(func(GetDiskFutureSizeFunc) GetDiskFutureSizeFunc) DataSourceServiceDecorator
	// DecorateGetDiskMetricDetail decorate method GetDiskMetricDetail
	DecorateGetDiskMetricDetail(func(GetDiskMetricDetailFunc) GetDiskMetricDetailFunc) DataSourceServiceDecorator
	// DecorateGetDiskUsageMetricDetail decorate method GetDiskUsageMetricDetail
	DecorateGetDiskUsageMetricDetail(func(GetDiskUsageMetricDetailFunc) GetDiskUsageMetricDetailFunc) DataSourceServiceDecorator
	// DecorateGetAvgQpsUsage decorate method GetAvgQpsUsage
	DecorateGetAvgQpsUsage(func(GetAvgQpsUsageFunc) GetAvgQpsUsageFunc) DataSourceServiceDecorator
	// DecorateGetMaxQpsUsage decorate method GetMaxQpsUsage
	DecorateGetMaxQpsUsage(func(GetMaxQpsUsageFunc) GetMaxQpsUsageFunc) DataSourceServiceDecorator
	// DecorateGetMinQpsUsage decorate method GetMinQpsUsage
	DecorateGetMinQpsUsage(func(GetMinQpsUsageFunc) GetMinQpsUsageFunc) DataSourceServiceDecorator
	// DecorateGetQpsUsage decorate method GetQpsUsage
	DecorateGetQpsUsage(func(GetQpsUsageFunc) GetQpsUsageFunc) DataSourceServiceDecorator
	// DecorateGetAvgTpsUsage decorate method GetAvgTpsUsage
	DecorateGetAvgTpsUsage(func(GetAvgTpsUsageFunc) GetAvgTpsUsageFunc) DataSourceServiceDecorator
	// DecorateGetMaxTpsUsage decorate method GetMaxTpsUsage
	DecorateGetMaxTpsUsage(func(GetMaxTpsUsageFunc) GetMaxTpsUsageFunc) DataSourceServiceDecorator
	// DecorateGetMinTpsUsage decorate method GetMinTpsUsage
	DecorateGetMinTpsUsage(func(GetMinTpsUsageFunc) GetMinTpsUsageFunc) DataSourceServiceDecorator
	// DecorateGetTpsUsage decorate method GetTpsUsage
	DecorateGetTpsUsage(func(GetTpsUsageFunc) GetTpsUsageFunc) DataSourceServiceDecorator
	// DecorateGetAvgConnectionUsage decorate method GetAvgConnectionUsage
	DecorateGetAvgConnectionUsage(func(GetAvgConnectionUsageFunc) GetAvgConnectionUsageFunc) DataSourceServiceDecorator
	// DecorateGetMaxConnectionUsage decorate method GetMaxConnectionUsage
	DecorateGetMaxConnectionUsage(func(GetMaxConnectionUsageFunc) GetMaxConnectionUsageFunc) DataSourceServiceDecorator
	// DecorateGetMinConnectionUsage decorate method GetMinConnectionUsage
	DecorateGetMinConnectionUsage(func(GetMinConnectionUsageFunc) GetMinConnectionUsageFunc) DataSourceServiceDecorator
	// DecorateGetConnectedRatioMetricDetail decorate method GetConnectedRatioMetricDetail
	DecorateGetConnectedRatioMetricDetail(func(GetConnectedRatioMetricDetailFunc) GetConnectedRatioMetricDetailFunc) DataSourceServiceDecorator
	// DecorateGetSessionMetricDetail decorate method GetSessionMetricDetail
	DecorateGetSessionMetricDetail(func(GetSessionMetricDetailFunc) GetSessionMetricDetailFunc) DataSourceServiceDecorator
	// DecorateGetAvgSessionUsage decorate method GetAvgSessionUsage
	DecorateGetAvgSessionUsage(func(GetAvgSessionUsageFunc) GetAvgSessionUsageFunc) DataSourceServiceDecorator
	// DecorateGetMaxSessionUsage decorate method GetMaxSessionUsage
	DecorateGetMaxSessionUsage(func(GetMaxSessionUsageFunc) GetMaxSessionUsageFunc) DataSourceServiceDecorator
	// DecorateGetMinSessionUsage decorate method GetMinSessionUsage
	DecorateGetMinSessionUsage(func(GetMinSessionUsageFunc) GetMinSessionUsageFunc) DataSourceServiceDecorator
	// DecorateGetLatestDiskUsage decorate method GetLatestDiskUsage
	DecorateGetLatestDiskUsage(func(GetLatestDiskUsageFunc) GetLatestDiskUsageFunc) DataSourceServiceDecorator
	// DecorateGetInspectionCpuMetric decorate method GetInspectionCpuMetric
	DecorateGetInspectionCpuMetric(func(GetInspectionCpuMetricFunc) GetInspectionCpuMetricFunc) DataSourceServiceDecorator
	// DecorateGetInspectionMemMetric decorate method GetInspectionMemMetric
	DecorateGetInspectionMemMetric(func(GetInspectionMemMetricFunc) GetInspectionMemMetricFunc) DataSourceServiceDecorator
	// DecorateGetInspectionDiskMetric decorate method GetInspectionDiskMetric
	DecorateGetInspectionDiskMetric(func(GetInspectionDiskMetricFunc) GetInspectionDiskMetricFunc) DataSourceServiceDecorator
	// DecorateGetInspectionQpsMetric decorate method GetInspectionQpsMetric
	DecorateGetInspectionQpsMetric(func(GetInspectionQpsMetricFunc) GetInspectionQpsMetricFunc) DataSourceServiceDecorator
	// DecorateGetInspectionTpsMetric decorate method GetInspectionTpsMetric
	DecorateGetInspectionTpsMetric(func(GetInspectionTpsMetricFunc) GetInspectionTpsMetricFunc) DataSourceServiceDecorator
	// DecorateGetInspectionConnectedMetric decorate method GetInspectionConnectedMetric
	DecorateGetInspectionConnectedMetric(func(GetInspectionConnectedMetricFunc) GetInspectionConnectedMetricFunc) DataSourceServiceDecorator
	// DecorateGetInspectionConnRatioMetric decorate method GetInspectionConnRatioMetric
	DecorateGetInspectionConnRatioMetric(func(GetInspectionConnRatioMetricFunc) GetInspectionConnRatioMetricFunc) DataSourceServiceDecorator
	// DecorateGetInspectionBpHitMetric decorate method GetInspectionBpHitMetric
	DecorateGetInspectionBpHitMetric(func(GetInspectionBpHitMetricFunc) GetInspectionBpHitMetricFunc) DataSourceServiceDecorator
	// DecorateGetInspectionOutputMetric decorate method GetInspectionOutputMetric
	DecorateGetInspectionOutputMetric(func(GetInspectionOutputMetricFunc) GetInspectionOutputMetricFunc) DataSourceServiceDecorator
	// DecorateGetInspectionInputMetric decorate method GetInspectionInputMetric
	DecorateGetInspectionInputMetric(func(GetInspectionInputMetricFunc) GetInspectionInputMetricFunc) DataSourceServiceDecorator
	// DecorateHealthSummary decorate method HealthSummary
	DecorateHealthSummary(func(HealthSummaryFunc) HealthSummaryFunc) DataSourceServiceDecorator
	// DecorateGetMonitorByMetric decorate method GetMonitorByMetric
	DecorateGetMonitorByMetric(func(GetMonitorByMetricFunc) GetMonitorByMetricFunc) DataSourceServiceDecorator
	// DecorateListCollections decorate method ListCollections
	DecorateListCollections(func(ListCollectionsFunc) ListCollectionsFunc) DataSourceServiceDecorator
	// DecorateListIndexs decorate method ListIndexs
	DecorateListIndexs(func(ListIndexsFunc) ListIndexsFunc) DataSourceServiceDecorator
	// DecorateListMongoDBs decorate method ListMongoDBs
	DecorateListMongoDBs(func(ListMongoDBsFunc) ListMongoDBsFunc) DataSourceServiceDecorator
	// DecorateCreateFreeLockCorrectOrder decorate method CreateFreeLockCorrectOrder
	DecorateCreateFreeLockCorrectOrder(func(CreateFreeLockCorrectOrderFunc) CreateFreeLockCorrectOrderFunc) DataSourceServiceDecorator
	// DecorateCreateFreeLockCorrectOrderDryRun decorate method CreateFreeLockCorrectOrderDryRun
	DecorateCreateFreeLockCorrectOrderDryRun(func(CreateFreeLockCorrectOrderDryRunFunc) CreateFreeLockCorrectOrderDryRunFunc) DataSourceServiceDecorator
	// DecorateDescribeFreeLockCorrectOrders decorate method DescribeFreeLockCorrectOrders
	DecorateDescribeFreeLockCorrectOrders(func(DescribeFreeLockCorrectOrdersFunc) DescribeFreeLockCorrectOrdersFunc) DataSourceServiceDecorator
	// DecorateStopFreeLockCorrectOrders decorate method StopFreeLockCorrectOrders
	DecorateStopFreeLockCorrectOrders(func(StopFreeLockCorrectOrdersFunc) StopFreeLockCorrectOrdersFunc) DataSourceServiceDecorator
	// DecoratePreCheckFreeLockCorrectOrders decorate method PreCheckFreeLockCorrectOrders
	DecoratePreCheckFreeLockCorrectOrders(func(PreCheckFreeLockCorrectOrdersFunc) PreCheckFreeLockCorrectOrdersFunc) DataSourceServiceDecorator
	// DecorateGetDBInnerAddress decorate method GetDBInnerAddress
	DecorateGetDBInnerAddress(func(GetDBInnerAddressFunc) GetDBInnerAddressFunc) DataSourceServiceDecorator
	// DecorateExecuteDQL decorate method ExecuteDQL
	DecorateExecuteDQL(func(ExecuteDQLFunc) ExecuteDQLFunc) DataSourceServiceDecorator
	// DecorateExecuteDMLAndGetAffectedRows decorate method ExecuteDMLAndGetAffectedRows
	DecorateExecuteDMLAndGetAffectedRows(func(ExecuteDMLAndGetAffectedRowsFunc) ExecuteDMLAndGetAffectedRowsFunc) DataSourceServiceDecorator
	// DecorateGetPartitionInfos decorate method GetPartitionInfos
	DecorateGetPartitionInfos(func(GetPartitionInfosFunc) GetPartitionInfosFunc) DataSourceServiceDecorator
	// DecorateGetShardingDbType decorate method GetShardingDbType
	DecorateGetShardingDbType(func(GetShardingDbTypeFunc) GetShardingDbTypeFunc) DataSourceServiceDecorator
	// DecorateExplainCommand decorate method ExplainCommand
	DecorateExplainCommand(func(ExplainCommandFunc) ExplainCommandFunc) DataSourceServiceDecorator
	// DecorateIsMyOwnInstance decorate method IsMyOwnInstance
	DecorateIsMyOwnInstance(func(IsMyOwnInstanceFunc) IsMyOwnInstanceFunc) DataSourceServiceDecorator
	// DecorateCheckInstanceState decorate method CheckInstanceState
	DecorateCheckInstanceState(func(CheckInstanceStateFunc) CheckInstanceStateFunc) DataSourceServiceDecorator
	// DecorateGetTableIndexInfo decorate method GetTableIndexInfo
	DecorateGetTableIndexInfo(func(GetTableIndexInfoFunc) GetTableIndexInfoFunc) DataSourceServiceDecorator
	// DecorateGetTableIndexValue decorate method GetTableIndexValue
	DecorateGetTableIndexValue(func(GetTableIndexValueFunc) GetTableIndexValueFunc) DataSourceServiceDecorator
	// DecorateGetMaxConnections decorate method GetMaxConnections
	DecorateGetMaxConnections(func(GetMaxConnectionsFunc) GetMaxConnectionsFunc) DataSourceServiceDecorator
	// DecorateGetCurrentBandwidth decorate method GetCurrentBandwidth
	DecorateGetCurrentBandwidth(func(GetCurrentBandwidthFunc) GetCurrentBandwidthFunc) DataSourceServiceDecorator
	// DecorateBandwidthScale decorate method BandwidthScale
	DecorateBandwidthScale(func(BandwidthScaleFunc) BandwidthScaleFunc) DataSourceServiceDecorator
	// DecorateGetMinBandwidth decorate method GetMinBandwidth
	DecorateGetMinBandwidth(func(GetMinBandwidthFunc) GetMinBandwidthFunc) DataSourceServiceDecorator
	// DecorateGetMaxBandwidth decorate method GetMaxBandwidth
	DecorateGetMaxBandwidth(func(GetMaxBandwidthFunc) GetMaxBandwidthFunc) DataSourceServiceDecorator
	// DecorateGetDiskSize decorate method GetDiskSize
	DecorateGetDiskSize(func(GetDiskSizeFunc) GetDiskSizeFunc) DataSourceServiceDecorator
	// DecorateGetUsedSize decorate method GetUsedSize
	DecorateGetUsedSize(func(GetUsedSizeFunc) GetUsedSizeFunc) DataSourceServiceDecorator
	// DecorateGetCurrentMetricData decorate method GetCurrentMetricData
	DecorateGetCurrentMetricData(func(GetCurrentMetricDataFunc) GetCurrentMetricDataFunc) DataSourceServiceDecorator
	// DecorateGetPreSecondMetricData decorate method GetPreSecondMetricData
	DecorateGetPreSecondMetricData(func(GetPreSecondMetricDataFunc) GetPreSecondMetricDataFunc) DataSourceServiceDecorator
	// DecorateGetPreSecondMetricDataByInstance decorate method GetPreSecondMetricDataByInstance
	DecorateGetPreSecondMetricDataByInstance(func(GetPreSecondMetricDataByInstanceFunc) GetPreSecondMetricDataByInstanceFunc) DataSourceServiceDecorator
	// DecorateDescribeFullSQLLogConfig decorate method DescribeFullSQLLogConfig
	DecorateDescribeFullSQLLogConfig(func(DescribeFullSQLLogConfigFunc) DescribeFullSQLLogConfigFunc) DataSourceServiceDecorator
	// DecorateModifyFullSQLLogConfig decorate method ModifyFullSQLLogConfig
	DecorateModifyFullSQLLogConfig(func(ModifyFullSQLLogConfigFunc) ModifyFullSQLLogConfigFunc) DataSourceServiceDecorator
	// DecorateDescribeInstanceVariables decorate method DescribeInstanceVariables
	DecorateDescribeInstanceVariables(func(DescribeInstanceVariablesFunc) DescribeInstanceVariablesFunc) DataSourceServiceDecorator
	// DecorateDescribePrimaryKeyRange decorate method DescribePrimaryKeyRange
	DecorateDescribePrimaryKeyRange(func(DescribePrimaryKeyRangeFunc) DescribePrimaryKeyRangeFunc) DataSourceServiceDecorator
	// DecorateDescribeSQLAdvisorTableMeta decorate method DescribeSQLAdvisorTableMeta
	DecorateDescribeSQLAdvisorTableMeta(func(DescribeSQLAdvisorTableMetaFunc) DescribeSQLAdvisorTableMetaFunc) DataSourceServiceDecorator
	// DecorateDescribeSampleData decorate method DescribeSampleData
	DecorateDescribeSampleData(func(DescribeSampleDataFunc) DescribeSampleDataFunc) DataSourceServiceDecorator
	// DecorateEnsureAccount decorate method EnsureAccount
	DecorateEnsureAccount(func(EnsureAccountFunc) EnsureAccountFunc) DataSourceServiceDecorator
	// DecorateGetDatasourceAddress decorate method GetDatasourceAddress
	DecorateGetDatasourceAddress(func(GetDatasourceAddressFunc) GetDatasourceAddressFunc) DataSourceServiceDecorator
	// DecorateGetDBServiceTreeMountInfo decorate method GetDBServiceTreeMountInfo
	DecorateGetDBServiceTreeMountInfo(func(GetDBServiceTreeMountInfoFunc) GetDBServiceTreeMountInfoFunc) DataSourceServiceDecorator
	// DecorateGetDBInstanceInfo decorate method GetDBInstanceInfo
	DecorateGetDBInstanceInfo(func(GetDBInstanceInfoFunc) GetDBInstanceInfoFunc) DataSourceServiceDecorator
	// DecorateInstanceIsExist decorate method InstanceIsExist
	DecorateInstanceIsExist(func(InstanceIsExistFunc) InstanceIsExistFunc) DataSourceServiceDecorator
	// DecorateGetInstanceTopo decorate method GetInstanceTopo
	DecorateGetInstanceTopo(func(GetInstanceTopoFunc) GetInstanceTopoFunc) DataSourceServiceDecorator
	// DecorateGetInstanceProxyTopo decorate method GetInstanceProxyTopo
	DecorateGetInstanceProxyTopo(func(GetInstanceProxyTopoFunc) GetInstanceProxyTopoFunc) DataSourceServiceDecorator
	// DecorateCreateLogDownloadTask decorate method CreateLogDownloadTask
	DecorateCreateLogDownloadTask(func(CreateLogDownloadTaskFunc) CreateLogDownloadTaskFunc) DataSourceServiceDecorator
	// DecorateGetLogDownloadList decorate method GetLogDownloadList
	DecorateGetLogDownloadList(func(GetLogDownloadListFunc) GetLogDownloadListFunc) DataSourceServiceDecorator
	// DecorateGetInstancePrimaryNodeId decorate method GetInstancePrimaryNodeId
	DecorateGetInstancePrimaryNodeId(func(GetInstancePrimaryNodeIdFunc) GetInstancePrimaryNodeIdFunc) DataSourceServiceDecorator
	// DecorateListSQLKillRules decorate method ListSQLKillRules
	DecorateListSQLKillRules(func(ListSQLKillRulesFunc) ListSQLKillRulesFunc) DataSourceServiceDecorator
	// DecorateModifySQLKillRule decorate method ModifySQLKillRule
	DecorateModifySQLKillRule(func(ModifySQLKillRuleFunc) ModifySQLKillRuleFunc) DataSourceServiceDecorator
	// DecorateGetManagedAccountAndPwd decorate method GetManagedAccountAndPwd
	DecorateGetManagedAccountAndPwd(func(GetManagedAccountAndPwdFunc) GetManagedAccountAndPwdFunc) DataSourceServiceDecorator
	// DecorateCalculateSpecAfterScale decorate method CalculateSpecAfterScale
	DecorateCalculateSpecAfterScale(func(CalculateSpecAfterScaleFunc) CalculateSpecAfterScaleFunc) DataSourceServiceDecorator
	// DecorateModifyDBInstanceSpec decorate method ModifyDBInstanceSpec
	DecorateModifyDBInstanceSpec(func(ModifyDBInstanceSpecFunc) ModifyDBInstanceSpecFunc) DataSourceServiceDecorator
	// DecorateDescribeDBAutoScalingConfig decorate method DescribeDBAutoScalingConfig
	DecorateDescribeDBAutoScalingConfig(func(DescribeDBAutoScalingConfigFunc) DescribeDBAutoScalingConfigFunc) DataSourceServiceDecorator
	// DecorateModifyDBAutoScalingConfig decorate method ModifyDBAutoScalingConfig
	DecorateModifyDBAutoScalingConfig(func(ModifyDBAutoScalingConfigFunc) ModifyDBAutoScalingConfigFunc) DataSourceServiceDecorator
	// DecorateDescribeDBAutoScaleEvents decorate method DescribeDBAutoScaleEvents
	DecorateDescribeDBAutoScaleEvents(func(DescribeDBAutoScaleEventsFunc) DescribeDBAutoScaleEventsFunc) DataSourceServiceDecorator
	// DecorateModifyDBLocalSpecManually decorate method ModifyDBLocalSpecManually
	DecorateModifyDBLocalSpecManually(func(ModifyDBLocalSpecManuallyFunc) ModifyDBLocalSpecManuallyFunc) DataSourceServiceDecorator
	// DecorateValidateDryRun decorate method ValidateDryRun
	DecorateValidateDryRun(func(ValidateDryRunFunc) ValidateDryRunFunc) DataSourceServiceDecorator
	// DecorateValidateOriginalTable decorate method ValidateOriginalTable
	DecorateValidateOriginalTable(func(ValidateOriginalTableFunc) ValidateOriginalTableFunc) DataSourceServiceDecorator
	// DecorateValidateUniqIndex decorate method ValidateUniqIndex
	DecorateValidateUniqIndex(func(ValidateUniqIndexFunc) ValidateUniqIndexFunc) DataSourceServiceDecorator
	// DecorateExecuteSql decorate method ExecuteSql
	DecorateExecuteSql(func(ExecuteSqlFunc) ExecuteSqlFunc) DataSourceServiceDecorator
	// DecorateGetCurrentMaxConnections decorate method GetCurrentMaxConnections
	DecorateGetCurrentMaxConnections(func(GetCurrentMaxConnectionsFunc) GetCurrentMaxConnectionsFunc) DataSourceServiceDecorator
	// DecorateGetInstanceSlaveAddress decorate method GetInstanceSlaveAddress
	DecorateGetInstanceSlaveAddress(func(GetInstanceSlaveAddressFunc) GetInstanceSlaveAddressFunc) DataSourceServiceDecorator
	// DecorateDescribeDBInstanceDetailForPilot decorate method DescribeDBInstanceDetailForPilot
	DecorateDescribeDBInstanceDetailForPilot(func(DescribeDBInstanceDetailForPilotFunc) DescribeDBInstanceDetailForPilotFunc) DataSourceServiceDecorator
	// DecorateDescribeDBInstanceParametersForPilot decorate method DescribeDBInstanceParametersForPilot
	DecorateDescribeDBInstanceParametersForPilot(func(DescribeDBInstanceParametersForPilotFunc) DescribeDBInstanceParametersForPilotFunc) DataSourceServiceDecorator
	// DecorateGetCreateTableInfo decorate method GetCreateTableInfo
	DecorateGetCreateTableInfo(func(GetCreateTableInfoFunc) GetCreateTableInfoFunc) DataSourceServiceDecorator
	// DecorateCheckAccountPrivilege decorate method CheckAccountPrivilege
	DecorateCheckAccountPrivilege(func(CheckAccountPrivilegeFunc) CheckAccountPrivilegeFunc) DataSourceServiceDecorator
	// DecorateResetAccount decorate method ResetAccount
	DecorateResetAccount(func(ResetAccountFunc) ResetAccountFunc) DataSourceServiceDecorator
	// DecorateGrantReplicationPrivilege decorate method GrantReplicationPrivilege
	DecorateGrantReplicationPrivilege(func(GrantReplicationPrivilegeFunc) GrantReplicationPrivilegeFunc) DataSourceServiceDecorator
	// DecorateDescribeInstancePodAddress decorate method DescribeInstancePodAddress
	DecorateDescribeInstancePodAddress(func(DescribeInstancePodAddressFunc) DescribeInstancePodAddressFunc) DataSourceServiceDecorator

	// DecorateAny decorate any method
	DecorateAny(match func(string, reflect.Type) bool, mw func(in []interface{}, next DataSourceServiceCommonFunc) []interface{}) DataSourceServiceDecorator
	// Export as DataSourceService
	Export() DataSourceService
}

type _DataSourceServiceDecorator struct {
	_Type                                 TypeFunc
	_AddWhiteList                         AddWhiteListFunc
	_UpdateWhiteList                      UpdateWhiteListFunc
	_RemoveWhiteList                      RemoveWhiteListFunc
	_FillDataSource                       FillDataSourceFunc
	_FillInnerDataSource                  FillInnerDataSourceFunc
	_CheckConn                            CheckConnFunc
	_CheckDataSource                      CheckDataSourceFunc
	_KillQuery                            KillQueryFunc
	_ListInstance                         ListInstanceFunc
	_ListInstanceLightWeight              ListInstanceLightWeightFunc
	_ListDatabases                        ListDatabasesFunc
	_ListInstanceNodes                    ListInstanceNodesFunc
	_ListInstanceNodesOri                 ListInstanceNodesOriFunc
	_ListInstancePods                     ListInstancePodsFunc
	_DescribeDBInstanceDetail             DescribeDBInstanceDetailFunc
	_DescribeDBInstanceSpec               DescribeDBInstanceSpecFunc
	_DescribeDBInstanceEndpoints          DescribeDBInstanceEndpointsFunc
	_DescribeDBInstanceShardInfos         DescribeDBInstanceShardInfosFunc
	_DescribeDBInstanceCluster            DescribeDBInstanceClusterFunc
	_DescribeDBInstanceAuditCollectedPod  DescribeDBInstanceAuditCollectedPodFunc
	_OpenDBInstanceAuditLog               OpenDBInstanceAuditLogFunc
	_CloseDBInstanceAuditLog              CloseDBInstanceAuditLogFunc
	_CheckDBInstanceAuditLogStatus        CheckDBInstanceAuditLogStatusFunc
	_DescribeDBProxyConfig                DescribeDBProxyConfigFunc
	_DescribeInstanceFeatures             DescribeInstanceFeaturesFunc
	_DescribeDBInstanceSSL                DescribeDBInstanceSSLFunc
	_DescribeSQLCCLConfig                 DescribeSQLCCLConfigFunc
	_ModifySQLCCLConfig                   ModifySQLCCLConfigFunc
	_AddSQLCCLRule                        AddSQLCCLRuleFunc
	_ModifyProxyThrottleRule              ModifyProxyThrottleRuleFunc
	_DeleteSQLCCLRule                     DeleteSQLCCLRuleFunc
	_FlushSQLCCLRule                      FlushSQLCCLRuleFunc
	_ListSQLCCLRules                      ListSQLCCLRulesFunc
	_DescribeSqlFingerPrintOrKeywords     DescribeSqlFingerPrintOrKeywordsFunc
	_DescribeSqlType                      DescribeSqlTypeFunc
	_DescribeInstanceAddress              DescribeInstanceAddressFunc
	_DescribeInstanceProxyAddress         DescribeInstanceProxyAddressFunc
	_DescribeInstanceAddressList          DescribeInstanceAddressListFunc
	_ListTables                           ListTablesFunc
	_ListAllTables                        ListAllTablesFunc
	_ListSchemaTables                     ListSchemaTablesFunc
	_ListTablesInfo                       ListTablesInfoFunc
	_DescribeTable                        DescribeTableFunc
	_DescribeAutoKillSessionConfig        DescribeAutoKillSessionConfigFunc
	_ModifyAutoKillSessionConfig          ModifyAutoKillSessionConfigFunc
	_DescribeInstanceVersion              DescribeInstanceVersionFunc
	_ListErrLogs                          ListErrLogsFunc
	_DescribePgTable                      DescribePgTableFunc
	_DescribeInstanceReplicaDelay         DescribeInstanceReplicaDelayFunc
	_ListViews                            ListViewsFunc
	_DescribeView                         DescribeViewFunc
	_DescribeFunction                     DescribeFunctionFunc
	_DescribeProcedure                    DescribeProcedureFunc
	_ListFunctions                        ListFunctionsFunc
	_ListProcedures                       ListProceduresFunc
	_ListTriggers                         ListTriggersFunc
	_DescribeTLSConnectionInfo            DescribeTLSConnectionInfoFunc
	_ListKeyNumbers                       ListKeyNumbersFunc
	_ListKeys                             ListKeysFunc
	_GetKey                               GetKeyFunc
	_ListKeyMembers                       ListKeyMembersFunc
	_ListAlterKVsCommands                 ListAlterKVsCommandsFunc
	_DescribeBigKeys                      DescribeBigKeysFunc
	_DescribeHotKeys                      DescribeHotKeysFunc
	_OpenTunnel                           OpenTunnelFunc
	_DescribeTrigger                      DescribeTriggerFunc
	_DescribeEvent                        DescribeEventFunc
	_ListEvents                           ListEventsFunc
	_CreateAccount                        CreateAccountFunc
	_CheckPrivilege                       CheckPrivilegeFunc
	_DescribeAccounts                     DescribeAccountsFunc
	_DescribeAccounts2                    DescribeAccounts2Func
	_CreateAccountAndGrant                CreateAccountAndGrantFunc
	_ModifyAccountPrivilege               ModifyAccountPrivilegeFunc
	_GrantAccountPrivilege                GrantAccountPrivilegeFunc
	_DeleteAccount                        DeleteAccountFunc
	_ListDatabasesWithAccount             ListDatabasesWithAccountFunc
	_GetAdvice                            GetAdviceFunc
	_ListCharsets                         ListCharsetsFunc
	_ListCollations                       ListCollationsFunc
	_ListSchema                           ListSchemaFunc
	_ListSequence                         ListSequenceFunc
	_ListPgCollations                     ListPgCollationsFunc
	_ListPgUsers                          ListPgUsersFunc
	_ListTableSpaces                      ListTableSpacesFunc
	_DescribeDialogDetails                DescribeDialogDetailsFunc
	_DescribeDialogStatistics             DescribeDialogStatisticsFunc
	_DescribeEngineStatus                 DescribeEngineStatusFunc
	_KillProcess                          KillProcessFunc
	_DescribeTrxAndLocks                  DescribeTrxAndLocksFunc
	_DescribeLockCurrentWaits             DescribeLockCurrentWaitsFunc
	_DescribeDeadlock                     DescribeDeadlockFunc
	_DescribeDeadlockDetect               DescribeDeadlockDetectFunc
	_DescribeDialogInfos                  DescribeDialogInfosFunc
	_DescribeCurrentConn                  DescribeCurrentConnFunc
	_DescribeTableSpace                   DescribeTableSpaceFunc
	_DescribeTableSpaceAutoIncr           DescribeTableSpaceAutoIncrFunc
	_ConvertTableSpaceToModel             ConvertTableSpaceToModelFunc
	_DescribeTableColumn                  DescribeTableColumnFunc
	_ConvertTableColumnToModel            ConvertTableColumnToModelFunc
	_DescribeTableIndex                   DescribeTableIndexFunc
	_ConvertTableIndexToModel             ConvertTableIndexToModelFunc
	_FormatDescribeStorageCapacityResp    FormatDescribeStorageCapacityRespFunc
	_ExecuteCCL                           ExecuteCCLFunc
	_CCLShow                              CCLShowFunc
	_GetAvgSlowQueries                    GetAvgSlowQueriesFunc
	_GetDataPointCountSlowQueries         GetDataPointCountSlowQueriesFunc
	_GetCpuMetricDetail                   GetCpuMetricDetailFunc
	_GetAvgCpuUsage                       GetAvgCpuUsageFunc
	_GetMinCpuUsage                       GetMinCpuUsageFunc
	_GetMaxCpuUsage                       GetMaxCpuUsageFunc
	_GetCpuUsageMetricDetail              GetCpuUsageMetricDetailFunc
	_DiagRootCauseALL                     DiagRootCauseALLFunc
	_DiagRootCauseYoYQoQ                  DiagRootCauseYoYQoQFunc
	_DiagRootCauseDiskMetrics             DiagRootCauseDiskMetricsFunc
	_GetMemMetricDetail                   GetMemMetricDetailFunc
	_GetAvgMemUsage                       GetAvgMemUsageFunc
	_GetMinMemUsage                       GetMinMemUsageFunc
	_GetMaxMemUsage                       GetMaxMemUsageFunc
	_GetMemUsageMetricDetail              GetMemUsageMetricDetailFunc
	_GetAvgDiskUsage                      GetAvgDiskUsageFunc
	_GetMaxDiskUsage                      GetMaxDiskUsageFunc
	_GetMinDiskUsage                      GetMinDiskUsageFunc
	_GetDiskAvailableDays                 GetDiskAvailableDaysFunc
	_GetDiskFutureSize                    GetDiskFutureSizeFunc
	_GetDiskMetricDetail                  GetDiskMetricDetailFunc
	_GetDiskUsageMetricDetail             GetDiskUsageMetricDetailFunc
	_GetAvgQpsUsage                       GetAvgQpsUsageFunc
	_GetMaxQpsUsage                       GetMaxQpsUsageFunc
	_GetMinQpsUsage                       GetMinQpsUsageFunc
	_GetQpsUsage                          GetQpsUsageFunc
	_GetAvgTpsUsage                       GetAvgTpsUsageFunc
	_GetMaxTpsUsage                       GetMaxTpsUsageFunc
	_GetMinTpsUsage                       GetMinTpsUsageFunc
	_GetTpsUsage                          GetTpsUsageFunc
	_GetAvgConnectionUsage                GetAvgConnectionUsageFunc
	_GetMaxConnectionUsage                GetMaxConnectionUsageFunc
	_GetMinConnectionUsage                GetMinConnectionUsageFunc
	_GetConnectedRatioMetricDetail        GetConnectedRatioMetricDetailFunc
	_GetSessionMetricDetail               GetSessionMetricDetailFunc
	_GetAvgSessionUsage                   GetAvgSessionUsageFunc
	_GetMaxSessionUsage                   GetMaxSessionUsageFunc
	_GetMinSessionUsage                   GetMinSessionUsageFunc
	_GetLatestDiskUsage                   GetLatestDiskUsageFunc
	_GetInspectionCpuMetric               GetInspectionCpuMetricFunc
	_GetInspectionMemMetric               GetInspectionMemMetricFunc
	_GetInspectionDiskMetric              GetInspectionDiskMetricFunc
	_GetInspectionQpsMetric               GetInspectionQpsMetricFunc
	_GetInspectionTpsMetric               GetInspectionTpsMetricFunc
	_GetInspectionConnectedMetric         GetInspectionConnectedMetricFunc
	_GetInspectionConnRatioMetric         GetInspectionConnRatioMetricFunc
	_GetInspectionBpHitMetric             GetInspectionBpHitMetricFunc
	_GetInspectionOutputMetric            GetInspectionOutputMetricFunc
	_GetInspectionInputMetric             GetInspectionInputMetricFunc
	_HealthSummary                        HealthSummaryFunc
	_GetMonitorByMetric                   GetMonitorByMetricFunc
	_ListCollections                      ListCollectionsFunc
	_ListIndexs                           ListIndexsFunc
	_ListMongoDBs                         ListMongoDBsFunc
	_CreateFreeLockCorrectOrder           CreateFreeLockCorrectOrderFunc
	_CreateFreeLockCorrectOrderDryRun     CreateFreeLockCorrectOrderDryRunFunc
	_DescribeFreeLockCorrectOrders        DescribeFreeLockCorrectOrdersFunc
	_StopFreeLockCorrectOrders            StopFreeLockCorrectOrdersFunc
	_PreCheckFreeLockCorrectOrders        PreCheckFreeLockCorrectOrdersFunc
	_GetDBInnerAddress                    GetDBInnerAddressFunc
	_ExecuteDQL                           ExecuteDQLFunc
	_ExecuteDMLAndGetAffectedRows         ExecuteDMLAndGetAffectedRowsFunc
	_GetPartitionInfos                    GetPartitionInfosFunc
	_GetShardingDbType                    GetShardingDbTypeFunc
	_ExplainCommand                       ExplainCommandFunc
	_IsMyOwnInstance                      IsMyOwnInstanceFunc
	_CheckInstanceState                   CheckInstanceStateFunc
	_GetTableIndexInfo                    GetTableIndexInfoFunc
	_GetTableIndexValue                   GetTableIndexValueFunc
	_GetMaxConnections                    GetMaxConnectionsFunc
	_GetCurrentBandwidth                  GetCurrentBandwidthFunc
	_BandwidthScale                       BandwidthScaleFunc
	_GetMinBandwidth                      GetMinBandwidthFunc
	_GetMaxBandwidth                      GetMaxBandwidthFunc
	_GetDiskSize                          GetDiskSizeFunc
	_GetUsedSize                          GetUsedSizeFunc
	_GetCurrentMetricData                 GetCurrentMetricDataFunc
	_GetPreSecondMetricData               GetPreSecondMetricDataFunc
	_GetPreSecondMetricDataByInstance     GetPreSecondMetricDataByInstanceFunc
	_DescribeFullSQLLogConfig             DescribeFullSQLLogConfigFunc
	_ModifyFullSQLLogConfig               ModifyFullSQLLogConfigFunc
	_DescribeInstanceVariables            DescribeInstanceVariablesFunc
	_DescribePrimaryKeyRange              DescribePrimaryKeyRangeFunc
	_DescribeSQLAdvisorTableMeta          DescribeSQLAdvisorTableMetaFunc
	_DescribeSampleData                   DescribeSampleDataFunc
	_EnsureAccount                        EnsureAccountFunc
	_GetDatasourceAddress                 GetDatasourceAddressFunc
	_GetDBServiceTreeMountInfo            GetDBServiceTreeMountInfoFunc
	_GetDBInstanceInfo                    GetDBInstanceInfoFunc
	_InstanceIsExist                      InstanceIsExistFunc
	_GetInstanceTopo                      GetInstanceTopoFunc
	_GetInstanceProxyTopo                 GetInstanceProxyTopoFunc
	_CreateLogDownloadTask                CreateLogDownloadTaskFunc
	_GetLogDownloadList                   GetLogDownloadListFunc
	_GetInstancePrimaryNodeId             GetInstancePrimaryNodeIdFunc
	_ListSQLKillRules                     ListSQLKillRulesFunc
	_ModifySQLKillRule                    ModifySQLKillRuleFunc
	_GetManagedAccountAndPwd              GetManagedAccountAndPwdFunc
	_CalculateSpecAfterScale              CalculateSpecAfterScaleFunc
	_ModifyDBInstanceSpec                 ModifyDBInstanceSpecFunc
	_DescribeDBAutoScalingConfig          DescribeDBAutoScalingConfigFunc
	_ModifyDBAutoScalingConfig            ModifyDBAutoScalingConfigFunc
	_DescribeDBAutoScaleEvents            DescribeDBAutoScaleEventsFunc
	_ModifyDBLocalSpecManually            ModifyDBLocalSpecManuallyFunc
	_ValidateDryRun                       ValidateDryRunFunc
	_ValidateOriginalTable                ValidateOriginalTableFunc
	_ValidateUniqIndex                    ValidateUniqIndexFunc
	_ExecuteSql                           ExecuteSqlFunc
	_GetCurrentMaxConnections             GetCurrentMaxConnectionsFunc
	_GetInstanceSlaveAddress              GetInstanceSlaveAddressFunc
	_DescribeDBInstanceDetailForPilot     DescribeDBInstanceDetailForPilotFunc
	_DescribeDBInstanceParametersForPilot DescribeDBInstanceParametersForPilotFunc
	_GetCreateTableInfo                   GetCreateTableInfoFunc
	_CheckAccountPrivilege                CheckAccountPrivilegeFunc
	_ResetAccount                         ResetAccountFunc
	_GrantReplicationPrivilege            GrantReplicationPrivilegeFunc
	_DescribeInstancePodAddress           DescribeInstancePodAddressFunc
}

// NewDataSourceServiceDecorator create decorator for DataSourceService
func NewDataSourceServiceDecorator(impl DataSourceService) DataSourceServiceDecorator {
	if impl == nil {
		return &_DataSourceServiceDecorator{
			_Type:                    func() (ret0 shared.DataSourceType) { return },
			_AddWhiteList:            func(arg0 context.Context, arg1 string, arg2 *shared.DataSource) (ret0 string, ret1 error) { return },
			_UpdateWhiteList:         func(arg0 context.Context, arg1 string, arg2 *shared.DataSource, arg3 []string) (ret0 error) { return },
			_RemoveWhiteList:         func(arg0 context.Context, arg1 string, arg2 *shared.DataSource, arg3 string) (ret0 error) { return },
			_FillDataSource:          func(arg0 context.Context, arg1 *shared.DataSource) (ret0 error) { return },
			_FillInnerDataSource:     func(arg0 context.Context, arg1 *shared.DataSource) (ret0 error) { return },
			_CheckConn:               func(arg0 context.Context, arg1 *shared.DataSource) (ret0 error) { return },
			_CheckDataSource:         func(arg0 context.Context, arg1 *shared.DataSource) (ret0 error) { return },
			_KillQuery:               func(arg0 context.Context, arg1 *shared.DataSource, arg2 *shared.ConnectionInfo) (ret0 error) { return },
			_ListInstance:            func(arg0 context.Context, arg1 *ListInstanceReq) (ret0 *ListInstanceResp, ret1 error) { return },
			_ListInstanceLightWeight: func(arg0 context.Context, arg1 *ListInstanceReq) (ret0 *ListInstanceResp, ret1 error) { return },
			_ListDatabases:           func(arg0 context.Context, arg1 *ListDatabasesReq) (ret0 *ListDatabasesResp, ret1 error) { return },
			_ListInstanceNodes: func(arg0 context.Context, arg1 *ListInstanceNodesReq) (ret0 *ListInstanceNodesResp, ret1 error) {
				return
			},
			_ListInstanceNodesOri: func(arg0 context.Context, arg1 *ListInstanceNodesReq) (ret0 *ListInstanceNodesOriResp, ret1 error) {
				return
			},
			_ListInstancePods: func(arg0 context.Context, arg1 *ListInstancePodsReq) (ret0 *ListInstancePodsResp, ret1 error) { return },
			_DescribeDBInstanceDetail: func(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 *DescribeDBInstanceDetailResp, ret1 error) {
				return
			},
			_DescribeDBInstanceSpec: func(arg0 context.Context, arg1 *DescribeDBInstanceSpecReq) (ret0 *DescribeDBInstanceSpecResp, ret1 error) {
				return
			},
			_DescribeDBInstanceEndpoints: func(arg0 context.Context, arg1 *DescribeDBInstanceEndpointsReq) (ret0 *DescribeDBInstanceEndpointsResp, ret1 error) {
				return
			},
			_DescribeDBInstanceShardInfos: func(arg0 context.Context, arg1 *DescribeDBInstanceShardInfosReq) (ret0 *DescribeDBInstanceShardInfosResp, ret1 error) {
				return
			},
			_DescribeDBInstanceCluster: func(arg0 context.Context, arg1 *DescribeDBInstanceClusterReq) (ret0 *DescribeDBInstanceClusterResp, ret1 error) {
				return
			},
			_DescribeDBInstanceAuditCollectedPod: func(arg0 context.Context, arg1 *DescribeDBInstanceAuditCollectedPodReq) (ret0 *DescribeDBInstanceAuditCollectedPodResp, ret1 error) {
				return
			},
			_OpenDBInstanceAuditLog: func(arg0 context.Context, arg1 *OpenDBInstanceAuditLogReq) (ret0 *OpenDBInstanceAuditLogResp, ret1 error) {
				return
			},
			_CloseDBInstanceAuditLog: func(arg0 context.Context, arg1 *CloseDBInstanceAuditLogReq) (ret0 *CloseDBInstanceAuditLogResp, ret1 error) {
				return
			},
			_CheckDBInstanceAuditLogStatus: func(arg0 context.Context, arg1 *CheckDBInstanceAuditLogStatusReq) (ret0 *CheckDBInstanceAuditLogStatusResp, ret1 error) {
				return
			},
			_DescribeDBProxyConfig: func(arg0 context.Context, arg1 *DescribeDBProxyConfigReq) (ret0 *DescribeDBProxyConfigResp, ret1 error) {
				return
			},
			_DescribeInstanceFeatures: func(arg0 context.Context, arg1 *DescribeInstanceFeaturesReq) (ret0 *DescribeInstanceFeaturesResp, ret1 error) {
				return
			},
			_DescribeDBInstanceSSL: func(arg0 context.Context, arg1 *DescribeDBInstanceSSLReq) (ret0 *DescribeDBInstanceSSLResp, ret1 error) {
				return
			},
			_DescribeSQLCCLConfig: func(arg0 context.Context, arg1 *DescribeSQLCCLConfigReq) (ret0 *DescribeSQLCCLConfigResp, ret1 error) {
				return
			},
			_ModifySQLCCLConfig: func(arg0 context.Context, arg1 *ModifySQLCCLConfigReq) (ret0 *ModifySQLCCLConfigResp, ret1 error) {
				return
			},
			_AddSQLCCLRule: func(arg0 context.Context, arg1 *AddSQLCCLRuleReq) (ret0 *AddSQLCCLRuleResp, ret1 error) { return },
			_ModifyProxyThrottleRule: func(arg0 context.Context, arg1 *ModifyProxyThrottleRuleReq) (ret0 *ModifyProxyThrottleRuleResp, ret1 error) {
				return
			},
			_DeleteSQLCCLRule: func(arg0 context.Context, arg1 *DeleteSQLCCLRuleReq) (ret0 *DeleteSQLCCLRuleResp, ret1 error) { return },
			_FlushSQLCCLRule:  func(arg0 context.Context, arg1 *FlushSQLCCLRuleReq) (ret0 *FlushSQLCCLRuleResp, ret1 error) { return },
			_ListSQLCCLRules:  func(arg0 context.Context, arg1 *ListSQLCCLRulesReq) (ret0 *ListSQLCCLRulesResp, ret1 error) { return },
			_DescribeSqlFingerPrintOrKeywords: func(arg0 context.Context, arg1 *DescribeSqlFingerPrintOrKeywordsReq) (ret0 *DescribeSqlFingerPrintOrKeywordsResp, ret1 error) {
				return
			},
			_DescribeSqlType: func(arg0 context.Context, arg1 *DescribeSqlTypeReq) (ret0 *DescribeSqlTypeResp, ret1 error) { return },
			_DescribeInstanceAddress: func(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 *DescribeInstanceAddressResp, ret1 error) {
				return
			},
			_DescribeInstanceProxyAddress: func(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 *DescribeInstanceAddressResp, ret1 error) {
				return
			},
			_DescribeInstanceAddressList: func(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 []*DescribeInstanceAddressResp, ret1 error) {
				return
			},
			_ListTables:       func(arg0 context.Context, arg1 *ListTablesReq) (ret0 *ListTablesResp, ret1 error) { return },
			_ListAllTables:    func(arg0 context.Context, arg1 *ListTablesReq) (ret0 *ListTablesResp, ret1 error) { return },
			_ListSchemaTables: func(arg0 context.Context, arg1 *ListSchemaTablesReq) (ret0 *ListSchemaTablesResp, ret1 error) { return },
			_ListTablesInfo:   func(arg0 context.Context, arg1 *ListTablesInfoReq) (ret0 *ListTablesInfoResp, ret1 error) { return },
			_DescribeTable:    func(arg0 context.Context, arg1 *DescribeTableReq) (ret0 *DescribeTableResp, ret1 error) { return },
			_DescribeAutoKillSessionConfig: func(arg0 context.Context, arg1 *DescribeAutoKillSessionConfigReq) (ret0 *DescribeAutoKillSessionConfigResp, ret1 error) {
				return
			},
			_ModifyAutoKillSessionConfig: func(arg0 context.Context, arg1 *ModifyAutoKillSessionConfigReq) (ret0 *ModifyAutoKillSessionConfigResp, ret1 error) {
				return
			},
			_DescribeInstanceVersion: func(arg0 context.Context, arg1 *DescribeInstanceVersionReq) (ret0 *DescribeInstanceVersionResp, ret1 error) {
				return
			},
			_ListErrLogs:                  func(arg0 context.Context, arg1 *ListErrLogsReq) (ret0 *ListErrLogsResp, ret1 error) { return },
			_DescribePgTable:              func(arg0 context.Context, arg1 *DescribePgTableReq) (ret0 *DescribePgTableResp, ret1 error) { return },
			_DescribeInstanceReplicaDelay: func(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 int64, ret1 error) { return },
			_ListViews:                    func(arg0 context.Context, arg1 *ListViewsReq) (ret0 *ListViewsResp, ret1 error) { return },
			_DescribeView:                 func(arg0 context.Context, arg1 *DescribeViewReq) (ret0 *DescribeViewResp, ret1 error) { return },
			_DescribeFunction:             func(arg0 context.Context, arg1 *DescribeFunctionReq) (ret0 *DescribeFunctionResp, ret1 error) { return },
			_DescribeProcedure: func(arg0 context.Context, arg1 *DescribeProcedureReq) (ret0 *DescribeProcedureResp, ret1 error) {
				return
			},
			_ListFunctions:  func(arg0 context.Context, arg1 *ListFunctionsReq) (ret0 *ListFunctionsResp, ret1 error) { return },
			_ListProcedures: func(arg0 context.Context, arg1 *ListProceduresReq) (ret0 *ListProceduresResp, ret1 error) { return },
			_ListTriggers:   func(arg0 context.Context, arg1 *ListTriggersReq) (ret0 *ListTriggersResp, ret1 error) { return },
			_DescribeTLSConnectionInfo: func(arg0 context.Context, arg1 *DescribeTLSConnectionInfoReq) (ret0 *DescribeTLSConnectionInfoResp, ret1 error) {
				return
			},
			_ListKeyNumbers: func(arg0 context.Context, arg1 *ListKeyNumbersReq) (ret0 *ListKeyNumbersResp, ret1 error) { return },
			_ListKeys:       func(arg0 context.Context, arg1 *ListKeysReq) (ret0 *ListKeysResp, ret1 error) { return },
			_GetKey:         func(arg0 context.Context, arg1 *GetKeyReq) (ret0 *GetKeyResp, ret1 error) { return },
			_ListKeyMembers: func(arg0 context.Context, arg1 *ListKeyMembersReq) (ret0 *ListKeyMembersResp, ret1 error) { return },
			_ListAlterKVsCommands: func(arg0 context.Context, arg1 *ListAlterKVsCommandsReq) (ret0 *ListAlterKVsCommandsResp, ret1 error) {
				return
			},
			_DescribeBigKeys: func(arg0 context.Context, arg1 *DescribeBigKeysReq) (ret0 *DescribeBigKeysResp, ret1 error) { return },
			_DescribeHotKeys: func(arg0 context.Context, arg1 *DescribeHotKeysReq) (ret0 *DescribeHotKeysResp, ret1 error) { return },
			_OpenTunnel:      func(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 error) { return },
			_DescribeTrigger: func(arg0 context.Context, arg1 *DescribeTriggerReq) (ret0 *DescribeTriggerResp, ret1 error) { return },
			_DescribeEvent:   func(arg0 context.Context, arg1 *DescribeEventReq) (ret0 *DescribeEventResp, ret1 error) { return },
			_ListEvents:      func(arg0 context.Context, arg1 *ListEventsReq) (ret0 *ListEventsResp, ret1 error) { return },
			_CreateAccount:   func(arg0 context.Context, arg1 *CreateAccountReq) (ret0 error) { return },
			_CheckPrivilege: func(arg0 context.Context, arg1 string, arg2 string, arg3 string, arg4 string, arg5 shared.DataSourceType) (ret0 bool, ret1 error) {
				return
			},
			_DescribeAccounts:  func(arg0 context.Context, arg1 *DescribeAccountsReq) (ret0 *DescribeAccountResp, ret1 error) { return },
			_DescribeAccounts2: func(arg0 context.Context, arg1 *DescribeAccountsReq) (ret0 *DescribeAccountResp2, ret1 error) { return },
			_CreateAccountAndGrant: func(arg0 context.Context, arg1 string, arg2 string, arg3 string, arg4 string, arg5 string, arg6 shared.DataSourceType) (ret0 error) {
				return
			},
			_ModifyAccountPrivilege: func(arg0 context.Context, arg1 *ModifyAccountPrivilegeReq) (ret0 error) { return },
			_GrantAccountPrivilege:  func(arg0 context.Context, arg1 *GrantAccountPrivilegeReq) (ret0 error) { return },
			_DeleteAccount:          func(arg0 context.Context, arg1 *DeleteAccountReq) (ret0 error) { return },
			_ListDatabasesWithAccount: func(arg0 context.Context, arg1 *ListDatabasesWithAccountReq) (ret0 *ListDatabasesWithAccountResp, ret1 error) {
				return
			},
			_GetAdvice:        func(arg0 context.Context, arg1 *GetAdviceReq) (ret0 *GetAdviceResp, ret1 error) { return },
			_ListCharsets:     func(arg0 context.Context, arg1 *ListCharsetsReq) (ret0 *ListCharsetsResp, ret1 error) { return },
			_ListCollations:   func(arg0 context.Context, arg1 *ListCollationsReq) (ret0 *ListCollationsResp, ret1 error) { return },
			_ListSchema:       func(arg0 context.Context, arg1 *ListSchemaReq) (ret0 *ListSchemaResp, ret1 error) { return },
			_ListSequence:     func(arg0 context.Context, arg1 *ListSequenceReq) (ret0 *ListSequenceResp, ret1 error) { return },
			_ListPgCollations: func(arg0 context.Context, arg1 *ListPgCollationsReq) (ret0 *ListPgCollationsResp, ret1 error) { return },
			_ListPgUsers:      func(arg0 context.Context, arg1 *ListPgUsersReq) (ret0 *ListPgUsersResp, ret1 error) { return },
			_ListTableSpaces:  func(arg0 context.Context, arg1 *ListTableSpacesReq) (ret0 *ListTableSpacesResp, ret1 error) { return },
			_DescribeDialogDetails: func(arg0 context.Context, arg1 *DescribeDialogDetailsReq) (ret0 *DescribeDialogDetailsResp, ret1 error) {
				return
			},
			_DescribeDialogStatistics: func(arg0 context.Context, arg1 *DescribeDialogStatisticsReq) (ret0 *DescribeDialogStatisticsResp, ret1 error) {
				return
			},
			_DescribeEngineStatus: func(arg0 context.Context, arg1 *DescribeEngineStatusReq) (ret0 *DescribeEngineStatusResp, ret1 error) {
				return
			},
			_KillProcess: func(arg0 context.Context, arg1 *KillProcessReq) (ret0 *KillProcessResp, ret1 error) { return },
			_DescribeTrxAndLocks: func(arg0 context.Context, arg1 *DescribeTrxAndLocksReq) (ret0 *DescribeTrxAndLocksResp, ret1 error) {
				return
			},
			_DescribeLockCurrentWaits: func(arg0 context.Context, arg1 *DescribeLockCurrentWaitsReq) (ret0 *DescribeLockCurrentWaitsResp, ret1 error) {
				return
			},
			_DescribeDeadlock: func(arg0 context.Context, arg1 *DescribeDeadlockReq) (ret0 *DescribeDeadlockResp, ret1 error) { return },
			_DescribeDeadlockDetect: func(arg0 context.Context, arg1 *DescribeDeadlockDetectReq) (ret0 *DescribeDeadlockDetectResp, ret1 error) {
				return
			},
			_DescribeDialogInfos: func(arg0 context.Context, arg1 *DescribeDialogInfosReq) (ret0 *DescribeDialogInfosResp, ret1 error) {
				return
			},
			_DescribeCurrentConn: func(arg0 context.Context, arg1 *DescribeCurrentConnsReq) (ret0 *DescribeCurrentConnsResp, ret1 error) {
				return
			},
			_DescribeTableSpace: func(arg0 context.Context, arg1 *DescribeTableSpaceReq) (ret0 *shared.DescribeTableSpaceResp, ret1 error) {
				return
			},
			_DescribeTableSpaceAutoIncr: func(arg0 context.Context, arg1 *DescribeTableSpaceReq) (ret0 *shared.DescribeTableSpaceAutoIncrResp, ret1 error) {
				return
			},
			_ConvertTableSpaceToModel: func(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableSpaceResp) (ret0 *model.DescribeTableSpaceResp) {
				return
			},
			_DescribeTableColumn: func(arg0 context.Context, arg1 *DescribeTableInfoReq) (ret0 *shared.DescribeTableColumnResp, ret1 error) {
				return
			},
			_ConvertTableColumnToModel: func(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableColumnResp) (ret0 *model.DescribeTableColumnResp) {
				return
			},
			_DescribeTableIndex: func(arg0 context.Context, arg1 *DescribeTableInfoReq) (ret0 *shared.DescribeTableIndexResp, ret1 error) {
				return
			},
			_ConvertTableIndexToModel: func(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableIndexResp) (ret0 *model.DescribeTableIndexResp) {
				return
			},
			_FormatDescribeStorageCapacityResp: func(arg0 shared.DataSourceType, arg1 *GetDiskSizeResp, arg2 float64) (ret0 *model.DescribeStorageCapacityResp) {
				return
			},
			_ExecuteCCL:                   func(arg0 context.Context, arg1 *ExecuteCCLReq) (ret0 *ExecuteCCLResp, ret1 error) { return },
			_CCLShow:                      func(arg0 context.Context, arg1 *CCLShowReq) (ret0 *CCLShowResp, ret1 error) { return },
			_GetAvgSlowQueries:            func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetDataPointCountSlowQueries: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 int, ret1 error) { return },
			_GetCpuMetricDetail: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error) {
				return
			},
			_GetAvgCpuUsage:          func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMinCpuUsage:          func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMaxCpuUsage:          func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetCpuUsageMetricDetail: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_DiagRootCauseALL: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagRootCauseResp, ret1 error) {
				return
			},
			_DiagRootCauseYoYQoQ:      func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 map[string]float64, ret1 error) { return },
			_DiagRootCauseDiskMetrics: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 map[string]string, ret1 error) { return },
			_GetMemMetricDetail: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error) {
				return
			},
			_GetAvgMemUsage:          func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMinMemUsage:          func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMaxMemUsage:          func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMemUsageMetricDetail: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetAvgDiskUsage:         func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMaxDiskUsage:         func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMinDiskUsage:         func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetDiskAvailableDays: func(arg0 context.Context, arg1 *GetDiskAvailableDaysReq) (ret0 *GetDiskAvailableDaysResp, ret1 error) {
				return
			},
			_GetDiskFutureSize: func(arg0 context.Context, arg1 *GetDiskFutureSizeReq) (ret0 *GetDiskFutureSizeResp, ret1 error) {
				return
			},
			_GetDiskMetricDetail: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error) {
				return
			},
			_GetDiskUsageMetricDetail:      func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetAvgQpsUsage:                func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMaxQpsUsage:                func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMinQpsUsage:                func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetQpsUsage:                   func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetAvgTpsUsage:                func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMaxTpsUsage:                func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMinTpsUsage:                func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetTpsUsage:                   func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetAvgConnectionUsage:         func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMaxConnectionUsage:         func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMinConnectionUsage:         func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetConnectedRatioMetricDetail: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetSessionMetricDetail: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error) {
				return
			},
			_GetAvgSessionUsage:           func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMaxSessionUsage:           func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetMinSessionUsage:           func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) { return },
			_GetLatestDiskUsage:           func(arg0 context.Context, arg1 *GetLatestDiskUsageReq) (ret0 float64, ret1 error) { return },
			_GetInspectionCpuMetric:       func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetInspectionMemMetric:       func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetInspectionDiskMetric:      func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetInspectionQpsMetric:       func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetInspectionTpsMetric:       func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetInspectionConnectedMetric: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetInspectionConnRatioMetric: func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetInspectionBpHitMetric:     func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetInspectionOutputMetric:    func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_GetInspectionInputMetric:     func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) { return },
			_HealthSummary:                func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 []*model.Resource, ret1 error) { return },
			_GetMonitorByMetric: func(arg0 context.Context, arg1 *GetMonitorByMetricReq) (ret0 []*model.MetricResource, ret1 error) {
				return
			},
			_ListCollections: func(arg0 context.Context, arg1 *ListCollectionsReq) (ret0 *ListCollectionsResp, ret1 error) { return },
			_ListIndexs:      func(arg0 context.Context, arg1 *ListIndexesReq) (ret0 *ListIndexesResp, ret1 error) { return },
			_ListMongoDBs:    func(arg0 context.Context, arg1 *ListMongoDBsReq) (ret0 *ListMongoDBsResp, ret1 error) { return },
			_CreateFreeLockCorrectOrder: func(arg0 context.Context, arg1 *CreateFreeLockCorrectOrderReq) (ret0 *CreateFreeLockCorrectOrderResp, ret1 error) {
				return
			},
			_CreateFreeLockCorrectOrderDryRun: func(arg0 context.Context, arg1 *CreateFreeLockCorrectOrderReq) (ret0 *CreateFreeLockCorrectOrderDryRunResp, ret1 error) {
				return
			},
			_DescribeFreeLockCorrectOrders: func(arg0 context.Context, arg1 *DescribeFreeLockCorrectOrdersReq) (ret0 *DescribeFreeLockCorrectOrdersResp, ret1 error) {
				return
			},
			_StopFreeLockCorrectOrders: func(arg0 context.Context, arg1 *StopFreeLockCorrectOrdersReq) (ret0 error) { return },
			_PreCheckFreeLockCorrectOrders: func(arg0 context.Context, arg1 *PreCheckFreeLockCorrectOrdersReq) (ret0 *PreCheckFreeLockCorrectOrdersResp, ret1 error) {
				return
			},
			_GetDBInnerAddress: func(arg0 context.Context, arg1 *GetDBInnerAddressReq) (ret0 *GetDBInnerAddressResp, ret1 error) {
				return
			},
			_ExecuteDQL:                   func(arg0 context.Context, arg1 *ExecuteDQLReq) (ret0 *ExecuteDQLResp, ret1 error) { return },
			_ExecuteDMLAndGetAffectedRows: func(arg0 context.Context, arg1 *ExecuteDMLAndGetAffectedRowsReq) (ret0 int64, ret1 error) { return },
			_GetPartitionInfos: func(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 []*DbPartitionInfo, ret1 error) {
				return
			},
			_GetShardingDbType: func(arg0 context.Context, arg1 *shared.DataSource, arg2 string, arg3 string) (ret0 string, ret1 error) {
				return
			},
			_ExplainCommand:     func(arg0 context.Context, arg1 *ExplainCommandReq) (ret0 *ExplainCommandResp, ret1 error) { return },
			_IsMyOwnInstance:    func(arg0 context.Context, arg1 string, arg2 shared.DataSourceType) (ret0 bool) { return },
			_CheckInstanceState: func(arg0 context.Context, arg1 string, arg2 shared.DataSourceType, arg3 bool) (ret0 error) { return },
			_GetTableIndexInfo: func(arg0 context.Context, arg1 *GetTableIndexInfoReq) (ret0 *GetTableInfoIndexResp, ret1 error) {
				return
			},
			_GetTableIndexValue: func(arg0 context.Context, arg1 *GetIndexValueReq) (ret0 *GetIndexValueResp, ret1 error) { return },
			_GetMaxConnections:  func(arg0 context.Context, arg1 *GetMaxConnectionsReq) (ret0 int, ret1 error) { return },
			_GetCurrentBandwidth: func(arg0 context.Context, arg1 *GetCurrentBandwidthReq) (ret0 *InstanceBandwidthInfo, ret1 error) {
				return
			},
			_BandwidthScale:  func(arg0 context.Context, arg1 *BandwidthScaleReq) (ret0 error) { return },
			_GetMinBandwidth: func(arg0 context.Context, arg1 *GetMinMaxBandwidthReq) (ret0 int, ret1 error) { return },
			_GetMaxBandwidth: func(arg0 context.Context, arg1 *GetMinMaxBandwidthReq) (ret0 int, ret1 error) { return },
			_GetDiskSize:     func(arg0 context.Context, arg1 *GetDiskSizeReq) (ret0 *GetDiskSizeResp, ret1 error) { return },
			_GetUsedSize:     func(arg0 context.Context, arg1 *GetDiskSizeReq) (ret0 int64, ret1 error) { return },
			_GetCurrentMetricData: func(arg0 context.Context, arg1 *GetMetricDatapointsReq) (ret0 *GetMetricDatapointsResp, ret1 error) {
				return
			},
			_GetPreSecondMetricData: func(arg0 context.Context, arg1 *GetMetricDatapointsReq) (ret0 *GetMetricDatapointsResp, ret1 error) {
				return
			},
			_GetPreSecondMetricDataByInstance: func(arg0 context.Context, arg1 *GetMetricDatapointsReq) (ret0 *GetMetricDatapointsResp, ret1 error) {
				return
			},
			_DescribeFullSQLLogConfig: func(arg0 context.Context, arg1 *DescribeFullSQLLogConfigReq) (ret0 *DescribeFullSQLLogConfigResp, ret1 error) {
				return
			},
			_ModifyFullSQLLogConfig: func(arg0 context.Context, arg1 *ModifyFullSQLLogConfigReq) (ret0 *ModifyFullSQLLogConfigResp, ret1 error) {
				return
			},
			_DescribeInstanceVariables: func(arg0 context.Context, arg1 *DescribeInstanceVariablesReq) (ret0 *DescribeInstanceVariablesResp, ret1 error) {
				return
			},
			_DescribePrimaryKeyRange: func(arg0 context.Context, arg1 *DescribePrimaryKeyRangeReq) (ret0 *DescribePrimaryKeyRangeResp, ret1 error) {
				return
			},
			_DescribeSQLAdvisorTableMeta: func(arg0 context.Context, arg1 *DescribeSQLAdvisorTableMetaReq) (ret0 *DescribeSQLAdvisorTableMetaResp, ret1 error) {
				return
			},
			_DescribeSampleData: func(arg0 context.Context, arg1 *DescribeSampleDataReq) (ret0 *DescribeSampleDataResp, ret1 error) {
				return
			},
			_EnsureAccount:        func(arg0 context.Context, arg1 *EnsureAccountReq) (ret0 error) { return },
			_GetDatasourceAddress: func(arg0 context.Context, arg1 *shared.DataSource) (ret0 error) { return },
			_GetDBServiceTreeMountInfo: func(arg0 context.Context, arg1 *GetDBServiceTreeMountInfoReq) (ret0 *GetDBServiceTreeMountInfoResp, ret1 error) {
				return
			},
			_GetDBInstanceInfo: func(arg0 context.Context, arg1 *GetDBInstanceInfoReq) (ret0 *GetDBInstanceInfoResp, ret1 error) {
				return
			},
			_InstanceIsExist: func(arg0 context.Context, arg1 *InstanceIsExistReq) (ret0 bool, ret1 error) { return },
			_GetInstanceTopo: func(arg0 context.Context, arg1 *GetInstanceTopoReq) (ret0 []*model.InnerRdsInstance, ret1 error) {
				return
			},
			_GetInstanceProxyTopo: func(arg0 context.Context, arg1 *GetInstanceTopoReq) (ret0 []*model.InnerRdsInstance, ret1 error) {
				return
			},
			_CreateLogDownloadTask: func(arg0 context.Context, arg1 *CreateLogDownloadTaskReq) (ret0 error) { return },
			_GetLogDownloadList: func(arg0 context.Context, arg1 *GetLogDownloadListReq) (ret0 *GetLogDownloadListResp, ret1 error) {
				return
			},
			_GetInstancePrimaryNodeId: func(arg0 context.Context, arg1 *GetInstancePrimaryNodeIdReq) (ret0 *GetInstancePrimaryNodeIdResp, ret1 error) {
				return
			},
			_ListSQLKillRules: func(arg0 context.Context, arg1 *ListSQLKillRulesReq) (ret0 *ListSQLKillRulesResp, ret1 error) { return },
			_ModifySQLKillRule: func(arg0 context.Context, arg1 *ModifySQLKillRuleReq) (ret0 *ModifySQLKillRuleResp, ret1 error) {
				return
			},
			_GetManagedAccountAndPwd: func(arg0 context.Context, arg1 *shared.DataSource) (ret0 *GetManagedAccountAndPwdResp, ret1 error) {
				return
			},
			_CalculateSpecAfterScale: func(arg0 context.Context, arg1 *CalculateSpecAfterScaleReq) (ret0 *CalculateSpecAfterScaleResp, ret1 error) {
				return
			},
			_ModifyDBInstanceSpec: func(arg0 context.Context, arg1 *ModifyDBInstanceSpecReq) (ret0 *ModifyDBInstanceSpecResp, ret1 error) {
				return
			},
			_DescribeDBAutoScalingConfig: func(arg0 context.Context, arg1 *DescribeDBAutoScalingConfigReq) (ret0 *DescribeDBAutoScalingConfigResp, ret1 error) {
				return
			},
			_ModifyDBAutoScalingConfig: func(arg0 context.Context, arg1 *ModifyDBAutoScalingConfigReq) (ret0 *ModifyDBAutoScalingConfigResp, ret1 error) {
				return
			},
			_DescribeDBAutoScaleEvents: func(arg0 context.Context, arg1 *DescribeDBAutoScaleEventsReq) (ret0 *DescribeDBAutoScaleEventsResp, ret1 error) {
				return
			},
			_ModifyDBLocalSpecManually: func(arg0 context.Context, arg1 *ModifyDBLocalSpecManuallyReq) (ret0 *ModifyDBLocalSpecManuallyResp, ret1 error) {
				return
			},
			_ValidateDryRun:           func(arg0 context.Context, arg1 *ValidateDryRunReq) (ret0 *shared.ValidateResponse) { return },
			_ValidateOriginalTable:    func(arg0 context.Context, arg1 *ValidateOriginalTableReq) (ret0 *shared.ValidateResponse) { return },
			_ValidateUniqIndex:        func(arg0 context.Context, arg1 *ValidateUniqIndexReq) (ret0 *shared.ValidateResponse) { return },
			_ExecuteSql:               func(arg0 context.Context, arg1 *ExecuteReq) (ret0 error) { return },
			_GetCurrentMaxConnections: func(arg0 context.Context, arg1 *GetCurrentMaxConnectionsReq) (ret0 int, ret1 error) { return },
			_GetInstanceSlaveAddress: func(arg0 types.Context, arg1 *GetInstanceSlaveAddressReq) (ret0 *GetInstanceSlaveAddressResp, ret1 error) {
				return
			},
			_DescribeDBInstanceDetailForPilot:     func(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 string, ret1 error) { return },
			_DescribeDBInstanceParametersForPilot: func(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 string, ret1 error) { return },
			_GetCreateTableInfo:                   func(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 string, ret1 error) { return },
			_CheckAccountPrivilege:                func(arg0 context.Context, arg1 *CheckDBWAccountReq) (ret0 bool, ret1 error) { return },
			_ResetAccount:                         func(arg0 context.Context, arg1 string, arg2 shared.DataSourceType) (ret0 error) { return },
			_GrantReplicationPrivilege:            func(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 error) { return },
			_DescribeInstancePodAddress: func(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 *DescribeInstanceAddressResp, ret1 error) {
				return
			},
		}
	}
	return &_DataSourceServiceDecorator{
		_Type:                                 impl.Type,
		_AddWhiteList:                         impl.AddWhiteList,
		_UpdateWhiteList:                      impl.UpdateWhiteList,
		_RemoveWhiteList:                      impl.RemoveWhiteList,
		_FillDataSource:                       impl.FillDataSource,
		_FillInnerDataSource:                  impl.FillInnerDataSource,
		_CheckConn:                            impl.CheckConn,
		_CheckDataSource:                      impl.CheckDataSource,
		_KillQuery:                            impl.KillQuery,
		_ListInstance:                         impl.ListInstance,
		_ListInstanceLightWeight:              impl.ListInstanceLightWeight,
		_ListDatabases:                        impl.ListDatabases,
		_ListInstanceNodes:                    impl.ListInstanceNodes,
		_ListInstanceNodesOri:                 impl.ListInstanceNodesOri,
		_ListInstancePods:                     impl.ListInstancePods,
		_DescribeDBInstanceDetail:             impl.DescribeDBInstanceDetail,
		_DescribeDBInstanceSpec:               impl.DescribeDBInstanceSpec,
		_DescribeDBInstanceEndpoints:          impl.DescribeDBInstanceEndpoints,
		_DescribeDBInstanceShardInfos:         impl.DescribeDBInstanceShardInfos,
		_DescribeDBInstanceCluster:            impl.DescribeDBInstanceCluster,
		_DescribeDBInstanceAuditCollectedPod:  impl.DescribeDBInstanceAuditCollectedPod,
		_OpenDBInstanceAuditLog:               impl.OpenDBInstanceAuditLog,
		_CloseDBInstanceAuditLog:              impl.CloseDBInstanceAuditLog,
		_CheckDBInstanceAuditLogStatus:        impl.CheckDBInstanceAuditLogStatus,
		_DescribeDBProxyConfig:                impl.DescribeDBProxyConfig,
		_DescribeInstanceFeatures:             impl.DescribeInstanceFeatures,
		_DescribeDBInstanceSSL:                impl.DescribeDBInstanceSSL,
		_DescribeSQLCCLConfig:                 impl.DescribeSQLCCLConfig,
		_ModifySQLCCLConfig:                   impl.ModifySQLCCLConfig,
		_AddSQLCCLRule:                        impl.AddSQLCCLRule,
		_ModifyProxyThrottleRule:              impl.ModifyProxyThrottleRule,
		_DeleteSQLCCLRule:                     impl.DeleteSQLCCLRule,
		_FlushSQLCCLRule:                      impl.FlushSQLCCLRule,
		_ListSQLCCLRules:                      impl.ListSQLCCLRules,
		_DescribeSqlFingerPrintOrKeywords:     impl.DescribeSqlFingerPrintOrKeywords,
		_DescribeSqlType:                      impl.DescribeSqlType,
		_DescribeInstanceAddress:              impl.DescribeInstanceAddress,
		_DescribeInstanceProxyAddress:         impl.DescribeInstanceProxyAddress,
		_DescribeInstanceAddressList:          impl.DescribeInstanceAddressList,
		_ListTables:                           impl.ListTables,
		_ListAllTables:                        impl.ListAllTables,
		_ListSchemaTables:                     impl.ListSchemaTables,
		_ListTablesInfo:                       impl.ListTablesInfo,
		_DescribeTable:                        impl.DescribeTable,
		_DescribeAutoKillSessionConfig:        impl.DescribeAutoKillSessionConfig,
		_ModifyAutoKillSessionConfig:          impl.ModifyAutoKillSessionConfig,
		_DescribeInstanceVersion:              impl.DescribeInstanceVersion,
		_ListErrLogs:                          impl.ListErrLogs,
		_DescribePgTable:                      impl.DescribePgTable,
		_DescribeInstanceReplicaDelay:         impl.DescribeInstanceReplicaDelay,
		_ListViews:                            impl.ListViews,
		_DescribeView:                         impl.DescribeView,
		_DescribeFunction:                     impl.DescribeFunction,
		_DescribeProcedure:                    impl.DescribeProcedure,
		_ListFunctions:                        impl.ListFunctions,
		_ListProcedures:                       impl.ListProcedures,
		_ListTriggers:                         impl.ListTriggers,
		_DescribeTLSConnectionInfo:            impl.DescribeTLSConnectionInfo,
		_ListKeyNumbers:                       impl.ListKeyNumbers,
		_ListKeys:                             impl.ListKeys,
		_GetKey:                               impl.GetKey,
		_ListKeyMembers:                       impl.ListKeyMembers,
		_ListAlterKVsCommands:                 impl.ListAlterKVsCommands,
		_DescribeBigKeys:                      impl.DescribeBigKeys,
		_DescribeHotKeys:                      impl.DescribeHotKeys,
		_OpenTunnel:                           impl.OpenTunnel,
		_DescribeTrigger:                      impl.DescribeTrigger,
		_DescribeEvent:                        impl.DescribeEvent,
		_ListEvents:                           impl.ListEvents,
		_CreateAccount:                        impl.CreateAccount,
		_CheckPrivilege:                       impl.CheckPrivilege,
		_DescribeAccounts:                     impl.DescribeAccounts,
		_DescribeAccounts2:                    impl.DescribeAccounts2,
		_CreateAccountAndGrant:                impl.CreateAccountAndGrant,
		_ModifyAccountPrivilege:               impl.ModifyAccountPrivilege,
		_GrantAccountPrivilege:                impl.GrantAccountPrivilege,
		_DeleteAccount:                        impl.DeleteAccount,
		_ListDatabasesWithAccount:             impl.ListDatabasesWithAccount,
		_GetAdvice:                            impl.GetAdvice,
		_ListCharsets:                         impl.ListCharsets,
		_ListCollations:                       impl.ListCollations,
		_ListSchema:                           impl.ListSchema,
		_ListSequence:                         impl.ListSequence,
		_ListPgCollations:                     impl.ListPgCollations,
		_ListPgUsers:                          impl.ListPgUsers,
		_ListTableSpaces:                      impl.ListTableSpaces,
		_DescribeDialogDetails:                impl.DescribeDialogDetails,
		_DescribeDialogStatistics:             impl.DescribeDialogStatistics,
		_DescribeEngineStatus:                 impl.DescribeEngineStatus,
		_KillProcess:                          impl.KillProcess,
		_DescribeTrxAndLocks:                  impl.DescribeTrxAndLocks,
		_DescribeLockCurrentWaits:             impl.DescribeLockCurrentWaits,
		_DescribeDeadlock:                     impl.DescribeDeadlock,
		_DescribeDeadlockDetect:               impl.DescribeDeadlockDetect,
		_DescribeDialogInfos:                  impl.DescribeDialogInfos,
		_DescribeCurrentConn:                  impl.DescribeCurrentConn,
		_DescribeTableSpace:                   impl.DescribeTableSpace,
		_DescribeTableSpaceAutoIncr:           impl.DescribeTableSpaceAutoIncr,
		_ConvertTableSpaceToModel:             impl.ConvertTableSpaceToModel,
		_DescribeTableColumn:                  impl.DescribeTableColumn,
		_ConvertTableColumnToModel:            impl.ConvertTableColumnToModel,
		_DescribeTableIndex:                   impl.DescribeTableIndex,
		_ConvertTableIndexToModel:             impl.ConvertTableIndexToModel,
		_FormatDescribeStorageCapacityResp:    impl.FormatDescribeStorageCapacityResp,
		_ExecuteCCL:                           impl.ExecuteCCL,
		_CCLShow:                              impl.CCLShow,
		_GetAvgSlowQueries:                    impl.GetAvgSlowQueries,
		_GetDataPointCountSlowQueries:         impl.GetDataPointCountSlowQueries,
		_GetCpuMetricDetail:                   impl.GetCpuMetricDetail,
		_GetAvgCpuUsage:                       impl.GetAvgCpuUsage,
		_GetMinCpuUsage:                       impl.GetMinCpuUsage,
		_GetMaxCpuUsage:                       impl.GetMaxCpuUsage,
		_GetCpuUsageMetricDetail:              impl.GetCpuUsageMetricDetail,
		_DiagRootCauseALL:                     impl.DiagRootCauseALL,
		_DiagRootCauseYoYQoQ:                  impl.DiagRootCauseYoYQoQ,
		_DiagRootCauseDiskMetrics:             impl.DiagRootCauseDiskMetrics,
		_GetMemMetricDetail:                   impl.GetMemMetricDetail,
		_GetAvgMemUsage:                       impl.GetAvgMemUsage,
		_GetMinMemUsage:                       impl.GetMinMemUsage,
		_GetMaxMemUsage:                       impl.GetMaxMemUsage,
		_GetMemUsageMetricDetail:              impl.GetMemUsageMetricDetail,
		_GetAvgDiskUsage:                      impl.GetAvgDiskUsage,
		_GetMaxDiskUsage:                      impl.GetMaxDiskUsage,
		_GetMinDiskUsage:                      impl.GetMinDiskUsage,
		_GetDiskAvailableDays:                 impl.GetDiskAvailableDays,
		_GetDiskFutureSize:                    impl.GetDiskFutureSize,
		_GetDiskMetricDetail:                  impl.GetDiskMetricDetail,
		_GetDiskUsageMetricDetail:             impl.GetDiskUsageMetricDetail,
		_GetAvgQpsUsage:                       impl.GetAvgQpsUsage,
		_GetMaxQpsUsage:                       impl.GetMaxQpsUsage,
		_GetMinQpsUsage:                       impl.GetMinQpsUsage,
		_GetQpsUsage:                          impl.GetQpsUsage,
		_GetAvgTpsUsage:                       impl.GetAvgTpsUsage,
		_GetMaxTpsUsage:                       impl.GetMaxTpsUsage,
		_GetMinTpsUsage:                       impl.GetMinTpsUsage,
		_GetTpsUsage:                          impl.GetTpsUsage,
		_GetAvgConnectionUsage:                impl.GetAvgConnectionUsage,
		_GetMaxConnectionUsage:                impl.GetMaxConnectionUsage,
		_GetMinConnectionUsage:                impl.GetMinConnectionUsage,
		_GetConnectedRatioMetricDetail:        impl.GetConnectedRatioMetricDetail,
		_GetSessionMetricDetail:               impl.GetSessionMetricDetail,
		_GetAvgSessionUsage:                   impl.GetAvgSessionUsage,
		_GetMaxSessionUsage:                   impl.GetMaxSessionUsage,
		_GetMinSessionUsage:                   impl.GetMinSessionUsage,
		_GetLatestDiskUsage:                   impl.GetLatestDiskUsage,
		_GetInspectionCpuMetric:               impl.GetInspectionCpuMetric,
		_GetInspectionMemMetric:               impl.GetInspectionMemMetric,
		_GetInspectionDiskMetric:              impl.GetInspectionDiskMetric,
		_GetInspectionQpsMetric:               impl.GetInspectionQpsMetric,
		_GetInspectionTpsMetric:               impl.GetInspectionTpsMetric,
		_GetInspectionConnectedMetric:         impl.GetInspectionConnectedMetric,
		_GetInspectionConnRatioMetric:         impl.GetInspectionConnRatioMetric,
		_GetInspectionBpHitMetric:             impl.GetInspectionBpHitMetric,
		_GetInspectionOutputMetric:            impl.GetInspectionOutputMetric,
		_GetInspectionInputMetric:             impl.GetInspectionInputMetric,
		_HealthSummary:                        impl.HealthSummary,
		_GetMonitorByMetric:                   impl.GetMonitorByMetric,
		_ListCollections:                      impl.ListCollections,
		_ListIndexs:                           impl.ListIndexs,
		_ListMongoDBs:                         impl.ListMongoDBs,
		_CreateFreeLockCorrectOrder:           impl.CreateFreeLockCorrectOrder,
		_CreateFreeLockCorrectOrderDryRun:     impl.CreateFreeLockCorrectOrderDryRun,
		_DescribeFreeLockCorrectOrders:        impl.DescribeFreeLockCorrectOrders,
		_StopFreeLockCorrectOrders:            impl.StopFreeLockCorrectOrders,
		_PreCheckFreeLockCorrectOrders:        impl.PreCheckFreeLockCorrectOrders,
		_GetDBInnerAddress:                    impl.GetDBInnerAddress,
		_ExecuteDQL:                           impl.ExecuteDQL,
		_ExecuteDMLAndGetAffectedRows:         impl.ExecuteDMLAndGetAffectedRows,
		_GetPartitionInfos:                    impl.GetPartitionInfos,
		_GetShardingDbType:                    impl.GetShardingDbType,
		_ExplainCommand:                       impl.ExplainCommand,
		_IsMyOwnInstance:                      impl.IsMyOwnInstance,
		_CheckInstanceState:                   impl.CheckInstanceState,
		_GetTableIndexInfo:                    impl.GetTableIndexInfo,
		_GetTableIndexValue:                   impl.GetTableIndexValue,
		_GetMaxConnections:                    impl.GetMaxConnections,
		_GetCurrentBandwidth:                  impl.GetCurrentBandwidth,
		_BandwidthScale:                       impl.BandwidthScale,
		_GetMinBandwidth:                      impl.GetMinBandwidth,
		_GetMaxBandwidth:                      impl.GetMaxBandwidth,
		_GetDiskSize:                          impl.GetDiskSize,
		_GetUsedSize:                          impl.GetUsedSize,
		_GetCurrentMetricData:                 impl.GetCurrentMetricData,
		_GetPreSecondMetricData:               impl.GetPreSecondMetricData,
		_GetPreSecondMetricDataByInstance:     impl.GetPreSecondMetricDataByInstance,
		_DescribeFullSQLLogConfig:             impl.DescribeFullSQLLogConfig,
		_ModifyFullSQLLogConfig:               impl.ModifyFullSQLLogConfig,
		_DescribeInstanceVariables:            impl.DescribeInstanceVariables,
		_DescribePrimaryKeyRange:              impl.DescribePrimaryKeyRange,
		_DescribeSQLAdvisorTableMeta:          impl.DescribeSQLAdvisorTableMeta,
		_DescribeSampleData:                   impl.DescribeSampleData,
		_EnsureAccount:                        impl.EnsureAccount,
		_GetDatasourceAddress:                 impl.GetDatasourceAddress,
		_GetDBServiceTreeMountInfo:            impl.GetDBServiceTreeMountInfo,
		_GetDBInstanceInfo:                    impl.GetDBInstanceInfo,
		_InstanceIsExist:                      impl.InstanceIsExist,
		_GetInstanceTopo:                      impl.GetInstanceTopo,
		_GetInstanceProxyTopo:                 impl.GetInstanceProxyTopo,
		_CreateLogDownloadTask:                impl.CreateLogDownloadTask,
		_GetLogDownloadList:                   impl.GetLogDownloadList,
		_GetInstancePrimaryNodeId:             impl.GetInstancePrimaryNodeId,
		_ListSQLKillRules:                     impl.ListSQLKillRules,
		_ModifySQLKillRule:                    impl.ModifySQLKillRule,
		_GetManagedAccountAndPwd:              impl.GetManagedAccountAndPwd,
		_CalculateSpecAfterScale:              impl.CalculateSpecAfterScale,
		_ModifyDBInstanceSpec:                 impl.ModifyDBInstanceSpec,
		_DescribeDBAutoScalingConfig:          impl.DescribeDBAutoScalingConfig,
		_ModifyDBAutoScalingConfig:            impl.ModifyDBAutoScalingConfig,
		_DescribeDBAutoScaleEvents:            impl.DescribeDBAutoScaleEvents,
		_ModifyDBLocalSpecManually:            impl.ModifyDBLocalSpecManually,
		_ValidateDryRun:                       impl.ValidateDryRun,
		_ValidateOriginalTable:                impl.ValidateOriginalTable,
		_ValidateUniqIndex:                    impl.ValidateUniqIndex,
		_ExecuteSql:                           impl.ExecuteSql,
		_GetCurrentMaxConnections:             impl.GetCurrentMaxConnections,
		_GetInstanceSlaveAddress:              impl.GetInstanceSlaveAddress,
		_DescribeDBInstanceDetailForPilot:     impl.DescribeDBInstanceDetailForPilot,
		_DescribeDBInstanceParametersForPilot: impl.DescribeDBInstanceParametersForPilot,
		_GetCreateTableInfo:                   impl.GetCreateTableInfo,
		_CheckAccountPrivilege:                impl.CheckAccountPrivilege,
		_ResetAccount:                         impl.ResetAccount,
		_GrantReplicationPrivilege:            impl.GrantReplicationPrivilege,
		_DescribeInstancePodAddress:           impl.DescribeInstancePodAddress,
	}
}

// TypeFunc alias for method Type
type TypeFunc func() (ret0 shared.DataSourceType)

// DecorateType decorate method Type
func (b *_DataSourceServiceDecorator) DecorateType(mw func(TypeFunc) TypeFunc) DataSourceServiceDecorator {
	b._Type = mw(b._Type)
	return b
}

// Type implement DataSourceService's method Type
func (b *_DataSourceServiceDecorator) Type() (ret0 shared.DataSourceType) {
	return b._Type()
}

// AddWhiteListFunc alias for method AddWhiteList
type AddWhiteListFunc func(arg0 context.Context, arg1 string, arg2 *shared.DataSource) (ret0 string, ret1 error)

// DecorateAddWhiteList decorate method AddWhiteList
func (b *_DataSourceServiceDecorator) DecorateAddWhiteList(mw func(AddWhiteListFunc) AddWhiteListFunc) DataSourceServiceDecorator {
	b._AddWhiteList = mw(b._AddWhiteList)
	return b
}

// AddWhiteList implement DataSourceService's method AddWhiteList
func (b *_DataSourceServiceDecorator) AddWhiteList(arg0 context.Context, arg1 string, arg2 *shared.DataSource) (ret0 string, ret1 error) {
	return b._AddWhiteList(arg0, arg1, arg2)
}

// UpdateWhiteListFunc alias for method UpdateWhiteList
type UpdateWhiteListFunc func(arg0 context.Context, arg1 string, arg2 *shared.DataSource, arg3 []string) (ret0 error)

// DecorateUpdateWhiteList decorate method UpdateWhiteList
func (b *_DataSourceServiceDecorator) DecorateUpdateWhiteList(mw func(UpdateWhiteListFunc) UpdateWhiteListFunc) DataSourceServiceDecorator {
	b._UpdateWhiteList = mw(b._UpdateWhiteList)
	return b
}

// UpdateWhiteList implement DataSourceService's method UpdateWhiteList
func (b *_DataSourceServiceDecorator) UpdateWhiteList(arg0 context.Context, arg1 string, arg2 *shared.DataSource, arg3 []string) (ret0 error) {
	return b._UpdateWhiteList(arg0, arg1, arg2, arg3)
}

// RemoveWhiteListFunc alias for method RemoveWhiteList
type RemoveWhiteListFunc func(arg0 context.Context, arg1 string, arg2 *shared.DataSource, arg3 string) (ret0 error)

// DecorateRemoveWhiteList decorate method RemoveWhiteList
func (b *_DataSourceServiceDecorator) DecorateRemoveWhiteList(mw func(RemoveWhiteListFunc) RemoveWhiteListFunc) DataSourceServiceDecorator {
	b._RemoveWhiteList = mw(b._RemoveWhiteList)
	return b
}

// RemoveWhiteList implement DataSourceService's method RemoveWhiteList
func (b *_DataSourceServiceDecorator) RemoveWhiteList(arg0 context.Context, arg1 string, arg2 *shared.DataSource, arg3 string) (ret0 error) {
	return b._RemoveWhiteList(arg0, arg1, arg2, arg3)
}

// FillDataSourceFunc alias for method FillDataSource
type FillDataSourceFunc func(arg0 context.Context, arg1 *shared.DataSource) (ret0 error)

// DecorateFillDataSource decorate method FillDataSource
func (b *_DataSourceServiceDecorator) DecorateFillDataSource(mw func(FillDataSourceFunc) FillDataSourceFunc) DataSourceServiceDecorator {
	b._FillDataSource = mw(b._FillDataSource)
	return b
}

// FillDataSource implement DataSourceService's method FillDataSource
func (b *_DataSourceServiceDecorator) FillDataSource(arg0 context.Context, arg1 *shared.DataSource) (ret0 error) {
	return b._FillDataSource(arg0, arg1)
}

// FillInnerDataSourceFunc alias for method FillInnerDataSource
type FillInnerDataSourceFunc func(arg0 context.Context, arg1 *shared.DataSource) (ret0 error)

// DecorateFillInnerDataSource decorate method FillInnerDataSource
func (b *_DataSourceServiceDecorator) DecorateFillInnerDataSource(mw func(FillInnerDataSourceFunc) FillInnerDataSourceFunc) DataSourceServiceDecorator {
	b._FillInnerDataSource = mw(b._FillInnerDataSource)
	return b
}

// FillInnerDataSource implement DataSourceService's method FillInnerDataSource
func (b *_DataSourceServiceDecorator) FillInnerDataSource(arg0 context.Context, arg1 *shared.DataSource) (ret0 error) {
	return b._FillInnerDataSource(arg0, arg1)
}

// CheckConnFunc alias for method CheckConn
type CheckConnFunc func(arg0 context.Context, arg1 *shared.DataSource) (ret0 error)

// DecorateCheckConn decorate method CheckConn
func (b *_DataSourceServiceDecorator) DecorateCheckConn(mw func(CheckConnFunc) CheckConnFunc) DataSourceServiceDecorator {
	b._CheckConn = mw(b._CheckConn)
	return b
}

// CheckConn implement DataSourceService's method CheckConn
func (b *_DataSourceServiceDecorator) CheckConn(arg0 context.Context, arg1 *shared.DataSource) (ret0 error) {
	return b._CheckConn(arg0, arg1)
}

// CheckDataSourceFunc alias for method CheckDataSource
type CheckDataSourceFunc func(arg0 context.Context, arg1 *shared.DataSource) (ret0 error)

// DecorateCheckDataSource decorate method CheckDataSource
func (b *_DataSourceServiceDecorator) DecorateCheckDataSource(mw func(CheckDataSourceFunc) CheckDataSourceFunc) DataSourceServiceDecorator {
	b._CheckDataSource = mw(b._CheckDataSource)
	return b
}

// CheckDataSource implement DataSourceService's method CheckDataSource
func (b *_DataSourceServiceDecorator) CheckDataSource(arg0 context.Context, arg1 *shared.DataSource) (ret0 error) {
	return b._CheckDataSource(arg0, arg1)
}

// KillQueryFunc alias for method KillQuery
type KillQueryFunc func(arg0 context.Context, arg1 *shared.DataSource, arg2 *shared.ConnectionInfo) (ret0 error)

// DecorateKillQuery decorate method KillQuery
func (b *_DataSourceServiceDecorator) DecorateKillQuery(mw func(KillQueryFunc) KillQueryFunc) DataSourceServiceDecorator {
	b._KillQuery = mw(b._KillQuery)
	return b
}

// KillQuery implement DataSourceService's method KillQuery
func (b *_DataSourceServiceDecorator) KillQuery(arg0 context.Context, arg1 *shared.DataSource, arg2 *shared.ConnectionInfo) (ret0 error) {
	return b._KillQuery(arg0, arg1, arg2)
}

// ListInstanceFunc alias for method ListInstance
type ListInstanceFunc func(arg0 context.Context, arg1 *ListInstanceReq) (ret0 *ListInstanceResp, ret1 error)

// DecorateListInstance decorate method ListInstance
func (b *_DataSourceServiceDecorator) DecorateListInstance(mw func(ListInstanceFunc) ListInstanceFunc) DataSourceServiceDecorator {
	b._ListInstance = mw(b._ListInstance)
	return b
}

// ListInstance implement DataSourceService's method ListInstance
func (b *_DataSourceServiceDecorator) ListInstance(arg0 context.Context, arg1 *ListInstanceReq) (ret0 *ListInstanceResp, ret1 error) {
	return b._ListInstance(arg0, arg1)
}

// ListInstanceLightWeightFunc alias for method ListInstanceLightWeight
type ListInstanceLightWeightFunc func(arg0 context.Context, arg1 *ListInstanceReq) (ret0 *ListInstanceResp, ret1 error)

// DecorateListInstanceLightWeight decorate method ListInstanceLightWeight
func (b *_DataSourceServiceDecorator) DecorateListInstanceLightWeight(mw func(ListInstanceLightWeightFunc) ListInstanceLightWeightFunc) DataSourceServiceDecorator {
	b._ListInstanceLightWeight = mw(b._ListInstanceLightWeight)
	return b
}

// ListInstanceLightWeight implement DataSourceService's method ListInstanceLightWeight
func (b *_DataSourceServiceDecorator) ListInstanceLightWeight(arg0 context.Context, arg1 *ListInstanceReq) (ret0 *ListInstanceResp, ret1 error) {
	return b._ListInstanceLightWeight(arg0, arg1)
}

// ListDatabasesFunc alias for method ListDatabases
type ListDatabasesFunc func(arg0 context.Context, arg1 *ListDatabasesReq) (ret0 *ListDatabasesResp, ret1 error)

// DecorateListDatabases decorate method ListDatabases
func (b *_DataSourceServiceDecorator) DecorateListDatabases(mw func(ListDatabasesFunc) ListDatabasesFunc) DataSourceServiceDecorator {
	b._ListDatabases = mw(b._ListDatabases)
	return b
}

// ListDatabases implement DataSourceService's method ListDatabases
func (b *_DataSourceServiceDecorator) ListDatabases(arg0 context.Context, arg1 *ListDatabasesReq) (ret0 *ListDatabasesResp, ret1 error) {
	return b._ListDatabases(arg0, arg1)
}

// ListInstanceNodesFunc alias for method ListInstanceNodes
type ListInstanceNodesFunc func(arg0 context.Context, arg1 *ListInstanceNodesReq) (ret0 *ListInstanceNodesResp, ret1 error)

// DecorateListInstanceNodes decorate method ListInstanceNodes
func (b *_DataSourceServiceDecorator) DecorateListInstanceNodes(mw func(ListInstanceNodesFunc) ListInstanceNodesFunc) DataSourceServiceDecorator {
	b._ListInstanceNodes = mw(b._ListInstanceNodes)
	return b
}

// ListInstanceNodes implement DataSourceService's method ListInstanceNodes
func (b *_DataSourceServiceDecorator) ListInstanceNodes(arg0 context.Context, arg1 *ListInstanceNodesReq) (ret0 *ListInstanceNodesResp, ret1 error) {
	return b._ListInstanceNodes(arg0, arg1)
}

// ListInstanceNodesOriFunc alias for method ListInstanceNodesOri
type ListInstanceNodesOriFunc func(arg0 context.Context, arg1 *ListInstanceNodesReq) (ret0 *ListInstanceNodesOriResp, ret1 error)

// DecorateListInstanceNodesOri decorate method ListInstanceNodesOri
func (b *_DataSourceServiceDecorator) DecorateListInstanceNodesOri(mw func(ListInstanceNodesOriFunc) ListInstanceNodesOriFunc) DataSourceServiceDecorator {
	b._ListInstanceNodesOri = mw(b._ListInstanceNodesOri)
	return b
}

// ListInstanceNodesOri implement DataSourceService's method ListInstanceNodesOri
func (b *_DataSourceServiceDecorator) ListInstanceNodesOri(arg0 context.Context, arg1 *ListInstanceNodesReq) (ret0 *ListInstanceNodesOriResp, ret1 error) {
	return b._ListInstanceNodesOri(arg0, arg1)
}

// ListInstancePodsFunc alias for method ListInstancePods
type ListInstancePodsFunc func(arg0 context.Context, arg1 *ListInstancePodsReq) (ret0 *ListInstancePodsResp, ret1 error)

// DecorateListInstancePods decorate method ListInstancePods
func (b *_DataSourceServiceDecorator) DecorateListInstancePods(mw func(ListInstancePodsFunc) ListInstancePodsFunc) DataSourceServiceDecorator {
	b._ListInstancePods = mw(b._ListInstancePods)
	return b
}

// ListInstancePods implement DataSourceService's method ListInstancePods
func (b *_DataSourceServiceDecorator) ListInstancePods(arg0 context.Context, arg1 *ListInstancePodsReq) (ret0 *ListInstancePodsResp, ret1 error) {
	return b._ListInstancePods(arg0, arg1)
}

// DescribeDBInstanceDetailFunc alias for method DescribeDBInstanceDetail
type DescribeDBInstanceDetailFunc func(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 *DescribeDBInstanceDetailResp, ret1 error)

// DecorateDescribeDBInstanceDetail decorate method DescribeDBInstanceDetail
func (b *_DataSourceServiceDecorator) DecorateDescribeDBInstanceDetail(mw func(DescribeDBInstanceDetailFunc) DescribeDBInstanceDetailFunc) DataSourceServiceDecorator {
	b._DescribeDBInstanceDetail = mw(b._DescribeDBInstanceDetail)
	return b
}

// DescribeDBInstanceDetail implement DataSourceService's method DescribeDBInstanceDetail
func (b *_DataSourceServiceDecorator) DescribeDBInstanceDetail(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 *DescribeDBInstanceDetailResp, ret1 error) {
	return b._DescribeDBInstanceDetail(arg0, arg1)
}

// DescribeDBInstanceSpecFunc alias for method DescribeDBInstanceSpec
type DescribeDBInstanceSpecFunc func(arg0 context.Context, arg1 *DescribeDBInstanceSpecReq) (ret0 *DescribeDBInstanceSpecResp, ret1 error)

// DecorateDescribeDBInstanceSpec decorate method DescribeDBInstanceSpec
func (b *_DataSourceServiceDecorator) DecorateDescribeDBInstanceSpec(mw func(DescribeDBInstanceSpecFunc) DescribeDBInstanceSpecFunc) DataSourceServiceDecorator {
	b._DescribeDBInstanceSpec = mw(b._DescribeDBInstanceSpec)
	return b
}

// DescribeDBInstanceSpec implement DataSourceService's method DescribeDBInstanceSpec
func (b *_DataSourceServiceDecorator) DescribeDBInstanceSpec(arg0 context.Context, arg1 *DescribeDBInstanceSpecReq) (ret0 *DescribeDBInstanceSpecResp, ret1 error) {
	return b._DescribeDBInstanceSpec(arg0, arg1)
}

// DescribeDBInstanceEndpointsFunc alias for method DescribeDBInstanceEndpoints
type DescribeDBInstanceEndpointsFunc func(arg0 context.Context, arg1 *DescribeDBInstanceEndpointsReq) (ret0 *DescribeDBInstanceEndpointsResp, ret1 error)

// DecorateDescribeDBInstanceEndpoints decorate method DescribeDBInstanceEndpoints
func (b *_DataSourceServiceDecorator) DecorateDescribeDBInstanceEndpoints(mw func(DescribeDBInstanceEndpointsFunc) DescribeDBInstanceEndpointsFunc) DataSourceServiceDecorator {
	b._DescribeDBInstanceEndpoints = mw(b._DescribeDBInstanceEndpoints)
	return b
}

// DescribeDBInstanceEndpoints implement DataSourceService's method DescribeDBInstanceEndpoints
func (b *_DataSourceServiceDecorator) DescribeDBInstanceEndpoints(arg0 context.Context, arg1 *DescribeDBInstanceEndpointsReq) (ret0 *DescribeDBInstanceEndpointsResp, ret1 error) {
	return b._DescribeDBInstanceEndpoints(arg0, arg1)
}

// DescribeDBInstanceShardInfosFunc alias for method DescribeDBInstanceShardInfos
type DescribeDBInstanceShardInfosFunc func(arg0 context.Context, arg1 *DescribeDBInstanceShardInfosReq) (ret0 *DescribeDBInstanceShardInfosResp, ret1 error)

// DecorateDescribeDBInstanceShardInfos decorate method DescribeDBInstanceShardInfos
func (b *_DataSourceServiceDecorator) DecorateDescribeDBInstanceShardInfos(mw func(DescribeDBInstanceShardInfosFunc) DescribeDBInstanceShardInfosFunc) DataSourceServiceDecorator {
	b._DescribeDBInstanceShardInfos = mw(b._DescribeDBInstanceShardInfos)
	return b
}

// DescribeDBInstanceShardInfos implement DataSourceService's method DescribeDBInstanceShardInfos
func (b *_DataSourceServiceDecorator) DescribeDBInstanceShardInfos(arg0 context.Context, arg1 *DescribeDBInstanceShardInfosReq) (ret0 *DescribeDBInstanceShardInfosResp, ret1 error) {
	return b._DescribeDBInstanceShardInfos(arg0, arg1)
}

// DescribeDBInstanceClusterFunc alias for method DescribeDBInstanceCluster
type DescribeDBInstanceClusterFunc func(arg0 context.Context, arg1 *DescribeDBInstanceClusterReq) (ret0 *DescribeDBInstanceClusterResp, ret1 error)

// DecorateDescribeDBInstanceCluster decorate method DescribeDBInstanceCluster
func (b *_DataSourceServiceDecorator) DecorateDescribeDBInstanceCluster(mw func(DescribeDBInstanceClusterFunc) DescribeDBInstanceClusterFunc) DataSourceServiceDecorator {
	b._DescribeDBInstanceCluster = mw(b._DescribeDBInstanceCluster)
	return b
}

// DescribeDBInstanceCluster implement DataSourceService's method DescribeDBInstanceCluster
func (b *_DataSourceServiceDecorator) DescribeDBInstanceCluster(arg0 context.Context, arg1 *DescribeDBInstanceClusterReq) (ret0 *DescribeDBInstanceClusterResp, ret1 error) {
	return b._DescribeDBInstanceCluster(arg0, arg1)
}

// DescribeDBInstanceAuditCollectedPodFunc alias for method DescribeDBInstanceAuditCollectedPod
type DescribeDBInstanceAuditCollectedPodFunc func(arg0 context.Context, arg1 *DescribeDBInstanceAuditCollectedPodReq) (ret0 *DescribeDBInstanceAuditCollectedPodResp, ret1 error)

// DecorateDescribeDBInstanceAuditCollectedPod decorate method DescribeDBInstanceAuditCollectedPod
func (b *_DataSourceServiceDecorator) DecorateDescribeDBInstanceAuditCollectedPod(mw func(DescribeDBInstanceAuditCollectedPodFunc) DescribeDBInstanceAuditCollectedPodFunc) DataSourceServiceDecorator {
	b._DescribeDBInstanceAuditCollectedPod = mw(b._DescribeDBInstanceAuditCollectedPod)
	return b
}

// DescribeDBInstanceAuditCollectedPod implement DataSourceService's method DescribeDBInstanceAuditCollectedPod
func (b *_DataSourceServiceDecorator) DescribeDBInstanceAuditCollectedPod(arg0 context.Context, arg1 *DescribeDBInstanceAuditCollectedPodReq) (ret0 *DescribeDBInstanceAuditCollectedPodResp, ret1 error) {
	return b._DescribeDBInstanceAuditCollectedPod(arg0, arg1)
}

// OpenDBInstanceAuditLogFunc alias for method OpenDBInstanceAuditLog
type OpenDBInstanceAuditLogFunc func(arg0 context.Context, arg1 *OpenDBInstanceAuditLogReq) (ret0 *OpenDBInstanceAuditLogResp, ret1 error)

// DecorateOpenDBInstanceAuditLog decorate method OpenDBInstanceAuditLog
func (b *_DataSourceServiceDecorator) DecorateOpenDBInstanceAuditLog(mw func(OpenDBInstanceAuditLogFunc) OpenDBInstanceAuditLogFunc) DataSourceServiceDecorator {
	b._OpenDBInstanceAuditLog = mw(b._OpenDBInstanceAuditLog)
	return b
}

// OpenDBInstanceAuditLog implement DataSourceService's method OpenDBInstanceAuditLog
func (b *_DataSourceServiceDecorator) OpenDBInstanceAuditLog(arg0 context.Context, arg1 *OpenDBInstanceAuditLogReq) (ret0 *OpenDBInstanceAuditLogResp, ret1 error) {
	return b._OpenDBInstanceAuditLog(arg0, arg1)
}

// CloseDBInstanceAuditLogFunc alias for method CloseDBInstanceAuditLog
type CloseDBInstanceAuditLogFunc func(arg0 context.Context, arg1 *CloseDBInstanceAuditLogReq) (ret0 *CloseDBInstanceAuditLogResp, ret1 error)

// DecorateCloseDBInstanceAuditLog decorate method CloseDBInstanceAuditLog
func (b *_DataSourceServiceDecorator) DecorateCloseDBInstanceAuditLog(mw func(CloseDBInstanceAuditLogFunc) CloseDBInstanceAuditLogFunc) DataSourceServiceDecorator {
	b._CloseDBInstanceAuditLog = mw(b._CloseDBInstanceAuditLog)
	return b
}

// CloseDBInstanceAuditLog implement DataSourceService's method CloseDBInstanceAuditLog
func (b *_DataSourceServiceDecorator) CloseDBInstanceAuditLog(arg0 context.Context, arg1 *CloseDBInstanceAuditLogReq) (ret0 *CloseDBInstanceAuditLogResp, ret1 error) {
	return b._CloseDBInstanceAuditLog(arg0, arg1)
}

// CheckDBInstanceAuditLogStatusFunc alias for method CheckDBInstanceAuditLogStatus
type CheckDBInstanceAuditLogStatusFunc func(arg0 context.Context, arg1 *CheckDBInstanceAuditLogStatusReq) (ret0 *CheckDBInstanceAuditLogStatusResp, ret1 error)

// DecorateCheckDBInstanceAuditLogStatus decorate method CheckDBInstanceAuditLogStatus
func (b *_DataSourceServiceDecorator) DecorateCheckDBInstanceAuditLogStatus(mw func(CheckDBInstanceAuditLogStatusFunc) CheckDBInstanceAuditLogStatusFunc) DataSourceServiceDecorator {
	b._CheckDBInstanceAuditLogStatus = mw(b._CheckDBInstanceAuditLogStatus)
	return b
}

// CheckDBInstanceAuditLogStatus implement DataSourceService's method CheckDBInstanceAuditLogStatus
func (b *_DataSourceServiceDecorator) CheckDBInstanceAuditLogStatus(arg0 context.Context, arg1 *CheckDBInstanceAuditLogStatusReq) (ret0 *CheckDBInstanceAuditLogStatusResp, ret1 error) {
	return b._CheckDBInstanceAuditLogStatus(arg0, arg1)
}

// DescribeDBProxyConfigFunc alias for method DescribeDBProxyConfig
type DescribeDBProxyConfigFunc func(arg0 context.Context, arg1 *DescribeDBProxyConfigReq) (ret0 *DescribeDBProxyConfigResp, ret1 error)

// DecorateDescribeDBProxyConfig decorate method DescribeDBProxyConfig
func (b *_DataSourceServiceDecorator) DecorateDescribeDBProxyConfig(mw func(DescribeDBProxyConfigFunc) DescribeDBProxyConfigFunc) DataSourceServiceDecorator {
	b._DescribeDBProxyConfig = mw(b._DescribeDBProxyConfig)
	return b
}

// DescribeDBProxyConfig implement DataSourceService's method DescribeDBProxyConfig
func (b *_DataSourceServiceDecorator) DescribeDBProxyConfig(arg0 context.Context, arg1 *DescribeDBProxyConfigReq) (ret0 *DescribeDBProxyConfigResp, ret1 error) {
	return b._DescribeDBProxyConfig(arg0, arg1)
}

// DescribeInstanceFeaturesFunc alias for method DescribeInstanceFeatures
type DescribeInstanceFeaturesFunc func(arg0 context.Context, arg1 *DescribeInstanceFeaturesReq) (ret0 *DescribeInstanceFeaturesResp, ret1 error)

// DecorateDescribeInstanceFeatures decorate method DescribeInstanceFeatures
func (b *_DataSourceServiceDecorator) DecorateDescribeInstanceFeatures(mw func(DescribeInstanceFeaturesFunc) DescribeInstanceFeaturesFunc) DataSourceServiceDecorator {
	b._DescribeInstanceFeatures = mw(b._DescribeInstanceFeatures)
	return b
}

// DescribeInstanceFeatures implement DataSourceService's method DescribeInstanceFeatures
func (b *_DataSourceServiceDecorator) DescribeInstanceFeatures(arg0 context.Context, arg1 *DescribeInstanceFeaturesReq) (ret0 *DescribeInstanceFeaturesResp, ret1 error) {
	return b._DescribeInstanceFeatures(arg0, arg1)
}

// DescribeDBInstanceSSLFunc alias for method DescribeDBInstanceSSL
type DescribeDBInstanceSSLFunc func(arg0 context.Context, arg1 *DescribeDBInstanceSSLReq) (ret0 *DescribeDBInstanceSSLResp, ret1 error)

// DecorateDescribeDBInstanceSSL decorate method DescribeDBInstanceSSL
func (b *_DataSourceServiceDecorator) DecorateDescribeDBInstanceSSL(mw func(DescribeDBInstanceSSLFunc) DescribeDBInstanceSSLFunc) DataSourceServiceDecorator {
	b._DescribeDBInstanceSSL = mw(b._DescribeDBInstanceSSL)
	return b
}

// DescribeDBInstanceSSL implement DataSourceService's method DescribeDBInstanceSSL
func (b *_DataSourceServiceDecorator) DescribeDBInstanceSSL(arg0 context.Context, arg1 *DescribeDBInstanceSSLReq) (ret0 *DescribeDBInstanceSSLResp, ret1 error) {
	return b._DescribeDBInstanceSSL(arg0, arg1)
}

// DescribeSQLCCLConfigFunc alias for method DescribeSQLCCLConfig
type DescribeSQLCCLConfigFunc func(arg0 context.Context, arg1 *DescribeSQLCCLConfigReq) (ret0 *DescribeSQLCCLConfigResp, ret1 error)

// DecorateDescribeSQLCCLConfig decorate method DescribeSQLCCLConfig
func (b *_DataSourceServiceDecorator) DecorateDescribeSQLCCLConfig(mw func(DescribeSQLCCLConfigFunc) DescribeSQLCCLConfigFunc) DataSourceServiceDecorator {
	b._DescribeSQLCCLConfig = mw(b._DescribeSQLCCLConfig)
	return b
}

// DescribeSQLCCLConfig implement DataSourceService's method DescribeSQLCCLConfig
func (b *_DataSourceServiceDecorator) DescribeSQLCCLConfig(arg0 context.Context, arg1 *DescribeSQLCCLConfigReq) (ret0 *DescribeSQLCCLConfigResp, ret1 error) {
	return b._DescribeSQLCCLConfig(arg0, arg1)
}

// ModifySQLCCLConfigFunc alias for method ModifySQLCCLConfig
type ModifySQLCCLConfigFunc func(arg0 context.Context, arg1 *ModifySQLCCLConfigReq) (ret0 *ModifySQLCCLConfigResp, ret1 error)

// DecorateModifySQLCCLConfig decorate method ModifySQLCCLConfig
func (b *_DataSourceServiceDecorator) DecorateModifySQLCCLConfig(mw func(ModifySQLCCLConfigFunc) ModifySQLCCLConfigFunc) DataSourceServiceDecorator {
	b._ModifySQLCCLConfig = mw(b._ModifySQLCCLConfig)
	return b
}

// ModifySQLCCLConfig implement DataSourceService's method ModifySQLCCLConfig
func (b *_DataSourceServiceDecorator) ModifySQLCCLConfig(arg0 context.Context, arg1 *ModifySQLCCLConfigReq) (ret0 *ModifySQLCCLConfigResp, ret1 error) {
	return b._ModifySQLCCLConfig(arg0, arg1)
}

// AddSQLCCLRuleFunc alias for method AddSQLCCLRule
type AddSQLCCLRuleFunc func(arg0 context.Context, arg1 *AddSQLCCLRuleReq) (ret0 *AddSQLCCLRuleResp, ret1 error)

// DecorateAddSQLCCLRule decorate method AddSQLCCLRule
func (b *_DataSourceServiceDecorator) DecorateAddSQLCCLRule(mw func(AddSQLCCLRuleFunc) AddSQLCCLRuleFunc) DataSourceServiceDecorator {
	b._AddSQLCCLRule = mw(b._AddSQLCCLRule)
	return b
}

// AddSQLCCLRule implement DataSourceService's method AddSQLCCLRule
func (b *_DataSourceServiceDecorator) AddSQLCCLRule(arg0 context.Context, arg1 *AddSQLCCLRuleReq) (ret0 *AddSQLCCLRuleResp, ret1 error) {
	return b._AddSQLCCLRule(arg0, arg1)
}

// ModifyProxyThrottleRuleFunc alias for method ModifyProxyThrottleRule
type ModifyProxyThrottleRuleFunc func(arg0 context.Context, arg1 *ModifyProxyThrottleRuleReq) (ret0 *ModifyProxyThrottleRuleResp, ret1 error)

// DecorateModifyProxyThrottleRule decorate method ModifyProxyThrottleRule
func (b *_DataSourceServiceDecorator) DecorateModifyProxyThrottleRule(mw func(ModifyProxyThrottleRuleFunc) ModifyProxyThrottleRuleFunc) DataSourceServiceDecorator {
	b._ModifyProxyThrottleRule = mw(b._ModifyProxyThrottleRule)
	return b
}

// ModifyProxyThrottleRule implement DataSourceService's method ModifyProxyThrottleRule
func (b *_DataSourceServiceDecorator) ModifyProxyThrottleRule(arg0 context.Context, arg1 *ModifyProxyThrottleRuleReq) (ret0 *ModifyProxyThrottleRuleResp, ret1 error) {
	return b._ModifyProxyThrottleRule(arg0, arg1)
}

// DeleteSQLCCLRuleFunc alias for method DeleteSQLCCLRule
type DeleteSQLCCLRuleFunc func(arg0 context.Context, arg1 *DeleteSQLCCLRuleReq) (ret0 *DeleteSQLCCLRuleResp, ret1 error)

// DecorateDeleteSQLCCLRule decorate method DeleteSQLCCLRule
func (b *_DataSourceServiceDecorator) DecorateDeleteSQLCCLRule(mw func(DeleteSQLCCLRuleFunc) DeleteSQLCCLRuleFunc) DataSourceServiceDecorator {
	b._DeleteSQLCCLRule = mw(b._DeleteSQLCCLRule)
	return b
}

// DeleteSQLCCLRule implement DataSourceService's method DeleteSQLCCLRule
func (b *_DataSourceServiceDecorator) DeleteSQLCCLRule(arg0 context.Context, arg1 *DeleteSQLCCLRuleReq) (ret0 *DeleteSQLCCLRuleResp, ret1 error) {
	return b._DeleteSQLCCLRule(arg0, arg1)
}

// FlushSQLCCLRuleFunc alias for method FlushSQLCCLRule
type FlushSQLCCLRuleFunc func(arg0 context.Context, arg1 *FlushSQLCCLRuleReq) (ret0 *FlushSQLCCLRuleResp, ret1 error)

// DecorateFlushSQLCCLRule decorate method FlushSQLCCLRule
func (b *_DataSourceServiceDecorator) DecorateFlushSQLCCLRule(mw func(FlushSQLCCLRuleFunc) FlushSQLCCLRuleFunc) DataSourceServiceDecorator {
	b._FlushSQLCCLRule = mw(b._FlushSQLCCLRule)
	return b
}

// FlushSQLCCLRule implement DataSourceService's method FlushSQLCCLRule
func (b *_DataSourceServiceDecorator) FlushSQLCCLRule(arg0 context.Context, arg1 *FlushSQLCCLRuleReq) (ret0 *FlushSQLCCLRuleResp, ret1 error) {
	return b._FlushSQLCCLRule(arg0, arg1)
}

// ListSQLCCLRulesFunc alias for method ListSQLCCLRules
type ListSQLCCLRulesFunc func(arg0 context.Context, arg1 *ListSQLCCLRulesReq) (ret0 *ListSQLCCLRulesResp, ret1 error)

// DecorateListSQLCCLRules decorate method ListSQLCCLRules
func (b *_DataSourceServiceDecorator) DecorateListSQLCCLRules(mw func(ListSQLCCLRulesFunc) ListSQLCCLRulesFunc) DataSourceServiceDecorator {
	b._ListSQLCCLRules = mw(b._ListSQLCCLRules)
	return b
}

// ListSQLCCLRules implement DataSourceService's method ListSQLCCLRules
func (b *_DataSourceServiceDecorator) ListSQLCCLRules(arg0 context.Context, arg1 *ListSQLCCLRulesReq) (ret0 *ListSQLCCLRulesResp, ret1 error) {
	return b._ListSQLCCLRules(arg0, arg1)
}

// DescribeSqlFingerPrintOrKeywordsFunc alias for method DescribeSqlFingerPrintOrKeywords
type DescribeSqlFingerPrintOrKeywordsFunc func(arg0 context.Context, arg1 *DescribeSqlFingerPrintOrKeywordsReq) (ret0 *DescribeSqlFingerPrintOrKeywordsResp, ret1 error)

// DecorateDescribeSqlFingerPrintOrKeywords decorate method DescribeSqlFingerPrintOrKeywords
func (b *_DataSourceServiceDecorator) DecorateDescribeSqlFingerPrintOrKeywords(mw func(DescribeSqlFingerPrintOrKeywordsFunc) DescribeSqlFingerPrintOrKeywordsFunc) DataSourceServiceDecorator {
	b._DescribeSqlFingerPrintOrKeywords = mw(b._DescribeSqlFingerPrintOrKeywords)
	return b
}

// DescribeSqlFingerPrintOrKeywords implement DataSourceService's method DescribeSqlFingerPrintOrKeywords
func (b *_DataSourceServiceDecorator) DescribeSqlFingerPrintOrKeywords(arg0 context.Context, arg1 *DescribeSqlFingerPrintOrKeywordsReq) (ret0 *DescribeSqlFingerPrintOrKeywordsResp, ret1 error) {
	return b._DescribeSqlFingerPrintOrKeywords(arg0, arg1)
}

// DescribeSqlTypeFunc alias for method DescribeSqlType
type DescribeSqlTypeFunc func(arg0 context.Context, arg1 *DescribeSqlTypeReq) (ret0 *DescribeSqlTypeResp, ret1 error)

// DecorateDescribeSqlType decorate method DescribeSqlType
func (b *_DataSourceServiceDecorator) DecorateDescribeSqlType(mw func(DescribeSqlTypeFunc) DescribeSqlTypeFunc) DataSourceServiceDecorator {
	b._DescribeSqlType = mw(b._DescribeSqlType)
	return b
}

// DescribeSqlType implement DataSourceService's method DescribeSqlType
func (b *_DataSourceServiceDecorator) DescribeSqlType(arg0 context.Context, arg1 *DescribeSqlTypeReq) (ret0 *DescribeSqlTypeResp, ret1 error) {
	return b._DescribeSqlType(arg0, arg1)
}

// DescribeInstanceAddressFunc alias for method DescribeInstanceAddress
type DescribeInstanceAddressFunc func(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 *DescribeInstanceAddressResp, ret1 error)

// DecorateDescribeInstanceAddress decorate method DescribeInstanceAddress
func (b *_DataSourceServiceDecorator) DecorateDescribeInstanceAddress(mw func(DescribeInstanceAddressFunc) DescribeInstanceAddressFunc) DataSourceServiceDecorator {
	b._DescribeInstanceAddress = mw(b._DescribeInstanceAddress)
	return b
}

// DescribeInstanceAddress implement DataSourceService's method DescribeInstanceAddress
func (b *_DataSourceServiceDecorator) DescribeInstanceAddress(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 *DescribeInstanceAddressResp, ret1 error) {
	return b._DescribeInstanceAddress(arg0, arg1)
}

// DescribeInstanceProxyAddressFunc alias for method DescribeInstanceProxyAddress
type DescribeInstanceProxyAddressFunc func(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 *DescribeInstanceAddressResp, ret1 error)

// DecorateDescribeInstanceProxyAddress decorate method DescribeInstanceProxyAddress
func (b *_DataSourceServiceDecorator) DecorateDescribeInstanceProxyAddress(mw func(DescribeInstanceProxyAddressFunc) DescribeInstanceProxyAddressFunc) DataSourceServiceDecorator {
	b._DescribeInstanceProxyAddress = mw(b._DescribeInstanceProxyAddress)
	return b
}

// DescribeInstanceProxyAddress implement DataSourceService's method DescribeInstanceProxyAddress
func (b *_DataSourceServiceDecorator) DescribeInstanceProxyAddress(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 *DescribeInstanceAddressResp, ret1 error) {
	return b._DescribeInstanceProxyAddress(arg0, arg1)
}

// DescribeInstanceAddressListFunc alias for method DescribeInstanceAddressList
type DescribeInstanceAddressListFunc func(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 []*DescribeInstanceAddressResp, ret1 error)

// DecorateDescribeInstanceAddressList decorate method DescribeInstanceAddressList
func (b *_DataSourceServiceDecorator) DecorateDescribeInstanceAddressList(mw func(DescribeInstanceAddressListFunc) DescribeInstanceAddressListFunc) DataSourceServiceDecorator {
	b._DescribeInstanceAddressList = mw(b._DescribeInstanceAddressList)
	return b
}

// DescribeInstanceAddressList implement DataSourceService's method DescribeInstanceAddressList
func (b *_DataSourceServiceDecorator) DescribeInstanceAddressList(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 []*DescribeInstanceAddressResp, ret1 error) {
	return b._DescribeInstanceAddressList(arg0, arg1)
}

// ListTablesFunc alias for method ListTables
type ListTablesFunc func(arg0 context.Context, arg1 *ListTablesReq) (ret0 *ListTablesResp, ret1 error)

// DecorateListTables decorate method ListTables
func (b *_DataSourceServiceDecorator) DecorateListTables(mw func(ListTablesFunc) ListTablesFunc) DataSourceServiceDecorator {
	b._ListTables = mw(b._ListTables)
	return b
}

// ListTables implement DataSourceService's method ListTables
func (b *_DataSourceServiceDecorator) ListTables(arg0 context.Context, arg1 *ListTablesReq) (ret0 *ListTablesResp, ret1 error) {
	return b._ListTables(arg0, arg1)
}

// ListAllTablesFunc alias for method ListAllTables
type ListAllTablesFunc func(arg0 context.Context, arg1 *ListTablesReq) (ret0 *ListTablesResp, ret1 error)

// DecorateListAllTables decorate method ListAllTables
func (b *_DataSourceServiceDecorator) DecorateListAllTables(mw func(ListAllTablesFunc) ListAllTablesFunc) DataSourceServiceDecorator {
	b._ListAllTables = mw(b._ListAllTables)
	return b
}

// ListAllTables implement DataSourceService's method ListAllTables
func (b *_DataSourceServiceDecorator) ListAllTables(arg0 context.Context, arg1 *ListTablesReq) (ret0 *ListTablesResp, ret1 error) {
	return b._ListAllTables(arg0, arg1)
}

// ListSchemaTablesFunc alias for method ListSchemaTables
type ListSchemaTablesFunc func(arg0 context.Context, arg1 *ListSchemaTablesReq) (ret0 *ListSchemaTablesResp, ret1 error)

// DecorateListSchemaTables decorate method ListSchemaTables
func (b *_DataSourceServiceDecorator) DecorateListSchemaTables(mw func(ListSchemaTablesFunc) ListSchemaTablesFunc) DataSourceServiceDecorator {
	b._ListSchemaTables = mw(b._ListSchemaTables)
	return b
}

// ListSchemaTables implement DataSourceService's method ListSchemaTables
func (b *_DataSourceServiceDecorator) ListSchemaTables(arg0 context.Context, arg1 *ListSchemaTablesReq) (ret0 *ListSchemaTablesResp, ret1 error) {
	return b._ListSchemaTables(arg0, arg1)
}

// ListTablesInfoFunc alias for method ListTablesInfo
type ListTablesInfoFunc func(arg0 context.Context, arg1 *ListTablesInfoReq) (ret0 *ListTablesInfoResp, ret1 error)

// DecorateListTablesInfo decorate method ListTablesInfo
func (b *_DataSourceServiceDecorator) DecorateListTablesInfo(mw func(ListTablesInfoFunc) ListTablesInfoFunc) DataSourceServiceDecorator {
	b._ListTablesInfo = mw(b._ListTablesInfo)
	return b
}

// ListTablesInfo implement DataSourceService's method ListTablesInfo
func (b *_DataSourceServiceDecorator) ListTablesInfo(arg0 context.Context, arg1 *ListTablesInfoReq) (ret0 *ListTablesInfoResp, ret1 error) {
	return b._ListTablesInfo(arg0, arg1)
}

// DescribeTableFunc alias for method DescribeTable
type DescribeTableFunc func(arg0 context.Context, arg1 *DescribeTableReq) (ret0 *DescribeTableResp, ret1 error)

// DecorateDescribeTable decorate method DescribeTable
func (b *_DataSourceServiceDecorator) DecorateDescribeTable(mw func(DescribeTableFunc) DescribeTableFunc) DataSourceServiceDecorator {
	b._DescribeTable = mw(b._DescribeTable)
	return b
}

// DescribeTable implement DataSourceService's method DescribeTable
func (b *_DataSourceServiceDecorator) DescribeTable(arg0 context.Context, arg1 *DescribeTableReq) (ret0 *DescribeTableResp, ret1 error) {
	return b._DescribeTable(arg0, arg1)
}

// DescribeAutoKillSessionConfigFunc alias for method DescribeAutoKillSessionConfig
type DescribeAutoKillSessionConfigFunc func(arg0 context.Context, arg1 *DescribeAutoKillSessionConfigReq) (ret0 *DescribeAutoKillSessionConfigResp, ret1 error)

// DecorateDescribeAutoKillSessionConfig decorate method DescribeAutoKillSessionConfig
func (b *_DataSourceServiceDecorator) DecorateDescribeAutoKillSessionConfig(mw func(DescribeAutoKillSessionConfigFunc) DescribeAutoKillSessionConfigFunc) DataSourceServiceDecorator {
	b._DescribeAutoKillSessionConfig = mw(b._DescribeAutoKillSessionConfig)
	return b
}

// DescribeAutoKillSessionConfig implement DataSourceService's method DescribeAutoKillSessionConfig
func (b *_DataSourceServiceDecorator) DescribeAutoKillSessionConfig(arg0 context.Context, arg1 *DescribeAutoKillSessionConfigReq) (ret0 *DescribeAutoKillSessionConfigResp, ret1 error) {
	return b._DescribeAutoKillSessionConfig(arg0, arg1)
}

// ModifyAutoKillSessionConfigFunc alias for method ModifyAutoKillSessionConfig
type ModifyAutoKillSessionConfigFunc func(arg0 context.Context, arg1 *ModifyAutoKillSessionConfigReq) (ret0 *ModifyAutoKillSessionConfigResp, ret1 error)

// DecorateModifyAutoKillSessionConfig decorate method ModifyAutoKillSessionConfig
func (b *_DataSourceServiceDecorator) DecorateModifyAutoKillSessionConfig(mw func(ModifyAutoKillSessionConfigFunc) ModifyAutoKillSessionConfigFunc) DataSourceServiceDecorator {
	b._ModifyAutoKillSessionConfig = mw(b._ModifyAutoKillSessionConfig)
	return b
}

// ModifyAutoKillSessionConfig implement DataSourceService's method ModifyAutoKillSessionConfig
func (b *_DataSourceServiceDecorator) ModifyAutoKillSessionConfig(arg0 context.Context, arg1 *ModifyAutoKillSessionConfigReq) (ret0 *ModifyAutoKillSessionConfigResp, ret1 error) {
	return b._ModifyAutoKillSessionConfig(arg0, arg1)
}

// DescribeInstanceVersionFunc alias for method DescribeInstanceVersion
type DescribeInstanceVersionFunc func(arg0 context.Context, arg1 *DescribeInstanceVersionReq) (ret0 *DescribeInstanceVersionResp, ret1 error)

// DecorateDescribeInstanceVersion decorate method DescribeInstanceVersion
func (b *_DataSourceServiceDecorator) DecorateDescribeInstanceVersion(mw func(DescribeInstanceVersionFunc) DescribeInstanceVersionFunc) DataSourceServiceDecorator {
	b._DescribeInstanceVersion = mw(b._DescribeInstanceVersion)
	return b
}

// DescribeInstanceVersion implement DataSourceService's method DescribeInstanceVersion
func (b *_DataSourceServiceDecorator) DescribeInstanceVersion(arg0 context.Context, arg1 *DescribeInstanceVersionReq) (ret0 *DescribeInstanceVersionResp, ret1 error) {
	return b._DescribeInstanceVersion(arg0, arg1)
}

// ListErrLogsFunc alias for method ListErrLogs
type ListErrLogsFunc func(arg0 context.Context, arg1 *ListErrLogsReq) (ret0 *ListErrLogsResp, ret1 error)

// DecorateListErrLogs decorate method ListErrLogs
func (b *_DataSourceServiceDecorator) DecorateListErrLogs(mw func(ListErrLogsFunc) ListErrLogsFunc) DataSourceServiceDecorator {
	b._ListErrLogs = mw(b._ListErrLogs)
	return b
}

// ListErrLogs implement DataSourceService's method ListErrLogs
func (b *_DataSourceServiceDecorator) ListErrLogs(arg0 context.Context, arg1 *ListErrLogsReq) (ret0 *ListErrLogsResp, ret1 error) {
	return b._ListErrLogs(arg0, arg1)
}

// DescribePgTableFunc alias for method DescribePgTable
type DescribePgTableFunc func(arg0 context.Context, arg1 *DescribePgTableReq) (ret0 *DescribePgTableResp, ret1 error)

// DecorateDescribePgTable decorate method DescribePgTable
func (b *_DataSourceServiceDecorator) DecorateDescribePgTable(mw func(DescribePgTableFunc) DescribePgTableFunc) DataSourceServiceDecorator {
	b._DescribePgTable = mw(b._DescribePgTable)
	return b
}

// DescribePgTable implement DataSourceService's method DescribePgTable
func (b *_DataSourceServiceDecorator) DescribePgTable(arg0 context.Context, arg1 *DescribePgTableReq) (ret0 *DescribePgTableResp, ret1 error) {
	return b._DescribePgTable(arg0, arg1)
}

// DescribeInstanceReplicaDelayFunc alias for method DescribeInstanceReplicaDelay
type DescribeInstanceReplicaDelayFunc func(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 int64, ret1 error)

// DecorateDescribeInstanceReplicaDelay decorate method DescribeInstanceReplicaDelay
func (b *_DataSourceServiceDecorator) DecorateDescribeInstanceReplicaDelay(mw func(DescribeInstanceReplicaDelayFunc) DescribeInstanceReplicaDelayFunc) DataSourceServiceDecorator {
	b._DescribeInstanceReplicaDelay = mw(b._DescribeInstanceReplicaDelay)
	return b
}

// DescribeInstanceReplicaDelay implement DataSourceService's method DescribeInstanceReplicaDelay
func (b *_DataSourceServiceDecorator) DescribeInstanceReplicaDelay(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 int64, ret1 error) {
	return b._DescribeInstanceReplicaDelay(arg0, arg1)
}

// ListViewsFunc alias for method ListViews
type ListViewsFunc func(arg0 context.Context, arg1 *ListViewsReq) (ret0 *ListViewsResp, ret1 error)

// DecorateListViews decorate method ListViews
func (b *_DataSourceServiceDecorator) DecorateListViews(mw func(ListViewsFunc) ListViewsFunc) DataSourceServiceDecorator {
	b._ListViews = mw(b._ListViews)
	return b
}

// ListViews implement DataSourceService's method ListViews
func (b *_DataSourceServiceDecorator) ListViews(arg0 context.Context, arg1 *ListViewsReq) (ret0 *ListViewsResp, ret1 error) {
	return b._ListViews(arg0, arg1)
}

// DescribeViewFunc alias for method DescribeView
type DescribeViewFunc func(arg0 context.Context, arg1 *DescribeViewReq) (ret0 *DescribeViewResp, ret1 error)

// DecorateDescribeView decorate method DescribeView
func (b *_DataSourceServiceDecorator) DecorateDescribeView(mw func(DescribeViewFunc) DescribeViewFunc) DataSourceServiceDecorator {
	b._DescribeView = mw(b._DescribeView)
	return b
}

// DescribeView implement DataSourceService's method DescribeView
func (b *_DataSourceServiceDecorator) DescribeView(arg0 context.Context, arg1 *DescribeViewReq) (ret0 *DescribeViewResp, ret1 error) {
	return b._DescribeView(arg0, arg1)
}

// DescribeFunctionFunc alias for method DescribeFunction
type DescribeFunctionFunc func(arg0 context.Context, arg1 *DescribeFunctionReq) (ret0 *DescribeFunctionResp, ret1 error)

// DecorateDescribeFunction decorate method DescribeFunction
func (b *_DataSourceServiceDecorator) DecorateDescribeFunction(mw func(DescribeFunctionFunc) DescribeFunctionFunc) DataSourceServiceDecorator {
	b._DescribeFunction = mw(b._DescribeFunction)
	return b
}

// DescribeFunction implement DataSourceService's method DescribeFunction
func (b *_DataSourceServiceDecorator) DescribeFunction(arg0 context.Context, arg1 *DescribeFunctionReq) (ret0 *DescribeFunctionResp, ret1 error) {
	return b._DescribeFunction(arg0, arg1)
}

// DescribeProcedureFunc alias for method DescribeProcedure
type DescribeProcedureFunc func(arg0 context.Context, arg1 *DescribeProcedureReq) (ret0 *DescribeProcedureResp, ret1 error)

// DecorateDescribeProcedure decorate method DescribeProcedure
func (b *_DataSourceServiceDecorator) DecorateDescribeProcedure(mw func(DescribeProcedureFunc) DescribeProcedureFunc) DataSourceServiceDecorator {
	b._DescribeProcedure = mw(b._DescribeProcedure)
	return b
}

// DescribeProcedure implement DataSourceService's method DescribeProcedure
func (b *_DataSourceServiceDecorator) DescribeProcedure(arg0 context.Context, arg1 *DescribeProcedureReq) (ret0 *DescribeProcedureResp, ret1 error) {
	return b._DescribeProcedure(arg0, arg1)
}

// ListFunctionsFunc alias for method ListFunctions
type ListFunctionsFunc func(arg0 context.Context, arg1 *ListFunctionsReq) (ret0 *ListFunctionsResp, ret1 error)

// DecorateListFunctions decorate method ListFunctions
func (b *_DataSourceServiceDecorator) DecorateListFunctions(mw func(ListFunctionsFunc) ListFunctionsFunc) DataSourceServiceDecorator {
	b._ListFunctions = mw(b._ListFunctions)
	return b
}

// ListFunctions implement DataSourceService's method ListFunctions
func (b *_DataSourceServiceDecorator) ListFunctions(arg0 context.Context, arg1 *ListFunctionsReq) (ret0 *ListFunctionsResp, ret1 error) {
	return b._ListFunctions(arg0, arg1)
}

// ListProceduresFunc alias for method ListProcedures
type ListProceduresFunc func(arg0 context.Context, arg1 *ListProceduresReq) (ret0 *ListProceduresResp, ret1 error)

// DecorateListProcedures decorate method ListProcedures
func (b *_DataSourceServiceDecorator) DecorateListProcedures(mw func(ListProceduresFunc) ListProceduresFunc) DataSourceServiceDecorator {
	b._ListProcedures = mw(b._ListProcedures)
	return b
}

// ListProcedures implement DataSourceService's method ListProcedures
func (b *_DataSourceServiceDecorator) ListProcedures(arg0 context.Context, arg1 *ListProceduresReq) (ret0 *ListProceduresResp, ret1 error) {
	return b._ListProcedures(arg0, arg1)
}

// ListTriggersFunc alias for method ListTriggers
type ListTriggersFunc func(arg0 context.Context, arg1 *ListTriggersReq) (ret0 *ListTriggersResp, ret1 error)

// DecorateListTriggers decorate method ListTriggers
func (b *_DataSourceServiceDecorator) DecorateListTriggers(mw func(ListTriggersFunc) ListTriggersFunc) DataSourceServiceDecorator {
	b._ListTriggers = mw(b._ListTriggers)
	return b
}

// ListTriggers implement DataSourceService's method ListTriggers
func (b *_DataSourceServiceDecorator) ListTriggers(arg0 context.Context, arg1 *ListTriggersReq) (ret0 *ListTriggersResp, ret1 error) {
	return b._ListTriggers(arg0, arg1)
}

// DescribeTLSConnectionInfoFunc alias for method DescribeTLSConnectionInfo
type DescribeTLSConnectionInfoFunc func(arg0 context.Context, arg1 *DescribeTLSConnectionInfoReq) (ret0 *DescribeTLSConnectionInfoResp, ret1 error)

// DecorateDescribeTLSConnectionInfo decorate method DescribeTLSConnectionInfo
func (b *_DataSourceServiceDecorator) DecorateDescribeTLSConnectionInfo(mw func(DescribeTLSConnectionInfoFunc) DescribeTLSConnectionInfoFunc) DataSourceServiceDecorator {
	b._DescribeTLSConnectionInfo = mw(b._DescribeTLSConnectionInfo)
	return b
}

// DescribeTLSConnectionInfo implement DataSourceService's method DescribeTLSConnectionInfo
func (b *_DataSourceServiceDecorator) DescribeTLSConnectionInfo(arg0 context.Context, arg1 *DescribeTLSConnectionInfoReq) (ret0 *DescribeTLSConnectionInfoResp, ret1 error) {
	return b._DescribeTLSConnectionInfo(arg0, arg1)
}

// ListKeyNumbersFunc alias for method ListKeyNumbers
type ListKeyNumbersFunc func(arg0 context.Context, arg1 *ListKeyNumbersReq) (ret0 *ListKeyNumbersResp, ret1 error)

// DecorateListKeyNumbers decorate method ListKeyNumbers
func (b *_DataSourceServiceDecorator) DecorateListKeyNumbers(mw func(ListKeyNumbersFunc) ListKeyNumbersFunc) DataSourceServiceDecorator {
	b._ListKeyNumbers = mw(b._ListKeyNumbers)
	return b
}

// ListKeyNumbers implement DataSourceService's method ListKeyNumbers
func (b *_DataSourceServiceDecorator) ListKeyNumbers(arg0 context.Context, arg1 *ListKeyNumbersReq) (ret0 *ListKeyNumbersResp, ret1 error) {
	return b._ListKeyNumbers(arg0, arg1)
}

// ListKeysFunc alias for method ListKeys
type ListKeysFunc func(arg0 context.Context, arg1 *ListKeysReq) (ret0 *ListKeysResp, ret1 error)

// DecorateListKeys decorate method ListKeys
func (b *_DataSourceServiceDecorator) DecorateListKeys(mw func(ListKeysFunc) ListKeysFunc) DataSourceServiceDecorator {
	b._ListKeys = mw(b._ListKeys)
	return b
}

// ListKeys implement DataSourceService's method ListKeys
func (b *_DataSourceServiceDecorator) ListKeys(arg0 context.Context, arg1 *ListKeysReq) (ret0 *ListKeysResp, ret1 error) {
	return b._ListKeys(arg0, arg1)
}

// GetKeyFunc alias for method GetKey
type GetKeyFunc func(arg0 context.Context, arg1 *GetKeyReq) (ret0 *GetKeyResp, ret1 error)

// DecorateGetKey decorate method GetKey
func (b *_DataSourceServiceDecorator) DecorateGetKey(mw func(GetKeyFunc) GetKeyFunc) DataSourceServiceDecorator {
	b._GetKey = mw(b._GetKey)
	return b
}

// GetKey implement DataSourceService's method GetKey
func (b *_DataSourceServiceDecorator) GetKey(arg0 context.Context, arg1 *GetKeyReq) (ret0 *GetKeyResp, ret1 error) {
	return b._GetKey(arg0, arg1)
}

// ListKeyMembersFunc alias for method ListKeyMembers
type ListKeyMembersFunc func(arg0 context.Context, arg1 *ListKeyMembersReq) (ret0 *ListKeyMembersResp, ret1 error)

// DecorateListKeyMembers decorate method ListKeyMembers
func (b *_DataSourceServiceDecorator) DecorateListKeyMembers(mw func(ListKeyMembersFunc) ListKeyMembersFunc) DataSourceServiceDecorator {
	b._ListKeyMembers = mw(b._ListKeyMembers)
	return b
}

// ListKeyMembers implement DataSourceService's method ListKeyMembers
func (b *_DataSourceServiceDecorator) ListKeyMembers(arg0 context.Context, arg1 *ListKeyMembersReq) (ret0 *ListKeyMembersResp, ret1 error) {
	return b._ListKeyMembers(arg0, arg1)
}

// ListAlterKVsCommandsFunc alias for method ListAlterKVsCommands
type ListAlterKVsCommandsFunc func(arg0 context.Context, arg1 *ListAlterKVsCommandsReq) (ret0 *ListAlterKVsCommandsResp, ret1 error)

// DecorateListAlterKVsCommands decorate method ListAlterKVsCommands
func (b *_DataSourceServiceDecorator) DecorateListAlterKVsCommands(mw func(ListAlterKVsCommandsFunc) ListAlterKVsCommandsFunc) DataSourceServiceDecorator {
	b._ListAlterKVsCommands = mw(b._ListAlterKVsCommands)
	return b
}

// ListAlterKVsCommands implement DataSourceService's method ListAlterKVsCommands
func (b *_DataSourceServiceDecorator) ListAlterKVsCommands(arg0 context.Context, arg1 *ListAlterKVsCommandsReq) (ret0 *ListAlterKVsCommandsResp, ret1 error) {
	return b._ListAlterKVsCommands(arg0, arg1)
}

// DescribeBigKeysFunc alias for method DescribeBigKeys
type DescribeBigKeysFunc func(arg0 context.Context, arg1 *DescribeBigKeysReq) (ret0 *DescribeBigKeysResp, ret1 error)

// DecorateDescribeBigKeys decorate method DescribeBigKeys
func (b *_DataSourceServiceDecorator) DecorateDescribeBigKeys(mw func(DescribeBigKeysFunc) DescribeBigKeysFunc) DataSourceServiceDecorator {
	b._DescribeBigKeys = mw(b._DescribeBigKeys)
	return b
}

// DescribeBigKeys implement DataSourceService's method DescribeBigKeys
func (b *_DataSourceServiceDecorator) DescribeBigKeys(arg0 context.Context, arg1 *DescribeBigKeysReq) (ret0 *DescribeBigKeysResp, ret1 error) {
	return b._DescribeBigKeys(arg0, arg1)
}

// DescribeHotKeysFunc alias for method DescribeHotKeys
type DescribeHotKeysFunc func(arg0 context.Context, arg1 *DescribeHotKeysReq) (ret0 *DescribeHotKeysResp, ret1 error)

// DecorateDescribeHotKeys decorate method DescribeHotKeys
func (b *_DataSourceServiceDecorator) DecorateDescribeHotKeys(mw func(DescribeHotKeysFunc) DescribeHotKeysFunc) DataSourceServiceDecorator {
	b._DescribeHotKeys = mw(b._DescribeHotKeys)
	return b
}

// DescribeHotKeys implement DataSourceService's method DescribeHotKeys
func (b *_DataSourceServiceDecorator) DescribeHotKeys(arg0 context.Context, arg1 *DescribeHotKeysReq) (ret0 *DescribeHotKeysResp, ret1 error) {
	return b._DescribeHotKeys(arg0, arg1)
}

// OpenTunnelFunc alias for method OpenTunnel
type OpenTunnelFunc func(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 error)

// DecorateOpenTunnel decorate method OpenTunnel
func (b *_DataSourceServiceDecorator) DecorateOpenTunnel(mw func(OpenTunnelFunc) OpenTunnelFunc) DataSourceServiceDecorator {
	b._OpenTunnel = mw(b._OpenTunnel)
	return b
}

// OpenTunnel implement DataSourceService's method OpenTunnel
func (b *_DataSourceServiceDecorator) OpenTunnel(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 error) {
	return b._OpenTunnel(arg0, arg1, arg2)
}

// DescribeTriggerFunc alias for method DescribeTrigger
type DescribeTriggerFunc func(arg0 context.Context, arg1 *DescribeTriggerReq) (ret0 *DescribeTriggerResp, ret1 error)

// DecorateDescribeTrigger decorate method DescribeTrigger
func (b *_DataSourceServiceDecorator) DecorateDescribeTrigger(mw func(DescribeTriggerFunc) DescribeTriggerFunc) DataSourceServiceDecorator {
	b._DescribeTrigger = mw(b._DescribeTrigger)
	return b
}

// DescribeTrigger implement DataSourceService's method DescribeTrigger
func (b *_DataSourceServiceDecorator) DescribeTrigger(arg0 context.Context, arg1 *DescribeTriggerReq) (ret0 *DescribeTriggerResp, ret1 error) {
	return b._DescribeTrigger(arg0, arg1)
}

// DescribeEventFunc alias for method DescribeEvent
type DescribeEventFunc func(arg0 context.Context, arg1 *DescribeEventReq) (ret0 *DescribeEventResp, ret1 error)

// DecorateDescribeEvent decorate method DescribeEvent
func (b *_DataSourceServiceDecorator) DecorateDescribeEvent(mw func(DescribeEventFunc) DescribeEventFunc) DataSourceServiceDecorator {
	b._DescribeEvent = mw(b._DescribeEvent)
	return b
}

// DescribeEvent implement DataSourceService's method DescribeEvent
func (b *_DataSourceServiceDecorator) DescribeEvent(arg0 context.Context, arg1 *DescribeEventReq) (ret0 *DescribeEventResp, ret1 error) {
	return b._DescribeEvent(arg0, arg1)
}

// ListEventsFunc alias for method ListEvents
type ListEventsFunc func(arg0 context.Context, arg1 *ListEventsReq) (ret0 *ListEventsResp, ret1 error)

// DecorateListEvents decorate method ListEvents
func (b *_DataSourceServiceDecorator) DecorateListEvents(mw func(ListEventsFunc) ListEventsFunc) DataSourceServiceDecorator {
	b._ListEvents = mw(b._ListEvents)
	return b
}

// ListEvents implement DataSourceService's method ListEvents
func (b *_DataSourceServiceDecorator) ListEvents(arg0 context.Context, arg1 *ListEventsReq) (ret0 *ListEventsResp, ret1 error) {
	return b._ListEvents(arg0, arg1)
}

// CreateAccountFunc alias for method CreateAccount
type CreateAccountFunc func(arg0 context.Context, arg1 *CreateAccountReq) (ret0 error)

// DecorateCreateAccount decorate method CreateAccount
func (b *_DataSourceServiceDecorator) DecorateCreateAccount(mw func(CreateAccountFunc) CreateAccountFunc) DataSourceServiceDecorator {
	b._CreateAccount = mw(b._CreateAccount)
	return b
}

// CreateAccount implement DataSourceService's method CreateAccount
func (b *_DataSourceServiceDecorator) CreateAccount(arg0 context.Context, arg1 *CreateAccountReq) (ret0 error) {
	return b._CreateAccount(arg0, arg1)
}

// CheckPrivilegeFunc alias for method CheckPrivilege
type CheckPrivilegeFunc func(arg0 context.Context, arg1 string, arg2 string, arg3 string, arg4 string, arg5 shared.DataSourceType) (ret0 bool, ret1 error)

// DecorateCheckPrivilege decorate method CheckPrivilege
func (b *_DataSourceServiceDecorator) DecorateCheckPrivilege(mw func(CheckPrivilegeFunc) CheckPrivilegeFunc) DataSourceServiceDecorator {
	b._CheckPrivilege = mw(b._CheckPrivilege)
	return b
}

// CheckPrivilege implement DataSourceService's method CheckPrivilege
func (b *_DataSourceServiceDecorator) CheckPrivilege(arg0 context.Context, arg1 string, arg2 string, arg3 string, arg4 string, arg5 shared.DataSourceType) (ret0 bool, ret1 error) {
	return b._CheckPrivilege(arg0, arg1, arg2, arg3, arg4, arg5)
}

// DescribeAccountsFunc alias for method DescribeAccounts
type DescribeAccountsFunc func(arg0 context.Context, arg1 *DescribeAccountsReq) (ret0 *DescribeAccountResp, ret1 error)

// DecorateDescribeAccounts decorate method DescribeAccounts
func (b *_DataSourceServiceDecorator) DecorateDescribeAccounts(mw func(DescribeAccountsFunc) DescribeAccountsFunc) DataSourceServiceDecorator {
	b._DescribeAccounts = mw(b._DescribeAccounts)
	return b
}

// DescribeAccounts implement DataSourceService's method DescribeAccounts
func (b *_DataSourceServiceDecorator) DescribeAccounts(arg0 context.Context, arg1 *DescribeAccountsReq) (ret0 *DescribeAccountResp, ret1 error) {
	return b._DescribeAccounts(arg0, arg1)
}

// DescribeAccounts2Func alias for method DescribeAccounts2
type DescribeAccounts2Func func(arg0 context.Context, arg1 *DescribeAccountsReq) (ret0 *DescribeAccountResp2, ret1 error)

// DecorateDescribeAccounts2 decorate method DescribeAccounts2
func (b *_DataSourceServiceDecorator) DecorateDescribeAccounts2(mw func(DescribeAccounts2Func) DescribeAccounts2Func) DataSourceServiceDecorator {
	b._DescribeAccounts2 = mw(b._DescribeAccounts2)
	return b
}

// DescribeAccounts2 implement DataSourceService's method DescribeAccounts2
func (b *_DataSourceServiceDecorator) DescribeAccounts2(arg0 context.Context, arg1 *DescribeAccountsReq) (ret0 *DescribeAccountResp2, ret1 error) {
	return b._DescribeAccounts2(arg0, arg1)
}

// CreateAccountAndGrantFunc alias for method CreateAccountAndGrant
type CreateAccountAndGrantFunc func(arg0 context.Context, arg1 string, arg2 string, arg3 string, arg4 string, arg5 string, arg6 shared.DataSourceType) (ret0 error)

// DecorateCreateAccountAndGrant decorate method CreateAccountAndGrant
func (b *_DataSourceServiceDecorator) DecorateCreateAccountAndGrant(mw func(CreateAccountAndGrantFunc) CreateAccountAndGrantFunc) DataSourceServiceDecorator {
	b._CreateAccountAndGrant = mw(b._CreateAccountAndGrant)
	return b
}

// CreateAccountAndGrant implement DataSourceService's method CreateAccountAndGrant
func (b *_DataSourceServiceDecorator) CreateAccountAndGrant(arg0 context.Context, arg1 string, arg2 string, arg3 string, arg4 string, arg5 string, arg6 shared.DataSourceType) (ret0 error) {
	return b._CreateAccountAndGrant(arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// ModifyAccountPrivilegeFunc alias for method ModifyAccountPrivilege
type ModifyAccountPrivilegeFunc func(arg0 context.Context, arg1 *ModifyAccountPrivilegeReq) (ret0 error)

// DecorateModifyAccountPrivilege decorate method ModifyAccountPrivilege
func (b *_DataSourceServiceDecorator) DecorateModifyAccountPrivilege(mw func(ModifyAccountPrivilegeFunc) ModifyAccountPrivilegeFunc) DataSourceServiceDecorator {
	b._ModifyAccountPrivilege = mw(b._ModifyAccountPrivilege)
	return b
}

// ModifyAccountPrivilege implement DataSourceService's method ModifyAccountPrivilege
func (b *_DataSourceServiceDecorator) ModifyAccountPrivilege(arg0 context.Context, arg1 *ModifyAccountPrivilegeReq) (ret0 error) {
	return b._ModifyAccountPrivilege(arg0, arg1)
}

// GrantAccountPrivilegeFunc alias for method GrantAccountPrivilege
type GrantAccountPrivilegeFunc func(arg0 context.Context, arg1 *GrantAccountPrivilegeReq) (ret0 error)

// DecorateGrantAccountPrivilege decorate method GrantAccountPrivilege
func (b *_DataSourceServiceDecorator) DecorateGrantAccountPrivilege(mw func(GrantAccountPrivilegeFunc) GrantAccountPrivilegeFunc) DataSourceServiceDecorator {
	b._GrantAccountPrivilege = mw(b._GrantAccountPrivilege)
	return b
}

// GrantAccountPrivilege implement DataSourceService's method GrantAccountPrivilege
func (b *_DataSourceServiceDecorator) GrantAccountPrivilege(arg0 context.Context, arg1 *GrantAccountPrivilegeReq) (ret0 error) {
	return b._GrantAccountPrivilege(arg0, arg1)
}

// DeleteAccountFunc alias for method DeleteAccount
type DeleteAccountFunc func(arg0 context.Context, arg1 *DeleteAccountReq) (ret0 error)

// DecorateDeleteAccount decorate method DeleteAccount
func (b *_DataSourceServiceDecorator) DecorateDeleteAccount(mw func(DeleteAccountFunc) DeleteAccountFunc) DataSourceServiceDecorator {
	b._DeleteAccount = mw(b._DeleteAccount)
	return b
}

// DeleteAccount implement DataSourceService's method DeleteAccount
func (b *_DataSourceServiceDecorator) DeleteAccount(arg0 context.Context, arg1 *DeleteAccountReq) (ret0 error) {
	return b._DeleteAccount(arg0, arg1)
}

// ListDatabasesWithAccountFunc alias for method ListDatabasesWithAccount
type ListDatabasesWithAccountFunc func(arg0 context.Context, arg1 *ListDatabasesWithAccountReq) (ret0 *ListDatabasesWithAccountResp, ret1 error)

// DecorateListDatabasesWithAccount decorate method ListDatabasesWithAccount
func (b *_DataSourceServiceDecorator) DecorateListDatabasesWithAccount(mw func(ListDatabasesWithAccountFunc) ListDatabasesWithAccountFunc) DataSourceServiceDecorator {
	b._ListDatabasesWithAccount = mw(b._ListDatabasesWithAccount)
	return b
}

// ListDatabasesWithAccount implement DataSourceService's method ListDatabasesWithAccount
func (b *_DataSourceServiceDecorator) ListDatabasesWithAccount(arg0 context.Context, arg1 *ListDatabasesWithAccountReq) (ret0 *ListDatabasesWithAccountResp, ret1 error) {
	return b._ListDatabasesWithAccount(arg0, arg1)
}

// GetAdviceFunc alias for method GetAdvice
type GetAdviceFunc func(arg0 context.Context, arg1 *GetAdviceReq) (ret0 *GetAdviceResp, ret1 error)

// DecorateGetAdvice decorate method GetAdvice
func (b *_DataSourceServiceDecorator) DecorateGetAdvice(mw func(GetAdviceFunc) GetAdviceFunc) DataSourceServiceDecorator {
	b._GetAdvice = mw(b._GetAdvice)
	return b
}

// GetAdvice implement DataSourceService's method GetAdvice
func (b *_DataSourceServiceDecorator) GetAdvice(arg0 context.Context, arg1 *GetAdviceReq) (ret0 *GetAdviceResp, ret1 error) {
	return b._GetAdvice(arg0, arg1)
}

// ListCharsetsFunc alias for method ListCharsets
type ListCharsetsFunc func(arg0 context.Context, arg1 *ListCharsetsReq) (ret0 *ListCharsetsResp, ret1 error)

// DecorateListCharsets decorate method ListCharsets
func (b *_DataSourceServiceDecorator) DecorateListCharsets(mw func(ListCharsetsFunc) ListCharsetsFunc) DataSourceServiceDecorator {
	b._ListCharsets = mw(b._ListCharsets)
	return b
}

// ListCharsets implement DataSourceService's method ListCharsets
func (b *_DataSourceServiceDecorator) ListCharsets(arg0 context.Context, arg1 *ListCharsetsReq) (ret0 *ListCharsetsResp, ret1 error) {
	return b._ListCharsets(arg0, arg1)
}

// ListCollationsFunc alias for method ListCollations
type ListCollationsFunc func(arg0 context.Context, arg1 *ListCollationsReq) (ret0 *ListCollationsResp, ret1 error)

// DecorateListCollations decorate method ListCollations
func (b *_DataSourceServiceDecorator) DecorateListCollations(mw func(ListCollationsFunc) ListCollationsFunc) DataSourceServiceDecorator {
	b._ListCollations = mw(b._ListCollations)
	return b
}

// ListCollations implement DataSourceService's method ListCollations
func (b *_DataSourceServiceDecorator) ListCollations(arg0 context.Context, arg1 *ListCollationsReq) (ret0 *ListCollationsResp, ret1 error) {
	return b._ListCollations(arg0, arg1)
}

// ListSchemaFunc alias for method ListSchema
type ListSchemaFunc func(arg0 context.Context, arg1 *ListSchemaReq) (ret0 *ListSchemaResp, ret1 error)

// DecorateListSchema decorate method ListSchema
func (b *_DataSourceServiceDecorator) DecorateListSchema(mw func(ListSchemaFunc) ListSchemaFunc) DataSourceServiceDecorator {
	b._ListSchema = mw(b._ListSchema)
	return b
}

// ListSchema implement DataSourceService's method ListSchema
func (b *_DataSourceServiceDecorator) ListSchema(arg0 context.Context, arg1 *ListSchemaReq) (ret0 *ListSchemaResp, ret1 error) {
	return b._ListSchema(arg0, arg1)
}

// ListSequenceFunc alias for method ListSequence
type ListSequenceFunc func(arg0 context.Context, arg1 *ListSequenceReq) (ret0 *ListSequenceResp, ret1 error)

// DecorateListSequence decorate method ListSequence
func (b *_DataSourceServiceDecorator) DecorateListSequence(mw func(ListSequenceFunc) ListSequenceFunc) DataSourceServiceDecorator {
	b._ListSequence = mw(b._ListSequence)
	return b
}

// ListSequence implement DataSourceService's method ListSequence
func (b *_DataSourceServiceDecorator) ListSequence(arg0 context.Context, arg1 *ListSequenceReq) (ret0 *ListSequenceResp, ret1 error) {
	return b._ListSequence(arg0, arg1)
}

// ListPgCollationsFunc alias for method ListPgCollations
type ListPgCollationsFunc func(arg0 context.Context, arg1 *ListPgCollationsReq) (ret0 *ListPgCollationsResp, ret1 error)

// DecorateListPgCollations decorate method ListPgCollations
func (b *_DataSourceServiceDecorator) DecorateListPgCollations(mw func(ListPgCollationsFunc) ListPgCollationsFunc) DataSourceServiceDecorator {
	b._ListPgCollations = mw(b._ListPgCollations)
	return b
}

// ListPgCollations implement DataSourceService's method ListPgCollations
func (b *_DataSourceServiceDecorator) ListPgCollations(arg0 context.Context, arg1 *ListPgCollationsReq) (ret0 *ListPgCollationsResp, ret1 error) {
	return b._ListPgCollations(arg0, arg1)
}

// ListPgUsersFunc alias for method ListPgUsers
type ListPgUsersFunc func(arg0 context.Context, arg1 *ListPgUsersReq) (ret0 *ListPgUsersResp, ret1 error)

// DecorateListPgUsers decorate method ListPgUsers
func (b *_DataSourceServiceDecorator) DecorateListPgUsers(mw func(ListPgUsersFunc) ListPgUsersFunc) DataSourceServiceDecorator {
	b._ListPgUsers = mw(b._ListPgUsers)
	return b
}

// ListPgUsers implement DataSourceService's method ListPgUsers
func (b *_DataSourceServiceDecorator) ListPgUsers(arg0 context.Context, arg1 *ListPgUsersReq) (ret0 *ListPgUsersResp, ret1 error) {
	return b._ListPgUsers(arg0, arg1)
}

// ListTableSpacesFunc alias for method ListTableSpaces
type ListTableSpacesFunc func(arg0 context.Context, arg1 *ListTableSpacesReq) (ret0 *ListTableSpacesResp, ret1 error)

// DecorateListTableSpaces decorate method ListTableSpaces
func (b *_DataSourceServiceDecorator) DecorateListTableSpaces(mw func(ListTableSpacesFunc) ListTableSpacesFunc) DataSourceServiceDecorator {
	b._ListTableSpaces = mw(b._ListTableSpaces)
	return b
}

// ListTableSpaces implement DataSourceService's method ListTableSpaces
func (b *_DataSourceServiceDecorator) ListTableSpaces(arg0 context.Context, arg1 *ListTableSpacesReq) (ret0 *ListTableSpacesResp, ret1 error) {
	return b._ListTableSpaces(arg0, arg1)
}

// DescribeDialogDetailsFunc alias for method DescribeDialogDetails
type DescribeDialogDetailsFunc func(arg0 context.Context, arg1 *DescribeDialogDetailsReq) (ret0 *DescribeDialogDetailsResp, ret1 error)

// DecorateDescribeDialogDetails decorate method DescribeDialogDetails
func (b *_DataSourceServiceDecorator) DecorateDescribeDialogDetails(mw func(DescribeDialogDetailsFunc) DescribeDialogDetailsFunc) DataSourceServiceDecorator {
	b._DescribeDialogDetails = mw(b._DescribeDialogDetails)
	return b
}

// DescribeDialogDetails implement DataSourceService's method DescribeDialogDetails
func (b *_DataSourceServiceDecorator) DescribeDialogDetails(arg0 context.Context, arg1 *DescribeDialogDetailsReq) (ret0 *DescribeDialogDetailsResp, ret1 error) {
	return b._DescribeDialogDetails(arg0, arg1)
}

// DescribeDialogStatisticsFunc alias for method DescribeDialogStatistics
type DescribeDialogStatisticsFunc func(arg0 context.Context, arg1 *DescribeDialogStatisticsReq) (ret0 *DescribeDialogStatisticsResp, ret1 error)

// DecorateDescribeDialogStatistics decorate method DescribeDialogStatistics
func (b *_DataSourceServiceDecorator) DecorateDescribeDialogStatistics(mw func(DescribeDialogStatisticsFunc) DescribeDialogStatisticsFunc) DataSourceServiceDecorator {
	b._DescribeDialogStatistics = mw(b._DescribeDialogStatistics)
	return b
}

// DescribeDialogStatistics implement DataSourceService's method DescribeDialogStatistics
func (b *_DataSourceServiceDecorator) DescribeDialogStatistics(arg0 context.Context, arg1 *DescribeDialogStatisticsReq) (ret0 *DescribeDialogStatisticsResp, ret1 error) {
	return b._DescribeDialogStatistics(arg0, arg1)
}

// DescribeEngineStatusFunc alias for method DescribeEngineStatus
type DescribeEngineStatusFunc func(arg0 context.Context, arg1 *DescribeEngineStatusReq) (ret0 *DescribeEngineStatusResp, ret1 error)

// DecorateDescribeEngineStatus decorate method DescribeEngineStatus
func (b *_DataSourceServiceDecorator) DecorateDescribeEngineStatus(mw func(DescribeEngineStatusFunc) DescribeEngineStatusFunc) DataSourceServiceDecorator {
	b._DescribeEngineStatus = mw(b._DescribeEngineStatus)
	return b
}

// DescribeEngineStatus implement DataSourceService's method DescribeEngineStatus
func (b *_DataSourceServiceDecorator) DescribeEngineStatus(arg0 context.Context, arg1 *DescribeEngineStatusReq) (ret0 *DescribeEngineStatusResp, ret1 error) {
	return b._DescribeEngineStatus(arg0, arg1)
}

// KillProcessFunc alias for method KillProcess
type KillProcessFunc func(arg0 context.Context, arg1 *KillProcessReq) (ret0 *KillProcessResp, ret1 error)

// DecorateKillProcess decorate method KillProcess
func (b *_DataSourceServiceDecorator) DecorateKillProcess(mw func(KillProcessFunc) KillProcessFunc) DataSourceServiceDecorator {
	b._KillProcess = mw(b._KillProcess)
	return b
}

// KillProcess implement DataSourceService's method KillProcess
func (b *_DataSourceServiceDecorator) KillProcess(arg0 context.Context, arg1 *KillProcessReq) (ret0 *KillProcessResp, ret1 error) {
	return b._KillProcess(arg0, arg1)
}

// DescribeTrxAndLocksFunc alias for method DescribeTrxAndLocks
type DescribeTrxAndLocksFunc func(arg0 context.Context, arg1 *DescribeTrxAndLocksReq) (ret0 *DescribeTrxAndLocksResp, ret1 error)

// DecorateDescribeTrxAndLocks decorate method DescribeTrxAndLocks
func (b *_DataSourceServiceDecorator) DecorateDescribeTrxAndLocks(mw func(DescribeTrxAndLocksFunc) DescribeTrxAndLocksFunc) DataSourceServiceDecorator {
	b._DescribeTrxAndLocks = mw(b._DescribeTrxAndLocks)
	return b
}

// DescribeTrxAndLocks implement DataSourceService's method DescribeTrxAndLocks
func (b *_DataSourceServiceDecorator) DescribeTrxAndLocks(arg0 context.Context, arg1 *DescribeTrxAndLocksReq) (ret0 *DescribeTrxAndLocksResp, ret1 error) {
	return b._DescribeTrxAndLocks(arg0, arg1)
}

// DescribeLockCurrentWaitsFunc alias for method DescribeLockCurrentWaits
type DescribeLockCurrentWaitsFunc func(arg0 context.Context, arg1 *DescribeLockCurrentWaitsReq) (ret0 *DescribeLockCurrentWaitsResp, ret1 error)

// DecorateDescribeLockCurrentWaits decorate method DescribeLockCurrentWaits
func (b *_DataSourceServiceDecorator) DecorateDescribeLockCurrentWaits(mw func(DescribeLockCurrentWaitsFunc) DescribeLockCurrentWaitsFunc) DataSourceServiceDecorator {
	b._DescribeLockCurrentWaits = mw(b._DescribeLockCurrentWaits)
	return b
}

// DescribeLockCurrentWaits implement DataSourceService's method DescribeLockCurrentWaits
func (b *_DataSourceServiceDecorator) DescribeLockCurrentWaits(arg0 context.Context, arg1 *DescribeLockCurrentWaitsReq) (ret0 *DescribeLockCurrentWaitsResp, ret1 error) {
	return b._DescribeLockCurrentWaits(arg0, arg1)
}

// DescribeDeadlockFunc alias for method DescribeDeadlock
type DescribeDeadlockFunc func(arg0 context.Context, arg1 *DescribeDeadlockReq) (ret0 *DescribeDeadlockResp, ret1 error)

// DecorateDescribeDeadlock decorate method DescribeDeadlock
func (b *_DataSourceServiceDecorator) DecorateDescribeDeadlock(mw func(DescribeDeadlockFunc) DescribeDeadlockFunc) DataSourceServiceDecorator {
	b._DescribeDeadlock = mw(b._DescribeDeadlock)
	return b
}

// DescribeDeadlock implement DataSourceService's method DescribeDeadlock
func (b *_DataSourceServiceDecorator) DescribeDeadlock(arg0 context.Context, arg1 *DescribeDeadlockReq) (ret0 *DescribeDeadlockResp, ret1 error) {
	return b._DescribeDeadlock(arg0, arg1)
}

// DescribeDeadlockDetectFunc alias for method DescribeDeadlockDetect
type DescribeDeadlockDetectFunc func(arg0 context.Context, arg1 *DescribeDeadlockDetectReq) (ret0 *DescribeDeadlockDetectResp, ret1 error)

// DecorateDescribeDeadlockDetect decorate method DescribeDeadlockDetect
func (b *_DataSourceServiceDecorator) DecorateDescribeDeadlockDetect(mw func(DescribeDeadlockDetectFunc) DescribeDeadlockDetectFunc) DataSourceServiceDecorator {
	b._DescribeDeadlockDetect = mw(b._DescribeDeadlockDetect)
	return b
}

// DescribeDeadlockDetect implement DataSourceService's method DescribeDeadlockDetect
func (b *_DataSourceServiceDecorator) DescribeDeadlockDetect(arg0 context.Context, arg1 *DescribeDeadlockDetectReq) (ret0 *DescribeDeadlockDetectResp, ret1 error) {
	return b._DescribeDeadlockDetect(arg0, arg1)
}

// DescribeDialogInfosFunc alias for method DescribeDialogInfos
type DescribeDialogInfosFunc func(arg0 context.Context, arg1 *DescribeDialogInfosReq) (ret0 *DescribeDialogInfosResp, ret1 error)

// DecorateDescribeDialogInfos decorate method DescribeDialogInfos
func (b *_DataSourceServiceDecorator) DecorateDescribeDialogInfos(mw func(DescribeDialogInfosFunc) DescribeDialogInfosFunc) DataSourceServiceDecorator {
	b._DescribeDialogInfos = mw(b._DescribeDialogInfos)
	return b
}

// DescribeDialogInfos implement DataSourceService's method DescribeDialogInfos
func (b *_DataSourceServiceDecorator) DescribeDialogInfos(arg0 context.Context, arg1 *DescribeDialogInfosReq) (ret0 *DescribeDialogInfosResp, ret1 error) {
	return b._DescribeDialogInfos(arg0, arg1)
}

// DescribeCurrentConnFunc alias for method DescribeCurrentConn
type DescribeCurrentConnFunc func(arg0 context.Context, arg1 *DescribeCurrentConnsReq) (ret0 *DescribeCurrentConnsResp, ret1 error)

// DecorateDescribeCurrentConn decorate method DescribeCurrentConn
func (b *_DataSourceServiceDecorator) DecorateDescribeCurrentConn(mw func(DescribeCurrentConnFunc) DescribeCurrentConnFunc) DataSourceServiceDecorator {
	b._DescribeCurrentConn = mw(b._DescribeCurrentConn)
	return b
}

// DescribeCurrentConn implement DataSourceService's method DescribeCurrentConn
func (b *_DataSourceServiceDecorator) DescribeCurrentConn(arg0 context.Context, arg1 *DescribeCurrentConnsReq) (ret0 *DescribeCurrentConnsResp, ret1 error) {
	return b._DescribeCurrentConn(arg0, arg1)
}

// DescribeTableSpaceFunc alias for method DescribeTableSpace
type DescribeTableSpaceFunc func(arg0 context.Context, arg1 *DescribeTableSpaceReq) (ret0 *shared.DescribeTableSpaceResp, ret1 error)

// DecorateDescribeTableSpace decorate method DescribeTableSpace
func (b *_DataSourceServiceDecorator) DecorateDescribeTableSpace(mw func(DescribeTableSpaceFunc) DescribeTableSpaceFunc) DataSourceServiceDecorator {
	b._DescribeTableSpace = mw(b._DescribeTableSpace)
	return b
}

// DescribeTableSpace implement DataSourceService's method DescribeTableSpace
func (b *_DataSourceServiceDecorator) DescribeTableSpace(arg0 context.Context, arg1 *DescribeTableSpaceReq) (ret0 *shared.DescribeTableSpaceResp, ret1 error) {
	return b._DescribeTableSpace(arg0, arg1)
}

// DescribeTableSpaceAutoIncrFunc alias for method DescribeTableSpaceAutoIncr
type DescribeTableSpaceAutoIncrFunc func(arg0 context.Context, arg1 *DescribeTableSpaceReq) (ret0 *shared.DescribeTableSpaceAutoIncrResp, ret1 error)

// DecorateDescribeTableSpaceAutoIncr decorate method DescribeTableSpaceAutoIncr
func (b *_DataSourceServiceDecorator) DecorateDescribeTableSpaceAutoIncr(mw func(DescribeTableSpaceAutoIncrFunc) DescribeTableSpaceAutoIncrFunc) DataSourceServiceDecorator {
	b._DescribeTableSpaceAutoIncr = mw(b._DescribeTableSpaceAutoIncr)
	return b
}

// DescribeTableSpaceAutoIncr implement DataSourceService's method DescribeTableSpaceAutoIncr
func (b *_DataSourceServiceDecorator) DescribeTableSpaceAutoIncr(arg0 context.Context, arg1 *DescribeTableSpaceReq) (ret0 *shared.DescribeTableSpaceAutoIncrResp, ret1 error) {
	return b._DescribeTableSpaceAutoIncr(arg0, arg1)
}

// ConvertTableSpaceToModelFunc alias for method ConvertTableSpaceToModel
type ConvertTableSpaceToModelFunc func(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableSpaceResp) (ret0 *model.DescribeTableSpaceResp)

// DecorateConvertTableSpaceToModel decorate method ConvertTableSpaceToModel
func (b *_DataSourceServiceDecorator) DecorateConvertTableSpaceToModel(mw func(ConvertTableSpaceToModelFunc) ConvertTableSpaceToModelFunc) DataSourceServiceDecorator {
	b._ConvertTableSpaceToModel = mw(b._ConvertTableSpaceToModel)
	return b
}

// ConvertTableSpaceToModel implement DataSourceService's method ConvertTableSpaceToModel
func (b *_DataSourceServiceDecorator) ConvertTableSpaceToModel(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableSpaceResp) (ret0 *model.DescribeTableSpaceResp) {
	return b._ConvertTableSpaceToModel(arg0, arg1, arg2)
}

// DescribeTableColumnFunc alias for method DescribeTableColumn
type DescribeTableColumnFunc func(arg0 context.Context, arg1 *DescribeTableInfoReq) (ret0 *shared.DescribeTableColumnResp, ret1 error)

// DecorateDescribeTableColumn decorate method DescribeTableColumn
func (b *_DataSourceServiceDecorator) DecorateDescribeTableColumn(mw func(DescribeTableColumnFunc) DescribeTableColumnFunc) DataSourceServiceDecorator {
	b._DescribeTableColumn = mw(b._DescribeTableColumn)
	return b
}

// DescribeTableColumn implement DataSourceService's method DescribeTableColumn
func (b *_DataSourceServiceDecorator) DescribeTableColumn(arg0 context.Context, arg1 *DescribeTableInfoReq) (ret0 *shared.DescribeTableColumnResp, ret1 error) {
	return b._DescribeTableColumn(arg0, arg1)
}

// ConvertTableColumnToModelFunc alias for method ConvertTableColumnToModel
type ConvertTableColumnToModelFunc func(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableColumnResp) (ret0 *model.DescribeTableColumnResp)

// DecorateConvertTableColumnToModel decorate method ConvertTableColumnToModel
func (b *_DataSourceServiceDecorator) DecorateConvertTableColumnToModel(mw func(ConvertTableColumnToModelFunc) ConvertTableColumnToModelFunc) DataSourceServiceDecorator {
	b._ConvertTableColumnToModel = mw(b._ConvertTableColumnToModel)
	return b
}

// ConvertTableColumnToModel implement DataSourceService's method ConvertTableColumnToModel
func (b *_DataSourceServiceDecorator) ConvertTableColumnToModel(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableColumnResp) (ret0 *model.DescribeTableColumnResp) {
	return b._ConvertTableColumnToModel(arg0, arg1, arg2)
}

// DescribeTableIndexFunc alias for method DescribeTableIndex
type DescribeTableIndexFunc func(arg0 context.Context, arg1 *DescribeTableInfoReq) (ret0 *shared.DescribeTableIndexResp, ret1 error)

// DecorateDescribeTableIndex decorate method DescribeTableIndex
func (b *_DataSourceServiceDecorator) DecorateDescribeTableIndex(mw func(DescribeTableIndexFunc) DescribeTableIndexFunc) DataSourceServiceDecorator {
	b._DescribeTableIndex = mw(b._DescribeTableIndex)
	return b
}

// DescribeTableIndex implement DataSourceService's method DescribeTableIndex
func (b *_DataSourceServiceDecorator) DescribeTableIndex(arg0 context.Context, arg1 *DescribeTableInfoReq) (ret0 *shared.DescribeTableIndexResp, ret1 error) {
	return b._DescribeTableIndex(arg0, arg1)
}

// ConvertTableIndexToModelFunc alias for method ConvertTableIndexToModel
type ConvertTableIndexToModelFunc func(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableIndexResp) (ret0 *model.DescribeTableIndexResp)

// DecorateConvertTableIndexToModel decorate method ConvertTableIndexToModel
func (b *_DataSourceServiceDecorator) DecorateConvertTableIndexToModel(mw func(ConvertTableIndexToModelFunc) ConvertTableIndexToModelFunc) DataSourceServiceDecorator {
	b._ConvertTableIndexToModel = mw(b._ConvertTableIndexToModel)
	return b
}

// ConvertTableIndexToModel implement DataSourceService's method ConvertTableIndexToModel
func (b *_DataSourceServiceDecorator) ConvertTableIndexToModel(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableIndexResp) (ret0 *model.DescribeTableIndexResp) {
	return b._ConvertTableIndexToModel(arg0, arg1, arg2)
}

// FormatDescribeStorageCapacityRespFunc alias for method FormatDescribeStorageCapacityResp
type FormatDescribeStorageCapacityRespFunc func(arg0 shared.DataSourceType, arg1 *GetDiskSizeResp, arg2 float64) (ret0 *model.DescribeStorageCapacityResp)

// DecorateFormatDescribeStorageCapacityResp decorate method FormatDescribeStorageCapacityResp
func (b *_DataSourceServiceDecorator) DecorateFormatDescribeStorageCapacityResp(mw func(FormatDescribeStorageCapacityRespFunc) FormatDescribeStorageCapacityRespFunc) DataSourceServiceDecorator {
	b._FormatDescribeStorageCapacityResp = mw(b._FormatDescribeStorageCapacityResp)
	return b
}

// FormatDescribeStorageCapacityResp implement DataSourceService's method FormatDescribeStorageCapacityResp
func (b *_DataSourceServiceDecorator) FormatDescribeStorageCapacityResp(arg0 shared.DataSourceType, arg1 *GetDiskSizeResp, arg2 float64) (ret0 *model.DescribeStorageCapacityResp) {
	return b._FormatDescribeStorageCapacityResp(arg0, arg1, arg2)
}

// ExecuteCCLFunc alias for method ExecuteCCL
type ExecuteCCLFunc func(arg0 context.Context, arg1 *ExecuteCCLReq) (ret0 *ExecuteCCLResp, ret1 error)

// DecorateExecuteCCL decorate method ExecuteCCL
func (b *_DataSourceServiceDecorator) DecorateExecuteCCL(mw func(ExecuteCCLFunc) ExecuteCCLFunc) DataSourceServiceDecorator {
	b._ExecuteCCL = mw(b._ExecuteCCL)
	return b
}

// ExecuteCCL implement DataSourceService's method ExecuteCCL
func (b *_DataSourceServiceDecorator) ExecuteCCL(arg0 context.Context, arg1 *ExecuteCCLReq) (ret0 *ExecuteCCLResp, ret1 error) {
	return b._ExecuteCCL(arg0, arg1)
}

// CCLShowFunc alias for method CCLShow
type CCLShowFunc func(arg0 context.Context, arg1 *CCLShowReq) (ret0 *CCLShowResp, ret1 error)

// DecorateCCLShow decorate method CCLShow
func (b *_DataSourceServiceDecorator) DecorateCCLShow(mw func(CCLShowFunc) CCLShowFunc) DataSourceServiceDecorator {
	b._CCLShow = mw(b._CCLShow)
	return b
}

// CCLShow implement DataSourceService's method CCLShow
func (b *_DataSourceServiceDecorator) CCLShow(arg0 context.Context, arg1 *CCLShowReq) (ret0 *CCLShowResp, ret1 error) {
	return b._CCLShow(arg0, arg1)
}

// GetAvgSlowQueriesFunc alias for method GetAvgSlowQueries
type GetAvgSlowQueriesFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetAvgSlowQueries decorate method GetAvgSlowQueries
func (b *_DataSourceServiceDecorator) DecorateGetAvgSlowQueries(mw func(GetAvgSlowQueriesFunc) GetAvgSlowQueriesFunc) DataSourceServiceDecorator {
	b._GetAvgSlowQueries = mw(b._GetAvgSlowQueries)
	return b
}

// GetAvgSlowQueries implement DataSourceService's method GetAvgSlowQueries
func (b *_DataSourceServiceDecorator) GetAvgSlowQueries(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetAvgSlowQueries(arg0, arg1)
}

// GetDataPointCountSlowQueriesFunc alias for method GetDataPointCountSlowQueries
type GetDataPointCountSlowQueriesFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 int, ret1 error)

// DecorateGetDataPointCountSlowQueries decorate method GetDataPointCountSlowQueries
func (b *_DataSourceServiceDecorator) DecorateGetDataPointCountSlowQueries(mw func(GetDataPointCountSlowQueriesFunc) GetDataPointCountSlowQueriesFunc) DataSourceServiceDecorator {
	b._GetDataPointCountSlowQueries = mw(b._GetDataPointCountSlowQueries)
	return b
}

// GetDataPointCountSlowQueries implement DataSourceService's method GetDataPointCountSlowQueries
func (b *_DataSourceServiceDecorator) GetDataPointCountSlowQueries(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 int, ret1 error) {
	return b._GetDataPointCountSlowQueries(arg0, arg1)
}

// GetCpuMetricDetailFunc alias for method GetCpuMetricDetail
type GetCpuMetricDetailFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error)

// DecorateGetCpuMetricDetail decorate method GetCpuMetricDetail
func (b *_DataSourceServiceDecorator) DecorateGetCpuMetricDetail(mw func(GetCpuMetricDetailFunc) GetCpuMetricDetailFunc) DataSourceServiceDecorator {
	b._GetCpuMetricDetail = mw(b._GetCpuMetricDetail)
	return b
}

// GetCpuMetricDetail implement DataSourceService's method GetCpuMetricDetail
func (b *_DataSourceServiceDecorator) GetCpuMetricDetail(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error) {
	return b._GetCpuMetricDetail(arg0, arg1)
}

// GetAvgCpuUsageFunc alias for method GetAvgCpuUsage
type GetAvgCpuUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetAvgCpuUsage decorate method GetAvgCpuUsage
func (b *_DataSourceServiceDecorator) DecorateGetAvgCpuUsage(mw func(GetAvgCpuUsageFunc) GetAvgCpuUsageFunc) DataSourceServiceDecorator {
	b._GetAvgCpuUsage = mw(b._GetAvgCpuUsage)
	return b
}

// GetAvgCpuUsage implement DataSourceService's method GetAvgCpuUsage
func (b *_DataSourceServiceDecorator) GetAvgCpuUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetAvgCpuUsage(arg0, arg1)
}

// GetMinCpuUsageFunc alias for method GetMinCpuUsage
type GetMinCpuUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMinCpuUsage decorate method GetMinCpuUsage
func (b *_DataSourceServiceDecorator) DecorateGetMinCpuUsage(mw func(GetMinCpuUsageFunc) GetMinCpuUsageFunc) DataSourceServiceDecorator {
	b._GetMinCpuUsage = mw(b._GetMinCpuUsage)
	return b
}

// GetMinCpuUsage implement DataSourceService's method GetMinCpuUsage
func (b *_DataSourceServiceDecorator) GetMinCpuUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMinCpuUsage(arg0, arg1)
}

// GetMaxCpuUsageFunc alias for method GetMaxCpuUsage
type GetMaxCpuUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMaxCpuUsage decorate method GetMaxCpuUsage
func (b *_DataSourceServiceDecorator) DecorateGetMaxCpuUsage(mw func(GetMaxCpuUsageFunc) GetMaxCpuUsageFunc) DataSourceServiceDecorator {
	b._GetMaxCpuUsage = mw(b._GetMaxCpuUsage)
	return b
}

// GetMaxCpuUsage implement DataSourceService's method GetMaxCpuUsage
func (b *_DataSourceServiceDecorator) GetMaxCpuUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMaxCpuUsage(arg0, arg1)
}

// GetCpuUsageMetricDetailFunc alias for method GetCpuUsageMetricDetail
type GetCpuUsageMetricDetailFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetCpuUsageMetricDetail decorate method GetCpuUsageMetricDetail
func (b *_DataSourceServiceDecorator) DecorateGetCpuUsageMetricDetail(mw func(GetCpuUsageMetricDetailFunc) GetCpuUsageMetricDetailFunc) DataSourceServiceDecorator {
	b._GetCpuUsageMetricDetail = mw(b._GetCpuUsageMetricDetail)
	return b
}

// GetCpuUsageMetricDetail implement DataSourceService's method GetCpuUsageMetricDetail
func (b *_DataSourceServiceDecorator) GetCpuUsageMetricDetail(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetCpuUsageMetricDetail(arg0, arg1)
}

// DiagRootCauseALLFunc alias for method DiagRootCauseALL
type DiagRootCauseALLFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagRootCauseResp, ret1 error)

// DecorateDiagRootCauseALL decorate method DiagRootCauseALL
func (b *_DataSourceServiceDecorator) DecorateDiagRootCauseALL(mw func(DiagRootCauseALLFunc) DiagRootCauseALLFunc) DataSourceServiceDecorator {
	b._DiagRootCauseALL = mw(b._DiagRootCauseALL)
	return b
}

// DiagRootCauseALL implement DataSourceService's method DiagRootCauseALL
func (b *_DataSourceServiceDecorator) DiagRootCauseALL(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagRootCauseResp, ret1 error) {
	return b._DiagRootCauseALL(arg0, arg1)
}

// DiagRootCauseYoYQoQFunc alias for method DiagRootCauseYoYQoQ
type DiagRootCauseYoYQoQFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 map[string]float64, ret1 error)

// DecorateDiagRootCauseYoYQoQ decorate method DiagRootCauseYoYQoQ
func (b *_DataSourceServiceDecorator) DecorateDiagRootCauseYoYQoQ(mw func(DiagRootCauseYoYQoQFunc) DiagRootCauseYoYQoQFunc) DataSourceServiceDecorator {
	b._DiagRootCauseYoYQoQ = mw(b._DiagRootCauseYoYQoQ)
	return b
}

// DiagRootCauseYoYQoQ implement DataSourceService's method DiagRootCauseYoYQoQ
func (b *_DataSourceServiceDecorator) DiagRootCauseYoYQoQ(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 map[string]float64, ret1 error) {
	return b._DiagRootCauseYoYQoQ(arg0, arg1)
}

// DiagRootCauseDiskMetricsFunc alias for method DiagRootCauseDiskMetrics
type DiagRootCauseDiskMetricsFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 map[string]string, ret1 error)

// DecorateDiagRootCauseDiskMetrics decorate method DiagRootCauseDiskMetrics
func (b *_DataSourceServiceDecorator) DecorateDiagRootCauseDiskMetrics(mw func(DiagRootCauseDiskMetricsFunc) DiagRootCauseDiskMetricsFunc) DataSourceServiceDecorator {
	b._DiagRootCauseDiskMetrics = mw(b._DiagRootCauseDiskMetrics)
	return b
}

// DiagRootCauseDiskMetrics implement DataSourceService's method DiagRootCauseDiskMetrics
func (b *_DataSourceServiceDecorator) DiagRootCauseDiskMetrics(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 map[string]string, ret1 error) {
	return b._DiagRootCauseDiskMetrics(arg0, arg1)
}

// GetMemMetricDetailFunc alias for method GetMemMetricDetail
type GetMemMetricDetailFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error)

// DecorateGetMemMetricDetail decorate method GetMemMetricDetail
func (b *_DataSourceServiceDecorator) DecorateGetMemMetricDetail(mw func(GetMemMetricDetailFunc) GetMemMetricDetailFunc) DataSourceServiceDecorator {
	b._GetMemMetricDetail = mw(b._GetMemMetricDetail)
	return b
}

// GetMemMetricDetail implement DataSourceService's method GetMemMetricDetail
func (b *_DataSourceServiceDecorator) GetMemMetricDetail(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error) {
	return b._GetMemMetricDetail(arg0, arg1)
}

// GetAvgMemUsageFunc alias for method GetAvgMemUsage
type GetAvgMemUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetAvgMemUsage decorate method GetAvgMemUsage
func (b *_DataSourceServiceDecorator) DecorateGetAvgMemUsage(mw func(GetAvgMemUsageFunc) GetAvgMemUsageFunc) DataSourceServiceDecorator {
	b._GetAvgMemUsage = mw(b._GetAvgMemUsage)
	return b
}

// GetAvgMemUsage implement DataSourceService's method GetAvgMemUsage
func (b *_DataSourceServiceDecorator) GetAvgMemUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetAvgMemUsage(arg0, arg1)
}

// GetMinMemUsageFunc alias for method GetMinMemUsage
type GetMinMemUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMinMemUsage decorate method GetMinMemUsage
func (b *_DataSourceServiceDecorator) DecorateGetMinMemUsage(mw func(GetMinMemUsageFunc) GetMinMemUsageFunc) DataSourceServiceDecorator {
	b._GetMinMemUsage = mw(b._GetMinMemUsage)
	return b
}

// GetMinMemUsage implement DataSourceService's method GetMinMemUsage
func (b *_DataSourceServiceDecorator) GetMinMemUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMinMemUsage(arg0, arg1)
}

// GetMaxMemUsageFunc alias for method GetMaxMemUsage
type GetMaxMemUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMaxMemUsage decorate method GetMaxMemUsage
func (b *_DataSourceServiceDecorator) DecorateGetMaxMemUsage(mw func(GetMaxMemUsageFunc) GetMaxMemUsageFunc) DataSourceServiceDecorator {
	b._GetMaxMemUsage = mw(b._GetMaxMemUsage)
	return b
}

// GetMaxMemUsage implement DataSourceService's method GetMaxMemUsage
func (b *_DataSourceServiceDecorator) GetMaxMemUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMaxMemUsage(arg0, arg1)
}

// GetMemUsageMetricDetailFunc alias for method GetMemUsageMetricDetail
type GetMemUsageMetricDetailFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetMemUsageMetricDetail decorate method GetMemUsageMetricDetail
func (b *_DataSourceServiceDecorator) DecorateGetMemUsageMetricDetail(mw func(GetMemUsageMetricDetailFunc) GetMemUsageMetricDetailFunc) DataSourceServiceDecorator {
	b._GetMemUsageMetricDetail = mw(b._GetMemUsageMetricDetail)
	return b
}

// GetMemUsageMetricDetail implement DataSourceService's method GetMemUsageMetricDetail
func (b *_DataSourceServiceDecorator) GetMemUsageMetricDetail(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetMemUsageMetricDetail(arg0, arg1)
}

// GetAvgDiskUsageFunc alias for method GetAvgDiskUsage
type GetAvgDiskUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetAvgDiskUsage decorate method GetAvgDiskUsage
func (b *_DataSourceServiceDecorator) DecorateGetAvgDiskUsage(mw func(GetAvgDiskUsageFunc) GetAvgDiskUsageFunc) DataSourceServiceDecorator {
	b._GetAvgDiskUsage = mw(b._GetAvgDiskUsage)
	return b
}

// GetAvgDiskUsage implement DataSourceService's method GetAvgDiskUsage
func (b *_DataSourceServiceDecorator) GetAvgDiskUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetAvgDiskUsage(arg0, arg1)
}

// GetMaxDiskUsageFunc alias for method GetMaxDiskUsage
type GetMaxDiskUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMaxDiskUsage decorate method GetMaxDiskUsage
func (b *_DataSourceServiceDecorator) DecorateGetMaxDiskUsage(mw func(GetMaxDiskUsageFunc) GetMaxDiskUsageFunc) DataSourceServiceDecorator {
	b._GetMaxDiskUsage = mw(b._GetMaxDiskUsage)
	return b
}

// GetMaxDiskUsage implement DataSourceService's method GetMaxDiskUsage
func (b *_DataSourceServiceDecorator) GetMaxDiskUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMaxDiskUsage(arg0, arg1)
}

// GetMinDiskUsageFunc alias for method GetMinDiskUsage
type GetMinDiskUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMinDiskUsage decorate method GetMinDiskUsage
func (b *_DataSourceServiceDecorator) DecorateGetMinDiskUsage(mw func(GetMinDiskUsageFunc) GetMinDiskUsageFunc) DataSourceServiceDecorator {
	b._GetMinDiskUsage = mw(b._GetMinDiskUsage)
	return b
}

// GetMinDiskUsage implement DataSourceService's method GetMinDiskUsage
func (b *_DataSourceServiceDecorator) GetMinDiskUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMinDiskUsage(arg0, arg1)
}

// GetDiskAvailableDaysFunc alias for method GetDiskAvailableDays
type GetDiskAvailableDaysFunc func(arg0 context.Context, arg1 *GetDiskAvailableDaysReq) (ret0 *GetDiskAvailableDaysResp, ret1 error)

// DecorateGetDiskAvailableDays decorate method GetDiskAvailableDays
func (b *_DataSourceServiceDecorator) DecorateGetDiskAvailableDays(mw func(GetDiskAvailableDaysFunc) GetDiskAvailableDaysFunc) DataSourceServiceDecorator {
	b._GetDiskAvailableDays = mw(b._GetDiskAvailableDays)
	return b
}

// GetDiskAvailableDays implement DataSourceService's method GetDiskAvailableDays
func (b *_DataSourceServiceDecorator) GetDiskAvailableDays(arg0 context.Context, arg1 *GetDiskAvailableDaysReq) (ret0 *GetDiskAvailableDaysResp, ret1 error) {
	return b._GetDiskAvailableDays(arg0, arg1)
}

// GetDiskFutureSizeFunc alias for method GetDiskFutureSize
type GetDiskFutureSizeFunc func(arg0 context.Context, arg1 *GetDiskFutureSizeReq) (ret0 *GetDiskFutureSizeResp, ret1 error)

// DecorateGetDiskFutureSize decorate method GetDiskFutureSize
func (b *_DataSourceServiceDecorator) DecorateGetDiskFutureSize(mw func(GetDiskFutureSizeFunc) GetDiskFutureSizeFunc) DataSourceServiceDecorator {
	b._GetDiskFutureSize = mw(b._GetDiskFutureSize)
	return b
}

// GetDiskFutureSize implement DataSourceService's method GetDiskFutureSize
func (b *_DataSourceServiceDecorator) GetDiskFutureSize(arg0 context.Context, arg1 *GetDiskFutureSizeReq) (ret0 *GetDiskFutureSizeResp, ret1 error) {
	return b._GetDiskFutureSize(arg0, arg1)
}

// GetDiskMetricDetailFunc alias for method GetDiskMetricDetail
type GetDiskMetricDetailFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error)

// DecorateGetDiskMetricDetail decorate method GetDiskMetricDetail
func (b *_DataSourceServiceDecorator) DecorateGetDiskMetricDetail(mw func(GetDiskMetricDetailFunc) GetDiskMetricDetailFunc) DataSourceServiceDecorator {
	b._GetDiskMetricDetail = mw(b._GetDiskMetricDetail)
	return b
}

// GetDiskMetricDetail implement DataSourceService's method GetDiskMetricDetail
func (b *_DataSourceServiceDecorator) GetDiskMetricDetail(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error) {
	return b._GetDiskMetricDetail(arg0, arg1)
}

// GetDiskUsageMetricDetailFunc alias for method GetDiskUsageMetricDetail
type GetDiskUsageMetricDetailFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetDiskUsageMetricDetail decorate method GetDiskUsageMetricDetail
func (b *_DataSourceServiceDecorator) DecorateGetDiskUsageMetricDetail(mw func(GetDiskUsageMetricDetailFunc) GetDiskUsageMetricDetailFunc) DataSourceServiceDecorator {
	b._GetDiskUsageMetricDetail = mw(b._GetDiskUsageMetricDetail)
	return b
}

// GetDiskUsageMetricDetail implement DataSourceService's method GetDiskUsageMetricDetail
func (b *_DataSourceServiceDecorator) GetDiskUsageMetricDetail(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetDiskUsageMetricDetail(arg0, arg1)
}

// GetAvgQpsUsageFunc alias for method GetAvgQpsUsage
type GetAvgQpsUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetAvgQpsUsage decorate method GetAvgQpsUsage
func (b *_DataSourceServiceDecorator) DecorateGetAvgQpsUsage(mw func(GetAvgQpsUsageFunc) GetAvgQpsUsageFunc) DataSourceServiceDecorator {
	b._GetAvgQpsUsage = mw(b._GetAvgQpsUsage)
	return b
}

// GetAvgQpsUsage implement DataSourceService's method GetAvgQpsUsage
func (b *_DataSourceServiceDecorator) GetAvgQpsUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetAvgQpsUsage(arg0, arg1)
}

// GetMaxQpsUsageFunc alias for method GetMaxQpsUsage
type GetMaxQpsUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMaxQpsUsage decorate method GetMaxQpsUsage
func (b *_DataSourceServiceDecorator) DecorateGetMaxQpsUsage(mw func(GetMaxQpsUsageFunc) GetMaxQpsUsageFunc) DataSourceServiceDecorator {
	b._GetMaxQpsUsage = mw(b._GetMaxQpsUsage)
	return b
}

// GetMaxQpsUsage implement DataSourceService's method GetMaxQpsUsage
func (b *_DataSourceServiceDecorator) GetMaxQpsUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMaxQpsUsage(arg0, arg1)
}

// GetMinQpsUsageFunc alias for method GetMinQpsUsage
type GetMinQpsUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMinQpsUsage decorate method GetMinQpsUsage
func (b *_DataSourceServiceDecorator) DecorateGetMinQpsUsage(mw func(GetMinQpsUsageFunc) GetMinQpsUsageFunc) DataSourceServiceDecorator {
	b._GetMinQpsUsage = mw(b._GetMinQpsUsage)
	return b
}

// GetMinQpsUsage implement DataSourceService's method GetMinQpsUsage
func (b *_DataSourceServiceDecorator) GetMinQpsUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMinQpsUsage(arg0, arg1)
}

// GetQpsUsageFunc alias for method GetQpsUsage
type GetQpsUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetQpsUsage decorate method GetQpsUsage
func (b *_DataSourceServiceDecorator) DecorateGetQpsUsage(mw func(GetQpsUsageFunc) GetQpsUsageFunc) DataSourceServiceDecorator {
	b._GetQpsUsage = mw(b._GetQpsUsage)
	return b
}

// GetQpsUsage implement DataSourceService's method GetQpsUsage
func (b *_DataSourceServiceDecorator) GetQpsUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetQpsUsage(arg0, arg1)
}

// GetAvgTpsUsageFunc alias for method GetAvgTpsUsage
type GetAvgTpsUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetAvgTpsUsage decorate method GetAvgTpsUsage
func (b *_DataSourceServiceDecorator) DecorateGetAvgTpsUsage(mw func(GetAvgTpsUsageFunc) GetAvgTpsUsageFunc) DataSourceServiceDecorator {
	b._GetAvgTpsUsage = mw(b._GetAvgTpsUsage)
	return b
}

// GetAvgTpsUsage implement DataSourceService's method GetAvgTpsUsage
func (b *_DataSourceServiceDecorator) GetAvgTpsUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetAvgTpsUsage(arg0, arg1)
}

// GetMaxTpsUsageFunc alias for method GetMaxTpsUsage
type GetMaxTpsUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMaxTpsUsage decorate method GetMaxTpsUsage
func (b *_DataSourceServiceDecorator) DecorateGetMaxTpsUsage(mw func(GetMaxTpsUsageFunc) GetMaxTpsUsageFunc) DataSourceServiceDecorator {
	b._GetMaxTpsUsage = mw(b._GetMaxTpsUsage)
	return b
}

// GetMaxTpsUsage implement DataSourceService's method GetMaxTpsUsage
func (b *_DataSourceServiceDecorator) GetMaxTpsUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMaxTpsUsage(arg0, arg1)
}

// GetMinTpsUsageFunc alias for method GetMinTpsUsage
type GetMinTpsUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMinTpsUsage decorate method GetMinTpsUsage
func (b *_DataSourceServiceDecorator) DecorateGetMinTpsUsage(mw func(GetMinTpsUsageFunc) GetMinTpsUsageFunc) DataSourceServiceDecorator {
	b._GetMinTpsUsage = mw(b._GetMinTpsUsage)
	return b
}

// GetMinTpsUsage implement DataSourceService's method GetMinTpsUsage
func (b *_DataSourceServiceDecorator) GetMinTpsUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMinTpsUsage(arg0, arg1)
}

// GetTpsUsageFunc alias for method GetTpsUsage
type GetTpsUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetTpsUsage decorate method GetTpsUsage
func (b *_DataSourceServiceDecorator) DecorateGetTpsUsage(mw func(GetTpsUsageFunc) GetTpsUsageFunc) DataSourceServiceDecorator {
	b._GetTpsUsage = mw(b._GetTpsUsage)
	return b
}

// GetTpsUsage implement DataSourceService's method GetTpsUsage
func (b *_DataSourceServiceDecorator) GetTpsUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetTpsUsage(arg0, arg1)
}

// GetAvgConnectionUsageFunc alias for method GetAvgConnectionUsage
type GetAvgConnectionUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetAvgConnectionUsage decorate method GetAvgConnectionUsage
func (b *_DataSourceServiceDecorator) DecorateGetAvgConnectionUsage(mw func(GetAvgConnectionUsageFunc) GetAvgConnectionUsageFunc) DataSourceServiceDecorator {
	b._GetAvgConnectionUsage = mw(b._GetAvgConnectionUsage)
	return b
}

// GetAvgConnectionUsage implement DataSourceService's method GetAvgConnectionUsage
func (b *_DataSourceServiceDecorator) GetAvgConnectionUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetAvgConnectionUsage(arg0, arg1)
}

// GetMaxConnectionUsageFunc alias for method GetMaxConnectionUsage
type GetMaxConnectionUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMaxConnectionUsage decorate method GetMaxConnectionUsage
func (b *_DataSourceServiceDecorator) DecorateGetMaxConnectionUsage(mw func(GetMaxConnectionUsageFunc) GetMaxConnectionUsageFunc) DataSourceServiceDecorator {
	b._GetMaxConnectionUsage = mw(b._GetMaxConnectionUsage)
	return b
}

// GetMaxConnectionUsage implement DataSourceService's method GetMaxConnectionUsage
func (b *_DataSourceServiceDecorator) GetMaxConnectionUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMaxConnectionUsage(arg0, arg1)
}

// GetMinConnectionUsageFunc alias for method GetMinConnectionUsage
type GetMinConnectionUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMinConnectionUsage decorate method GetMinConnectionUsage
func (b *_DataSourceServiceDecorator) DecorateGetMinConnectionUsage(mw func(GetMinConnectionUsageFunc) GetMinConnectionUsageFunc) DataSourceServiceDecorator {
	b._GetMinConnectionUsage = mw(b._GetMinConnectionUsage)
	return b
}

// GetMinConnectionUsage implement DataSourceService's method GetMinConnectionUsage
func (b *_DataSourceServiceDecorator) GetMinConnectionUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMinConnectionUsage(arg0, arg1)
}

// GetConnectedRatioMetricDetailFunc alias for method GetConnectedRatioMetricDetail
type GetConnectedRatioMetricDetailFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetConnectedRatioMetricDetail decorate method GetConnectedRatioMetricDetail
func (b *_DataSourceServiceDecorator) DecorateGetConnectedRatioMetricDetail(mw func(GetConnectedRatioMetricDetailFunc) GetConnectedRatioMetricDetailFunc) DataSourceServiceDecorator {
	b._GetConnectedRatioMetricDetail = mw(b._GetConnectedRatioMetricDetail)
	return b
}

// GetConnectedRatioMetricDetail implement DataSourceService's method GetConnectedRatioMetricDetail
func (b *_DataSourceServiceDecorator) GetConnectedRatioMetricDetail(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetConnectedRatioMetricDetail(arg0, arg1)
}

// GetSessionMetricDetailFunc alias for method GetSessionMetricDetail
type GetSessionMetricDetailFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error)

// DecorateGetSessionMetricDetail decorate method GetSessionMetricDetail
func (b *_DataSourceServiceDecorator) DecorateGetSessionMetricDetail(mw func(GetSessionMetricDetailFunc) GetSessionMetricDetailFunc) DataSourceServiceDecorator {
	b._GetSessionMetricDetail = mw(b._GetSessionMetricDetail)
	return b
}

// GetSessionMetricDetail implement DataSourceService's method GetSessionMetricDetail
func (b *_DataSourceServiceDecorator) GetSessionMetricDetail(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.DescribeDiagItemDetailResp, ret1 error) {
	return b._GetSessionMetricDetail(arg0, arg1)
}

// GetAvgSessionUsageFunc alias for method GetAvgSessionUsage
type GetAvgSessionUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetAvgSessionUsage decorate method GetAvgSessionUsage
func (b *_DataSourceServiceDecorator) DecorateGetAvgSessionUsage(mw func(GetAvgSessionUsageFunc) GetAvgSessionUsageFunc) DataSourceServiceDecorator {
	b._GetAvgSessionUsage = mw(b._GetAvgSessionUsage)
	return b
}

// GetAvgSessionUsage implement DataSourceService's method GetAvgSessionUsage
func (b *_DataSourceServiceDecorator) GetAvgSessionUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetAvgSessionUsage(arg0, arg1)
}

// GetMaxSessionUsageFunc alias for method GetMaxSessionUsage
type GetMaxSessionUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMaxSessionUsage decorate method GetMaxSessionUsage
func (b *_DataSourceServiceDecorator) DecorateGetMaxSessionUsage(mw func(GetMaxSessionUsageFunc) GetMaxSessionUsageFunc) DataSourceServiceDecorator {
	b._GetMaxSessionUsage = mw(b._GetMaxSessionUsage)
	return b
}

// GetMaxSessionUsage implement DataSourceService's method GetMaxSessionUsage
func (b *_DataSourceServiceDecorator) GetMaxSessionUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMaxSessionUsage(arg0, arg1)
}

// GetMinSessionUsageFunc alias for method GetMinSessionUsage
type GetMinSessionUsageFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error)

// DecorateGetMinSessionUsage decorate method GetMinSessionUsage
func (b *_DataSourceServiceDecorator) DecorateGetMinSessionUsage(mw func(GetMinSessionUsageFunc) GetMinSessionUsageFunc) DataSourceServiceDecorator {
	b._GetMinSessionUsage = mw(b._GetMinSessionUsage)
	return b
}

// GetMinSessionUsage implement DataSourceService's method GetMinSessionUsage
func (b *_DataSourceServiceDecorator) GetMinSessionUsage(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *GetMetricUsageResp, ret1 error) {
	return b._GetMinSessionUsage(arg0, arg1)
}

// GetLatestDiskUsageFunc alias for method GetLatestDiskUsage
type GetLatestDiskUsageFunc func(arg0 context.Context, arg1 *GetLatestDiskUsageReq) (ret0 float64, ret1 error)

// DecorateGetLatestDiskUsage decorate method GetLatestDiskUsage
func (b *_DataSourceServiceDecorator) DecorateGetLatestDiskUsage(mw func(GetLatestDiskUsageFunc) GetLatestDiskUsageFunc) DataSourceServiceDecorator {
	b._GetLatestDiskUsage = mw(b._GetLatestDiskUsage)
	return b
}

// GetLatestDiskUsage implement DataSourceService's method GetLatestDiskUsage
func (b *_DataSourceServiceDecorator) GetLatestDiskUsage(arg0 context.Context, arg1 *GetLatestDiskUsageReq) (ret0 float64, ret1 error) {
	return b._GetLatestDiskUsage(arg0, arg1)
}

// GetInspectionCpuMetricFunc alias for method GetInspectionCpuMetric
type GetInspectionCpuMetricFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetInspectionCpuMetric decorate method GetInspectionCpuMetric
func (b *_DataSourceServiceDecorator) DecorateGetInspectionCpuMetric(mw func(GetInspectionCpuMetricFunc) GetInspectionCpuMetricFunc) DataSourceServiceDecorator {
	b._GetInspectionCpuMetric = mw(b._GetInspectionCpuMetric)
	return b
}

// GetInspectionCpuMetric implement DataSourceService's method GetInspectionCpuMetric
func (b *_DataSourceServiceDecorator) GetInspectionCpuMetric(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetInspectionCpuMetric(arg0, arg1)
}

// GetInspectionMemMetricFunc alias for method GetInspectionMemMetric
type GetInspectionMemMetricFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetInspectionMemMetric decorate method GetInspectionMemMetric
func (b *_DataSourceServiceDecorator) DecorateGetInspectionMemMetric(mw func(GetInspectionMemMetricFunc) GetInspectionMemMetricFunc) DataSourceServiceDecorator {
	b._GetInspectionMemMetric = mw(b._GetInspectionMemMetric)
	return b
}

// GetInspectionMemMetric implement DataSourceService's method GetInspectionMemMetric
func (b *_DataSourceServiceDecorator) GetInspectionMemMetric(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetInspectionMemMetric(arg0, arg1)
}

// GetInspectionDiskMetricFunc alias for method GetInspectionDiskMetric
type GetInspectionDiskMetricFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetInspectionDiskMetric decorate method GetInspectionDiskMetric
func (b *_DataSourceServiceDecorator) DecorateGetInspectionDiskMetric(mw func(GetInspectionDiskMetricFunc) GetInspectionDiskMetricFunc) DataSourceServiceDecorator {
	b._GetInspectionDiskMetric = mw(b._GetInspectionDiskMetric)
	return b
}

// GetInspectionDiskMetric implement DataSourceService's method GetInspectionDiskMetric
func (b *_DataSourceServiceDecorator) GetInspectionDiskMetric(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetInspectionDiskMetric(arg0, arg1)
}

// GetInspectionQpsMetricFunc alias for method GetInspectionQpsMetric
type GetInspectionQpsMetricFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetInspectionQpsMetric decorate method GetInspectionQpsMetric
func (b *_DataSourceServiceDecorator) DecorateGetInspectionQpsMetric(mw func(GetInspectionQpsMetricFunc) GetInspectionQpsMetricFunc) DataSourceServiceDecorator {
	b._GetInspectionQpsMetric = mw(b._GetInspectionQpsMetric)
	return b
}

// GetInspectionQpsMetric implement DataSourceService's method GetInspectionQpsMetric
func (b *_DataSourceServiceDecorator) GetInspectionQpsMetric(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetInspectionQpsMetric(arg0, arg1)
}

// GetInspectionTpsMetricFunc alias for method GetInspectionTpsMetric
type GetInspectionTpsMetricFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetInspectionTpsMetric decorate method GetInspectionTpsMetric
func (b *_DataSourceServiceDecorator) DecorateGetInspectionTpsMetric(mw func(GetInspectionTpsMetricFunc) GetInspectionTpsMetricFunc) DataSourceServiceDecorator {
	b._GetInspectionTpsMetric = mw(b._GetInspectionTpsMetric)
	return b
}

// GetInspectionTpsMetric implement DataSourceService's method GetInspectionTpsMetric
func (b *_DataSourceServiceDecorator) GetInspectionTpsMetric(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetInspectionTpsMetric(arg0, arg1)
}

// GetInspectionConnectedMetricFunc alias for method GetInspectionConnectedMetric
type GetInspectionConnectedMetricFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetInspectionConnectedMetric decorate method GetInspectionConnectedMetric
func (b *_DataSourceServiceDecorator) DecorateGetInspectionConnectedMetric(mw func(GetInspectionConnectedMetricFunc) GetInspectionConnectedMetricFunc) DataSourceServiceDecorator {
	b._GetInspectionConnectedMetric = mw(b._GetInspectionConnectedMetric)
	return b
}

// GetInspectionConnectedMetric implement DataSourceService's method GetInspectionConnectedMetric
func (b *_DataSourceServiceDecorator) GetInspectionConnectedMetric(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetInspectionConnectedMetric(arg0, arg1)
}

// GetInspectionConnRatioMetricFunc alias for method GetInspectionConnRatioMetric
type GetInspectionConnRatioMetricFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetInspectionConnRatioMetric decorate method GetInspectionConnRatioMetric
func (b *_DataSourceServiceDecorator) DecorateGetInspectionConnRatioMetric(mw func(GetInspectionConnRatioMetricFunc) GetInspectionConnRatioMetricFunc) DataSourceServiceDecorator {
	b._GetInspectionConnRatioMetric = mw(b._GetInspectionConnRatioMetric)
	return b
}

// GetInspectionConnRatioMetric implement DataSourceService's method GetInspectionConnRatioMetric
func (b *_DataSourceServiceDecorator) GetInspectionConnRatioMetric(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetInspectionConnRatioMetric(arg0, arg1)
}

// GetInspectionBpHitMetricFunc alias for method GetInspectionBpHitMetric
type GetInspectionBpHitMetricFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetInspectionBpHitMetric decorate method GetInspectionBpHitMetric
func (b *_DataSourceServiceDecorator) DecorateGetInspectionBpHitMetric(mw func(GetInspectionBpHitMetricFunc) GetInspectionBpHitMetricFunc) DataSourceServiceDecorator {
	b._GetInspectionBpHitMetric = mw(b._GetInspectionBpHitMetric)
	return b
}

// GetInspectionBpHitMetric implement DataSourceService's method GetInspectionBpHitMetric
func (b *_DataSourceServiceDecorator) GetInspectionBpHitMetric(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetInspectionBpHitMetric(arg0, arg1)
}

// GetInspectionOutputMetricFunc alias for method GetInspectionOutputMetric
type GetInspectionOutputMetricFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetInspectionOutputMetric decorate method GetInspectionOutputMetric
func (b *_DataSourceServiceDecorator) DecorateGetInspectionOutputMetric(mw func(GetInspectionOutputMetricFunc) GetInspectionOutputMetricFunc) DataSourceServiceDecorator {
	b._GetInspectionOutputMetric = mw(b._GetInspectionOutputMetric)
	return b
}

// GetInspectionOutputMetric implement DataSourceService's method GetInspectionOutputMetric
func (b *_DataSourceServiceDecorator) GetInspectionOutputMetric(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetInspectionOutputMetric(arg0, arg1)
}

// GetInspectionInputMetricFunc alias for method GetInspectionInputMetric
type GetInspectionInputMetricFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error)

// DecorateGetInspectionInputMetric decorate method GetInspectionInputMetric
func (b *_DataSourceServiceDecorator) DecorateGetInspectionInputMetric(mw func(GetInspectionInputMetricFunc) GetInspectionInputMetricFunc) DataSourceServiceDecorator {
	b._GetInspectionInputMetric = mw(b._GetInspectionInputMetric)
	return b
}

// GetInspectionInputMetric implement DataSourceService's method GetInspectionInputMetric
func (b *_DataSourceServiceDecorator) GetInspectionInputMetric(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 *model.ItemDataResult_, ret1 error) {
	return b._GetInspectionInputMetric(arg0, arg1)
}

// HealthSummaryFunc alias for method HealthSummary
type HealthSummaryFunc func(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 []*model.Resource, ret1 error)

// DecorateHealthSummary decorate method HealthSummary
func (b *_DataSourceServiceDecorator) DecorateHealthSummary(mw func(HealthSummaryFunc) HealthSummaryFunc) DataSourceServiceDecorator {
	b._HealthSummary = mw(b._HealthSummary)
	return b
}

// HealthSummary implement DataSourceService's method HealthSummary
func (b *_DataSourceServiceDecorator) HealthSummary(arg0 context.Context, arg1 *GetMetricUsageReq) (ret0 []*model.Resource, ret1 error) {
	return b._HealthSummary(arg0, arg1)
}

// GetMonitorByMetricFunc alias for method GetMonitorByMetric
type GetMonitorByMetricFunc func(arg0 context.Context, arg1 *GetMonitorByMetricReq) (ret0 []*model.MetricResource, ret1 error)

// DecorateGetMonitorByMetric decorate method GetMonitorByMetric
func (b *_DataSourceServiceDecorator) DecorateGetMonitorByMetric(mw func(GetMonitorByMetricFunc) GetMonitorByMetricFunc) DataSourceServiceDecorator {
	b._GetMonitorByMetric = mw(b._GetMonitorByMetric)
	return b
}

// GetMonitorByMetric implement DataSourceService's method GetMonitorByMetric
func (b *_DataSourceServiceDecorator) GetMonitorByMetric(arg0 context.Context, arg1 *GetMonitorByMetricReq) (ret0 []*model.MetricResource, ret1 error) {
	return b._GetMonitorByMetric(arg0, arg1)
}

// ListCollectionsFunc alias for method ListCollections
type ListCollectionsFunc func(arg0 context.Context, arg1 *ListCollectionsReq) (ret0 *ListCollectionsResp, ret1 error)

// DecorateListCollections decorate method ListCollections
func (b *_DataSourceServiceDecorator) DecorateListCollections(mw func(ListCollectionsFunc) ListCollectionsFunc) DataSourceServiceDecorator {
	b._ListCollections = mw(b._ListCollections)
	return b
}

// ListCollections implement DataSourceService's method ListCollections
func (b *_DataSourceServiceDecorator) ListCollections(arg0 context.Context, arg1 *ListCollectionsReq) (ret0 *ListCollectionsResp, ret1 error) {
	return b._ListCollections(arg0, arg1)
}

// ListIndexsFunc alias for method ListIndexs
type ListIndexsFunc func(arg0 context.Context, arg1 *ListIndexesReq) (ret0 *ListIndexesResp, ret1 error)

// DecorateListIndexs decorate method ListIndexs
func (b *_DataSourceServiceDecorator) DecorateListIndexs(mw func(ListIndexsFunc) ListIndexsFunc) DataSourceServiceDecorator {
	b._ListIndexs = mw(b._ListIndexs)
	return b
}

// ListIndexs implement DataSourceService's method ListIndexs
func (b *_DataSourceServiceDecorator) ListIndexs(arg0 context.Context, arg1 *ListIndexesReq) (ret0 *ListIndexesResp, ret1 error) {
	return b._ListIndexs(arg0, arg1)
}

// ListMongoDBsFunc alias for method ListMongoDBs
type ListMongoDBsFunc func(arg0 context.Context, arg1 *ListMongoDBsReq) (ret0 *ListMongoDBsResp, ret1 error)

// DecorateListMongoDBs decorate method ListMongoDBs
func (b *_DataSourceServiceDecorator) DecorateListMongoDBs(mw func(ListMongoDBsFunc) ListMongoDBsFunc) DataSourceServiceDecorator {
	b._ListMongoDBs = mw(b._ListMongoDBs)
	return b
}

// ListMongoDBs implement DataSourceService's method ListMongoDBs
func (b *_DataSourceServiceDecorator) ListMongoDBs(arg0 context.Context, arg1 *ListMongoDBsReq) (ret0 *ListMongoDBsResp, ret1 error) {
	return b._ListMongoDBs(arg0, arg1)
}

// CreateFreeLockCorrectOrderFunc alias for method CreateFreeLockCorrectOrder
type CreateFreeLockCorrectOrderFunc func(arg0 context.Context, arg1 *CreateFreeLockCorrectOrderReq) (ret0 *CreateFreeLockCorrectOrderResp, ret1 error)

// DecorateCreateFreeLockCorrectOrder decorate method CreateFreeLockCorrectOrder
func (b *_DataSourceServiceDecorator) DecorateCreateFreeLockCorrectOrder(mw func(CreateFreeLockCorrectOrderFunc) CreateFreeLockCorrectOrderFunc) DataSourceServiceDecorator {
	b._CreateFreeLockCorrectOrder = mw(b._CreateFreeLockCorrectOrder)
	return b
}

// CreateFreeLockCorrectOrder implement DataSourceService's method CreateFreeLockCorrectOrder
func (b *_DataSourceServiceDecorator) CreateFreeLockCorrectOrder(arg0 context.Context, arg1 *CreateFreeLockCorrectOrderReq) (ret0 *CreateFreeLockCorrectOrderResp, ret1 error) {
	return b._CreateFreeLockCorrectOrder(arg0, arg1)
}

// CreateFreeLockCorrectOrderDryRunFunc alias for method CreateFreeLockCorrectOrderDryRun
type CreateFreeLockCorrectOrderDryRunFunc func(arg0 context.Context, arg1 *CreateFreeLockCorrectOrderReq) (ret0 *CreateFreeLockCorrectOrderDryRunResp, ret1 error)

// DecorateCreateFreeLockCorrectOrderDryRun decorate method CreateFreeLockCorrectOrderDryRun
func (b *_DataSourceServiceDecorator) DecorateCreateFreeLockCorrectOrderDryRun(mw func(CreateFreeLockCorrectOrderDryRunFunc) CreateFreeLockCorrectOrderDryRunFunc) DataSourceServiceDecorator {
	b._CreateFreeLockCorrectOrderDryRun = mw(b._CreateFreeLockCorrectOrderDryRun)
	return b
}

// CreateFreeLockCorrectOrderDryRun implement DataSourceService's method CreateFreeLockCorrectOrderDryRun
func (b *_DataSourceServiceDecorator) CreateFreeLockCorrectOrderDryRun(arg0 context.Context, arg1 *CreateFreeLockCorrectOrderReq) (ret0 *CreateFreeLockCorrectOrderDryRunResp, ret1 error) {
	return b._CreateFreeLockCorrectOrderDryRun(arg0, arg1)
}

// DescribeFreeLockCorrectOrdersFunc alias for method DescribeFreeLockCorrectOrders
type DescribeFreeLockCorrectOrdersFunc func(arg0 context.Context, arg1 *DescribeFreeLockCorrectOrdersReq) (ret0 *DescribeFreeLockCorrectOrdersResp, ret1 error)

// DecorateDescribeFreeLockCorrectOrders decorate method DescribeFreeLockCorrectOrders
func (b *_DataSourceServiceDecorator) DecorateDescribeFreeLockCorrectOrders(mw func(DescribeFreeLockCorrectOrdersFunc) DescribeFreeLockCorrectOrdersFunc) DataSourceServiceDecorator {
	b._DescribeFreeLockCorrectOrders = mw(b._DescribeFreeLockCorrectOrders)
	return b
}

// DescribeFreeLockCorrectOrders implement DataSourceService's method DescribeFreeLockCorrectOrders
func (b *_DataSourceServiceDecorator) DescribeFreeLockCorrectOrders(arg0 context.Context, arg1 *DescribeFreeLockCorrectOrdersReq) (ret0 *DescribeFreeLockCorrectOrdersResp, ret1 error) {
	return b._DescribeFreeLockCorrectOrders(arg0, arg1)
}

// StopFreeLockCorrectOrdersFunc alias for method StopFreeLockCorrectOrders
type StopFreeLockCorrectOrdersFunc func(arg0 context.Context, arg1 *StopFreeLockCorrectOrdersReq) (ret0 error)

// DecorateStopFreeLockCorrectOrders decorate method StopFreeLockCorrectOrders
func (b *_DataSourceServiceDecorator) DecorateStopFreeLockCorrectOrders(mw func(StopFreeLockCorrectOrdersFunc) StopFreeLockCorrectOrdersFunc) DataSourceServiceDecorator {
	b._StopFreeLockCorrectOrders = mw(b._StopFreeLockCorrectOrders)
	return b
}

// StopFreeLockCorrectOrders implement DataSourceService's method StopFreeLockCorrectOrders
func (b *_DataSourceServiceDecorator) StopFreeLockCorrectOrders(arg0 context.Context, arg1 *StopFreeLockCorrectOrdersReq) (ret0 error) {
	return b._StopFreeLockCorrectOrders(arg0, arg1)
}

// PreCheckFreeLockCorrectOrdersFunc alias for method PreCheckFreeLockCorrectOrders
type PreCheckFreeLockCorrectOrdersFunc func(arg0 context.Context, arg1 *PreCheckFreeLockCorrectOrdersReq) (ret0 *PreCheckFreeLockCorrectOrdersResp, ret1 error)

// DecoratePreCheckFreeLockCorrectOrders decorate method PreCheckFreeLockCorrectOrders
func (b *_DataSourceServiceDecorator) DecoratePreCheckFreeLockCorrectOrders(mw func(PreCheckFreeLockCorrectOrdersFunc) PreCheckFreeLockCorrectOrdersFunc) DataSourceServiceDecorator {
	b._PreCheckFreeLockCorrectOrders = mw(b._PreCheckFreeLockCorrectOrders)
	return b
}

// PreCheckFreeLockCorrectOrders implement DataSourceService's method PreCheckFreeLockCorrectOrders
func (b *_DataSourceServiceDecorator) PreCheckFreeLockCorrectOrders(arg0 context.Context, arg1 *PreCheckFreeLockCorrectOrdersReq) (ret0 *PreCheckFreeLockCorrectOrdersResp, ret1 error) {
	return b._PreCheckFreeLockCorrectOrders(arg0, arg1)
}

// GetDBInnerAddressFunc alias for method GetDBInnerAddress
type GetDBInnerAddressFunc func(arg0 context.Context, arg1 *GetDBInnerAddressReq) (ret0 *GetDBInnerAddressResp, ret1 error)

// DecorateGetDBInnerAddress decorate method GetDBInnerAddress
func (b *_DataSourceServiceDecorator) DecorateGetDBInnerAddress(mw func(GetDBInnerAddressFunc) GetDBInnerAddressFunc) DataSourceServiceDecorator {
	b._GetDBInnerAddress = mw(b._GetDBInnerAddress)
	return b
}

// GetDBInnerAddress implement DataSourceService's method GetDBInnerAddress
func (b *_DataSourceServiceDecorator) GetDBInnerAddress(arg0 context.Context, arg1 *GetDBInnerAddressReq) (ret0 *GetDBInnerAddressResp, ret1 error) {
	return b._GetDBInnerAddress(arg0, arg1)
}

// ExecuteDQLFunc alias for method ExecuteDQL
type ExecuteDQLFunc func(arg0 context.Context, arg1 *ExecuteDQLReq) (ret0 *ExecuteDQLResp, ret1 error)

// DecorateExecuteDQL decorate method ExecuteDQL
func (b *_DataSourceServiceDecorator) DecorateExecuteDQL(mw func(ExecuteDQLFunc) ExecuteDQLFunc) DataSourceServiceDecorator {
	b._ExecuteDQL = mw(b._ExecuteDQL)
	return b
}

// ExecuteDQL implement DataSourceService's method ExecuteDQL
func (b *_DataSourceServiceDecorator) ExecuteDQL(arg0 context.Context, arg1 *ExecuteDQLReq) (ret0 *ExecuteDQLResp, ret1 error) {
	return b._ExecuteDQL(arg0, arg1)
}

// ExecuteDMLAndGetAffectedRowsFunc alias for method ExecuteDMLAndGetAffectedRows
type ExecuteDMLAndGetAffectedRowsFunc func(arg0 context.Context, arg1 *ExecuteDMLAndGetAffectedRowsReq) (ret0 int64, ret1 error)

// DecorateExecuteDMLAndGetAffectedRows decorate method ExecuteDMLAndGetAffectedRows
func (b *_DataSourceServiceDecorator) DecorateExecuteDMLAndGetAffectedRows(mw func(ExecuteDMLAndGetAffectedRowsFunc) ExecuteDMLAndGetAffectedRowsFunc) DataSourceServiceDecorator {
	b._ExecuteDMLAndGetAffectedRows = mw(b._ExecuteDMLAndGetAffectedRows)
	return b
}

// ExecuteDMLAndGetAffectedRows implement DataSourceService's method ExecuteDMLAndGetAffectedRows
func (b *_DataSourceServiceDecorator) ExecuteDMLAndGetAffectedRows(arg0 context.Context, arg1 *ExecuteDMLAndGetAffectedRowsReq) (ret0 int64, ret1 error) {
	return b._ExecuteDMLAndGetAffectedRows(arg0, arg1)
}

// GetPartitionInfosFunc alias for method GetPartitionInfos
type GetPartitionInfosFunc func(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 []*DbPartitionInfo, ret1 error)

// DecorateGetPartitionInfos decorate method GetPartitionInfos
func (b *_DataSourceServiceDecorator) DecorateGetPartitionInfos(mw func(GetPartitionInfosFunc) GetPartitionInfosFunc) DataSourceServiceDecorator {
	b._GetPartitionInfos = mw(b._GetPartitionInfos)
	return b
}

// GetPartitionInfos implement DataSourceService's method GetPartitionInfos
func (b *_DataSourceServiceDecorator) GetPartitionInfos(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 []*DbPartitionInfo, ret1 error) {
	return b._GetPartitionInfos(arg0, arg1, arg2)
}

// GetShardingDbTypeFunc alias for method GetShardingDbType
type GetShardingDbTypeFunc func(arg0 context.Context, arg1 *shared.DataSource, arg2 string, arg3 string) (ret0 string, ret1 error)

// DecorateGetShardingDbType decorate method GetShardingDbType
func (b *_DataSourceServiceDecorator) DecorateGetShardingDbType(mw func(GetShardingDbTypeFunc) GetShardingDbTypeFunc) DataSourceServiceDecorator {
	b._GetShardingDbType = mw(b._GetShardingDbType)
	return b
}

// GetShardingDbType implement DataSourceService's method GetShardingDbType
func (b *_DataSourceServiceDecorator) GetShardingDbType(arg0 context.Context, arg1 *shared.DataSource, arg2 string, arg3 string) (ret0 string, ret1 error) {
	return b._GetShardingDbType(arg0, arg1, arg2, arg3)
}

// ExplainCommandFunc alias for method ExplainCommand
type ExplainCommandFunc func(arg0 context.Context, arg1 *ExplainCommandReq) (ret0 *ExplainCommandResp, ret1 error)

// DecorateExplainCommand decorate method ExplainCommand
func (b *_DataSourceServiceDecorator) DecorateExplainCommand(mw func(ExplainCommandFunc) ExplainCommandFunc) DataSourceServiceDecorator {
	b._ExplainCommand = mw(b._ExplainCommand)
	return b
}

// ExplainCommand implement DataSourceService's method ExplainCommand
func (b *_DataSourceServiceDecorator) ExplainCommand(arg0 context.Context, arg1 *ExplainCommandReq) (ret0 *ExplainCommandResp, ret1 error) {
	return b._ExplainCommand(arg0, arg1)
}

// IsMyOwnInstanceFunc alias for method IsMyOwnInstance
type IsMyOwnInstanceFunc func(arg0 context.Context, arg1 string, arg2 shared.DataSourceType) (ret0 bool)

// DecorateIsMyOwnInstance decorate method IsMyOwnInstance
func (b *_DataSourceServiceDecorator) DecorateIsMyOwnInstance(mw func(IsMyOwnInstanceFunc) IsMyOwnInstanceFunc) DataSourceServiceDecorator {
	b._IsMyOwnInstance = mw(b._IsMyOwnInstance)
	return b
}

// IsMyOwnInstance implement DataSourceService's method IsMyOwnInstance
func (b *_DataSourceServiceDecorator) IsMyOwnInstance(arg0 context.Context, arg1 string, arg2 shared.DataSourceType) (ret0 bool) {
	return b._IsMyOwnInstance(arg0, arg1, arg2)
}

// CheckInstanceStateFunc alias for method CheckInstanceState
type CheckInstanceStateFunc func(arg0 context.Context, arg1 string, arg2 shared.DataSourceType, arg3 bool) (ret0 error)

// DecorateCheckInstanceState decorate method CheckInstanceState
func (b *_DataSourceServiceDecorator) DecorateCheckInstanceState(mw func(CheckInstanceStateFunc) CheckInstanceStateFunc) DataSourceServiceDecorator {
	b._CheckInstanceState = mw(b._CheckInstanceState)
	return b
}

// CheckInstanceState implement DataSourceService's method CheckInstanceState
func (b *_DataSourceServiceDecorator) CheckInstanceState(arg0 context.Context, arg1 string, arg2 shared.DataSourceType, arg3 bool) (ret0 error) {
	return b._CheckInstanceState(arg0, arg1, arg2, arg3)
}

// GetTableIndexInfoFunc alias for method GetTableIndexInfo
type GetTableIndexInfoFunc func(arg0 context.Context, arg1 *GetTableIndexInfoReq) (ret0 *GetTableInfoIndexResp, ret1 error)

// DecorateGetTableIndexInfo decorate method GetTableIndexInfo
func (b *_DataSourceServiceDecorator) DecorateGetTableIndexInfo(mw func(GetTableIndexInfoFunc) GetTableIndexInfoFunc) DataSourceServiceDecorator {
	b._GetTableIndexInfo = mw(b._GetTableIndexInfo)
	return b
}

// GetTableIndexInfo implement DataSourceService's method GetTableIndexInfo
func (b *_DataSourceServiceDecorator) GetTableIndexInfo(arg0 context.Context, arg1 *GetTableIndexInfoReq) (ret0 *GetTableInfoIndexResp, ret1 error) {
	return b._GetTableIndexInfo(arg0, arg1)
}

// GetTableIndexValueFunc alias for method GetTableIndexValue
type GetTableIndexValueFunc func(arg0 context.Context, arg1 *GetIndexValueReq) (ret0 *GetIndexValueResp, ret1 error)

// DecorateGetTableIndexValue decorate method GetTableIndexValue
func (b *_DataSourceServiceDecorator) DecorateGetTableIndexValue(mw func(GetTableIndexValueFunc) GetTableIndexValueFunc) DataSourceServiceDecorator {
	b._GetTableIndexValue = mw(b._GetTableIndexValue)
	return b
}

// GetTableIndexValue implement DataSourceService's method GetTableIndexValue
func (b *_DataSourceServiceDecorator) GetTableIndexValue(arg0 context.Context, arg1 *GetIndexValueReq) (ret0 *GetIndexValueResp, ret1 error) {
	return b._GetTableIndexValue(arg0, arg1)
}

// GetMaxConnectionsFunc alias for method GetMaxConnections
type GetMaxConnectionsFunc func(arg0 context.Context, arg1 *GetMaxConnectionsReq) (ret0 int, ret1 error)

// DecorateGetMaxConnections decorate method GetMaxConnections
func (b *_DataSourceServiceDecorator) DecorateGetMaxConnections(mw func(GetMaxConnectionsFunc) GetMaxConnectionsFunc) DataSourceServiceDecorator {
	b._GetMaxConnections = mw(b._GetMaxConnections)
	return b
}

// GetMaxConnections implement DataSourceService's method GetMaxConnections
func (b *_DataSourceServiceDecorator) GetMaxConnections(arg0 context.Context, arg1 *GetMaxConnectionsReq) (ret0 int, ret1 error) {
	return b._GetMaxConnections(arg0, arg1)
}

// GetCurrentBandwidthFunc alias for method GetCurrentBandwidth
type GetCurrentBandwidthFunc func(arg0 context.Context, arg1 *GetCurrentBandwidthReq) (ret0 *InstanceBandwidthInfo, ret1 error)

// DecorateGetCurrentBandwidth decorate method GetCurrentBandwidth
func (b *_DataSourceServiceDecorator) DecorateGetCurrentBandwidth(mw func(GetCurrentBandwidthFunc) GetCurrentBandwidthFunc) DataSourceServiceDecorator {
	b._GetCurrentBandwidth = mw(b._GetCurrentBandwidth)
	return b
}

// GetCurrentBandwidth implement DataSourceService's method GetCurrentBandwidth
func (b *_DataSourceServiceDecorator) GetCurrentBandwidth(arg0 context.Context, arg1 *GetCurrentBandwidthReq) (ret0 *InstanceBandwidthInfo, ret1 error) {
	return b._GetCurrentBandwidth(arg0, arg1)
}

// BandwidthScaleFunc alias for method BandwidthScale
type BandwidthScaleFunc func(arg0 context.Context, arg1 *BandwidthScaleReq) (ret0 error)

// DecorateBandwidthScale decorate method BandwidthScale
func (b *_DataSourceServiceDecorator) DecorateBandwidthScale(mw func(BandwidthScaleFunc) BandwidthScaleFunc) DataSourceServiceDecorator {
	b._BandwidthScale = mw(b._BandwidthScale)
	return b
}

// BandwidthScale implement DataSourceService's method BandwidthScale
func (b *_DataSourceServiceDecorator) BandwidthScale(arg0 context.Context, arg1 *BandwidthScaleReq) (ret0 error) {
	return b._BandwidthScale(arg0, arg1)
}

// GetMinBandwidthFunc alias for method GetMinBandwidth
type GetMinBandwidthFunc func(arg0 context.Context, arg1 *GetMinMaxBandwidthReq) (ret0 int, ret1 error)

// DecorateGetMinBandwidth decorate method GetMinBandwidth
func (b *_DataSourceServiceDecorator) DecorateGetMinBandwidth(mw func(GetMinBandwidthFunc) GetMinBandwidthFunc) DataSourceServiceDecorator {
	b._GetMinBandwidth = mw(b._GetMinBandwidth)
	return b
}

// GetMinBandwidth implement DataSourceService's method GetMinBandwidth
func (b *_DataSourceServiceDecorator) GetMinBandwidth(arg0 context.Context, arg1 *GetMinMaxBandwidthReq) (ret0 int, ret1 error) {
	return b._GetMinBandwidth(arg0, arg1)
}

// GetMaxBandwidthFunc alias for method GetMaxBandwidth
type GetMaxBandwidthFunc func(arg0 context.Context, arg1 *GetMinMaxBandwidthReq) (ret0 int, ret1 error)

// DecorateGetMaxBandwidth decorate method GetMaxBandwidth
func (b *_DataSourceServiceDecorator) DecorateGetMaxBandwidth(mw func(GetMaxBandwidthFunc) GetMaxBandwidthFunc) DataSourceServiceDecorator {
	b._GetMaxBandwidth = mw(b._GetMaxBandwidth)
	return b
}

// GetMaxBandwidth implement DataSourceService's method GetMaxBandwidth
func (b *_DataSourceServiceDecorator) GetMaxBandwidth(arg0 context.Context, arg1 *GetMinMaxBandwidthReq) (ret0 int, ret1 error) {
	return b._GetMaxBandwidth(arg0, arg1)
}

// GetDiskSizeFunc alias for method GetDiskSize
type GetDiskSizeFunc func(arg0 context.Context, arg1 *GetDiskSizeReq) (ret0 *GetDiskSizeResp, ret1 error)

// DecorateGetDiskSize decorate method GetDiskSize
func (b *_DataSourceServiceDecorator) DecorateGetDiskSize(mw func(GetDiskSizeFunc) GetDiskSizeFunc) DataSourceServiceDecorator {
	b._GetDiskSize = mw(b._GetDiskSize)
	return b
}

// GetDiskSize implement DataSourceService's method GetDiskSize
func (b *_DataSourceServiceDecorator) GetDiskSize(arg0 context.Context, arg1 *GetDiskSizeReq) (ret0 *GetDiskSizeResp, ret1 error) {
	return b._GetDiskSize(arg0, arg1)
}

// GetUsedSizeFunc alias for method GetUsedSize
type GetUsedSizeFunc func(arg0 context.Context, arg1 *GetDiskSizeReq) (ret0 int64, ret1 error)

// DecorateGetUsedSize decorate method GetUsedSize
func (b *_DataSourceServiceDecorator) DecorateGetUsedSize(mw func(GetUsedSizeFunc) GetUsedSizeFunc) DataSourceServiceDecorator {
	b._GetUsedSize = mw(b._GetUsedSize)
	return b
}

// GetUsedSize implement DataSourceService's method GetUsedSize
func (b *_DataSourceServiceDecorator) GetUsedSize(arg0 context.Context, arg1 *GetDiskSizeReq) (ret0 int64, ret1 error) {
	return b._GetUsedSize(arg0, arg1)
}

// GetCurrentMetricDataFunc alias for method GetCurrentMetricData
type GetCurrentMetricDataFunc func(arg0 context.Context, arg1 *GetMetricDatapointsReq) (ret0 *GetMetricDatapointsResp, ret1 error)

// DecorateGetCurrentMetricData decorate method GetCurrentMetricData
func (b *_DataSourceServiceDecorator) DecorateGetCurrentMetricData(mw func(GetCurrentMetricDataFunc) GetCurrentMetricDataFunc) DataSourceServiceDecorator {
	b._GetCurrentMetricData = mw(b._GetCurrentMetricData)
	return b
}

// GetCurrentMetricData implement DataSourceService's method GetCurrentMetricData
func (b *_DataSourceServiceDecorator) GetCurrentMetricData(arg0 context.Context, arg1 *GetMetricDatapointsReq) (ret0 *GetMetricDatapointsResp, ret1 error) {
	return b._GetCurrentMetricData(arg0, arg1)
}

// GetPreSecondMetricDataFunc alias for method GetPreSecondMetricData
type GetPreSecondMetricDataFunc func(arg0 context.Context, arg1 *GetMetricDatapointsReq) (ret0 *GetMetricDatapointsResp, ret1 error)

// DecorateGetPreSecondMetricData decorate method GetPreSecondMetricData
func (b *_DataSourceServiceDecorator) DecorateGetPreSecondMetricData(mw func(GetPreSecondMetricDataFunc) GetPreSecondMetricDataFunc) DataSourceServiceDecorator {
	b._GetPreSecondMetricData = mw(b._GetPreSecondMetricData)
	return b
}

// GetPreSecondMetricData implement DataSourceService's method GetPreSecondMetricData
func (b *_DataSourceServiceDecorator) GetPreSecondMetricData(arg0 context.Context, arg1 *GetMetricDatapointsReq) (ret0 *GetMetricDatapointsResp, ret1 error) {
	return b._GetPreSecondMetricData(arg0, arg1)
}

// GetPreSecondMetricDataByInstanceFunc alias for method GetPreSecondMetricDataByInstance
type GetPreSecondMetricDataByInstanceFunc func(arg0 context.Context, arg1 *GetMetricDatapointsReq) (ret0 *GetMetricDatapointsResp, ret1 error)

// DecorateGetPreSecondMetricDataByInstance decorate method GetPreSecondMetricDataByInstance
func (b *_DataSourceServiceDecorator) DecorateGetPreSecondMetricDataByInstance(mw func(GetPreSecondMetricDataByInstanceFunc) GetPreSecondMetricDataByInstanceFunc) DataSourceServiceDecorator {
	b._GetPreSecondMetricDataByInstance = mw(b._GetPreSecondMetricDataByInstance)
	return b
}

// GetPreSecondMetricDataByInstance implement DataSourceService's method GetPreSecondMetricDataByInstance
func (b *_DataSourceServiceDecorator) GetPreSecondMetricDataByInstance(arg0 context.Context, arg1 *GetMetricDatapointsReq) (ret0 *GetMetricDatapointsResp, ret1 error) {
	return b._GetPreSecondMetricDataByInstance(arg0, arg1)
}

// DescribeFullSQLLogConfigFunc alias for method DescribeFullSQLLogConfig
type DescribeFullSQLLogConfigFunc func(arg0 context.Context, arg1 *DescribeFullSQLLogConfigReq) (ret0 *DescribeFullSQLLogConfigResp, ret1 error)

// DecorateDescribeFullSQLLogConfig decorate method DescribeFullSQLLogConfig
func (b *_DataSourceServiceDecorator) DecorateDescribeFullSQLLogConfig(mw func(DescribeFullSQLLogConfigFunc) DescribeFullSQLLogConfigFunc) DataSourceServiceDecorator {
	b._DescribeFullSQLLogConfig = mw(b._DescribeFullSQLLogConfig)
	return b
}

// DescribeFullSQLLogConfig implement DataSourceService's method DescribeFullSQLLogConfig
func (b *_DataSourceServiceDecorator) DescribeFullSQLLogConfig(arg0 context.Context, arg1 *DescribeFullSQLLogConfigReq) (ret0 *DescribeFullSQLLogConfigResp, ret1 error) {
	return b._DescribeFullSQLLogConfig(arg0, arg1)
}

// ModifyFullSQLLogConfigFunc alias for method ModifyFullSQLLogConfig
type ModifyFullSQLLogConfigFunc func(arg0 context.Context, arg1 *ModifyFullSQLLogConfigReq) (ret0 *ModifyFullSQLLogConfigResp, ret1 error)

// DecorateModifyFullSQLLogConfig decorate method ModifyFullSQLLogConfig
func (b *_DataSourceServiceDecorator) DecorateModifyFullSQLLogConfig(mw func(ModifyFullSQLLogConfigFunc) ModifyFullSQLLogConfigFunc) DataSourceServiceDecorator {
	b._ModifyFullSQLLogConfig = mw(b._ModifyFullSQLLogConfig)
	return b
}

// ModifyFullSQLLogConfig implement DataSourceService's method ModifyFullSQLLogConfig
func (b *_DataSourceServiceDecorator) ModifyFullSQLLogConfig(arg0 context.Context, arg1 *ModifyFullSQLLogConfigReq) (ret0 *ModifyFullSQLLogConfigResp, ret1 error) {
	return b._ModifyFullSQLLogConfig(arg0, arg1)
}

// DescribeInstanceVariablesFunc alias for method DescribeInstanceVariables
type DescribeInstanceVariablesFunc func(arg0 context.Context, arg1 *DescribeInstanceVariablesReq) (ret0 *DescribeInstanceVariablesResp, ret1 error)

// DecorateDescribeInstanceVariables decorate method DescribeInstanceVariables
func (b *_DataSourceServiceDecorator) DecorateDescribeInstanceVariables(mw func(DescribeInstanceVariablesFunc) DescribeInstanceVariablesFunc) DataSourceServiceDecorator {
	b._DescribeInstanceVariables = mw(b._DescribeInstanceVariables)
	return b
}

// DescribeInstanceVariables implement DataSourceService's method DescribeInstanceVariables
func (b *_DataSourceServiceDecorator) DescribeInstanceVariables(arg0 context.Context, arg1 *DescribeInstanceVariablesReq) (ret0 *DescribeInstanceVariablesResp, ret1 error) {
	return b._DescribeInstanceVariables(arg0, arg1)
}

// DescribePrimaryKeyRangeFunc alias for method DescribePrimaryKeyRange
type DescribePrimaryKeyRangeFunc func(arg0 context.Context, arg1 *DescribePrimaryKeyRangeReq) (ret0 *DescribePrimaryKeyRangeResp, ret1 error)

// DecorateDescribePrimaryKeyRange decorate method DescribePrimaryKeyRange
func (b *_DataSourceServiceDecorator) DecorateDescribePrimaryKeyRange(mw func(DescribePrimaryKeyRangeFunc) DescribePrimaryKeyRangeFunc) DataSourceServiceDecorator {
	b._DescribePrimaryKeyRange = mw(b._DescribePrimaryKeyRange)
	return b
}

// DescribePrimaryKeyRange implement DataSourceService's method DescribePrimaryKeyRange
func (b *_DataSourceServiceDecorator) DescribePrimaryKeyRange(arg0 context.Context, arg1 *DescribePrimaryKeyRangeReq) (ret0 *DescribePrimaryKeyRangeResp, ret1 error) {
	return b._DescribePrimaryKeyRange(arg0, arg1)
}

// DescribeSQLAdvisorTableMetaFunc alias for method DescribeSQLAdvisorTableMeta
type DescribeSQLAdvisorTableMetaFunc func(arg0 context.Context, arg1 *DescribeSQLAdvisorTableMetaReq) (ret0 *DescribeSQLAdvisorTableMetaResp, ret1 error)

// DecorateDescribeSQLAdvisorTableMeta decorate method DescribeSQLAdvisorTableMeta
func (b *_DataSourceServiceDecorator) DecorateDescribeSQLAdvisorTableMeta(mw func(DescribeSQLAdvisorTableMetaFunc) DescribeSQLAdvisorTableMetaFunc) DataSourceServiceDecorator {
	b._DescribeSQLAdvisorTableMeta = mw(b._DescribeSQLAdvisorTableMeta)
	return b
}

// DescribeSQLAdvisorTableMeta implement DataSourceService's method DescribeSQLAdvisorTableMeta
func (b *_DataSourceServiceDecorator) DescribeSQLAdvisorTableMeta(arg0 context.Context, arg1 *DescribeSQLAdvisorTableMetaReq) (ret0 *DescribeSQLAdvisorTableMetaResp, ret1 error) {
	return b._DescribeSQLAdvisorTableMeta(arg0, arg1)
}

// DescribeSampleDataFunc alias for method DescribeSampleData
type DescribeSampleDataFunc func(arg0 context.Context, arg1 *DescribeSampleDataReq) (ret0 *DescribeSampleDataResp, ret1 error)

// DecorateDescribeSampleData decorate method DescribeSampleData
func (b *_DataSourceServiceDecorator) DecorateDescribeSampleData(mw func(DescribeSampleDataFunc) DescribeSampleDataFunc) DataSourceServiceDecorator {
	b._DescribeSampleData = mw(b._DescribeSampleData)
	return b
}

// DescribeSampleData implement DataSourceService's method DescribeSampleData
func (b *_DataSourceServiceDecorator) DescribeSampleData(arg0 context.Context, arg1 *DescribeSampleDataReq) (ret0 *DescribeSampleDataResp, ret1 error) {
	return b._DescribeSampleData(arg0, arg1)
}

// EnsureAccountFunc alias for method EnsureAccount
type EnsureAccountFunc func(arg0 context.Context, arg1 *EnsureAccountReq) (ret0 error)

// DecorateEnsureAccount decorate method EnsureAccount
func (b *_DataSourceServiceDecorator) DecorateEnsureAccount(mw func(EnsureAccountFunc) EnsureAccountFunc) DataSourceServiceDecorator {
	b._EnsureAccount = mw(b._EnsureAccount)
	return b
}

// EnsureAccount implement DataSourceService's method EnsureAccount
func (b *_DataSourceServiceDecorator) EnsureAccount(arg0 context.Context, arg1 *EnsureAccountReq) (ret0 error) {
	return b._EnsureAccount(arg0, arg1)
}

// GetDatasourceAddressFunc alias for method GetDatasourceAddress
type GetDatasourceAddressFunc func(arg0 context.Context, arg1 *shared.DataSource) (ret0 error)

// DecorateGetDatasourceAddress decorate method GetDatasourceAddress
func (b *_DataSourceServiceDecorator) DecorateGetDatasourceAddress(mw func(GetDatasourceAddressFunc) GetDatasourceAddressFunc) DataSourceServiceDecorator {
	b._GetDatasourceAddress = mw(b._GetDatasourceAddress)
	return b
}

// GetDatasourceAddress implement DataSourceService's method GetDatasourceAddress
func (b *_DataSourceServiceDecorator) GetDatasourceAddress(arg0 context.Context, arg1 *shared.DataSource) (ret0 error) {
	return b._GetDatasourceAddress(arg0, arg1)
}

// GetDBServiceTreeMountInfoFunc alias for method GetDBServiceTreeMountInfo
type GetDBServiceTreeMountInfoFunc func(arg0 context.Context, arg1 *GetDBServiceTreeMountInfoReq) (ret0 *GetDBServiceTreeMountInfoResp, ret1 error)

// DecorateGetDBServiceTreeMountInfo decorate method GetDBServiceTreeMountInfo
func (b *_DataSourceServiceDecorator) DecorateGetDBServiceTreeMountInfo(mw func(GetDBServiceTreeMountInfoFunc) GetDBServiceTreeMountInfoFunc) DataSourceServiceDecorator {
	b._GetDBServiceTreeMountInfo = mw(b._GetDBServiceTreeMountInfo)
	return b
}

// GetDBServiceTreeMountInfo implement DataSourceService's method GetDBServiceTreeMountInfo
func (b *_DataSourceServiceDecorator) GetDBServiceTreeMountInfo(arg0 context.Context, arg1 *GetDBServiceTreeMountInfoReq) (ret0 *GetDBServiceTreeMountInfoResp, ret1 error) {
	return b._GetDBServiceTreeMountInfo(arg0, arg1)
}

// GetDBInstanceInfoFunc alias for method GetDBInstanceInfo
type GetDBInstanceInfoFunc func(arg0 context.Context, arg1 *GetDBInstanceInfoReq) (ret0 *GetDBInstanceInfoResp, ret1 error)

// DecorateGetDBInstanceInfo decorate method GetDBInstanceInfo
func (b *_DataSourceServiceDecorator) DecorateGetDBInstanceInfo(mw func(GetDBInstanceInfoFunc) GetDBInstanceInfoFunc) DataSourceServiceDecorator {
	b._GetDBInstanceInfo = mw(b._GetDBInstanceInfo)
	return b
}

// GetDBInstanceInfo implement DataSourceService's method GetDBInstanceInfo
func (b *_DataSourceServiceDecorator) GetDBInstanceInfo(arg0 context.Context, arg1 *GetDBInstanceInfoReq) (ret0 *GetDBInstanceInfoResp, ret1 error) {
	return b._GetDBInstanceInfo(arg0, arg1)
}

// InstanceIsExistFunc alias for method InstanceIsExist
type InstanceIsExistFunc func(arg0 context.Context, arg1 *InstanceIsExistReq) (ret0 bool, ret1 error)

// DecorateInstanceIsExist decorate method InstanceIsExist
func (b *_DataSourceServiceDecorator) DecorateInstanceIsExist(mw func(InstanceIsExistFunc) InstanceIsExistFunc) DataSourceServiceDecorator {
	b._InstanceIsExist = mw(b._InstanceIsExist)
	return b
}

// InstanceIsExist implement DataSourceService's method InstanceIsExist
func (b *_DataSourceServiceDecorator) InstanceIsExist(arg0 context.Context, arg1 *InstanceIsExistReq) (ret0 bool, ret1 error) {
	return b._InstanceIsExist(arg0, arg1)
}

// GetInstanceTopoFunc alias for method GetInstanceTopo
type GetInstanceTopoFunc func(arg0 context.Context, arg1 *GetInstanceTopoReq) (ret0 []*model.InnerRdsInstance, ret1 error)

// DecorateGetInstanceTopo decorate method GetInstanceTopo
func (b *_DataSourceServiceDecorator) DecorateGetInstanceTopo(mw func(GetInstanceTopoFunc) GetInstanceTopoFunc) DataSourceServiceDecorator {
	b._GetInstanceTopo = mw(b._GetInstanceTopo)
	return b
}

// GetInstanceTopo implement DataSourceService's method GetInstanceTopo
func (b *_DataSourceServiceDecorator) GetInstanceTopo(arg0 context.Context, arg1 *GetInstanceTopoReq) (ret0 []*model.InnerRdsInstance, ret1 error) {
	return b._GetInstanceTopo(arg0, arg1)
}

// GetInstanceProxyTopoFunc alias for method GetInstanceProxyTopo
type GetInstanceProxyTopoFunc func(arg0 context.Context, arg1 *GetInstanceTopoReq) (ret0 []*model.InnerRdsInstance, ret1 error)

// DecorateGetInstanceProxyTopo decorate method GetInstanceProxyTopo
func (b *_DataSourceServiceDecorator) DecorateGetInstanceProxyTopo(mw func(GetInstanceProxyTopoFunc) GetInstanceProxyTopoFunc) DataSourceServiceDecorator {
	b._GetInstanceProxyTopo = mw(b._GetInstanceProxyTopo)
	return b
}

// GetInstanceProxyTopo implement DataSourceService's method GetInstanceProxyTopo
func (b *_DataSourceServiceDecorator) GetInstanceProxyTopo(arg0 context.Context, arg1 *GetInstanceTopoReq) (ret0 []*model.InnerRdsInstance, ret1 error) {
	return b._GetInstanceProxyTopo(arg0, arg1)
}

// CreateLogDownloadTaskFunc alias for method CreateLogDownloadTask
type CreateLogDownloadTaskFunc func(arg0 context.Context, arg1 *CreateLogDownloadTaskReq) (ret0 error)

// DecorateCreateLogDownloadTask decorate method CreateLogDownloadTask
func (b *_DataSourceServiceDecorator) DecorateCreateLogDownloadTask(mw func(CreateLogDownloadTaskFunc) CreateLogDownloadTaskFunc) DataSourceServiceDecorator {
	b._CreateLogDownloadTask = mw(b._CreateLogDownloadTask)
	return b
}

// CreateLogDownloadTask implement DataSourceService's method CreateLogDownloadTask
func (b *_DataSourceServiceDecorator) CreateLogDownloadTask(arg0 context.Context, arg1 *CreateLogDownloadTaskReq) (ret0 error) {
	return b._CreateLogDownloadTask(arg0, arg1)
}

// GetLogDownloadListFunc alias for method GetLogDownloadList
type GetLogDownloadListFunc func(arg0 context.Context, arg1 *GetLogDownloadListReq) (ret0 *GetLogDownloadListResp, ret1 error)

// DecorateGetLogDownloadList decorate method GetLogDownloadList
func (b *_DataSourceServiceDecorator) DecorateGetLogDownloadList(mw func(GetLogDownloadListFunc) GetLogDownloadListFunc) DataSourceServiceDecorator {
	b._GetLogDownloadList = mw(b._GetLogDownloadList)
	return b
}

// GetLogDownloadList implement DataSourceService's method GetLogDownloadList
func (b *_DataSourceServiceDecorator) GetLogDownloadList(arg0 context.Context, arg1 *GetLogDownloadListReq) (ret0 *GetLogDownloadListResp, ret1 error) {
	return b._GetLogDownloadList(arg0, arg1)
}

// GetInstancePrimaryNodeIdFunc alias for method GetInstancePrimaryNodeId
type GetInstancePrimaryNodeIdFunc func(arg0 context.Context, arg1 *GetInstancePrimaryNodeIdReq) (ret0 *GetInstancePrimaryNodeIdResp, ret1 error)

// DecorateGetInstancePrimaryNodeId decorate method GetInstancePrimaryNodeId
func (b *_DataSourceServiceDecorator) DecorateGetInstancePrimaryNodeId(mw func(GetInstancePrimaryNodeIdFunc) GetInstancePrimaryNodeIdFunc) DataSourceServiceDecorator {
	b._GetInstancePrimaryNodeId = mw(b._GetInstancePrimaryNodeId)
	return b
}

// GetInstancePrimaryNodeId implement DataSourceService's method GetInstancePrimaryNodeId
func (b *_DataSourceServiceDecorator) GetInstancePrimaryNodeId(arg0 context.Context, arg1 *GetInstancePrimaryNodeIdReq) (ret0 *GetInstancePrimaryNodeIdResp, ret1 error) {
	return b._GetInstancePrimaryNodeId(arg0, arg1)
}

// ListSQLKillRulesFunc alias for method ListSQLKillRules
type ListSQLKillRulesFunc func(arg0 context.Context, arg1 *ListSQLKillRulesReq) (ret0 *ListSQLKillRulesResp, ret1 error)

// DecorateListSQLKillRules decorate method ListSQLKillRules
func (b *_DataSourceServiceDecorator) DecorateListSQLKillRules(mw func(ListSQLKillRulesFunc) ListSQLKillRulesFunc) DataSourceServiceDecorator {
	b._ListSQLKillRules = mw(b._ListSQLKillRules)
	return b
}

// ListSQLKillRules implement DataSourceService's method ListSQLKillRules
func (b *_DataSourceServiceDecorator) ListSQLKillRules(arg0 context.Context, arg1 *ListSQLKillRulesReq) (ret0 *ListSQLKillRulesResp, ret1 error) {
	return b._ListSQLKillRules(arg0, arg1)
}

// ModifySQLKillRuleFunc alias for method ModifySQLKillRule
type ModifySQLKillRuleFunc func(arg0 context.Context, arg1 *ModifySQLKillRuleReq) (ret0 *ModifySQLKillRuleResp, ret1 error)

// DecorateModifySQLKillRule decorate method ModifySQLKillRule
func (b *_DataSourceServiceDecorator) DecorateModifySQLKillRule(mw func(ModifySQLKillRuleFunc) ModifySQLKillRuleFunc) DataSourceServiceDecorator {
	b._ModifySQLKillRule = mw(b._ModifySQLKillRule)
	return b
}

// ModifySQLKillRule implement DataSourceService's method ModifySQLKillRule
func (b *_DataSourceServiceDecorator) ModifySQLKillRule(arg0 context.Context, arg1 *ModifySQLKillRuleReq) (ret0 *ModifySQLKillRuleResp, ret1 error) {
	return b._ModifySQLKillRule(arg0, arg1)
}

// GetManagedAccountAndPwdFunc alias for method GetManagedAccountAndPwd
type GetManagedAccountAndPwdFunc func(arg0 context.Context, arg1 *shared.DataSource) (ret0 *GetManagedAccountAndPwdResp, ret1 error)

// DecorateGetManagedAccountAndPwd decorate method GetManagedAccountAndPwd
func (b *_DataSourceServiceDecorator) DecorateGetManagedAccountAndPwd(mw func(GetManagedAccountAndPwdFunc) GetManagedAccountAndPwdFunc) DataSourceServiceDecorator {
	b._GetManagedAccountAndPwd = mw(b._GetManagedAccountAndPwd)
	return b
}

// GetManagedAccountAndPwd implement DataSourceService's method GetManagedAccountAndPwd
func (b *_DataSourceServiceDecorator) GetManagedAccountAndPwd(arg0 context.Context, arg1 *shared.DataSource) (ret0 *GetManagedAccountAndPwdResp, ret1 error) {
	return b._GetManagedAccountAndPwd(arg0, arg1)
}

// CalculateSpecAfterScaleFunc alias for method CalculateSpecAfterScale
type CalculateSpecAfterScaleFunc func(arg0 context.Context, arg1 *CalculateSpecAfterScaleReq) (ret0 *CalculateSpecAfterScaleResp, ret1 error)

// DecorateCalculateSpecAfterScale decorate method CalculateSpecAfterScale
func (b *_DataSourceServiceDecorator) DecorateCalculateSpecAfterScale(mw func(CalculateSpecAfterScaleFunc) CalculateSpecAfterScaleFunc) DataSourceServiceDecorator {
	b._CalculateSpecAfterScale = mw(b._CalculateSpecAfterScale)
	return b
}

// CalculateSpecAfterScale implement DataSourceService's method CalculateSpecAfterScale
func (b *_DataSourceServiceDecorator) CalculateSpecAfterScale(arg0 context.Context, arg1 *CalculateSpecAfterScaleReq) (ret0 *CalculateSpecAfterScaleResp, ret1 error) {
	return b._CalculateSpecAfterScale(arg0, arg1)
}

// ModifyDBInstanceSpecFunc alias for method ModifyDBInstanceSpec
type ModifyDBInstanceSpecFunc func(arg0 context.Context, arg1 *ModifyDBInstanceSpecReq) (ret0 *ModifyDBInstanceSpecResp, ret1 error)

// DecorateModifyDBInstanceSpec decorate method ModifyDBInstanceSpec
func (b *_DataSourceServiceDecorator) DecorateModifyDBInstanceSpec(mw func(ModifyDBInstanceSpecFunc) ModifyDBInstanceSpecFunc) DataSourceServiceDecorator {
	b._ModifyDBInstanceSpec = mw(b._ModifyDBInstanceSpec)
	return b
}

// ModifyDBInstanceSpec implement DataSourceService's method ModifyDBInstanceSpec
func (b *_DataSourceServiceDecorator) ModifyDBInstanceSpec(arg0 context.Context, arg1 *ModifyDBInstanceSpecReq) (ret0 *ModifyDBInstanceSpecResp, ret1 error) {
	return b._ModifyDBInstanceSpec(arg0, arg1)
}

// DescribeDBAutoScalingConfigFunc alias for method DescribeDBAutoScalingConfig
type DescribeDBAutoScalingConfigFunc func(arg0 context.Context, arg1 *DescribeDBAutoScalingConfigReq) (ret0 *DescribeDBAutoScalingConfigResp, ret1 error)

// DecorateDescribeDBAutoScalingConfig decorate method DescribeDBAutoScalingConfig
func (b *_DataSourceServiceDecorator) DecorateDescribeDBAutoScalingConfig(mw func(DescribeDBAutoScalingConfigFunc) DescribeDBAutoScalingConfigFunc) DataSourceServiceDecorator {
	b._DescribeDBAutoScalingConfig = mw(b._DescribeDBAutoScalingConfig)
	return b
}

// DescribeDBAutoScalingConfig implement DataSourceService's method DescribeDBAutoScalingConfig
func (b *_DataSourceServiceDecorator) DescribeDBAutoScalingConfig(arg0 context.Context, arg1 *DescribeDBAutoScalingConfigReq) (ret0 *DescribeDBAutoScalingConfigResp, ret1 error) {
	return b._DescribeDBAutoScalingConfig(arg0, arg1)
}

// ModifyDBAutoScalingConfigFunc alias for method ModifyDBAutoScalingConfig
type ModifyDBAutoScalingConfigFunc func(arg0 context.Context, arg1 *ModifyDBAutoScalingConfigReq) (ret0 *ModifyDBAutoScalingConfigResp, ret1 error)

// DecorateModifyDBAutoScalingConfig decorate method ModifyDBAutoScalingConfig
func (b *_DataSourceServiceDecorator) DecorateModifyDBAutoScalingConfig(mw func(ModifyDBAutoScalingConfigFunc) ModifyDBAutoScalingConfigFunc) DataSourceServiceDecorator {
	b._ModifyDBAutoScalingConfig = mw(b._ModifyDBAutoScalingConfig)
	return b
}

// ModifyDBAutoScalingConfig implement DataSourceService's method ModifyDBAutoScalingConfig
func (b *_DataSourceServiceDecorator) ModifyDBAutoScalingConfig(arg0 context.Context, arg1 *ModifyDBAutoScalingConfigReq) (ret0 *ModifyDBAutoScalingConfigResp, ret1 error) {
	return b._ModifyDBAutoScalingConfig(arg0, arg1)
}

// DescribeDBAutoScaleEventsFunc alias for method DescribeDBAutoScaleEvents
type DescribeDBAutoScaleEventsFunc func(arg0 context.Context, arg1 *DescribeDBAutoScaleEventsReq) (ret0 *DescribeDBAutoScaleEventsResp, ret1 error)

// DecorateDescribeDBAutoScaleEvents decorate method DescribeDBAutoScaleEvents
func (b *_DataSourceServiceDecorator) DecorateDescribeDBAutoScaleEvents(mw func(DescribeDBAutoScaleEventsFunc) DescribeDBAutoScaleEventsFunc) DataSourceServiceDecorator {
	b._DescribeDBAutoScaleEvents = mw(b._DescribeDBAutoScaleEvents)
	return b
}

// DescribeDBAutoScaleEvents implement DataSourceService's method DescribeDBAutoScaleEvents
func (b *_DataSourceServiceDecorator) DescribeDBAutoScaleEvents(arg0 context.Context, arg1 *DescribeDBAutoScaleEventsReq) (ret0 *DescribeDBAutoScaleEventsResp, ret1 error) {
	return b._DescribeDBAutoScaleEvents(arg0, arg1)
}

// ModifyDBLocalSpecManuallyFunc alias for method ModifyDBLocalSpecManually
type ModifyDBLocalSpecManuallyFunc func(arg0 context.Context, arg1 *ModifyDBLocalSpecManuallyReq) (ret0 *ModifyDBLocalSpecManuallyResp, ret1 error)

// DecorateModifyDBLocalSpecManually decorate method ModifyDBLocalSpecManually
func (b *_DataSourceServiceDecorator) DecorateModifyDBLocalSpecManually(mw func(ModifyDBLocalSpecManuallyFunc) ModifyDBLocalSpecManuallyFunc) DataSourceServiceDecorator {
	b._ModifyDBLocalSpecManually = mw(b._ModifyDBLocalSpecManually)
	return b
}

// ModifyDBLocalSpecManually implement DataSourceService's method ModifyDBLocalSpecManually
func (b *_DataSourceServiceDecorator) ModifyDBLocalSpecManually(arg0 context.Context, arg1 *ModifyDBLocalSpecManuallyReq) (ret0 *ModifyDBLocalSpecManuallyResp, ret1 error) {
	return b._ModifyDBLocalSpecManually(arg0, arg1)
}

// ValidateDryRunFunc alias for method ValidateDryRun
type ValidateDryRunFunc func(arg0 context.Context, arg1 *ValidateDryRunReq) (ret0 *shared.ValidateResponse)

// DecorateValidateDryRun decorate method ValidateDryRun
func (b *_DataSourceServiceDecorator) DecorateValidateDryRun(mw func(ValidateDryRunFunc) ValidateDryRunFunc) DataSourceServiceDecorator {
	b._ValidateDryRun = mw(b._ValidateDryRun)
	return b
}

// ValidateDryRun implement DataSourceService's method ValidateDryRun
func (b *_DataSourceServiceDecorator) ValidateDryRun(arg0 context.Context, arg1 *ValidateDryRunReq) (ret0 *shared.ValidateResponse) {
	return b._ValidateDryRun(arg0, arg1)
}

// ValidateOriginalTableFunc alias for method ValidateOriginalTable
type ValidateOriginalTableFunc func(arg0 context.Context, arg1 *ValidateOriginalTableReq) (ret0 *shared.ValidateResponse)

// DecorateValidateOriginalTable decorate method ValidateOriginalTable
func (b *_DataSourceServiceDecorator) DecorateValidateOriginalTable(mw func(ValidateOriginalTableFunc) ValidateOriginalTableFunc) DataSourceServiceDecorator {
	b._ValidateOriginalTable = mw(b._ValidateOriginalTable)
	return b
}

// ValidateOriginalTable implement DataSourceService's method ValidateOriginalTable
func (b *_DataSourceServiceDecorator) ValidateOriginalTable(arg0 context.Context, arg1 *ValidateOriginalTableReq) (ret0 *shared.ValidateResponse) {
	return b._ValidateOriginalTable(arg0, arg1)
}

// ValidateUniqIndexFunc alias for method ValidateUniqIndex
type ValidateUniqIndexFunc func(arg0 context.Context, arg1 *ValidateUniqIndexReq) (ret0 *shared.ValidateResponse)

// DecorateValidateUniqIndex decorate method ValidateUniqIndex
func (b *_DataSourceServiceDecorator) DecorateValidateUniqIndex(mw func(ValidateUniqIndexFunc) ValidateUniqIndexFunc) DataSourceServiceDecorator {
	b._ValidateUniqIndex = mw(b._ValidateUniqIndex)
	return b
}

// ValidateUniqIndex implement DataSourceService's method ValidateUniqIndex
func (b *_DataSourceServiceDecorator) ValidateUniqIndex(arg0 context.Context, arg1 *ValidateUniqIndexReq) (ret0 *shared.ValidateResponse) {
	return b._ValidateUniqIndex(arg0, arg1)
}

// ExecuteSqlFunc alias for method ExecuteSql
type ExecuteSqlFunc func(arg0 context.Context, arg1 *ExecuteReq) (ret0 error)

// DecorateExecuteSql decorate method ExecuteSql
func (b *_DataSourceServiceDecorator) DecorateExecuteSql(mw func(ExecuteSqlFunc) ExecuteSqlFunc) DataSourceServiceDecorator {
	b._ExecuteSql = mw(b._ExecuteSql)
	return b
}

// ExecuteSql implement DataSourceService's method ExecuteSql
func (b *_DataSourceServiceDecorator) ExecuteSql(arg0 context.Context, arg1 *ExecuteReq) (ret0 error) {
	return b._ExecuteSql(arg0, arg1)
}

// GetCurrentMaxConnectionsFunc alias for method GetCurrentMaxConnections
type GetCurrentMaxConnectionsFunc func(arg0 context.Context, arg1 *GetCurrentMaxConnectionsReq) (ret0 int, ret1 error)

// DecorateGetCurrentMaxConnections decorate method GetCurrentMaxConnections
func (b *_DataSourceServiceDecorator) DecorateGetCurrentMaxConnections(mw func(GetCurrentMaxConnectionsFunc) GetCurrentMaxConnectionsFunc) DataSourceServiceDecorator {
	b._GetCurrentMaxConnections = mw(b._GetCurrentMaxConnections)
	return b
}

// GetCurrentMaxConnections implement DataSourceService's method GetCurrentMaxConnections
func (b *_DataSourceServiceDecorator) GetCurrentMaxConnections(arg0 context.Context, arg1 *GetCurrentMaxConnectionsReq) (ret0 int, ret1 error) {
	return b._GetCurrentMaxConnections(arg0, arg1)
}

// GetInstanceSlaveAddressFunc alias for method GetInstanceSlaveAddress
type GetInstanceSlaveAddressFunc func(arg0 types.Context, arg1 *GetInstanceSlaveAddressReq) (ret0 *GetInstanceSlaveAddressResp, ret1 error)

// DecorateGetInstanceSlaveAddress decorate method GetInstanceSlaveAddress
func (b *_DataSourceServiceDecorator) DecorateGetInstanceSlaveAddress(mw func(GetInstanceSlaveAddressFunc) GetInstanceSlaveAddressFunc) DataSourceServiceDecorator {
	b._GetInstanceSlaveAddress = mw(b._GetInstanceSlaveAddress)
	return b
}

// GetInstanceSlaveAddress implement DataSourceService's method GetInstanceSlaveAddress
func (b *_DataSourceServiceDecorator) GetInstanceSlaveAddress(arg0 types.Context, arg1 *GetInstanceSlaveAddressReq) (ret0 *GetInstanceSlaveAddressResp, ret1 error) {
	return b._GetInstanceSlaveAddress(arg0, arg1)
}

// DescribeDBInstanceDetailForPilotFunc alias for method DescribeDBInstanceDetailForPilot
type DescribeDBInstanceDetailForPilotFunc func(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 string, ret1 error)

// DecorateDescribeDBInstanceDetailForPilot decorate method DescribeDBInstanceDetailForPilot
func (b *_DataSourceServiceDecorator) DecorateDescribeDBInstanceDetailForPilot(mw func(DescribeDBInstanceDetailForPilotFunc) DescribeDBInstanceDetailForPilotFunc) DataSourceServiceDecorator {
	b._DescribeDBInstanceDetailForPilot = mw(b._DescribeDBInstanceDetailForPilot)
	return b
}

// DescribeDBInstanceDetailForPilot implement DataSourceService's method DescribeDBInstanceDetailForPilot
func (b *_DataSourceServiceDecorator) DescribeDBInstanceDetailForPilot(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 string, ret1 error) {
	return b._DescribeDBInstanceDetailForPilot(arg0, arg1)
}

// DescribeDBInstanceParametersForPilotFunc alias for method DescribeDBInstanceParametersForPilot
type DescribeDBInstanceParametersForPilotFunc func(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 string, ret1 error)

// DecorateDescribeDBInstanceParametersForPilot decorate method DescribeDBInstanceParametersForPilot
func (b *_DataSourceServiceDecorator) DecorateDescribeDBInstanceParametersForPilot(mw func(DescribeDBInstanceParametersForPilotFunc) DescribeDBInstanceParametersForPilotFunc) DataSourceServiceDecorator {
	b._DescribeDBInstanceParametersForPilot = mw(b._DescribeDBInstanceParametersForPilot)
	return b
}

// DescribeDBInstanceParametersForPilot implement DataSourceService's method DescribeDBInstanceParametersForPilot
func (b *_DataSourceServiceDecorator) DescribeDBInstanceParametersForPilot(arg0 context.Context, arg1 *DescribeDBInstanceDetailReq) (ret0 string, ret1 error) {
	return b._DescribeDBInstanceParametersForPilot(arg0, arg1)
}

// GetCreateTableInfoFunc alias for method GetCreateTableInfo
type GetCreateTableInfoFunc func(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 string, ret1 error)

// DecorateGetCreateTableInfo decorate method GetCreateTableInfo
func (b *_DataSourceServiceDecorator) DecorateGetCreateTableInfo(mw func(GetCreateTableInfoFunc) GetCreateTableInfoFunc) DataSourceServiceDecorator {
	b._GetCreateTableInfo = mw(b._GetCreateTableInfo)
	return b
}

// GetCreateTableInfo implement DataSourceService's method GetCreateTableInfo
func (b *_DataSourceServiceDecorator) GetCreateTableInfo(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 string, ret1 error) {
	return b._GetCreateTableInfo(arg0, arg1, arg2)
}

// CheckAccountPrivilegeFunc alias for method CheckAccountPrivilege
type CheckAccountPrivilegeFunc func(arg0 context.Context, arg1 *CheckDBWAccountReq) (ret0 bool, ret1 error)

// DecorateCheckAccountPrivilege decorate method CheckAccountPrivilege
func (b *_DataSourceServiceDecorator) DecorateCheckAccountPrivilege(mw func(CheckAccountPrivilegeFunc) CheckAccountPrivilegeFunc) DataSourceServiceDecorator {
	b._CheckAccountPrivilege = mw(b._CheckAccountPrivilege)
	return b
}

// CheckAccountPrivilege implement DataSourceService's method CheckAccountPrivilege
func (b *_DataSourceServiceDecorator) CheckAccountPrivilege(arg0 context.Context, arg1 *CheckDBWAccountReq) (ret0 bool, ret1 error) {
	return b._CheckAccountPrivilege(arg0, arg1)
}

// ResetAccountFunc alias for method ResetAccount
type ResetAccountFunc func(arg0 context.Context, arg1 string, arg2 shared.DataSourceType) (ret0 error)

// DecorateResetAccount decorate method ResetAccount
func (b *_DataSourceServiceDecorator) DecorateResetAccount(mw func(ResetAccountFunc) ResetAccountFunc) DataSourceServiceDecorator {
	b._ResetAccount = mw(b._ResetAccount)
	return b
}

// ResetAccount implement DataSourceService's method ResetAccount
func (b *_DataSourceServiceDecorator) ResetAccount(arg0 context.Context, arg1 string, arg2 shared.DataSourceType) (ret0 error) {
	return b._ResetAccount(arg0, arg1, arg2)
}

// GrantReplicationPrivilegeFunc alias for method GrantReplicationPrivilege
type GrantReplicationPrivilegeFunc func(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 error)

// DecorateGrantReplicationPrivilege decorate method GrantReplicationPrivilege
func (b *_DataSourceServiceDecorator) DecorateGrantReplicationPrivilege(mw func(GrantReplicationPrivilegeFunc) GrantReplicationPrivilegeFunc) DataSourceServiceDecorator {
	b._GrantReplicationPrivilege = mw(b._GrantReplicationPrivilege)
	return b
}

// GrantReplicationPrivilege implement DataSourceService's method GrantReplicationPrivilege
func (b *_DataSourceServiceDecorator) GrantReplicationPrivilege(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (ret0 error) {
	return b._GrantReplicationPrivilege(arg0, arg1, arg2)
}

// DescribeInstancePodAddressFunc alias for method DescribeInstancePodAddress
type DescribeInstancePodAddressFunc func(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 *DescribeInstanceAddressResp, ret1 error)

// DecorateDescribeInstancePodAddress decorate method DescribeInstancePodAddress
func (b *_DataSourceServiceDecorator) DecorateDescribeInstancePodAddress(mw func(DescribeInstancePodAddressFunc) DescribeInstancePodAddressFunc) DataSourceServiceDecorator {
	b._DescribeInstancePodAddress = mw(b._DescribeInstancePodAddress)
	return b
}

// DescribeInstancePodAddress implement DataSourceService's method DescribeInstancePodAddress
func (b *_DataSourceServiceDecorator) DescribeInstancePodAddress(arg0 context.Context, arg1 *DescribeInstanceAddressReq) (ret0 *DescribeInstanceAddressResp, ret1 error) {
	return b._DescribeInstancePodAddress(arg0, arg1)
}

type DataSourceServiceCommonFunc interface {
	Call([]interface{}) []interface{}
	InTypes() []reflect.Type
	OutTypes() []reflect.Type
}

func newDataSourceServiceCommonFunc(fn func([]interface{}) []interface{}, in, out []reflect.Type) DataSourceServiceCommonFunc {
	return &_DataSourceServiceCommonFunc{call: fn, inTypes: in, outTypes: out}
}

type _DataSourceServiceCommonFunc struct {
	call     func([]interface{}) []interface{}
	inTypes  []reflect.Type
	outTypes []reflect.Type
}

func (f *_DataSourceServiceCommonFunc) Call(in []interface{}) []interface{} { return f.call(in) }

func (f *_DataSourceServiceCommonFunc) InTypes() []reflect.Type { return f.inTypes }

func (f *_DataSourceServiceCommonFunc) OutTypes() []reflect.Type { return f.outTypes }

func (b *_DataSourceServiceDecorator) mapValuesByType(types []reflect.Type, in []reflect.Value) []interface{} {
	args := make([]interface{}, len(in))
	for i, v := range in {
		if !v.IsValid() {
			args[i] = reflect.Zero(types[i])
		} else {
			args[i] = v.Interface()
		}
	}
	return args
}

func (b *_DataSourceServiceDecorator) mapInterfacesByType(types []reflect.Type, in []interface{}) []reflect.Value {
	args := make([]reflect.Value, len(in))
	for i, v := range in {
		args[i] = reflect.ValueOf(v)
		if !args[i].IsValid() {
			args[i] = reflect.Zero(types[i])
		}
	}
	return args
}

func (b *_DataSourceServiceDecorator) outTypes(fn reflect.Type) []reflect.Type {
	args := make([]reflect.Type, fn.NumOut())
	for i := 0; i < fn.NumOut(); i++ {
		args[i] = fn.Out(i)
	}
	return args
}

func (b *_DataSourceServiceDecorator) inTypes(fn reflect.Type) []reflect.Type {
	args := make([]reflect.Type, fn.NumIn())
	for i := 0; i < fn.NumIn(); i++ {
		args[i] = fn.In(i)
	}
	return args
}

func (b *_DataSourceServiceDecorator) DecorateAny(match func(string, reflect.Type) bool, mw func(in []interface{}, next DataSourceServiceCommonFunc) []interface{}) DataSourceServiceDecorator {
	if nextFn := reflect.ValueOf(b._Type); match("Type", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._Type = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(TypeFunc)
	}
	if nextFn := reflect.ValueOf(b._AddWhiteList); match("AddWhiteList", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._AddWhiteList = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(AddWhiteListFunc)
	}
	if nextFn := reflect.ValueOf(b._UpdateWhiteList); match("UpdateWhiteList", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._UpdateWhiteList = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(UpdateWhiteListFunc)
	}
	if nextFn := reflect.ValueOf(b._RemoveWhiteList); match("RemoveWhiteList", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._RemoveWhiteList = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(RemoveWhiteListFunc)
	}
	if nextFn := reflect.ValueOf(b._FillDataSource); match("FillDataSource", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._FillDataSource = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(FillDataSourceFunc)
	}
	if nextFn := reflect.ValueOf(b._FillInnerDataSource); match("FillInnerDataSource", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._FillInnerDataSource = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(FillInnerDataSourceFunc)
	}
	if nextFn := reflect.ValueOf(b._CheckConn); match("CheckConn", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CheckConn = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CheckConnFunc)
	}
	if nextFn := reflect.ValueOf(b._CheckDataSource); match("CheckDataSource", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CheckDataSource = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CheckDataSourceFunc)
	}
	if nextFn := reflect.ValueOf(b._KillQuery); match("KillQuery", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._KillQuery = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(KillQueryFunc)
	}
	if nextFn := reflect.ValueOf(b._ListInstance); match("ListInstance", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListInstance = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListInstanceFunc)
	}
	if nextFn := reflect.ValueOf(b._ListInstanceLightWeight); match("ListInstanceLightWeight", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListInstanceLightWeight = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListInstanceLightWeightFunc)
	}
	if nextFn := reflect.ValueOf(b._ListDatabases); match("ListDatabases", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListDatabases = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListDatabasesFunc)
	}
	if nextFn := reflect.ValueOf(b._ListInstanceNodes); match("ListInstanceNodes", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListInstanceNodes = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListInstanceNodesFunc)
	}
	if nextFn := reflect.ValueOf(b._ListInstanceNodesOri); match("ListInstanceNodesOri", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListInstanceNodesOri = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListInstanceNodesOriFunc)
	}
	if nextFn := reflect.ValueOf(b._ListInstancePods); match("ListInstancePods", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListInstancePods = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListInstancePodsFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBInstanceDetail); match("DescribeDBInstanceDetail", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBInstanceDetail = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBInstanceDetailFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBInstanceSpec); match("DescribeDBInstanceSpec", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBInstanceSpec = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBInstanceSpecFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBInstanceEndpoints); match("DescribeDBInstanceEndpoints", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBInstanceEndpoints = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBInstanceEndpointsFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBInstanceShardInfos); match("DescribeDBInstanceShardInfos", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBInstanceShardInfos = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBInstanceShardInfosFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBInstanceCluster); match("DescribeDBInstanceCluster", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBInstanceCluster = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBInstanceClusterFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBInstanceAuditCollectedPod); match("DescribeDBInstanceAuditCollectedPod", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBInstanceAuditCollectedPod = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBInstanceAuditCollectedPodFunc)
	}
	if nextFn := reflect.ValueOf(b._OpenDBInstanceAuditLog); match("OpenDBInstanceAuditLog", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._OpenDBInstanceAuditLog = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(OpenDBInstanceAuditLogFunc)
	}
	if nextFn := reflect.ValueOf(b._CloseDBInstanceAuditLog); match("CloseDBInstanceAuditLog", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CloseDBInstanceAuditLog = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CloseDBInstanceAuditLogFunc)
	}
	if nextFn := reflect.ValueOf(b._CheckDBInstanceAuditLogStatus); match("CheckDBInstanceAuditLogStatus", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CheckDBInstanceAuditLogStatus = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CheckDBInstanceAuditLogStatusFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBProxyConfig); match("DescribeDBProxyConfig", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBProxyConfig = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBProxyConfigFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeInstanceFeatures); match("DescribeInstanceFeatures", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeInstanceFeatures = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeInstanceFeaturesFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBInstanceSSL); match("DescribeDBInstanceSSL", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBInstanceSSL = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBInstanceSSLFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeSQLCCLConfig); match("DescribeSQLCCLConfig", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeSQLCCLConfig = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeSQLCCLConfigFunc)
	}
	if nextFn := reflect.ValueOf(b._ModifySQLCCLConfig); match("ModifySQLCCLConfig", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ModifySQLCCLConfig = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ModifySQLCCLConfigFunc)
	}
	if nextFn := reflect.ValueOf(b._AddSQLCCLRule); match("AddSQLCCLRule", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._AddSQLCCLRule = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(AddSQLCCLRuleFunc)
	}
	if nextFn := reflect.ValueOf(b._ModifyProxyThrottleRule); match("ModifyProxyThrottleRule", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ModifyProxyThrottleRule = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ModifyProxyThrottleRuleFunc)
	}
	if nextFn := reflect.ValueOf(b._DeleteSQLCCLRule); match("DeleteSQLCCLRule", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DeleteSQLCCLRule = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DeleteSQLCCLRuleFunc)
	}
	if nextFn := reflect.ValueOf(b._FlushSQLCCLRule); match("FlushSQLCCLRule", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._FlushSQLCCLRule = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(FlushSQLCCLRuleFunc)
	}
	if nextFn := reflect.ValueOf(b._ListSQLCCLRules); match("ListSQLCCLRules", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListSQLCCLRules = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListSQLCCLRulesFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeSqlFingerPrintOrKeywords); match("DescribeSqlFingerPrintOrKeywords", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeSqlFingerPrintOrKeywords = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeSqlFingerPrintOrKeywordsFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeSqlType); match("DescribeSqlType", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeSqlType = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeSqlTypeFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeInstanceAddress); match("DescribeInstanceAddress", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeInstanceAddress = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeInstanceAddressFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeInstanceProxyAddress); match("DescribeInstanceProxyAddress", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeInstanceProxyAddress = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeInstanceProxyAddressFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeInstanceAddressList); match("DescribeInstanceAddressList", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeInstanceAddressList = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeInstanceAddressListFunc)
	}
	if nextFn := reflect.ValueOf(b._ListTables); match("ListTables", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListTables = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListTablesFunc)
	}
	if nextFn := reflect.ValueOf(b._ListAllTables); match("ListAllTables", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListAllTables = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListAllTablesFunc)
	}
	if nextFn := reflect.ValueOf(b._ListSchemaTables); match("ListSchemaTables", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListSchemaTables = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListSchemaTablesFunc)
	}
	if nextFn := reflect.ValueOf(b._ListTablesInfo); match("ListTablesInfo", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListTablesInfo = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListTablesInfoFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeTable); match("DescribeTable", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeTable = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeTableFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeAutoKillSessionConfig); match("DescribeAutoKillSessionConfig", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeAutoKillSessionConfig = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeAutoKillSessionConfigFunc)
	}
	if nextFn := reflect.ValueOf(b._ModifyAutoKillSessionConfig); match("ModifyAutoKillSessionConfig", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ModifyAutoKillSessionConfig = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ModifyAutoKillSessionConfigFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeInstanceVersion); match("DescribeInstanceVersion", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeInstanceVersion = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeInstanceVersionFunc)
	}
	if nextFn := reflect.ValueOf(b._ListErrLogs); match("ListErrLogs", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListErrLogs = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListErrLogsFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribePgTable); match("DescribePgTable", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribePgTable = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribePgTableFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeInstanceReplicaDelay); match("DescribeInstanceReplicaDelay", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeInstanceReplicaDelay = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeInstanceReplicaDelayFunc)
	}
	if nextFn := reflect.ValueOf(b._ListViews); match("ListViews", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListViews = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListViewsFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeView); match("DescribeView", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeView = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeViewFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeFunction); match("DescribeFunction", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeFunction = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeFunctionFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeProcedure); match("DescribeProcedure", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeProcedure = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeProcedureFunc)
	}
	if nextFn := reflect.ValueOf(b._ListFunctions); match("ListFunctions", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListFunctions = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListFunctionsFunc)
	}
	if nextFn := reflect.ValueOf(b._ListProcedures); match("ListProcedures", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListProcedures = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListProceduresFunc)
	}
	if nextFn := reflect.ValueOf(b._ListTriggers); match("ListTriggers", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListTriggers = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListTriggersFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeTLSConnectionInfo); match("DescribeTLSConnectionInfo", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeTLSConnectionInfo = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeTLSConnectionInfoFunc)
	}
	if nextFn := reflect.ValueOf(b._ListKeyNumbers); match("ListKeyNumbers", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListKeyNumbers = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListKeyNumbersFunc)
	}
	if nextFn := reflect.ValueOf(b._ListKeys); match("ListKeys", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListKeys = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListKeysFunc)
	}
	if nextFn := reflect.ValueOf(b._GetKey); match("GetKey", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetKey = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetKeyFunc)
	}
	if nextFn := reflect.ValueOf(b._ListKeyMembers); match("ListKeyMembers", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListKeyMembers = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListKeyMembersFunc)
	}
	if nextFn := reflect.ValueOf(b._ListAlterKVsCommands); match("ListAlterKVsCommands", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListAlterKVsCommands = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListAlterKVsCommandsFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeBigKeys); match("DescribeBigKeys", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeBigKeys = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeBigKeysFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeHotKeys); match("DescribeHotKeys", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeHotKeys = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeHotKeysFunc)
	}
	if nextFn := reflect.ValueOf(b._OpenTunnel); match("OpenTunnel", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._OpenTunnel = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(OpenTunnelFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeTrigger); match("DescribeTrigger", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeTrigger = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeTriggerFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeEvent); match("DescribeEvent", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeEvent = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeEventFunc)
	}
	if nextFn := reflect.ValueOf(b._ListEvents); match("ListEvents", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListEvents = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListEventsFunc)
	}
	if nextFn := reflect.ValueOf(b._CreateAccount); match("CreateAccount", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CreateAccount = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CreateAccountFunc)
	}
	if nextFn := reflect.ValueOf(b._CheckPrivilege); match("CheckPrivilege", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CheckPrivilege = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CheckPrivilegeFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeAccounts); match("DescribeAccounts", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeAccounts = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeAccountsFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeAccounts2); match("DescribeAccounts2", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeAccounts2 = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeAccounts2Func)
	}
	if nextFn := reflect.ValueOf(b._CreateAccountAndGrant); match("CreateAccountAndGrant", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CreateAccountAndGrant = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CreateAccountAndGrantFunc)
	}
	if nextFn := reflect.ValueOf(b._ModifyAccountPrivilege); match("ModifyAccountPrivilege", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ModifyAccountPrivilege = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ModifyAccountPrivilegeFunc)
	}
	if nextFn := reflect.ValueOf(b._GrantAccountPrivilege); match("GrantAccountPrivilege", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GrantAccountPrivilege = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GrantAccountPrivilegeFunc)
	}
	if nextFn := reflect.ValueOf(b._DeleteAccount); match("DeleteAccount", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DeleteAccount = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DeleteAccountFunc)
	}
	if nextFn := reflect.ValueOf(b._ListDatabasesWithAccount); match("ListDatabasesWithAccount", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListDatabasesWithAccount = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListDatabasesWithAccountFunc)
	}
	if nextFn := reflect.ValueOf(b._GetAdvice); match("GetAdvice", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetAdvice = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetAdviceFunc)
	}
	if nextFn := reflect.ValueOf(b._ListCharsets); match("ListCharsets", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListCharsets = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListCharsetsFunc)
	}
	if nextFn := reflect.ValueOf(b._ListCollations); match("ListCollations", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListCollations = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListCollationsFunc)
	}
	if nextFn := reflect.ValueOf(b._ListSchema); match("ListSchema", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListSchema = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListSchemaFunc)
	}
	if nextFn := reflect.ValueOf(b._ListSequence); match("ListSequence", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListSequence = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListSequenceFunc)
	}
	if nextFn := reflect.ValueOf(b._ListPgCollations); match("ListPgCollations", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListPgCollations = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListPgCollationsFunc)
	}
	if nextFn := reflect.ValueOf(b._ListPgUsers); match("ListPgUsers", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListPgUsers = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListPgUsersFunc)
	}
	if nextFn := reflect.ValueOf(b._ListTableSpaces); match("ListTableSpaces", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListTableSpaces = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListTableSpacesFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDialogDetails); match("DescribeDialogDetails", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDialogDetails = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDialogDetailsFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDialogStatistics); match("DescribeDialogStatistics", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDialogStatistics = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDialogStatisticsFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeEngineStatus); match("DescribeEngineStatus", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeEngineStatus = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeEngineStatusFunc)
	}
	if nextFn := reflect.ValueOf(b._KillProcess); match("KillProcess", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._KillProcess = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(KillProcessFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeTrxAndLocks); match("DescribeTrxAndLocks", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeTrxAndLocks = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeTrxAndLocksFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeLockCurrentWaits); match("DescribeLockCurrentWaits", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeLockCurrentWaits = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeLockCurrentWaitsFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDeadlock); match("DescribeDeadlock", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDeadlock = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDeadlockFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDeadlockDetect); match("DescribeDeadlockDetect", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDeadlockDetect = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDeadlockDetectFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDialogInfos); match("DescribeDialogInfos", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDialogInfos = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDialogInfosFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeCurrentConn); match("DescribeCurrentConn", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeCurrentConn = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeCurrentConnFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeTableSpace); match("DescribeTableSpace", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeTableSpace = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeTableSpaceFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeTableSpaceAutoIncr); match("DescribeTableSpaceAutoIncr", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeTableSpaceAutoIncr = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeTableSpaceAutoIncrFunc)
	}
	if nextFn := reflect.ValueOf(b._ConvertTableSpaceToModel); match("ConvertTableSpaceToModel", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ConvertTableSpaceToModel = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ConvertTableSpaceToModelFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeTableColumn); match("DescribeTableColumn", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeTableColumn = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeTableColumnFunc)
	}
	if nextFn := reflect.ValueOf(b._ConvertTableColumnToModel); match("ConvertTableColumnToModel", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ConvertTableColumnToModel = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ConvertTableColumnToModelFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeTableIndex); match("DescribeTableIndex", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeTableIndex = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeTableIndexFunc)
	}
	if nextFn := reflect.ValueOf(b._ConvertTableIndexToModel); match("ConvertTableIndexToModel", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ConvertTableIndexToModel = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ConvertTableIndexToModelFunc)
	}
	if nextFn := reflect.ValueOf(b._FormatDescribeStorageCapacityResp); match("FormatDescribeStorageCapacityResp", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._FormatDescribeStorageCapacityResp = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(FormatDescribeStorageCapacityRespFunc)
	}
	if nextFn := reflect.ValueOf(b._ExecuteCCL); match("ExecuteCCL", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ExecuteCCL = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ExecuteCCLFunc)
	}
	if nextFn := reflect.ValueOf(b._CCLShow); match("CCLShow", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CCLShow = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CCLShowFunc)
	}
	if nextFn := reflect.ValueOf(b._GetAvgSlowQueries); match("GetAvgSlowQueries", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetAvgSlowQueries = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetAvgSlowQueriesFunc)
	}
	if nextFn := reflect.ValueOf(b._GetDataPointCountSlowQueries); match("GetDataPointCountSlowQueries", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetDataPointCountSlowQueries = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetDataPointCountSlowQueriesFunc)
	}
	if nextFn := reflect.ValueOf(b._GetCpuMetricDetail); match("GetCpuMetricDetail", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetCpuMetricDetail = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetCpuMetricDetailFunc)
	}
	if nextFn := reflect.ValueOf(b._GetAvgCpuUsage); match("GetAvgCpuUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetAvgCpuUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetAvgCpuUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMinCpuUsage); match("GetMinCpuUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMinCpuUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMinCpuUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMaxCpuUsage); match("GetMaxCpuUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMaxCpuUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMaxCpuUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetCpuUsageMetricDetail); match("GetCpuUsageMetricDetail", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetCpuUsageMetricDetail = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetCpuUsageMetricDetailFunc)
	}
	if nextFn := reflect.ValueOf(b._DiagRootCauseALL); match("DiagRootCauseALL", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DiagRootCauseALL = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DiagRootCauseALLFunc)
	}
	if nextFn := reflect.ValueOf(b._DiagRootCauseYoYQoQ); match("DiagRootCauseYoYQoQ", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DiagRootCauseYoYQoQ = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DiagRootCauseYoYQoQFunc)
	}
	if nextFn := reflect.ValueOf(b._DiagRootCauseDiskMetrics); match("DiagRootCauseDiskMetrics", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DiagRootCauseDiskMetrics = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DiagRootCauseDiskMetricsFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMemMetricDetail); match("GetMemMetricDetail", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMemMetricDetail = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMemMetricDetailFunc)
	}
	if nextFn := reflect.ValueOf(b._GetAvgMemUsage); match("GetAvgMemUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetAvgMemUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetAvgMemUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMinMemUsage); match("GetMinMemUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMinMemUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMinMemUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMaxMemUsage); match("GetMaxMemUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMaxMemUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMaxMemUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMemUsageMetricDetail); match("GetMemUsageMetricDetail", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMemUsageMetricDetail = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMemUsageMetricDetailFunc)
	}
	if nextFn := reflect.ValueOf(b._GetAvgDiskUsage); match("GetAvgDiskUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetAvgDiskUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetAvgDiskUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMaxDiskUsage); match("GetMaxDiskUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMaxDiskUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMaxDiskUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMinDiskUsage); match("GetMinDiskUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMinDiskUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMinDiskUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetDiskAvailableDays); match("GetDiskAvailableDays", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetDiskAvailableDays = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetDiskAvailableDaysFunc)
	}
	if nextFn := reflect.ValueOf(b._GetDiskFutureSize); match("GetDiskFutureSize", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetDiskFutureSize = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetDiskFutureSizeFunc)
	}
	if nextFn := reflect.ValueOf(b._GetDiskMetricDetail); match("GetDiskMetricDetail", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetDiskMetricDetail = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetDiskMetricDetailFunc)
	}
	if nextFn := reflect.ValueOf(b._GetDiskUsageMetricDetail); match("GetDiskUsageMetricDetail", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetDiskUsageMetricDetail = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetDiskUsageMetricDetailFunc)
	}
	if nextFn := reflect.ValueOf(b._GetAvgQpsUsage); match("GetAvgQpsUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetAvgQpsUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetAvgQpsUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMaxQpsUsage); match("GetMaxQpsUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMaxQpsUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMaxQpsUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMinQpsUsage); match("GetMinQpsUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMinQpsUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMinQpsUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetQpsUsage); match("GetQpsUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetQpsUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetQpsUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetAvgTpsUsage); match("GetAvgTpsUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetAvgTpsUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetAvgTpsUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMaxTpsUsage); match("GetMaxTpsUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMaxTpsUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMaxTpsUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMinTpsUsage); match("GetMinTpsUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMinTpsUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMinTpsUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetTpsUsage); match("GetTpsUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetTpsUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetTpsUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetAvgConnectionUsage); match("GetAvgConnectionUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetAvgConnectionUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetAvgConnectionUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMaxConnectionUsage); match("GetMaxConnectionUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMaxConnectionUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMaxConnectionUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMinConnectionUsage); match("GetMinConnectionUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMinConnectionUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMinConnectionUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetConnectedRatioMetricDetail); match("GetConnectedRatioMetricDetail", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetConnectedRatioMetricDetail = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetConnectedRatioMetricDetailFunc)
	}
	if nextFn := reflect.ValueOf(b._GetSessionMetricDetail); match("GetSessionMetricDetail", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetSessionMetricDetail = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetSessionMetricDetailFunc)
	}
	if nextFn := reflect.ValueOf(b._GetAvgSessionUsage); match("GetAvgSessionUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetAvgSessionUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetAvgSessionUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMaxSessionUsage); match("GetMaxSessionUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMaxSessionUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMaxSessionUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMinSessionUsage); match("GetMinSessionUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMinSessionUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMinSessionUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetLatestDiskUsage); match("GetLatestDiskUsage", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetLatestDiskUsage = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetLatestDiskUsageFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInspectionCpuMetric); match("GetInspectionCpuMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInspectionCpuMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInspectionCpuMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInspectionMemMetric); match("GetInspectionMemMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInspectionMemMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInspectionMemMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInspectionDiskMetric); match("GetInspectionDiskMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInspectionDiskMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInspectionDiskMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInspectionQpsMetric); match("GetInspectionQpsMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInspectionQpsMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInspectionQpsMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInspectionTpsMetric); match("GetInspectionTpsMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInspectionTpsMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInspectionTpsMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInspectionConnectedMetric); match("GetInspectionConnectedMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInspectionConnectedMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInspectionConnectedMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInspectionConnRatioMetric); match("GetInspectionConnRatioMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInspectionConnRatioMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInspectionConnRatioMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInspectionBpHitMetric); match("GetInspectionBpHitMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInspectionBpHitMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInspectionBpHitMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInspectionOutputMetric); match("GetInspectionOutputMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInspectionOutputMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInspectionOutputMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInspectionInputMetric); match("GetInspectionInputMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInspectionInputMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInspectionInputMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._HealthSummary); match("HealthSummary", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._HealthSummary = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(HealthSummaryFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMonitorByMetric); match("GetMonitorByMetric", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMonitorByMetric = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMonitorByMetricFunc)
	}
	if nextFn := reflect.ValueOf(b._ListCollections); match("ListCollections", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListCollections = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListCollectionsFunc)
	}
	if nextFn := reflect.ValueOf(b._ListIndexs); match("ListIndexs", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListIndexs = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListIndexsFunc)
	}
	if nextFn := reflect.ValueOf(b._ListMongoDBs); match("ListMongoDBs", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListMongoDBs = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListMongoDBsFunc)
	}
	if nextFn := reflect.ValueOf(b._CreateFreeLockCorrectOrder); match("CreateFreeLockCorrectOrder", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CreateFreeLockCorrectOrder = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CreateFreeLockCorrectOrderFunc)
	}
	if nextFn := reflect.ValueOf(b._CreateFreeLockCorrectOrderDryRun); match("CreateFreeLockCorrectOrderDryRun", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CreateFreeLockCorrectOrderDryRun = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CreateFreeLockCorrectOrderDryRunFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeFreeLockCorrectOrders); match("DescribeFreeLockCorrectOrders", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeFreeLockCorrectOrders = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeFreeLockCorrectOrdersFunc)
	}
	if nextFn := reflect.ValueOf(b._StopFreeLockCorrectOrders); match("StopFreeLockCorrectOrders", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._StopFreeLockCorrectOrders = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(StopFreeLockCorrectOrdersFunc)
	}
	if nextFn := reflect.ValueOf(b._PreCheckFreeLockCorrectOrders); match("PreCheckFreeLockCorrectOrders", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._PreCheckFreeLockCorrectOrders = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(PreCheckFreeLockCorrectOrdersFunc)
	}
	if nextFn := reflect.ValueOf(b._GetDBInnerAddress); match("GetDBInnerAddress", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetDBInnerAddress = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetDBInnerAddressFunc)
	}
	if nextFn := reflect.ValueOf(b._ExecuteDQL); match("ExecuteDQL", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ExecuteDQL = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ExecuteDQLFunc)
	}
	if nextFn := reflect.ValueOf(b._ExecuteDMLAndGetAffectedRows); match("ExecuteDMLAndGetAffectedRows", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ExecuteDMLAndGetAffectedRows = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ExecuteDMLAndGetAffectedRowsFunc)
	}
	if nextFn := reflect.ValueOf(b._GetPartitionInfos); match("GetPartitionInfos", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetPartitionInfos = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetPartitionInfosFunc)
	}
	if nextFn := reflect.ValueOf(b._GetShardingDbType); match("GetShardingDbType", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetShardingDbType = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetShardingDbTypeFunc)
	}
	if nextFn := reflect.ValueOf(b._ExplainCommand); match("ExplainCommand", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ExplainCommand = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ExplainCommandFunc)
	}
	if nextFn := reflect.ValueOf(b._IsMyOwnInstance); match("IsMyOwnInstance", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._IsMyOwnInstance = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(IsMyOwnInstanceFunc)
	}
	if nextFn := reflect.ValueOf(b._CheckInstanceState); match("CheckInstanceState", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CheckInstanceState = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CheckInstanceStateFunc)
	}
	if nextFn := reflect.ValueOf(b._GetTableIndexInfo); match("GetTableIndexInfo", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetTableIndexInfo = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetTableIndexInfoFunc)
	}
	if nextFn := reflect.ValueOf(b._GetTableIndexValue); match("GetTableIndexValue", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetTableIndexValue = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetTableIndexValueFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMaxConnections); match("GetMaxConnections", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMaxConnections = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMaxConnectionsFunc)
	}
	if nextFn := reflect.ValueOf(b._GetCurrentBandwidth); match("GetCurrentBandwidth", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetCurrentBandwidth = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetCurrentBandwidthFunc)
	}
	if nextFn := reflect.ValueOf(b._BandwidthScale); match("BandwidthScale", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._BandwidthScale = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(BandwidthScaleFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMinBandwidth); match("GetMinBandwidth", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMinBandwidth = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMinBandwidthFunc)
	}
	if nextFn := reflect.ValueOf(b._GetMaxBandwidth); match("GetMaxBandwidth", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetMaxBandwidth = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetMaxBandwidthFunc)
	}
	if nextFn := reflect.ValueOf(b._GetDiskSize); match("GetDiskSize", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetDiskSize = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetDiskSizeFunc)
	}
	if nextFn := reflect.ValueOf(b._GetUsedSize); match("GetUsedSize", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetUsedSize = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetUsedSizeFunc)
	}
	if nextFn := reflect.ValueOf(b._GetCurrentMetricData); match("GetCurrentMetricData", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetCurrentMetricData = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetCurrentMetricDataFunc)
	}
	if nextFn := reflect.ValueOf(b._GetPreSecondMetricData); match("GetPreSecondMetricData", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetPreSecondMetricData = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetPreSecondMetricDataFunc)
	}
	if nextFn := reflect.ValueOf(b._GetPreSecondMetricDataByInstance); match("GetPreSecondMetricDataByInstance", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetPreSecondMetricDataByInstance = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetPreSecondMetricDataByInstanceFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeFullSQLLogConfig); match("DescribeFullSQLLogConfig", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeFullSQLLogConfig = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeFullSQLLogConfigFunc)
	}
	if nextFn := reflect.ValueOf(b._ModifyFullSQLLogConfig); match("ModifyFullSQLLogConfig", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ModifyFullSQLLogConfig = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ModifyFullSQLLogConfigFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeInstanceVariables); match("DescribeInstanceVariables", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeInstanceVariables = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeInstanceVariablesFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribePrimaryKeyRange); match("DescribePrimaryKeyRange", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribePrimaryKeyRange = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribePrimaryKeyRangeFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeSQLAdvisorTableMeta); match("DescribeSQLAdvisorTableMeta", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeSQLAdvisorTableMeta = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeSQLAdvisorTableMetaFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeSampleData); match("DescribeSampleData", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeSampleData = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeSampleDataFunc)
	}
	if nextFn := reflect.ValueOf(b._EnsureAccount); match("EnsureAccount", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._EnsureAccount = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(EnsureAccountFunc)
	}
	if nextFn := reflect.ValueOf(b._GetDatasourceAddress); match("GetDatasourceAddress", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetDatasourceAddress = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetDatasourceAddressFunc)
	}
	if nextFn := reflect.ValueOf(b._GetDBServiceTreeMountInfo); match("GetDBServiceTreeMountInfo", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetDBServiceTreeMountInfo = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetDBServiceTreeMountInfoFunc)
	}
	if nextFn := reflect.ValueOf(b._GetDBInstanceInfo); match("GetDBInstanceInfo", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetDBInstanceInfo = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetDBInstanceInfoFunc)
	}
	if nextFn := reflect.ValueOf(b._InstanceIsExist); match("InstanceIsExist", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._InstanceIsExist = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(InstanceIsExistFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInstanceTopo); match("GetInstanceTopo", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInstanceTopo = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInstanceTopoFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInstanceProxyTopo); match("GetInstanceProxyTopo", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInstanceProxyTopo = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInstanceProxyTopoFunc)
	}
	if nextFn := reflect.ValueOf(b._CreateLogDownloadTask); match("CreateLogDownloadTask", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CreateLogDownloadTask = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CreateLogDownloadTaskFunc)
	}
	if nextFn := reflect.ValueOf(b._GetLogDownloadList); match("GetLogDownloadList", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetLogDownloadList = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetLogDownloadListFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInstancePrimaryNodeId); match("GetInstancePrimaryNodeId", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInstancePrimaryNodeId = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInstancePrimaryNodeIdFunc)
	}
	if nextFn := reflect.ValueOf(b._ListSQLKillRules); match("ListSQLKillRules", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ListSQLKillRules = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ListSQLKillRulesFunc)
	}
	if nextFn := reflect.ValueOf(b._ModifySQLKillRule); match("ModifySQLKillRule", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ModifySQLKillRule = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ModifySQLKillRuleFunc)
	}
	if nextFn := reflect.ValueOf(b._GetManagedAccountAndPwd); match("GetManagedAccountAndPwd", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetManagedAccountAndPwd = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetManagedAccountAndPwdFunc)
	}
	if nextFn := reflect.ValueOf(b._CalculateSpecAfterScale); match("CalculateSpecAfterScale", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CalculateSpecAfterScale = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CalculateSpecAfterScaleFunc)
	}
	if nextFn := reflect.ValueOf(b._ModifyDBInstanceSpec); match("ModifyDBInstanceSpec", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ModifyDBInstanceSpec = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ModifyDBInstanceSpecFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBAutoScalingConfig); match("DescribeDBAutoScalingConfig", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBAutoScalingConfig = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBAutoScalingConfigFunc)
	}
	if nextFn := reflect.ValueOf(b._ModifyDBAutoScalingConfig); match("ModifyDBAutoScalingConfig", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ModifyDBAutoScalingConfig = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ModifyDBAutoScalingConfigFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBAutoScaleEvents); match("DescribeDBAutoScaleEvents", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBAutoScaleEvents = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBAutoScaleEventsFunc)
	}
	if nextFn := reflect.ValueOf(b._ModifyDBLocalSpecManually); match("ModifyDBLocalSpecManually", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ModifyDBLocalSpecManually = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ModifyDBLocalSpecManuallyFunc)
	}
	if nextFn := reflect.ValueOf(b._ValidateDryRun); match("ValidateDryRun", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ValidateDryRun = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ValidateDryRunFunc)
	}
	if nextFn := reflect.ValueOf(b._ValidateOriginalTable); match("ValidateOriginalTable", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ValidateOriginalTable = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ValidateOriginalTableFunc)
	}
	if nextFn := reflect.ValueOf(b._ValidateUniqIndex); match("ValidateUniqIndex", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ValidateUniqIndex = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ValidateUniqIndexFunc)
	}
	if nextFn := reflect.ValueOf(b._ExecuteSql); match("ExecuteSql", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ExecuteSql = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ExecuteSqlFunc)
	}
	if nextFn := reflect.ValueOf(b._GetCurrentMaxConnections); match("GetCurrentMaxConnections", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetCurrentMaxConnections = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetCurrentMaxConnectionsFunc)
	}
	if nextFn := reflect.ValueOf(b._GetInstanceSlaveAddress); match("GetInstanceSlaveAddress", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetInstanceSlaveAddress = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetInstanceSlaveAddressFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBInstanceDetailForPilot); match("DescribeDBInstanceDetailForPilot", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBInstanceDetailForPilot = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBInstanceDetailForPilotFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeDBInstanceParametersForPilot); match("DescribeDBInstanceParametersForPilot", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeDBInstanceParametersForPilot = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeDBInstanceParametersForPilotFunc)
	}
	if nextFn := reflect.ValueOf(b._GetCreateTableInfo); match("GetCreateTableInfo", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GetCreateTableInfo = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GetCreateTableInfoFunc)
	}
	if nextFn := reflect.ValueOf(b._CheckAccountPrivilege); match("CheckAccountPrivilege", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._CheckAccountPrivilege = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(CheckAccountPrivilegeFunc)
	}
	if nextFn := reflect.ValueOf(b._ResetAccount); match("ResetAccount", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._ResetAccount = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(ResetAccountFunc)
	}
	if nextFn := reflect.ValueOf(b._GrantReplicationPrivilege); match("GrantReplicationPrivilege", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._GrantReplicationPrivilege = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(GrantReplicationPrivilegeFunc)
	}
	if nextFn := reflect.ValueOf(b._DescribeInstancePodAddress); match("DescribeInstancePodAddress", nextFn.Type()) {
		outTyp := b.outTypes(nextFn.Type())
		inTyp := b.inTypes(nextFn.Type())
		b._DescribeInstancePodAddress = reflect.MakeFunc(nextFn.Type(), func(in []reflect.Value) []reflect.Value {
			return b.mapInterfacesByType(outTyp, mw(b.mapValuesByType(inTyp, in), newDataSourceServiceCommonFunc(func(a []interface{}) []interface{} {
				return b.mapValuesByType(outTyp, nextFn.Call(b.mapInterfacesByType(inTyp, a)))
			}, inTyp, outTyp)))
		}).Interface().(DescribeInstancePodAddressFunc)
	}

	return b
}

// Export as DataSourceService
func (b *_DataSourceServiceDecorator) Export() DataSourceService {
	return &_DataSourceServiceDecorator{
		_Type:                                 b._Type,
		_AddWhiteList:                         b._AddWhiteList,
		_UpdateWhiteList:                      b._UpdateWhiteList,
		_RemoveWhiteList:                      b._RemoveWhiteList,
		_FillDataSource:                       b._FillDataSource,
		_FillInnerDataSource:                  b._FillInnerDataSource,
		_CheckConn:                            b._CheckConn,
		_CheckDataSource:                      b._CheckDataSource,
		_KillQuery:                            b._KillQuery,
		_ListInstance:                         b._ListInstance,
		_ListInstanceLightWeight:              b._ListInstanceLightWeight,
		_ListDatabases:                        b._ListDatabases,
		_ListInstanceNodes:                    b._ListInstanceNodes,
		_ListInstanceNodesOri:                 b._ListInstanceNodesOri,
		_ListInstancePods:                     b._ListInstancePods,
		_DescribeDBInstanceDetail:             b._DescribeDBInstanceDetail,
		_DescribeDBInstanceSpec:               b._DescribeDBInstanceSpec,
		_DescribeDBInstanceEndpoints:          b._DescribeDBInstanceEndpoints,
		_DescribeDBInstanceShardInfos:         b._DescribeDBInstanceShardInfos,
		_DescribeDBInstanceCluster:            b._DescribeDBInstanceCluster,
		_DescribeDBInstanceAuditCollectedPod:  b._DescribeDBInstanceAuditCollectedPod,
		_OpenDBInstanceAuditLog:               b._OpenDBInstanceAuditLog,
		_CloseDBInstanceAuditLog:              b._CloseDBInstanceAuditLog,
		_CheckDBInstanceAuditLogStatus:        b._CheckDBInstanceAuditLogStatus,
		_DescribeDBProxyConfig:                b._DescribeDBProxyConfig,
		_DescribeInstanceFeatures:             b._DescribeInstanceFeatures,
		_DescribeDBInstanceSSL:                b._DescribeDBInstanceSSL,
		_DescribeSQLCCLConfig:                 b._DescribeSQLCCLConfig,
		_ModifySQLCCLConfig:                   b._ModifySQLCCLConfig,
		_AddSQLCCLRule:                        b._AddSQLCCLRule,
		_ModifyProxyThrottleRule:              b._ModifyProxyThrottleRule,
		_DeleteSQLCCLRule:                     b._DeleteSQLCCLRule,
		_FlushSQLCCLRule:                      b._FlushSQLCCLRule,
		_ListSQLCCLRules:                      b._ListSQLCCLRules,
		_DescribeSqlFingerPrintOrKeywords:     b._DescribeSqlFingerPrintOrKeywords,
		_DescribeSqlType:                      b._DescribeSqlType,
		_DescribeInstanceAddress:              b._DescribeInstanceAddress,
		_DescribeInstanceProxyAddress:         b._DescribeInstanceProxyAddress,
		_DescribeInstanceAddressList:          b._DescribeInstanceAddressList,
		_ListTables:                           b._ListTables,
		_ListAllTables:                        b._ListAllTables,
		_ListSchemaTables:                     b._ListSchemaTables,
		_ListTablesInfo:                       b._ListTablesInfo,
		_DescribeTable:                        b._DescribeTable,
		_DescribeAutoKillSessionConfig:        b._DescribeAutoKillSessionConfig,
		_ModifyAutoKillSessionConfig:          b._ModifyAutoKillSessionConfig,
		_DescribeInstanceVersion:              b._DescribeInstanceVersion,
		_ListErrLogs:                          b._ListErrLogs,
		_DescribePgTable:                      b._DescribePgTable,
		_DescribeInstanceReplicaDelay:         b._DescribeInstanceReplicaDelay,
		_ListViews:                            b._ListViews,
		_DescribeView:                         b._DescribeView,
		_DescribeFunction:                     b._DescribeFunction,
		_DescribeProcedure:                    b._DescribeProcedure,
		_ListFunctions:                        b._ListFunctions,
		_ListProcedures:                       b._ListProcedures,
		_ListTriggers:                         b._ListTriggers,
		_DescribeTLSConnectionInfo:            b._DescribeTLSConnectionInfo,
		_ListKeyNumbers:                       b._ListKeyNumbers,
		_ListKeys:                             b._ListKeys,
		_GetKey:                               b._GetKey,
		_ListKeyMembers:                       b._ListKeyMembers,
		_ListAlterKVsCommands:                 b._ListAlterKVsCommands,
		_DescribeBigKeys:                      b._DescribeBigKeys,
		_DescribeHotKeys:                      b._DescribeHotKeys,
		_OpenTunnel:                           b._OpenTunnel,
		_DescribeTrigger:                      b._DescribeTrigger,
		_DescribeEvent:                        b._DescribeEvent,
		_ListEvents:                           b._ListEvents,
		_CreateAccount:                        b._CreateAccount,
		_CheckPrivilege:                       b._CheckPrivilege,
		_DescribeAccounts:                     b._DescribeAccounts,
		_DescribeAccounts2:                    b._DescribeAccounts2,
		_CreateAccountAndGrant:                b._CreateAccountAndGrant,
		_ModifyAccountPrivilege:               b._ModifyAccountPrivilege,
		_GrantAccountPrivilege:                b._GrantAccountPrivilege,
		_DeleteAccount:                        b._DeleteAccount,
		_ListDatabasesWithAccount:             b._ListDatabasesWithAccount,
		_GetAdvice:                            b._GetAdvice,
		_ListCharsets:                         b._ListCharsets,
		_ListCollations:                       b._ListCollations,
		_ListSchema:                           b._ListSchema,
		_ListSequence:                         b._ListSequence,
		_ListPgCollations:                     b._ListPgCollations,
		_ListPgUsers:                          b._ListPgUsers,
		_ListTableSpaces:                      b._ListTableSpaces,
		_DescribeDialogDetails:                b._DescribeDialogDetails,
		_DescribeDialogStatistics:             b._DescribeDialogStatistics,
		_DescribeEngineStatus:                 b._DescribeEngineStatus,
		_KillProcess:                          b._KillProcess,
		_DescribeTrxAndLocks:                  b._DescribeTrxAndLocks,
		_DescribeLockCurrentWaits:             b._DescribeLockCurrentWaits,
		_DescribeDeadlock:                     b._DescribeDeadlock,
		_DescribeDeadlockDetect:               b._DescribeDeadlockDetect,
		_DescribeDialogInfos:                  b._DescribeDialogInfos,
		_DescribeCurrentConn:                  b._DescribeCurrentConn,
		_DescribeTableSpace:                   b._DescribeTableSpace,
		_DescribeTableSpaceAutoIncr:           b._DescribeTableSpaceAutoIncr,
		_ConvertTableSpaceToModel:             b._ConvertTableSpaceToModel,
		_DescribeTableColumn:                  b._DescribeTableColumn,
		_ConvertTableColumnToModel:            b._ConvertTableColumnToModel,
		_DescribeTableIndex:                   b._DescribeTableIndex,
		_ConvertTableIndexToModel:             b._ConvertTableIndexToModel,
		_FormatDescribeStorageCapacityResp:    b._FormatDescribeStorageCapacityResp,
		_ExecuteCCL:                           b._ExecuteCCL,
		_CCLShow:                              b._CCLShow,
		_GetAvgSlowQueries:                    b._GetAvgSlowQueries,
		_GetDataPointCountSlowQueries:         b._GetDataPointCountSlowQueries,
		_GetCpuMetricDetail:                   b._GetCpuMetricDetail,
		_GetAvgCpuUsage:                       b._GetAvgCpuUsage,
		_GetMinCpuUsage:                       b._GetMinCpuUsage,
		_GetMaxCpuUsage:                       b._GetMaxCpuUsage,
		_GetCpuUsageMetricDetail:              b._GetCpuUsageMetricDetail,
		_DiagRootCauseALL:                     b._DiagRootCauseALL,
		_DiagRootCauseYoYQoQ:                  b._DiagRootCauseYoYQoQ,
		_DiagRootCauseDiskMetrics:             b._DiagRootCauseDiskMetrics,
		_GetMemMetricDetail:                   b._GetMemMetricDetail,
		_GetAvgMemUsage:                       b._GetAvgMemUsage,
		_GetMinMemUsage:                       b._GetMinMemUsage,
		_GetMaxMemUsage:                       b._GetMaxMemUsage,
		_GetMemUsageMetricDetail:              b._GetMemUsageMetricDetail,
		_GetAvgDiskUsage:                      b._GetAvgDiskUsage,
		_GetMaxDiskUsage:                      b._GetMaxDiskUsage,
		_GetMinDiskUsage:                      b._GetMinDiskUsage,
		_GetDiskAvailableDays:                 b._GetDiskAvailableDays,
		_GetDiskFutureSize:                    b._GetDiskFutureSize,
		_GetDiskMetricDetail:                  b._GetDiskMetricDetail,
		_GetDiskUsageMetricDetail:             b._GetDiskUsageMetricDetail,
		_GetAvgQpsUsage:                       b._GetAvgQpsUsage,
		_GetMaxQpsUsage:                       b._GetMaxQpsUsage,
		_GetMinQpsUsage:                       b._GetMinQpsUsage,
		_GetQpsUsage:                          b._GetQpsUsage,
		_GetAvgTpsUsage:                       b._GetAvgTpsUsage,
		_GetMaxTpsUsage:                       b._GetMaxTpsUsage,
		_GetMinTpsUsage:                       b._GetMinTpsUsage,
		_GetTpsUsage:                          b._GetTpsUsage,
		_GetAvgConnectionUsage:                b._GetAvgConnectionUsage,
		_GetMaxConnectionUsage:                b._GetMaxConnectionUsage,
		_GetMinConnectionUsage:                b._GetMinConnectionUsage,
		_GetConnectedRatioMetricDetail:        b._GetConnectedRatioMetricDetail,
		_GetSessionMetricDetail:               b._GetSessionMetricDetail,
		_GetAvgSessionUsage:                   b._GetAvgSessionUsage,
		_GetMaxSessionUsage:                   b._GetMaxSessionUsage,
		_GetMinSessionUsage:                   b._GetMinSessionUsage,
		_GetLatestDiskUsage:                   b._GetLatestDiskUsage,
		_GetInspectionCpuMetric:               b._GetInspectionCpuMetric,
		_GetInspectionMemMetric:               b._GetInspectionMemMetric,
		_GetInspectionDiskMetric:              b._GetInspectionDiskMetric,
		_GetInspectionQpsMetric:               b._GetInspectionQpsMetric,
		_GetInspectionTpsMetric:               b._GetInspectionTpsMetric,
		_GetInspectionConnectedMetric:         b._GetInspectionConnectedMetric,
		_GetInspectionConnRatioMetric:         b._GetInspectionConnRatioMetric,
		_GetInspectionBpHitMetric:             b._GetInspectionBpHitMetric,
		_GetInspectionOutputMetric:            b._GetInspectionOutputMetric,
		_GetInspectionInputMetric:             b._GetInspectionInputMetric,
		_HealthSummary:                        b._HealthSummary,
		_GetMonitorByMetric:                   b._GetMonitorByMetric,
		_ListCollections:                      b._ListCollections,
		_ListIndexs:                           b._ListIndexs,
		_ListMongoDBs:                         b._ListMongoDBs,
		_CreateFreeLockCorrectOrder:           b._CreateFreeLockCorrectOrder,
		_CreateFreeLockCorrectOrderDryRun:     b._CreateFreeLockCorrectOrderDryRun,
		_DescribeFreeLockCorrectOrders:        b._DescribeFreeLockCorrectOrders,
		_StopFreeLockCorrectOrders:            b._StopFreeLockCorrectOrders,
		_PreCheckFreeLockCorrectOrders:        b._PreCheckFreeLockCorrectOrders,
		_GetDBInnerAddress:                    b._GetDBInnerAddress,
		_ExecuteDQL:                           b._ExecuteDQL,
		_ExecuteDMLAndGetAffectedRows:         b._ExecuteDMLAndGetAffectedRows,
		_GetPartitionInfos:                    b._GetPartitionInfos,
		_GetShardingDbType:                    b._GetShardingDbType,
		_ExplainCommand:                       b._ExplainCommand,
		_IsMyOwnInstance:                      b._IsMyOwnInstance,
		_CheckInstanceState:                   b._CheckInstanceState,
		_GetTableIndexInfo:                    b._GetTableIndexInfo,
		_GetTableIndexValue:                   b._GetTableIndexValue,
		_GetMaxConnections:                    b._GetMaxConnections,
		_GetCurrentBandwidth:                  b._GetCurrentBandwidth,
		_BandwidthScale:                       b._BandwidthScale,
		_GetMinBandwidth:                      b._GetMinBandwidth,
		_GetMaxBandwidth:                      b._GetMaxBandwidth,
		_GetDiskSize:                          b._GetDiskSize,
		_GetUsedSize:                          b._GetUsedSize,
		_GetCurrentMetricData:                 b._GetCurrentMetricData,
		_GetPreSecondMetricData:               b._GetPreSecondMetricData,
		_GetPreSecondMetricDataByInstance:     b._GetPreSecondMetricDataByInstance,
		_DescribeFullSQLLogConfig:             b._DescribeFullSQLLogConfig,
		_ModifyFullSQLLogConfig:               b._ModifyFullSQLLogConfig,
		_DescribeInstanceVariables:            b._DescribeInstanceVariables,
		_DescribePrimaryKeyRange:              b._DescribePrimaryKeyRange,
		_DescribeSQLAdvisorTableMeta:          b._DescribeSQLAdvisorTableMeta,
		_DescribeSampleData:                   b._DescribeSampleData,
		_EnsureAccount:                        b._EnsureAccount,
		_GetDatasourceAddress:                 b._GetDatasourceAddress,
		_GetDBServiceTreeMountInfo:            b._GetDBServiceTreeMountInfo,
		_GetDBInstanceInfo:                    b._GetDBInstanceInfo,
		_InstanceIsExist:                      b._InstanceIsExist,
		_GetInstanceTopo:                      b._GetInstanceTopo,
		_GetInstanceProxyTopo:                 b._GetInstanceProxyTopo,
		_CreateLogDownloadTask:                b._CreateLogDownloadTask,
		_GetLogDownloadList:                   b._GetLogDownloadList,
		_GetInstancePrimaryNodeId:             b._GetInstancePrimaryNodeId,
		_ListSQLKillRules:                     b._ListSQLKillRules,
		_ModifySQLKillRule:                    b._ModifySQLKillRule,
		_GetManagedAccountAndPwd:              b._GetManagedAccountAndPwd,
		_CalculateSpecAfterScale:              b._CalculateSpecAfterScale,
		_ModifyDBInstanceSpec:                 b._ModifyDBInstanceSpec,
		_DescribeDBAutoScalingConfig:          b._DescribeDBAutoScalingConfig,
		_ModifyDBAutoScalingConfig:            b._ModifyDBAutoScalingConfig,
		_DescribeDBAutoScaleEvents:            b._DescribeDBAutoScaleEvents,
		_ModifyDBLocalSpecManually:            b._ModifyDBLocalSpecManually,
		_ValidateDryRun:                       b._ValidateDryRun,
		_ValidateOriginalTable:                b._ValidateOriginalTable,
		_ValidateUniqIndex:                    b._ValidateUniqIndex,
		_ExecuteSql:                           b._ExecuteSql,
		_GetCurrentMaxConnections:             b._GetCurrentMaxConnections,
		_GetInstanceSlaveAddress:              b._GetInstanceSlaveAddress,
		_DescribeDBInstanceDetailForPilot:     b._DescribeDBInstanceDetailForPilot,
		_DescribeDBInstanceParametersForPilot: b._DescribeDBInstanceParametersForPilot,
		_GetCreateTableInfo:                   b._GetCreateTableInfo,
		_CheckAccountPrivilege:                b._CheckAccountPrivilege,
		_ResetAccount:                         b._ResetAccount,
		_GrantReplicationPrivilege:            b._GrantReplicationPrivilege,
		_DescribeInstancePodAddress:           b._DescribeInstancePodAddress,
	}
}
