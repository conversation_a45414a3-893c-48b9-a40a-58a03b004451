package mysql

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"crypto/md5"
	"database/sql"
	gosql "database/sql"
	"encoding/hex"
	"fmt"
	"github.com/openark/golib/sqlutils"
	"strconv"
	"strings"

	"code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
	"code.byted.org/infcs/ds-sql-parser/format"
	paser_model "code.byted.org/infcs/ds-sql-parser/model"
	_ "github.com/pingcap/tidb/types/parser_driver"
)

const DbwConsoleDefaultHint = " /*+ DBW SQL CONSOLE DEFAULT*/ "

func (self *mysqlImpl) ValidateDryRun(ctx context.Context, req *datasource.ValidateDryRunReq) *shared.ValidateResponse {
	myDb, err := gosql.Open("mysql", GetDBUri(req.Source))
	res := &shared.ValidateResponse{ItemCn: datasource.OnlineDDLDryCn, ItemEn: model.PreCheckItem_OnlineDDLDry.String(), Success: datasource.ValidateSuccess}
	res.ItemDetail = []*shared.ItemDetail{{SqlText: "-", Result: "success"}}
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		res.Success = datasource.ValidateError
		res.Result = "connect to database error, please check user/password in security control"
		res.ItemDetail[0].Result = "connect to database error, please check user/password in security control"
		return res
	}
	defer myDb.Close()

	if err = self.validateGrants(ctx, myDb, req.Source); err != nil {
		res.Success = datasource.ValidateError
		res.Result = err.Error()
		res.ItemDetail[0].Result = err.Error()
		return res
	}

	if err = self.validateBinlogs(ctx, myDb, req.Source); err != nil {
		res.Success = datasource.ValidateError
		res.Result = err.Error()
		res.ItemDetail[0].Result = err.Error()
		return res
	}

	_, err = myDb.Exec(fmt.Sprintf("use `%s`", req.Source.Db))
	if err != nil {
		res.Success = datasource.ValidateError
		res.Result = "dry run check error:%s " + err.Error()
		res.ItemDetail[0].Result = "dry run check error:%s " + err.Error()
		return res
	}

	self.validateSqlExecute(ctx, myDb, req, res)

	return res
}

func (self *mysqlImpl) validateSqlExecute(ctx context.Context, myDb *gosql.DB, req *datasource.ValidateDryRunReq, res *shared.ValidateResponse) {
	gId, _ := self.IdSvc.NextID(ctx)
	gmd5 := Generate6MD5(gId)
	var itemDetails []*shared.ItemDetail
	copyTableMap := make(map[string]bool)
	for _, sqlInfo := range req.SqlList {
		item := &shared.ItemDetail{SqlText: sqlInfo.SqlText, Result: "success"}
		itemDetails = append(itemDetails, item)
		if len(sqlInfo.TableNames) < 0 {
			continue
		}
		if !isAlterTableSql(sqlInfo.SqlText) {
			continue
		}
		tableName := sqlInfo.TableNames[0]
		copyTableName := fmt.Sprintf("~%s_%s_gho", tableName, gmd5)

		copyAlterSql, err := renameTableInSql(sqlInfo.SqlText, copyTableName)
		if err != nil {
			log.Warn(ctx, "renameTableInSql error: %v", err)
			continue
		}
		if !strings.Contains(copyAlterSql, copyTableName) {
			// 保险起见，检查修改后的alter sql表名
			continue
		}
		// 用create 是为了保障我们百分百下面操作的是我们自己创建的影子表
		createCopySql := fmt.Sprintf("create table `%s` like `%s` ", copyTableName, tableName)
		_, err = myDb.Exec(createCopySql)
		if err != nil && !copyTableMap[copyTableName] {
			// 如果这个表的影子表创建失败了，且这个表的影子表我们没有创建过，就不检查这个了。
			log.Warn(ctx, "copyTableName: %s,  create copy table error: %s", createCopySql, err.Error())
			continue
		}
		copyTableMap[copyTableName] = true
		_, err = myDb.Exec(copyAlterSql)
		if err != nil {
			res.Success = datasource.ValidateError
			item.Result = err.Error()
		}
		// 检查这个sql是否是删索引的
		isDropSql, indexInfo := self.DescribeDropIndexSql(item.SqlText)
		if !isDropSql {
			// 不是drop index就走下一条
			continue
		}
		success, result := self.ValidateDropIndex(ctx, myDb, req.Source.Db, indexInfo)
		if success {
			continue
		}
		// 检查删索引没成功，说明不满足删除主键/唯一索引条件
		res.Success = datasource.ValidateError
		item.Result = result
	}
	self.dropAllCopyTable(ctx, myDb, req.Source.Db, copyTableMap)
	res.ItemDetail = itemDetails
}

func (self *mysqlImpl) dropAllCopyTable(ctx context.Context, myDb *gosql.DB, dbName string, copyTableMap map[string]bool) {
	for copyTableName, _ := range copyTableMap {
		dropSql := fmt.Sprintf("drop table if exists `%s`.`%s`", dbName, copyTableName)
		// 兜底，我们自己的表，一定是_gho结尾的
		if !endsWithGho(copyTableName) {
			continue
		}
		_, err := myDb.Exec(dropSql)
		if err != nil {
			log.Warn(ctx, "``.`` delete error, drop sql:%s, error:%v", dbName, copyTableName, dropSql, err)
		}
	}
}

func endsWithGho(s string) bool {
	// 1. 去除字符串首尾的空格
	trimmed := strings.TrimSpace(s)

	// 2. 长度不足直接返回 false
	if len(trimmed) < 4 {
		return false
	}

	// 3. 判断是否以 "_del" 结尾
	return strings.HasSuffix(trimmed, "_gho")
}

func renameTableInSql(sql string, newTableName string) (string, error) {
	// 初始化 TiDB 解析器
	p := parser.New()

	// 解析 SQL 语句
	stmtNodes, _, err := p.Parse(sql, "", "")
	if err != nil {
		return "", fmt.Errorf("parse error: %v", err)
	}

	// 确保只有一个语句
	if len(stmtNodes) != 1 {
		return "", fmt.Errorf("expected exactly one SQL statement")
	}

	// 类型断言检查是否为 ALTER TABLE 语句
	alterStmt, ok := stmtNodes[0].(*ast.AlterTableStmt)
	if !ok {
		return "", fmt.Errorf("not an ALTER TABLE statement")
	}

	// 修改表名逻辑

	fmt.Println(alterStmt.Table.Name.String())
	alterStmt.Table.Name = paser_model.NewCIStr(newTableName)

	// 生成新 SQL 语句
	var sb strings.Builder
	ctx := format.NewRestoreCtx(
		format.RestoreNameBackQuotes| // 保留反引号
			format.RestoreStringSingleQuotes| // 保留单引号
			format.RestoreKeyWordLowercase, // 保持小写关键字
		&sb,
	)

	if err = alterStmt.Restore(ctx); err != nil {
		return "", fmt.Errorf("restore error: %v", err)
	}

	return sb.String() + ";", nil // 补充可能丢失的分号
}

func isAlterTableSql(sql string) bool {
	p := parser.New()
	stmt, err := p.ParseOneStmt(sql, "", "")
	if err != nil {
		return false
	}

	// 检查语句类型是否为DDL
	switch stmt.(type) {
	case
		*ast.AlterTableStmt:
		return true
	default:

	}
	return false
}

func Generate6MD5(input int64) string {
	// 计算 MD5 哈希
	hash := md5.Sum([]byte(fmt.Sprintf("%d", input)))
	// 转换为十六进制字符串
	hashString := hex.EncodeToString(hash[:])
	// 返回前 6 位
	return hashString[:6]
}

func (self *mysqlImpl) validateGrants(ctx context.Context, myDb *gosql.DB, ds *shared.DataSource) error {
	query := DbwConsoleDefaultHint + `show grants for current_user()`
	foundAll := false
	foundSuper := false
	foundReplicationClient := false
	foundReplicationSlave := false
	foundDBAll := false
	err := QueryRowsMap(myDb, query, func(rowMap sqlutils.RowMap) error {
		for _, grantData := range rowMap {
			grant := grantData.String
			if strings.Contains(grant, `GRANT ALL PRIVILEGES ON *.*`) {
				foundAll = true
			}
			if strings.Contains(grant, `SUPER`) && strings.Contains(grant, ` ON *.*`) {
				foundSuper = true
			}
			if strings.Contains(grant, `REPLICATION CLIENT`) && strings.Contains(grant, ` ON *.*`) {
				foundReplicationClient = true
			}
			if strings.Contains(grant, `REPLICATION SLAVE`) && strings.Contains(grant, ` ON *.*`) {
				foundReplicationSlave = true
			}
			if strings.Contains(grant, fmt.Sprintf("GRANT ALL PRIVILEGES ON `%s`.*", ds.Db)) {
				foundDBAll = true
			}
			if strings.Contains(grant, fmt.Sprintf("GRANT ALL PRIVILEGES ON `%s`.*", strings.Replace(ds.Db, "_", "\\_", -1))) {
				foundDBAll = true
			}
			if StringContainsAll(grant, `ALTER`, `CREATE`, `DELETE`, `DROP`, `INDEX`, `INSERT`, `LOCK TABLES`, `SELECT`, `TRIGGER`, `UPDATE`, ` ON *.*`) {
				foundDBAll = true
			}
			if StringContainsAll(grant, `ALTER`, `CREATE`, `DELETE`, `DROP`, `INDEX`, `INSERT`, `LOCK TABLES`, `SELECT`, `TRIGGER`, `UPDATE`, fmt.Sprintf(" ON `%s`.*", ds.Db)) {
				foundDBAll = true
			}
		}
		return nil
	})
	if err != nil {
		return fmt.Errorf("query user grants error: %s", err.Error())
	}

	if foundAll {
		return nil
	}
	if foundSuper && foundReplicationSlave && foundDBAll {
		return nil
	}
	if foundReplicationClient && foundReplicationSlave && foundDBAll {
		return nil
	}
	return fmt.Errorf("user has insufficient privileges for migration. Needed: SUPER|REPLICATION CLIENT, REPLICATION SLAVE and ALL on %s.*", EscapeName(ds.Db))
}

func (self *mysqlImpl) validateBinlogs(ctx context.Context, myDb *gosql.DB, ds *shared.DataSource) error {
	query := `select /*+ DBW DEFAULT*/ @@global.log_bin, @@global.binlog_format`
	var hasBinaryLogs bool
	var binlogFormat string
	if err := myDb.QueryRow(query).Scan(&hasBinaryLogs, &binlogFormat); err != nil {
		return fmt.Errorf("query binlog info error: %s", err.Error())
	}
	if !hasBinaryLogs {
		return fmt.Errorf("instance must have binary logs enabled")
	}
	if binlogFormat != "ROW" {
		return fmt.Errorf("must be using ROW binlog format")
	}

	query = `select /*+ DBW DEFAULT*/ @@global.binlog_row_image`
	var rowImage string
	if err := myDb.QueryRow(query).Scan(&rowImage); err != nil {
		return fmt.Errorf("query binlog row image: %s", err.Error())
	}
	rowImage = strings.ToUpper(rowImage)
	if rowImage != "FULL" {
		return fmt.Errorf("instance has binlog_row_image, but only 'FULL' is supported")
	}
	return nil
}

func (self *mysqlImpl) ValidateOriginalTable(ctx context.Context, req *datasource.ValidateOriginalTableReq) *shared.ValidateResponse {
	myDb, err := gosql.Open("mysql", GetDBUri(req.Source))
	res := &shared.ValidateResponse{ItemCn: datasource.OnlineDDLOriginalTableCn, ItemEn: model.PreCheckItem_OnlineDDLOriginalTable.String(), Success: datasource.ValidateSuccess}
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		res.Success = datasource.ValidateError
		res.Result = "connect to database error, please check user/password in security control"
		return res
	}
	defer myDb.Close()

	maxTableLength := 52
	var itemDetails []*shared.ItemDetail
	for _, value := range req.SqlList {
		itemDetail := &shared.ItemDetail{SqlText: value.SqlText, Result: "success"}
		itemDetails = append(itemDetails, itemDetail)
		for _, tableName := range value.TableNames {
			if len(tableName) > maxTableLength {
				itemDetail.Result = fmt.Sprintf("table:%s length over than max table-length 52", tableName)
				res.Success = datasource.ValidateError
				break
			}
			if self.isCreateTableSql(value.SqlText) {
				continue
			}
			if err = self.validateTable(ctx, myDb, req.Source, tableName); err != nil {
				itemDetail.Result = err.Error()
				res.Success = datasource.ValidateError
				break
			}
			if err = self.validateTableForeignKeys(ctx, myDb, req.Source, tableName); err != nil {
				itemDetail.Result = err.Error()
				res.Success = datasource.ValidateError
				break
			}
			if err = self.validateTableTriggers(ctx, myDb, req.Source, tableName); err != nil {
				itemDetail.Result = err.Error()
				res.Success = datasource.ValidateError
				break
			}
			if err = self.validateTablePrimaryOrUniqKey(ctx, myDb, req.Source, tableName); err != nil {
				itemDetail.Result = err.Error()
				res.Success = datasource.ValidateError
				break
			}
		}
	}
	res.ItemDetail = itemDetails
	return res
}

func (self *mysqlImpl) validateTablePrimaryOrUniqKey(ctx context.Context, myDb *gosql.DB, ds *shared.DataSource, tbName string) error {
	query := `
		SELECT /*+ DBW DEFAULT*/ COUNT(*) AS num_primary
		FROM
			information_schema.TABLE_CONSTRAINTS
		WHERE
			TABLE_SCHEMA = ?
			AND TABLE_NAME = ?
			AND (CONSTRAINT_TYPE = 'PRIMARY KEY' OR CONSTRAINT_TYPE = 'UNIQUE');`

	numPrimary := 0
	err := sqlutils.QueryRowsMap(myDb, query, func(rowMap sqlutils.RowMap) error {
		numPrimary = rowMap.GetInt("num_primary")
		return nil
	}, ds.Db, tbName)
	if err != nil {
		return err
	}
	if numPrimary == 0 {
		return fmt.Errorf("table %s.%s has no primary key or unique key", EscapeName(ds.Db), EscapeName(tbName))
	}
	return nil
}

func (self *mysqlImpl) isCreateTableSql(sql string) bool {
	p := parser.New()
	stmt, err := p.ParseOneStmt(sql, "", "")
	if err != nil {
		return false
	}

	// 检查语句类型是否为DDL
	switch stmt.(type) {
	case
		*ast.CreateTableStmt:
		return true
	default:

	}
	return false
}

func (self *mysqlImpl) ValidateUniqIndex(ctx context.Context, req *datasource.ValidateUniqIndexReq) *shared.ValidateResponse {
	// conn, err := self.getConn(ctx, req.Source)
	res := &shared.ValidateResponse{Success: 0, ItemDetail: []*shared.ItemDetail{}}
	//if err != nil {
	//	log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
	//	res.Success = datasource.ValidateError
	//	res.Result = "connect to database error, please check user/password in security control"
	//	return res
	//}
	//defer func(conn db.Conn) { _ = conn.Close() }(conn)

	//for _, idxInfo := range req.IdxInfos {
	//	self.ValidateDropIndex(ctx, req, conn, idxInfo, res)
	//}
	return res
}

func (self *mysqlImpl) DescribeDropIndexSql(sqlText string) (isDropIdx bool, idxInfo *shared.PreCheckIndexInfo) {
	idxInfo = &shared.PreCheckIndexInfo{SqlText: sqlText}
	p := parser.New()

	// Parse the SQL statement
	stmtNodes, _, err := p.Parse(sqlText, "", "")
	if err != nil {
		return false, idxInfo
	}
	var tableIndexInfos []*shared.TableIndexInfo

	for _, stmtNode := range stmtNodes {
		switch node := stmtNode.(type) {
		case *ast.AlterTableStmt:
			tableName := node.Table.Name.String()
			for _, spec := range node.Specs {
				tableIndexInfo := &shared.TableIndexInfo{TableName: tableName}
				if spec.Tp == ast.AlterTableDropIndex {
					isDropIdx = true
					tableIndexInfo.IndexName = spec.Name
					tableIndexInfos = append(tableIndexInfos, tableIndexInfo)
				}
				if spec.Tp == ast.AlterTableDropPrimaryKey {
					isDropIdx = true
					tableIndexInfo.IndexName = "PRIMARY"
					tableIndexInfos = append(tableIndexInfos, tableIndexInfo)
				}
			}
		}
	}
	idxInfo.TableIndexInfos = tableIndexInfos
	return
}

func (self *mysqlImpl) ValidateDropIndex(ctx context.Context, myDb *gosql.DB, dbName string, idxInfo *shared.PreCheckIndexInfo) (bool, string) {
	query := "SELECT NON_UNIQUE  FROM INFORMATION_SCHEMA.STATISTICS where TABLE_SCHEMA = ? AND TABLE_NAME = ? and index_name = ? limit 1 "
	query += DBW_CONSOLE_DEFAULT_HINT

	for _, idx := range idxInfo.TableIndexInfos {
		nonUnique := 0
		// 如果为0说明是唯一键/主键，如果不为0说明不是唯一键/主键
		if err := myDb.QueryRow(query, dbName, idx.TableName, idx.IndexName).Scan(&nonUnique); err != nil {
			log.Warn(ctx, "query index info error fail %v", err)
			return false, "query index info error fail, " + err.Error()
		}

		if nonUnique != 0 {
			continue
		}
		// 如果这次删除的是唯一键/主键，需要看除了这个唯一键/主键外，是否还有唯一键
		// 简单来说就是删唯一键需要有主键，删主键需要有唯一键
		queryOtherUnique := "SELECT count(*)  FROM INFORMATION_SCHEMA.STATISTICS where TABLE_SCHEMA = ? AND TABLE_NAME = ? and index_name != ? and NON_UNIQUE = 0 "
		query += DBW_CONSOLE_DEFAULT_HINT
		uniqueCount := 0
		if err := myDb.QueryRow(queryOtherUnique, dbName, idx.TableName, idx.IndexName).Scan(&uniqueCount); err != nil {
			log.Warn(ctx, "query index info error fail %v", err)
			return false, "query index info error fail, " + err.Error()
		}
		if uniqueCount == 0 {
			return false, "No shared unique key can be found after ALTER! Bailing out"
		}
	}
	return true, "success"
}

func (self *mysqlImpl) ExecuteSql(ctx context.Context, req *datasource.ExecuteReq) error {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "getConn error:%s", err.Error())
		return err
	}
	defer conn.Close()
	err = conn.Exec(req.ExecuteSql)
	if err != nil {
		log.Warn(ctx, "execute drop sql error:%s", err.Error())
		return err
	}
	return nil
}

func (self *mysqlImpl) GetCurrentMaxConnections(ctx context.Context, req *datasource.GetCurrentMaxConnectionsReq) (int, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "getConn error:%s", err.Error())
		return 0, err
	}
	defer conn.Close()

	maxConnections := 0
	query := `SELECT /*+ DBW DEFAULT*/ @@max_connections as max_connections`
	if err = conn.Raw(query).Scan(&maxConnections); err != nil {
		log.Warn(ctx, "query max connections error:%s", err.Error())
		return 0, err
	}
	return maxConnections, nil
}

func (self *mysqlImpl) validateTable(ctx context.Context, myDb *gosql.DB, ds *shared.DataSource, tbName string) error {
	query := fmt.Sprintf(`show /*+ DBW DEFAULT*/ table status from %s like '%s'`, EscapeName(ds.Db), tbName)

	tableFound := false
	err := QueryRowsMap(myDb, query, func(rowMap sqlutils.RowMap) error {
		if rowMap.GetString("Comment") == "VIEW" {
			return fmt.Errorf("%s.%s is a VIEW, not a real table. Bailing out", EscapeName(ds.Db), EscapeName(tbName))
		}
		tableFound = true

		return nil
	})
	if err != nil {
		return err
	}
	if !tableFound {
		return fmt.Errorf("cannot find table %s.%s", EscapeName(ds.Db), EscapeName(tbName))
	}
	return nil
}

func (self *mysqlImpl) validateTableForeignKeys(ctx context.Context, myDb *gosql.DB, ds *shared.DataSource, tbName string) error {
	query := `
		SELECT /*+ DBW DEFAULT*/
			SUM(REFERENCED_TABLE_NAME IS NOT NULL AND TABLE_SCHEMA=? AND TABLE_NAME=?) as num_child_side_fk,
			SUM(REFERENCED_TABLE_NAME IS NOT NULL AND REFERENCED_TABLE_SCHEMA=? AND REFERENCED_TABLE_NAME=?) as num_parent_side_fk
		FROM
			INFORMATION_SCHEMA.KEY_COLUMN_USAGE
		WHERE
			REFERENCED_TABLE_NAME IS NOT NULL
			AND (
				(TABLE_SCHEMA=? AND TABLE_NAME=?)
				OR
				(REFERENCED_TABLE_SCHEMA=? AND REFERENCED_TABLE_NAME=?)
			)`
	numParentForeignKeys := 0
	numChildForeignKeys := 0
	err := sqlutils.QueryRowsMap(myDb, query, func(m sqlutils.RowMap) error {
		numChildForeignKeys = m.GetInt("num_child_side_fk")
		numParentForeignKeys = m.GetInt("num_parent_side_fk")
		return nil
	}, ds.Db, tbName, ds.Db, tbName, ds.Db, tbName, ds.Db, tbName)
	if err != nil {
		return err
	}
	if numParentForeignKeys > 0 {
		return fmt.Errorf("found %d parent-side foreign keys on %s.%s. Parent-side foreign keys are not supported. Bailing out", numParentForeignKeys, EscapeName(ds.Db), EscapeName(tbName))
	}
	if numChildForeignKeys > 0 {
		return fmt.Errorf("found %d child-side foreign keys on %s.%s. Child-side foreign keys are not supported. Bailing out", numChildForeignKeys, EscapeName(ds.Db), EscapeName(tbName))
	}
	return nil
}

func (self *mysqlImpl) validateTableTriggers(ctx context.Context, myDb *gosql.DB, ds *shared.DataSource, tbName string) error {
	query := `
		SELECT /*+ DBW DEFAULT*/ COUNT(*) AS num_triggers
		FROM
			INFORMATION_SCHEMA.TRIGGERS
		WHERE
			TRIGGER_SCHEMA=?
			AND EVENT_OBJECT_TABLE=?`
	numTriggers := 0
	err := sqlutils.QueryRowsMap(myDb, query, func(rowMap sqlutils.RowMap) error {
		numTriggers = rowMap.GetInt("num_triggers")
		return nil
	}, ds.Db, tbName)
	if err != nil {
		return err
	}
	if numTriggers > 0 {
		return fmt.Errorf("found triggers on %s.%s. Tables with triggers are supported only when using \"include-triggers\" flag. Bailing out", EscapeName(ds.Db), EscapeName(tbName))
	}
	return nil
}

func GetDBUri(ds *shared.DataSource) string {
	connectionParams := []string{
		"autocommit=true",
		"interpolateParams=true",
	}
	if ds.ConnectTimeoutMs != 0 {
		connectionParams = append(connectionParams, fmt.Sprintf("timeout=%ds", ds.ConnectTimeoutMs/1000))
	}

	if ds.ReadTimeoutMs != 0 {
		connectionParams = append(connectionParams, fmt.Sprintf("readTimeout=%ds", ds.ReadTimeoutMs/1000))
	}
	if ds.WriteTimeoutMs != 0 {
		connectionParams = append(connectionParams, fmt.Sprintf("writeTimeout=%ds", ds.WriteTimeoutMs/1000))
	}
	return fmt.Sprintf("%s:%s@tcp(%s)/%s?%s", ds.User, ds.Password, ds.Address, ds.Db, strings.Join(connectionParams, "&"))
}

func QueryRowsMap(db *sql.DB, query string, on_row func(sqlutils.RowMap) error, args ...interface{}) (err error) {
	defer func() {
		if derr := recover(); derr != nil {
			err = fmt.Errorf("QueryRowsMap unexpected error: %+v", derr)
		}
	}()

	var rows *sql.Rows
	rows, err = db.Query(query, args...)
	if rows != nil {
		defer rows.Close()
	}
	if err != nil && err != sql.ErrNoRows {
		return err
	}
	err = ScanRowsToMaps(rows, on_row)
	return
}

func ScanRowsToMaps(rows *sql.Rows, on_row func(sqlutils.RowMap) error) error {
	columns, _ := rows.Columns()
	err := ScanRowsToArrays(rows, func(arr []sqlutils.CellData) error {
		m := rowToMap(arr, columns)
		err := on_row(m)
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

func rowToMap(row []sqlutils.CellData, columns []string) map[string]sqlutils.CellData {
	m := make(map[string]sqlutils.CellData)
	for k, data_col := range row {
		m[columns[k]] = data_col
	}
	return m
}

func ScanRowsToArrays(rows *sql.Rows, on_row func([]sqlutils.CellData) error) error {
	columns, _ := rows.Columns()
	for rows.Next() {
		arr := RowToArray(rows, columns)
		err := on_row(arr)
		if err != nil {
			return err
		}
	}
	return nil
}

func RowToArray(rows *sql.Rows, columns []string) []sqlutils.CellData {
	buff := make([]interface{}, len(columns))
	data := make([]sqlutils.CellData, len(columns))
	for i, _ := range buff {
		buff[i] = data[i].NullString()
	}
	rows.Scan(buff...)
	return data
}

func StringContainsAll(s string, substrings ...string) bool {
	nonEmptyStringsFound := false
	for _, substring := range substrings {
		if substring == "" {
			continue
		}
		if strings.Contains(s, substring) {
			nonEmptyStringsFound = true
		} else {
			// Immediate failure
			return false
		}
	}
	return nonEmptyStringsFound
}

func EscapeName(name string) string {
	if unquoted, err := strconv.Unquote(name); err == nil {
		name = unquoted
	}
	return fmt.Sprintf("`%s`", name)
}
