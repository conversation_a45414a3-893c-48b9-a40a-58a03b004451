package bytedoc

import (
	"bytes"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	dshttp "code.byted.org/infcs/ds-lib/common/http"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"crypto/md5"

	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

type ByteDocClient interface {
	Call(context.Context, string, interface{}, interface{}, bool) error
	GET(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, regionId string, crossRegion bool) (*http.Response, error)
	POST(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, body interface{}, regionId string, crossRegion bool) (*http.Response, error)
}

func NewByteDocClient(conf config.ConfigProvider) ByteDocClient {
	return &client{
		conf: conf,
	}
}

type client struct {
	conf config.ConfigProvider
}

func (c *client) Call(ctx context.Context, action string, req interface{}, resp interface{}, crossRegion bool) error {
	conf := c.conf.Get(ctx)
	result := c.getClient().AddBeforeHook(func(req *http.Request) {
		ts := time.Now().Unix()
		req.Header.Set(`ts`, strconv.FormatInt(ts, 10))
		req.Header.Set(`authtoken`, calcAuthToken(conf.ByteRDS.Secret, ts))
	}).PostJSON(ctx, fmt.Sprintf(`%s/rds/%s`, conf.ByteRDS.ProxyAddress, action), req)
	body, err := result.GetBody()
	if err != nil {
		log.Warn(ctx, "get result body failed,err = %s", err.Error())
	}
	log.Info(ctx, "result body = %s", string(body))
	return result.Unmarshal(resp)
}

func (c *client) getClient() dshttp.Client {

	cli := dshttp.NewClient().SetTimeout(40 * time.Second)
	if log.GetLevel() == log.Level_Debug {
		cli = cli.SetDebug(dshttp.DefaultLogger)
	}
	return cli
}

func (c *client) GET(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, regionId string, crossRegion bool) (*http.Response, error) {
	conf := c.conf.Get(ctx)
	var apiAddress map[string]string
	apiDomain := conf.ByteDoc.APIDomain
	//log.Info(ctx, "apiDomain is %s", apiDomain)
	if err := json.Unmarshal([]byte(apiDomain), &apiAddress); err != nil {
		log.Warn(ctx, "%s json unmarshal failed %s", apiDomain, err)
		return nil, err
	}
	if _, ok := apiAddress[regionId]; !ok {
		log.Warn(ctx, "apiAddress %s not config for region %s", apiDomain, regionId)
		return nil, errors.New("api address not exist")
	}
	apiUrl := fmt.Sprintf(`%s%s`, apiAddress[regionId], apiPath)
	// 创建 URL 对象
	u, err := url.Parse(apiUrl)
	if err != nil {
		log.Warn(ctx, "Error parsing URL:%s", err)
		return nil, err
	}
	// 设置查询参数
	param := url.Values{}
	for k, v := range params {
		param.Add(k, v)
	}
	u.RawQuery = param.Encode()
	log.Info(ctx, "GET request url: %s", u.String())
	request, _ := http.NewRequest(http.MethodGet, u.String(), nil)
	// 设置header
	for k, v := range headers {
		request.Header.Set(k, v)
	}
	ts := time.Now().Unix()
	request.Header.Set(`ts`, strconv.FormatInt(ts, 10))
	request.Header.Set(`authtoken`, calcAuthToken(conf.ByteDoc.Secret, ts))
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", conf.ByteDoc.Secret))
	request.Header.Set("X-Tt-Logid", fwctx.GetLogID(ctx))
	result := c.getClient().DoRequest(request)
	if result.Err != nil {
		log.Warn(ctx, "request error:%s", result.Err)
	}
	log.Info(ctx, "response header is %s", utils.Show(result.Response.Header))
	return result.Response, result.Err
}

func (c *client) POST(ctx context.Context, apiPath string, params map[string]string, headers map[string]string, body interface{}, regionId string, crossRegion bool) (*http.Response, error) {
	conf := c.conf.Get(ctx)
	var apiAddress map[string]string
	apiDomain := conf.ByteDoc.APIDomain
	if err := json.Unmarshal([]byte(apiDomain), &apiAddress); err != nil {
		log.Warn(ctx, "%s json unmarshal failed %s", apiDomain, err)
		return nil, err
	}
	if _, ok := apiAddress[regionId]; !ok {
		log.Warn(ctx, "apiAddress %s not config for region %s", apiDomain, regionId)
		return nil, errors.New("api address not exist")
	}
	apiUrl := fmt.Sprintf(`%s%s`, apiAddress[regionId], apiPath)
	// 创建 URL 对象
	u, err := url.Parse(apiUrl)
	if err != nil {
		log.Warn(ctx, "Error parsing URL:%s", err)
		return nil, err
	}
	// 设置查询参数
	param := url.Values{}
	for k, v := range params {
		param.Add(k, v)
	}
	u.RawQuery = param.Encode()
	sentData, err := json.Marshal(body)
	if err != nil {
		log.Warn(ctx, "Error marshal body:%s", err)
		return nil, err
	}
	// 创建请求的 Body
	data := bytes.NewBuffer(sentData)
	log.Info(ctx, "POST request url is %s, send data: %s", u.String(), sentData)
	request, _ := http.NewRequest(http.MethodPost, u.String(), data)
	// 设置header
	for k, v := range headers {
		request.Header.Set(k, v)
	}
	ts := time.Now().Unix()
	request.Header.Set(`ts`, strconv.FormatInt(ts, 10))
	request.Header.Set(`authtoken`, calcAuthToken(conf.ByteDoc.Secret, ts))
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("X-Tt-Logid", fwctx.GetLogID(ctx))
	request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", conf.ByteDoc.Secret))
	result := c.getClient().DoRequest(request)
	if result.Err != nil {
		log.Warn(ctx, "request error:%s", result.Err)
	}
	return result.Response, result.Err
}

func calcAuthToken(base string, ts int64) string {
	return fmt.Sprintf(`%x`, md5.Sum([]byte(fmt.Sprintf("%s.%d", base, ts))))
}
