package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/abnormal_diagnosis"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
	"time"
)

func NewGetMetricDataHandler(in NewGetMetricDataHandlerIn) handler.HandlerImplementationEnvolope {
	h := &GetMetricDataHandler{
		adSvc: in.AdSvc,
	}
	return handler.<PERSON><PERSON>and<PERSON>(h.GetMetricData)
}

type GetMetricDataHandler struct {
	adSvc abnormal_diagnosis.MetricDataService
}

type NewGetMetricDataHandlerIn struct {
	dig.In
	AdSvc abnormal_diagnosis.MetricDataService
}

func (self *GetMetricDataHandler) GetMetricData(ctx context.Context, req *model.GetMetricDataReq) (*model.GetMetricDataResp, error) {
	err := self.preCheck(ctx, req)
	if err != nil {
		log.Warn(ctx, "GetMetricData precheck error, req: %v", utils.Show(req))
		return &model.GetMetricDataResp{}, err
	}
	svc, err := self.adSvc.GetMetricDataMeasurement(ctx, req)
	if err != nil {
		return &model.GetMetricDataResp{}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	var (
		dataList []*model.MetricDataResults
	)
	start := time.Unix(int64(req.StartTime), 0)
	end := time.Unix(int64(req.EndTime), 0)
	for _, node := range req.GetFilters() {
		rreq := &datasource.GetMetricDatapointsReq{
			InstanceId:  node.InstanceId,
			NodeId:      node.NodeId,
			Type:        shared.DataSourceType(req.InstanceType),
			Measurement: svc.Measurement,
			StartTime:   start,
			EndTime:     end,
			Period:      req.Period,
			Value:       svc.Value,
			MonitorType: svc.MonitorType,
		}
		log.Info(ctx, "GetMetricDatapointsReq: %v", utils.Show(rreq))
		data, _ := self.adSvc.GetMetricDataPoints(ctx, rreq)
		dataResults := &model.MetricDataResults{
			InstanceId: node.InstanceId,
			NodeId:     node.NodeId,
			DataPoints: data.DataPoints,
		}
		dataList = append(dataList, dataResults)
	}
	if len(dataList) == 0 {
		return &model.GetMetricDataResp{}, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	resp := &model.GetMetricDataResp{
		MetricName:        req.MetricName,
		Period:            req.Period,
		StartTime:         req.StartTime,
		EndTime:           req.EndTime,
		DescriptionCN:     svc.DescriptionCN,
		DescriptionEN:     svc.DescriptionEN,
		Unit:              svc.Unit,
		MetricDataResults: dataList,
	}
	return resp, nil
}

func (self *GetMetricDataHandler) preCheck(ctx context.Context, req *model.GetMetricDataReq) error {
	if req.GetStartTime() == 1 || req.GetEndTime() == 1 {
		log.Warn(ctx, "monitor time error")
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if fwctx.GetTenantID(ctx) == "" || req.GetMetricName() == "" || len(req.GetFilters()) == 0 {
		log.Warn(ctx, "monitor input error")
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if req.GetInstanceType() != model.InstanceType_MySQL {
		return consts.ErrorOf(model.ErrorCode_InstanceTypeNotSupport)
	}
	return nil
}
