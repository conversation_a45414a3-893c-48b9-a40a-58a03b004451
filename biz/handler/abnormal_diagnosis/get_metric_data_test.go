package abnormal_detection

import (
	adsvc "code.byted.org/infcs/dbw-mgr/biz/service/abnormal_diagnosis"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/abnormal_diagnosis"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	. "code.byted.org/luoshiqi/mockito"
	"context"
	"github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"testing"
)

func mockMetricDataConfigHandler() *GetMetricDataHandler {
	return &GetMetricDataHandler{
		adSvc: &abnormal_diagnosis.MockMetricDataService{},
	}
}

func TestNewGetMetricDataHandler(t *testing.T) {
	PatchConvey("Test NewGetMetricDataHandler Success", t, func() {
		in := NewGetMetricDataHandlerIn{AdSvc: &abnormal_diagnosis.MockMetricDataService{}}
		h := NewGetMetricDataHandler(in)
		So(h, ShouldNotBeNil)
	})
}

func TestGetMetricData(t *testing.T) {
	impl := mockMetricDataConfigHandler()
	ctx := fwctx.SetBizContext(context.Background())
	c := fwctx.GetBizContext(ctx)
	PatchConvey("Test GetMetricData Success", t, func() {
		c.TenantID = "12345"
		req := &model.GetMetricDataReq{
			InstanceType: model.InstanceType_MySQL,
			Filters: []*model.MetricFilter{
				{InstanceId: "mysql-e1b0ec4a1fe7",
					NodeId: "mysql-e1b0ec4a1fe7-0"},
			},
			MetricName: "com_update_ps",
			Period:     30,
			StartTime:  1709875581,
			EndTime:    1709885581,
		}
		resp := adsvc.Metric{
			Name:          "com_update_ps",
			Measurement:   "tob_rds_mysql_global_status_com_update",
			DescriptionCN: "更新数",
			DescriptionEN: "com_update_ps",
			Unit:          "count/s",
			MonitorType:   "persencond",
		}
		data := &datasource.GetMetricDatapointsResp{
			DataPoints: []*model.DataPoint{
				{TimeStamp: 1709875581, Value: 1},
			},
		}

		mock1 := mockey.Mock((*abnormal_diagnosis.MockMetricDataService).GetMetricDataMeasurement).Return(resp, nil).Build()
		defer mock1.UnPatch()
		mock2 := mockey.Mock((*abnormal_diagnosis.MockMetricDataService).GetMetricDataPoints).Return(data, nil).Build()
		defer mock2.UnPatch()
		_, err := impl.GetMetricData(ctx, req)
		So(err, ShouldBeNil)
	})
}
