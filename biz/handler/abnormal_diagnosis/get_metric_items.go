package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/abnormal_diagnosis"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
)

func NewGetMetricItemsHandler(in NewGetMetricItemsHandlerIn) handler.HandlerImplementationEnvolope {
	h := &GetMetricItemsHandler{
		adSvc: in.AdSvc,
	}
	return handler.NewHandler(h.GetMetricItems)
}

type GetMetricItemsHandler struct {
	adSvc abnormal_diagnosis.MetricDataService
}

type NewGetMetricItemsHandlerIn struct {
	dig.In
	AdSvc abnormal_diagnosis.MetricDataService
}

func (self *GetMetricItemsHandler) GetMetricItems(ctx context.Context, req *model.GetMetricItemsReq) (*model.GetMetricItemsResp, error) {
	err := self.preCheck(ctx, req)
	if err != nil {
		log.Warn(ctx, "GetMetricItems precheck error, req: %v", utils.Show(req))
		return nil, err
	}
	metricItems, err := self.adSvc.GetMetricDataItems(ctx, req)
	if err != nil {
		return nil, err
	}
	var Items []string
	for _, item := range metricItems {
		Items = append(Items, item.Item)
	}
	return &model.GetMetricItemsResp{
		MetricItems: metricItems,
		Total:       int32(len(metricItems)),
		Items:       Items,
	}, nil
}

func (self *GetMetricItemsHandler) preCheck(ctx context.Context, req *model.GetMetricItemsReq) error {
	if fwctx.GetTenantID(ctx) == "" {
		return consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if req.GetInstanceType() != model.InstanceType_MySQL {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	return nil
}
