package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/abnormal_diagnosis"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	"context"
	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"testing"
)

func mockGetMetricItemsConfigHandler() *GetMetricItemsHandler {
	return &GetMetricItemsHandler{
		adSvc: &abnormal_diagnosis.MockMetricDataService{},
	}
}

func TestNewGetMetricItemsHandler(t *testing.T) {
	PatchConvey("Test NewGetMetricItemsHandler Success", t, func() {
		in := NewGetMetricItemsHandlerIn{AdSvc: &abnormal_diagnosis.MockMetricDataService{}}
		Mock(handler.NewHandler).Return(handler.HandlerImplementationEnvolope{}).Build()
		got := NewGetMetricItemsHandler(in)
		So(got, ShouldNotBeNil)
	})
}

func TestGetMetricItems(t *testing.T) {
	impl := mockGetMetricItemsConfigHandler()
	ctx := fwctx.SetBizContext(context.Background())
	c := fwctx.GetBizContext(ctx)
	PatchConvey("Test GetMetricItems Success", t, func() {
		c.TenantID = "12345"
		req := &model.GetMetricItemsReq{
			InstanceType: model.InstanceType_MySQL,
		}
		resp := []*model.MetricItem{{
			Item:          "com_update_ps",
			DescriptionCN: "更新数",
			DescriptionEN: "com_update_ps",
		}}

		mock1 := mockey.Mock((*abnormal_diagnosis.MockMetricDataService).GetMetricDataItems).Return(resp, nil).Build()
		defer mock1.UnPatch()
		_, err := impl.GetMetricItems(ctx, req)
		So(err, ShouldBeNil)
	})
}
