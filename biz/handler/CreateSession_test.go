package handler

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

type CreateSessionTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *CreateSessionTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *CreateSessionTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestCreateSessionTestSuite(t *testing.T) {
	suite.Run(t, new(CreateSessionTestSuite))
}

type mockStandardError struct {
	code    int32
	message string
	status  string
}

func (m mockStandardError) GetCode() int32 {
	return m.code
}

func (m mockStandardError) GetMessage() string {
	return m.message
}

func (m mockStandardError) GetStatus() string {
	return m.status
}

func (m mockStandardError) GetHTTPCode() int64 {
	return 400
}

func (m mockStandardError) Error() string {
	return m.message
}

func (suite *CreateSessionTestSuite) TestWrapError_ReachMaxRetryTimes() {
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "test-tenant"

	req := &model.CreateSessionReq{
		DataSource: &model.DataSource{
			InstanceId: &[]string{"test-instance"}[0],
			Type:       model.DSType_MySQL,
		},
	}

	h := &CreateSessionHandler{}

	suite.Run("should return error as-is when StandardError with ReachMaxRetryTimes", func() {
		mockErr := mockStandardError{
			code:    int32(model.ErrorCode_ReachMaxRetryTimes),
			message: "The number of retries has reached the maximum limit 5 times, please try again after one minute",
			status:  "ReachMaxRetryTimes",
		}

		result := h.wrapError(ctx, req, mockErr)

		suite.Equal(mockErr, result)
		suite.Equal(int32(model.ErrorCode_ReachMaxRetryTimes), result.(consts.StandardError).GetCode())
	})

	suite.Run("should return CreateSessionError when StandardError with different error code", func() {
		mockErr := mockStandardError{
			code:    int32(model.ErrorCode_InternalError),
			message: "Internal error occurred",
			status:  "InternalError",
		}

		result := h.wrapError(ctx, req, mockErr)

		suite.NotEqual(mockErr, result)
		suite.Equal(int32(model.ErrorCode_CreateSessionError), result.(consts.StandardError).GetCode())
	})

	suite.Run("should return CreateSessionError when non-StandardError", func() {
		regularErr := errors.New("some regular error")
		result := h.wrapError(ctx, req, regularErr)
		suite.Equal(int32(model.ErrorCode_CreateSessionError), result.(consts.StandardError).GetCode())
	})

	suite.Run("should return nil when error is nil", func() {
		result := h.wrapError(ctx, req, nil)
		suite.Nil(result)
	})
}

func (suite *CreateSessionTestSuite) TestWrapError_OtherErrorTypes() {
	ctx := fwctx.SetBizContext(context.Background())
	req := &model.CreateSessionReq{
		DataSource: &model.DataSource{
			InstanceId: &[]string{"test-instance"}[0],
			Type:       model.DSType_MySQL,
		},
	}

	h := &CreateSessionHandler{}

	suite.Run("should handle regular errors correctly", func() {
		regularErr := errors.New("connection failed")
		result := h.wrapError(ctx, req, regularErr)
		suite.Equal(int32(model.ErrorCode_CreateSessionError), result.(consts.StandardError).GetCode())
	})
}

func (suite *CreateSessionTestSuite) TestWrapError_IntegrationWithBuildDBErrorWithParam() {
	ctx := fwctx.SetBizContext(context.Background())
	req := &model.CreateSessionReq{
		DataSource: &model.DataSource{
			InstanceId: &[]string{"test-instance-123"}[0],
			Type:       model.DSType_MySQL,
		},
	}

	h := &CreateSessionHandler{}

	suite.Run("should preserve BuildDBErrorWithParam error when it has ReachMaxRetryTimes code", func() {
		originalErr := consts.BuildDBErrorWithParam(model.ErrorCode_ReachMaxRetryTimes, "test-instance-123", 5)
		result := h.wrapError(ctx, req, originalErr)

		suite.Equal(originalErr, result)
		suite.Equal(int32(model.ErrorCode_ReachMaxRetryTimes), result.(consts.StandardError).GetCode())

		suite.Contains(result.Error(), "ReachMaxRetryTimes")
		suite.Contains(result.Error(), "test-instance-123")
	})

	suite.Run("should not preserve BuildDBErrorWithParam error when it has different code", func() {
		originalErr := consts.BuildDBErrorWithParam(model.ErrorCode_InternalError, "test-instance-123", "some error")

		result := h.wrapError(ctx, req, originalErr)
		suite.NotEqual(originalErr, result)
		suite.Equal(int32(model.ErrorCode_CreateSessionError), result.(consts.StandardError).GetCode())
	})
}
