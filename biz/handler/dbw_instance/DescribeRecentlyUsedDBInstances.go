package dbw_instance

import (
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	dbwInsSvc "code.byted.org/infcs/dbw-mgr/biz/service/dbwinstance"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"context"
	"go.uber.org/dig"
)

type DescribeRecentlyUsedDBInstancesHandler struct {
	actorClient    cli.ActorClient
	dbwInstanceSvc dbwInsSvc.DbwInstanceInterface
}

type DescribeRecentlyUsedDBInstancesHandlerIn struct {
	dig.In
	ActorClient    cli.ActorClient
	DbwInstanceSvc dbwInsSvc.DbwInstanceInterface
}

func NewDescribeRecentlyUsedDBInstancesHandler(in DescribeRecentlyUsedDBInstancesHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeRecentlyUsedDBInstancesHandler{
		actorClient:    in.ActorClient,
		dbwInstanceSvc: in.DbwInstanceSvc,
	}
	return handler.NewHandler(h.DescribeRecentlyUsedDBInstances)
}

func (h *DescribeRecentlyUsedDBInstancesHandler) DescribeRecentlyUsedDBInstances(ctx context.Context, req *model.DescribeRecentlyUsedDBInstancesReq) (*model.DescribeRecentlyUsedDBInstancesResp, error) {
	if req.GetPageSize() == 0 {
		req.PageSize = utils.Int32Ref(10)
	}
	if req.GetPageNumber() == 0 {
		req.PageNumber = utils.Int32Ref(1)
	}
	resp, err := h.dbwInstanceSvc.ListInstanceHistory(ctx, req)
	if err != nil {
		log.Warn(ctx, "DescribeRecentlyUsedDBInstances fail %v", err)
		return nil, err
	}
	return resp, nil
}
