package dbw_instance

import (
	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	config2 "code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"testing"
)

type DisableInstanceManagementHandlerTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *DisableInstanceManagementHandlerTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *DisableInstanceManagementHandlerTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestDisableInstanceManagementHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(DisableInstanceManagementHandlerTestSuite))
}

func (suite *DisableInstanceManagementHandlerTestSuite) TestDisableInstanceManagementOk() {
	dbwInstanceDal := mocks.NewMockDbwInstanceDAL(suite.ctrl)
	workflowDal := mocks.NewMockWorkflowDAL(suite.ctrl)
	idSvc := mocks.NewMockService(suite.ctrl)
	ctx := mocks.NewMockContext(suite.ctrl)
	cnf := config2.NewMockConfigProvider(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()

	DisableInstanceManagementHandler := NewDisableInstanceManagementHandler(DisableInstanceManagementHandlerIn{DbwInstanceDal: dbwInstanceDal, WorkflowDal: workflowDal})
	fn := DisableInstanceManagementHandler.Impl.Impl.(func(ctx context.Context, req *model.DisableInstanceManagementReq) error)
	//var dbaUser = int32(**********)
	var instanceId = "mysql-67245173607b"
	var dsType = model.DSType_MySQL
	//var ownerUser = int32(**********)
	//var account = "mysql-67245173607b"
	//var password = "mysql-67245173607b"
	var linktype = model.LinkType_Volc
	req := &model.DisableInstanceManagementReq{
		InstanceId:   &instanceId,
		InstanceType: &dsType,
		Source:       &linktype,
	}
	mcnf := &config.Config{
		ConnectionIdleTimeout:    1,
		ConnectionReadTimeout:    1,
		ConnectionConnectTimeout: 1,
		ConnectionWriteTimeout:   1,
	}
	ticket := &dao.Ticket{
		TicketStatus: 4,
	}
	tickets := []*dao.Ticket{ticket}
	info := &dao.DbwInstance{
		InstanceId:   "1",
		InstanceType: "1",
		Source:       "1",
	}
	workflowDal.EXPECT().GetInstanceTickets(gomock.Any(), gomock.Any(), gomock.Any()).Return(tickets, nil)
	cnf.EXPECT().Get(gomock.Any()).Return(mcnf).AnyTimes()
	idSvc.EXPECT().NextID(gomock.Any()).AnyTimes()
	dbwInstanceDal.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(info, nil)
	//dbwInstanceDal.EXPECT().Delete(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	dbwInstanceDal.EXPECT().SaveInstance(gomock.Any(), gomock.Any()).Return(nil)
	err := fn(ctx, req)
	suite.Empty(err)
}
