package dbw_instance

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/handler/user_mgmt"
	"code.byted.org/infcs/dbw-mgr/biz/instance/control"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"errors"
	"go.uber.org/dig"
	"gorm.io/gorm"
	"k8s.io/utils/strings/slices"
	"time"
)

type DisableInstanceManagementHandler struct {
	dbwInstanceDal dal.DbwInstanceDAL
	workflowDal    dal.WorkflowDAL
	userDal        dal.DbwUserDAL
	userSvc        usermgmt.UserService
}

type DisableInstanceManagementHandlerIn struct {
	dig.In
	DbwInstanceDal dal.DbwInstanceDAL
	WorkflowDal    dal.WorkflowDAL
	UserDal        dal.DbwUserDAL
	UserSvc        usermgmt.UserService
}

func NewDisableInstanceManagementHandler(in DisableInstanceManagementHandlerIn) handler.HandlerImplementationEnvolope {
	a := &DisableInstanceManagementHandler{
		dbwInstanceDal: in.DbwInstanceDal,
		workflowDal:    in.WorkflowDal,
		userDal:        in.UserDal,
		userSvc:        in.UserSvc,
	}
	return handler.NewHandler(a.DisableInstanceManagement)
}

func (a *DisableInstanceManagementHandler) DisableInstanceManagement(ctx context.Context, req *model.DisableInstanceManagementReq) error {
	log.Info(ctx, "DisableInstanceManagementHandler in: %+v", req)
	// 兼容单用户和批量
	req = a.compatible(ctx, req)
	// 参数校验
	if err := a.preCheck(ctx, req); err != nil {
		log.Warn(ctx, "DisableInstanceManagementHandler percheck failed err=%v", err)
		return err
	}
	for _, inst := range req.GetDetailList() {
		dbwInstance, err := a.dbwInstanceDal.Get(ctx, *inst.InstanceId, inst.InstanceType.String(), inst.Source.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
		if err != nil {
			log.Warn(ctx, "DisableInstanceManagementHandler failed to get instance from MetaDB err=%v", err)
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return consts.ErrorOf(model.ErrorCode_InstanceNotFound)
			}
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
		dbwInstance.ControlMode = 0
		dbwInstance.OwnerUid = ""
		dbwInstance.DbaUid = ""
		dbwInstance.ApprovalFlowConfigId = 0
		dbwInstance.WorkflowTemplateId = 0
		dbwInstance.SecurityGroupId = 0
		dbwInstance.DatabaseUser = ""
		dbwInstance.DatabasePassword = ""
		dbwInstance.UpdatedAt = time.Now().Unix()
		log.Info(ctx, "DisableInstanceManagementHandler update instance=%v", dbwInstance)
		err = a.dbwInstanceDal.SaveInstance(ctx, dbwInstance)
		if err != nil {
			log.Warn(ctx, "DisableInstanceManagementHandler failed to update DbwInstance from MetaDB err=%v", err)
			e := consts.ErrorOf(model.ErrorCode_InternalError)
			return e
		}
	}
	return nil
}

func (a *DisableInstanceManagementHandler) preCheck(ctx context.Context, req *model.DisableInstanceManagementReq) error {
	for _, inst := range req.GetDetailList() {
		allowed, err := user_mgmt.UserMgmtPermissionValidation(ctx, a.userDal, a.userSvc)
		if err != nil {
			return consts.ErrorOf(model.ErrorCode_CurrentUserNotDbwUser)
		}
		if !allowed {
			log.Warn(ctx, "User management is only open to administrators")
			return consts.ErrorOf(model.ErrorCode_UserMgmtPermissionDeny)
		}
		if !slices.Contains(control.SupportControlModeInstanceType(), inst.InstanceType.String()) {
			return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
		}
		// checkTicketStatus
		tickets, err := a.workflowDal.GetInstanceTickets(ctx, *inst.InstanceId, fwctx.GetTenantID(ctx))
		if err != nil {
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
		var noCompleteStatus = []int8{0, 1, 3, 6, 7}
		for _, ticket := range tickets {
			for _, status := range noCompleteStatus {
				if ticket.TicketStatus == status {
					return consts.ErrorOf(model.ErrorCode_TicketStatusNotCompleted)
				}
			}
		}
	}
	return nil
}

func (a *DisableInstanceManagementHandler) compatible(ctx context.Context, req *model.DisableInstanceManagementReq) *model.DisableInstanceManagementReq {
	if req.GetDetailList() == nil || len(req.GetDetailList()) == 0 {
		req.DetailList = []*model.InstanceManagementDetail{{
			InstanceType: req.InstanceType,
			InstanceId:   req.InstanceId,
			Source:       req.Source,
		}}
	}
	return req
}
