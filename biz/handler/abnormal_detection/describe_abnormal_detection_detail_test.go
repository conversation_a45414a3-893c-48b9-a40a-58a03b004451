package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func mockDetailHandler() *DescribeAbnormalDetectionDetailHandler {
	return &DescribeAbnormalDetectionDetailHandler{
		detectionService:       &mocks.MockAbnormalDetectionService{},
		checkPermissionService: &mocks.MockCheckPermissionService{},
	}
}

func mockDetectionDetailReq() *model.DescribeAbnormalDetectionDetailReq {
	return &model.DescribeAbnormalDetectionDetailReq{}
}

func TestDescribeAbnormalDetectionDetail(t *testing.T) {
	handler := mockDetailHandler()
	req := mockDetectionDetailReq()
	_, err := handler.DescribeAbnormalDetectionDetail(context.Background(), req)
	assert.NotNil(t, err)

	ctx := fwctx.SetBizContext(context.Background())
	c := fwctx.GetBizContext(ctx)
	c.TenantID = "12345"
	mock1 := mockey.Mock((*mocks.MockAbnormalDetectionService).CheckTimeLegal).Return(nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockCheckPermissionService).CheckAndModifyTenantPermissions).Return(fmt.Errorf("test")).Build()
	_, err = handler.DescribeAbnormalDetectionDetail(ctx, req)
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockCheckPermissionService).CheckAndModifyTenantPermissions).Return(nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockAbnormalDetectionService).GetMetricDetail).Return(nil, fmt.Errorf("test")).Build()
	_, err = handler.DescribeAbnormalDetectionDetail(ctx, req)
	assert.NotNil(t, err)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*mocks.MockAbnormalDetectionService).GetMetricDetail).Return([]*model.ItemMetric{{}}, nil).Build()
	defer mock5.UnPatch()
	res, err := handler.DescribeAbnormalDetectionDetail(ctx, req)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(res.ItemMetrics))
}
