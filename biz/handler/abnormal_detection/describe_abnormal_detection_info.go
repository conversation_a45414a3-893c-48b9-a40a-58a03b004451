package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/abnormal_detection"
	"code.byted.org/infcs/dbw-mgr/biz/service/common"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
)

func NewDescribeAbnormalDetectionInfoHandler(in NewDescribeAbnormalDetectionInfoHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeAbnormalDetectionInfoHandler{
		detectionService:       in.DetectionService,
		checkPermissionService: in.CheckPermissionService,
	}
	return handler.NewHandler(h.DescribeAbnormalDetectionInfo)
}

type DescribeAbnormalDetectionInfoHandler struct {
	detectionService       abnormal_detection.AbnormalDetectionService
	checkPermissionService common.CheckPermissionService
}

type NewDescribeAbnormalDetectionInfoHandlerIn struct {
	dig.In
	DetectionService       abnormal_detection.AbnormalDetectionService
	CheckPermissionService common.CheckPermissionService
}

func (h *DescribeAbnormalDetectionInfoHandler) DescribeAbnormalDetectionInfo(ctx context.Context, req *model.DescribeAbnormalDetectionInfoReq) (*model.DescribeAbnormalDetectionInfoResp, error) {
	tenantId := fwctx.GetTenantID(ctx)
	if tenantId == "" {
		log.Warn(ctx, "tenantId is empty")
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	if err := h.checkPermissionService.CheckAndModifyTenantPermissions(ctx, req.InstanceId, req.InstanceType, tenantId); err != nil {
		return nil, err
	}

	if err := h.detectionService.CheckTimeLegal(req.StartTime, req.EndTime); err != nil {
		return nil, err
	}

	resp := &model.DescribeAbnormalDetectionInfoResp{}
	detectionEvents, err := h.detectionService.GetAbnormalDetectionEvents(ctx, fwctx.GetTenantID(ctx), req)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	resp.AbnormalDetails = detectionEvents
	eventTotal, err := h.detectionService.GetTotalDetectionEvents(ctx, tenantId, req)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	resp.Total = eventTotal
	return resp, nil
}
