package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/abnormal_detection"
	"code.byted.org/infcs/dbw-mgr/biz/service/common"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
)

func NewDescribeAbnormalDetectionDetailHandler(in NewDescribeAbnormalDetectionDetailHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeAbnormalDetectionDetailHandler{
		detectionService:       in.DetectionService,
		checkPermissionService: in.CheckPermissionService,
	}
	return handler.NewHandler(h.DescribeAbnormalDetectionDetail)
}

type DescribeAbnormalDetectionDetailHandler struct {
	detectionService       abnormal_detection.AbnormalDetectionService
	checkPermissionService common.CheckPermissionService
}

type NewDescribeAbnormalDetectionDetailHandlerIn struct {
	dig.In
	DetectionService       abnormal_detection.AbnormalDetectionService
	CheckPermissionService common.CheckPermissionService
}

func (h *DescribeAbnormalDetectionDetailHandler) DescribeAbnormalDetectionDetail(ctx context.Context, req *model.DescribeAbnormalDetectionDetailReq) (*model.DescribeAbnormalDetectionDetailResp, error) {
	tenantId := fwctx.GetTenantID(ctx)
	if tenantId == "" {
		log.Warn(ctx, "tenantId is empty")
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	if err := h.checkPermissionService.CheckAndModifyTenantPermissions(ctx, req.InstanceId, req.InstanceType, tenantId); err != nil {
		return nil, err
	}

	if err := h.detectionService.CheckTimeLegal(req.StartTime, req.EndTime); err != nil {
		return nil, err
	}

	resp := &model.DescribeAbnormalDetectionDetailResp{}
	metricDatas, err := h.detectionService.GetMetricDetail(ctx, fwctx.GetTenantID(ctx), req)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	resp.ItemMetrics = metricDatas
	return resp, nil
}
