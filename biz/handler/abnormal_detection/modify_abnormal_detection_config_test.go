package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func mockModifyHandler() *ModifyAbnormalDetectionConfigHandler {
	return &ModifyAbnormalDetectionConfigHandler{
		detectionService:       &mocks.MockAbnormalDetectionService{},
		checkPermissionService: &mocks.MockCheckPermissionService{},
	}
}

func mockModifyReq() *model.ModifyAbnormalDetectionConfigReq {
	return &model.ModifyAbnormalDetectionConfigReq{Enable: true}
}

func TestModifyAbnormalDetectionConfigNormal(t *testing.T) {
	handler := mockModifyHandler()
	req := mockModifyReq()
	_, err := handler.ModifyAbnormalDetectionConfig(context.Background(), req)
	assert.NotNil(t, err)

	ctx := fwctx.SetBizContext(context.Background())
	c := fwctx.GetBizContext(ctx)
	c.TenantID = "12345"
	mock2 := mockey.Mock((*mocks.MockCheckPermissionService).CheckAndModifyTenantPermissions).Return(fmt.Errorf("test")).Build()
	_, err = handler.ModifyAbnormalDetectionConfig(ctx, req)
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockCheckPermissionService).CheckAndModifyTenantPermissions).Return(nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockAbnormalDetectionService).GetAbnormalDetectionConfig).Return(nil, fmt.Errorf("test")).Build()
	_, err = handler.ModifyAbnormalDetectionConfig(ctx, req)
	assert.NotNil(t, err)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*mocks.MockAbnormalDetectionService).GetAbnormalDetectionConfig).Return(&entity.DbwAbnormalDetectionConfig{}, nil).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*mocks.MockAbnormalDetectionService).CreateAbnormalDetectionConfig).Return(nil).Build()
	defer mock6.UnPatch()
	req.Enable = false
	_, err = handler.ModifyAbnormalDetectionConfig(ctx, req)
	assert.Nil(t, err)
}

func TestModifyAbnormalDetectionConfigError(t *testing.T) {
	handler := mockModifyHandler()
	req := mockModifyReq()
	ctx := fwctx.SetBizContext(context.Background())
	c := fwctx.GetBizContext(ctx)
	c.TenantID = "12345"
	mock2 := mockey.Mock((*mocks.MockCheckPermissionService).CheckAndModifyTenantPermissions).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockAbnormalDetectionService).GetAbnormalDetectionConfig).Return(nil, nil).Build()
	mock4 := mockey.Mock((*mocks.MockAbnormalDetectionService).CreateAbnormalDetectionConfig).Return(fmt.Errorf("test")).Build()
	defer mock4.UnPatch()
	_, err := handler.ModifyAbnormalDetectionConfig(ctx, req)
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*mocks.MockAbnormalDetectionService).GetAbnormalDetectionConfig).Return(&entity.DbwAbnormalDetectionConfig{}, nil).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*mocks.MockAbnormalDetectionService).ModifyAbnormalDetectionConfig).Return(fmt.Errorf("test")).Build()
	defer mock6.UnPatch()
	_, err = handler.ModifyAbnormalDetectionConfig(ctx, req)
	assert.NotNil(t, err)
}
