package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/abnormal_detection"
	"code.byted.org/infcs/dbw-mgr/biz/service/common"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
)

func NewDescribeAbnormalDetectionConfigHandler(in NewDescribeAbnormalDetectionConfigHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeAbnormalDetectionConfigHandler{
		detectionService:       in.DetectionService,
		checkPermissionService: in.CheckPermissionService,
	}
	return handler.<PERSON>Hand<PERSON>(h.DescribeAbnormalDetectionConfig)
}

type DescribeAbnormalDetectionConfigHandler struct {
	detectionService       abnormal_detection.AbnormalDetectionService
	checkPermissionService common.CheckPermissionService
}

type NewDescribeAbnormalDetectionConfigHandlerIn struct {
	dig.In
	DetectionService       abnormal_detection.AbnormalDetectionService
	CheckPermissionService common.CheckPermissionService
}

func (h *DescribeAbnormalDetectionConfigHandler) DescribeAbnormalDetectionConfig(ctx context.Context, req *model.DescribeAbnormalDetectionConfigReq) (*model.DescribeAbnormalDetectionConfigResp, error) {
	tenantId := fwctx.GetTenantID(ctx)
	if tenantId == "" {
		log.Warn(ctx, "tenantId is empty")
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	if err := h.checkPermissionService.CheckAndModifyTenantPermissions(ctx, req.InstanceId, req.InstanceType, tenantId); err != nil {
		return nil, err
	}
	detectionConfig, err := h.detectionService.GetAbnormalDetectionConfig(ctx, req.InstanceId, req.InstanceType.String())
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	resp := &model.DescribeAbnormalDetectionConfigResp{
		Enable: true,
	}
	if detectionConfig == nil || detectionConfig.Enable == 0 {
		resp.Enable = false
	}
	return resp, nil
}
