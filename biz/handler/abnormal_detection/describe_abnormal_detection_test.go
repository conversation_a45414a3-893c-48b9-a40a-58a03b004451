package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/metric_data"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"fmt"
	"github.com/qjpcpu/common.v2/json"
	"testing"
)

func TestDbwUserGet(t *testing.T) {

	resp := &model.DescribeAbnormalDetectionDetailResp{
		ItemMetrics: []*model.ItemMetric{mockItemMetric(&metric_data.CpuUsage{}), mockItemMetric(&metric_data.MemUsage{})},
	}
	jsonStr, _ := json.Marshal(resp)
	fmt.Printf("%s \n", jsonStr)
}

func mockItemMetric(item metric_data.MetricDataItem) *model.ItemMetric {
	return &model.ItemMetric{
		ItemName:           item.Name(),
		Group:              item.Group(),
		Unit:               item.Unit(),
		DataPoints:         []*model.DataPoint{{Value: 0.1, TimeStamp: 1698134400}, {Value: 0.2, TimeStamp: 1698134430}},
		AbnormalDataPoints: []*model.DataPoint{{Value: 0.1, TimeStamp: 1698134400}},
	}
}

func mockAbnormalPointDetail(item metric_data.MetricDataItem) *model.AbnormalPointDetail {
	return &model.AbnormalPointDetail{
		ItemName:  item.Name(),
		Group:     item.Group(),
		TimeStamp: 1698134400,
		Value:     0.1,
	}
}
