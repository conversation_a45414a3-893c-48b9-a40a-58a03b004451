package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func mockConfigHandler() *DescribeAbnormalDetectionConfigHandler {
	return &DescribeAbnormalDetectionConfigHandler{
		detectionService:       &mocks.MockAbnormalDetectionService{},
		checkPermissionService: &mocks.MockCheckPermissionService{},
	}
}

func mockDetectionConfigReq() *model.DescribeAbnormalDetectionConfigReq {
	return &model.DescribeAbnormalDetectionConfigReq{}
}

func TestDescribeAbnormalDetectionConfig(t *testing.T) {
	handler := mockConfigHandler()
	req := mockDetectionConfigReq()
	_, err := handler.DescribeAbnormalDetectionConfig(context.Background(), req)
	assert.NotNil(t, err)

	ctx := fwctx.SetBizContext(context.Background())
	c := fwctx.GetBizContext(ctx)
	c.TenantID = "12345"
	mock2 := mockey.Mock((*mocks.MockCheckPermissionService).CheckAndModifyTenantPermissions).Return(fmt.Errorf("test")).Build()
	_, err = handler.DescribeAbnormalDetectionConfig(ctx, req)
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockCheckPermissionService).CheckAndModifyTenantPermissions).Return(nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockAbnormalDetectionService).GetAbnormalDetectionConfig).Return(nil, fmt.Errorf("test")).Build()
	_, err = handler.DescribeAbnormalDetectionConfig(ctx, req)
	assert.NotNil(t, err)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*mocks.MockAbnormalDetectionService).GetAbnormalDetectionConfig).Return(&entity.DbwAbnormalDetectionConfig{}, nil).Build()
	defer mock5.UnPatch()
	res, err := handler.DescribeAbnormalDetectionConfig(ctx, req)
	assert.Nil(t, err)
	assert.Equal(t, false, res.Enable)
}
