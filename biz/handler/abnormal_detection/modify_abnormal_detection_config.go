package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/abnormal_detection"
	"code.byted.org/infcs/dbw-mgr/biz/service/common"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
)

func NewModifyAbnormalDetectionConfigHandler(in NewModifyAbnormalDetectionConfigHandlerIn) handler.HandlerImplementationEnvolope {
	h := &ModifyAbnormalDetectionConfigHandler{
		detectionService:       in.DetectionService,
		checkPermissionService: in.CheckPermissionService,
	}
	return handler.NewHandler(h.ModifyAbnormalDetectionConfig)
}

type ModifyAbnormalDetectionConfigHandler struct {
	detectionService       abnormal_detection.AbnormalDetectionService
	checkPermissionService common.CheckPermissionService
}

type NewModifyAbnormalDetectionConfigHandlerIn struct {
	dig.In
	DetectionService       abnormal_detection.AbnormalDetectionService
	CheckPermissionService common.CheckPermissionService
}

func (h *ModifyAbnormalDetectionConfigHandler) ModifyAbnormalDetectionConfig(ctx context.Context, req *model.ModifyAbnormalDetectionConfigReq) (*model.ModifyAbnormalDetectionConfigResp, error) {
	tenantId := fwctx.GetTenantID(ctx)
	if tenantId == "" {
		log.Warn(ctx, "tenantId is empty")
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	if err := h.checkPermissionService.CheckAndModifyTenantPermissions(ctx, req.InstanceId, req.InstanceType, tenantId); err != nil {
		return nil, err
	}
	detectionConfig, err := h.detectionService.GetAbnormalDetectionConfig(ctx, req.InstanceId, req.InstanceType.String())
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if detectionConfig == nil && req.Enable == true {
		// 如果不存在且需要打开，就新建一个
		if err := h.detectionService.CreateAbnormalDetectionConfig(ctx, fwctx.GetTenantID(ctx), req); err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
	} else if detectionConfig != nil && ChangeEnableToBool(detectionConfig.Enable) != req.Enable {
		// 如果存在且enable与配置文件不一样，就修改
		err := h.detectionService.ModifyAbnormalDetectionConfig(ctx, req)
		if err != nil {
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
	}
	resp := &model.ModifyAbnormalDetectionConfigResp{}
	return resp, nil
}

func ChangeEnableToBool(enable int8) bool {
	return enable == 1
}
