package data_migration

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/tos"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"go.uber.org/dig"
)

type DescribeDbExportDownloadUrlHandler struct {
	tosClient tos.Client
	c3Conf    c3.ConfigProvider
	loc       location.Location
	migRepo   repository.MigrationRepo
}

type DescribeDbExportDownloadUrlHandlerIn struct {
	dig.In
	C3Conf  c3.ConfigProvider
	Loc     location.Location
	MigRepo repository.MigrationRepo
}

func NewDescribeDbExportDownloadUrlHandler(d DescribeDbExportDownloadUrlHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeDbExportDownloadUrlHandler{
		c3Conf:  d.C3Conf,
		loc:     d.Loc,
		migRepo: d.MigRepo,
	}
	return handler.NewHandler(h.DescribeDbExportDownloadUrl)
}

func (h *DescribeDbExportDownloadUrlHandler) checkReq(req *model.DescribeDbExportDownloadUrlReq) error {
	if req.GetTaskId() == "" {
		return consts.ErrorOf(model.ErrorCode_TaskIdParamError)
	}
	return nil
}

func (h *DescribeDbExportDownloadUrlHandler) checkTOSClient(ctx context.Context) error {
	if h.tosClient == nil {
		err := h.initTOSConnection(ctx)
		if err != nil {
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
		return nil
	}
	return nil
}

// TODO move TOS functions to tos service.
func (h *DescribeDbExportDownloadUrlHandler) initTOSConnection(ctx context.Context) error {
	connectionInfo := new(tos.ConnectionInfo)
	c3Cfg := h.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	tosAK := c3Cfg.TOSServiceAccessKey
	tosSK := c3Cfg.TOSServiceSecretKey
	tosRegion := c3Cfg.TOSServiceRegion
	tosEndpoint := c3Cfg.TOSServiceEndpoint
	tosEndpoint = strings.Replace(tosEndpoint, "-s3", "", 1)
	connectionInfo = &tos.ConnectionInfo{
		Endpoint:        tosEndpoint,
		AccessKeyID:     tosAK,
		AccessKeySecret: tosSK,
		Region:          tosRegion,
		BucketName:      c3Cfg.TOSBucketName,
	}
	var client tos.Client
	if h.isInnerRegion(utils.GetRegion()) {
		client = tos.NewByteCloudTOSClient(ctx, connectionInfo)
	} else {
		client = tos.NewTOSClient(ctx, connectionInfo)
	}
	h.tosClient = client
	return nil
}
func (h *DescribeDbExportDownloadUrlHandler) DescribeDbExportDownloadUrl(ctx context.Context, req *model.DescribeDbExportDownloadUrlReq) (*model.DescribeDbExportDownloadUrlResp, error) {
	// check req params
	if err := h.checkReq(req); err != nil {
		return nil, err
	}
	// init TOSClient
	if err := h.checkTOSClient(ctx); err != nil {
		log.Warn(ctx, "TOS client connected failed!")
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	tenantId := fwctx.GetTenantID(ctx)
	bucketName := h.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace).TOSBucketName
	taskId, err := strconv.ParseInt(req.GetTaskId(), 10, 64)
	if err != nil {
		log.Warn(ctx, "req.TaskId format is invalid %s", req.GetTaskId())
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	taskInfo, err := h.migRepo.GetTask(ctx, taskId, tenantId)
	if err != nil {
		log.Warn(ctx, "Get task %s infos failed %+v", req.TaskId, err)
		e := consts.ErrorOf(model.ErrorCode_InternalError)
		return nil, e
	}
	if taskInfo.Status != uint8(model.DBMigrationStatus_SUCCESS) {
		msg := "only support download tasks which status is succeed"
		log.Warn(ctx, msg)
		return &model.DescribeDbExportDownloadUrlResp{
			FileUrl: ""}, nil
	}
	exportConfig := &entity.ExportConfig{}
	err = json.Unmarshal([]byte(taskInfo.Config), exportConfig)
	if err != nil {
		log.Warn(ctx, "export_config unmarshal failed:%+v", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if exportConfig.FileType == shared.CLOUD_DOC.String() {
		return h.formatInnerUrl(exportConfig)
	}

	return h.formatVocalUrl(ctx, taskInfo, bucketName)
}

func (h *DescribeDbExportDownloadUrlHandler) formatVocalUrl(ctx context.Context, taskInfo *entity.MigrationTask, bucketName string) (*model.DescribeDbExportDownloadUrlResp, error) {
	var objectName string
	if strings.HasSuffix(taskInfo.ObjectName, ".sql") {
		objectName = taskInfo.ObjectName + ".gz"
	} else {
		objectName = taskInfo.ObjectName + ".tar.gz"
	}
	log.Info(ctx, "task %d bucketName %s,objectName:%s", taskInfo.ID, bucketName, objectName)
	url, err := h.tosClient.DescribeDownloadUrl(ctx, bucketName, objectName)
	if err != nil {
		log.Warn(ctx, "Get task %s Exported Url failed %v", taskInfo.ID, err)
		e := consts.ErrorOf(model.ErrorCode_InternalError)
		return nil, e
	}
	ret := &model.DescribeDbExportDownloadUrlResp{
		FileUrl: url,
	}
	log.Info(ctx, "task %d DownloadUrl is %s", taskInfo.ID, ret)
	return ret, nil
}

func (h *DescribeDbExportDownloadUrlHandler) formatInnerUrl(exportConfig *entity.ExportConfig) (*model.DescribeDbExportDownloadUrlResp, error) {
	url := fmt.Sprintf("https://bytedance.larkoffice.com/sheets/%s", exportConfig.CloudFileToken)
	ret := &model.DescribeDbExportDownloadUrlResp{
		FileUrl: url,
	}
	return ret, nil
}

func (h *DescribeDbExportDownloadUrlHandler) isInnerRegion(region string) bool {
	return region == "cn-north-1-dedicated"
}
