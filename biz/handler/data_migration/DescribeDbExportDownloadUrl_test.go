package data_migration

import (
	"context"
	"errors"
	"strings"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	tos_lib "code.byted.org/infcs/dbw-mgr/biz/service/tos"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	dmRepoMocks "code.byted.org/infcs/dbw-mgr/biz/test/mocks/dmRepo"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/tos"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
)

type DescribeDbExportDownloadUrlTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *DescribeDbExportDownloadUrlTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *DescribeDbExportDownloadUrlTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestDescribeDbExportDownloadUrlTestSuite(t *testing.T) {
	suite.Run(t, new(DescribeDbExportDownloadUrlTestSuite))
}

func (suite *DescribeDbExportDownloadUrlTestSuite) TestCheckReqError() {
	c3cnf := mocks.NewMockC3ConfigProvider(suite.ctrl)
	repo := dmRepoMocks.NewMockMigrationRepo(suite.ctrl)
	loc := mocks.NewMockLocation(suite.ctrl)
	p := DescribeDbExportDownloadUrlHandlerIn{
		dig.In{},
		c3cnf, loc, repo,
	}
	describeDbExportDownloadUrlHandler := NewDescribeDbExportDownloadUrlHandler(p)
	fn := describeDbExportDownloadUrlHandler.Impl.Impl.(func(ctx context.Context, req *model.DescribeDbExportDownloadUrlReq) (*model.DescribeDbExportDownloadUrlResp, error))
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()

	testCases := []struct {
		name string
		req  *model.DescribeDbExportDownloadUrlReq
		want error
	}{
		{
			name: "empty TaskId",
			req:  &model.DescribeDbExportDownloadUrlReq{},
			want: consts.ErrorOf(model.ErrorCode_TaskIdParamError),
		},
	}

	for _, testCase := range testCases {
		suite.Suite.Run(testCase.name, func() {
			ret, got := fn(ctx, testCase.req)
			suite.Empty(ret)
			suite.Equal(testCase.want, got)
		})
	}
}

func (suite *DescribeDbExportDownloadUrlTestSuite) TestCheckTosClientOK() {
	c3cnf := mocks.NewMockC3ConfigProvider(suite.ctrl)
	repo := dmRepoMocks.NewMockMigrationRepo(suite.ctrl)
	loc := mocks.NewMockLocation(suite.ctrl)
	ctx := context.Background()
	describeDbExportDownloadUrlHandler := &DescribeDbExportDownloadUrlHandler{
		c3Conf:  c3cnf,
		migRepo: repo,
		loc:     loc,
	}
	if describeDbExportDownloadUrlHandler.tosClient == nil {
		connectionInfo := new(tos_lib.ConnectionInfo)
		applicationConfig := struct {
			TOSServiceSecretKey string
			TOSServiceAccessKey string
			TOSServiceRegion    string
			TOSServiceEndpoint  string
		}{
			TOSServiceSecretKey: "111",
			TOSServiceAccessKey: "222",
			TOSServiceRegion:    "boe-nanjing-xxx",
			TOSServiceEndpoint:  "tos-s3-cn-nanjing-xxx.volces.com",
		}
		c3cnf.EXPECT().GetNamespace(ctx, gomock.Any()).Return(
			&config.C3Config{Application: config.Application{DbwAccountId: "1"}, Aksk: config.Aksk{IamAksk: "xxx"}}).AnyTimes()
		tosAK := applicationConfig.TOSServiceAccessKey
		tosSK := applicationConfig.TOSServiceSecretKey
		tosRegion := applicationConfig.TOSServiceRegion
		tosEndpoint := applicationConfig.TOSServiceEndpoint
		tosEndpoint = strings.Replace(tosEndpoint, "-s3", "", 1)
		connectionInfo = &tos_lib.ConnectionInfo{
			Endpoint:        tosEndpoint,
			AccessKeyID:     tosAK,
			AccessKeySecret: tosSK,
			Region:          tosRegion,
		}
		client := tos_lib.NewTOSClient(ctx, connectionInfo)
		describeDbExportDownloadUrlHandler.tosClient = client
	}
	err := describeDbExportDownloadUrlHandler.initTOSConnection(ctx)
	suite.Empty(err)
}

func (suite *DescribeDbExportDownloadUrlTestSuite) TestDescribeDbExportDownloadUrlError() {
	c3mock := mocks.NewMockC3ConfigProvider(suite.ctrl)
	repo := dmRepoMocks.NewMockMigrationRepo(suite.ctrl)
	loc := mocks.NewMockLocation(suite.ctrl)
	tosclient := tos.NewMockClient(suite.ctrl)
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	c3mock.EXPECT().GetNamespace(ctx, gomock.Any()).Return(
		&config.C3Config{Application: config.Application{DbwAccountId: "1"}, Aksk: config.Aksk{IamAksk: "xxx"}}).AnyTimes()
	taskInfo := &entity.MigrationTask{Status: uint8(model.DBMigrationStatus_SUCCESS), ObjectName: "sql.tar.gz"}
	repo.EXPECT().GetTask(gomock.Any(), gomock.Any(), gomock.Any()).Return(taskInfo, nil)
	tosclient.EXPECT().DescribeDownloadUrl(gomock.Any(), gomock.Any(), gomock.Any()).Return("", errors.New("ddd")).AnyTimes()
	h := &DescribeDbExportDownloadUrlHandler{
		tosclient, c3mock, loc, repo,
	}
	Mock((*h).checkTOSClient).Return(nil).Build()
	req := &model.DescribeDbExportDownloadUrlReq{TaskId: "1"}

	suite.Suite.Run("xxx", func() {
		_, err := h.DescribeDbExportDownloadUrl(ctx, req)
		suite.NotEmpty(err)
	})
}
