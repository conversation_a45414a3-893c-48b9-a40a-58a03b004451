package console

import (
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"go.uber.org/dig"
	"gorm.io/gorm"
)

type UpdateConsoleConnEnvsHandlerIn struct {
	dig.In
	EnvRepo  repository.ConsoleConnEnvRepo
	IdgenSvc idgen.Service
}

func NewUpdateConsoleConnEnvHandler(in UpdateConsoleConnEnvsHandlerIn) handler.HandlerImplementationEnvolope {
	h := &UpdateConsoleConnEnvHandler{
		envRepo:  in.EnvRepo,
		idgenSvc: in.IdgenSvc,
	}
	return handler.NewHandler(h.UpdateConsoleConnEnv)
}

type UpdateConsoleConnEnvHandler struct {
	envRepo  repository.ConsoleConnEnvRepo
	idgenSvc idgen.Service
}

func (c *UpdateConsoleConnEnvHandler) UpdateConsoleConnEnv(ctx context.Context, req *model.UpdateConsoleConnEnvReq) (*model.UpdateConsoleConnEnvResp, error) {
	if err := c.createOrUpdate(ctx, req); err != nil {
		return nil, err
	}
	return &model.UpdateConsoleConnEnvResp{}, nil
}

func (c *UpdateConsoleConnEnvHandler) createOrUpdate(ctx context.Context, req *model.UpdateConsoleConnEnvReq) error {
	env, err := c.envRepo.GetByAccountName(ctx, req.InstanceId, req.AccountName)
	if err != nil {
		log.Warn(ctx, "get env by account error %s", err)
		if err != gorm.ErrRecordNotFound {
			return consts.ErrorOf(model.ErrorCode_UpdateConsoleConnEnvFailed)
		}
	}
	if env == nil {
		return c.create(ctx, req)
	}
	env.Env = req.GetEnv()
	if req.GetPassword() != "" {
		env.Password = req.GetPassword()
	}
	if err := c.envRepo.Update(ctx, env); err != nil {
		log.Warn(ctx, "update console conn env error %s", err)
		return consts.ErrorOf(model.ErrorCode_UpdateConsoleConnEnvFailed)
	}
	return nil
}

func (c *UpdateConsoleConnEnvHandler) create(ctx context.Context, req *model.UpdateConsoleConnEnvReq) error {
	id, err := c.idgenSvc.NextIDStr(ctx)
	if err != nil {
		log.Warn(ctx, "generate id error %s", err)
		return consts.ErrorOf(model.ErrorCode_UpdateConsoleConnEnvFailed)
	}
	env := &entity.ConsoleConnEnv{
		ID:           id,
		InstanceType: req.InstanceType,
		InstanceId:   req.GetInstanceId(),
		AccountName:  req.AccountName,
		Env:          req.GetEnv(),
	}
	if err := c.envRepo.Create(ctx, env); err != nil {
		log.Warn(ctx, "create console conn env error %s", err)
		return consts.ErrorOf(model.ErrorCode_UpdateConsoleConnEnvFailed)
	}
	return nil
}
