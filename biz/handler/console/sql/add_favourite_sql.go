package sql

import (
	"context"
	"time"

	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

type AddMyFavouriteSQLHandlerIn struct {
	dig.In
	SQLRepo  repository.FavouriteSQLRepo
	IdgenSvc idgen.Service
}

func NewAddMyFavouriteSQLHandler(in AddMyFavouriteSQLHandlerIn) handler.HandlerImplementationEnvolope {
	h := &AddMyFavouriteSQLHandler{
		sqlRepo:  in.SQLRepo,
		idgenSvc: in.IdgenSvc,
	}
	return handler.NewHandler(h.AddMyFavouriteSQL)
}

type AddMyFavouriteSQLHandler struct {
	sqlRepo  repository.FavouriteSQLRepo
	idgenSvc idgen.Service
}

func (c *AddMyFavouriteSQLHandler) AddMyFavouriteSQL(ctx context.Context, req *model.AddMyFavouriteSQLReq) (*model.AddMyFavouriteSQLResp, error) {
	id, err := c.idgenSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "generate id error %s", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	favouriteSql := &entity.FavouriteSQL{
		ID:           id,
		InstanceType: req.InstanceType,
		InstanceId:   req.InstanceId,
		DBName:       req.GetDBName(),
		SQLText:      req.SQLText,
		Scope:        req.GetScope(),
		Title:        req.Title,
		TenantId:     fwctx.GetTenantID(ctx),
		UserId:       fwctx.GetUserID(ctx),
		CreateTime:   time.Now().Unix(),
	}
	err = c.sqlRepo.Create(ctx, favouriteSql)
	return &model.AddMyFavouriteSQLResp{}, err
}
