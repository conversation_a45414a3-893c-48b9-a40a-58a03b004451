package sql

import (
	"context"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"go.uber.org/dig"
	"gorm.io/gorm"
)

type UpdateMyFavouriteSQLHandlerIn struct {
	dig.In
	SQLRepo repository.FavouriteSQLRepo
}

func NewUpdateMyFavouriteSQLHandler(in DescribeMyFavouriteSQLHandlerIn) handler.HandlerImplementationEnvolope {
	h := &UpdateMyFavouriteSQLHandler{
		sqlRepo: in.SQLRepo,
	}
	return handler.NewHandler(h.UpdateMyFavouriteSQL)
}

type UpdateMyFavouriteSQLHandler struct {
	sqlRepo repository.FavouriteSQLRepo
}

func (c *UpdateMyFavouriteSQLHandler) UpdateMyFavouriteSQL(ctx context.Context, req *model.UpdateMyFavouriteSQLReq) (*model.UpdateMyFavouriteSQLResp, error) {
	sql, err := c.sqlRepo.Get(ctx, req.ID)
	if err != nil {
		log.Warn(ctx, "get favourite sql error %s", err)
		if err == gorm.ErrRecordNotFound {
			return nil, consts.ErrorOf(model.ErrorCode_ResourceNotFoundInvalid)
		}
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if req.SQLText != nil {
		sql.SQLText = req.GetSQLText()
	}
	if req.Scope != nil {
		sql.Scope = req.GetScope()
	}
	if req.Title != nil {
		sql.Title = req.GetTitle()
	}
	sql.ModifyTime = time.Now().Unix()
	err = c.sqlRepo.Update(ctx, sql)
	if err != nil {
		log.Warn(ctx, "update favourite sql error %s", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return &model.UpdateMyFavouriteSQLResp{}, nil
}
