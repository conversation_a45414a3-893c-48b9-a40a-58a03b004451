package sql

import (
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"go.uber.org/dig"
	"gorm.io/gorm"
)

type DeleteMyFavouriteSQLHandlerIn struct {
	dig.In
	SQLRepo repository.FavouriteSQLRepo
}

func NewDeleteMyFavouriteSQLHandler(in DescribeMyFavouriteSQLHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DeleteMyFavouriteSQLHandler{
		sqlRepo: in.SQLRepo,
	}
	return handler.NewHandler(h.DeleteMyFavouriteSQL)
}

type DeleteMyFavouriteSQLHandler struct {
	sqlRepo repository.FavouriteSQLRepo
}

func (c *DeleteMyFavouriteSQLHandler) DeleteMyFavouriteSQL(ctx context.Context, req *model.UpdateMyFavouriteSQLReq) (*model.UpdateMyFavouriteSQLResp, error) {
	_, err := c.sqlRepo.Get(ctx, req.ID)
	if err != nil {
		log.Warn(ctx, "get favourite sql error %s", err)
		if err == gorm.ErrRecordNotFound {
			return nil, consts.ErrorOf(model.ErrorCode_ResourceNotFoundInvalid)
		}
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if err := c.sqlRepo.Delete(ctx, req.ID); err != nil {
		log.Warn(ctx, "delete favourite sql error %s", err)
		return nil, consts.ErrorOf(model.ErrorCode_ResourceNotFoundInvalid)
	}
	return &model.UpdateMyFavouriteSQLResp{}, nil
}
