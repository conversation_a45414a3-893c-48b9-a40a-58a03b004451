package sql

import (
	"context"
	"strconv"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

type DescribeMyFavouriteSQLHandlerIn struct {
	dig.In
	SQLRepo repository.FavouriteSQLRepo
}

func NewDescribeMyFavouriteSQLHandler(in DescribeMyFavouriteSQLHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeMyFavouriteSQLHandler{
		sqlRepo: in.SQLRepo,
	}
	return handler.<PERSON>Handler(h.DescribeMyFavouriteSQL)
}

type DescribeMyFavouriteSQLHandler struct {
	sqlRepo repository.FavouriteSQLRepo
}

func (c *DescribeMyFavouriteSQLHandler) DescribeMyFavouriteSQL(ctx context.Context, req *model.DescribeMyFavouriteSQLReq) (*model.DescribeMyFavouriteSQLResp, error) {
	sqlsEntity, err := c.sqlRepo.List(ctx, req.InstanceId, req.DBName)
	if err != nil {
		log.Warn(ctx, "list favourite sql error %s", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	var resp = &model.DescribeMyFavouriteSQLResp{}
	fp.StreamOf(sqlsEntity).Map(func(sql *entity.FavouriteSQL) *model.FavouriteSQL {
		return convertSqlEntityToModel(sql)
	}).ToSlice(&resp.SQLs)
	return resp, nil
}

func convertSqlEntityToModel(sql *entity.FavouriteSQL) *model.FavouriteSQL {
	return &model.FavouriteSQL{
		ID:         strconv.FormatInt(sql.ID, 10),
		Title:      sql.Title,
		SQLText:    sql.SQLText,
		Scope:      sql.Scope,
		CreateTime: strconv.FormatInt(sql.CreateTime, 10),
	}
}
