package collector

import (
	"context"
	"math"
	"os"
	"sync"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

type DescribeInstanceListHandler struct {
	ActorClient cli.ActorClient
	tenantDAL   dal.TenantDAL
	source      datasource.DataSourceService
	cnf         config.ConfigProvider
}

func NewDescribeInstanceListHandler(ActorClient cli.ActorClient, tenantDAL dal.TenantDAL, source datasource.DataSourceService, cnf config.ConfigProvider) handler.HandlerImplementationEnvolope {
	h := &DescribeInstanceListHandler{
		ActorClient: ActorClient,
		tenantDAL:   tenantDAL,
		source:      source,
		cnf:         cnf,
	}
	return handler.NewHandler(h.DescribeInstanceList)
}

// DescribeInstanceList 根据条件查询所有租户下的实例列表
func (d *DescribeInstanceListHandler) DescribeInstanceList(ctx context.Context, req *model.DescribeInstanceListReq) (*model.DescribeInstanceListResp, error) {
	var results []*model.CollectorInstanceInfo
	// 字节云场景
	if d.cnf.Get(ctx).EnableByteRDS {
		var instanceList []*model.CollectorInstanceInfo
		byteInstances, err := d.getAllByteRdsInstances(ctx, req)
		if err != nil {
			return nil, err
		}
		for _, instanceInfo := range byteInstances {
			info := &model.CollectorInstanceInfo{
				InstanceId:   *instanceInfo.InstanceId,
				InstanceType: req.InstanceType,
			}
			instanceList = append(instanceList, info)
		}
		result := &model.DescribeInstanceListResp{
			InstanceList: instanceList,
		}
		return result, nil
	}
	// 若实例类型为vedb,则需要判断当前region是否部署vedb
	if req.GetInstanceType() == model.InstanceType_VeDBMySQL && !d.cnf.Get(ctx).EnableVeDB {
		return &model.DescribeInstanceListResp{
			InstanceList: results,
		}, nil
	}
	// 若实例类型为sharding,则需要判断当前region是否部署sharding
	if req.GetInstanceType() == model.InstanceType_MySQLSharding && !d.cnf.Get(ctx).EnableSharding {
		return &model.DescribeInstanceListResp{
			InstanceList: results,
		}, nil
	}
	//FIXME: 暂时不支持mongo,因mongo接口性能太差导致大region无法拉取实例
	//if req.GetInstanceType() == model.InstanceType_Mongo {
	//	return &model.DescribeInstanceListResp{
	//		InstanceList: results,
	//	}, nil
	//}
	//先查询所有的租户信息
	tenants, err := d.tenantDAL.GetAllTenants(ctx)
	if err != nil {
		return nil, err
	}
	var wg sync.WaitGroup
	// 控制并发度的通道
	cnf := d.cnf.Get(ctx)
	concurrency := cnf.DbwListInstanceConcurrency // 并发度配置
	sem := make(chan struct{}, concurrency)
	// 创建结果 channel
	resultChannel := make(chan []*model.CollectorInstanceInfo)
	wg.Add(len(tenants) + 1)
	log.Info(ctx, "tenant size:%v", len(tenants))
	go func() {
		defer wg.Done()
		var count int64 = 0
		for {
			if count >= int64(len(tenants)) {
				break
			}
			list := <-resultChannel
			results = append(results, list...)
			count++
		}
	}()
	//遍历租户信息，按照租户维度，调用接口查询实例数据
	for _, tenantInfo := range tenants {
		sem <- struct{}{} // 占用一个信号
		var isDiff bool
		tenantId := tenantInfo.TenantId
		// 新增租户加入的时间在最近12h内，全量数据采集
		if time.Now().UnixMilli()-tenantInfo.CreateTimeMS <= 43200000 && req.IsPreHour {
			isDiff = false
		} else {
			isDiff = req.IsPreHour
		}
		//此处需要将tenantId传进去，否则开异步协程的情况下，会导致tenantId污染
		go func(currentTid string, IsPreHour bool) {
			defer func() {
				<-sem
			}()
			defer wg.Done()
			var instanceList []*model.CollectorInstanceInfo
			instances, err := d.listInstances(ctx, currentTid, shared.DataSourceType(req.InstanceType), IsPreHour)
			if err != nil {
				log.Error(ctx, "DescribeInstanceList listInstances err, tenantID:%v, err:%v", currentTid, err)
				resultChannel <- instanceList
				return
			}
			log.Info(ctx, "DescribeInstanceList listInstances TenantId:%v  finished! instances size is:%v", currentTid, len(instances))
			for _, instanceInfo := range instances {
				//过滤掉所有非运行中的实例
				if instanceInfo.InstanceStatus != "Running" {
					continue
				}
				info := &model.CollectorInstanceInfo{
					InstanceId:   *instanceInfo.InstanceId,
					InstanceType: req.InstanceType,
					TenantId:     currentTid,
				}
				instanceList = append(instanceList, info)
			}
			resultChannel <- instanceList
		}(tenantId, isDiff)
	}
	wg.Wait()
	close(sem)
	close(resultChannel)
	log.Info(ctx, "DescribeInstanceList finished! results size is:%v", len(results))
	ret := &model.DescribeInstanceListResp{
		InstanceList: results,
		Total:        int32(len(results)),
	}
	return ret, nil
}

func (d *DescribeInstanceListHandler) listInstances(ctx context.Context, tenantID string, dsType shared.DataSourceType, IsPreHour bool) ([]*model.InstanceInfo, error) {
	var instances []*model.InstanceInfo
	var pageNumber, pageSize, cnt int32 = 1, 100, 0
	query := &datasource.ListInstanceReq{
		Type:           dsType,
		LinkType:       shared.Volc,
		PageNumber:     pageNumber,
		PageSize:       pageSize,
		InstanceStatus: "Running",
		RegionId:       os.Getenv(`BDC_REGION_ID`),
		TenantId:       tenantID,
	}
	if dsType == shared.MetaMySQL {
		query.LinkType = shared.Public
	}
	//查询24小时内的数据
	if IsPreHour {
		endTime := time.Now().UTC()
		startTime := endTime.Add(-24 * time.Hour)
		layout := "2006-01-02T15:04:05Z"
		CreateTimeStart := startTime.Format(layout)
		CreateTimeEnd := endTime.Format(layout)
		query.CreateTimeStart = CreateTimeStart
		query.CreateTimeEnd = CreateTimeEnd
	}
	for {
		select {
		case <-ctx.Done():
			return nil, nil
		default:
			query.PageNumber = pageNumber
			query.PageSize = pageSize
			resp, err := d.source.ListInstanceLightWeight(ctx, query)
			if err != nil {
				log.Warn(ctx, "failed to list instance, err=%v", err)
				return []*model.InstanceInfo{}, err
			}
			instances = append(instances, resp.InstanceList...)
			cnt += int32(len(resp.InstanceList))
			if cnt >= int32(resp.Total) || int32(len(resp.InstanceList)) < pageSize {
				return instances, nil
			}
			pageNumber += 1
		}
	}
}

func (d *DescribeInstanceListHandler) getAllByteRdsInstances(ctx context.Context, req *model.DescribeInstanceListReq) ([]*model.InstanceInfo, error) {
	var (
		instanceList    []*model.InstanceInfo
		DefaultPageSize int32
		wg              sync.WaitGroup
	)
	DefaultPageSize = 100
	if req.GetInstanceType() == model.InstanceType_ByteDoc {
		DefaultPageSize = 10 // 当前默认为10，且不支持配置
	}
	query := &datasource.ListInstanceReq{
		Type:       conv.ToSharedTypeV2(req.InstanceType),
		LinkType:   shared.Volc,
		RegionId:   req.GetRegionId(),
		PageNumber: 1,
		PageSize:   10,
	}
	testEnv := os.Getenv("BYTE_REGION_ID")
	log.Info(ctx, "os region is %s,req region is %s userId is %s", testEnv, req.GetRegionId(), fwctx.GetUserID(ctx))
	resp, err := d.source.ListInstance(ctx, query)
	if err != nil {
		log.Warn(ctx, "failed to list instance, err=%v", err)
		return nil, err
	}
	instancesChannel := make(chan *model.InstanceInfo, resp.Total)
	totalPages := int32(math.Ceil(float64(resp.Total) / float64(DefaultPageSize))) // 获取总页数
	if totalPages >= 1 {
		for pageNumber := int32(1); pageNumber <= totalPages; pageNumber++ {
			wg.Add(1)
			go func(pageNumber int32) {
				defer wg.Done()
				query := &datasource.ListInstanceReq{
					Type:       conv.ToSharedTypeV2(req.InstanceType),
					LinkType:   shared.Volc,
					RegionId:   req.GetRegionId(),
					PageNumber: pageNumber,
					PageSize:   DefaultPageSize,
				}
				resp, err := d.source.ListInstance(ctx, query)
				if err != nil {
					log.Warn(ctx, "failed to list instance, err=%v", err)
					return
				}
				for _, instance := range resp.InstanceList {
					instancesChannel <- instance
				}
			}(pageNumber)
		}
		go func() {
			wg.Wait()
			close(instancesChannel)
		}()

		for instance := range instancesChannel {
			instanceList = append(instanceList, instance)
		}
	} else {
		instanceList = append(instanceList, resp.InstanceList...)
	}
	return instanceList, nil
}
