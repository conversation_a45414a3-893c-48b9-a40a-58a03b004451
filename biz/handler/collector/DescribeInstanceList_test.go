package collector

import (
	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"github.com/bytedance/mockey"
	"testing"
)

func TestDescribeInstanceList(t *testing.T) {
	handler := DescribeInstanceListHandler{
		ActorClient: &mocks.MockActorClient{},
		tenantDAL:   &mocks.MockTenantDAL{},
		source:      &mocks.MockDataSourceService{},
		cnf:         &mocks.MockConfigProvider{},
	}

	tenants := []*dao.Tenant{}

	tenant := &dao.Tenant{
		ID:       1,
		TenantId: "123",
	}
	tenants = append(tenants, tenant)

	resp := &datasource.ListInstanceResp{
		Total: 2,
		InstanceList: []*model.InstanceInfo{
			{
				InstanceId:     utils.StringRef("mysql_1"),
				InstanceStatus: "Running",
			},
			{
				InstanceId:     utils.StringRef("mysql_2"),
				InstanceStatus: "Running"},
		},
	}
	configResp := &config.Config{
		DbwListInstanceConcurrency: 10,
	}
	mock1 := mockey.Mock((*mocks.MockTenantDAL).GetAllTenants).Return(tenants, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockDataSourceService).ListInstanceLightWeight).Return(resp, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockConfigProvider).Get).Return(configResp).Build()
	defer mock3.UnPatch()
	ctx := context.Background()
	requset := &model.DescribeInstanceListReq{}
	list, err := handler.DescribeInstanceList(ctx, requset)
	if err != nil {
		t.Fatal("TestDescribeInstanceList failed!", err)
	}
	if len(list.InstanceList) == 0 {
		t.Fatal("TestDescribeInstanceList result failed!", err)
	}
}
func TestDescribeInstanceList1(t *testing.T) {
	handler := DescribeInstanceListHandler{
		ActorClient: &mocks.MockActorClient{},
		tenantDAL:   &mocks.MockTenantDAL{},
		source:      &mocks.MockDataSourceService{},
		cnf:         &mocks.MockConfigProvider{},
	}

	tenants := []*dao.Tenant{}

	tenant := &dao.Tenant{
		ID:       1,
		TenantId: "123",
	}
	tenants = append(tenants, tenant)

	resp := &datasource.ListInstanceResp{
		Total: 2,
		InstanceList: []*model.InstanceInfo{
			{
				InstanceId:     utils.StringRef("mysql_1"),
				InstanceStatus: "Running",
			},
			{
				InstanceId:     utils.StringRef("mysql_2"),
				InstanceStatus: "Running"},
		},
	}
	configResp := &config.Config{
		DbwListInstanceConcurrency: 10,
	}
	mock1 := mockey.Mock((*mocks.MockTenantDAL).GetAllTenants).Return(tenants, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockDataSourceService).ListInstanceLightWeight).Return(resp, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockConfigProvider).Get).Return(configResp).Build()
	defer mock3.UnPatch()
	ctx := context.Background()
	requset := &model.DescribeInstanceListReq{
		InstanceType: model.InstanceType_Mongo,
	}
	_, err := handler.DescribeInstanceList(ctx, requset)
	if err != nil {
		t.Fatal("TestDescribeInstanceList failed!", err)
	}
}
