package collector

import (
	"context"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"github.com/bytedance/mockey"
)

func TestDescribeDataSource(t *testing.T) {
	handler := DescribeDataSourceHandler{
		idSvc:       &mocks.MockService{},
		actorClient: &mocks.MockActorClient{},
		loc:         &mocks.MockLocation{},
		source:      &mocks.MockDataSourceService{},
		c3Conf:      &mocks.MockC3ConfigProvider{},
		cnf:         &mocks.MockConfigProvider{},
	}

	c3Config := &config.C3Config{
		Application: config.Application{DbwAccountPasswordGenKey: "dbw_internal_account"},
	}

	config := &config.Config{ConnectionConnectTimeout: 1000, ConnectionReadTimeout: 1000, ConnectionWriteTimeout: 1000, ConnectionIdleTimeout: 1000}

	detailResp := &datasource.DescribeDBInstanceDetailResp{
		InstanceStatus: "Running",
	}

	resps := []*datasource.DescribeInstanceAddressResp{}
	for i := 0; i < 3; i++ {
		resps = append(resps, &datasource.DescribeInstanceAddressResp{
			NodeId: "123",
			IP:     "123",
			Port:   123,
		})
	}

	mock1 := mockey.Mock((*mocks.MockService).NextIDStr).Return(11111, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockC3ConfigProvider).GetNamespace).Return(c3Config).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockConfigProvider).Get).Return(config).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(detailResp, nil).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*mocks.MockDataSourceService).EnsureAccount).Return(nil).Build()
	defer mock5.UnPatch()
	//mock5 := mockey.Mock((*mocks.MockDataSourceService).DeleteAccount).Return(nil).Build()
	//defer mock5.UnPatch()
	//mock6 := mockey.Mock((*mocks.MockDataSourceService).CreateAccount).Return(nil).Build()
	//defer mock6.UnPatch()
	//mock7 := mockey.Mock((*mocks.MockDataSourceService).GrantAccountPrivilege).Return(nil).Build()
	//defer mock7.UnPatch()
	mock9 := mockey.Mock((*mocks.MockDataSourceService).FillDataSource).Return(nil).Build()
	defer mock9.UnPatch()
	mock10 := mockey.Mock((*mocks.MockDataSourceService).OpenTunnel).Return(nil).Build()
	defer mock10.UnPatch()
	mock11 := mockey.Mock((*mocks.MockDataSourceService).DescribeInstanceAddressList).Return(resps, nil).Build()
	defer mock11.UnPatch()
	mock12 := mockey.Mock((*mocks.MockDataSourceService).FillInnerDataSource).Return(nil).Build()
	defer mock12.UnPatch()

	request := &model.DescribeDataSourceReq{
		InstanceId:   "123",
		InstanceType: model.InstanceType_MySQL,
		HostIP:       "333333",
	}
	ctx := context.Background()
	source, err := handler.DescribeDataSource(ctx, request)
	if err != nil {
		t.Fatal("TestDescribeDataSource failed!", err)
	}
	if len(source.DataSourceList) == 0 {
		t.Fatal("TestDescribeDataSource result failed!", err)
	}

	request1 := &model.DescribeDataSourceReq{
		InstanceId:   "123",
		InstanceType: model.InstanceType_VeDBMySQL,
		HostIP:       "333333",
	}
	source, err = handler.DescribeDataSource(ctx, request1)
	if err != nil {
		t.Fatal("TestDescribeDataSource failed!", err)
	}
	if len(source.DataSourceList) == 0 {
		t.Fatal("TestDescribeDataSource result failed!", err)
	}
	request2 := &model.DescribeDataSourceReq{
		InstanceId:   "123",
		InstanceType: model.InstanceType_Postgres,
		HostIP:       "333333",
	}
	source, err = handler.DescribeDataSource(ctx, request2)
	if err != nil {
		t.Fatal("TestDescribeDataSource failed!", err)
	}
	if len(source.DataSourceList) == 0 {
		t.Fatal("TestDescribeDataSource result failed!", err)
	}
	request3 := &model.DescribeDataSourceReq{
		InstanceId:   "123",
		InstanceType: model.InstanceType_MySQLSharding,
		HostIP:       "333333",
	}
	source, err = handler.DescribeDataSource(ctx, request3)
	if err != nil {
		t.Fatal("TestDescribeDataSource failed!", err)
	}
	if len(source.DataSourceList) == 0 {
		t.Fatal("TestDescribeDataSource result failed!", err)
	}
	request4 := &model.DescribeDataSourceReq{
		InstanceId:   "123",
		InstanceType: model.InstanceType_MetaMySQL,
		HostIP:       "333333",
	}
	source, err = handler.DescribeDataSource(ctx, request4)
	if err != nil {
		t.Fatal("TestDescribeDataSource failed!", err)
	}
	if len(source.DataSourceList) == 0 {
		t.Fatal("TestDescribeDataSource result failed!", err)
	}
	request5 := &model.DescribeDataSourceReq{
		InstanceId:   "123",
		InstanceType: model.InstanceType_Mongo,
		HostIP:       "333333",
	}
	source, err = handler.DescribeDataSource(ctx, request5)
	if err != nil {
		t.Fatal("TestDescribeDataSource failed!", err)
	}
	if len(source.DataSourceList) == 0 {
		t.Fatal("TestDescribeDataSource result failed!", err)
	}
}
