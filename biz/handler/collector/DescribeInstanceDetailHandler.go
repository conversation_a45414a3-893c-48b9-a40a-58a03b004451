package collector

import (
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
)

type DescribeInstanceDetailHandler struct {
	source datasource.DataSourceService
}

type DescribeInstanceDetailHandlerIn struct {
	Source datasource.DataSourceService
}

func NewDescribeInstanceDetailHandler(in DescribeInstanceDetailHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeInstanceDetailHandler{
		source: in.Source,
	}
	return handler.NewHandler(h.DescribeInstanceDetail)
}

// DescribeInstanceDetail 根据条件查询所有租户下的实例列表
func (d *DescribeInstanceDetailHandler) DescribeInstanceDetail(ctx context.Context, req *model.DescribeInstanceDetailReq) (*model.DescribeInstanceDetailResp, error) {

	request := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
		Type:       shared.DataSourceType(req.InstanceType),
	}
	detail, err := d.source.DescribeDBInstanceDetail(ctx, request)
	if err != nil {
		log.Info(ctx, "DescribeInstanceDetail err, instanceID is:%v, err is:%v", "1213123", err)
		return nil, err
	}

	result := &model.DescribeInstanceDetailResp{
		InstanceId:      detail.InstanceId,
		InstanceName:    detail.InstanceName,
		InstanceStatus:  detail.InstanceStatus,
		RegionId:        detail.RegionId,
		ZoneId:          detail.ZoneId,
		DBEngine:        detail.DBEngine,
		DBEngineVersion: detail.DBEngineVersion,
	}

	return result, nil
}
