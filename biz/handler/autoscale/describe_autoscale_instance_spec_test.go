package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
)

func TestNewDescribeAutoScaleInstanceSpecHandler(t *testing.T) {
	NewDescribeAutoScaleInstanceSpecHandler(NewDescribeAutoScaleInstanceSpecIn{})
}

func TestCheckReq(t *testing.T) {
	hder := DescribeAutoScaleInstanceSpecHandler{
		service: &mocks.MockAutoScaleService{},
		ds:      &mocks.MockDataSourceService{},
	}
	hder.checkReq(context.Background(), &model.DescribeAutoScaleInstanceSpecReq{
		InstanceType: 0,
		InstanceId:   "",
	})
	hder.checkReq(context.Background(), nil)
}

func TestDescribeAutoScaleInstanceSpec(t *testing.T) {
	hder := DescribeAutoScaleInstanceSpecHandler{
		service: &mocks.MockAutoScaleService{},
		ds:      &mocks.MockDataSourceService{},
	}
	hder.DescribeAutoScaleInstanceSpec(context.Background(), nil)

	mock1 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(nil, fmt.Errorf("err")).Build()
	hder.DescribeAutoScaleInstanceSpec(context.Background(), &model.DescribeAutoScaleInstanceSpecReq{
		InstanceType: 0,
		InstanceId:   "",
	})
	mock1.UnPatch()

	mock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{
		InstanceId:         "",
		InstanceName:       "",
		InstanceStatus:     "",
		RegionId:           "",
		ZoneId:             "",
		DBEngine:           "",
		DBEngineVersion:    "",
		InstanceType:       "",
		VCPU:               0,
		Memory:             0,
		StorageSpace:       0,
		ProjectName:        "",
		VPCID:              "",
		NodeInfos:          nil,
		ConnectionsInfos:   nil,
		Endpoints:          nil,
		DBEngineSubVersion: "",
		NodeSpec:           "",
		NoAuthMode:         nil,
		AllowListInfo:      datasource.AllowListInfo{},
		SpecFamily:         "",
		ChargeType:         "",
		NodeNumber:         0,
	}, nil).Build()
	mock2_1 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceSpec).Return(nil, fmt.Errorf("err")).Build()
	hder.DescribeAutoScaleInstanceSpec(context.Background(), &model.DescribeAutoScaleInstanceSpecReq{
		InstanceType: 0,
		InstanceId:   "",
	})
	mock2_1.UnPatch()
	mock2.UnPatch()

	mock3 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{
		InstanceId:         "",
		InstanceName:       "",
		InstanceStatus:     "",
		RegionId:           "",
		ZoneId:             "",
		DBEngine:           "",
		DBEngineVersion:    "",
		InstanceType:       "",
		VCPU:               0,
		Memory:             0,
		StorageSpace:       0,
		ProjectName:        "",
		VPCID:              "",
		NodeInfos:          nil,
		ConnectionsInfos:   nil,
		Endpoints:          nil,
		DBEngineSubVersion: "",
		NodeSpec:           "",
		NoAuthMode:         nil,
		AllowListInfo:      datasource.AllowListInfo{},
		SpecFamily:         "1",
		ChargeType:         "",
		NodeNumber:         0,
	}, nil).Build()
	defer mock3.UnPatch()
	mock3_1 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceSpec).Return(&datasource.DescribeDBInstanceSpecResp{
		Total: 1,
		NodeSpecs: []*datasource.NodeSpecObject{{
			NodeSpec:          "",
			VCPU:              0,
			Memory:            0,
			Connection:        0,
			SpecFamily:        "1",
			PrePaidMinStorage: 0,
			PrePaidMaxStorage: 0,
			MaxIops:           0,
		}, {}},
	}, nil).Build()

	hder.DescribeAutoScaleInstanceSpec(context.Background(), &model.DescribeAutoScaleInstanceSpecReq{
		InstanceType: 0,
		InstanceId:   "",
	})
	mock3_1.UnPatch()

}
