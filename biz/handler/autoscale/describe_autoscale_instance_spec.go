package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"go.uber.org/dig"
)

func NewDescribeAutoScaleInstanceSpecHandler(in NewDescribeAutoScaleInstanceSpecIn) handler.HandlerImplementationEnvolope {
	h := &DescribeAutoScaleInstanceSpecHandler{
		service: in.Service,
		ds:      in.Ds,
	}
	return handler.<PERSON><PERSON><PERSON><PERSON>(h.DescribeAutoScaleInstanceSpec)
}

type DescribeAutoScaleInstanceSpecHandler struct {
	service autoscale.AutoScaleService
	ds      datasource.DataSourceService
}

type NewDescribeAutoScaleInstanceSpecIn struct {
	dig.In
	Service autoscale.AutoScaleService
	Ds      datasource.DataSourceService
}

func (h *DescribeAutoScaleInstanceSpecHandler) DescribeAutoScaleInstanceSpec(ctx context.Context, req *model.DescribeAutoScaleInstanceSpecReq) (*model.DescribeAutoScaleInstanceSpecResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	detail, err := h.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
		Type:       shared.DataSourceType(req.InstanceType),
	})
	if err != nil {
		return nil, err
	}
	specs, err := h.ds.DescribeDBInstanceSpec(ctx, &datasource.DescribeDBInstanceSpecReq{
		InstanceType: shared.DataSourceType(req.InstanceType),
	})
	if err != nil {
		log.Warn(ctx, "AutoScale: get instance specs error:%s ", err.Error())
		return nil, err
	}
	if specs == nil || len(specs.NodeSpecs) == 0 {
		return nil, err
	}
	// 只获取相同SpecFamily的实例,通用型扩容通用型,独享型扩容独享型
	var res = &model.DescribeAutoScaleInstanceSpecResp{}
	for _, val := range specs.NodeSpecs {
		if detail.SpecFamily != val.SpecFamily {
			continue
		}
		if val.VCPU <= detail.VCPU {
			continue
		}
		res.NodeObjectSpecs = append(res.NodeObjectSpecs, &model.NodeObjectSpec{
			NodeSpec:          val.NodeSpec,
			VCPU:              val.VCPU,
			Memory:            val.Memory,
			Connection:        val.Connection,
			SpecFamily:        val.SpecFamily,
			PrePaidMinStorage: val.PrePaidMinStorage,
			PrePaidMaxStorage: val.PrePaidMaxStorage,
			MaxIops:           val.MaxIops,
		})
	}
	res.Total = specs.Total
	return res, nil
}

func (h *DescribeAutoScaleInstanceSpecHandler) checkReq(ctx context.Context, req *model.DescribeAutoScaleInstanceSpecReq) error {
	// 检查请求
	if req == nil {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "auto scale instance spec param error")
	}
	return nil
}
