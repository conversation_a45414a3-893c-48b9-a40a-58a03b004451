package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"go.uber.org/dig"
)

func NewDescribeDBAutoScaleConfigHandler(in NewDescribeDBAutoScaleConfigIn) handler.HandlerImplementationEnvolope {
	h := &DescribeDBAutoScaleConfigHandler{
		service: in.Service,
	}
	return handler.NewHandler(h.DescribeDBAutoScalingConfig)
}

type DescribeDBAutoScaleConfigHandler struct {
	service autoscale.AutoScaleService
}

type NewDescribeDBAutoScaleConfigIn struct {
	dig.In
	Service  autoscale.AutoScaleService
	MySQLMgr mgr.Provider `name:"mysql"`
}

func (h *DescribeDBAutoScaleConfigHandler) DescribeDBAutoScalingConfig(ctx context.Context, req *model.DescribeDBAutoScalingConfigReq) (*model.DescribeDBAutoScalingConfigResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	res, err := h.service.DescribeDBAutoScalingConfig(ctx, req)
	if err != nil {
		log.Warn(ctx, "DescribeDiskDBAutoScalingConfig: err is %s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_AutoScaleFailed, err.Error())
	}
	return res, nil
}

func (h *DescribeDBAutoScaleConfigHandler) checkReq(ctx context.Context, req *model.DescribeDBAutoScalingConfigReq) error {
	// 检查请求
	if req == nil || req.GetInstanceType().String() != model.InstanceType_MySQL.String() ||
		req.GetInstanceId() == "" {
		log.Warn(ctx, "AutoScale: check req err ,req is %v", utils.Show(req))
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "instance id is empty or instance type error")
	}
	return nil
}

/*func (h *AutoScaleHandler) DescribeDBInstances(ctx context.Context, req *redisModel.DescribeDBInstancesReq) (*redisModel.DescribeDBInstancesResp, error) {
	resp := &redisModel.DescribeDBInstancesResp{}
	if err := h.nosql.Get().Call(ctx, redisModel.Action_DescribeDBInstances.String(), req, resp); err != nil {
		log.Warn(ctx, "AutoScale: get redis instances fail %v", err)
		return nil, err
	}
	return resp, nil
}
*/
