package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"go.uber.org/dig"
)

func NewModifyDBAutoScaleConfigHandler(in NewModifyDBAutoScaleConfigIn) handler.HandlerImplementationEnvolope {
	h := &AutoModifyDBAutoScaleConfigHandler{
		service: in.Service,
	}
	return handler.NewHandler(h.ModifyDBAutoScalingConfig)
}

type AutoModifyDBAutoScaleConfigHandler struct {
	service autoscale.AutoScaleService
}

type NewModifyDBAutoScaleConfigIn struct {
	dig.In
	Service  autoscale.AutoScaleService
	MySQLMgr mgr.Provider `name:"mysql"`
}

func (h *AutoModifyDBAutoScaleConfigHandler) ModifyDBAutoScalingConfig(ctx context.Context, req *model.ModifyDBAutoScalingConfigReq) (*model.ModifyDBAutoScalingConfigResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	res, err := h.service.ModifyDBAutoScalingConfig(ctx, req)
	if err != nil {
		log.Warn(ctx, "DescribeDiskDBAutoScalingConfig: err is %s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_AutoScaleFailed, err.Error())
	}
	return res, nil
}

func (h *AutoModifyDBAutoScaleConfigHandler) checkReq(ctx context.Context, req *model.ModifyDBAutoScalingConfigReq) error {
	// 检查请求
	if req == nil || req.GetInstanceType().String() != model.InstanceType_MySQL.String() ||
		req.GetInstanceId() == "" {
		log.Warn(ctx, "AutoScale: check req err ,req is %v", utils.Show(req))
		return consts.ErrorOf(model.ErrorCode_InputParamError)
	}
	return nil
}
