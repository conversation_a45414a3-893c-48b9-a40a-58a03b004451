package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
)

func mockDescribeDBAutoScaleEventsHandler() *DescribeDBAutoScaleEventsHandler {
	return &DescribeDBAutoScaleEventsHandler{
		service: &mocks.MockAutoScaleService{},
	}
}

func TestNewDescribeDBAutoScaleEventsHandler(t *testing.T) {
	NewDescribeDBAutoScaleEventsHandler(NewDescribeDBAutoScaleEventsIn{})
}

func TestDescribeDBAutoScaleEventsHandler(t *testing.T) {
	handler := mockDescribeDBAutoScaleEventsHandler()
	req := &model.DescribeDBAutoScaleEventsReq{
		InstanceType: 0,
		InstanceId:   "",
		Metric:       0,
	}
	mock1 := mockey.Mock((*mocks.MockAutoScaleService).DescribeDBAutoScaleEvents).Return(nil, fmt.Errorf("err")).Build()
	defer mock1.UnPatch()
	handler.DescribeDBAutoScaleEvents(context.Background(), req)

	req1 := &model.DescribeDBAutoScaleEventsReq{
		InstanceType: model.InstanceType_MySQL,
		InstanceId:   "xx",
		Metric:       0,
	}
	handler.DescribeDBAutoScaleEvents(context.Background(), req1)
}
