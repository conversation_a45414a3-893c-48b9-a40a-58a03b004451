package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	redisModel "code.byted.org/infcs/dbw-mgr/gen/redis-mgr/kitex_gen/base"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
)

func NewDescribeAutoScaleRulesHandler(in NewDescribeAutoScaleRulesIn) handler.HandlerImplementationEnvolope {
	h := &DescribeAutoScaleRulesHandler{
		service: in.BandWidth,
		nosql:   in.Nosql,
		mysql:   in.Mysql,
	}
	return handler.NewHandler(h.DescribeAutoScaleRules)
}

type DescribeAutoScaleRulesHandler struct {
	service autoscale.AutoScaleService
	nosql   mgr.Provider
	mysql   mgr.Provider
}

type NewDescribeAutoScaleRulesIn struct {
	dig.In
	BandWidth autoscale.AutoScaleService
	Ds        datasource.DataSourceService
	Nosql     mgr.Provider `name:"redis"`
	Mysql     mgr.Provider `name:"mysql"`
}

func (h *DescribeAutoScaleRulesHandler) DescribeAutoScaleRules(ctx context.Context, req *model.DescribeAutoScaleRulesReq) (*model.DescribeAutoScaleRulesResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	// 检查租户和Redis实例是否匹配,防止越权访问
	if err := h.service.CheckTenantAndInstance(ctx, req.InstanceId, req.InstanceType); err != nil {
		log.Warn(ctx, "AutoScale: check tenant err")
		return nil, err
	}
	resp, err := h.service.DescribeAutoScaleRules(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (h *DescribeAutoScaleRulesHandler) checkReq(ctx context.Context, req *model.DescribeAutoScaleRulesReq) error {
	// 检查请求
	log.Info(ctx, "req is %v", utils.Show(req))
	log.Info(ctx, "req is %v", req)
	if req == nil || req.GetInstanceId() == "" || req.GetRegionId() == "" {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "AutoScale InstanceId or RegionId param error,please check")
	}
	if req.Metric != model.AutoScaleMetricName_BandWidth && req.Metric != model.AutoScaleMetricName_Spec {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "AutoScale Metric param error,please check")
	}
	return nil
}

func (h *DescribeAutoScaleRulesHandler) checkTenantAndInstance(ctx context.Context, req *model.DescribeAutoScaleRulesReq) error {
	// 检查实例
	rreq := &redisModel.DescribeDBInstancesReq{
		RegionId:   utils.StringRef(req.RegionId),
		PageNumber: utils.Int32Ref(1),
		PageSize:   utils.Int32Ref(10),
		Filter: []*redisModel.InstanceFilter{
			{
				FilterKey:   redisModel.InstanceFilterKey_Status.String(),
				FilterValue: redisModel.InstanceStatus_Running.String(),
			},
			{
				FilterKey:   redisModel.InstanceFilterKey_InstanceId.String(),
				FilterValue: req.InstanceId,
			},
		},
	}
	instances, err := h.DescribeDBInstances(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "AutoScale: describe instances error,%s", err.Error())
	}
	if !h.isInstanceBelongsToTenant(ctx, req.InstanceId, instances) {
		log.Warn(ctx, "AutoScale: describe instances error,instance does not match tenant")
		return consts.ErrorOf(model.ErrorCode_CheckInstanceError)
	}
	return nil
}

func (h *DescribeAutoScaleRulesHandler) isInstanceBelongsToTenant(ctx context.Context, instanceId string, resp *redisModel.DescribeDBInstancesResp) bool {
	if resp == nil {
		return false
	}
	for _, val := range resp.Instances {
		if val.InstanceId == instanceId && val.Status == redisModel.InstanceStatus_Running.String() {
			log.Info(ctx, "AutoScale: there are %d instances for tenant %s, check tenant success", resp.TotalInstancesNum, fwctx.GetTenantID(ctx))
			return true
		}
	}
	return false
}

func (h *DescribeAutoScaleRulesHandler) DescribeDBInstances(ctx context.Context, req *redisModel.DescribeDBInstancesReq) (*redisModel.DescribeDBInstancesResp, error) {
	resp := &redisModel.DescribeDBInstancesResp{}
	if err := h.nosql.Get().Call(ctx, redisModel.Action_DescribeDBInstances.String(), req, resp); err != nil {
		log.Warn(ctx, "AutoScale: get redis instances fail %v", err)
		return nil, err
	}
	return resp, nil
}
