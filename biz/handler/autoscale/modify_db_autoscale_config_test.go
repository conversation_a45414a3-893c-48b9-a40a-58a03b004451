package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
)

func mockModifyDBAutoScaleConfigHandler() *AutoModifyDBAutoScaleConfigHandler {
	return &AutoModifyDBAutoScaleConfigHandler{
		service: &mocks.MockAutoScaleService{},
	}
}

func TestNewModifyDBAutoScaleConfigHandler(t *testing.T) {
	NewModifyDBAutoScaleConfigHandler(NewModifyDBAutoScaleConfigIn{})
}

func TestModifyDBAutoScaleConfigHandler(t *testing.T) {
	handler := mockModifyDBAutoScaleConfigHandler()
	req := &model.ModifyDBAutoScalingConfigReq{
		InstanceType: 0,
		InstanceId:   "",
	}
	mock1 := mockey.Mock((*mocks.MockAutoScaleService).ModifyDBAutoScalingConfig).Return(nil, fmt.Errorf("err")).Build()
	defer mock1.UnPatch()
	handler.ModifyDBAutoScalingConfig(context.Background(), req)

	req1 := &model.ModifyDBAutoScalingConfigReq{
		InstanceType: model.InstanceType_MySQL,
		InstanceId:   "xx",
	}
	handler.ModifyDBAutoScalingConfig(context.Background(), req1)
}
