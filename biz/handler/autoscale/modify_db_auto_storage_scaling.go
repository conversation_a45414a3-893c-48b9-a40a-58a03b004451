package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"go.uber.org/dig"
)

func NewModifyDBAutoStorageScalingHandler(in NewModifyDBAutoStorageScalingIn) handler.HandlerImplementationEnvolope {
	h := &ModifyDBAutoStorageScalingHandler{
		service: in.Service,
	}
	return handler.NewHandler(h.ModifyDBAutoStorageScaling)
}

type ModifyDBAutoStorageScalingHandler struct {
	service autoscale.AutoScaleDiskService
}

type NewModifyDBAutoStorageScalingIn struct {
	dig.In
	Service autoscale.AutoScaleDiskService
}

func (h *ModifyDBAutoStorageScalingHandler) ModifyDBAutoStorageScaling(ctx context.Context, req *model.ModifyDBAutoStorageScalingReq) (*model.ModifyDBAutoStorageScalingResp, error) {
	res, err := h.service.ModifyDBAutoStorageScaling(ctx, req)
	if err != nil {
		log.Warn(ctx, "ModifyDBAutoStorageScaling: err is %s", err.Error())
		return nil, err
	}
	return res, nil
}
