package autoscale

import (
	"code.byted.org/gopkg/mockito"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/dbw-mgr/gen/redis-mgr/kitex_gen/base"
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"testing"
)

type AutoScaleBandWidthTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

// 测试开始之前需要做的事情
func (suite *AutoScaleBandWidthTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

// 测试结束之后需要做的事情
func (suite *AutoScaleBandWidthTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestAutoScaleBandWidthTestSuite(t *testing.T) {
	suite.Run(t, new(AutoScaleBandWidthTestSuite))
}

func TestAutoScaleBandWidthHandler_OK(t *testing.T) {
	NewAutoScaleHandler(NewAutoScaleIn{})
}

func (suite *AutoScaleBandWidthTestSuite) Test_AutoScaleBandWidth() {
	ctx := context.Background()

	autoScaleService := mocks.NewMockAutoScaleService(suite.ctrl)
	nosql := mocks.NewMockProvider(suite.ctrl)
	d := AutoScaleHandler{
		service: autoScaleService,
		nosql:   nosql,
	}
	mockito.PatchConvey("AutoScaleBandWidth test", suite.T(), func() {
		mockito.PatchConvey("checkReq error1", func() {
			req := &model.AutoScaleReq{}
			ret, err := d.AutoScale(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})
		mockito.PatchConvey("checkReq error2", func() {
			req := &model.AutoScaleReq{
				InstanceType:      model.InstanceType_MySQL,
				RegionId:          "cn-chongqing-sdv",
				InstanceId:        "redis-xxx",
				InstanceName:      "redis-xxx",
				ObservationWindow: 2,
				Configs:           []*model.RuleConfig{{Action: 3, IsOpen: false, Threshold: 20}},
			}
			ret, err := d.AutoScale(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})
		mockito.PatchConvey("checkReq error3", func() {
			req := &model.AutoScaleReq{
				InstanceType:      model.InstanceType_MySQL,
				RegionId:          "cn-chongqing-sdv",
				InstanceId:        "redis-xxx",
				InstanceName:      "redis-xxx",
				ObservationWindow: 2,
				Configs:           []*model.RuleConfig{{Action: model.AutoScaleAction_Reduce, IsOpen: false, Threshold: 200}},
			}
			ret, err := d.AutoScale(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})
		mockito.PatchConvey("AutoScaleBandWidth checkTenantAndInstance error1", func() {
			req := &model.AutoScaleReq{
				InstanceType:      model.InstanceType_MySQL,
				RegionId:          "cn-chongqing-sdv",
				InstanceId:        "redis-xxx",
				InstanceName:      "redis-xxx",
				ObservationWindow: 2,
				Configs:           []*model.RuleConfig{{Action: model.AutoScaleAction_Reduce, IsOpen: false, Threshold: 20}},
			}
			mock := mockey.Mock((*AutoScaleHandler).DescribeDBInstances).Return(nil, errors.New("test")).Build()
			defer mock.UnPatch()
			ret, err := d.AutoScale(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})
		mockito.PatchConvey("AutoScaleBandWidth checkTenantAndInstance error2", func() {
			req := &model.AutoScaleReq{
				InstanceType:      model.InstanceType_MySQL,
				RegionId:          "cn-chongqing-sdv",
				InstanceId:        "redis-xxx",
				InstanceName:      "redis-xxx",
				ObservationWindow: 2,
				Configs:           []*model.RuleConfig{{Action: model.AutoScaleAction_Reduce, IsOpen: false, Threshold: 20}},
			}
			mock := mockey.Mock((*AutoScaleHandler).DescribeDBInstances).Return(
				&base.DescribeDBInstancesResp{
					Instances: []*base.DBInstance{
						{InstanceId: "redis-xx", Status: "Running", CreateTime: "2023-05-21T08:00:00Z"},
					}}, nil).Build()
			defer mock.UnPatch()
			ret, err := d.AutoScale(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})
		mockito.PatchConvey("AutoScaleBandWidth checkTenantAndInstance error3", func() {
			req := &model.AutoScaleReq{
				InstanceType:      model.InstanceType_MySQL,
				RegionId:          "cn-chongqing-sdv",
				InstanceId:        "redis-xxx",
				InstanceName:      "redis-xxx",
				ObservationWindow: 2,
				Configs:           []*model.RuleConfig{{Action: model.AutoScaleAction_Reduce, IsOpen: false, Threshold: 20}},
			}
			mock := mockey.Mock((*AutoScaleHandler).DescribeDBInstances).Return(
				&base.DescribeDBInstancesResp{
					Instances: []*base.DBInstance{
						{InstanceId: "redis-xxx", Status: "Running", CreateTime: "2023-05-21T08:00:00Z"},
					}}, nil).Build()
			defer mock.UnPatch()
			ret, err := d.AutoScale(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})
		mockito.PatchConvey("AutoScaleBandWidth checkTenantAndInstance error4", func() {
			req := &model.AutoScaleReq{
				InstanceType:      model.InstanceType_MySQL,
				RegionId:          "cn-chongqing-sdv",
				InstanceId:        "redis-xxx",
				InstanceName:      "redis-xxx",
				ObservationWindow: 2,
				Configs:           []*model.RuleConfig{{Action: model.AutoScaleAction_Reduce, IsOpen: false, Threshold: 20}},
			}
			mock := mockey.Mock((*AutoScaleHandler).DescribeDBInstances).Return(
				&base.DescribeDBInstancesResp{
					Instances: []*base.DBInstance{
						{InstanceId: "redis-xxx", Status: "Running", CreateTime: "2023-xxxxxx1T08:00:00Z"},
					}}, nil).Build()
			defer mock.UnPatch()
			ret, err := d.AutoScale(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})
	})
}
