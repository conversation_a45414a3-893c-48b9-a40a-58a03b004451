package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"go.uber.org/dig"
)

func NewDescribeAutoScaleDiskEventsHandler(in NewDescribeAutoScaleDiskEventsIn) handler.HandlerImplementationEnvolope {
	h := &AutoDescribeScaleDiskEventsHandler{
		service: in.Service,
	}
	return handler.NewHandler(h.DescribeDiskAutoScaleEvents)
}

type AutoDescribeScaleDiskEventsHandler struct {
	service autoscale.AutoScaleDiskService
}

type NewDescribeAutoScaleDiskEventsIn struct {
	dig.In
	Service autoscale.AutoScaleDiskService
}

// DescribeDiskAutoScaleEvents FixMe 这块是历史遗留问题,第一次写的时候,这个列表的命名中包含了disk,其实不应该这么叫,因为可能还有其他资源的扩缩容,但是为了不影响线上业务,这一期暂时不做改造
// DescribeDiskAutoScaleEvents 查看磁盘自动扩缩容事件
func (h *AutoDescribeScaleDiskEventsHandler) DescribeDiskAutoScaleEvents(ctx context.Context, req *model.DescribeDiskAutoScaleEventsReq) (*model.DescribeDiskAutoScaleEventsResp, error) {
	res, err := h.service.DescribeDiskAutoScaleEvents(ctx, req)
	if err != nil {
		log.Warn(ctx, "DescribeDBAutoScalingEvents: err is %v", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_AutoScaleFailed, err.Error())
	}
	return res, nil
}

/*func (h *AutoScaleHandler) DescribeDBInstances(ctx context.Context, req *redisModel.DescribeDBInstancesReq) (*redisModel.DescribeDBInstancesResp, error) {
	resp := &redisModel.DescribeDBInstancesResp{}
	if err := h.nosql.Get().Call(ctx, redisModel.Action_DescribeDBInstances.String(), req, resp); err != nil {
		log.Warn(ctx, "AutoScale: get redis instances fail %v", err)
		return nil, err
	}
	return resp, nil
}
*/
