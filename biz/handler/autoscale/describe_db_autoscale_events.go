package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"go.uber.org/dig"
)

func NewDescribeDBAutoScaleEventsHandler(in NewDescribeDBAutoScaleEventsIn) handler.HandlerImplementationEnvolope {
	h := &DescribeDBAutoScaleEventsHandler{
		service: in.Service,
	}
	return handler.NewHandler(h.DescribeDBAutoScaleEvents)
}

type DescribeDBAutoScaleEventsHandler struct {
	service autoscale.AutoScaleService
}

type NewDescribeDBAutoScaleEventsIn struct {
	dig.In
	Service autoscale.AutoScaleService
}

func (h *DescribeDBAutoScaleEventsHandler) DescribeDBAutoScaleEvents(ctx context.Context, req *model.DescribeDBAutoScaleEventsReq) (*model.DescribeDBAutoScaleEventsResp, error) {
	err := h.checkReq(ctx, req)
	if err != nil {
		return nil, err
	}
	res, err := h.service.DescribeDBAutoScaleEvents(ctx, req)
	if err != nil {
		log.Warn(ctx, "DescribeDiskDBAutoScalingConfig: err is %s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_AutoScaleFailed, err.Error())
	}
	return res, nil
}

func (h *DescribeDBAutoScaleEventsHandler) checkReq(ctx context.Context, req *model.DescribeDBAutoScaleEventsReq) error {
	// 检查请求
	if req == nil || req.GetInstanceType().String() != model.InstanceType_MySQL.String() ||
		req.GetInstanceId() == "" {
		log.Warn(ctx, "AutoScale: check req err ,req is %v", utils.Show(req))
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "instanceId is empty or instance type error")
	}
	if req.GetPageNumber() == 0 {
		req.PageNumber = utils.Int32Ref(1)
	}
	if req.GetPageSize() == 0 {
		req.PageSize = utils.Int32Ref(10)
	}
	return nil
}
