package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"go.uber.org/dig"
)

func NewModifyAutoScaleDiskHandler(in NewModifyAutoScaleDiskIn) handler.HandlerImplementationEnvolope {
	h := &ModifyAutoScaleDiskHandler{
		service: in.Service,
	}
	return handler.<PERSON><PERSON>andler(h.ModifyDiskDBAutoScalingConfig)
}

type ModifyAutoScaleDiskHandler struct {
	service autoscale.AutoScaleDiskService
}

type NewModifyAutoScaleDiskIn struct {
	dig.In
	Service autoscale.AutoScaleDiskService
}

func (h *ModifyAutoScaleDiskHandler) ModifyDiskDBAutoScalingConfig(ctx context.Context, req *model.ModifyDiskDBAutoScalingConfigReq) (*model.ModifyDiskDBAutoScalingConfigResp, error) {
	res, err := h.service.ModifyDiskDBAutoScalingConfig(ctx, req)
	if err != nil {
		log.Warn(ctx, "DescribeDiskDBAutoScalingConfig: err is %s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_AutoScaleFailed)
	}
	return res, nil
}

/*func (h *AutoScaleHandler) DescribeDBInstances(ctx context.Context, req *redisModel.DescribeDBInstancesReq) (*redisModel.DescribeDBInstancesResp, error) {
	resp := &redisModel.DescribeDBInstancesResp{}
	if err := h.nosql.Get().Call(ctx, redisModel.Action_DescribeDBInstances.String(), req, resp); err != nil {
		log.Warn(ctx, "AutoScale: get redis instances fail %v", err)
		return nil, err
	}
	return resp, nil
}
*/
