package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"go.uber.org/dig"
)

func NewModifyDBLocalSpecManuallyHandler(in NewModifyDBLocalSpecManuallyIn) handler.HandlerImplementationEnvolope {
	h := &ModifyDBLocalSpecManuallyHandler{
		service: in.Service,
	}
	return handler.NewHandler(h.ModifyDBLocalSpecManually)
}

type ModifyDBLocalSpecManuallyHandler struct {
	service autoscale.AutoScaleService
}

type NewModifyDBLocalSpecManuallyIn struct {
	dig.In
	Service autoscale.AutoScaleService
}

func (h *ModifyDBLocalSpecManuallyHandler) ModifyDBLocalSpecManually(ctx context.Context, req *model.ModifyDBLocalSpecManuallyReq) (*model.ModifyDBLocalSpecManuallyResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	res, err := h.service.ModifyDBLocalSpecManually(ctx, req)
	if err != nil {
		log.Warn(ctx, "DescribeDiskDBAutoScalingConfig: err is %s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_AutoScaleFailed, err.Error())
	}
	return res, nil
}

func (h *ModifyDBLocalSpecManuallyHandler) checkReq(ctx context.Context, req *model.ModifyDBLocalSpecManuallyReq) error {
	// 检查请求
	if req == nil || req.GetInstanceType().String() != model.InstanceType_MySQL.String() ||
		req.GetInstanceId() == "" {
		log.Warn(ctx, "AutoScale: check req err ,req is %v", utils.Show(req))
		return consts.ErrorOf(model.ErrorCode_InputParamError)
	}
	return nil
}
