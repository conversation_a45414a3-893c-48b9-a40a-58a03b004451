package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
)

func mockDescribeDBAutoScaleConfigHandler() *DescribeDBAutoScaleConfigHandler {
	return &DescribeDBAutoScaleConfigHandler{
		service: &mocks.MockAutoScaleService{},
	}
}

func TestNewDescribeDBAutoScaleConfigHandler(t *testing.T) {
	NewDescribeDBAutoScaleConfigHandler(NewDescribeDBAutoScaleConfigIn{})
}

func TestDescribeDBAutoScaleConfigHandler(t *testing.T) {
	handler := mockDescribeDBAutoScaleConfigHandler()
	req := &model.DescribeDBAutoScalingConfigReq{
		InstanceType: 0,
		InstanceId:   "",
		Metric:       0,
	}
	mock1 := mockey.Mock((*mocks.MockAutoScaleService).DescribeDBAutoScalingConfig).Return(nil, fmt.Errorf("err")).Build()
	defer mock1.UnPatch()
	handler.DescribeDBAutoScalingConfig(context.Background(), req)

	req1 := &model.DescribeDBAutoScalingConfigReq{
		InstanceType: model.InstanceType_MySQL,
		InstanceId:   "xx",
		Metric:       0,
	}
	handler.DescribeDBAutoScalingConfig(context.Background(), req1)
}
