package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	redisModel "code.byted.org/infcs/dbw-mgr/gen/redis-mgr/kitex_gen/base"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
)

func NewDescribeAutoScaleEventsHandler(in NewDescribeAutoScaleEventsIn) handler.HandlerImplementationEnvolope {
	h := &DescribeAutoScaleEventsHandler{
		service: in.Service,
		nosql:   in.Nosql,
		mysql:   in.Mysql,
	}
	return handler.NewHandler(h.DescribeAutoScaleEvents)
}

type DescribeAutoScaleEventsHandler struct {
	service autoscale.AutoScaleService
	nosql   mgr.Provider
	mysql   mgr.Provider
}

type NewDescribeAutoScaleEventsIn struct {
	dig.In
	Service autoscale.AutoScaleService
	Nosql   mgr.Provider `name:"redis"`
	Mysql   mgr.Provider `name:"mysql"`
}

func (h *DescribeAutoScaleEventsHandler) DescribeAutoScaleEvents(ctx context.Context, req *model.DescribeAutoScaleEventsReq) (*model.DescribeAutoScaleEventsResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	if err := h.service.CheckTenantAndInstance(ctx, req.InstanceId, req.InstanceType); err != nil {
		log.Warn(ctx, "AutoScale: check tenant err")
		return nil, err
	}
	events, err := h.service.DescribeAutoScaleEvents(ctx, req)
	if err != nil {
		log.Warn(ctx, "AutoScale: get events error:%s ", err.Error())
		return nil, err
	}
	return events, nil
}

func (h *DescribeAutoScaleEventsHandler) checkReq(ctx context.Context, req *model.DescribeAutoScaleEventsReq) error {
	// 检查请求
	if req == nil || req.GetInstanceId() == "" {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "AutoScale参数错误,请检查")
	}
	if req.PageSize == nil || *req.PageSize <= 0 {
		req.PageSize = utils.Int32Ref(10)
	}
	if req.PageNumber == nil || *req.PageNumber <= 0 {
		req.PageNumber = utils.Int32Ref(1)
	}
	if req.SortBy == nil { // 默认倒序
		req.SortBy = model.SortByPtr(model.SortBy_DESC)
	}
	if req.AutoScaleSearchParam == nil {
		req.AutoScaleSearchParam = &model.AutoScaleSearchParam{}
	}
	return nil
}

func (h *DescribeAutoScaleEventsHandler) checkTenantAndInstance(ctx context.Context, req *model.DescribeAutoScaleEventsReq) error {
	// 检查实例
	rreq := &redisModel.DescribeDBInstancesReq{
		RegionId:   utils.StringRef(req.RegionId),
		PageNumber: utils.Int32Ref(1),
		PageSize:   utils.Int32Ref(10),
		Filter: []*redisModel.InstanceFilter{
			{
				FilterKey:   redisModel.InstanceFilterKey_Status.String(),
				FilterValue: redisModel.InstanceStatus_Running.String(),
			},
			{
				FilterKey:   redisModel.InstanceFilterKey_InstanceId.String(),
				FilterValue: req.InstanceId,
			},
		},
	}
	instances, err := h.DescribeDBInstances(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "AutoScale: describe instances error,%s", err.Error())
		return consts.ErrorOf(model.ErrorCode_CheckInstanceError)
	}
	if !h.isInstanceBelongsToTenant(ctx, req.InstanceId, instances) {
		log.Warn(ctx, "AutoScale: describe instances error,instance does not match tenant")
		return consts.ErrorOf(model.ErrorCode_CheckInstanceError)
	}
	return nil
}

func (h *DescribeAutoScaleEventsHandler) isInstanceBelongsToTenant(ctx context.Context, instanceId string, resp *redisModel.DescribeDBInstancesResp) bool {
	if resp == nil {
		return false
	}
	for _, val := range resp.Instances {
		if val.InstanceId == instanceId && val.Status == redisModel.InstanceStatus_Running.String() {
			log.Info(ctx, "AutoScale: there are %d instances for tenant %s, check tenant success", resp.TotalInstancesNum, fwctx.GetTenantID(ctx))
			return true
		}
	}
	return false
}

func (h *DescribeAutoScaleEventsHandler) DescribeDBInstances(ctx context.Context, req *redisModel.DescribeDBInstancesReq) (*redisModel.DescribeDBInstancesResp, error) {
	resp := &redisModel.DescribeDBInstancesResp{}
	if err := h.nosql.Get().Call(ctx, redisModel.Action_DescribeDBInstances.String(), req, resp); err != nil {
		log.Warn(ctx, "AutoScale: get redis instances fail %v", err)
		return nil, err
	}
	return resp, nil
}
