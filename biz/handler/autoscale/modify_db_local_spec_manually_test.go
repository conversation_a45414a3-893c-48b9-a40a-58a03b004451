package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
)

func mockModifyDBLocalSpecManuallyHandler() *ModifyDBLocalSpecManuallyHandler {
	return &ModifyDBLocalSpecManuallyHandler{
		service: &mocks.MockAutoScaleService{},
	}
}

func TestNewModifyDBLocalSpecManuallyHandler(t *testing.T) {
	NewDescribeDBAutoScaleEventsHandler(NewDescribeDBAutoScaleEventsIn{})
}

func TestModifyDBLocalSpecManuallyHandler(t *testing.T) {
	handler := mockModifyDBLocalSpecManuallyHandler()
	req := &model.ModifyDBLocalSpecManuallyReq{
		InstanceType: 0,
		InstanceId:   "",
	}
	mock1 := mockey.Mock((*mocks.MockAutoScaleService).ModifyDBLocalSpecManually).Return(nil, fmt.Errorf("err")).Build()
	defer mock1.UnPatch()
	handler.ModifyDBLocalSpecManually(context.Background(), req)

	req1 := &model.ModifyDBLocalSpecManuallyReq{
		InstanceType: model.InstanceType_MySQL,
		InstanceId:   "xx",
	}
	handler.ModifyDBLocalSpecManually(context.Background(), req1)
}
