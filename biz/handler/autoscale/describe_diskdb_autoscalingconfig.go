package autoscale

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"go.uber.org/dig"
)

func NewDescribeAutoScaleDiskHandler(in NewDescribeAutoScaleDiskIn) handler.HandlerImplementationEnvolope {
	h := &AutoDescribeScaleDiskHandler{
		service: in.Service,
	}
	return handler.NewHandler(h.DescribeDiskDBAutoScalingConfig)
}

type AutoDescribeScaleDiskHandler struct {
	service autoscale.AutoScaleDiskService
}

type NewDescribeAutoScaleDiskIn struct {
	dig.In
	Service  autoscale.AutoScaleDiskService
	MySQLMgr mgr.Provider `name:"mysql"`
}

func (h *AutoDescribeScaleDiskHandler) DescribeDiskDBAutoScalingConfig(ctx context.Context, req *model.DescribeDiskDBAutoScalingConfigReq) (*model.DescribeDiskDBAutoScalingConfigResp, error) {
	res, err := h.service.DescribeDiskDBAutoScalingConfig(ctx, req)
	if err != nil {
		log.Warn(ctx, "DescribeDiskDBAutoScalingConfig: err is %s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_AutoScaleFailed)
	}
	return res, nil
}

/*func (h *AutoScaleHandler) DescribeDBInstances(ctx context.Context, req *redisModel.DescribeDBInstancesReq) (*redisModel.DescribeDBInstancesResp, error) {
	resp := &redisModel.DescribeDBInstancesResp{}
	if err := h.nosql.Get().Call(ctx, redisModel.Action_DescribeDBInstances.String(), req, resp); err != nil {
		log.Warn(ctx, "AutoScale: get redis instances fail %v", err)
		return nil, err
	}
	return resp, nil
}
*/
