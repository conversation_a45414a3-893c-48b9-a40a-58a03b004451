package autoscale

import (
	"context"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	redisModel "code.byted.org/infcs/dbw-mgr/gen/redis-mgr/kitex_gen/base"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"go.uber.org/dig"
)

func NewAutoScaleHandler(in NewAutoScaleIn) handler.HandlerImplementationEnvolope {
	h := &AutoScaleHandler{
		service: in.Service,
		nosql:   in.Nosql,
		mysql:   in.Mysql,
	}
	return handler.NewHandler(h.AutoScale)
}

type AutoScaleHandler struct {
	service autoscale.AutoScaleService
	nosql   mgr.Provider
	mysql   mgr.Provider
}

type NewAutoScaleIn struct {
	dig.In
	Service autoscale.AutoScaleService
	Nosql   mgr.Provider `name:"redis"`
	Mysql   mgr.Provider `name:"mysql"`
}

func (h *AutoScaleHandler) AutoScale(ctx context.Context, req *model.AutoScaleReq) (*model.AutoScaleResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	// 检查租户和实例是否匹配,防止越权访问
	if err := h.service.CheckTenantAndInstance(ctx, req.InstanceId, req.InstanceType); err != nil {
		log.Warn(ctx, "AutoScale: check tenant err")
		return nil, err
	}
	if _, err := h.service.AutoScale(ctx, req); err != nil {
		log.Warn(ctx, "AutoScale: err is %s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_AutoScaleFailed)
	}
	return &model.AutoScaleResp{}, nil
}

func (h *AutoScaleHandler) checkReq(ctx context.Context, req *model.AutoScaleReq) error {
	// 检查请求
	if req == nil || req.GetInstanceType().String() == "<UNSET>" ||
		req.GetInstanceId() == "" ||
		req.GetObservationWindow() < 0 ||
		len(req.GetConfigs()) == 0 {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "AutoScale param error,please check")
	}
	// 这里需要区分数据库的类型
	switch req.GetInstanceType() {
	case model.InstanceType_Redis:
		// Redis目前支持带宽扩容
		for _, val := range req.Configs {
			// 这里加一个开关,将缩容操作屏蔽掉,本期只上扩容
			if val.Action.String() == "<UNSET>" || val.Action == model.AutoScaleAction_Reduce {
				return consts.ErrorWithParam(model.ErrorCode_InputParamError, "AutoScale Action param error,do not support reduce,please check")
			}
			if val.Threshold >= 100 {
				return consts.ErrorWithParam(model.ErrorCode_InputParamError, "AutoScale Threshold param error,please check")
			}
			if req.Metric == model.AutoScaleMetricName_BandWidth && val.IsOpen &&
				val.Action == model.AutoScaleAction_Expand && val.Threshold < 70 {
				return consts.ErrorWithParam(model.ErrorCode_InputParamError, "AutoScale bandwidth usage lower than 70%,do not need expand")
			}
		}
	case model.InstanceType_VeDBMySQL:
		// veDB支持实例规格扩容
		for _, val := range req.Configs {
			// 这里加一个开关,将缩容操作屏蔽掉,本期只上扩容
			if val.Action.String() == "<UNSET>" || val.Action == model.AutoScaleAction_Reduce {
				return consts.ErrorWithParam(model.ErrorCode_InputParamError, "AutoScale Action param error,do not support reduce,please check")
			}
			if val.Threshold >= 100 {
				return consts.ErrorWithParam(model.ErrorCode_InputParamError, "AutoScale Threshold param error,please check")
			}
			if req.Metric == model.AutoScaleMetricName_Spec && val.IsOpen &&
				val.Action == model.AutoScaleAction_Expand && (val.Threshold < 50 || val.Threshold > 90) {
				return consts.ErrorWithParam(model.ErrorCode_InputParamError, "AutoScale CPU usage is lower than 50%, do not need expand")
			}
		}
	default:
		log.Warn(ctx, "AutoScale: InstanceType is %v, only support VeDB and Redis,please check", req.InstanceType.String())
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "InstanceType is:", req.InstanceType.String(), "only support VeDB and Redis,please check")
	}
	return nil
}

func (h *AutoScaleHandler) checkTenantAndInstance(ctx context.Context, req *model.AutoScaleReq) error {
	// 检查实例
	rreq := &redisModel.DescribeDBInstancesReq{
		RegionId:   utils.StringRef(req.RegionId),
		PageNumber: utils.Int32Ref(1),
		PageSize:   utils.Int32Ref(10),
		Filter: []*redisModel.InstanceFilter{
			{
				FilterKey:   redisModel.InstanceFilterKey_Status.String(),
				FilterValue: redisModel.InstanceStatus_Running.String(),
			},
			{
				FilterKey:   redisModel.InstanceFilterKey_InstanceId.String(),
				FilterValue: req.InstanceId,
			},
		},
	}
	instances, err := h.DescribeDBInstances(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "AutoScale: describe instances error,%s", err.Error())
		return consts.ErrorOf(model.ErrorCode_CheckInstanceError)
	}
	if !h.isInstanceBelongsToTenant(ctx, req.InstanceId, instances) {
		log.Warn(ctx, "AutoScale: describe instances error,instance does not match tenant")
		return consts.ErrorOf(model.ErrorCode_CheckInstanceError)
	}
	if !h.isInstanceCreateTimeLegal(ctx, req.InstanceId, instances) {
		log.Warn(ctx, "AutoScale: describe instances error,instance does not match tenant")
		return consts.ErrorOf(model.ErrorCode_InstanceCreateTimeIllegal)
	}
	return nil
}

func (h *AutoScaleHandler) isInstanceBelongsToTenant(ctx context.Context, instanceId string, resp *redisModel.DescribeDBInstancesResp) bool {
	for _, val := range resp.Instances {
		if val.InstanceId == instanceId && val.Status == redisModel.InstanceStatus_Running.String() {
			log.Info(ctx, "AutoScale: there are %d instances for tenant %s, check tenant success", resp.TotalInstancesNum, fwctx.GetTenantID(ctx))
			return true
		}
	}
	return false
}

func (h *AutoScaleHandler) isInstanceCreateTimeLegal(ctx context.Context, instanceId string, resp *redisModel.DescribeDBInstancesResp) bool {
	for _, val := range resp.Instances {
		if val.InstanceId == instanceId && val.Status == redisModel.InstanceStatus_Running.String() {
			// 判断实例创建时间
			log.Info(ctx, "AutoScale: instance createTime is %s", val.CreateTime)
			utcCreateTime, err := time.Parse(time.RFC3339, val.CreateTime)
			if err != nil {
				log.Warn(ctx, "AutoScale: createTime is not valid to parse", val.CreateTime)
				return false
			}
			validTime := time.Date(2023, 5, 25, 0, 0, 0, 0, time.Now().Location())
			if utcCreateTime.After(validTime) {
				return true
			}
		}
	}
	return false
}

func (h *AutoScaleHandler) DescribeDBInstances(ctx context.Context, req *redisModel.DescribeDBInstancesReq) (*redisModel.DescribeDBInstancesResp, error) {
	resp := &redisModel.DescribeDBInstancesResp{}
	if err := h.nosql.Get().Call(ctx, redisModel.Action_DescribeDBInstances.String(), req, resp); err != nil {
		log.Warn(ctx, "AutoScale: get redis instances fail %v", err)
		return nil, err
	}
	return resp, nil
}
