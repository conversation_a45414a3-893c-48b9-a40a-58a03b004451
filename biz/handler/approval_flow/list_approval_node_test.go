package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func MockListApprovalNodeHandler() *ListApprovalNodeHandler {
	return &ListApprovalNodeHandler{
		approvalFlowService: &mocks.MockApprovalFlowService{},
		workflowService:     &mocks.MockTicketService{},
	}
}

func TestListApprovalNode(t *testing.T) {
	handler := MockListApprovalNodeHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock((*mocks.MockTicketService).GetUserNameByIds).Return(&userNameMap, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalNode).Return([]*entity.ApprovalNode{{}}, nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockApprovalFlowService).GetApprovalNodesSum).Return(1, fmt.Errorf("test")).Build()
	_, err := handler.ListApprovalNode(context.Background(), &model.ListApprovalNodeReq{})
	assert.NotNil(t, err)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*mocks.MockApprovalFlowService).GetApprovalNodesSum).Return(1, nil).Build()
	defer mock5.UnPatch()
	_, err = handler.ListApprovalNode(context.Background(), &model.ListApprovalNodeReq{})
	assert.Nil(t, err)
}
