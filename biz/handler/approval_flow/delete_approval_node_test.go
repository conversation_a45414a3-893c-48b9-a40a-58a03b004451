package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func MockDeleteApprovalNodeHandler() *DeleteApprovalNodeHandler {
	return &DeleteApprovalNodeHandler{
		approvalFlowService: &mocks.MockApprovalFlowService{},
	}
}

func TestDeleteApprovalNode(t *testing.T) {
	handler := MockDeleteApprovalNodeHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).CheckActionPermission).Return(nil).Build()
	defer mock2.UnPatch()
	mock7 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalNode).Return([]*entity.ApprovalNode{{}}, fmt.Errorf("test")).Build()
	_, err := handler.DeleteApprovalNode(context.Background(), &model.DeleteApprovalNodeReq{NodeId: "1"})
	assert.NotNil(t, err)
	mock7.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock8 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalNode).Return([]*entity.ApprovalNode{{}}, nil).Build()
	defer mock8.UnPatch()
	mock4 := mockey.Mock((*DeleteApprovalNodeHandler).checkNodeDeletedAllowed).Return(nil).Build()
	defer mock4.UnPatch()
	mock3 := mockey.Mock((*mocks.MockApprovalFlowService).DeleteApprovalNode).Return(fmt.Errorf("test")).Build()
	_, err = handler.DeleteApprovalNode(context.Background(), &model.DeleteApprovalNodeReq{NodeId: "1"})
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*mocks.MockApprovalFlowService).DeleteApprovalNode).Return(nil).Build()
	_, err = handler.DeleteApprovalNode(context.Background(), &model.DeleteApprovalNodeReq{NodeId: "1"})
	assert.Nil(t, err)
	mock5.UnPatch()
	time.Sleep(100 * time.Millisecond)
}

func TestCheckNodeDeletedAllowed(t *testing.T) {
	handler := MockDeleteApprovalNodeHandler()

	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).IsNodeInApprovalConfig).Return(true, fmt.Errorf("test")).Build()
	err := handler.checkNodeDeletedAllowed(context.Background(), "1", 1)
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockApprovalFlowService).IsNodeInApprovalConfig).Return(true, nil).Build()
	err = handler.checkNodeDeletedAllowed(context.Background(), "1", 1)
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)
}
