package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"github.com/qjpcpu/fp"
	"regexp"
	"strconv"
	"strings"
)

func covertApprovalUsers(approvalIds string, userNameMap map[string]string) []*model.UserInfo {
	spaceRe, _ := regexp.Compile(`\s*,\s*`)
	idList := spaceRe.Split(approvalIds, -1)
	var res []*model.UserInfo
	for _, id := range idList {
		res = append(res, &model.UserInfo{UserId: id, UserName: userNameMap[id]})
	}
	return res
}

func getTenantUserId(ctx context.Context) (string, string, error) {
	tenantId := fwctx.GetTenantID(ctx)
	if tenantId == "" {
		log.Warn(ctx, "tenantId is empty")
		return "", "", consts.ErrorWithParam(model.ErrorCode_SystemError, "tenantId is empty")
	}
	userId := fwctx.GetUserID(ctx)
	if userId == "" {
		userId = tenantId
	}
	return tenantId, userId, nil
}

func checkNodeActionAllowed(nodeId int64) error {
	if nodeId <= 2 {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "system node is not support modify/delete ")
	}
	return nil
}

func checkConfigActionAllowed(configId int64) error {
	if configId <= 0 {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "system config is not support modify/delete ")
	}
	return nil
}

func getUserNames(userIds []string, userNameMap map[string]string) string {
	return fp.StreamOf(userIds).Map(func(id string) string {
		return (userNameMap)[id]
	}).JoinStrings(",")
}

func coverStringIdToInt64(id string) int64 {
	num, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		return -1
	}
	return num
}

func coverStringsToInt64List(id []string) []int64 {
	var res []int64
	for _, idStr := range id {
		res = append(res, coverStringIdToInt64(idStr))
	}
	return res
}

func covertInt64ToString(num int64) string {
	return strconv.FormatInt(num, 10)
}

func formatName(name string) (string, error) {
	result := strings.TrimSpace(name)
	if result == "" {
		return "", consts.ErrorWithParam(model.ErrorCode_SystemError, "name is not support empty")
	}
	return result, nil
}
