package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

func NewListApprovalFlowConfigHandler(in NewListApprovalFlowConfigHandlerIn) handler.HandlerImplementationEnvolope {
	h := &ListApprovalFlowConfigHandler{
		approvalFlowService: in.ApprovalFlowService,
		workflowService:     in.WorkflowService,
	}
	return handler.NewHandler(h.ListApprovalFlowConfig)
}

type ListApprovalFlowConfigHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
	workflowService     workflow.TicketService
}

type NewListApprovalFlowConfigHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
	WorkflowService     workflow.TicketService
}

func (h *ListApprovalFlowConfigHandler) ListApprovalFlowConfig(ctx context.Context, req *model.ListApprovalFlowConfigReq) (*model.ListApprovalFlowConfigResp, error) {
	tenantId, _, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}
	if req.PageSize <= 0 {
		req.SetPageSize(10)
	}
	if req.PageNumber <= 0 {
		req.SetPageNumber(1)
	}
	FlowConfigs, err := h.approvalFlowService.ListApprovalFlowConfig(ctx, tenantId, req)
	if err != nil {
		log.Warn(ctx, "ListApprovalFlowConfig error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	users := fp.StreamOf(FlowConfigs).Map(func(FlowConfig *entity.ApprovalFlowConfig) string {
		return FlowConfig.CreateUserId
	}).JoinStrings(",")
	userNameMap, err := h.workflowService.GetUserNameByIds(ctx, users, tenantId)
	if err != nil {
		log.Warn(ctx, "GetUserNameByIds error:%s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get user info error")
	}
	total, err := h.approvalFlowService.GetApprovalFlowConfigsSum(ctx, req)
	if err != nil {
		log.Warn(ctx, "GetApprovalFlowConfigsSum error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return h.covertFlowConfigResp(ctx, tenantId, FlowConfigs, *userNameMap, total), nil
}

func (h *ListApprovalFlowConfigHandler) covertFlowConfigResp(ctx context.Context, tenantId string, FlowConfigs []*entity.ApprovalFlowConfig, userNameMap map[string]string, total int32) *model.ListApprovalFlowConfigResp {
	result := &model.ListApprovalFlowConfigResp{
		FlowConfigs: []*model.FlowConfig{},
		Total:       total,
	}
	for _, FlowConfig := range FlowConfigs {
		approvalFlowConfig := &model.FlowConfig{
			ConfigId:              covertInt64ToString(FlowConfig.ConfigId),
			ConfigName:            FlowConfig.ConfigName,
			Type:                  model.ConfigType(FlowConfig.ConfigType),
			CreateUser:            &model.UserInfo{UserId: FlowConfig.CreateUserId, UserName: userNameMap[FlowConfig.CreateUserId]},
			Memo:                  &FlowConfig.Memo,
			AssociatedInstanceNum: int32(h.approvalFlowService.GetConfigAssociatedInstanceNum(ctx, tenantId, FlowConfig.ConfigId)),
		}
		result.FlowConfigs = append(result.FlowConfigs, approvalFlowConfig)
	}
	return result
}
