package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

func NewModifyApprovalNodeHandler(in NewModifyApprovalNodeHandlerIn) handler.HandlerImplementationEnvolope {
	h := &ModifyApprovalNodeHandler{
		approvalFlowService: in.ApprovalFlowService,
		workflowService:     in.WorkflowService,
	}
	return handler.NewHandler(h.ModifyApprovalNode)
}

type ModifyApprovalNodeHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
	workflowService     workflow.TicketService
}

type NewModifyApprovalNodeHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
	WorkflowService     workflow.TicketService
}

func (h *ModifyApprovalNodeHandler) ModifyApprovalNode(ctx context.Context, req *model.ModifyApprovalNodeReq) (*model.ModifyApprovalNodeResp, error) {
	tenantId, userId, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}
	err = h.approvalFlowService.CheckActionPermission(ctx, tenantId, userId)
	if err != nil {
		return nil, err
	}

	if err = checkNodeActionAllowed(coverStringIdToInt64(req.NodeId)); err != nil {
		return nil, err
	}

	nodes, err := h.approvalFlowService.ListApprovalNode(ctx, tenantId, &model.ListApprovalNodeReq{NodeId: &req.NodeId, PageSize: 1, PageNumber: 1})
	if err != nil {
		log.Warn(ctx, "ListApprovalNode error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if len(nodes) == 0 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "The node does not exist or does not belong to the tenant,  not support modify ")
	}

	if len(req.ApprovalUsers) == 0 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "At least one approver is required")
	}
	userIds := fp.StreamOf(req.ApprovalUsers).JoinStrings(",") + "," + userId
	userNameMap, err := h.workflowService.GetUserNameByIds(ctx, userIds, tenantId)
	if err != nil {
		log.Warn(ctx, "获取用户信息报错：%s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get user info error")
	}
	approvalNode := &entity.ApprovalNode{
		NodeId:      coverStringIdToInt64(req.NodeId),
		NodeName:    req.ApprovalNodeName,
		ApproverIds: fp.StreamOf(req.ApprovalUsers).JoinStrings(","),
		Approvers:   getUserNames(req.ApprovalUsers, *userNameMap),
		TenantId:    tenantId,
		ModifyUser:  userId,
		Memo:        req.GetMemo(),
	}
	if err := h.approvalFlowService.ModifyApprovalNode(ctx, approvalNode); err != nil {
		log.Warn(ctx, "ModifyApprovalNode error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if err := h.UpdateEveScenesApprovalInfo(ctx, approvalNode.NodeId, approvalNode.ApproverIds, approvalNode.NodeName); err != nil {
		log.Warn(ctx, "UpdateEveScenesApprovalInfo error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)

	}
	return &model.ModifyApprovalNodeResp{}, nil
}

func (h *ModifyApprovalNodeHandler) UpdateEveScenesApprovalInfo(ctx context.Context, approvalNodeId int64, approvalUserIds string, approvalNodeName string) error {
	return h.workflowService.UpdateTicketCurrentUser(ctx, approvalNodeId, approvalUserIds, approvalNodeName)
}
