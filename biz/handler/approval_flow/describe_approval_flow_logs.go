package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
	"regexp"
	"strings"
)

func NewDescribeApprovalFlowLogsHandler(in NewDescribeApprovalFlowLogsHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeApprovalFlowLogsHandler{
		approvalFlowService: in.ApprovalFlowService,
		workflowService:     in.WorkflowService,
	}
	return handler.<PERSON><PERSON><PERSON><PERSON>(h.DescribeApprovalFlowLogs)
}

type DescribeApprovalFlowLogsHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
	workflowService     workflow.TicketService
}

type NewDescribeApprovalFlowLogsHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
	WorkflowService     workflow.TicketService
}

func (h *DescribeApprovalFlowLogsHandler) DescribeApprovalFlowLogs(ctx context.Context, req *model.DescribeApprovalFlowLogsReq) (*model.DescribeApprovalFlowLogsResp, error) {
	tenantId, userId, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}
	// 1.拿实例信息
	instanceInfo, err := h.approvalFlowService.GetInstanceInfo(ctx, req.InstanceId, req.InstanceType.String(), tenantId)
	if err != nil {
		log.Warn(ctx, "GetInstanceInfo error: %s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InstanceNotInSecureMode)
	}
	// 2.获取审批流的信息
	flowConfig, err := h.approvalFlowService.GetApprovalFlowConfig(ctx, instanceInfo.ApprovalFlowConfigId)
	if err != nil {
		log.Warn(ctx, "GetApprovalFlowConfig error: %s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	switch req.ScenesType {
	case model.ScenesType_Ticket:
		return h.DescribeTicketApprovalFlowLogs(ctx, tenantId, flowConfig, req, userId)
	}
	return nil, nil
}

func (h *DescribeApprovalFlowLogsHandler) DescribeTicketApprovalFlowLogs(ctx context.Context, tenantId string, flowConfig *entity.ApprovalFlowConfig, req *model.DescribeApprovalFlowLogsReq, createUserId string) (*model.DescribeApprovalFlowLogsResp, error) {
	ticketFlowTemplateId := int64(0)
	for _, scenes := range flowConfig.FlowScenes {
		if scenes.ScenesType == entity.TicketFlowType {
			ticketFlowTemplateId = scenes.TemplateId
		}
	}
	// 3.获取审批节点信息
	flowNodes, err := h.approvalFlowService.DescribeApprovalFlowLogs(ctx, ticketFlowTemplateId, -1, req.InstanceId, tenantId, createUserId)
	if err != nil {
		log.Warn(ctx, "DescribeApprovalFlowLogs error: %s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get approval flow nodes error")
	}
	// 4.构造userid和用户名的映射
	allIds := fp.StreamOf(flowNodes).Map(func(node *model.WorkflowNode) string {
		return node.Operator
	}).JoinStrings(",")
	idNameMap, err := h.workflowService.GetUserNameByIds(ctx, allIds, tenantId)
	if err != nil {
		log.Warn(ctx, "GetUserNameByIds error: %s", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	for _, flowNode := range flowNodes {
		if flowNode.Status == model.FlowNodeStatus_Undo || flowNode.Status == model.FlowNodeStatus_Approval {
			spaceRe, _ := regexp.Compile(`\s*,\s*`)
			idList := spaceRe.Split(flowNode.Operator, -1)
			flowNode.Operator = h.getUserNamesByIds(idList, idNameMap)
		} else {
			flowNode.Operator = (*idNameMap)[flowNode.Operator]
		}
	}
	return &model.DescribeApprovalFlowLogsResp{FlowNodes: flowNodes}, nil
}

func (h *DescribeApprovalFlowLogsHandler) getUserNamesByIds(ids []string, idNameMap *map[string]string) string {
	var userNames strings.Builder

	for i := 0; i < len(ids); i++ {
		userNames.WriteString((*idNameMap)[(ids)[i]])
		if i != len(ids)-1 {
			userNames.WriteString(",")
		}
	}
	return userNames.String()
}
