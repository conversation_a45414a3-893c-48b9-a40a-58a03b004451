package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"go.uber.org/dig"
)

func NewDeleteApprovalNodeHandler(in NewDeleteApprovalNodeHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DeleteApprovalNodeHandler{
		approvalFlowService: in.ApprovalFlowService,
	}
	return handler.NewHandler(h.DeleteApprovalNode)
}

type DeleteApprovalNodeHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
}

type NewDeleteApprovalNodeHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
}

func (h *DeleteApprovalNodeHandler) DeleteApprovalNode(ctx context.Context, req *model.DeleteApprovalNodeReq) (*model.DeleteApprovalNodeResp, error) {
	tenantId, userId, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}
	if err = h.approvalFlowService.CheckActionPermission(ctx, tenantId, userId); err != nil {
		return nil, err
	}
	nodes, err := h.approvalFlowService.ListApprovalNode(ctx, tenantId, &model.ListApprovalNodeReq{NodeId: &req.NodeId, PageSize: 1, PageNumber: 1})
	if err != nil {
		log.Warn(ctx, "ListApprovalNode error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if len(nodes) == 0 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "The node does not exist or does not belong to the tenant,  not support delete ")
	}
	if err = h.checkNodeDeletedAllowed(ctx, tenantId, coverStringIdToInt64(req.NodeId)); err != nil {
		return nil, err
	}
	if err = h.approvalFlowService.DeleteApprovalNode(ctx, coverStringIdToInt64(req.NodeId)); err != nil {
		log.Warn(ctx, "DeleteApprovalNode error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)

	}
	return &model.DeleteApprovalNodeResp{}, nil
}

func (h *DeleteApprovalNodeHandler) checkNodeDeletedAllowed(ctx context.Context, tenantId string, nodeId int64) error {
	if err := checkNodeActionAllowed(nodeId); err != nil {
		return err
	}
	isIn, err := h.approvalFlowService.IsNodeInApprovalConfig(ctx, tenantId, nodeId)
	if err != nil {
		log.Warn(ctx, "IsNodeInApprovalConfig error:%s", err.Error())
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if isIn {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "the node in approval config, is not support delete ")
	}
	return nil
}
