package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"go.uber.org/dig"
)

func NewCreateApprovalFlowConfigHandler(in NewCreateApprovalFlowConfigHandlerIn) handler.HandlerImplementationEnvolope {
	h := &CreateApprovalFlowConfigHandler{
		approvalFlowService: in.ApprovalFlowService,
		workflowService:     in.WorkflowService,
	}
	return handler.NewHandler(h.CreateApprovalFlowConfig)
}

type CreateApprovalFlowConfigHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
	workflowService     workflow.TicketService
}

type NewCreateApprovalFlowConfigHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
	WorkflowService     workflow.TicketService
}

func (h *CreateApprovalFlowConfigHandler) CreateApprovalFlowConfig(ctx context.Context, req *model.CreateApprovalFlowConfigReq) (*model.CreateApprovalFlowConfigResp, error) {
	tenantId, userId, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}
	err = h.approvalFlowService.CheckActionPermission(ctx, tenantId, userId)
	if err != nil {
		return nil, err
	}
	if len(req.TicketNodeIdList) == 0 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "node is not support 0")
	}
	flowScenes, err := h.CreateApprovalFlowScenes(ctx, req)
	if err != nil {
		log.Warn(ctx, "CreateApprovalFlowScenes error :%s", err.Error())
		return nil, err
	}
	userNameMap, err := h.workflowService.GetUserNameByIds(ctx, userId, tenantId)
	if err != nil {
		log.Warn(ctx, "获取用户信息报错：%s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get user info error")
	}
	configName, err := formatName(req.FlowConfigName)
	if err != nil {
		return nil, err
	}
	approvalFlowConfig := &entity.ApprovalFlowConfig{
		ConfigName:     configName,
		TenantId:       tenantId,
		Memo:           req.GetMemo(),
		CreateUserId:   userId,
		CreateUserName: (*userNameMap)[userId],
		ModifyUserId:   userId,
		FlowScenes:     flowScenes,
	}
	configId, err := h.approvalFlowService.CreateApprovalFlowConfig(ctx, approvalFlowConfig)
	if err != nil {
		log.Warn(ctx, "CreateApprovalFlowConfig error :%s", err.Error())
		return nil, err
	}
	return &model.CreateApprovalFlowConfigResp{FlowConfigId: covertInt64ToString(configId)}, nil
}

func (h *CreateApprovalFlowConfigHandler) CreateApprovalFlowScenes(ctx context.Context, req *model.CreateApprovalFlowConfigReq) ([]*entity.ApprovalFlowScenes, error) {
	var flowScenes []*entity.ApprovalFlowScenes
	// 工单
	ticketId, err := h.approvalFlowService.CreateApprovalTemplate(ctx, coverStringsToInt64List(req.TicketNodeIdList), req.TicketFlowTemplate)
	if err != nil {
		log.Warn(ctx, "CreateApprovalTemplate error :%s", err.Error())
		return nil, err
	}
	flowScenes = append(flowScenes, &entity.ApprovalFlowScenes{TemplateId: ticketId, ScenesType: entity.TicketFlowType})

	return flowScenes, nil
}
