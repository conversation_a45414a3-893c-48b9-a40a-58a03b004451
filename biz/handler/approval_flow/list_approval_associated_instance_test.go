package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
)

func MockListApprovalAssociatedInstanceHandler() *ListApprovalAssociatedInstanceHandler {
	return &ListApprovalAssociatedInstanceHandler{
		approvalFlowService: &mocks.MockApprovalFlowService{},
		workflowService:     &mocks.MockTicketService{},
		dbwInstanceDal:      &mocks.MockDbwInstanceDAL{},
	}
}

func TestListApprovalAssociatedInstance(t *testing.T) {
	handler := MockListApprovalAssociatedInstanceHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockDbwInstanceDAL).ListAllInstancesAssociatedApprovalFlow).Return(&dao.DbwInstances{Items: []*dao.DbwInstance{{}}}, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*ListApprovalAssociatedInstanceHandler).getAllInstance).Return(userNameMap, nil).Build()
	defer mock3.UnPatch()

	_, err := handler.ListApprovalAssociatedInstance(context.Background(), &model.ListApprovalAssociatedInstanceReq{})
	assert.Nil(t, err)
}
