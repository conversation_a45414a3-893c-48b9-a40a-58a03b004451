package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"go.uber.org/dig"
)

func NewModifyApprovalFlowConfigHandler(in NewModifyApprovalFlowConfigHandlerIn) handler.HandlerImplementationEnvolope {
	h := &ModifyApprovalFlowConfigHandler{
		approvalFlowService: in.ApprovalFlowService,
		workflowService:     in.WorkflowService,
	}
	return handler.NewHandler(h.ModifyApprovalFlowConfig)
}

type ModifyApprovalFlowConfigHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
	workflowService     workflow.TicketService
}

type NewModifyApprovalFlowConfigHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
	WorkflowService     workflow.TicketService
}

func (h *ModifyApprovalFlowConfigHandler) ModifyApprovalFlowConfig(ctx context.Context, req *model.ModifyApprovalFlowConfigReq) (*model.ModifyApprovalFlowConfigResp, error) {
	tenantId, userId, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}
	err = h.approvalFlowService.CheckActionPermission(ctx, tenantId, userId)
	if err != nil {
		return nil, err
	}

	if err = checkConfigActionAllowed(coverStringIdToInt64(req.FlowConfigId)); err != nil {
		return nil, err
	}

	configs, err := h.approvalFlowService.ListApprovalFlowConfig(ctx, tenantId, &model.ListApprovalFlowConfigReq{ConfigId: &req.FlowConfigId, PageSize: 1, PageNumber: 1})
	if err != nil {
		log.Warn(ctx, "ListApprovalFlowConfig error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if len(configs) == 0 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "The config does not exist or does not belong to the tenant,  not support modify ")
	}

	if req.TicketFlowTemplate == nil || len(req.TicketFlowTemplate.NodeIdList) == 0 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "At least one approver node is required ")
	}
	flowScenes, err := h.ModifyApprovalFlowScenes(ctx, tenantId, req)
	if err != nil {
		log.Warn(ctx, "ModifyApprovalFlowScenes error:%s", err.Error())
		return nil, err
	}

	approvalFlowConfig := &entity.ApprovalFlowConfig{
		ConfigName:   req.FlowConfigName,
		ConfigId:     coverStringIdToInt64(req.FlowConfigId),
		TenantId:     tenantId,
		Memo:         req.GetMemo(),
		ModifyUserId: userId,
		FlowScenes:   flowScenes,
	}
	err = h.approvalFlowService.ModifyApprovalFlowConfig(ctx, approvalFlowConfig)
	if err != nil {
		log.Warn(ctx, "ModifyApprovalFlowConfig error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return &model.ModifyApprovalFlowConfigResp{}, nil
}

func (h *ModifyApprovalFlowConfigHandler) ModifyApprovalFlowScenes(ctx context.Context, tenantId string, req *model.ModifyApprovalFlowConfigReq) ([]*entity.ApprovalFlowScenes, error) {
	var flowScenes []*entity.ApprovalFlowScenes
	// 工单
	ticketFlowScenes, err := h.ModifyTicketApprovalInfo(ctx, tenantId, req)
	if err != nil {
		log.Warn(ctx, "ModifyTicketApprovalInfo error:%s", err.Error())
		return nil, err
	}
	flowScenes = append(flowScenes, ticketFlowScenes)
	// TODO 如果后续有其他类型，需要按照工单继续补充
	return flowScenes, nil
}

func (h *ModifyApprovalFlowConfigHandler) ModifyTicketApprovalInfo(ctx context.Context, tenantId string, req *model.ModifyApprovalFlowConfigReq) (*entity.ApprovalFlowScenes, error) {
	if err := h.checkTicketApproval(ctx, tenantId, coverStringIdToInt64(req.TicketFlowTemplate.TemplateId)); err != nil {
		return nil, err
	}
	ticketTemplateId, err := h.ModifyApprovalTemplate(ctx, req.TicketFlowTemplate)
	if err != nil {
		log.Warn(ctx, "ModifyApprovalTemplate error:%s", err.Error())
		return nil, err
	}
	return &entity.ApprovalFlowScenes{TemplateId: ticketTemplateId, ScenesType: entity.TicketFlowType}, nil
}

func (h *ModifyApprovalFlowConfigHandler) ModifyApprovalTemplate(ctx context.Context, flow *model.SimpleApprovalFlowTemplate) (int64, error) {
	if len(flow.NodeIdList) == 0 {
		// 免审批，没修改，这一期没有，但是先预留
		return 0, nil
	}
	templateId := coverStringIdToInt64(flow.TemplateId)
	ticketFlowTemplate := &model.ApprovalTemplate{
		NodeList:             flow.NodeIdList,
		NoNeedForApproval:    flow.NoNeedForApproval,
		NotAllowSelfApproval: flow.NotAllowSelfApproval,
		SelfAutoApproval:     flow.SelfAutoApproval,
	}
	if coverStringIdToInt64(flow.TemplateId) == int64(0) {
		// 对于这个模板来说是新建
		newTemplateId, err := h.approvalFlowService.CreateApprovalTemplate(ctx, coverStringsToInt64List(flow.NodeIdList), ticketFlowTemplate)
		if err != nil {
			log.Warn(ctx, "CreateApprovalTemplate error:%s", err.Error())
			return 0, err
		}
		templateId = newTemplateId
	} else {
		// 如果有id，则说明是修改已有的模板
		// 5.6.1 版本，不再对原有模板进行修改，而是新建一个模板，然后修改流程的关联，这样保证旧的，已经创建出来的，依然走旧的流程
		newTemplateId, err := h.approvalFlowService.CreateApprovalTemplate(ctx, coverStringsToInt64List(flow.NodeIdList), ticketFlowTemplate)
		if err != nil {
			log.Warn(ctx, "CreateApprovalTemplate error:%s", err.Error())
			return 0, err
		}
		// 将原有的模板删除，如果这个时候程序挂了怎么办，不影响，因为没有真的删除，只是标记为了deleted = 1，还是可以正常查询
		// 正确的方式是先关联到新配置，再删除模板，但需要修改的逻辑比较多，所以先不做，现有的方式可以满足需求
		err = h.approvalFlowService.DeleteApprovalFlowTemplate(ctx, templateId)
		if err != nil {
			log.Warn(ctx, "DeleteApprovalFlowTemplate error:%s", err.Error())
		}
		templateId = newTemplateId
	}
	return templateId, nil
}

func (h *ModifyApprovalFlowConfigHandler) checkTicketApproval(ctx context.Context, tenantId string, approvalTemplateId int64) error {
	if approvalTemplateId == 0 {
		// 不检查0号，因为0永远不会被改，所以即使这里换了，已经新生成的模板仍然会在执行
		return nil
	}
	isIn, err := h.workflowService.IsTemplateInTicket(ctx, tenantId, approvalTemplateId)
	if err != nil {
		log.Warn(ctx, "IsNodeInApprovalConfig error:%s", err.Error())
		return err
	}
	if isIn {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "the config in approval flow, is not support modify ")
	}
	return nil
}
