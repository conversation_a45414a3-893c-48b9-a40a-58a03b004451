package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

func NewCreateApprovalNodeHandler(in NewCreateApprovalNodeHandlerIn) handler.HandlerImplementationEnvolope {
	h := &CreateApprovalNodeHandler{
		approvalFlowService: in.ApprovalFlowService,
		workflowService:     in.WorkflowService,
	}
	return handler.NewHandler(h.CreateApprovalNode)
}

type CreateApprovalNodeHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
	workflowService     workflow.TicketService
}

type NewCreateApprovalNodeHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
	WorkflowService     workflow.TicketService
}

func (h *CreateApprovalNodeHandler) CreateApprovalNode(ctx context.Context, req *model.CreateApprovalNodeReq) (*model.CreateApprovalNodeResp, error) {
	tenantId, userId, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}
	err = h.approvalFlowService.CheckActionPermission(ctx, tenantId, userId)
	if err != nil {
		return nil, err
	}
	if len(req.ApprovalUsers) == 0 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "At least one approver is required")
	}
	userIds := fp.StreamOf(req.ApprovalUsers).JoinStrings(",") + "," + userId
	userNameMap, err := h.workflowService.GetUserNameByIds(ctx, userIds, tenantId)
	if err != nil {
		log.Warn(ctx, "获取用户信息报错：%s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get user info error")
	}
	nodeName, err := formatName(req.ApprovalNodeName)
	if err != nil {
		return nil, err
	}
	approvalNode := &entity.ApprovalNode{
		NodeName:       nodeName,
		ApproverIds:    fp.StreamOf(req.ApprovalUsers).JoinStrings(","),
		Approvers:      getUserNames(req.ApprovalUsers, *userNameMap),
		TenantId:       tenantId,
		CreateUser:     userId,
		CreateUserName: (*userNameMap)[userId],
		ModifyUser:     userId,
		Memo:           req.GetMemo(),
	}
	nodeId, err := h.approvalFlowService.CreateApprovalNode(ctx, approvalNode)
	if err != nil {
		log.Warn(ctx, "CreateApprovalNode error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return &model.CreateApprovalNodeResp{NodeId: covertInt64ToString(nodeId)}, nil
}
