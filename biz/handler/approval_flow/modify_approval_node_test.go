package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func MockModifyApprovalNodeHandler() *ModifyApprovalNodeHandler {
	return &ModifyApprovalNodeHandler{
		approvalFlowService: &mocks.MockApprovalFlowService{},
		workflowService:     &mocks.MockTicketService{},
	}
}

func TestModifyApprovalNodeError(t *testing.T) {
	handler := MockModifyApprovalNodeHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).CheckActionPermission).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockTicketService).GetUserNameByIds).Return(&userNameMap, nil).Build()
	defer mock3.UnPatch()
	mock8 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalNode).Return([]*entity.ApprovalNode{{}}, fmt.Errorf("test")).Build()
	defer mock8.UnPatch()

	_, err := handler.ModifyApprovalNode(context.Background(), &model.ModifyApprovalNodeReq{NodeId: "123"})
	assert.NotNil(t, err)
}

func TestModifyApprovalNode(t *testing.T) {
	handler := MockModifyApprovalNodeHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).CheckActionPermission).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockTicketService).GetUserNameByIds).Return(&userNameMap, nil).Build()
	defer mock3.UnPatch()
	mock8 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalNode).Return([]*entity.ApprovalNode{{}}, nil).Build()
	defer mock8.UnPatch()

	_, err := handler.ModifyApprovalNode(context.Background(), &model.ModifyApprovalNodeReq{NodeId: "123"})
	assert.NotNil(t, err)

	mock4 := mockey.Mock((*mocks.MockApprovalFlowService).ModifyApprovalNode).Return(fmt.Errorf("test")).Build()
	req := &model.ModifyApprovalNodeReq{NodeId: "123", ApprovalUsers: []string{"1"}}
	_, err = handler.ModifyApprovalNode(context.Background(), req)
	assert.NotNil(t, err)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	_, err = handler.ModifyApprovalNode(context.Background(), &model.ModifyApprovalNodeReq{NodeId: "1"})
	assert.NotNil(t, err)

	mock5 := mockey.Mock((*mocks.MockApprovalFlowService).ModifyApprovalNode).Return(nil).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*ModifyApprovalNodeHandler).UpdateEveScenesApprovalInfo).Return(fmt.Errorf("test")).Build()
	_, err = handler.ModifyApprovalNode(context.Background(), req)
	assert.NotNil(t, err)
	mock6.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock7 := mockey.Mock((*ModifyApprovalNodeHandler).UpdateEveScenesApprovalInfo).Return(nil).Build()
	defer mock7.UnPatch()
	_, err = handler.ModifyApprovalNode(context.Background(), req)
	assert.Nil(t, err)
}
