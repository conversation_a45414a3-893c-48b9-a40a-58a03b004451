package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"go.uber.org/dig"
)

func NewDeleteApprovalFlowConfigHandler(in NewDeleteApprovalFlowConfigHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DeleteApprovalFlowConfigHandler{
		approvalFlowService: in.ApprovalFlowService,
		workflowService:     in.WorkflowService,
	}
	return handler.NewHandler(h.DeleteApprovalFlowConfig)
}

type DeleteApprovalFlowConfigHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
	workflowService     workflow.TicketService
}

type NewDeleteApprovalFlowConfigHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
	WorkflowService     workflow.TicketService
}

func (h *DeleteApprovalFlowConfigHandler) DeleteApprovalFlowConfig(ctx context.Context, req *model.DeleteApprovalFlowConfigReq) (*model.DeleteApprovalFlowConfigResp, error) {
	tenantId, userId, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}
	if err = h.approvalFlowService.CheckActionPermission(ctx, tenantId, userId); err != nil {
		return nil, err
	}
	configs, err := h.approvalFlowService.ListApprovalFlowConfig(ctx, tenantId, &model.ListApprovalFlowConfigReq{ConfigId: &req.FlowConfigId, PageSize: 1, PageNumber: 1})
	if err != nil {
		log.Warn(ctx, "ListApprovalFlowConfig error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if len(configs) == 0 {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "The config does not exist or does not belong to the tenant,  not support delete ")
	}
	approvalConfig, err := h.approvalFlowService.GetApprovalFlowConfig(ctx, coverStringIdToInt64(req.FlowConfigId))
	if err != nil {
		log.Warn(ctx, "GetApprovalFlowConfig error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if err = h.checkConfigDeletedAllowed(ctx, tenantId, approvalConfig); err != nil {
		return nil, err
	}
	if err = h.approvalFlowService.DeleteApprovalFlowConfig(ctx, coverStringIdToInt64(req.FlowConfigId)); err != nil {
		log.Warn(ctx, "DeleteApprovalFlowConfig error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	for _, scenes := range approvalConfig.FlowScenes {
		if err = h.approvalFlowService.DeleteApprovalFlowTemplate(ctx, scenes.TemplateId); err != nil {
			log.Warn(ctx, "DeleteApprovalFlowTemplate error:%s", err.Error())
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
	}
	return &model.DeleteApprovalFlowConfigResp{}, nil
}

func (h *DeleteApprovalFlowConfigHandler) checkConfigDeletedAllowed(ctx context.Context, tenantId string, approvalConfig *entity.ApprovalFlowConfig) error {
	if err := checkConfigActionAllowed(approvalConfig.ConfigId); err != nil {
		return err
	}
	isIn, err := h.approvalFlowService.IsConfigAssociateInstance(ctx, tenantId, approvalConfig.ConfigId)
	if err != nil {
		log.Warn(ctx, "IsConfigAssociateInstance error:%s", err.Error())
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if isIn {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "the approval config is associated other instances, is not support delete ")
	}
	// 检查当前配置，是否
	for _, scenes := range approvalConfig.FlowScenes {
		switch scenes.ScenesType {
		case entity.TicketFlowType:
			if err := h.checkTicketApproval(ctx, tenantId, scenes.TemplateId); err != nil {
				return err
			}
		}
	}
	return nil
}

func (h *DeleteApprovalFlowConfigHandler) checkTicketApproval(ctx context.Context, tenantId string, approvalTemplateId int64) error {
	isIn, err := h.workflowService.IsTemplateInTicket(ctx, tenantId, approvalTemplateId)
	if err != nil {
		log.Warn(ctx, "IsNodeInApprovalConfig error:%s", err.Error())
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if isIn {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "the config in approval flow, is not support delete ")
	}
	return nil
}
