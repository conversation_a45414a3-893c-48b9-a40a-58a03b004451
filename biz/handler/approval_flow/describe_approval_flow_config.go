package approval_flow

import (
	"context"
	"fmt"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

func NewDescribeApprovalFlowConfigHandler(in NewDescribeApprovalFlowConfigHandlerIn) handler.HandlerImplementationEnvolope {
	h := &DescribeApprovalFlowConfigHandler{
		approvalFlowService: in.ApprovalFlowService,
		workflowService:     in.WorkflowService,
	}
	return handler.<PERSON><PERSON>and<PERSON>(h.DescribeApprovalFlowConfig)
}

type DescribeApprovalFlowConfigHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
	workflowService     workflow.TicketService
}

type NewDescribeApprovalFlowConfigHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
	WorkflowService     workflow.TicketService
}

func (h *DescribeApprovalFlowConfigHandler) DescribeApprovalFlowConfig(ctx context.Context, req *model.DescribeApprovalFlowConfigReq) (*model.DescribeApprovalFlowConfigResp, error) {
	tenantId, _, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}

	flowConfigs, err := h.approvalFlowService.ListApprovalFlowConfig(ctx, tenantId, &model.ListApprovalFlowConfigReq{ConfigId: &req.ConfigId, PageNumber: 1, PageSize: 1})
	if err != nil {
		log.Warn(ctx, "ListApprovalFlowConfig error: %s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if len(flowConfigs) <= 0 {
		return nil, fmt.Errorf("didn't find config")
	}

	return h.covertFlowConfigResp(ctx, tenantId, flowConfigs[0])
}

func (h *DescribeApprovalFlowConfigHandler) covertFlowConfigResp(ctx context.Context, tenantId string, flowConfig *entity.ApprovalFlowConfig) (*model.DescribeApprovalFlowConfigResp, error) {
	userNameMap, _ := h.workflowService.GetUserNameByIds(ctx, flowConfig.CreateUserId, tenantId)

	result := &model.DescribeApprovalFlowConfigResp{
		ConfigId:   covertInt64ToString(flowConfig.ConfigId),
		ConfigName: flowConfig.ConfigName,
		Type:       model.ConfigType(flowConfig.ConfigType),
		CreateUser: &model.UserInfo{UserId: flowConfig.CreateUserId, UserName: (*userNameMap)[flowConfig.CreateUserId]},
		Memo:       flowConfig.Memo,
	}
	result.AssociatedInstanceNum = int32(h.approvalFlowService.GetConfigAssociatedInstanceNum(ctx, tenantId, flowConfig.ConfigId))
	for _, scenes := range flowConfig.FlowScenes {
		switch scenes.ScenesType {
		case entity.TicketFlowType:
			flowTemplate, err := h.formatTemplateApprovalNodeInfo(ctx, tenantId, scenes.TemplateId)
			if err != nil {
				return nil, err
			}
			result.TicketFlowTemplate = flowTemplate
		}
	}
	return result, nil
}

func (h *DescribeApprovalFlowConfigHandler) formatTemplateApprovalNodeInfo(ctx context.Context, tenantId string, templateId int64) (*model.ApprovalFlowTemplate, error) {
	template, err := h.approvalFlowService.DescribeApprovalTemplate(ctx, templateId)
	if err != nil {
		log.Warn(ctx, "DescribeApprovalTemplate error: %s", err.Error())
		return nil, err
	}
	users := fp.StreamOf(template.FlowNodes).Map(func(node *entity.ApprovalNode) string {
		if node != nil {
			return node.ApproverIds
		}
		return ""
	}).JoinStrings(",")

	userNameMap, err := h.workflowService.GetUserNameByIds(ctx, users, tenantId)
	if err != nil {
		log.Warn(ctx, "获取用户信息报错：%s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get user info error")
	}
	nodeList := h.covertNode(template, *userNameMap)

	res := &model.ApprovalFlowTemplate{
		NodeList:             nodeList,
		NoNeedForApproval:    template.NoNeedForApproval,
		NotAllowSelfApproval: template.NotAllowSelfApproval,
		SelfAutoApproval:     template.SelfAutoApproval,
		TemplateId:           covertInt64ToString(template.TemplateId),
	}
	return res, nil
}

func (h *DescribeApprovalFlowConfigHandler) covertNode(template *entity.ApprovalFlowTemplate, userNameMap map[string]string) []*model.ApprovalNode {
	var result []*model.ApprovalNode
	for _, node := range template.FlowNodes {
		approvalNode := &model.ApprovalNode{
			NodeId:        covertInt64ToString(node.NodeId),
			NodeName:      node.NodeName,
			NodeType:      model.ConfigType(node.NodeType),
			CreateUser:    &model.UserInfo{UserId: node.CreateUser, UserName: userNameMap[node.CreateUser]},
			Memo:          &node.Memo,
			ApprovalUsers: covertApprovalUsers(node.ApproverIds, userNameMap),
		}
		result = append(result, approvalNode)
	}

	return result
}
