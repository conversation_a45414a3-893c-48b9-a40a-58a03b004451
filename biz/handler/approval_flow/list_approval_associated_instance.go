package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
	"math"
	"sync"
)

func NewListApprovalAssociatedInstanceHandler(in NewListApprovalAssociatedInstanceHandlerIn) handler.HandlerImplementationEnvolope {
	h := &ListApprovalAssociatedInstanceHandler{
		approvalFlowService: in.ApprovalFlowService,
		workflowService:     in.WorkflowService,
		dbwInstanceDal:      in.DbwInstanceDal,
		cfg:                 in.Cfg,
		dsSvc:               in.DsSvc,
		loc:                 in.Loc,
	}
	return handler.NewHandler(h.ListApprovalAssociatedInstance)
}

type ListApprovalAssociatedInstanceHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
	workflowService     workflow.TicketService
	dbwInstanceDal      dal.DbwInstanceDAL
	cfg                 config.ConfigProvider
	dsSvc               datasource.DataSourceService
	loc                 location.Location
}

type NewListApprovalAssociatedInstanceHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
	WorkflowService     workflow.TicketService
	DbwInstanceDal      dal.DbwInstanceDAL
	Cfg                 config.ConfigProvider
	DsSvc               datasource.DataSourceService
	Loc                 location.Location
}

func (h *ListApprovalAssociatedInstanceHandler) ListApprovalAssociatedInstance(ctx context.Context, req *model.ListApprovalAssociatedInstanceReq) (*model.ListApprovalAssociatedInstanceResp, error) {
	tenantId, _, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}
	if req.PageSize <= 0 {
		req.SetPageSize(10)
	}
	if req.PageNumber <= 0 {
		req.SetPageNumber(1)
	}

	limit := req.PageSize
	offset := (req.PageNumber - 1) * req.PageSize
	// 获取所有管控实例列表
	controlInstances, err := h.dbwInstanceDal.ListAllInstancesAssociatedApprovalFlow(ctx, tenantId, coverStringIdToInt64(req.ConfigId), limit, offset)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	typeMap := make(map[string]string)
	for _, value := range controlInstances.Items {
		typeMap[value.InstanceType] = value.InstanceType
	}
	instanceMap, err := h.getAllInstance(ctx, typeMap)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	resp := &model.ListApprovalAssociatedInstanceResp{Total: controlInstances.Total}
	for _, value := range controlInstances.Items {
		resp.Instances = append(resp.Instances, &model.ApprovalInstance{InstanceId: value.InstanceId, InstanceName: instanceMap[value.InstanceId]})
	}
	return resp, nil
}

func (h *ListApprovalAssociatedInstanceHandler) getAllInstance(ctx context.Context, typeMap map[string]string) (map[string]string, error) {
	resInstanceMap := make(map[string]string)
	for instanceType, _ := range typeMap {
		dsType, err := model.DSTypeFromString(instanceType)
		if err != nil {
			log.Warn(ctx, "DSTypeFromString instanceType:%s, error:%s", instanceType, err.Error())
			continue
		}
		instanceTypeMap, err := h.getTypeAllInstances(ctx, &model.DescribeInstancesReq{DSType: &dsType})
		if err != nil {
			log.Warn(ctx, "getTypeAllInstances error:%s", err.Error())
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
		for key, value := range instanceTypeMap {
			resInstanceMap[key] = value
		}
	}
	return resInstanceMap, nil
}

func (h *ListApprovalAssociatedInstanceHandler) getTypeAllInstances(ctx context.Context, req *model.DescribeInstancesReq) (map[string]string, error) {
	instanceMap := make(map[string]string)
	if req.GetDSType() == model.DSType_MetaRDS {
		return nil, nil
	}
	var (
		instanceList    []*model.InstanceInfo
		DefaultPageSize int32
		wg              sync.WaitGroup
	)
	if fwctx.GetTenantID(ctx) == "1" {
		DefaultPageSize = int32(h.cfg.Get(ctx).MaxDescribeDBInstancePageSize) // 默认为500
	} else {
		if req.GetDSType() == model.DSType_Mongo {
			DefaultPageSize = 50 // mongoDB查询性能pageSize=100性能差(当异常实例比较多的时候)
		} else {
			DefaultPageSize = 100
		}
	}
	query := &datasource.ListInstanceReq{
		Type:            conv.ToSharedType(req.GetDSType()),
		LinkType:        shared.Volc,
		RegionId:        h.loc.RegionID(),
		InstanceName:    req.GetInstanceName(),
		InstanceId:      req.GetInstanceId(),
		InstanceStatus:  req.GetInstanceStatus(),
		DBEngineVersion: req.GetDBEngineVersion(),
		ZoneId:          req.GetZoneId(),
		TenantId:        fwctx.GetTenantID(ctx),
		PageNumber:      1,
		PageSize:        10,
	}
	if req.GetDSType() == model.DSType_MSSQL && req.GetInstanceId() != "" {
		query.InstanceId = "" //sqlserver不支持实例id模糊查询,忽略传参
	}
	if req.IsSetSubInstanceType() {
		query.SubInstanceType = req.GetSubInstanceType().String()
	}
	resp, err := h.dsSvc.ListInstance(ctx, query)
	if err != nil {
		log.Warn(ctx, "failed to list instance, err=%v", err)
		return nil, err
	}
	instancesChannel := make(chan *model.InstanceInfo, resp.Total)
	totalPages := int32(math.Ceil(float64(resp.Total) / float64(DefaultPageSize))) // 获取总页数
	if totalPages >= 1 {
		for pageNumber := int32(1); pageNumber <= totalPages; pageNumber++ {
			wg.Add(1)
			go func(pageNumber int32) {
				defer func() {
					recover()
				}()
				defer wg.Done()
				query := &datasource.ListInstanceReq{
					Type:            conv.ToSharedType(req.GetDSType()),
					LinkType:        shared.Volc,
					RegionId:        h.loc.RegionID(),
					InstanceName:    req.GetInstanceName(),
					InstanceId:      req.GetInstanceId(),
					InstanceStatus:  req.GetInstanceStatus(),
					DBEngineVersion: req.GetDBEngineVersion(),
					ZoneId:          req.GetZoneId(),
					TenantId:        fwctx.GetTenantID(ctx),
					PageNumber:      pageNumber,
					PageSize:        DefaultPageSize,
				}
				if req.GetDSType() == model.DSType_MSSQL && req.GetInstanceId() != "" {
					query.InstanceId = "" //sqlserver不支持实例id模糊查询
				}
				if req.IsSetSubInstanceType() {
					query.SubInstanceType = req.GetSubInstanceType().String()
				}
				resp, err := h.dsSvc.ListInstance(ctx, query)
				if err != nil {
					log.Warn(ctx, "failed to list instance, err=%v", err)
					return
				}
				for _, instance := range resp.InstanceList {
					instancesChannel <- instance
				}
			}(pageNumber)
		}
		go func() {
			defer func() {
				recover()
			}()
			wg.Wait()
			close(instancesChannel)
		}()

		for instance := range instancesChannel {
			instanceList = append(instanceList, instance)
		}
	} else {
		instanceList = append(instanceList, resp.InstanceList...)
	}
	for _, instance := range instanceList {
		if instance.InstanceId != nil && instance.InstanceName != nil {
			instanceMap[*instance.InstanceId] = *instance.InstanceName
		}
	}
	return instanceMap, nil
}
