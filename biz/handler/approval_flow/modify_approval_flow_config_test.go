package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func MockModifyApprovalFlowConfigHandler() *ModifyApprovalFlowConfigHandler {
	return &ModifyApprovalFlowConfigHandler{
		approvalFlowService: &mocks.MockApprovalFlowService{},
		workflowService:     &mocks.MockTicketService{},
	}
}

func TestModifyApprovalFlowConfigError(t *testing.T) {
	handler := MockModifyApprovalFlowConfigHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).CheckActionPermission).Return(nil).Build()
	defer mock2.UnPatch()
	mock8 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalFlowConfig).Return([]*entity.ApprovalFlowConfig{{}}, fmt.Errorf("test")).Build()
	defer mock8.UnPatch()

	_, err := handler.ModifyApprovalFlowConfig(context.Background(), &model.ModifyApprovalFlowConfigReq{FlowConfigId: "0"})
	assert.NotNil(t, err)

	_, err = handler.ModifyApprovalFlowConfig(context.Background(), &model.ModifyApprovalFlowConfigReq{FlowConfigId: "123"})
	assert.NotNil(t, err)

}

func TestModifyApprovalFlowConfig(t *testing.T) {
	handler := MockModifyApprovalFlowConfigHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).CheckActionPermission).Return(nil).Build()
	defer mock2.UnPatch()
	mock8 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalFlowConfig).Return([]*entity.ApprovalFlowConfig{{}}, nil).Build()
	defer mock8.UnPatch()

	_, err := handler.ModifyApprovalFlowConfig(context.Background(), &model.ModifyApprovalFlowConfigReq{FlowConfigId: "123"})
	assert.NotNil(t, err)

	req := &model.ModifyApprovalFlowConfigReq{FlowConfigId: "123", TicketFlowTemplate: &model.SimpleApprovalFlowTemplate{NodeIdList: []string{"1"}}}
	mock3 := mockey.Mock((*ModifyApprovalFlowConfigHandler).ModifyApprovalFlowScenes).Return(nil, fmt.Errorf("test")).Build()
	_, err = handler.ModifyApprovalFlowConfig(context.Background(), req)
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	_, err = handler.ModifyApprovalFlowConfig(context.Background(), &model.ModifyApprovalFlowConfigReq{FlowConfigId: "1"})
	assert.NotNil(t, err)

	mock4 := mockey.Mock((*ModifyApprovalFlowConfigHandler).ModifyApprovalFlowScenes).Return(nil, fmt.Errorf("test")).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*mocks.MockApprovalFlowService).ModifyApprovalFlowConfig).Return(fmt.Errorf("test")).Build()
	defer mock5.UnPatch()
	_, err = handler.ModifyApprovalFlowConfig(context.Background(), req)
	assert.NotNil(t, err)
}

func TestModifyApprovalFlowScenes(t *testing.T) {
	handler := MockModifyApprovalFlowConfigHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	req := &model.ModifyApprovalFlowConfigReq{FlowConfigId: "1", TicketFlowTemplate: &model.SimpleApprovalFlowTemplate{NodeIdList: []string{"1"}}}
	mock1 := mockey.Mock((*ModifyApprovalFlowConfigHandler).ModifyTicketApprovalInfo).Return(&entity.ApprovalFlowScenes{}, fmt.Errorf("test")).Build()
	_, err := handler.ModifyApprovalFlowScenes(context.Background(), "1", req)
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*ModifyApprovalFlowConfigHandler).ModifyTicketApprovalInfo).Return(&entity.ApprovalFlowScenes{}, nil).Build()
	defer mock2.UnPatch()
	_, err = handler.ModifyApprovalFlowScenes(context.Background(), "1", req)
	assert.Nil(t, err)
}

func TestModifyTicketApprovalInfo(t *testing.T) {
	handler := MockModifyApprovalFlowConfigHandler()

	req := &model.ModifyApprovalFlowConfigReq{FlowConfigId: "1", TicketFlowTemplate: &model.SimpleApprovalFlowTemplate{TemplateId: "1", NodeIdList: []string{"1"}}}
	mock1 := mockey.Mock((*ModifyApprovalFlowConfigHandler).checkTicketApproval).Return(nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*ModifyApprovalFlowConfigHandler).ModifyApprovalTemplate).Return(1, fmt.Errorf("test")).Build()
	_, err := handler.ModifyTicketApprovalInfo(context.Background(), "1", req)
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*ModifyApprovalFlowConfigHandler).ModifyApprovalTemplate).Return(1, nil).Build()
	defer mock3.UnPatch()
	_, err = handler.ModifyTicketApprovalInfo(context.Background(), "1", req)
	assert.Nil(t, err)
}

func TestModifyApprovalTemplate(t *testing.T) {
	handler := MockModifyApprovalFlowConfigHandler()

	_, err := handler.ModifyApprovalTemplate(context.Background(), &model.SimpleApprovalFlowTemplate{})
	assert.Nil(t, err)

	mock1 := mockey.Mock((*mocks.MockApprovalFlowService).CreateApprovalTemplate).Return(1, fmt.Errorf("test")).Build()
	_, err = handler.ModifyApprovalTemplate(context.Background(), &model.SimpleApprovalFlowTemplate{TemplateId: "0", NodeIdList: []string{"1"}})
	assert.NotNil(t, err)
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).ModifyApprovalTemplate).Return(fmt.Errorf("test")).Build()
	_, err = handler.ModifyApprovalTemplate(context.Background(), &model.SimpleApprovalFlowTemplate{TemplateId: "1", NodeIdList: []string{"1"}})
	assert.NotNil(t, err)
	mock1.UnPatch()
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockApprovalFlowService).ModifyApprovalTemplate).Return(nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockApprovalFlowService).DeleteApprovalFlowTemplate).Return(fmt.Errorf("test")).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*mocks.MockApprovalFlowService).CreateApprovalTemplate).Return(1, nil).Build()
	defer mock5.UnPatch()

	_, err = handler.ModifyApprovalTemplate(context.Background(), &model.SimpleApprovalFlowTemplate{TemplateId: "1", NodeIdList: []string{"1"}})
	assert.Nil(t, err)
}

func TestCheckModifyTicketApproval(t *testing.T) {
	handler := MockModifyApprovalFlowConfigHandler()

	mock2 := mockey.Mock((*mocks.MockTicketService).IsTemplateInTicket).Return(true, fmt.Errorf("test")).Build()
	err := handler.checkTicketApproval(context.Background(), "1", 1)
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockTicketService).IsTemplateInTicket).Return(true, nil).Build()
	err = handler.checkTicketApproval(context.Background(), "1", 1)
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)
}
