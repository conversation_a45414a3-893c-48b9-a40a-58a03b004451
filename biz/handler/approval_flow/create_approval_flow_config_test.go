package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func MockCreateApprovalFlowConfigHandler() *CreateApprovalFlowConfigHandler {
	return &CreateApprovalFlowConfigHandler{
		approvalFlowService: &mocks.MockApprovalFlowService{},
		workflowService:     &mocks.MockTicketService{},
	}
}

func TestCreateApprovalFlowScenes(t *testing.T) {
	handler := MockCreateApprovalFlowConfigHandler()

	mock1 := mockey.Mock((*mocks.MockApprovalFlowService).CreateApprovalTemplate).Return(1, fmt.Errorf("test")).Build()
	_, err := handler.CreateApprovalFlowScenes(context.Background(), &model.CreateApprovalFlowConfigReq{TicketNodeIdList: []string{}})
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).CreateApprovalTemplate).Return(1, nil).Build()
	defer mock2.UnPatch()
	_, err = handler.CreateApprovalFlowScenes(context.Background(), &model.CreateApprovalFlowConfigReq{TicketNodeIdList: []string{}})
	assert.Nil(t, err)
}

func TestCreateApprovalFlowConfig(t *testing.T) {
	handler := MockCreateApprovalFlowConfigHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).CheckActionPermission).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*CreateApprovalFlowConfigHandler).CreateApprovalFlowScenes).Return(nil, nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockTicketService).GetUserNameByIds).Return(&userNameMap, nil).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*mocks.MockApprovalFlowService).CreateApprovalFlowConfig).Return(1, fmt.Errorf("test")).Build()
	_, err := handler.CreateApprovalFlowConfig(context.Background(), &model.CreateApprovalFlowConfigReq{FlowConfigName: "123", TicketNodeIdList: []string{"1"}})
	assert.NotNil(t, err)
	mock5.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock6 := mockey.Mock((*mocks.MockApprovalFlowService).CreateApprovalFlowConfig).Return(1, nil).Build()
	defer mock6.UnPatch()
	_, err = handler.CreateApprovalFlowConfig(context.Background(), &model.CreateApprovalFlowConfigReq{FlowConfigName: "123", TicketNodeIdList: []string{"1"}})
	assert.Nil(t, err)
}
