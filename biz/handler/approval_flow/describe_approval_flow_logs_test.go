package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func MockDescribeApprovalFlowLogsHandler() *DescribeApprovalFlowLogsHandler {
	return &DescribeApprovalFlowLogsHandler{
		approvalFlowService: &mocks.MockApprovalFlowService{},
		workflowService:     &mocks.MockTicketService{},
	}
}

func TestDescribeApprovalFlowLogs(t *testing.T) {
	handler := MockDescribeApprovalFlowLogsHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).GetInstanceInfo).Return(&dao.DbwInstance{}, fmt.Errorf("test")).Build()
	_, err := handler.DescribeApprovalFlowLogs(context.Background(), &model.DescribeApprovalFlowLogsReq{})
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockApprovalFlowService).GetInstanceInfo).Return(&dao.DbwInstance{}, nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockApprovalFlowService).GetApprovalFlowConfig).Return(&entity.ApprovalFlowConfig{}, fmt.Errorf("test")).Build()
	_, err = handler.DescribeApprovalFlowLogs(context.Background(), &model.DescribeApprovalFlowLogsReq{})
	assert.NotNil(t, err)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*mocks.MockApprovalFlowService).GetApprovalFlowConfig).Return(&entity.ApprovalFlowConfig{}, nil).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*DescribeApprovalFlowLogsHandler).DescribeTicketApprovalFlowLogs).Return(&model.DescribeApprovalFlowLogsResp{}, nil).Build()
	defer mock6.UnPatch()
	_, err = handler.DescribeApprovalFlowLogs(context.Background(), &model.DescribeApprovalFlowLogsReq{ScenesType: model.ScenesType_Ticket})
	assert.Nil(t, err)
}

func TestDescribeTicketApprovalFlowLogs(t *testing.T) {
	handler := MockDescribeApprovalFlowLogsHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock((*mocks.MockTicketService).GetUserNameByIds).Return(&userNameMap, nil).Build()
	defer mock1.UnPatch()

	flowConfig := &entity.ApprovalFlowConfig{FlowScenes: []*entity.ApprovalFlowScenes{{ScenesType: entity.TicketFlowType}}}

	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).DescribeApprovalFlowLogs).Return([]*model.WorkflowNode{}, fmt.Errorf("test")).Build()
	_, err := handler.DescribeTicketApprovalFlowLogs(context.Background(), "1", flowConfig, &model.DescribeApprovalFlowLogsReq{}, "")
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockApprovalFlowService).DescribeApprovalFlowLogs).Return([]*model.WorkflowNode{{}}, nil).Build()
	defer mock3.UnPatch()
	_, err = handler.DescribeTicketApprovalFlowLogs(context.Background(), "1", flowConfig, &model.DescribeApprovalFlowLogsReq{}, "")
	assert.Nil(t, err)
}
