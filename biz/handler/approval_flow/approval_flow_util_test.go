package approval_flow

import (
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestCovertApprovalUsers(t *testing.T) {
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"
	res := covertApprovalUsers("123", userNameMap)
	assert.Equal(t, 1, len(res))
	assert.Equal(t, "aaa", res[0].UserName)
}

func TestGetTenantUserId(t *testing.T) {
	mock1 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
	defer mock2.UnPatch()

	_, _, err := getTenantUserId(context.Background())
	mock1.UnPatch()
	assert.NotNil(t, err)
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock(fwctx.GetTenantID).Return("12345").Build()
	defer mock3.UnPatch()

	tenantId, userId, err := getTenantUserId(context.Background())
	assert.Nil(t, err)
	assert.Equal(t, "12345", tenantId)
	assert.Equal(t, "12345", userId)
}

func TestCheckNodeActionAllowed(t *testing.T) {
	err := checkNodeActionAllowed(1)
	assert.NotNil(t, err)
	err = checkNodeActionAllowed(12345)
	assert.Nil(t, err)
}

func TestCheckConfigActionAllowed(t *testing.T) {
	err := checkConfigActionAllowed(0)
	assert.NotNil(t, err)
	err = checkConfigActionAllowed(12345)
	assert.Nil(t, err)
}

func TestGetUserNames(t *testing.T) {
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"
	userNameMap["456"] = "bbb"
	res := getUserNames([]string{"123", "456"}, userNameMap)
	assert.Equal(t, "aaa,bbb", res)
}

func TestCoverStringIdToInt64(t *testing.T) {
	res := coverStringIdToInt64("123")
	assert.Equal(t, int64(123), res)
}

func TestCoverStringsToInt64List(t *testing.T) {
	res := coverStringsToInt64List([]string{"123", "456"})
	assert.Equal(t, 2, len(res))
	assert.Equal(t, int64(123), res[0])
}

func TestCovertInt64ToString(t *testing.T) {
	res := covertInt64ToString(1)
	assert.Equal(t, "1", res)
}

func TestFormatName(t *testing.T) {
	_, err := formatName("   ")
	assert.NotNil(t, err)
	res, err := formatName("  abc  ")
	assert.Nil(t, err)
	assert.Equal(t, "abc", res)
}
