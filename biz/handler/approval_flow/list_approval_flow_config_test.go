package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func MockListApprovalFlowConfigHandler() *ListApprovalFlowConfigHandler {
	return &ListApprovalFlowConfigHandler{
		approvalFlowService: &mocks.MockApprovalFlowService{},
		workflowService:     &mocks.MockTicketService{},
	}
}

func TestListApprovalFlowConfig(t *testing.T) {
	handler := MockListApprovalFlowConfigHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock((*mocks.MockTicketService).GetUserNameByIds).Return(&userNameMap, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalFlowConfig).Return([]*entity.ApprovalFlowConfig{{}}, nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockApprovalFlowService).GetApprovalFlowConfigsSum).Return(1, fmt.Errorf("test")).Build()
	_, err := handler.ListApprovalFlowConfig(context.Background(), &model.ListApprovalFlowConfigReq{})
	assert.NotNil(t, err)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*mocks.MockApprovalFlowService).GetApprovalFlowConfigsSum).Return(1, nil).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*mocks.MockApprovalFlowService).GetConfigAssociatedInstanceNum).Return(1).Build()
	defer mock6.UnPatch()
	_, err = handler.ListApprovalFlowConfig(context.Background(), &model.ListApprovalFlowConfigReq{})
	assert.Nil(t, err)
}
