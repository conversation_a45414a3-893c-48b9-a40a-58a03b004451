package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func MockDeleteApprovalFlowConfigHandler() *DeleteApprovalFlowConfigHandler {
	return &DeleteApprovalFlowConfigHandler{
		approvalFlowService: &mocks.MockApprovalFlowService{},
		workflowService:     &mocks.MockTicketService{},
	}
}

func TestDeleteApprovalFlowConfig(t *testing.T) {
	handler := MockDeleteApprovalFlowConfigHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).CheckActionPermission).Return(nil).Build()
	defer mock2.UnPatch()
	mock4 := mockey.Mock((*DeleteApprovalFlowConfigHandler).checkConfigDeletedAllowed).Return(nil).Build()
	defer mock4.UnPatch()
	mock3 := mockey.Mock((*mocks.MockApprovalFlowService).GetApprovalFlowConfig).Return(&entity.ApprovalFlowConfig{FlowScenes: []*entity.ApprovalFlowScenes{{}}}, nil).Build()
	defer mock3.UnPatch()
	mock10 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalFlowConfig).Return([]*entity.ApprovalFlowConfig{{}}, fmt.Errorf("test")).Build()
	_, err := handler.DeleteApprovalFlowConfig(context.Background(), &model.DeleteApprovalFlowConfigReq{FlowConfigId: "1"})
	assert.NotNil(t, err)
	mock10.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock11 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalFlowConfig).Return([]*entity.ApprovalFlowConfig{{}}, nil).Build()
	defer mock11.UnPatch()
	mock5 := mockey.Mock((*mocks.MockApprovalFlowService).DeleteApprovalFlowConfig).Return(fmt.Errorf("test")).Build()
	_, err = handler.DeleteApprovalFlowConfig(context.Background(), &model.DeleteApprovalFlowConfigReq{FlowConfigId: "1"})
	assert.NotNil(t, err)
	mock5.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock6 := mockey.Mock((*mocks.MockApprovalFlowService).DeleteApprovalFlowConfig).Return(nil).Build()
	defer mock6.UnPatch()
	mock7 := mockey.Mock((*mocks.MockApprovalFlowService).DeleteApprovalFlowTemplate).Return(fmt.Errorf("test")).Build()
	_, err = handler.DeleteApprovalFlowConfig(context.Background(), &model.DeleteApprovalFlowConfigReq{FlowConfigId: "1"})
	assert.NotNil(t, err)
	mock7.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock8 := mockey.Mock((*mocks.MockApprovalFlowService).DeleteApprovalFlowTemplate).Return(nil).Build()
	defer mock8.UnPatch()
	_, err = handler.DeleteApprovalFlowConfig(context.Background(), &model.DeleteApprovalFlowConfigReq{FlowConfigId: "1"})
	assert.Nil(t, err)
}

func TestCheckConfigDeletedAllowed(t *testing.T) {
	handler := MockDeleteApprovalFlowConfigHandler()

	mock1 := mockey.Mock(checkConfigActionAllowed).Return(nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).IsConfigAssociateInstance).Return(true, fmt.Errorf("test")).Build()
	err := handler.checkConfigDeletedAllowed(context.Background(), "1", &entity.ApprovalFlowConfig{FlowScenes: []*entity.ApprovalFlowScenes{{}}})
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockApprovalFlowService).IsConfigAssociateInstance).Return(true, nil).Build()
	err = handler.checkConfigDeletedAllowed(context.Background(), "1", &entity.ApprovalFlowConfig{FlowScenes: []*entity.ApprovalFlowScenes{{}}})
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*mocks.MockApprovalFlowService).IsConfigAssociateInstance).Return(true, nil).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*DeleteApprovalFlowConfigHandler).checkTicketApproval).Return(fmt.Errorf("test")).Build()
	defer mock5.UnPatch()
	err = handler.checkConfigDeletedAllowed(context.Background(), "1", &entity.ApprovalFlowConfig{FlowScenes: []*entity.ApprovalFlowScenes{{}}})
	assert.NotNil(t, err)
}

func TestCheckTicketApproval(t *testing.T) {
	handler := MockDeleteApprovalFlowConfigHandler()

	mock2 := mockey.Mock((*mocks.MockTicketService).IsTemplateInTicket).Return(true, fmt.Errorf("test")).Build()
	err := handler.checkTicketApproval(context.Background(), "1", 1)
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockTicketService).IsTemplateInTicket).Return(true, nil).Build()
	err = handler.checkTicketApproval(context.Background(), "1", 1)
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)
}
