package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func MockDescribeApprovalFlowConfigHandler() *DescribeApprovalFlowConfigHandler {
	return &DescribeApprovalFlowConfigHandler{
		approvalFlowService: &mocks.MockApprovalFlowService{},
		workflowService:     &mocks.MockTicketService{},
	}
}

func TestDescribeApprovalFlowConfig(t *testing.T) {
	handler := MockDescribeApprovalFlowConfigHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock(getTenantUserId).Return("123", "123", nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*DescribeApprovalFlowConfigHandler).covertFlowConfigResp).Return(nil, nil).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalFlowConfig).Return([]*entity.ApprovalFlowConfig{}, fmt.Errorf("test")).Build()
	_, err := handler.DescribeApprovalFlowConfig(context.Background(), &model.DescribeApprovalFlowConfigReq{})
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalFlowConfig).Return([]*entity.ApprovalFlowConfig{}, nil).Build()
	_, err = handler.DescribeApprovalFlowConfig(context.Background(), &model.DescribeApprovalFlowConfigReq{})
	assert.NotNil(t, err)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*mocks.MockApprovalFlowService).ListApprovalFlowConfig).Return([]*entity.ApprovalFlowConfig{{}}, nil).Build()
	defer mock5.UnPatch()
	_, err = handler.DescribeApprovalFlowConfig(context.Background(), &model.DescribeApprovalFlowConfigReq{})
	assert.Nil(t, err)
}

func TestCovertFlowConfigResp(t *testing.T) {
	handler := MockDescribeApprovalFlowConfigHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock((*mocks.MockTicketService).GetUserNameByIds).Return(&userNameMap, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).GetConfigAssociatedInstanceNum).Return(1).Build()
	defer mock2.UnPatch()

	req := &entity.ApprovalFlowConfig{FlowScenes: []*entity.ApprovalFlowScenes{{ScenesType: entity.TicketFlowType}}}

	mock3 := mockey.Mock((*DescribeApprovalFlowConfigHandler).formatTemplateApprovalNodeInfo).Return(&model.ApprovalFlowTemplate{}, fmt.Errorf("test")).Build()
	_, err := handler.covertFlowConfigResp(context.Background(), "", req)
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*DescribeApprovalFlowConfigHandler).formatTemplateApprovalNodeInfo).Return(&model.ApprovalFlowTemplate{}, nil).Build()
	defer mock4.UnPatch()
	_, err = handler.covertFlowConfigResp(context.Background(), "", req)
	assert.Nil(t, err)
}

func TestFormatTemplateApprovalNodeInfo(t *testing.T) {
	handler := MockDescribeApprovalFlowConfigHandler()
	userNameMap := make(map[string]string)
	userNameMap["123"] = "aaa"

	mock1 := mockey.Mock((*mocks.MockApprovalFlowService).DescribeApprovalTemplate).Return(nil, fmt.Errorf("test")).Build()
	_, err := handler.formatTemplateApprovalNodeInfo(context.Background(), "1", 1)
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*mocks.MockApprovalFlowService).DescribeApprovalTemplate).Return(&entity.ApprovalFlowTemplate{FlowNodes: []*entity.ApprovalNode{{}}}, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockTicketService).GetUserNameByIds).Return(&userNameMap, fmt.Errorf("test")).Build()
	_, err = handler.formatTemplateApprovalNodeInfo(context.Background(), "1", 1)
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*mocks.MockTicketService).GetUserNameByIds).Return(&userNameMap, nil).Build()
	defer mock4.UnPatch()
	_, err = handler.formatTemplateApprovalNodeInfo(context.Background(), "1", 1)
	assert.Nil(t, err)
}
