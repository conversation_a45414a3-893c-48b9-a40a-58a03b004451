package approval_flow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

func NewListApprovalNodeHandler(in NewListApprovalNodeHandlerIn) NewListApprovalNodeHandlerOut {
	h := &ListApprovalNodeHandler{
		approvalFlowService: in.ApprovalFlowService,
		workflowService:     in.WorkflowService,
	}
	return NewListApprovalNodeHandlerOut{HandlerImplEnvolope: handler.<PERSON><PERSON><PERSON><PERSON>(h.ListApprovalNode), Handler: h}
}

type ListApprovalNodeHandler struct {
	approvalFlowService approval_flow.ApprovalFlowService
	workflowService     workflow.TicketService
}

type NewListApprovalNodeHandlerIn struct {
	dig.In
	ApprovalFlowService approval_flow.ApprovalFlowService
	WorkflowService     workflow.TicketService
}

type NewListApprovalNodeHandlerOut struct {
	dig.Out
	HandlerImplEnvolope handler.HandlerImplementationEnvolope
	Handler             *ListApprovalNodeHandler
}

func (h *ListApprovalNodeHandler) ListApprovalNode(ctx context.Context, req *model.ListApprovalNodeReq) (*model.ListApprovalNodeResp, error) {
	tenantId, _, err := getTenantUserId(ctx)
	if err != nil {
		return nil, err
	}
	if req.PageSize <= 0 {
		req.SetPageSize(10)
	}
	if req.PageNumber <= 0 {
		req.SetPageNumber(1)
	}
	nodes, err := h.approvalFlowService.ListApprovalNode(ctx, tenantId, req)
	if err != nil {
		log.Warn(ctx, "ListApprovalNode error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	users := fp.StreamOf(nodes).Map(func(node *entity.ApprovalNode) string {
		if node != nil {
			return node.ApproverIds + "," + node.CreateUser
		}
		return ""
	}).JoinStrings(",")
	userNameMap, err := h.workflowService.GetUserNameByIds(ctx, users, tenantId)
	if err != nil {
		log.Warn(ctx, "GetUserNameByIds error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	total, err := h.approvalFlowService.GetApprovalNodesSum(ctx, tenantId, req)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return h.covertNodeResp(nodes, *userNameMap, total), nil
}

func (h *ListApprovalNodeHandler) covertNodeResp(nodes []*entity.ApprovalNode, userNameMap map[string]string, total int32) *model.ListApprovalNodeResp {
	result := &model.ListApprovalNodeResp{
		ApprovalNodes: []*model.ApprovalNode{},
		Total:         total,
	}
	for _, node := range nodes {
		approvalNode := &model.ApprovalNode{
			NodeId:        covertInt64ToString(node.NodeId),
			NodeName:      node.NodeName,
			NodeType:      model.ConfigType(node.NodeType),
			CreateUser:    &model.UserInfo{UserId: node.CreateUser, UserName: userNameMap[node.CreateUser]},
			Memo:          &node.Memo,
			ApprovalUsers: covertApprovalUsers(node.ApproverIds, userNameMap),
		}
		result.ApprovalNodes = append(result.ApprovalNodes, approvalNode)
	}

	return result
}
