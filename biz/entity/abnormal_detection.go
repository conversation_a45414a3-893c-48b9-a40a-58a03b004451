package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"github.com/qjpcpu/fp"
)

type DbwAbnormalDetectionConfig struct {
	ConfigId      int64
	InstanceId    string
	InstanceType  string
	RegionId      string
	TenantId      string
	Enable        int8
	Deleted       int8
	UpdateTime    int64
	LastAlarmTime int64
}

type DbwAbnormalDetectionEvent struct {
	EventId     int64
	ConfigId    int64
	DateTime    int64
	Item        string
	ItemValue   string
	Score       string
	ErrorDetail string
	Deleted     int8
}

func DetectionConfigDaoToEntity(t []*dao.DbwAbnormalDetectionConfig) []*DbwAbnormalDetectionConfig {
	var res = make([]*DbwAbnormalDetectionConfig, 0, len(t))
	_ = fp.StreamOf(t).Map(func(item *dao.DbwAbnormalDetectionConfig) *DbwAbnormalDetectionConfig {
		return DbwAbnormalDetectionConfigDaoToEntity(item)
	}).ToSlice(&res)
	return res
}

func DetectionEventDaoToEntity(t []*dao.DbwAbnormalDetectionEvent) []*DbwAbnormalDetectionEvent {
	var res = make([]*DbwAbnormalDetectionEvent, 0, len(t))
	_ = fp.StreamOf(t).Map(func(item *dao.DbwAbnormalDetectionEvent) *DbwAbnormalDetectionEvent {
		return DbwAbnormalDetectionEventDaoToEntity(item)
	}).ToSlice(&res)
	return res
}

func DbwAbnormalDetectionConfigDaoToEntity(t *dao.DbwAbnormalDetectionConfig) *DbwAbnormalDetectionConfig {
	return &DbwAbnormalDetectionConfig{
		ConfigId:      t.ConfigId,
		InstanceId:    t.InstanceId,
		InstanceType:  t.InstanceType,
		RegionId:      t.RegionId,
		TenantId:      t.TenantId,
		Enable:        t.Enable,
		Deleted:       t.Deleted,
		UpdateTime:    t.UpdateTime,
		LastAlarmTime: t.LastAlarmTime,
	}
}

func (t *DbwAbnormalDetectionConfig) ToDao() *dao.DbwAbnormalDetectionConfig {
	return &dao.DbwAbnormalDetectionConfig{
		ConfigId:      t.ConfigId,
		InstanceId:    t.InstanceId,
		InstanceType:  t.InstanceType,
		RegionId:      t.RegionId,
		TenantId:      t.TenantId,
		Enable:        t.Enable,
		Deleted:       t.Deleted,
		UpdateTime:    t.UpdateTime,
		LastAlarmTime: t.LastAlarmTime,
	}
}

func DbwAbnormalDetectionEventDaoToEntity(t *dao.DbwAbnormalDetectionEvent) *DbwAbnormalDetectionEvent {
	return &DbwAbnormalDetectionEvent{
		EventId:     t.EventId,
		ConfigId:    t.ConfigId,
		DateTime:    t.DateTime,
		Item:        t.Item,
		ItemValue:   t.ItemValue,
		ErrorDetail: t.ErrorDetail,
		Deleted:     t.Deleted,
		Score:       t.Score,
	}
}

func (t *DbwAbnormalDetectionEvent) ToDao() *dao.DbwAbnormalDetectionEvent {
	return &dao.DbwAbnormalDetectionEvent{
		EventId:     t.EventId,
		ConfigId:    t.ConfigId,
		DateTime:    t.DateTime,
		Item:        t.Item,
		ItemValue:   t.ItemValue,
		ErrorDetail: t.ErrorDetail,
		Deleted:     t.Deleted,
		Score:       t.Score,
	}
}
