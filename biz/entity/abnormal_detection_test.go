package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestDetectionConfigDaoToEntity(t *testing.T) {
	res := DetectionConfigDaoToEntity([]*dao.DbwAbnormalDetectionConfig{{ConfigId: 123}})
	assert.Equal(t, 1, len(res))
	assert.Equal(t, int64(123), res[0].ConfigId)
}

func TestDetectionEventDaoToEntity(t *testing.T) {
	res := DetectionEventDaoToEntity([]*dao.DbwAbnormalDetectionEvent{{ConfigId: 123}})
	assert.Equal(t, 1, len(res))
	assert.Equal(t, int64(123), res[0].ConfigId)
}

func TestDbwAbnormalDetectionConfigToDao(t *testing.T) {
	config := &DbwAbnormalDetectionConfig{ConfigId: 123}
	res := config.ToDao()
	assert.Equal(t, int64(123), res.ConfigId)
}

func TestDbwAbnormalDetectionEventToDao(t *testing.T) {
	event := &DbwAbnormalDetectionEvent{ConfigId: 123}
	res := event.ToDao()
	assert.Equal(t, int64(123), res.ConfigId)
}
