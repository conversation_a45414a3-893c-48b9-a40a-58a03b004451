package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"encoding/json"
)

type ArchiveTask struct {
	TaskId          int64
	ArchiveConfigId int64
	IsBackUp        bool
	InstanceId      string
	InstanceType    string
	Database        string
	Table           string
	TenantId        string
	CreateTime      int64
	TicketId        int64
	SqlTaskId       string
	DeleteSql       string
	ExecTime        int64
	TaskStatus      model.ArchiveTaskStatus
	ErrorMsg        string
	UpdateTime      int64

	BackupTaskId string
	SubSessionId string

	AffectedRows string
	UserName     string
}

type ArchiveConfig struct {
	ConfigId          int64
	TicketId          int64
	InstanceId        string
	InstanceType      string
	TenantId          string
	CronStr           string
	BizTimes          []*model.TimeInfo
	IsBackUp          bool
	IsOpen            bool
	ArchiveType       model.ArchiveType
	LastArchiveStatus model.ArchiveConfigStatus
	Database          string
	TableName         string
	OtherCase         string
	CreateTime        int64
	UpdateTime        int64
	Deleted           int8
	CreateUserId      string
	CreateUser        string

	BackUpType model.BackUpType
}

type ListArchiveTasksResp struct {
	Tasks []*ArchiveTask
	Total int32
}

type ListArchiveConfigsResp struct {
	Configs []*ArchiveConfig
	Total   int32
}

func ArchiveTasksToEntity(tasks []*dao.ArchiveTask) []*ArchiveTask {
	var res []*ArchiveTask
	for _, task := range tasks {
		res = append(res, ArchiveTaskDaoToEntity(task))
	}
	return res
}

func ArchiveTaskDaoToEntity(task *dao.ArchiveTask) *ArchiveTask {
	return &ArchiveTask{
		TaskId:          task.TaskId,
		ArchiveConfigId: task.ArchiveConfigId,
		IsBackUp:        task.IsBackUp,
		InstanceId:      task.InstanceId,
		InstanceType:    task.InstanceType,
		Database:        task.Database,
		Table:           task.Table,
		TenantId:        task.TenantId,
		CreateTime:      task.CreateTime,
		TicketId:        task.TicketId,
		SqlTaskId:       task.SqlTaskId,
		DeleteSql:       task.DeleteSql,
		ExecTime:        task.ExecTime,
		TaskStatus:      model.ArchiveTaskStatus(task.TaskStatus),
		ErrorMsg:        task.ErrorMsg,
		UpdateTime:      task.UpdateTime,
		BackupTaskId:    task.BackupTaskId,

		AffectedRows: task.AffectedRows,
		UserName:     task.UserName,
	}
}

func ArchiveConfigsToEntity(configs []*dao.ArchiveConfig) []*ArchiveConfig {
	var res []*ArchiveConfig
	for _, config := range configs {
		res = append(res, ArchiveConfigDaoToEntity(config))
	}
	return res
}

func ArchiveConfigDaoToEntity(config *dao.ArchiveConfig) *ArchiveConfig {
	var BizTimes []*model.TimeInfo
	err := json.Unmarshal([]byte(config.BizTimes), &BizTimes)
	if err != nil {
		log.Warn(context.Background(), "configId:%d Unmarshal BizTimes error: %s", config.ConfigId, err.Error())
	}
	return &ArchiveConfig{
		ConfigId:          config.ConfigId,
		TicketId:          config.TicketId,
		InstanceId:        config.InstanceId,
		InstanceType:      config.InstanceType,
		TenantId:          config.TenantId,
		CronStr:           config.CronStr,
		BizTimes:          BizTimes,
		IsBackUp:          config.IsBackUp == 1,
		IsOpen:            config.IsOpen == 1,
		ArchiveType:       model.ArchiveType(config.ArchiveType),
		LastArchiveStatus: model.ArchiveConfigStatus(config.LastArchiveStatus),
		Database:          config.Database,
		TableName:         config.Table,
		OtherCase:         config.OtherCase,
		CreateTime:        config.CreateTime,
		UpdateTime:        config.UpdateTime,
		Deleted:           config.Deleted,
		CreateUserId:      config.CreateUserId,
		CreateUser:        config.CreateUser,
		BackUpType:        model.BackUpType(config.BackUpType),
	}
}

func (t *ArchiveTask) ToDao() *dao.ArchiveTask {
	return &dao.ArchiveTask{
		TaskId:          t.TaskId,
		ArchiveConfigId: t.ArchiveConfigId,
		IsBackUp:        t.IsBackUp,
		InstanceId:      t.InstanceId,
		InstanceType:    t.InstanceType,
		Database:        t.Database,
		Table:           t.Table,
		TenantId:        t.TenantId,
		CreateTime:      t.CreateTime,
		TicketId:        t.TicketId,
		SqlTaskId:       t.SqlTaskId,
		DeleteSql:       t.DeleteSql,
		ExecTime:        t.ExecTime,
		TaskStatus:      int8(t.TaskStatus),
		ErrorMsg:        t.ErrorMsg,
		UpdateTime:      t.UpdateTime,

		BackupTaskId: t.BackupTaskId,
		AffectedRows: t.AffectedRows,
		UserName:     t.UserName,
	}
}

func (t *ArchiveConfig) ToDao() *dao.ArchiveConfig {
	jsonData, _ := json.Marshal(t.BizTimes)
	BizTimesStr := string(jsonData)

	return &dao.ArchiveConfig{
		ConfigId:     t.ConfigId,
		TicketId:     t.TicketId,
		InstanceId:   t.InstanceId,
		InstanceType: t.InstanceType,
		TenantId:     t.TenantId,
		CronStr:      t.CronStr,
		BizTimes:     BizTimesStr,
		IsBackUp:     boolToInt(t.IsBackUp),
		IsOpen:       boolToInt(t.IsOpen),
		ArchiveType:  int8(t.ArchiveType),
		Database:     t.Database,
		Table:        t.TableName,
		OtherCase:    t.OtherCase,
		CreateTime:   t.CreateTime,
		UpdateTime:   t.UpdateTime,
		Deleted:      t.Deleted,
		CreateUserId: t.CreateUserId,
		CreateUser:   t.CreateUser,
		BackUpType:   int8(t.BackUpType),
	}
}

func boolToInt(value bool) int8 {
	if value {
		return 1
	}
	return 0
}
