package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"reflect"
	"testing"
)

func TestInspectionConfigDaoToEntity(t *testing.T) {
	type args struct {
		t *dao.InspectionConfig
	}
	tests := []struct {
		name string
		args args
		want *InspectionConfig
	}{
		{
			name: "test",
			args: args{t: &dao.InspectionConfig{
				InstanceId: "1",
			}},
			want: &InspectionConfig{InstanceId: "1"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionConfigDaoToEntity(tt.args.t); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON>("InspectionConfigDaoToEntity() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionConfig_ToDao(t1 *testing.T) {
	type fields struct {
		InstanceId                    string
		InstanceName                  string
		RegionId                      string
		IsOpen                        int8
		InspectionStartTime           int64
		InspectionEndTime             int64
		InspectionExecutableStartTime int64
		InspectionExecutableEndTime   int64
		LastCreateTaskTime            int64
		TenantId                      string
	}
	tests := []struct {
		name   string
		fields fields
		want   *dao.InspectionConfig
	}{
		{
			name: "test",
			fields: fields{
				InstanceId: "1",
			},
			want: &dao.InspectionConfig{InstanceId: "1"},
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &InspectionConfig{
				InstanceId:                    tt.fields.InstanceId,
				InstanceName:                  tt.fields.InstanceName,
				RegionId:                      tt.fields.RegionId,
				IsOpen:                        tt.fields.IsOpen,
				InspectionStartTime:           tt.fields.InspectionStartTime,
				InspectionEndTime:             tt.fields.InspectionEndTime,
				InspectionExecutableStartTime: tt.fields.InspectionExecutableStartTime,
				InspectionExecutableEndTime:   tt.fields.InspectionExecutableEndTime,
				LastCreateTaskTime:            tt.fields.LastCreateTaskTime,
				TenantId:                      tt.fields.TenantId,
			}
			if got := t.ToDao(); !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ToDao() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionReportDaoToEntity(t *testing.T) {
	type args struct {
		t *dao.InspectionReport
	}
	tests := []struct {
		name string
		args args
		want *InspectionReport
	}{
		{
			name: "test",
			args: args{t: &dao.InspectionReport{
				InstanceId: "1",
			}},
			want: &InspectionReport{InstanceId: "1"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionReportDaoToEntity(tt.args.t); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InspectionReportDaoToEntity() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionReport_ToDao(t1 *testing.T) {
	type fields struct {
		TaskID              int64
		InstanceType        string
		InstanceId          string
		InstanceSpec        string
		InstanceVersion     string
		InspectionStartTime int64
		InspectionEndTime   int64
		MaxCpuUsage         float64
		MinCpuUsage         float64
		AvgCpuUsage         float64
		MaxMemUsage         float64
		MinMemUsage         float64
		AvgMemUsage         float64
		MaxDiskUsage        float64
		MinDiskUsage        float64
		AvgDiskUsage        float64
		MaxConnUsage        float64
		MinConnUsage        float64
		AvgConnUsage        float64
		MaxConnected        int64
		ThreadConnected     int64
		ThreadRunning       int64
	}
	tests := []struct {
		name   string
		fields fields
		want   *dao.InspectionReport
	}{
		{
			name: "test",
			fields: fields{
				InstanceId: "1",
			},
			want: &dao.InspectionReport{InstanceId: "1"},
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &InspectionReport{
				TaskID:              tt.fields.TaskID,
				InstanceType:        tt.fields.InstanceType,
				InstanceId:          tt.fields.InstanceId,
				InstanceSpec:        tt.fields.InstanceSpec,
				InstanceVersion:     tt.fields.InstanceVersion,
				InspectionStartTime: tt.fields.InspectionStartTime,
				InspectionEndTime:   tt.fields.InspectionEndTime,
				MaxCpuUsage:         tt.fields.MaxCpuUsage,
				MinCpuUsage:         tt.fields.MinCpuUsage,
				AvgCpuUsage:         tt.fields.AvgCpuUsage,
				MaxMemUsage:         tt.fields.MaxMemUsage,
				MinMemUsage:         tt.fields.MinMemUsage,
				AvgMemUsage:         tt.fields.AvgMemUsage,
				MaxDiskUsage:        tt.fields.MaxDiskUsage,
				MinDiskUsage:        tt.fields.MinDiskUsage,
				AvgDiskUsage:        tt.fields.AvgDiskUsage,
				MaxConnUsage:        tt.fields.MaxConnUsage,
				MinConnUsage:        tt.fields.MinConnUsage,
				AvgConnUsage:        tt.fields.AvgConnUsage,
				MaxConnected:        tt.fields.MaxConnected,
				ThreadConnected:     tt.fields.ThreadConnected,
				ThreadRunning:       tt.fields.ThreadRunning,
			}
			if got := t.ToDao(); !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ToDao() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionSlowLogDaoToEntity(t *testing.T) {
	type args struct {
		t []*dao.InspectionSlowLog
	}
	tests := []struct {
		name string
		args args
		want []*InspectionSlowLog
	}{
		{
			name: "test",
			args: args{t: []*dao.InspectionSlowLog{{
				InstanceId: "1",
			},
			}},
			want: []*InspectionSlowLog{{InstanceId: "1"}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionSlowLogDaoToEntity(tt.args.t); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InspectionSlowLogDaoToEntity() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionSlowLog_ToDao(t1 *testing.T) {
	type fields struct {
		ID                  int64
		NumberId            int32
		InstanceId          string
		InstanceName        string
		InspectionStartTime int64
		InspectionEndTime   int64
		SqlTemplate         string
		DBName              string
		ExecuteUser         string
		ExecuteCount        int64
		TotalQueryTime      float64
		AvgQueryTime        float64
		AvgLockTime         float64
		AvgRowsSent         float64
	}
	tests := []struct {
		name   string
		fields fields
		want   *dao.InspectionSlowLog
	}{
		{
			name: "test",
			fields: fields{
				InstanceId: "1",
			},
			want: &dao.InspectionSlowLog{InstanceId: "1"},
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &InspectionSlowLog{
				ID:                  tt.fields.ID,
				NumberId:            tt.fields.NumberId,
				InstanceId:          tt.fields.InstanceId,
				InstanceName:        tt.fields.InstanceName,
				InspectionStartTime: tt.fields.InspectionStartTime,
				InspectionEndTime:   tt.fields.InspectionEndTime,
				SqlTemplate:         tt.fields.SqlTemplate,
				DBName:              tt.fields.DBName,
				ExecuteUser:         tt.fields.ExecuteUser,
				ExecuteCount:        tt.fields.ExecuteCount,
				TotalQueryTime:      tt.fields.TotalQueryTime,
				AvgQueryTime:        tt.fields.AvgQueryTime,
				AvgLockTime:         tt.fields.AvgLockTime,
				AvgRowsSent:         tt.fields.AvgRowsSent,
			}
			if got := t.ToDao(); !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ToDao() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionTaskDaoToEntity(t *testing.T) {
	type args struct {
		t *dao.InspectionTask
	}
	tests := []struct {
		name string
		args args
		want *InspectionTask
	}{
		{
			name: "test",
			args: args{t: &dao.InspectionTask{
				InstanceId: "1",
			}},
			want: &InspectionTask{InstanceId: "1"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionTaskDaoToEntity(tt.args.t); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InspectionTaskDaoToEntity() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionTask_ToDao(t1 *testing.T) {
	type fields struct {
		ID                    int64
		InstanceType          string
		InstanceId            string
		InstanceName          string
		TenantId              string
		InspectionStatus      model.InspectionStatus
		InspectionType        model.InspectionType
		InspectionStartTime   int64
		InspectionEndTime     int64
		InspectionExecuteTime int64
		HealthScore           int8
		CpuUsage              float64
		MemUsage              float64
		DiskUsage             float64
		ConnUsage             float64
		Qps                   float64
		Tps                   float64
		SlowLog               int32
		CreatedAt             int64
		UpdatedAt             int64
		Deleted               int8
		Memo                  string
		RegionId              string
	}
	tests := []struct {
		name   string
		fields fields
		want   *dao.InspectionTask
	}{
		{
			name: "test",
			fields: fields{
				InstanceId: "1",
			},
			want: &dao.InspectionTask{InstanceId: "1"},
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &InspectionTask{
				ID:                    tt.fields.ID,
				InstanceType:          tt.fields.InstanceType,
				InstanceId:            tt.fields.InstanceId,
				InstanceName:          tt.fields.InstanceName,
				TenantId:              tt.fields.TenantId,
				InspectionStatus:      tt.fields.InspectionStatus,
				InspectionType:        tt.fields.InspectionType,
				InspectionStartTime:   tt.fields.InspectionStartTime,
				InspectionEndTime:     tt.fields.InspectionEndTime,
				InspectionExecuteTime: tt.fields.InspectionExecuteTime,
				HealthScore:           tt.fields.HealthScore,
				CpuUsage:              tt.fields.CpuUsage,
				MemUsage:              tt.fields.MemUsage,
				DiskUsage:             tt.fields.DiskUsage,
				ConnUsage:             tt.fields.ConnUsage,
				Qps:                   tt.fields.Qps,
				Tps:                   tt.fields.Tps,
				SlowLog:               tt.fields.SlowLog,
				CreatedAt:             tt.fields.CreatedAt,
				UpdatedAt:             tt.fields.UpdatedAt,
				Deleted:               tt.fields.Deleted,
				Memo:                  tt.fields.Memo,
				RegionId:              tt.fields.RegionId,
			}
			if got := t.ToDao(); !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("ToDao() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionTasksDaoToEntity(t *testing.T) {
	type args struct {
		t []*dao.InspectionTask
	}
	tests := []struct {
		name string
		args args
		want []*InspectionTask
	}{
		{
			name: "test",
			args: args{t: []*dao.InspectionTask{{
				InstanceId: "1",
			},
			}},
			want: []*InspectionTask{{InstanceId: "1"}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionTasksDaoToEntity(tt.args.t); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InspectionTasksDaoToEntity() = %v, want %v", got, tt.want)
			}
		})
	}
}
