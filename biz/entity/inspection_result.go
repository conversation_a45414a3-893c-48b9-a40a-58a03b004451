package entity

import "code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

type InspectResultInfo interface {
	GetBasic() *InspectionBasicInfo
	GetReport() *InspectionReportInfo
	GetSlowLogs() *[]*model.InspectionSlowLog
	SetStatus(status model.InspectionStatus)
	SetDescribeTableSpaceAutoIncrResp(res *model.DescribeTableSpaceAutoIncrResp, err error)
	SetDescribeTableSpaceResp(res *model.DescribeTableSpaceResp, err error)
	SetDescribeSqlTemplatesContrastResp(res *model.DescribeSqlTemplatesContrastResp, err error)
	SetDescribeAggregateDialogsResp(res *model.DescribeAggregateDialogsResp, err error)
	SetDescribeFullSqlStatusResp(res *model.DescribeFullSqlStatusResp, err error)
	GetInspectErr() *[]*model.InspectErr
}

type BigKeyInfo struct {
	InstanceId string
	DBName     string
	KeyType    string
	KeyInfo    string
	ValueLen   string
	ValueSize  string
}

type HotKeysInfo struct {
	InstanceId string
	DBName     string
	KeyType    string
	KeyInfo    string
	QueryCount string
	ShardId    string
	NodeId     string
	CreateTime string
}

type InspectionResultInfo struct {
	TaskId                           int64
	Basic                            *InspectionBasicInfo
	Report                           *InspectionReportInfo
	SlowLogs                         []*model.InspectionSlowLog
	InspectionNodeResultInfos        map[string]*InspectionNodeResultInfo
	InspectionNodeItems              map[string]*model.InspectionNodeItem
	LastOneNodeAgg                   *model.InspectAgg
	DescribeSqlTemplatesContrastResp *model.DescribeSqlTemplatesContrastResp
	DescribeAggregateDialogsResp     *model.DescribeAggregateDialogsResp
	DescribeFullSqlStatusResp        *model.DescribeFullSqlStatusResp
	DescribeTableSpaceAutoIncrResp   *model.DescribeTableSpaceAutoIncrResp
	DescribeTableSpaceResp           *model.DescribeTableSpaceResp
	InspectErr                       []*model.InspectErr

	RedisInspectionBigKeysItemItemsValueSize []*BigKeyInfo
	RedisInspectionBigKeysItemItemsValueLen  []*BigKeyInfo
	RedisInspectionHotKeysItemItems          []*HotKeysInfo
}

func (info *InspectionResultInfo) GetInspectErr() *[]*model.InspectErr {
	return &info.InspectErr
}

// SetStatus implements InspectResultInfo.
func (info *InspectionResultInfo) SetStatus(status model.InspectionStatus) {
	// todo
}

func (info *InspectionResultInfo) GetBasic() *InspectionBasicInfo {
	return info.Basic
}
func (info *InspectionResultInfo) GetReport() *InspectionReportInfo {
	return info.Report
}

func (info *InspectionResultInfo) GetSlowLogs() *[]*model.InspectionSlowLog {
	return &info.SlowLogs
}

func (info *InspectionResultInfo) SetDescribeTableSpaceAutoIncrResp(res *model.DescribeTableSpaceAutoIncrResp, err error) {
	if err != nil {
		info.InspectErr = append(info.InspectErr, &model.InspectErr{
			InspectionItem: "DescribeTableSpaceAutoIncrResp",
			ErrMsg:         err.Error(),
		})
	}
	info.DescribeTableSpaceAutoIncrResp = res
}

func (info *InspectionResultInfo) SetDescribeTableSpaceResp(res *model.DescribeTableSpaceResp, err error) {
	if err != nil {
		info.InspectErr = append(info.InspectErr, &model.InspectErr{
			InspectionItem: "DescribeTableSpaceResp",
			ErrMsg:         err.Error(),
		})
	}
	info.DescribeTableSpaceResp = res
}

func (info *InspectionResultInfo) SetDescribeSqlTemplatesContrastResp(res *model.DescribeSqlTemplatesContrastResp, err error) {
	if err != nil {
		info.InspectErr = append(info.InspectErr, &model.InspectErr{
			InspectionItem: "DescribeSqlTemplatesContrastResp",
			ErrMsg:         err.Error(),
		})
	}
	info.DescribeSqlTemplatesContrastResp = res
}

func (info *InspectionResultInfo) SetDescribeAggregateDialogsResp(res *model.DescribeAggregateDialogsResp, err error) {
	if err != nil {
		info.InspectErr = append(info.InspectErr, &model.InspectErr{
			InspectionItem: "DescribeAggregateDialogsResp",
			ErrMsg:         err.Error(),
		})
	}
	info.DescribeAggregateDialogsResp = res
}
func (info *InspectionResultInfo) SetDescribeFullSqlStatusResp(res *model.DescribeFullSqlStatusResp, err error) {
	if err != nil {
		info.InspectErr = append(info.InspectErr, &model.InspectErr{
			InspectionItem: "DescribeFullSqlStatusResp",
			ErrMsg:         err.Error(),
		})
	}
	info.DescribeFullSqlStatusResp = res
}

var (
	_ InspectResultInfo = (*InspectionResultInfo)(nil)
)

type InspectionNodeResultInfo struct {
	TaskId                           int64
	NodeId                           string
	Status                           model.InspectionStatus
	Basic                            *InspectionBasicInfo
	SlowLogs                         []*model.InspectionSlowLog
	Report                           *InspectionReportInfo
	DescribeSqlTemplatesContrastResp *model.DescribeSqlTemplatesContrastResp
	DescribeAggregateDialogsResp     *model.DescribeAggregateDialogsResp
	DescribeFullSqlStatusResp        *model.DescribeFullSqlStatusResp
	DescribeTableSpaceAutoIncrResp   *model.DescribeTableSpaceAutoIncrResp
	DescribeTableSpaceResp           *model.DescribeTableSpaceResp
	InspectErr                       []*model.InspectErr
}

func (i *InspectionNodeResultInfo) GetInspectErr() *[]*model.InspectErr {
	return &i.InspectErr
}

// SetStatus implements InspectResultInfo.
func (i *InspectionNodeResultInfo) SetStatus(status model.InspectionStatus) {
	i.Status = status
}

// GetBasic implements InspectResultInfo.
func (i *InspectionNodeResultInfo) GetBasic() *InspectionBasicInfo {
	return i.Basic
}

// GetReport implements InspectResultInfo.
func (i *InspectionNodeResultInfo) GetReport() *InspectionReportInfo {
	return i.Report
}

// GetSlowLogs implements InspectResultInfo.
func (i *InspectionNodeResultInfo) GetSlowLogs() *[]*model.InspectionSlowLog {
	return &i.SlowLogs
}

func (i *InspectionNodeResultInfo) SetDescribeTableSpaceAutoIncrResp(res *model.DescribeTableSpaceAutoIncrResp, err error) {
	if err != nil {
		i.InspectErr = append(i.InspectErr, &model.InspectErr{
			InspectionItem: "DescribeTableSpaceAutoIncrResp",
			ErrMsg:         err.Error(),
		})
	}
	i.DescribeTableSpaceAutoIncrResp = res
}

func (i *InspectionNodeResultInfo) SetDescribeTableSpaceResp(res *model.DescribeTableSpaceResp, err error) {
	if err != nil {
		i.InspectErr = append(i.InspectErr, &model.InspectErr{
			InspectionItem: "DescribeTableSpaceResp",
			ErrMsg:         err.Error(),
		})
	}
	i.DescribeTableSpaceResp = res
}

func (i *InspectionNodeResultInfo) SetDescribeSqlTemplatesContrastResp(res *model.DescribeSqlTemplatesContrastResp, err error) {
	if err != nil {
		i.InspectErr = append(i.InspectErr, &model.InspectErr{
			InspectionItem: "DescribeSqlTemplatesContrastResp",
			ErrMsg:         err.Error(),
		})
	}
	i.DescribeSqlTemplatesContrastResp = res
}

func (i *InspectionNodeResultInfo) SetDescribeAggregateDialogsResp(res *model.DescribeAggregateDialogsResp, err error) {
	if err != nil {
		i.InspectErr = append(i.InspectErr, &model.InspectErr{
			InspectionItem: "DescribeAggregateDialogsResp",
			ErrMsg:         err.Error(),
		})
	}
	i.DescribeAggregateDialogsResp = res
}

func (i *InspectionNodeResultInfo) SetDescribeFullSqlStatusResp(res *model.DescribeFullSqlStatusResp, err error) {
	if err != nil {
		i.InspectErr = append(i.InspectErr, &model.InspectErr{
			InspectionItem: "DescribeFullSqlStatusResp",
			ErrMsg:         err.Error(),
		})
	}
	i.DescribeFullSqlStatusResp = res
}

var (
	_ InspectResultInfo = (*InspectionNodeResultInfo)(nil)
)

type InspectionBasicInfo struct {
	InspectionExecuteTime int64
	HealthScore           int
	CpuUsage              float64
	MemUsage              float64
	DiskUsage             float64
	ConnectedRatio        float64
	Qps                   float64
	Tps                   float64
	SlowSqlNum            int32
	InspectAgg            *model.InspectAgg
	InspectionStartTime   int64
	InspectionEndTime     int64
}

type InspectionItemMetric struct {
	DataPoints []*model.DataPoint
	Unit       string
	Group      string
	Min        float64
	Avg        float64
	Max        float64
}

type InspectionReportInfo struct {
	InstanceId            string
	InstanceSpecification string
	InstanceType          string
	InstanceVersion       string
	//扣分详情
	ScoreDetail []*model.MetricScore
	//图表数据点
	ItemMetrics []*InspectionItemMetric
	DataMetrics []*model.InspectionDataMetric
}
