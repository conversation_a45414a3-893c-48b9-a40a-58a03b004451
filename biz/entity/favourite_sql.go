package entity

import (
	"github.com/qjpcpu/fp"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

type FavouriteSQL struct {
	ID           int64
	Title        string
	InstanceType model.InstanceType
	InstanceId   string
	TenantId     string
	UserId       string
	DBName       string
	SQLText      string
	Scope        model.SQLScope
	UseTimes     int64
	CreateTime   int64
	ModifyTime   int64
}

func FavouriteSQLDaoToEntity(sql *dao.FavouriteSQL) *FavouriteSQL {
	if sql == nil {
		return nil
	}
	ret := &FavouriteSQL{
		ID:         sql.ID,
		Title:      sql.Title,
		InstanceId: sql.InstanceId,
		DBName:     sql.DBName,
		SQLText:    sql.SQLText,
	}
	scope, _ := model.SQLScopeFromString(sql.Scope)
	instanceType, _ := model.InstanceTypeFromString(sql.InstanceType)
	ret.Scope = scope
	ret.InstanceType = instanceType
	return ret
}

func FavouriteSQLEntityToDao(sql *FavouriteSQL) *dao.FavouriteSQL {
	if sql == nil {
		return nil
	}
	return &dao.FavouriteSQL{
		ID:           sql.ID,
		Title:        sql.Title,
		InstanceId:   sql.InstanceId,
		InstanceType: sql.InstanceType.String(),
		DBName:       sql.DBName,
		SQLText:      sql.SQLText,
		Scope:        sql.Scope.String(),
		CreateTime:   sql.CreateTime,
		ModifyTime:   sql.ModifyTime,
		TenantId:     sql.TenantId,
		UserId:       sql.UserId,
	}
}

func FavouriteSQLEntitiesToDao(sqls []*FavouriteSQL) []*dao.FavouriteSQL {
	var res []*dao.FavouriteSQL
	fp.StreamOf(sqls).Map(func(sql *FavouriteSQL) *dao.FavouriteSQL {
		return FavouriteSQLEntityToDao(sql)
	}).ToSlice(&res)
	return res
}

func FavouriteSQLDaosToEntity(sqls []*dao.FavouriteSQL) []*FavouriteSQL {
	var res []*FavouriteSQL
	fp.StreamOf(sqls).Map(func(sql *dao.FavouriteSQL) *FavouriteSQL {
		return FavouriteSQLDaoToEntity(sql)
	}).ToSlice(&res)
	return res
}
