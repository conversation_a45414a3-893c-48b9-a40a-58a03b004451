package entity

type CommandState int

const (
	CommandPending CommandState = iota
	CommandExecuting
	CommandTerminated
)

type CommandTerminatedReason int

const (
	CommandTerminated_Success CommandTerminatedReason = iota
	CommandTerminated_Failed
	CommandTerminated_Cancel
)

func CommandTerminatedReasonPtr(r CommandTerminatedReason) *CommandTerminatedReason { return &r }

type ResultType int

const (
	TableResult ResultType = iota
)

type CommandType int

const (
	CustomCommand CommandType = iota
)

type Command struct {
	ID           string
	CommandSetID string
	State        CommandState
	Reason       *CommandTerminatedReason
	Content      string
	StartTimeMS  int64
	EndTimeMS    int64
	ResultType   *ResultType
	Header       []string
	ReasonDetail string
	Extra        map[string]interface{}
	TenantID     string
}

func (c *Command) IsFailed() bool {
	return *c.Reason == CommandTerminated_Failed
}
