package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
)

type MigrationTask struct {
	ID          int64
	UserID      string
	UserName    string
	TenantID    string
	Type        string
	InstanceID  string
	DBType      string
	DbName      string
	Tables      string
	Status      uint8
	JobName     string
	ClusterName string
	ObjectName  string
	IgnoreError uint8
	ProgressPt  uint8
	Description string
	Config      string
	CreatedAt   int64
	ExecutedAt  int64
	UpdatedAt   int64
	DeletedAt   int64
	Deleted     uint8
	SqlText     string

	FromType int8
}

type MigrationProgress struct {
	ID             int64
	TaskID         int64
	TenantID       string
	ErrorStatus    int
	ProgressDetail string
	CreatedAt      int64
}

const (
	ExportType          = "Export"
	SqlResultExportType = "SqlResultExport"
)

type ExportConfig struct {
	FileType               string
	Charset                string
	ContentFormat          string
	Dbname                 string
	TargetName             string
	TableList              []string
	Structures             []shared.ExportStructure
	ExportType             string
	IP                     string
	User                   string
	Password               string
	Port                   int
	TempPath               string
	TosAK                  string
	TosSK                  string
	TosToken               string
	CsvFirstRowIsColumnDef bool
	SqlText                string
	AppId                  string
	AppSecret              string
	UserName               string
	ChildFolderToken       string
	CloudFileToken         string
	AdvancedOptions        *shared.AdvancedOptions
	TosBucket              string
}

type UserFolderToken struct {
	UserName    string
	FolderToken string
	TenantId    string
	UserId      string
	Deleted     uint8
	DeletedAt   int64
}

type ImportConfig struct {
	FileType               string
	Charset                string
	DbName                 string
	TableName              string
	IgnoreError            bool
	IP                     string
	User                   string
	Password               string
	Port                   int
	CsvInputType           string
	SourceName             string
	CsvFirstRowIsColumnDef bool
	BlackList              []string
	BatchNum               int
	TempPath               string
	TosAK                  string
	TosSK                  string
	TosToken               string
}
type MigrationTasksInfo struct {
	Total          int64
	MigrationTasks []*MigrationTask
}

type MigrationTaskDetail struct {
	TaskId      int64
	Note        string
	Progress    []*MigrationProgress
	IgnoreError uint8
	SqlText     *string
}

func MigrationTaskDaoToEntity(t *dao.MigrationTask) *MigrationTask {
	return &MigrationTask{
		ID:          t.ID,
		UserID:      t.UserID,
		UserName:    t.UserName,
		DBType:      t.DBType,
		InstanceID:  t.InstanceID,
		Type:        t.Type,
		DbName:      t.DbName,
		Tables:      t.Tables,
		CreatedAt:   t.CreatedAt,
		ExecutedAt:  t.ExecutedAt,
		UpdatedAt:   t.UpdatedAt,
		Status:      t.Status,
		ProgressPt:  t.ProgressPt,
		Description: t.Description,
		Config:      t.Config,
		ObjectName:  t.ObjectName,
		JobName:     t.JobName,
		ClusterName: t.ClusterName,
		TenantID:    t.TenantID,
	}
}

func UserFolderTokenDaoToEntity(t *dao.UserFolderToken) *UserFolderToken {
	return &UserFolderToken{
		UserName:    t.UserName,
		FolderToken: t.FolderToken,
		TenantId:    t.TenantId,
		Deleted:     t.Deleted,
		DeletedAt:   t.DeletedAt,
	}
}

func (p *UserFolderToken) ToDao() *dao.UserFolderToken {
	UserFolderToken := &dao.UserFolderToken{
		UserName:    p.UserName,
		FolderToken: p.FolderToken,
		TenantId:    p.TenantId,
		Deleted:     p.Deleted,
		DeletedAt:   p.DeletedAt,
	}
	return UserFolderToken
}

func MigrationProgressDaoToEntity(t *dao.MigrationProgress) *MigrationProgress {
	return &MigrationProgress{
		ID:             t.ID,
		TenantID:       t.TenantID,
		ProgressDetail: t.ProgressDetail,
		CreatedAt:      t.CreatedAt,
		ErrorStatus:    t.ErrorStatus,
	}
}

func (p *MigrationProgress) ToDao() *dao.MigrationProgress {
	MigrationProgress := &dao.MigrationProgress{
		ID:             p.ID,
		TenantID:       p.TenantID,
		CreatedAt:      p.CreatedAt,
		ErrorStatus:    p.ErrorStatus,
		ProgressDetail: p.ProgressDetail,
		TaskID:         p.TaskID,
	}
	return MigrationProgress
}

func (t *MigrationTask) ToDao() *dao.MigrationTask {
	migrationTask := &dao.MigrationTask{
		ID:          t.ID,
		UserID:      t.UserID,
		UserName:    t.UserName,
		Type:        t.Type,
		DbName:      t.DbName,
		DBType:      t.DBType,
		Tables:      t.Tables,
		Status:      t.Status,
		InstanceID:  t.InstanceID,
		ObjectName:  t.ObjectName,
		Description: t.Description,
		CreatedAt:   t.CreatedAt,
		UpdatedAt:   t.UpdatedAt,
		ExecutedAt:  t.ExecutedAt,
		ClusterName: t.ClusterName,
		TenantID:    t.TenantID,
		Config:      t.Config,
		IgnoreError: t.IgnoreError,
		FromType:    t.FromType,
	}
	return migrationTask
}
