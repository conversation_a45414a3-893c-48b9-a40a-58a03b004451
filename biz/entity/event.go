package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	volctrade "code.byted.org/infcs/lib-mgr-common/volc/trade"
)

type Event struct {
	ID            uint64
	UUID          string
	InstanceID    string
	Type          model.EventType
	SubType       string
	Result        model.EventResult_
	OperationTime *int64
	Payload       *string
	TenantID      string
	UserID        string
}

func (event *Event) ToDao() *dao.Event {
	return &dao.Event{
		UUID:       event.UUID,
		InstanceID: event.InstanceID,
		Type:       event.Type.String(),
		SubType:    event.SubType,
		Result:     int8(event.Result),
		Payload:    event.Payload,
		TenantID:   event.TenantID,
		UserID:     event.UserID,
	}
}

func FromDaoToEvent(dao *dao.Event) *Event {
	eventType, _ := model.EventTypeFromString(dao.Type)
	return &Event{
		ID:            dao.ID,
		UUID:          dao.UUID,
		InstanceID:    dao.InstanceID,
		Type:          eventType,
		SubType:       dao.SubType,
		Result:        model.EventResult_(dao.Result),
		OperationTime: dao.OperationTime,
		Payload:       dao.Payload,
		TenantID:      dao.TenantID,
		UserID:        dao.UserID,
	}
}

type TaskBasicPayload struct {
	LogID                  string  `json:"log_id,omitempty"`
	TaskName               *string `json:"task_name,omitempty"`
	TaskType               *string `json:"task_type,omitempty"`
	ParentTaskID           *string `json:"parent_task_id,omitempty"`
	TaskSubType            *string `json:"task_sub_type,omitempty"`
	SrcType                *string `json:"src_ds_type,omitempty"`
	DestType               *string `json:"dest_ds_type,omitempty"`
	EnableMetaMigration    *bool   `json:"meta_migration_enabled,omitempty"`
	EnableFullMigration    *bool   `json:"full_migration_enabled,omitempty"`
	EnableIncrMigration    *bool   `json:"incr_migration_enabled,omitempty"`
	EnableAccountMigration *bool   `json:"account_migration_enabled,omitempty"`
}

type TaskUpdatePayload struct {
	OldTaskPayload *TaskBasicPayload `json:"old_task_payload,omitempty"`
	NewTaskPayload *TaskBasicPayload `json:"new_task_payload,omitempty"`
}

type PrecheckPayload struct {
	*TaskBasicPayload
	PreCheckID    string   `json:"precheck_id,omitempty"`
	PreCheckItems []string `json:"precheck_items,omitempty"`
}

type ConsumeGroupPayload struct {
	LogID            string `json:"log_id,omitempty"`
	TaskID           string `json:"task_id,omitempty"`
	QueueID          uint64 `json:"queue_id,omitempty"`
	Topic            string `json:"consume_topic,omitempty"`
	GroupName        string `json:"consume_group_name,omitempty"`
	GroupDescription string `json:"group_description,omitempty"`
	UserName         string `json:"user_name,omitempty"`
	UserPassword     string `json:"user_password,omitempty"`
}

type UpdateConsumeGroupPayload struct {
	OldConsumeGroupPayload *ConsumeGroupPayload
	NewConsumeGroupPayload *ConsumeGroupPayload
}

type TaskBillingPayload struct {
	OrderID string  `json:"order_id,omitempty"`
	Err     *string `json:"err,omitempty"`
}

type BillingEventPayload struct {
	Msg *volctrade.TradeMessage
	Err *string `json:"err,omitempty"`
}
