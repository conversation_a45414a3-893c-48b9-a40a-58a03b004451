package entity

import "code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

const (
)
var (
	DefaultSlowLogInspectUnit = &model.InspectionUnit{
		InspectionMetric: model.InspectionMetric_SlowLog,
		ValueType:        model.ValueTypePtr(model.ValueType_Max),
		DownSampleType:   model.DownSampleTypePtr(model.DownSampleType_Avg),
		InspectLevel:     model.InspectLevelPtr(model.InspectLevel_InspectLevelTwo),
	}
	DefaultCpuUsageInspectUnit = &model.InspectionUnit{
		InspectionMetric: model.InspectionMetric_CpuUsage,
		ValueType:        model.ValueTypePtr(model.ValueType_Max),
		DownSampleType:   model.DownSampleTypePtr(model.DownSampleType_Avg),
		InspectLevel:     model.InspectLevelPtr(model.InspectLevel_InspectLevelTwo),
	}
	DefaultMemUsageInspectUnit = &model.InspectionUnit{
		InspectionMetric: model.InspectionMetric_MemUsage,
		ValueType:        model.ValueTypePtr(model.ValueType_Max),
		DownSampleType:   model.DownSampleTypePtr(model.DownSampleType_Avg),
		InspectLevel:     model.InspectLevelPtr(model.InspectLevel_InspectLevelTwo),
	}
	DefaultSpaceUsageInspectUnit = &model.InspectionUnit{
		InspectionMetric: model.InspectionMetric_SpaceUsage,
		ValueType:        model.ValueTypePtr(model.ValueType_Max),
		DownSampleType:   model.DownSampleTypePtr(model.DownSampleType_Avg),
		InspectLevel:     model.InspectLevelPtr(model.InspectLevel_InspectLevelTwo),
	}
	DefaultSessionUsageInspectUnit = &model.InspectionUnit{
		InspectionMetric: model.InspectionMetric_SessionUsage,
		ValueType:        model.ValueTypePtr(model.ValueType_Max),
		DownSampleType:   model.DownSampleTypePtr(model.DownSampleType_Avg),
		InspectLevel:     model.InspectLevelPtr(model.InspectLevel_InspectLevelTwo),
	}

	DefaultInspectionConfig = map[model.InstanceType]*model.InspectionConfig{
		model.InstanceType_MySQL: {
			InstanceType: model.InstanceType_MySQL,
			InspectionUnits: []*model.InspectionUnit{
				DefaultSlowLogInspectUnit,
				DefaultCpuUsageInspectUnit,
				DefaultMemUsageInspectUnit,
				DefaultSpaceUsageInspectUnit,
				DefaultSessionUsageInspectUnit,
			},
		},
		model.InstanceType_VeDBMySQL: {
			InstanceType: model.InstanceType_VeDBMySQL,
			InspectionUnits: []*model.InspectionUnit{
				DefaultSlowLogInspectUnit,
				DefaultCpuUsageInspectUnit,
				DefaultMemUsageInspectUnit,
				DefaultSpaceUsageInspectUnit,
				DefaultSessionUsageInspectUnit,
			},
		},
		model.InstanceType_Postgres: {
			InstanceType: model.InstanceType_Postgres,
			InspectionUnits: []*model.InspectionUnit{
				DefaultSlowLogInspectUnit,
				DefaultCpuUsageInspectUnit,
				DefaultMemUsageInspectUnit,
				DefaultSpaceUsageInspectUnit,
				DefaultSessionUsageInspectUnit,
			},
		},
		model.InstanceType_Redis: {
			InstanceType: model.InstanceType_Postgres,
			InspectionUnits: []*model.InspectionUnit{
				DefaultCpuUsageInspectUnit,
				DefaultMemUsageInspectUnit,
				DefaultSessionUsageInspectUnit,
			},
		},
	}

)