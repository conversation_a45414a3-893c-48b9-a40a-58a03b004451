package entity

import "github.com/qjpcpu/fp"

type CommandSet struct {
	ID           string
	SessionID    string
	ConnectionID string
	CreateTimeMS int64
	StartTimeMS  int64
	EndTimeMS    int64
	Progress     int64
	Content      string
	Commands     []*Command
	TenantID     string
}

func (cs *CommandSet) GetSessionId() string    { return cs.SessionID }
func (cs *CommandSet) GetConnectionId() string { return cs.ConnectionID }

func (cs *CommandSet) CancelNonTerminated(now int64, why string) *CommandSet {
	if cs.EndTimeMS == 0 {
		cs.EndTimeMS = now
	}
	for _, cmd := range cs.Commands {
		if state := cmd.State; state != CommandTerminated {
			cmd.State = CommandTerminated
			cmd.Reason = CommandTerminatedReasonPtr(CommandTerminated_Cancel)
			cmd.ReasonDetail = why
			if state == CommandExecuting && cmd.EndTimeMS == 0 {
				cmd.EndTimeMS = now
			}
		}
	}
	return cs
}

func (cs *CommandSet) MarkCommandFail(cmdId string, now int64, why string) *CommandSet {
	for _, cmd := range cs.Commands {
		if cmd.ID == cmdId {
			cmd.State = CommandTerminated
			cmd.Reason = CommandTerminatedReasonPtr(CommandTerminated_Failed)
			cmd.ReasonDetail = why
			if cmd.EndTimeMS == 0 {
				cmd.EndTimeMS = now
			}
		}
	}
	return cs
}

func (cs *CommandSet) GetCommand(cmdId string) *Command {
	for _, cmd := range cs.Commands {
		if cmd.ID == cmdId {
			return cmd
		}
	}
	return nil
}

func (cs *CommandSet) MarkCommandSuccess(cmdId string, now int64) *CommandSet {
	for _, cmd := range cs.Commands {
		if cmd.ID == cmdId && cmd.State == CommandExecuting {
			cmd.State = CommandTerminated
			cmd.Reason = CommandTerminatedReasonPtr(CommandTerminated_Success)
			cmd.ReasonDetail = ""
			if cmd.EndTimeMS == 0 {
				cmd.EndTimeMS = now
			}
		}
	}
	return cs
}

func (cs *CommandSet) MarkCommandExecuting(cmdId string, now int64) *CommandSet {
	if cs.StartTimeMS == 0 {
		cs.StartTimeMS = now
	}
	for _, cmd := range cs.Commands {
		if cmd.ID == cmdId {
			cmd.State = CommandExecuting
			cmd.Reason = nil
			cmd.ReasonDetail = ""
			cmd.StartTimeMS = now
		}
	}
	return cs
}

func (cs *CommandSet) UpdateProgress(now int64) *CommandSet {
	if len(cs.Commands) == 0 {
		cs.Progress = 100
		return cs
	}
	finished := fp.StreamOf(cs.Commands).Filter(func(c *Command) bool { return c.State == CommandTerminated }).Count()
	cs.Progress = int64(finished * 100 / len(cs.Commands))
	if cs.Progress == 0 && fp.StreamOf(cs.Commands).Filter(func(c *Command) bool { return c.StartTimeMS > 0 }).Count() > 0 {
		cs.Progress = 1
	}
	if cs.Progress == 100 && cs.EndTimeMS == 0 {
		cs.EndTimeMS = now
	}
	return cs
}

func (cs *CommandSet) NextNonTerminatedCommand() *Command {
	for _, cmd := range cs.Commands {
		if cmd.State != CommandTerminated {
			return cmd
		}
	}
	return nil
}

func (cs *CommandSet) SetCommandResultMeta(cmdId string, typ ResultType, header []string) *CommandSet {
	for _, cmd := range cs.Commands {
		if cmd.ID == cmdId {
			cmd.ResultType = &typ
			cmd.Header = header
		}
	}
	return cs
}

func (cs *CommandSet) SetCommandConnCurrentDB(cmdID string, db string) {
	for _, cmd := range cs.Commands {
		if cmd.ID == cmdID {
			if cmd.Extra == nil {
				cmd.Extra = map[string]interface{}{}
			}
			cmd.Extra["CurrentDB"] = db
		}
	}
}

func (cs *CommandSet) IsFinished() bool {
	return cs.Progress == 100 || cs.EndTimeMS != 0
}
