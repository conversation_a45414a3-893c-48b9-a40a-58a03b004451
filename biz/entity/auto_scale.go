package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"github.com/qjpcpu/fp"
)

// AutoScaleRule 带宽自动扩缩容表
type AutoScaleRule struct {
	RuleId            int64
	InstanceId        string
	InstanceType      string
	ScaleTarget       string
	TenantId          string
	RegionId          string
	ScalingType       int8
	ScalingThreshold  float64
	Enable            int8
	ObservationWindow int32
	CloudAlarmId      string
	CreateTime        int64
	UpdateTime        int64
	UpdateUser        string
	Deleted           int8
	ScalingLimit      string
}

func ScaleRulesDaoToEntity(t []*dao.DbwAutoScaleRule) []*AutoScaleRule {
	var res = make([]*AutoScaleRule, 0, len(t))
	_ = fp.StreamOf(t).Map(func(item *dao.DbwAutoScaleRule) *AutoScaleRule {
		return ScaleRuleDaoToEntity(item)
	}).ToSlice(&res)
	return res
}

func ScaleRuleDaoToEntity(t *dao.DbwAutoScaleRule) *AutoScaleRule {
	return &AutoScaleRule{
		RuleId:            t.RuleId,
		InstanceId:        t.InstanceId,
		InstanceType:      t.InstanceType,
		ScaleTarget:       t.ScaleTarget,
		TenantId:          t.TenantId,
		RegionId:          t.RegionId,
		ScalingType:       t.ScalingType,
		ScalingThreshold:  t.ScalingThreshold,
		Enable:            t.Enable,
		ObservationWindow: t.ObservationWindow,
		CloudAlarmId:      t.CloudAlarmId,
		CreateTime:        t.CreateTime,
		UpdateTime:        t.UpdateTime,
		UpdateUser:        t.UpdateUser,
		Deleted:           t.Deleted,
		ScalingLimit:      t.ScalingLimit,
	}
}

func (t *AutoScaleRule) ToDao() *dao.DbwAutoScaleRule {
	autoScaleRules := &dao.DbwAutoScaleRule{
		RuleId:            t.RuleId,
		InstanceId:        t.InstanceId,
		InstanceType:      t.InstanceType,
		ScaleTarget:       t.ScaleTarget,
		TenantId:          t.TenantId,
		RegionId:          t.RegionId,
		ScalingType:       t.ScalingType,
		ScalingThreshold:  t.ScalingThreshold,
		Enable:            t.Enable,
		ObservationWindow: t.ObservationWindow,
		CloudAlarmId:      t.CloudAlarmId,
		CreateTime:        t.CreateTime,
		UpdateTime:        t.UpdateTime,
		UpdateUser:        t.UpdateUser,
		Deleted:           t.Deleted,
		ScalingLimit:      t.ScalingLimit,
	}
	return autoScaleRules
}

// AutoScaleEvent 带宽扩缩容记录表
type AutoScaleEvent struct {
	EventId           int64
	RuleId            int64
	InstanceId        string
	TenantId          string
	ScalingType       int8
	CreateTime        int64
	StartTime         int64
	EndTime           int64
	BeforeValue       int32
	TargetUtilization string
	AfterValue        int32
	Status            int8
	Memo              string
	Deleted           int8
	BeforeMetric      string
	AfterMetric       string
}

func ScaleEventsDaoToEntity(t []*dao.DbwAutoScaleEvent) []*AutoScaleEvent {
	var res = make([]*AutoScaleEvent, 0, len(t))
	_ = fp.StreamOf(t).Map(func(item *dao.DbwAutoScaleEvent) *AutoScaleEvent {
		return ScaleEventDaoToEntity(item)
	}).ToSlice(&res)
	return res
}

func ScaleEventDaoToEntity(t *dao.DbwAutoScaleEvent) *AutoScaleEvent {
	return &AutoScaleEvent{
		EventId:           t.EventId,
		RuleId:            t.RuleId,
		InstanceId:        t.InstanceId,
		TenantId:          t.TenantId,
		ScalingType:       t.ScalingType,
		CreateTime:        t.CreateTime,
		StartTime:         t.StartTime,
		EndTime:           t.EndTime,
		BeforeValue:       t.BeforeValue,
		TargetUtilization: t.TargetUtilization,
		AfterValue:        t.AfterValue,
		Status:            t.Status,
		Memo:              t.Memo,
		Deleted:           t.Deleted,
		BeforeMetric:      t.BeforeMetric,
		AfterMetric:       t.AfterMetric,
	}
}

func (t *AutoScaleEvent) ToDao() *dao.DbwAutoScaleEvent {
	autoScaleRules := &dao.DbwAutoScaleEvent{
		EventId:           t.EventId,
		RuleId:            t.RuleId,
		InstanceId:        t.InstanceId,
		TenantId:          t.TenantId,
		ScalingType:       t.ScalingType,
		CreateTime:        t.CreateTime,
		StartTime:         t.StartTime,
		EndTime:           t.EndTime,
		BeforeValue:       t.BeforeValue,
		TargetUtilization: t.TargetUtilization,
		AfterValue:        t.AfterValue,
		Status:            t.Status,
		Memo:              t.Memo,
		Deleted:           t.Deleted,
		BeforeMetric:      t.BeforeMetric,
		AfterMetric:       t.AfterMetric,
	}
	return autoScaleRules
}

type AutoScaleEventsInfo struct {
	Total           int64
	AutoScaleEvents []*AutoScaleEvent
}
