package entity

type ClusterInfo struct {
	ClusterName string
	NodePool    string
	Namespace   string
	Storage     *StorageConfig
}

func (self *ClusterInfo) GetClusterName() string {
	return self.ClusterName
}

func (self *ClusterInfo) GetNodePool() string {
	return self.NodePool
}

func (self *ClusterInfo) GetNamespace() string {

	return self.Namespace
}

type StorageConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	DB       string
}
