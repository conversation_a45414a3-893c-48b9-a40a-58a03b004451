package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"github.com/qjpcpu/fp"
)

// 巡检任务表

type InspectionTask struct {
	ID                    int64
	InstanceType          string
	InstanceId            string
	InstanceName          string
	TenantId              string
	InspectionStatus      model.InspectionStatus
	InspectionType        model.InspectionType
	InspectionStartTime   int64
	InspectionEndTime     int64
	InspectionExecuteTime int64
	HealthScore           int8
	CpuUsage              float64
	MemUsage              float64
	DiskUsage             float64
	ConnUsage             float64
	Qps                   float64
	Tps                   float64
	SlowLog               int32
	CreatedAt             int64
	UpdatedAt             int64
	Deleted               int8
	Memo                  string
	RegionId              string
	NodeId                *string	
	LastOneNodeAgg        string
}

func InspectionTasksDaoToEntity(t []*dao.InspectionTask) []*InspectionTask {
	var res = make([]*InspectionTask, 0, len(t))
	fp.StreamOf(t).Map(func(item *dao.InspectionTask) *InspectionTask {
		return InspectionTaskDaoToEntity(item)
	}).ToSlice(&res)
	return res
}

func InspectionTaskDaoToEntity(t *dao.InspectionTask) *InspectionTask {
	one := &InspectionTask{
		ID:                    t.TaskID,
		InstanceType:          t.InstanceType,
		InstanceId:            t.InstanceId,
		InstanceName:          t.InstanceName,
		TenantId:              t.TenantId,
		InspectionStatus:      model.InspectionStatus(t.TaskStatus),
		InspectionType:        model.InspectionType(t.TaskType),
		InspectionStartTime:   t.InspectionStartTime,
		InspectionEndTime:     t.InspectionEndTime,
		InspectionExecuteTime: t.InspectionExecuteTime,
		HealthScore:           t.HealthScore,
		CpuUsage:              t.CpuUsage,
		MemUsage:              t.MemUsage,
		DiskUsage:             t.DiskUsage,
		ConnUsage:             t.ConnUsage,
		Qps:                   t.Qps,
		Tps:                   t.Tps,
		SlowLog:               t.SlowLog,
		CreatedAt:             t.CreatedAt,
		UpdatedAt:             t.UpdatedAt,
		Deleted:               t.Deleted,
		Memo:                  t.Memo,
		RegionId:              t.RegionId,
		LastOneNodeAgg:        t.LastOneNodeAgg,
	}
	return one
}

func (t *InspectionTask) ToDao() *dao.InspectionTask {
	inspectionTask := &dao.InspectionTask{
		TaskID:                t.ID,
		InstanceType:          t.InstanceType,
		InstanceId:            t.InstanceId,
		RegionId:              t.RegionId,
		InstanceName:          t.InstanceName,
		TenantId:              t.TenantId,
		TaskStatus:            int8(t.InspectionStatus),
		TaskType:              int8(t.InspectionType),
		InspectionStartTime:   t.InspectionStartTime,
		InspectionEndTime:     t.InspectionEndTime,
		InspectionExecuteTime: t.InspectionExecuteTime,
		HealthScore:           t.HealthScore,
		CpuUsage:              t.CpuUsage,
		MemUsage:              t.MemUsage,
		DiskUsage:             t.DiskUsage,
		ConnUsage:             t.ConnUsage,
		Qps:                   t.Qps,
		Tps:                   t.Tps,
		SlowLog:               t.SlowLog,
		CreatedAt:             t.CreatedAt,
		UpdatedAt:             t.UpdatedAt,
		Deleted:               t.Deleted,
		Memo:                  t.Memo,
		LastOneNodeAgg:        t.LastOneNodeAgg,
	}
	return inspectionTask
}

type InspectionTasksInfo struct {
	Total           int64
	InspectionTasks []*InspectionTask
}

// 巡检配置表

type InspectionConfig struct {
	InstanceId                    string
	InstanceType                  string
	InstanceName                  string
	RegionId                      string
	IsOpen                        int8
	InspectionStartTime           int64
	InspectionEndTime             int64
	InspectionExecutableStartTime int64
	InspectionExecutableEndTime   int64
	LastCreateTaskTime            int64
	TenantId                      string
}

func InspectionConfigDaoToEntity(t *dao.InspectionConfig) *InspectionConfig {
	return &InspectionConfig{
		InstanceId:                    t.InstanceId,
		InstanceType:                  t.InstanceType,
		InstanceName:                  t.InstanceName,
		IsOpen:                        t.IsOpen,
		InspectionStartTime:           t.InspectionStartTime,
		InspectionEndTime:             t.InspectionEndTime,
		InspectionExecutableStartTime: t.InspectionExecutableStartTime,
		InspectionExecutableEndTime:   t.InspectionExecutableEndTime,
		RegionId:                      t.RegionId,
		TenantId:                      t.TenantId,
	}
}

func (t *InspectionConfig) ToDao() *dao.InspectionConfig {
	return &dao.InspectionConfig{
		InstanceId:                    t.InstanceId,
		InstanceType:                  t.InstanceType,
		InstanceName:                  t.InstanceName,
		IsOpen:                        t.IsOpen,
		InspectionStartTime:           t.InspectionStartTime,
		InspectionEndTime:             t.InspectionEndTime,
		InspectionExecutableStartTime: t.InspectionExecutableStartTime,
		InspectionExecutableEndTime:   t.InspectionExecutableEndTime,
		RegionId:                      t.RegionId,
		TenantId:                      t.TenantId,
	}
}

type InspectionConfigsInfo struct {
	Total             int64
	InspectionConfigs []*InspectionConfig
}

// 巡检参数配置表
type InspectionParamConfig struct {
	ID                    int64
	RegionId              string
	TenantId              string
	InstanceType          string
	InstanceId            string // empty if default
	NodeId 			      string // empty if instance
	Config 				  string // json
	CreatedAt             int64
	UpdatedAt             int64
	Deleted               int8
}

func InspectionParamConfigDaoToEntity(t *dao.InspectionParamConfig) *InspectionParamConfig {
	return &InspectionParamConfig{
		ID:                    t.ID,
		RegionId:              t.RegionId,
		InstanceType:          t.InstanceType,
		InstanceId:            t.InstanceId,
		NodeId:                t.NodeId,
		Config:                t.Config,
		CreatedAt:             t.CreatedAt,
		UpdatedAt:             t.UpdatedAt,
		Deleted:               t.Deleted,
	}
}

func (t *InspectionParamConfig) ToDao() *dao.InspectionParamConfig {
	return &dao.InspectionParamConfig{
		ID:                    t.ID,
		RegionId:              t.RegionId,
		InstanceType:         t.InstanceType,
		InstanceId:            t.InstanceId,
		NodeId:                t.NodeId,
		Config:                t.Config,
		CreatedAt:             t.CreatedAt,
		UpdatedAt:             t.UpdatedAt,
		Deleted:               t.Deleted,
	}
}

type InspectionReport struct {
	TaskID              int64
	InstanceType        string
	InstanceId          string
	InstanceSpec        string
	InstanceVersion     string
	InspectionStartTime int64
	InspectionEndTime   int64
	MaxCpuUsage         float64
	MinCpuUsage         float64
	AvgCpuUsage         float64
	MaxMemUsage         float64
	MinMemUsage         float64
	AvgMemUsage         float64
	MaxDiskUsage        float64
	MinDiskUsage        float64
	AvgDiskUsage        float64
	MaxConnUsage        float64
	MinConnUsage        float64
	AvgConnUsage        float64
	MaxConnected        int64
	ThreadConnected     int64
	ThreadRunning       int64
}

func InspectionReportDaoToEntity(t *dao.InspectionReport) *InspectionReport {
	return &InspectionReport{
		TaskID:              t.TaskID,
		InstanceType:        t.InstanceType,
		InstanceId:          t.InstanceId,
		InstanceSpec:        t.InstanceSpec,
		InstanceVersion:     t.InstanceVersion,
		InspectionStartTime: t.InspectionStartTime,
		InspectionEndTime:   t.InspectionEndTime,
		MaxCpuUsage:         t.MaxCpuUsage,
		MinCpuUsage:         t.MinCpuUsage,
		AvgCpuUsage:         t.AvgCpuUsage,
		MaxMemUsage:         t.MaxMemUsage,
		MinMemUsage:         t.MinMemUsage,
		AvgMemUsage:         t.AvgMemUsage,
		MaxDiskUsage:        t.MaxDiskUsage,
		MinDiskUsage:        t.MinDiskUsage,
		AvgDiskUsage:        t.AvgDiskUsage,
		MaxConnUsage:        t.MaxConnUsage,
		MinConnUsage:        t.MinConnUsage,
		AvgConnUsage:        t.AvgConnUsage,
		MaxConnected:        t.MaxConnected,
		ThreadConnected:     t.ThreadConnected,
		ThreadRunning:       t.ThreadRunning,
	}
}

func (t *InspectionReport) ToDao() *dao.InspectionReport {
	return &dao.InspectionReport{
		TaskID:              t.TaskID,
		InstanceType:        t.InstanceType,
		InstanceId:          t.InstanceId,
		InstanceSpec:        t.InstanceSpec,
		InstanceVersion:     t.InstanceVersion,
		InspectionStartTime: t.InspectionStartTime,
		InspectionEndTime:   t.InspectionEndTime,
		MaxCpuUsage:         t.MaxCpuUsage,
		MinCpuUsage:         t.MinCpuUsage,
		AvgCpuUsage:         t.AvgCpuUsage,
		MaxMemUsage:         t.MaxMemUsage,
		MinMemUsage:         t.MinMemUsage,
		AvgMemUsage:         t.AvgMemUsage,
		MaxDiskUsage:        t.MaxDiskUsage,
		MinDiskUsage:        t.MinDiskUsage,
		AvgDiskUsage:        t.AvgDiskUsage,
		MaxConnUsage:        t.MaxConnUsage,
		MinConnUsage:        t.MinConnUsage,
		AvgConnUsage:        t.AvgConnUsage,
		MaxConnected:        t.MaxConnected,
		ThreadConnected:     t.ThreadConnected,
		ThreadRunning:       t.ThreadRunning,
	}
}

type InspectionSlowLog struct {
	ID                  int64
	NumberId            int32
	InstanceId          string
	InstanceName        string
	InspectionStartTime int64
	InspectionEndTime   int64
	SqlTemplate         string
	DBName              string
	ExecuteUser         string
	ExecuteCount        int64
	TotalQueryTime      float64
	AvgQueryTime        float64
	AvgLockTime         float64
	AvgRowsSent         float64
}

func InspectionSlowLogDaoToEntity(t []*dao.InspectionSlowLog) []*InspectionSlowLog {
	var result []*InspectionSlowLog
	if t == nil {
		return result
	}
	for _, value := range t {
		slowLog := &InspectionSlowLog{
			ID:                  value.ID,
			NumberId:            value.NumberId,
			InstanceId:          value.InstanceId,
			InstanceName:        value.InstanceName,
			InspectionStartTime: value.InspectionStartTime,
			InspectionEndTime:   value.InspectionEndTime,
			SqlTemplate:         value.SqlTemplate,
			DBName:              value.DBName,
			ExecuteUser:         value.ExecuteUser,
			ExecuteCount:        value.ExecuteCount,
			TotalQueryTime:      value.TotalQueryTime,
			AvgQueryTime:        value.AvgQueryTime,
			AvgLockTime:         value.AvgLockTime,
			AvgRowsSent:         value.AvgRowsSent,
		}
		result = append(result, slowLog)
	}
	return result
}

func (t *InspectionSlowLog) ToDao() *dao.InspectionSlowLog {
	return &dao.InspectionSlowLog{
		ID:                  t.ID,
		NumberId:            t.NumberId,
		InstanceId:          t.InstanceId,
		InstanceName:        t.InstanceName,
		InspectionStartTime: t.InspectionStartTime,
		InspectionEndTime:   t.InspectionEndTime,
		SqlTemplate:         t.SqlTemplate,
		DBName:              t.DBName,
		ExecuteUser:         t.ExecuteUser,
		ExecuteCount:        t.ExecuteCount,
		TotalQueryTime:      t.TotalQueryTime,
		AvgQueryTime:        t.AvgQueryTime,
		AvgLockTime:         t.AvgLockTime,
		AvgRowsSent:         t.AvgRowsSent,
	}
}
