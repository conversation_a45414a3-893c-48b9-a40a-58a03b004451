package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"encoding/json"
	"fmt"
	"github.com/qjpcpu/fp"
	"regexp"
	"strconv"
)

const (
	FlowStatusApproving = 1
	FlowStatusFinished  = 2
	FlowStatusReject    = 3
	FlowStatusCancel    = 4

	System    = 0
	Customize = 1

	TicketFlowType = "Ticket"

	SystemDBANode   = "DBA"
	SystemOwnerNode = "Owner"
	SystemAdminNode = "Admin"
)

type ApprovalNode struct {
	NodeId         int64
	NodeType       int8
	NodeName       string
	ApproverIds    string // ,隔开
	Approvers      string
	TenantId       string
	Memo           string
	CreateUser     string
	CreateUserName string
	ModifyUser     string
	CreateTime     int64
	ModifyTime     int64
	Deleted        int8
}

type ApprovalFlowConfig struct {
	ConfigId              int64
	ConfigName            string
	ConfigType            int8
	FlowScenes            []*ApprovalFlowScenes
	TenantId              string
	CreateUserId          string
	CreateUserName        string
	ModifyUserId          string
	CreateTime            int64
	ModifyTime            int64
	Memo                  string
	AssociatedInstanceNum int64
}

type ApprovalFlowScenes struct {
	TemplateId int64
	ScenesType string
}

type ApprovalFlowTemplate struct {
	TemplateId   int64
	TotalStep    int64
	FlowNodes    []*ApprovalNode
	CreateTime   int64
	ModifyTime   int64
	CreateUserId string
	ModifyUserId string
	TenantId     string
	Deleted      int8

	NoNeedForApproval    bool // 是否开启全节点免审批
	NotAllowSelfApproval bool // 是否允许提交人为自己审批
	SelfAutoApproval     bool // 是否允许审批人为提交人自己时自动审批
}

type ApprovalFlow struct {
	FlowId         int64
	InstanceId     string
	TenantId       string
	FlowTemplateId int64
	Status         int8
	Step           int8
	ApproverNodeId int64
}

type ApprovalFlowHistory struct {
	Id             int64
	FlowTemplateId int64
	FlowId         int64
	Step           int8
	OperatorId     string
	OperateType    int8
	CreateTime     int64
}

func ApprovalFlowNodesToEntity(nodes []*dao.ApprovalNode) []*ApprovalNode {
	var res []*ApprovalNode
	for _, node := range nodes {
		res = append(res, ApprovalFlowNodeDaoToEntity(node))
	}
	return res
}

func ApprovalFlowConfigsToEntity(flowConfigs []*dao.ApprovalFlowConfig) []*ApprovalFlowConfig {
	var res []*ApprovalFlowConfig
	for _, flowConfig := range flowConfigs {
		res = append(res, ApprovalFlowConfigDaoToEntity(flowConfig))
	}
	return res
}

func ApprovalFlowHistoriesToEntity(flowHistories []*dao.ApprovalFlowHistory) []*ApprovalFlowHistory {
	var res []*ApprovalFlowHistory
	for _, history := range flowHistories {
		res = append(res, ApprovalFlowHistoryDaoToEntity(history))
	}
	return res
}

func ApprovalFlowNodeDaoToEntity(node *dao.ApprovalNode) *ApprovalNode {
	return &ApprovalNode{
		NodeId:         node.NodeId,
		NodeType:       node.NodeType,
		NodeName:       node.NodeName,
		ApproverIds:    node.ApproverIds,
		Approvers:      node.Approvers,
		TenantId:       node.TenantId,
		Memo:           node.Memo,
		CreateUser:     node.CreateUser,
		CreateUserName: node.CreateUserName,
		ModifyUser:     node.ModifyUser,
		CreateTime:     node.CreateTime,
		ModifyTime:     node.ModifyTime,
		Deleted:        node.Deleted,
	}
}

func ApprovalFlowConfigDaoToEntity(flowConfig *dao.ApprovalFlowConfig) *ApprovalFlowConfig {
	if flowConfig == nil {
		return &ApprovalFlowConfig{}
	}
	var scenes []*ApprovalFlowScenes
	_ = json.Unmarshal([]byte(flowConfig.FlowScenes), &scenes)
	return &ApprovalFlowConfig{
		ConfigId:       flowConfig.ConfigId,
		ConfigName:     flowConfig.ConfigName,
		ConfigType:     flowConfig.ConfigType,
		FlowScenes:     scenes,
		TenantId:       flowConfig.TenantId,
		CreateUserId:   flowConfig.CreateUserId,
		ModifyUserId:   flowConfig.ModifyUserId,
		CreateTime:     flowConfig.CreateTime,
		CreateUserName: flowConfig.CreateUserName,
		ModifyTime:     flowConfig.ModifyTime,
		Memo:           flowConfig.Memo,
	}
}

func ApprovalFlowHistoryDaoToEntity(flowHistory *dao.ApprovalFlowHistory) *ApprovalFlowHistory {
	return &ApprovalFlowHistory{
		Id:             flowHistory.Id,
		FlowTemplateId: flowHistory.FlowTemplateId,
		FlowId:         flowHistory.FlowId,
		Step:           flowHistory.Step,
		OperatorId:     flowHistory.OperatorId,
		OperateType:    flowHistory.OperateType,
		CreateTime:     flowHistory.CreateTime,
	}
}

func ApprovalFlowDaoToEntity(flow *dao.ApprovalFlow) *ApprovalFlow {
	return &ApprovalFlow{
		FlowId:         flow.FlowId,
		InstanceId:     flow.InstanceId,
		TenantId:       flow.TenantId,
		FlowTemplateId: flow.FlowTemplateId,
		Status:         flow.Status,
		Step:           flow.Step,
		ApproverNodeId: flow.ApproverNodeId,
	}
}

func ApprovalFlowTemplateDaoToEntity(template *dao.ApprovalFlowTemplate) *ApprovalFlowTemplate {
	var Nodes []*ApprovalNode
	spaceRe, _ := regexp.Compile(`\s*,\s*`)
	idList := spaceRe.Split(template.FlowNodes, -1)
	for _, idStr := range idList {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		Nodes = append(Nodes, &ApprovalNode{NodeId: id})
	}
	return &ApprovalFlowTemplate{
		TemplateId:   template.TemplateId,
		TotalStep:    template.TotalStep,
		FlowNodes:    Nodes,
		CreateTime:   template.CreateTime,
		ModifyTime:   template.ModifyTime,
		CreateUserId: template.CreateUserId,
		ModifyUserId: template.ModifyUserId,
		TenantId:     template.TenantId,
		Deleted:      template.Deleted,

		NoNeedForApproval:    template.NoNeedForApproval == 1,
		NotAllowSelfApproval: template.NotAllowSelfApproval == 1,
		SelfAutoApproval:     template.SelfAutoApproval == 1,
	}
}

func (t *ApprovalFlowTemplate) ToDao() *dao.ApprovalFlowTemplate {
	nodeIds := fp.StreamOf(t.FlowNodes).Map(func(node *ApprovalNode) string {
		return fmt.Sprintf("%d", node.NodeId)
	}).JoinStrings(",")
	return &dao.ApprovalFlowTemplate{
		TemplateId:   t.TemplateId,
		TotalStep:    t.TotalStep,
		FlowNodes:    nodeIds,
		CreateTime:   t.CreateTime,
		ModifyTime:   t.ModifyTime,
		CreateUserId: t.CreateUserId,
		ModifyUserId: t.ModifyUserId,
		TenantId:     t.TenantId,
		Deleted:      t.Deleted,

		NoNeedForApproval:    boolToInt(t.NoNeedForApproval),
		NotAllowSelfApproval: boolToInt(t.NotAllowSelfApproval),
		SelfAutoApproval:     boolToInt(t.SelfAutoApproval),
	}
}

func (t *ApprovalFlow) ToDao() *dao.ApprovalFlow {
	return &dao.ApprovalFlow{
		FlowId:         t.FlowId,
		InstanceId:     t.InstanceId,
		TenantId:       t.TenantId,
		FlowTemplateId: t.FlowTemplateId,
		Status:         t.Status,
		Step:           t.Step,
		ApproverNodeId: t.ApproverNodeId,
	}
}

func (t *ApprovalFlowHistory) ToDao() *dao.ApprovalFlowHistory {
	return &dao.ApprovalFlowHistory{
		Id:             t.Id,
		FlowTemplateId: t.FlowTemplateId,
		FlowId:         t.FlowId,
		Step:           t.Step,
		OperatorId:     t.OperatorId,
		OperateType:    t.OperateType,
		CreateTime:     t.CreateTime,
	}
}

func (t *ApprovalNode) ToDao() *dao.ApprovalNode {
	return &dao.ApprovalNode{
		NodeId:         t.NodeId,
		NodeType:       t.NodeType,
		NodeName:       t.NodeName,
		ApproverIds:    t.ApproverIds,
		Approvers:      t.Approvers,
		TenantId:       t.TenantId,
		Memo:           t.Memo,
		CreateUser:     t.CreateUser,
		ModifyUser:     t.ModifyUser,
		CreateTime:     t.CreateTime,
		CreateUserName: t.CreateUserName,
		ModifyTime:     t.ModifyTime,
		Deleted:        t.Deleted,
	}
}

func (t *ApprovalFlowConfig) ToDao() *dao.ApprovalFlowConfig {
	flowScenesJson, _ := json.Marshal(t.FlowScenes)
	return &dao.ApprovalFlowConfig{
		ConfigId:       t.ConfigId,
		ConfigName:     t.ConfigName,
		ConfigType:     t.ConfigType,
		FlowScenes:     string(flowScenesJson),
		TenantId:       t.TenantId,
		CreateUserId:   t.CreateUserId,
		ModifyUserId:   t.ModifyUserId,
		CreateTime:     t.CreateTime,
		CreateUserName: t.CreateUserName,
		ModifyTime:     t.ModifyTime,
		Memo:           t.Memo,
	}
}
