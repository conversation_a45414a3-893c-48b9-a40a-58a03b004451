package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"reflect"
	"testing"
)

func TestAutoScaleEvent_ToDao(t1 *testing.T) {
	type fields struct {
		EventId           int64
		RuleId            int64
		InstanceId        string
		TenantId          string
		ScalingType       int8
		CreateTime        int64
		StartTime         int64
		EndTime           int64
		BeforeValue       int32
		TargetUtilization string
		AfterValue        int32
		Status            int8
		Memo              string
		Deleted           int8
	}
	tests := []struct {
		name   string
		fields fields
		want   *dao.DbwAutoScaleEvent
	}{
		{name: "test1", fields: fields{EventId: 1}, want: &dao.DbwAutoScaleEvent{EventId: 1}},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &AutoScaleEvent{
				EventId:           tt.fields.EventId,
				RuleId:            tt.fields.RuleId,
				InstanceId:        tt.fields.InstanceId,
				TenantId:          tt.fields.TenantId,
				ScalingType:       tt.fields.ScalingType,
				CreateTime:        tt.fields.CreateTime,
				StartTime:         tt.fields.StartTime,
				EndTime:           tt.fields.EndTime,
				BeforeValue:       tt.fields.BeforeValue,
				TargetUtilization: tt.fields.TargetUtilization,
				AfterValue:        tt.fields.AfterValue,
				Status:            tt.fields.Status,
				Memo:              tt.fields.Memo,
				Deleted:           tt.fields.Deleted,
			}
			if got := t.ToDao(); !reflect.DeepEqual(got.EventId, tt.want.EventId) {
				t1.Errorf("ToDao() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAutoScaleRule_ToDao(t1 *testing.T) {
	type fields struct {
		RuleId            int64
		InstanceId        string
		InstanceType      string
		ScaleTarget       string
		TenantId          string
		RegionId          string
		ScalingType       int8
		ScalingThreshold  float64
		Enable            int8
		ObservationWindow int32
		CloudAlarmId      string
		CreateTime        int64
		UpdateTime        int64
		UpdateUser        string
		Deleted           int8
	}
	tests := []struct {
		name   string
		fields fields
		want   *dao.DbwAutoScaleRule
	}{
		{name: "test1", fields: fields{RuleId: 1}, want: &dao.DbwAutoScaleRule{RuleId: 1}},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &AutoScaleRule{
				RuleId:            tt.fields.RuleId,
				InstanceId:        tt.fields.InstanceId,
				InstanceType:      tt.fields.InstanceType,
				ScaleTarget:       tt.fields.ScaleTarget,
				TenantId:          tt.fields.TenantId,
				RegionId:          tt.fields.RegionId,
				ScalingType:       tt.fields.ScalingType,
				ScalingThreshold:  tt.fields.ScalingThreshold,
				Enable:            tt.fields.Enable,
				ObservationWindow: tt.fields.ObservationWindow,
				CloudAlarmId:      tt.fields.CloudAlarmId,
				CreateTime:        tt.fields.CreateTime,
				UpdateTime:        tt.fields.UpdateTime,
				UpdateUser:        tt.fields.UpdateUser,
				Deleted:           tt.fields.Deleted,
			}
			if got := t.ToDao(); !reflect.DeepEqual(got.RuleId, tt.want.RuleId) {
				t1.Errorf("ToDao() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestScaleEventDaoToEntity(t *testing.T) {
	type args struct {
		t *dao.DbwAutoScaleEvent
	}
	tests := []struct {
		name string
		args args
		want *AutoScaleEvent
	}{
		{name: "test1", args: args{&dao.DbwAutoScaleEvent{EventId: 1}}, want: &AutoScaleEvent{EventId: 1}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ScaleEventDaoToEntity(tt.args.t); !reflect.DeepEqual(got.EventId, tt.want.EventId) {
				t.Errorf("ScaleEventDaoToEntity() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestScaleEventsDaoToEntity(t *testing.T) {
	type args struct {
		t []*dao.DbwAutoScaleEvent
	}
	tests := []struct {
		name string
		args args
		want []*AutoScaleEvent
	}{
		{name: "test1", args: args{[]*dao.DbwAutoScaleEvent{{EventId: 1}}}, want: []*AutoScaleEvent{{EventId: 1}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ScaleEventsDaoToEntity(tt.args.t); !reflect.DeepEqual(len(got), len(tt.want)) {
				t.Errorf("ScaleEventsDaoToEntity() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestScaleRuleDaoToEntity(t *testing.T) {
	type args struct {
		t *dao.DbwAutoScaleRule
	}
	tests := []struct {
		name string
		args args
		want *AutoScaleRule
	}{
		{name: "test1", args: args{&dao.DbwAutoScaleRule{RuleId: 1}}, want: &AutoScaleRule{RuleId: 1}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ScaleRuleDaoToEntity(tt.args.t); !reflect.DeepEqual(got.RuleId, tt.want.RuleId) {
				t.Errorf("ScaleRuleDaoToEntity() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestScaleRulesDaoToEntity(t *testing.T) {
	type args struct {
		t []*dao.DbwAutoScaleRule
	}
	tests := []struct {
		name string
		args args
		want []*AutoScaleRule
	}{
		{name: "test1", args: args{[]*dao.DbwAutoScaleRule{{RuleId: 1}}}, want: []*AutoScaleRule{{RuleId: 1}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ScaleRulesDaoToEntity(tt.args.t); !reflect.DeepEqual(len(got), len(tt.want)) {
				t.Errorf("ScaleRulesDaoToEntity() = %v, want %v", got, tt.want)
			}
		})
	}
}
