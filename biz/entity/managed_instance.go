package entity

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	dbwutils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"encoding/json"
)

type ManagedInstance struct {
	InstanceId         string
	InstanceName       string
	InstanceType       string
	Status             string
	TenantId           string
	Region             string
	Source             string
	CpuNum             int32
	MemInGiB           float64
	NodeNumber         int32
	ShardNumber        int32
	DatabaseUser       string
	DatabasePassword   string
	ControlMode        int32
	NoAuthMode         string
	Zone               string
	OwnerUid           string
	DbaUid             string
	SecurityGroupId    int64
	ApprovalFlowId     int64
	CreatedAt          int64
	UpdatedAt          int64
	InstanceCreateTime string
	DBEngineVersion    string
	ProjectName        string
	Tags               []*model.TagObject
	SubInstanceType    string
	StorageType        string
}

func ManagedInstanceDaoToEntity(instance *dao.DbwInstance) *ManagedInstance {
	if instance == nil {
		return nil
	}
	return &ManagedInstance{
		InstanceId:         instance.InstanceId,
		InstanceName:       instance.InstanceName,
		InstanceType:       instance.InstanceType,
		Status:             instance.Status,
		TenantId:           instance.TenantId,
		Region:             instance.Region,
		Source:             instance.Source,
		CpuNum:             int32(instance.CpuNum),
		MemInGiB:           instance.MemInGiB,
		NodeNumber:         int32(instance.NodeNumber),
		ShardNumber:        int32(instance.ShardNumber),
		ControlMode:        int32(instance.ControlMode),
		NoAuthMode:         instance.NoAuthMode,
		DatabaseUser:       instance.DatabaseUser,
		DatabasePassword:   dbwutils.DecryptData(instance.DatabasePassword, instance.InstanceId),
		Zone:               instance.Zone,
		OwnerUid:           instance.OwnerUid,
		DbaUid:             instance.DbaUid,
		SecurityGroupId:    instance.SecurityGroupId,
		ApprovalFlowId:     instance.ApprovalFlowConfigId,
		CreatedAt:          instance.CreatedAt,
		UpdatedAt:          instance.UpdatedAt,
		InstanceCreateTime: instance.InstanceCreateTime,
		DBEngineVersion:    instance.DBEngineVersion,
		ProjectName:        instance.ProjectName,
		Tags:               ConvertTags(instance.Tags),
		SubInstanceType:    instance.SubInstanceType,
		StorageType:        instance.StorageType,
	}
}

func ManagedInstancesDaoToEntity(instances []*dao.DbwInstance) []*ManagedInstance {
	if len(instances) == 0 {
		return nil
	}
	var ret []*ManagedInstance
	for _, instance := range instances {
		ret = append(ret, ManagedInstanceDaoToEntity(instance))
	}
	return ret
}

func ConvertTags(sourceTags string) []*model.TagObject {
	var newTags []*model.TagObject
	if err := json.Unmarshal([]byte(sourceTags), &newTags); err != nil {
		return newTags
	}
	return newTags
}
