package bootstrap

import (
	_ "code.byted.org/bytedts/dts-lib-bd/framework-bytedance/tcc"
	c3Impl "code.byted.org/infcs/dbw-mgr/biz/infrastructure/c3"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/bytedoc"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/byterds"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/ds-lib/framework/config"
	"code.byted.org/infcs/ds-lib/framework/di"
)

func InitC3Config() {
	di.GetAppContainer().MustRegister(c3Impl.NewClientFactory)
	//if os.Getenv("runtime") == "bytecloud" {
	if utils.IsByteCloud() {
		config.SetConfig("../conf/bytecloud.config.yml")
		di.GetAppContainer().MustRegister(c3Impl.NewTCCConfigProviderImpl)
		di.GetAppContainer().MustRegister(byterds.NewByteRDSClient)
		di.GetAppContainer().MustRegister(bytedoc.NewByteDocClient)
		di.GetAppContainer().MustRegister(mgr.NewDsMgrBuilder)
		di.GetAppContainer().MustRegister(byterds.NewByteRdsDataSource)
		di.GetAppContainer().MustRegister(bytedoc.NewByteDocDataSource)
	} else {
		// TODO use args to set config file path
		di.GetAppContainer().MustRegister(c3Impl.NewC3ConfigProviderImpl)
	}

}
