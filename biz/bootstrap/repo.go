package bootstrap

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/ds-lib/framework/di"
)

func InitDALAndRepo() {
	di.GetAppContainer().MustRegister(dal.NewParamDAL)
	di.GetAppContainer().MustRegister(dal.NewCommandResultDAL)
	di.GetAppContainer().MustRegister(dal.NewCommandSetDAL)
	di.GetAppContainer().MustRegister(dal.NewShuttleDAL)
	initAuditDAL()
}

func initAuditDAL() {
	di.GetAppContainer().MustRegister(dal.NewAuditTlsDAL)
	di.GetAppContainer().MustRegister(dal.NewTlsDAL)
	di.GetAppContainer().MustRegister(dal.NewAuditTlsJobPodDAL)
	di.GetAppContainer().MustRegister(dal.NewAccountantGenDAL)
	di.GetAppContainer().MustRegister(dal.NewAuditAccountantDAL)

}
