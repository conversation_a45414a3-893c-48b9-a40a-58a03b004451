package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type DataArchiveConfigDAL interface {
	GetDataArchiveConfig(ctx context.Context, archiveConfigId int64) (*dao.ArchiveConfig, error)
	CreateArchiveConfig(ctx context.Context, archiveConfig *dao.ArchiveConfig) error
	CloseArchiveConfig(ctx context.Context, archiveConfigId int64) error
	DeleteArchiveConfig(ctx context.Context, archiveConfigId int64) error
	ListArchiveConfigs(ctx context.Context, tenantId string, req *model.DescribeArchiveConfigsReq) (*dao.ListArchiveConfigsResp, error)
	UpdateLastArchiveStatus(ctx context.Context, archiveConfigId int64, LastArchiveStatus model.ArchiveConfigStatus) error
}

func NewDataArchiveConfigDAL(provider DBProvider) DataArchiveConfigDAL {
	return &DataArchiveConfigDal{dbProvider: provider}
}

type DataArchiveConfigDal struct {
	dbProvider DBProvider
}

func (dal *DataArchiveConfigDal) GetDataArchiveConfig(ctx context.Context, archiveConfigId int64) (*dao.ArchiveConfig, error) {
	db := dal.dbProvider.GetMetaDB(ctx)
	var config *dao.ArchiveConfig
	err := db.Where("config_id=? and deleted = 0", archiveConfigId).Limit(1).Find(&config).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		log.Warn(ctx, "ErrRecordNotFound")
		return nil, nil
	}
	if err == nil && (config == nil || config.ConfigId == 0) {
		return nil, fmt.Errorf("not found")
	}
	return config, err
}

func (dal *DataArchiveConfigDal) CreateArchiveConfig(ctx context.Context, archiveConfig *dao.ArchiveConfig) error {
	db := dal.dbProvider.GetMetaDB(ctx)
	err := db.Create(archiveConfig).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *DataArchiveConfigDal) CloseArchiveConfig(ctx context.Context, archiveConfigId int64) error {
	db := dal.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.ArchiveConfig{}).Where("config_id = ? ", archiveConfigId).Updates(map[string]interface{}{
		"IsOpen":     0,
		"UpdateTime": time.Now().Unix(),
	}).Error
	return err
}

func (dal *DataArchiveConfigDal) UpdateLastArchiveStatus(ctx context.Context, archiveConfigId int64, LastArchiveStatus model.ArchiveConfigStatus) error {
	db := dal.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.ArchiveConfig{}).Where("config_id = ? ", archiveConfigId).Updates(map[string]interface{}{
		"LastArchiveStatus": int8(LastArchiveStatus),
		"UpdateTime":        time.Now().Unix(),
	}).Error
	return err
}

func (dal *DataArchiveConfigDal) DeleteArchiveConfig(ctx context.Context, archiveConfigId int64) error {
	db := dal.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.ArchiveConfig{}).Where("config_id = ? ", archiveConfigId).Updates(map[string]interface{}{
		"Deleted":    1,
		"UpdateTime": time.Now().Unix(),
	}).Error
	return err
}

func (dal *DataArchiveConfigDal) ListArchiveConfigs(ctx context.Context, tenantId string, req *model.DescribeArchiveConfigsReq) (*dao.ListArchiveConfigsResp, error) {
	db := dal.dbProvider.GetMetaDB(ctx)
	whereCase, values := covertReqToWhereCase(tenantId, req)

	var total int64
	err := db.Model(&dao.ArchiveConfig{}).Where(whereCase, values...).Count(&total).Error
	if err != nil {
		log.Warn(ctx, "ticketId:%s get total config error:%s", *req.TicketId, err.Error())
		return nil, err
	}

	limit := int(req.PageSize)
	offset := int(req.PageSize * (req.PageNumber - 1))
	var res []*dao.ArchiveConfig
	err = db.Where(whereCase, values...).Offset(offset).Limit(limit).Order(getOrderInfo(req)).Find(&res).Error
	if err != nil {
		log.Warn(ctx, "ticketId:%s get configs error:%s", req.TicketId, err.Error())
		return nil, err
	}
	return &dao.ListArchiveConfigsResp{
		Configs: res,
		Total:   int32(total),
	}, nil
}

func getOrderInfo(req *model.DescribeArchiveConfigsReq) string {
	orderBy := "create_time"
	if req.OrderBy != nil {
		switch req.GetOrderBy() {
		case model.OrderByForArchiveConfig_CreateTime:
			orderBy = "create_time"
		case model.OrderByForArchiveConfig_LastExecuteTime:
			orderBy = "update_time"
		}
	}
	sortBy := "ASC"
	if req.SortBy != nil {
		switch req.GetSortBy() {
		case model.SortBy_ASC:
			sortBy = "ASC"
		case model.SortBy_DESC:
			sortBy = "DESC"
		}
	}
	return fmt.Sprintf(" %s %s ", orderBy, sortBy)
}

func covertReqToWhereCase(tenantId string, req *model.DescribeArchiveConfigsReq) (string, []interface{}) {
	whereCase := " tenant_id = ? and deleted = 0 "
	var values []interface{}
	values = append(values, tenantId)
	if req.CreateUserId != nil {
		whereCase += " AND create_user_id like ? "
		values = append(values, "%"+req.GetCreateUserId()+"%")
	}
	if req.CreateUser != nil {
		whereCase += " AND create_user like ? "
		values = append(values, "%"+req.GetCreateUser()+"%")
	}
	if req.DbName != nil {
		whereCase += " AND db_name like ? "
		values = append(values, "%"+req.GetDbName()+"%")
	}
	if req.InstanceId != nil {
		whereCase += " AND instance_id like ? "
		values = append(values, "%"+req.GetInstanceId()+"%")
	}
	if req.TableName != nil {
		whereCase += " AND table_name like ? "
		values = append(values, "%"+req.GetTableName()+"%")
	}
	if req.TicketId != nil {
		whereCase += " AND ticket_id like ? "
		values = append(values, "%"+req.GetTicketId()+"%")
	}

	if req.StartCreateTime != nil {
		whereCase += " AND create_time > ? "
		values = append(values, req.GetStartCreateTime())
	}
	if req.EndCreateTime != nil {
		whereCase += " AND create_time < ? "
		values = append(values, req.GetEndCreateTime())
	}
	if req.ConfigStatus != nil {
		whereCase += " AND last_archive_status = ? "
		values = append(values, int8(req.GetConfigStatus()))
	}

	return whereCase, values
}
