package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
)

type CollectionDAL interface {
	Count(ctx context.Context, instanceID string, collectionType CollectionType) (count int64, err error)
	Create(ctx context.Context, collection *dao.Collection) (id int32, err error)
	DeleteAll(ctx context.Context, instanceID string, collectionType CollectionType) (err error)
	DeleteKey(ctx context.Context, key string, collectionType CollectionType) (err error)
	List(ctx context.Context, instanceID string, collectionType CollectionType) ([]*dao.Collection, error)
	Get(ctx context.Context, instanceID string, collectionType CollectionType, collectionKey string) (collection *dao.Collection, err error)
}

type CollectionType string

const (
	CollectionType_FullSqlFingerprint CollectionType = "FullSqlFingerprint"
)

func (c CollectionType) String() string {
	return string(c)
}

type collection struct {
	dbProvider DBProvider
}

func (c collection) Create(ctx context.Context, collection *dao.Collection) (int32, error) {
	db := c.dbProvider.GetMetaDB(ctx)
	err := db.Create(collection).Error
	return collection.ID, err
}

func (c collection) DeleteAll(ctx context.Context, instanceID string, collectionType CollectionType) (err error) {
	db := c.dbProvider.GetMetaDB(ctx)
	err = db.Model(&dao.Collection{}).Where("instance_id =? and collection_type =? ",
		instanceID,
		collectionType).Delete(&dao.Collection{}).Error
	return err
}

func (c collection) DeleteKey(ctx context.Context, key string, collectionType CollectionType) (err error) {
	db := c.dbProvider.GetMetaDB(ctx)
	err = db.Model(&dao.Collection{}).Where("collection_key =? and collection_type =? ",
		key,
		collectionType).Delete(&dao.Collection{}).Error
	return err
}

func (c collection) List(ctx context.Context, instanceID string, collectionType CollectionType) ([]*dao.Collection, error) {
	db := c.dbProvider.GetMetaDB(ctx)
	var collections []*dao.Collection
	err := db.Where("instance_id =? and collection_type =? ", instanceID, collectionType).
		Find(&collections).Error
	return collections, err
}

func (c collection) Count(ctx context.Context, instanceID string, collectionType CollectionType) (count int64, err error) {
	db := c.dbProvider.GetMetaDB(ctx)
	err = db.Model(&dao.Collection{}).Where("instance_id =? and collection_type =? ", instanceID, collectionType).
		Count(&count).Error
	return count, err
}
func (c collection) Get(ctx context.Context, instanceID string, collectionType CollectionType, collectionKey string) (collection *dao.Collection, err error) {
	db := c.dbProvider.GetMetaDB(ctx)
	err = db.Model(&dao.Collection{}).Where("instance_id =? and collection_type =? and collection_key =? ", instanceID, collectionType, collectionKey).
		First(&collection).Error
	return collection, err
}

func NewCollectionDAL(dbProvider DBProvider) CollectionDAL {
	return &collection{
		dbProvider: dbProvider,
	}
}
