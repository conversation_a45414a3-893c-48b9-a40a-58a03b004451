package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	context "context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockDataArchiveConfigDAL() DataArchiveConfigDAL {
	return NewDataArchiveConfigDAL(&mockDBProvider{})
}

func TestGetDataArchiveConfig(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Find).Return(mockRes).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Limit).Return(mockRes).Build()
	defer mock3.UnPatch()
	mockRes.Error = gorm.ErrRecordNotFound

	dal := mockDataArchiveConfigDAL()
	_, err := dal.GetDataArchiveConfig(context.Background(), 1)
	assert.Nil(t, err)

	mockRes.Error = nil
	_, err = dal.GetDataArchiveConfig(context.Background(), 1)
	assert.NotNil(t, err)
}

func TestCreateArchiveConfig(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDataArchiveConfigDAL()
	err := dal.CreateArchiveConfig(context.Background(), &dao.ArchiveConfig{})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.CreateArchiveConfig(context.Background(), &dao.ArchiveConfig{})
	assert.Nil(t, err)
}

func TestCloseArchiveConfig(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock4.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDataArchiveConfigDAL()
	err := dal.CloseArchiveConfig(context.Background(), 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.CloseArchiveConfig(context.Background(), 1)
	assert.Nil(t, err)
}

func TestUpdateLastArchiveStatus(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock4.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDataArchiveConfigDAL()
	err := dal.UpdateLastArchiveStatus(context.Background(), 1, model.ArchiveConfigStatus_Success)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.UpdateLastArchiveStatus(context.Background(), 1, model.ArchiveConfigStatus_Success)
	assert.Nil(t, err)
}

func TestDeleteArchiveConfig(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock4.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDataArchiveConfigDAL()
	err := dal.DeleteArchiveConfig(context.Background(), 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.DeleteArchiveConfig(context.Background(), 1)
	assert.Nil(t, err)
}

func TestListArchiveConfigs(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mockRes := &gorm.DB{}
	mock3 := mockey.Mock((*gorm.DB).Count).Return(mockRes).Build()
	defer mock3.UnPatch()

	mockRes.Error = fmt.Errorf("test")

	req := mockListArchiveConfigReq()
	dal := mockDataArchiveConfigDAL()
	_, err := dal.ListArchiveConfigs(context.Background(), "1", req)
	assert.NotNil(t, err)
	mockRes.Error = nil

	mock4 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock6.UnPatch()
	mock7 := mockey.Mock((*gorm.DB).Find).Return(mockRes).Build()
	defer mock7.UnPatch()
	_, err = dal.ListArchiveConfigs(context.Background(), "1", req)
	assert.Nil(t, err)
}

func mockListArchiveConfigReq() *model.DescribeArchiveConfigsReq {
	str := ""
	num := int32(1)
	orderBy := model.OrderByForArchiveConfig_CreateTime
	sortBy := model.SortBy_ASC
	return &model.DescribeArchiveConfigsReq{
		CreateUserId:    &str,
		CreateUser:      &str,
		DbName:          &str,
		InstanceId:      &str,
		TableName:       &str,
		TicketId:        &str,
		StartCreateTime: &num,
		EndCreateTime:   &num,
		PageNumber:      1,
		PageSize:        1,
		OrderBy:         &orderBy,
		SortBy:          &sortBy,
	}
}

func TestGetOrderInfo(t *testing.T) {
	req := mockListArchiveConfigReq()
	orderBy := model.OrderByForArchiveConfig_LastExecuteTime
	sortBy := model.SortBy_DESC
	req.OrderBy = &orderBy
	req.SortBy = &sortBy
	res := getOrderInfo(req)
	assert.Equal(t, " update_time DESC ", res)
}
