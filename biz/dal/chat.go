package dal

import (
	"context"
	"time"

	bizCtx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-lib/framework/db"
	"gorm.io/gorm"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

const (
	nl2sql = "nl2sql"
)

type ChatDAL interface {
	Create(ctx context.Context, chat *dao.Chat) error
	Update(ctx context.Context, chat *dao.Chat) error
	GetChat(ctx context.Context, ChatID int64) (chat *dao.Chat, err error)
	GetChats(ctx context.Context, cursor *int64, limit int) (chats []*dao.Chat, err error)
	Delete(ctx context.Context, ChatID int64) error
}

func NewChatDAL(dbPro DBProvider) ChatDAL {
	return &chatDal{dbPro: dbPro}
}

type chatDal struct {
	dbPro DBProvider
}

func (c *chatDal) Create(ctx context.Context, chat *dao.Chat) error {
	dbx := c.dbPro.GetMetaDB(ctx)
	return dbx.Create(chat).Error
}

func (c *chatDal) Update(ctx context.Context, chat *dao.Chat) error {
	dbx := c.dbPro.GetMetaDB(ctx)
	return dbx.Model(&dao.Chat{}).Where(`id=?`, chat.ID).Update("name", chat.Name).Error
}

func (c *chatDal) GetChats(ctx context.Context, cursor *int64, limit int) (chats []*dao.Chat, err error) {
	dbx := c.dbPro.GetMetaDB(ctx)
	var query *gorm.DB
	tenantID := bizCtx.GetTenantID(ctx)
	query = dbx.Where("tenant_id=?", tenantID)
	userID := bizCtx.GetUserID(ctx)

	if userID != "" {
		query = query.Where("user_id=?", userID)
	} else {
		query = query.Where(`user_id IS NULL`)
	}
	if cursor != nil {
		query = query.Where("id<?", cursor)
	}
	query.Where(`deleted=0`).Where("name!=?", nl2sql)
	err = query.Order("id DESC").Limit(limit).Find(&chats).Error
	return
}

func (c *chatDal) GetChat(ctx context.Context, ChatID int64) (chat *dao.Chat, err error) {
	dbx := c.dbPro.GetMetaDB(ctx)
	query := dbx.Where("id=?", ChatID)
	tenantID := bizCtx.GetTenantID(ctx)
	if tenantID != "" {
		query = query.Where("tenant_id=?", tenantID)
	}
	query.Where(`deleted=0`)
	err = query.First(&chat).Error
	return
}

func (c *chatDal) Delete(ctx context.Context, chatID int64) error {
	dbx := c.dbPro.GetMetaDB(ctx)
	return db.ExecWithinTransaction(dbx, func(d *db.DB) error {
		_, err := c.GetChat(ctx, chatID)
		if err != nil {
			return err
		}
		q := d.Model(&dao.Chat{}).Where("id=?", chatID)
		tenantID := bizCtx.GetTenantID(ctx)
		q = q.Where("tenant_id=?", tenantID)
		userID := bizCtx.GetUserID(ctx)
		if userID != "" {
			q.Where("user_id=?", userID)
		}
		q.Update("deleted", 1).Update("deleted_time", time.Now().UnixNano()/1e6)
		d.Model(&dao.Message{}).Where("chat_id=?", chatID).Update("state", 1)
		return nil
	})
}
