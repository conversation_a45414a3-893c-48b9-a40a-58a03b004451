package dal

import (
	"code.byted.org/infcs/ds-lib/framework/db"
	"context"
	"gorm.io/gorm"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
)

func initInstanceHistoryDal() *instanceHistory {
	return &instanceHistory{
		dbPro: &mockDBProvider{},
	}
}

func Test_List(t *testing.T) {
	ctx := context.Background()
	query := &dao.HistoryQuery{
		InstanceId:   "123",
		InstanceName: "test-instance",
		InstanceType: "typeA",
		Keyword:      "test",
		SortBy:       "asc",
	}
	c := initInstanceHistoryDal()

	PatchConvey("Test List with nil query", t, func() {
		// Mock the GetMetaDB method to return a mock DB instance
		Mock(GetMethod(c.dbPro, "GetMetaDB")).Return(&db.DB{}).Build()

		mock1 := Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
		defer mock1.UnPatch()
		mock2 := Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
		defer mock2.UnPatch()
		mock3 := Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
		defer mock3.UnPatch()
		mock4 := Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
		defer mock4.UnPatch()
		mock5 := Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
		defer mock5.UnPatch()
		mock6 := Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
		defer mock6.UnPatch()
		mock7 := Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
		defer mock7.UnPatch()
		mock9 := Mock((*gorm.DB).ToSQL).Return("test").Build()
		defer mock9.UnPatch()

		result, err := c.List(ctx, query, 10, 0)

		So(err, ShouldBeNil)
		So(result.Total, ShouldEqual, 0)
		So(len(result.Items), ShouldEqual, 0)
	})
}

func Test_Get(t *testing.T) {
	ctx := context.Background()
	query := &dao.HistoryQuery{
		InstanceId:   "123",
		InstanceName: "test-instance",
		InstanceType: "typeA",
		Keyword:      "test",
		SortBy:       "asc",
	}
	c := initInstanceHistoryDal()

	PatchConvey("Test Get with nil query", t, func() {
		// Mock the GetMetaDB method to return a mock DB instance
		Mock(GetMethod(c.dbPro, "GetMetaDB")).Return(&db.DB{}).Build()

		mock1 := Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
		defer mock1.UnPatch()
		mock2 := Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
		defer mock2.UnPatch()
		mock3 := Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
		defer mock3.UnPatch()
		mock4 := Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
		defer mock4.UnPatch()
		mock5 := Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
		defer mock5.UnPatch()
		mock6 := Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
		defer mock6.UnPatch()
		mock7 := Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
		defer mock7.UnPatch()
		mock8 := Mock((*gorm.DB).First).Return(&gorm.DB{}).Build()
		defer mock8.UnPatch()
		mock9 := Mock((*gorm.DB).ToSQL).Return("test").Build()
		defer mock9.UnPatch()

		_, err := c.Get(ctx, query)

		So(err, ShouldBeNil)
	})
}

func Test_Delete(t *testing.T) {
	ctx := context.Background()
	c := initInstanceHistoryDal()

	PatchConvey("Test Delete with nil query", t, func() {
		// Mock the GetMetaDB method to return a mock DB instance
		Mock(GetMethod(c.dbPro, "GetMetaDB")).Return(&db.DB{}).Build()

		mock1 := Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
		defer mock1.UnPatch()
		mock2 := Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
		defer mock2.UnPatch()
		mock3 := Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
		defer mock3.UnPatch()
		mock4 := Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
		defer mock4.UnPatch()
		mock5 := Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
		defer mock5.UnPatch()
		mock6 := Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
		defer mock6.UnPatch()
		mock7 := Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
		defer mock7.UnPatch()
		mock8 := Mock((*gorm.DB).Delete).Return(&gorm.DB{}).Build()
		defer mock8.UnPatch()
		mock9 := Mock((*gorm.DB).ToSQL).Return("test").Build()
		defer mock9.UnPatch()

		err := c.Delete(ctx, "123")

		So(err, ShouldNotBeNil)
	})
}
func Test_DeleteAll(t *testing.T) {
	ctx := context.Background()
	c := initInstanceHistoryDal()

	PatchConvey("Test DeleteAll with nil query", t, func() {
		// Mock the GetMetaDB method to return a mock DB instance
		Mock(GetMethod(c.dbPro, "GetMetaDB")).Return(&db.DB{}).Build()

		mock1 := Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
		defer mock1.UnPatch()
		mock2 := Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
		defer mock2.UnPatch()
		mock3 := Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
		defer mock3.UnPatch()
		mock4 := Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
		defer mock4.UnPatch()
		mock5 := Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
		defer mock5.UnPatch()
		mock6 := Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
		defer mock6.UnPatch()
		mock7 := Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
		defer mock7.UnPatch()
		mock8 := Mock((*gorm.DB).Delete).Return(&gorm.DB{}).Build()
		defer mock8.UnPatch()
		mock9 := Mock((*gorm.DB).ToSQL).Return("test").Build()
		defer mock9.UnPatch()

		err := c.DeleteAll(ctx)

		So(err, ShouldNotBeNil)
	})
}
