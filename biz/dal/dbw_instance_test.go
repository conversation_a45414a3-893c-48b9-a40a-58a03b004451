package dal

import (
	"context"
	"github.com/bytedance/mockey"
	"github.com/stvp/assert"
	"gorm.io/gorm"
	"testing"
)

func TestUpdateDbwInstanceAndTicketByID(t *testing.T) {
	dal := &DbwInstanceDal{dbPro: &mockDBProvider{}}

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Begin).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(&gorm.DB{}).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*gorm.DB).Exec).Return(&gorm.DB{}).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*gorm.DB).Commit).Return(&gorm.DB{}).Build()
	defer mock6.UnPatch()
	err := dal.UpdateDbwInstanceAndTicketByID(context.Background(), "", 1, "", "", "", "", "", 1, 1)
	assert.Nil(t, err)
}
