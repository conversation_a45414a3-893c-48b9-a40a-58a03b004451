package dal

import (
	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
)

type CopilotChatDAL interface {
	Create(ctx context.Context, CopilotChat *dao.CopilotChat) error
	UpdateChatName(ctx context.Context, tenantId string, chatId string, chatName string) error
	DeleteChat(ctx context.Context, tenantId string, chatId string) error
	QueryChatList(ctx context.Context, tenantId string, userId string, sceneType int8, instanceId string) []*dao.CopilotChat
	QueryChatById(ctx context.Context, tenantId string, chatId string) (*dao.CopilotChat, error)
}

func NewCopilotChatDAL(dbPro DBProvider) CopilotChatDAL {
	return &CopilotChatDal{dbPro: dbPro}
}

type CopilotChatDal struct {
	dbPro DBProvider
}

type CopilotChatParam struct {
	ID   string
	Name string
}

func (dal *CopilotChatDal) Create(ctx context.Context, CopilotChat *dao.CopilotChat) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(CopilotChat).Error
	if err != nil {
		log.Warn(ctx, "CopilotChatDal Create err:%v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return err
}

func (dal *CopilotChatDal) QueryChatById(ctx context.Context, tenantId string, chatId string) (*dao.CopilotChat, error) {

	db := dal.dbPro.GetMetaDB(ctx)

	var (
		CopilotChat   *dao.CopilotChat
		BaseCondition []interface{}
	)

	baseQuery := "is_delete = 0 "
	if !strings.IsEmpty(tenantId) {
		baseQuery += "and tenant_id = ? "
		BaseCondition = append(BaseCondition, tenantId)
	}
	baseQuery += "and id = ? "
	BaseCondition = append(BaseCondition, chatId)

	if err := db.Where(baseQuery, BaseCondition...).First(&CopilotChat).Error; err != nil {
		return nil, err
	}

	return CopilotChat, nil
}

func (dal *CopilotChatDal) QueryChatList(ctx context.Context, tenantId string, userId string, sceneType int8, instanceId string) []*dao.CopilotChat {

	db := dal.dbPro.GetMetaDB(ctx)

	var (
		CopilotChatList []*dao.CopilotChat
		BaseCondition   []interface{}
	)

	baseQuery := "is_delete = 0 and tenant_id = ? "
	BaseCondition = append(BaseCondition, tenantId)

	baseQuery += "and instance_id = ? "
	BaseCondition = append(BaseCondition, instanceId)

	if userId != "" {
		baseQuery += "and user_id = ? "
		BaseCondition = append(BaseCondition, userId)
	}

	if sceneType != int8(model.CopilotSceneType_None) {
		baseQuery += "and scene_type = ? "
		BaseCondition = append(BaseCondition, sceneType)
	}

	if err := db.Where(baseQuery, BaseCondition...).Order("id DESC").Find(&CopilotChatList).Error; err != nil {
		return CopilotChatList
	}

	return CopilotChatList
}

func (dal *CopilotChatDal) UpdateChatName(ctx context.Context, tenantId string, chatId string, chatName string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.CopilotChat{}).Where("id=? and tenant_id=? and is_delete=0", chatId, tenantId).Updates(map[string]interface{}{
		"ChatName": chatName,
	})
	if err := res.Error; err != nil {
		return err
	}
	return nil
}

func (dal *CopilotChatDal) DeleteChat(ctx context.Context, tenantId string, chatId string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.CopilotChat{}).Where("id=? and tenant_id=?", chatId, tenantId).Updates(map[string]interface{}{
		"IsDelete": 1,
	})
	if err := res.Error; err != nil {
		log.Warn(ctx, "CopilotChatDal UpdateChatName err:%v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return nil
}
