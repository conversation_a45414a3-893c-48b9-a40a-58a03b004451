package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	. "gorm.io/gorm"
)

type MysqlInformationSchemaTableDAL interface {
	Create(ctx context.Context, Instance *dao.MysqlInformationSchemaTable) error
	List(ctx context.Context, InstanceId string, InstanceType string, TenantId string, limit int32, offset int32, OrderBy string, SortBy string, query dao.DataQuery) ([]*dao.MysqlInformationSchemaTable, error)
	Get(ctx context.Context, InstanceId string, InstanceType string, TenantId string, query dao.DataQuery) ([]*dao.MysqlInformationSchemaTable, error)
}

func NewMysqlInformationSchemaTableDAL(dbPro DBProvider) MysqlInformationSchemaTableDAL {
	return &MysqlInformationSchemaTableDal{dbPro: dbPro}
}

type MysqlInformationSchemaTableDal struct {
	dbPro DBProvider
}

func (dal *MysqlInformationSchemaTableDal) Create(ctx context.Context, Instance *dao.MysqlInformationSchemaTable) error {
	db := dal.dbPro.GetSyncerDB(ctx)
	err := db.Create(Instance).Error
	return err
}

func (dal *MysqlInformationSchemaTableDal) List(ctx context.Context, InstanceId string, InstanceType string, TenantId string, limit int32, offset int32, OrderBy string, SortBy string, query dao.DataQuery) ([]*dao.MysqlInformationSchemaTable, error) {
	var (
		DbwTables      []*dao.MysqlInformationSchemaTable
		baseConditions []interface{}
	)
	orderBy := "created_at"
	sortBy := "desc"
	db := dal.dbPro.GetSyncerDB(ctx)
	baseQuery := "instance_type=? and instance_id=? "
	baseConditions = append(baseConditions, InstanceType)
	baseConditions = append(baseConditions, InstanceId)

	if OrderBy != "" {
		orderBy = OrderBy
	}
	if SortBy != "" {
		sortBy = SortBy
	}
	if query.DbName != "" {
		baseQuery += " and table_schema=? "
		baseConditions = append(baseConditions, query.DbName)
	}
	if query.TableName != "" {
		baseQuery += " and table_name_value=? "
		baseConditions = append(baseConditions, query.TableName)
	}
	if query.AfterTime != 0 {
		baseQuery += " and created_at > ? "
		baseConditions = append(baseConditions, query.AfterTime)
	}
	if err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(int(offset)).Limit(int(limit)).Find(&DbwTables).Error; err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(int(offset)).Limit(int(limit)).Find(&DbwTables)
	}))
	return DbwTables, nil
}

func (dal *MysqlInformationSchemaTableDal) Get(ctx context.Context, InstanceId string, InstanceType string, TenantId string, query dao.DataQuery) ([]*dao.MysqlInformationSchemaTable, error) {
	var (
		DbwTables      []*dao.MysqlInformationSchemaTable
		baseConditions []interface{}
	)
	db := dal.dbPro.GetSyncerDB(ctx)
	baseQuery := "instance_type=? and instance_id=? "
	baseConditions = append(baseConditions, InstanceType)
	baseConditions = append(baseConditions, InstanceId)

	if query.DataTime != 0 {
		baseQuery += " and created_at=? "
		baseConditions = append(baseConditions, query.DataTime)
	}
	if query.DbName != "" {
		baseQuery += " and table_schema=? "
		baseConditions = append(baseConditions, query.DbName)
	}
	if query.TableName != "" {
		baseQuery += " and table_name_value=? "
		baseConditions = append(baseConditions, query.TableName)
	}
	if query.AfterTime != 0 {
		baseQuery += " and created_at >=? "
		baseConditions = append(baseConditions, query.AfterTime)
	}
	if query.BeforeTime != 0 {
		baseQuery += " and created_at <=? "
		baseConditions = append(baseConditions, query.BeforeTime)
	}
	if err := db.Where(baseQuery, baseConditions...).Find(&DbwTables).Error; err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Find(&DbwTables)
	}))
	return DbwTables, nil
}

type TableIoWaitsSummaryByTableDAL interface {
	Create(ctx context.Context, Instance *dao.TableIoWaitsSummaryByTable) error
}

func NewTableIoWaitsSummaryByTableDAL(dbPro DBProvider) TableIoWaitsSummaryByTableDAL {
	return &TableIoWaitsSummaryByTableDal{dbPro: dbPro}
}

type TableIoWaitsSummaryByTableDal struct {
	dbPro DBProvider
}

func (dal *TableIoWaitsSummaryByTableDal) Create(ctx context.Context, Instance *dao.TableIoWaitsSummaryByTable) error {
	db := dal.dbPro.GetSyncerDB(ctx)
	err := db.Create(Instance).Error
	return err
}

type TableIoWaitsSummaryByIndexUsageDAL interface {
	Create(ctx context.Context, Instance *dao.TableIoWaitsSummaryByIndexUsage) error
	List(ctx context.Context, InstanceId string, InstanceType string, TenantId string, limit int32, offset int32, OrderBy string, SortBy string, query dao.DataQuery) ([]*dao.TableIoWaitsSummaryByIndexUsage, error)
	Get(ctx context.Context, InstanceId string, InstanceType string, TenantId string, query dao.DataQuery) ([]*dao.TableIoWaitsSummaryByIndexUsage, error)
}

func NewTableIoWaitsSummaryByIndexUsageDAL(dbPro DBProvider) TableIoWaitsSummaryByIndexUsageDAL {
	return &TableIoWaitsSummaryByIndexUsageDal{dbPro: dbPro}
}

type TableIoWaitsSummaryByIndexUsageDal struct {
	dbPro DBProvider
}

func (dal *TableIoWaitsSummaryByIndexUsageDal) Create(ctx context.Context, Instance *dao.TableIoWaitsSummaryByIndexUsage) error {
	db := dal.dbPro.GetSyncerDB(ctx)
	err := db.Create(Instance).Error
	return err
}

func (dal *TableIoWaitsSummaryByIndexUsageDal) List(ctx context.Context, InstanceId string, InstanceType string, TenantId string, limit int32, offset int32, OrderBy string, SortBy string, query dao.DataQuery) ([]*dao.TableIoWaitsSummaryByIndexUsage, error) {
	var (
		DbwTables      []*dao.TableIoWaitsSummaryByIndexUsage
		baseConditions []interface{}
	)
	orderBy := "created_at"
	sortBy := "desc"
	db := dal.dbPro.GetSyncerDB(ctx)
	baseQuery := "instance_type=? and instance_id=? "
	baseConditions = append(baseConditions, InstanceType)
	baseConditions = append(baseConditions, InstanceId)

	if OrderBy != "" {
		orderBy = OrderBy
	}
	if SortBy != "" {
		sortBy = SortBy
	}
	if query.DbName != "" {
		baseQuery += " and object_schema=? "
		baseConditions = append(baseConditions, query.DbName)
	}
	if query.TableName != "" {
		baseQuery += " and object_name=? "
		baseConditions = append(baseConditions, query.TableName)
	}
	if query.AfterTime != 0 {
		baseQuery += " and created_at >= ? "
		baseConditions = append(baseConditions, query.AfterTime)
	}
	if query.BeforeTime != 0 {
		baseQuery += " and created_at <=? "
		baseConditions = append(baseConditions, query.BeforeTime)
	}
	if err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(int(offset)).Limit(int(limit)).Find(&DbwTables).Error; err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(int(offset)).Limit(int(limit)).Find(&DbwTables)
	}))
	return DbwTables, nil
}

func (dal *TableIoWaitsSummaryByIndexUsageDal) Get(ctx context.Context, InstanceId string, InstanceType string, TenantId string, query dao.DataQuery) ([]*dao.TableIoWaitsSummaryByIndexUsage, error) {
	var (
		DbwTables      []*dao.TableIoWaitsSummaryByIndexUsage
		baseConditions []interface{}
	)
	db := dal.dbPro.GetSyncerDB(ctx)
	baseQuery := "instance_type=? and instance_id=? "
	baseConditions = append(baseConditions, InstanceType)
	baseConditions = append(baseConditions, InstanceId)

	if query.DataTime != 0 {
		baseQuery += " and created_at=? "
		baseConditions = append(baseConditions, query.DataTime)
	}
	if query.DbName != "" {
		baseQuery += " and object_schema=? "
		baseConditions = append(baseConditions, query.DbName)
	}
	if query.TableName != "" {
		baseQuery += " and object_name=? "
		baseConditions = append(baseConditions, query.TableName)
	}
	if err := db.Where(baseQuery, baseConditions...).Find(&DbwTables).Error; err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Find(&DbwTables)
	}))
	return DbwTables, nil
}

type MysqlInnodbIndexStatsDAL interface {
	Create(ctx context.Context, Instance *dao.MysqlInnodbIndexStats) error
	List(ctx context.Context, InstanceId string, InstanceType string, TenantId string, limit int32, offset int32, OrderBy string, SortBy string, query dao.DataQuery) ([]*dao.MysqlInnodbIndexStats, error)
	Get(ctx context.Context, InstanceId string, InstanceType string, TenantId string, query dao.DataQuery) ([]*dao.MysqlInnodbIndexStats, error)
}

func NewMysqlInnodbIndexStatsDAL(dbPro DBProvider) MysqlInnodbIndexStatsDAL {
	return &MysqlInnodbIndexStatsDal{dbPro: dbPro}
}

type MysqlInnodbIndexStatsDal struct {
	dbPro DBProvider
}

func (dal *MysqlInnodbIndexStatsDal) Create(ctx context.Context, Instance *dao.MysqlInnodbIndexStats) error {
	db := dal.dbPro.GetSyncerDB(ctx)
	err := db.Create(Instance).Error
	return err
}

func (dal *MysqlInnodbIndexStatsDal) List(ctx context.Context, InstanceId string, InstanceType string, TenantId string, limit int32, offset int32, OrderBy string, SortBy string, query dao.DataQuery) ([]*dao.MysqlInnodbIndexStats, error) {
	var (
		DbwTables      []*dao.MysqlInnodbIndexStats
		baseConditions []interface{}
	)
	orderBy := "created_at"
	sortBy := "desc"
	db := dal.dbPro.GetSyncerDB(ctx)
	baseQuery := "instance_type=? and instance_id=? "
	baseConditions = append(baseConditions, InstanceType)
	baseConditions = append(baseConditions, InstanceId)

	if OrderBy != "" {
		orderBy = OrderBy
	}
	if SortBy != "" {
		sortBy = SortBy
	}
	if query.DbName != "" {
		baseQuery += " and database_name=? "
		baseConditions = append(baseConditions, query.DbName)
	}
	if query.TableName != "" {
		baseQuery += " and table_name_value=? "
		baseConditions = append(baseConditions, query.TableName)
	}
	if query.AfterTime != 0 {
		baseQuery += " and created_at > ? "
		baseConditions = append(baseConditions, query.AfterTime)
	}
	if err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(int(offset)).Limit(int(limit)).Find(&DbwTables).Error; err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(int(offset)).Limit(int(limit)).Find(&DbwTables)
	}))
	return DbwTables, nil
}

func (dal *MysqlInnodbIndexStatsDal) Get(ctx context.Context, InstanceId string, InstanceType string, TenantId string, query dao.DataQuery) ([]*dao.MysqlInnodbIndexStats, error) {
	var (
		DbwTables      []*dao.MysqlInnodbIndexStats
		baseConditions []interface{}
	)
	db := dal.dbPro.GetSyncerDB(ctx)
	baseQuery := "instance_type=? and instance_id=? "
	baseConditions = append(baseConditions, InstanceType)
	baseConditions = append(baseConditions, InstanceId)

	if query.DataTime != 0 {
		baseQuery += " and created_at=? "
		baseConditions = append(baseConditions, query.DataTime)
	}
	if query.DbName != "" {
		baseQuery += " and database_name=? "
		baseConditions = append(baseConditions, query.DbName)
	}
	if query.TableName != "" {
		baseQuery += " and table_name_value=? "
		baseConditions = append(baseConditions, query.TableName)
	}
	if query.AfterTime != 0 {
		baseQuery += " and created_at >=? "
		baseConditions = append(baseConditions, query.AfterTime)
	}
	if query.BeforeTime != 0 {
		baseQuery += " and created_at <=? "
		baseConditions = append(baseConditions, query.BeforeTime)
	}
	if err := db.Where(baseQuery, baseConditions...).Find(&DbwTables).Error; err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Find(&DbwTables)
	}))
	return DbwTables, nil
}
