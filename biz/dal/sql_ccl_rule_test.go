package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockSqlCclRuleDAL() SqlCCLRulesDAL {
	return NewSqlCCLRulesDAL(&mockDBProvider{})
}

func TestListCCLRuleByState(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mockRes := &gorm.DB{}

	mock3 := mockey.Mock((*gorm.DB).Select).Return(mockRes).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*gorm.DB).Group).Return(mockRes).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*gorm.DB).Having).Return(mockRes).Build()
	defer mock5.UnPatch()
	mockRes.Error = gorm.ErrRecordNotFound
	mock6 := mockey.Mock((*gorm.DB).Find).Return(mockRes).Build()
	defer mock6.UnPatch()
	dal := mockSqlCclRuleDAL()
	_, err := dal.ListCCLRuleByState(context.Background(), "mysql-xxx", []model.RuleState{model.RuleState_ACTIVE, model.RuleState_NONE}, 0, 1735737169000)
	assert.NotNil(t, err)
}

func TestCreateSqlCClRule(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = nil
	dal := mockSqlCclRuleDAL()
	err := dal.Create(context.Background(), &dao.SqlCCLRule{})
	assert.Nil(t, err)
}
