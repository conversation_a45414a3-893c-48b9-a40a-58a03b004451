package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"fmt"
	. "gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type InspectionTaskDAL interface {
	Create(ctx context.Context, task *dao.InspectionTask) error
	Get(ctx context.Context, taskId int64, tenantID string) (*dao.InspectionTask, error)
	GetAll(ctx context.Context, instanceType string, searchParam *model.InspectionSearchParam, orderBy string, sortBy string, pageNumber int64, pageSize int64, startTime int64, endTime int64) (*dao.InspectionTasksInfo, error)
	GetTaskTodayByInstanceId(ctx context.Context, instanceId string, tenantID string) (*dao.InspectionTask, error)
	UpdateTaskStatus(ctx context.Context, status int, taskId int64) error
	UpdateTaskInspectionValue(ctx context.Context, task *dao.InspectionTask) error
	GetTasksByStatus(ctx context.Context, taskStatus int64) ([]*dao.InspectionTask, error)
	GetTaskTenant(ctx context.Context, taskId int64) (string, error)
	UpdateInstanceNameByInstanceID(ctx context.Context, instanceID string, instanceName string, tenantID string) error
}

func NewInspectionTaskDAL(dbPro DBProvider) InspectionTaskDAL {
	return &inspectionTaskDal{dbPro: dbPro}
}

type inspectionTaskDal struct {
	dbPro DBProvider
}

func (dal *inspectionTaskDal) Create(ctx context.Context, task *dao.InspectionTask) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Clauses(clause.Insert{Modifier: "IGNORE"}).Create(task).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *inspectionTaskDal) Get(ctx context.Context, taskId int64, tenantID string) (*dao.InspectionTask, error) {
	var ret dao.InspectionTask
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("task_id=? and tenant_id=? and deleted=0", taskId, tenantID).First(&ret).Error
	if err != nil {
		return nil, err
	}
	return &ret, err
}

func (dal *inspectionTaskDal) GetAll(ctx context.Context, instanceType string, searchParam *model.InspectionSearchParam, orderBy string, sortBy string, pageNumber int64, pageSize int64, startTime int64, endTime int64) (*dao.InspectionTasksInfo, error) {
	var (
		ret            []*dao.InspectionTask
		total          int64
		limit          = int(pageSize)
		offset         = int(pageSize * (pageNumber - 1))
		baseConditions []interface{}
		baseQuery      string
	)
	baseQuery = " deleted = 0 and tenant_id = ? and instance_type = ? "
	baseConditions = append(baseConditions, fwctx.GetTenantID(ctx), instanceType)
	generateInspectionConditions(searchParam, &baseQuery, &baseConditions)

	if startTime != 0 && endTime != 0 {
		baseQuery += " and execute_time >= ? and execute_time <= ? "
		baseConditions = append(baseConditions, fmt.Sprintf("%d", startTime))
		baseConditions = append(baseConditions, fmt.Sprintf("%d", endTime))
	}
	db := dal.dbPro.GetMetaDB(ctx)
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Order(orderBy).Offset(offset).Limit(limit).Find(&ret) // ignore_security_alert
	}))
	// 求total
	if err := db.Model(ret).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(offset).Limit(limit).Find(&ret).Error // ignore_security_alert
	if err != nil {
		log.Info(ctx, "inspection: get all task error is :%s", err.Error())
		return nil, err
	}
	return &dao.InspectionTasksInfo{
		Total:           total,
		InspectionTasks: ret,
	}, nil
}

func (dal *inspectionTaskDal) GetTaskTodayByInstanceId(ctx context.Context, instanceId string, tenantID string) (*dao.InspectionTask, error) {
	now := time.Now()
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).UnixMilli()
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location()).UnixMilli()

	var ret dao.InspectionTask
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("instance_id=? and execute_time>=? and execute_time<=? and tenant_id=? and task_status=2 and task_type=0 and deleted=0", instanceId, startOfDay, endOfDay, tenantID).First(&ret).Error
	return &ret, err
}

func (dal *inspectionTaskDal) UpdateTaskStatus(ctx context.Context, status int, taskId int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.InspectionTaskTableName + " set task_status = ?, update_at = ? where task_id = ? " // ignore_security_alert
	err := db.Exec(sqlStr, status, time.Now().UnixMilli(), taskId).Error
	return err
}

func (dal *inspectionTaskDal) UpdateInstanceNameByInstanceID(ctx context.Context, instanceName string, instanceID string, tenantID string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.InspectionTaskTableName + " set instance_name = ? where instance_id = ? and tenant_id= ? " // ignore_security_alert
	err := db.Exec(sqlStr, instanceName, instanceID, tenantID).Error
	return err
}

func (dal *inspectionTaskDal) UpdateTaskInspectionValue(ctx context.Context, task *dao.InspectionTask) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Model(&dao.InspectionTask{}).Where("task_id=?", task.TaskID).Updates(map[string]interface{}{
		"InspectionExecuteTime": task.InspectionExecuteTime,
		"HealthScore":           task.HealthScore,
		"CpuUsage":              task.CpuUsage,
		"MemUsage":              task.MemUsage,
		"DiskUsage":             task.DiskUsage,
		"ConnUsage":             task.ConnUsage,
		"Qps":                   task.Qps,
		"Tps":                   task.Tps,
		"SlowLog":               task.SlowLog,
		"TaskStatus":            task.TaskStatus,
		"Memo":                  "Success",
		"UpdatedAt":             time.Now().UnixMilli(),
	}).Error
	return err
}

func (dal *inspectionTaskDal) GetTasksByStatus(ctx context.Context, taskStatus int64) ([]*dao.InspectionTask, error) {
	var ret []*dao.InspectionTask
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("task_status=? and task_type=0 and deleted=0", taskStatus).First(&ret).Error
	return ret, err
}

func (dal *inspectionTaskDal) GetTaskTenant(ctx context.Context, taskId int64) (string, error) {
	db := dal.dbPro.GetMetaDB(ctx)
	var tenantId string
	err := db.Raw("select tenant_id from "+dao.InspectionTaskTableName+" where task_id = ? and deleted = 0 limit 1", taskId).Scan(&tenantId).Error
	return tenantId, err
}

func generateInspectionConditions(searchParam *model.InspectionSearchParam, baseQuery *string, baseConditions *[]interface{}) {
	if searchParam == nil {
		return
	}
	if searchParam.IsSetInspectionType() {
		*baseQuery += " and task_type = ? "
		*baseConditions = append(*baseConditions, *searchParam.InspectionType)
	}
	if searchParam.IsSetInspectionStatus() {
		*baseQuery += " and task_status = ? "
		*baseConditions = append(*baseConditions, *searchParam.InspectionStatus)
	}
	if searchParam.IsSetInstanceId() {
		*baseQuery += " and instance_id like ? "
		*baseConditions = append(*baseConditions, "%"+*searchParam.InstanceId+"%")
	}
	if searchParam.IsSetInstanceName() {
		*baseQuery += " and instance_name like ? "
		*baseConditions = append(*baseConditions, "%"+*searchParam.InstanceName+"%")
	}
	if searchParam.IsSetMinScore() {
		*baseQuery += " and health_score >= ? "
		*baseConditions = append(*baseConditions, *searchParam.MinScore)
	}
	if searchParam.IsSetMaxScore() {
		*baseQuery += " and health_score <= ? "
		*baseConditions = append(*baseConditions, *searchParam.MaxScore)
	}
	
	return
}
