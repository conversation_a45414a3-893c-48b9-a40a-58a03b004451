package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"gorm.io/gorm"
)

type InstanceExtraNodeDAL interface {
	Create(ctx context.Context, node *dao.InstanceExtraNode) error
	UpdateNode(ctx context.Context, node *dao.InstanceExtraNode) error
	DeleteNode(ctx context.Context, InstanceId string, NodeId string) error
	DeleteNodes(ctx context.Context, InstanceId string) error
	GetNode(ctx context.Context, InstanceId string, NodeId string) (*dao.InstanceExtraNode, error)
	GetNodes(ctx context.Context, InstanceId string) ([]*dao.InstanceExtraNode, error)
}

func NewInstanceExtraNodeDAL(dbPro DBProvider) InstanceExtraNodeDAL {
	return &instanceExtraNodeDAL{dbPro: dbPro}
}

type instanceExtraNodeDAL struct {
	dbPro DBProvider
}

func (i instanceExtraNodeDAL) Create(ctx context.Context, node *dao.InstanceExtraNode) error {
	db := i.dbPro.GetMetaDB(ctx)
	return db.Create(node).Error
}

func (i instanceExtraNodeDAL) UpdateNode(ctx context.Context, node *dao.InstanceExtraNode) error {
	if node.Id == 0 {
		return gorm.ErrRecordNotFound
	}
	db := i.dbPro.GetMetaDB(ctx)
	return db.Save(node).Error
}

func (i instanceExtraNodeDAL) DeleteNode(ctx context.Context, InstanceId string, NodeId string) error {
	db := i.dbPro.GetMetaDB(ctx)
	return db.Model(&dao.InstanceExtraNode{}).Where(" instance_id = ? and node_id = ? and deleted = 0 ", InstanceId, NodeId).Updates(map[string]interface{}{
		"deleted": 1,
	}).Error
}

func (i instanceExtraNodeDAL) DeleteNodes(ctx context.Context, InstanceId string) error {
	db := i.dbPro.GetMetaDB(ctx)
	return db.Model(&dao.InstanceExtraNode{}).Where(" instance_id = ? and deleted = 0 ", InstanceId).Updates(map[string]interface{}{
		"deleted": 1,
	}).Error
}

func (i instanceExtraNodeDAL) GetNode(ctx context.Context, InstanceId string, NodeId string) (*dao.InstanceExtraNode, error) {
	db := i.dbPro.GetMetaDB(ctx)
	var instanceExtraNode dao.InstanceExtraNode
	err := db.Model(&dao.InstanceExtraNode{}).Where(" instance_id = ? and node_id = ? and deleted = 0 ", NodeId).First(&instanceExtraNode).Error
	if err != nil {
		return nil, err
	}
	return &instanceExtraNode, nil
}

func (i instanceExtraNodeDAL) GetNodes(ctx context.Context, InstanceId string) ([]*dao.InstanceExtraNode, error) {
	db := i.dbPro.GetMetaDB(ctx)
	var instanceExtraNodes []*dao.InstanceExtraNode
	err := db.Model(&dao.InstanceExtraNode{}).Where(" instance_id = ? and deleted = 0 ", InstanceId).Find(&instanceExtraNodes).Error
	if err != nil {
		return nil, err
	}
	return instanceExtraNodes, nil
}
