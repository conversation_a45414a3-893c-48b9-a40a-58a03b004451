package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type UserFolderTokenDAL interface {
	Create(ctx context.Context, task *dao.UserFolderToken) error
	GetFolderToken(ctx context.Context, userName string) (*dao.UserFolderToken, error)
	DeleteUserToken(ctx context.Context, userName string) error
}

func NewUserFolderTokenDAL(dbPro DBProvider) UserFolderTokenDAL {
	return &userFolderTokenDal{dbPro: dbPro}
}

type userFolderTokenDal struct {
	dbPro DBProvider
}

func (dal *userFolderTokenDal) Create(ctx context.Context, user *dao.UserFolderToken) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(user).Error
	if err != nil {
		return err
	}
	return err
}

func (dal *userFolderTokenDal) GetFolderToken(ctx context.Context, userName string) (*dao.UserFolderToken, error) {
	var ret dao.UserFolderToken
	db := dal.dbPro.GetMetaDB(ctx)
	//from ops
	err := db.Where("user_name = ? and deleted=0", userName).First(&ret).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		// 如果没有找到就返回一个空的
		return &dao.UserFolderToken{UserName: userName}, nil
	}
	return &ret, err
}

func (dal *userFolderTokenDal) DeleteUserToken(ctx context.Context, userName string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.UserFolderToken{}).Where("userName=?", userName).Updates(map[string]interface{}{
		"deleted":    1,
		"deleted_at": time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete task error %v", err)
	}

	return nil
}
