package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
)

type StatisticSqlTlsDal interface {
	Create(ctx context.Context, task dao.StatisticSqlTls) (int64, error)
	Update(ctx context.Context, task dao.StatisticSqlTls) error
	Get(ctx context.Context, tenantId string) ([]*dao.StatisticSqlTls, error)
	GetByID(ctx context.Context, id int64) (*dao.StatisticSqlTls, error)
	GetByDataType(ctx context.Context, tenantId string, dataType string) (*dao.StatisticSqlTls, error)
	Delete(ctx context.Context, id string, tenantId string) error
}

type statisticSqlTlsDal struct {
	DbProvider DBProvider
}

func (s statisticSqlTlsDal) Create(ctx context.Context, statisticSqlTls dao.StatisticSqlTls) (int64, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.StatisticSqlTls{}).Create(&statisticSqlTls).Error
	return statisticSqlTls.Id, err
}

func (s statisticSqlTlsDal) Update(ctx context.Context, statisticSqlTls dao.StatisticSqlTls) error {
	db := s.DbProvider.GetMetaDB(ctx)
	return db.Model(&dao.StatisticSqlTls{}).
		Where(" id = ? ", statisticSqlTls.Id).
		Updates(&statisticSqlTls).Error
}

func (s statisticSqlTlsDal) Get(ctx context.Context, tenantId string) ([]*dao.StatisticSqlTls, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	var statisticSqlTls []*dao.StatisticSqlTls
	err := db.Model(&dao.StatisticSqlTls{}).
		Where(" tenant_id = ? ", tenantId).
		Where(" deleted = 0 ").
		Find(&statisticSqlTls).Error
	return statisticSqlTls, err
}

func (s statisticSqlTlsDal) GetByID(ctx context.Context, id int64) (*dao.StatisticSqlTls, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	var statisticSqlTls dao.StatisticSqlTls
	err := db.Model(&dao.StatisticSqlTls{}).
		Where(" id = ? ", id).
		Where(" deleted = 0 ").
		First(&statisticSqlTls).Error
	return &statisticSqlTls, err
}

func (s statisticSqlTlsDal) GetByDataType(ctx context.Context, tenantId string, dataType string) (*dao.StatisticSqlTls, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	var statisticSqlTls dao.StatisticSqlTls
	err := db.Model(&dao.StatisticSqlTls{}).
		Where(" tenant_id = ? ", tenantId).
		Where(" data_type = ? ", dataType).
		Where(" deleted = 0 ").
		First(&statisticSqlTls).Error
	return &statisticSqlTls, err
}

func (s statisticSqlTlsDal) Delete(ctx context.Context, id string, tenantId string) error {
	db := s.DbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.StatisticSqlTls{}).
		Where(" id = ? ", id).
		Where(" tenant_id = ? ", tenantId).
		Where(" deleted = 0 ").
		Updates(map[string]interface{}{"deleted": 1}).Error
	return err
}

func NewStatisticSqlTlsDal(dbProvider DBProvider) StatisticSqlTlsDal {
	return &statisticSqlTlsDal{
		DbProvider: dbProvider,
	}
}
