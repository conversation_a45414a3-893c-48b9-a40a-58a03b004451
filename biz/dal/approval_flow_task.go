package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"gorm.io/gorm"
)

type ApprovalFlowDAL interface {
	CreateFlow(ctx context.Context, flow *dao.ApprovalFlow) error
	UpdateFlowStatus(ctx context.Context, flow *dao.ApprovalFlow) error
	Get(ctx context.Context, flowId int64) (*dao.ApprovalFlow, error)
}

func NewApprovalFlowDAL(provider DBProvider) ApprovalFlowDAL {
	return &ApprovalFlowDal{dbProvider: provider}
}

type ApprovalFlowDal struct {
	dbProvider DBProvider
}

func (dal *ApprovalFlowDal) CreateFlow(ctx context.Context, flow *dao.ApprovalFlow) error {
	db := dal.dbProvider.GetMetaDB(ctx)
	err := db.Create(flow).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *ApprovalFlowDal) UpdateFlowStatus(ctx context.Context, flow *dao.ApprovalFlow) error {
	db := dal.dbProvider.GetMetaDB(ctx)

	err := db.Model(&dao.ApprovalFlow{}).Where("flow_id = ? ", flow.FlowId).Updates(map[string]interface{}{
		"Status":         flow.Status,
		"Step":           flow.Step,
		"ApproverNodeId": flow.ApproverNodeId,
	}).Error

	return err
}

func (dal *ApprovalFlowDal) Get(ctx context.Context, flowId int64) (*dao.ApprovalFlow, error) {
	db := dal.dbProvider.GetMetaDB(ctx)
	var flow *dao.ApprovalFlow
	err := db.Where("flow_id=? and deleted = 0", flowId).Limit(1).Find(&flow).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		log.Warn(ctx, "ErrRecordNotFound")
		return nil, nil
	}
	if err == nil && (flow == nil || flow.FlowId == 0) {
		return nil, nil
	}
	return flow, err
}
