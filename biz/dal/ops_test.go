package dal

import (
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"reflect"
	"testing"
)

func Test_genAuditInstanceCondition(t *testing.T) {
	type args struct {
		instance AuditInstance
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 []interface{}
	}{
		{
			name: "a",
			args: args{
				instance: AuditInstance{
					DbType:   model.DSType_MySQL.String(),
					TenantID: "123",
					Deleted:  0,
				},
			},
			want:  " deleted=?  and db_type=?  and tenant_id=? ",
			want1: []interface{}{"0", "MySQL", "123"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := genAuditInstanceCondition(tt.args.instance)
			if got != tt.want {
				t.Errorf("genAuditInstanceCondition() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.<PERSON><PERSON><PERSON>("genAuditInstanceCondition() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
