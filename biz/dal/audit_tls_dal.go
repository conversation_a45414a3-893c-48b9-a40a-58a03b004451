package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
)

const (
	INST_WHERE_INSTANCE_ID_CONDITION     = "deleted=0 and instance_id=?        and tenant_id=? "
	INST_WHERE_ALL                       = "deleted=0 and product_type=? "
	INST_WHERE_TENANTID_CONDITION        = "deleted=0 and tenant_id=?          and product_type=? "
	INST_WHERE_CONDITION                 = "deleted=0 and follow_instance_id=? and tenant_id=?    and product_type=? "
	INST_WHERE_RDS_INSTANCE_CONDITION    = "deleted=0 and follow_instance_id=? and product_type=? "
	INSTANCE_ID_CONDITION_WITHOUT_TENANT = "deleted=0 and instance_id=? "
)

type ObInstDAL interface {
	Create(ctx context.Context, cs *dao.AuditTls) (int64, error)
	GetByFollowInstanceID(ctx context.Context, followInstanceID string, tenantId string, logProductType string) (*dao.AuditTls, error)
	GetByID(ctx context.Context, instanceID string, tenantId string) (*dao.AuditTls, error)
	GetByIDWithoutTenant(ctx context.Context, id string) (*dao.AuditTls, error)
	GetAll(ctx context.Context, logProductType string) ([]*dao.AuditTls, error)
	GetByFollowInstanceIDWithoutTenant(ctx context.Context, followInstanceID string, logProductType string) (*dao.AuditTls, error)
	GetByTenantId(ctx context.Context, tenantId string, logProductType string) ([]*dao.AuditTls, error)
	UpdateByFollowInstanceID(ctx context.Context, cs *dao.AuditTls) error
	Delete(ctx context.Context, instanceId string, tenantId string, logProductType string) error
	GetAllByTenantId(ctx context.Context, tenantId string, logProductType string, followInstanceID string, dsType string) ([]*dao.AuditTls, error)
}

func NewObInstDAL(provider DBProvider) ObInstDAL {
	return &obInst{dbProvider: provider}
}

type obInst struct {
	dbProvider DBProvider
}

func (a obInst) Create(ctx context.Context, at *dao.AuditTls) (int64, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Create(at).Error
	return at.ID, err
}

func (a obInst) GetByFollowInstanceID(ctx context.Context, followInstanceID string, tenantId string, logProductType string) (*dao.AuditTls, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at dao.AuditTls
	err := db.Where(INST_WHERE_CONDITION, followInstanceID, tenantId, logProductType).First(&at).Error
	return &at, err
}

func (a obInst) GetByID(ctx context.Context, id string, tenantId string) (*dao.AuditTls, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at dao.AuditTls
	err := db.Where(INST_WHERE_INSTANCE_ID_CONDITION, id, tenantId).First(&at).Error
	return &at, err
}

func (a obInst) GetByIDWithoutTenant(ctx context.Context, id string) (*dao.AuditTls, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at dao.AuditTls
	err := db.Where(INSTANCE_ID_CONDITION_WITHOUT_TENANT, id).First(&at).Error
	return &at, err
}

func (a obInst) GetAll(ctx context.Context, logProductType string) ([]*dao.AuditTls, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at []*dao.AuditTls
	err := db.Where(INST_WHERE_ALL, logProductType).Find(&at).Error
	return at, err
}

func (a obInst) GetByFollowInstanceIDWithoutTenant(ctx context.Context, followInstanceID string, logProductType string) (*dao.AuditTls, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at dao.AuditTls
	err := db.Where(INST_WHERE_RDS_INSTANCE_CONDITION, followInstanceID, logProductType).First(&at).Error
	return &at, err
}

func (a obInst) GetByTenantId(ctx context.Context, tenantId string, logProductType string) ([]*dao.AuditTls, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at []*dao.AuditTls
	err := db.Model(&dao.AuditTls{}).Where(INST_WHERE_TENANTID_CONDITION, tenantId, logProductType).Find(&at).Error
	return at, err
}

// UpdateByFollowInstanceID status与tls_id只能更新大于0的状态值
func (a obInst) UpdateByFollowInstanceID(ctx context.Context, cs *dao.AuditTls) error {
	updateField := make(map[string]interface{})
	if cs.Status != 0 {
		updateField["status"] = cs.Status
	}
	if cs.TlsId > 0 {
		updateField["tls_id"] = cs.TlsId
	}
	db := a.dbProvider.GetMetaDB(ctx)
	return db.Model(&dao.AuditTls{}).Where(INST_WHERE_CONDITION, cs.FollowInstanceID, cs.TenantID, cs.ProductType).Updates(updateField).Error
}

func (a obInst) Delete(ctx context.Context, followInstanceID string, tenantId string, logProductType string) error {
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.AuditTls{}).
		Where(INST_WHERE_CONDITION, followInstanceID, tenantId, logProductType).Updates(map[string]interface{}{"deleted": 1}).Error
	return err
}

func (a obInst) GetAllByTenantId(ctx context.Context, tenantId string, logProductType string, followInstanceID string, dsType string) ([]*dao.AuditTls, error) {
	cond := make(map[string]interface{})
	cond["tenant_id"] = tenantId
	cond["deleted"] = 0
	if logProductType != "" {
		cond["product_type"] = logProductType
	}
	if followInstanceID != "" {
		cond["follow_instance_id"] = followInstanceID
	}
	if dsType != "" {
		cond["db_type"] = dsType
	}
	db := a.dbProvider.GetMetaDB(ctx)
	var at []*dao.AuditTls
	tx := db.Model(&dao.AuditTls{})
	tx.Where(cond)
	err := tx.Find(&at).Error
	return at, err
}
