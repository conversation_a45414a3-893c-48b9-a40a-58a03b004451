package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"time"
)

type MigrationTaskDAL interface {
	Create(ctx context.Context, task *dao.MigrationTask) error
	List(ctx context.Context, instanceId string, taskType string, keyWords string, tenantId string, userId string, sortBy string, orderBy string, limit int64, offset int64, taskId string, instanceType string) (*dao.MigrationTasksInfo, error)
	ListActiveTasks(ctx context.Context, instanceId string, taskType string, tenantId string) (*dao.MigrationTasksInfo, error)
	Get(ctx context.Context, taskId int64, tenantID string) (*dao.MigrationTask, error)
	Update(ctx context.Context, task *dao.MigrationTask, tenantID string) error
	UpdateJobByTaskID(ctx context.Context, taskID int64, jobName string, clusterName string, tenantID string) error
	UpdateTaskStatusByTaskID(ctx context.Context, taskID int64, status uint8, tenantID string) error
	UpdateTaskConfigByTaskID(ctx context.Context, taskID int64, objectName string, config string, tenantID string) error
	UpdateTaskProgressPtByTaskID(ctx context.Context, taskID int64, processPt uint8, tenantID string) error
	ListFinishedJobs(ctx context.Context, status []model.DBMigrationStatus) (*dao.MigrationTasksInfo, error)
	Delete(ctx context.Context, taskIDs []int64, tenantID string) error
	CountUserFinishedTasks(ctx context.Context, userName string, startTime int64, endTime int64, taskType string, instanceId string) (int64, error)

	CountInnerRunningTasks(ctx context.Context, instanceId string, taskType string, tenantId string) (int64, error)
}

func NewMigrationTaskDAL(dbPro DBProvider) MigrationTaskDAL {
	return &migrationTaskDal{dbPro: dbPro}
}

type migrationTaskDal struct {
	dbPro DBProvider
}

func (dal *migrationTaskDal) ListFinishedJobs(ctx context.Context, status []model.DBMigrationStatus) (*dao.MigrationTasksInfo, error) {
	var MigrationTasks []*dao.MigrationTask
	var total int64
	db := dal.dbPro.GetMetaDB(ctx)
	query := "status IN (?) AND deleted=0 and from_type=0 AND job_name != ''"
	if err := db.Model(MigrationTasks).Where(query, status).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(query, status).Find(&MigrationTasks).Error; err != nil {
		return nil, err
	}
	ret := &dao.MigrationTasksInfo{
		Total:          total,
		MigrationTasks: MigrationTasks,
	}
	return ret, nil
}

func (dal *migrationTaskDal) UpdateTaskConfigByTaskID(ctx context.Context, taskID int64, objectName string, config string, tenantID string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.MigrationTask{}).Where("id=? AND tenant_id=?", taskID, tenantID).Updates(map[string]interface{}{
		"Config":     config,
		"ObjectName": objectName,
		"UpdatedAt":  time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save taskId %d config info error %v", taskID, err)
	}
	return nil
}

func (dal *migrationTaskDal) UpdateTaskStatusByTaskID(ctx context.Context, taskID int64, status uint8, tenantID string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.MigrationTask{}).Where("id=? AND tenant_id=?", taskID, tenantID).Updates(map[string]interface{}{
		"Status":    status,
		"UpdatedAt": time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save taskId %d status info error %v", taskID, err)
	}
	return nil
}

func (dal *migrationTaskDal) UpdateTaskProgressPtByTaskID(ctx context.Context, taskID int64, processPt uint8, tenantID string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.MigrationTask{}).Where("id=? AND tenant_id=?", taskID, tenantID).Updates(map[string]interface{}{
		"ProgressPt": processPt,
		"UpdatedAt":  time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save taskId %d progressPt info error %v", taskID, err)
	}
	return nil
}

func (dal *migrationTaskDal) UpdateJobByTaskID(ctx context.Context, taskID int64, jobName string, clusterName string, tenantID string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.MigrationTask{}).Where("id=? AND tenant_id=?", taskID, tenantID).Updates(map[string]interface{}{
		"JobName":     jobName,
		"ClusterName": clusterName,
		"UpdatedAt":   time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save job info error %v", err)
	}
	return nil
}
func (dal *migrationTaskDal) ListActiveTasks(ctx context.Context, instanceId string, taskType string, tenantId string) (*dao.MigrationTasksInfo, error) {
	var MigrationTasks []*dao.MigrationTask
	var total int64
	db := dal.dbPro.GetMetaDB(ctx)
	if err := db.Model(MigrationTasks).Where("instance_id=? and tenant_id=? and type=? and deleted=0 and from_type=0 and status NOT IN (2,5,6,7)", instanceId, tenantId, taskType).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where("instance_id=? and tenant_id=? and type=? and deleted=0 and from_type=0 and status NOT IN (2,5,6,7)", instanceId, tenantId, taskType).Find(&MigrationTasks).Error; err != nil {
		return nil, err
	}
	ret := &dao.MigrationTasksInfo{
		Total:          total,
		MigrationTasks: MigrationTasks,
	}
	return ret, nil
}

func (dal *migrationTaskDal) List(ctx context.Context, instanceId string, taskType string, keyWords string, tenantId string, userId string, sortBy string, orderBy string, limit int64, offset int64, taskId string, instanceType string) (*dao.MigrationTasksInfo, error) {
	var (
		MigrationTasks []*dao.MigrationTask
		total          int64
	)

	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery, conditions := generateConditions(instanceId, tenantId, taskType, keyWords, userId, taskId, instanceType)
	if err := db.Model(MigrationTasks).Where(baseQuery, conditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, conditions...).Offset(int(offset)).Limit(int(limit)).Order(orderBy + " " + sortBy).Find(&MigrationTasks).Error; err != nil {
		return nil, err
	}
	ret := &dao.MigrationTasksInfo{
		Total:          total,
		MigrationTasks: MigrationTasks,
	}
	return ret, nil
}

func generateConditions(instanceId string, tenantId string, taskType string, keyWords string, userId string, taskId string, instanceType string) (string, []interface{}) {
	var conditions []interface{}
	var baseQuery string
	if tenantId == "1" {
		baseQuery = "deleted=0 and from_type=0 "
	} else {
		baseQuery = "tenant_id=? and deleted=0 and from_type=0 "
		conditions = append(conditions, tenantId)
	}
	if instanceId != "" {
		baseQuery += "and instance_id=? "
		conditions = append(conditions, instanceId)
	}
	if taskType != "" {
		baseQuery += "and type=? "
		conditions = append(conditions, taskType)
	}
	if keyWords != "" {
		baseQuery += "and (db like ? or table_list like ?) "
		conditions = append(conditions, "%"+keyWords+"%")
		conditions = append(conditions, "%"+keyWords+"%")
	}
	if userId != "" {
		baseQuery += "and user_id=? "
		conditions = append(conditions, userId)
	}
	if taskId != "" {
		baseQuery += "and id=? "
		conditions = append(conditions, taskId)
	}
	if instanceType != "" {
		baseQuery += "and ds_type=? "
		conditions = append(conditions, instanceType)
	}
	return baseQuery, conditions
}

func (dal *migrationTaskDal) Update(ctx context.Context, task *dao.MigrationTask, tenantID string) (err error) {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.MigrationTask{}).Where("id=? AND tenant_id=?", task.ID, tenantID).Updates(map[string]interface{}{
		"Status":     task.Status,
		"ProgressPt": task.ProgressPt,
		"UpdatedAt":  time.Now().UnixNano() / 1e6,
	})

	if err = res.Error; err != nil {
		return fmt.Errorf("save job status and progress  error %v", err)
	}

	return nil

}

func (dal *migrationTaskDal) Delete(ctx context.Context, taskIDs []int64, tenantID string) (err error) {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.MigrationTask{}).Where("tenant_id=? and id in ?", tenantID, taskIDs).Updates(map[string]interface{}{
		"UpdatedAt":  time.Now().UnixNano() / 1e6,
		"deleted":    1,
		"deleted_at": time.Now().UnixNano() / 1e6,
	})
	if err = res.Error; err != nil {
		return fmt.Errorf("delete task error %v", err)
	}

	return nil
}

func (dal *migrationTaskDal) Create(ctx context.Context, task *dao.MigrationTask) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(task).Error
	if err != nil {
		return err
	}
	return err
}

func (dal *migrationTaskDal) Get(ctx context.Context, taskID int64, tenantID string) (*dao.MigrationTask, error) {
	var ret dao.MigrationTask
	db := dal.dbPro.GetMetaDB(ctx)
	//from ops
	if tenantID == "1" {
		err := db.Where("id=? and deleted=0", taskID).First(&ret).Error
		return &ret, err
	} else {
		err := db.Where("id=? and tenant_id=? and deleted=0", taskID, tenantID).First(&ret).Error
		return &ret, err
	}
}

func (dal *migrationTaskDal) CountUserFinishedTasks(ctx context.Context, userName string, startTime int64, endTime int64, taskType string, instanceId string) (int64, error) {
	var MigrationTasks []*dao.MigrationTask
	db := dal.dbPro.GetMetaDB(ctx)
	whereCase := " deleted=0 and from_type=0 AND status=6 AND user_name=? AND created_at >= ? AND created_at < ? AND type = ? AND instance_id = ? "
	var count int64
	if err := db.Model(MigrationTasks).Where(whereCase, userName, startTime, endTime, taskType, instanceId).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (dal *migrationTaskDal) CountInnerRunningTasks(ctx context.Context, instanceId string, taskType string, tenantId string) (int64, error) {
	var MigrationTasks []*dao.MigrationTask
	db := dal.dbPro.GetMetaDB(ctx)
	whereCase := " deleted=0 and from_type=1 AND status=4  AND type = ? AND instance_id = ? and tenant_id = ? "
	var count int64
	if err := db.Model(MigrationTasks).Where(whereCase, taskType, instanceId, tenantId).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
