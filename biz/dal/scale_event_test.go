package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockScaleEventDAL() ScaleEventDAL {
	return NewScaleEventDAL(&mockDBProvider{})
}

func TestScaleEventCreate(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleEventDAL()
	err := dal.Create(context.Background(), &dao.DbwAutoScaleEvent{})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Create(context.Background(), &dao.DbwAutoScaleEvent{})
	assert.Nil(t, err)
}

func TestUpdateScalingEvent(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Exec).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleEventDAL()
	err := dal.UpdateScalingEvent(context.Background(), 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.UpdateScalingEvent(context.Background(), 1)
	assert.Nil(t, err)
}

func TestUpdateScaleEndEvent(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Exec).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleEventDAL()
	err := dal.UpdateScaleEndEvent(context.Background(), 1, 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.UpdateScaleEndEvent(context.Background(), 1, 1)
	assert.Nil(t, err)
}

func TestUpdateScaleErrorEvent(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Exec).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleEventDAL()
	err := dal.UpdateScaleErrorEvent(context.Background(), "", 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.UpdateScaleErrorEvent(context.Background(), "", 1)
	assert.Nil(t, err)
}

func TestUpdateAutoScaleMetricValueEvent(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Exec).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleEventDAL()
	err := dal.UpdateAutoScaleMetricValueEvent(context.Background(), 1, "", "", "", 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.UpdateAutoScaleMetricValueEvent(context.Background(), 1, "", "", "", 1)
	assert.Nil(t, err)
}

func TestUpdateAutoScaleEndEvent(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Exec).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleEventDAL()
	err := dal.UpdateAutoScaleEndEvent(context.Background(), "", 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.UpdateAutoScaleEndEvent(context.Background(), "", 1)
	assert.Nil(t, err)
}

func TestUpdateAutoScaleErrorEvent(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Exec).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleEventDAL()
	err := dal.UpdateAutoScaleErrorEvent(context.Background(), "", 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.UpdateAutoScaleErrorEvent(context.Background(), "", 1)
	assert.Nil(t, err)
}

func TestGetScaleEvents(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Scan).Return(mockRes).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Order).Return(mockRes).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*gorm.DB).Count).Return(mockRes).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*gorm.DB).Model).Return(mockRes).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock6.UnPatch()
	mock7 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock7.UnPatch()
	mock8 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock8.UnPatch()
	mock9 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock9.UnPatch()
	mock10 := mockey.Mock((*gorm.DB).ToSQL).Return("").Build()
	defer mock10.UnPatch()
	mockRes.Error = nil

	dal := mockScaleEventDAL()
	_, err := dal.GetScaleEvents(context.Background(), "", "1", &model.AutoScaleSearchParam{}, "", 1, 1)
	assert.Nil(t, err)
}

func TestGetLatestFinishedEventByRuleId(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Order).Return(mockRes).Build()
	defer mock3.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleEventDAL()
	_, err := dal.GetLatestFinishedEventByRuleId(context.Background(), 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.GetLatestFinishedEventByRuleId(context.Background(), 1)
	assert.Nil(t, err)
}
