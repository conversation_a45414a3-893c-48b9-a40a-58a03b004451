package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockDataArchiveTaskDAL() DataArchiveTaskDAL {
	return NewDataArchiveTaskDAL(&mockDBProvider{})
}

func TestGetDataArchiveTask(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Find).Return(mockRes).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Limit).Return(mockRes).Build()
	defer mock3.UnPatch()
	mockRes.Error = gorm.ErrRecordNotFound

	dal := mockDataArchiveTaskDAL()
	_, err := dal.GetDataArchiveTask(context.Background(), 1)
	assert.Nil(t, err)

	mockRes.Error = nil
	_, err = dal.GetDataArchiveTask(context.Background(), 1)
	assert.Nil(t, err)
}

func TestUpdateTaskInfo(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock4.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDataArchiveTaskDAL()
	err := dal.UpdateTaskInfo(context.Background(), &dao.ArchiveTask{})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.UpdateTaskInfo(context.Background(), &dao.ArchiveTask{})
	assert.Nil(t, err)
}

func TestCreateArchiveTask(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDataArchiveTaskDAL()
	err := dal.CreateArchiveTask(context.Background(), &dao.ArchiveTask{})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.CreateArchiveTask(context.Background(), &dao.ArchiveTask{})
	assert.Nil(t, err)
}

func TestDeleteArchiveTask(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock4.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDataArchiveTaskDAL()
	err := dal.DeleteArchiveTask(context.Background(), 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.DeleteArchiveTask(context.Background(), 1)
	assert.Nil(t, err)
}

func TestListArchiveTasks(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock7 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock7.UnPatch()
	mockRes := &gorm.DB{}
	mock3 := mockey.Mock((*gorm.DB).Count).Return(mockRes).Build()
	defer mock3.UnPatch()

	mockRes.Error = fmt.Errorf("test")

	req := &model.DescribeArchiveTasksReq{}
	dal := mockDataArchiveTaskDAL()
	_, err := dal.ListArchiveTasks(context.Background(), "1", req)
	assert.NotNil(t, err)
	mockRes.Error = nil

	mock4 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*gorm.DB).Find).Return(mockRes).Build()
	defer mock6.UnPatch()
	_, err = dal.ListArchiveTasks(context.Background(), "1", req)
	assert.Nil(t, err)
}
