package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockApprovalNodeDAL() ApprovalFlowNodeDAL {
	return NewApprovalFlowNodeDAL(&mockDBProvider{})
}

func TestModifyApprovalNode(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock4.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockApprovalNodeDAL()
	err := dal.ModifyNode(context.Background(), &dao.ApprovalNode{})
	assert.NotNil(t, err)
}

func TestCreateApprovalNode(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockApprovalNodeDAL()
	err := dal.CreateNode(context.Background(), &dao.ApprovalNode{})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.CreateNode(context.Background(), &dao.ApprovalNode{})
	assert.Nil(t, err)
}

func TestDeleteApprovalNode(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock4.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockApprovalNodeDAL()
	err := dal.DeleteNode(context.Background(), 1)
	assert.NotNil(t, err)
}

func TestGetApprovalFlowNode(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Find).Return(mockRes).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Limit).Return(mockRes).Build()
	defer mock3.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockApprovalNodeDAL()
	_, err := dal.GetNode(context.Background(), 1)
	assert.NotNil(t, err)
}

func TestGetApprovalFlowNodesSum(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mockRes := &gorm.DB{}
	mock3 := mockey.Mock((*gorm.DB).Count).Return(mockRes).Build()
	defer mock3.UnPatch()

	mock4 := mockey.Mock((*ApprovalFlowNodeDal).listAllSystemApprovalNode).Return([]*dao.ApprovalNode{}, nil).Build()
	defer mock4.UnPatch()

	dal := mockApprovalNodeDAL()
	req := mockListNodeReq()
	_, err := dal.GetApprovalNodesSum(context.Background(), "1", req)
	assert.Nil(t, err)
}

func TestListUseApprovalNode(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock5 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock5.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Find).Return(mockRes).Build()
	defer mock4.UnPatch()

	dal := mockApprovalNodeDAL()
	req := mockListNodeReq()
	_, err := dal.ListUseApprovalNode(context.Background(), "1", req)
	assert.Nil(t, err)
}

func TestListSystemApprovalNode(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Find).Return(mockRes).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock5.UnPatch()

	dal := mockApprovalNodeDAL()
	req := mockListNodeReq()
	_, err := dal.ListSystemApprovalNode(context.Background(), req)
	assert.Nil(t, err)
}

func TestIsNodeInApprovalConfig(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Count).Return(mockRes).Build()
	defer mock4.UnPatch()

	mockRes.Error = fmt.Errorf("test")

	dal := mockApprovalNodeDAL()
	_, err := dal.IsNodeInApprovalConfig(context.Background(), "1", 1)
	assert.NotNil(t, err)
}

func mockListNodeReq() *model.ListApprovalNodeReq {
	str := ""
	configType := model.ConfigType_System
	return &model.ListApprovalNodeReq{
		NodeId:         &str,
		NodeName:       &str,
		Type:           &configType,
		CreateUser:     &str,
		ApprovalUser:   &str,
		ApprovalUserId: &str,
		CreateUserId:   &str,
		Memo:           &str,
		PageNumber:     0,
		PageSize:       0,
	}
}

func TestGetNodeByName(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Find).Return(mockRes).Build()
	defer mock4.UnPatch()
	mockRes.Error = fmt.Errorf("test")
	mock04 := mockey.Mock(fwctx.GetTenantID).Return("1").Build()
	defer mock04.UnPatch()

	dal := mockApprovalNodeDAL()
	_, err := dal.GetNodeByName(context.Background(), "xxx")
	assert.NotNil(t, err)
}
