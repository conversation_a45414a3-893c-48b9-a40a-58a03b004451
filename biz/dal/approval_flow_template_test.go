package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockApprovalFlowTemplateDAL() ApprovalFlowTemplateDAL {
	return NewApprovalFlowTemplateDAL(&mockDBProvider{})
}

func TestModifyApprovalFlowTemplateTask(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock4.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockApprovalFlowTemplateDAL()
	err := dal.ModifyFlowTemplate(context.Background(), &dao.ApprovalFlowTemplate{})
	assert.NotNil(t, err)
}

func TestCreateApprovalFlowTemplateTask(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockApprovalFlowTemplateDAL()
	err := dal.CreateFlowTemplate(context.Background(), &dao.ApprovalFlowTemplate{})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.CreateFlowTemplate(context.Background(), &dao.ApprovalFlowTemplate{})
	assert.Nil(t, err)
}

func TestGetApprovalFlowTemplate(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Limit).Return(mockRes).Build()
	defer mock3.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockApprovalFlowTemplateDAL()
	_, err := dal.GetTemplate(context.Background(), "1")
	assert.NotNil(t, err)
}

func TestDeleteApprovalFlowTemplate(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock4.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockApprovalFlowTemplateDAL()
	err := dal.DeleteApprovalFlowTemplate(context.Background(), 1)
	assert.NotNil(t, err)
}
