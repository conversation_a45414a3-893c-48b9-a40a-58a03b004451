package dal

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
)

type InspectionConfigDAL interface {
	Create(ctx context.Context, task *dao.InspectionConfig) error
	Get(ctx context.Context, instanceType string, instanceId string, tenantId string) (*dao.InspectionConfig, error)
	GetAll(ctx context.Context) ([]*dao.InspectionConfig, error)
	Update(ctx context.Context, config *dao.InspectionConfig) error
	ListAll(ctx context.Context, TenantId string, InstanceType string) ([]*dao.InspectionConfig, error)
}

func NewInspectionConfigDAL(dbPro DBProvider) InspectionConfigDAL {
	return &InspectionConfigDal{dbPro: dbPro}
}

type InspectionConfigDal struct {
	dbPro DBProvider
}

func (dal *InspectionConfigDal) Create(ctx context.Context, conf *dao.InspectionConfig) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(conf).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *InspectionConfigDal) Get(ctx context.Context, instanceType string, instanceId string, tenantId string) (*dao.InspectionConfig, error) {
	var ret dao.InspectionConfig
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "select * from " + dao.InspectionConfigTableName + " where instance_id=\"" + instanceId + "\" and instance_type=\"" + instanceType +
		"\" and tenant_id=\"" + tenantId + "\" "
	err := db.Raw(sqlStr).Scan(&ret).Error // ignore_security_alert
	return &ret, err
}

func (dal *InspectionConfigDal) GetAll(ctx context.Context) ([]*dao.InspectionConfig, error) {
	var ret []*dao.InspectionConfig
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "select * from " + dao.InspectionConfigTableName + " where is_open=1"
	log.Info(ctx, "inspection: sqlStr is %s", sqlStr)
	err := db.Raw(sqlStr).Scan(&ret).Error // ignore_security_alert
	return ret, err
}

func (dal *InspectionConfigDal) Update(ctx context.Context, config *dao.InspectionConfig) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.InspectionConfigTableName +
		" set is_open=" + conv.Int64ToStr(int64(config.IsOpen)) +
		", inspection_executable_start_time=\"" + conv.Int64ToStr(config.InspectionExecutableStartTime) + "\"" +
		", inspection_executable_end_time=\"" + conv.Int64ToStr(config.InspectionExecutableEndTime) + "\"" +
		" where instance_id=\"" + config.InstanceId + "\"" + " and tenant_id=\"" + config.TenantId + "\" "
	log.Info(ctx, "update sql is %s", sqlStr)
	err := db.Exec(sqlStr).Error // ignore_security_alert
	return err
}

func (dal *InspectionConfigDal) ListAll(ctx context.Context, TenantId string, InstanceType string) ([]*dao.InspectionConfig, error) {
	var (
		ret            []*dao.InspectionConfig
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and is_open=1 "
	baseConditions = append(baseConditions, TenantId)
	if InstanceType != "" {
		baseQuery += " and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if err := db.Where(baseQuery, baseConditions...).Find(&ret).Error; err != nil {
		return nil, err
	}
	return ret, nil
}
