package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type SQLAdvisorDAL interface {
	Create(ctx context.Context, sqlAdvisor *dao.SqlAdvisor) error
	UpdateTaskByID(ctx context.Context, taskId string, taskStatus string, memo string, result string) error
	GetTaskByID(ctx context.Context, taskId string) (*dao.SqlAdvisor, error)
}

func NewSQLAdvisorDAL(dbPro DBProvider) SQLAdvisorDAL {
	return &SQLAdvisorDal{dbPro: dbPro}
}

type SQLAdvisorDal struct {
	dbPro DBProvider
}

func (dal *SQLAdvisorDal) Create(ctx context.Context, sqlAdvisor *dao.SqlAdvisor) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(sqlAdvisor).Error
	return err
}

func (dal *SQLAdvisorDal) UpdateTaskByID(ctx context.Context, taskId string, taskStatus string, memo string, result string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	var res *gorm.DB
	if result == "" {
		res = db.Model(&dao.SqlAdvisor{}).Where("task_id=?", taskId).Updates(map[string]interface{}{
			"TaskStatus": taskStatus,
			"Memo":       memo,
			"ModifyTime": time.Now().Unix(),
		})
	} else {
		res = db.Model(&dao.SqlAdvisor{}).Where("task_id=?", taskId).Updates(map[string]interface{}{
			"TaskStatus": taskStatus,
			"Memo":       memo,
			"Result":     result,
			"ModifyTime": time.Now().Unix(),
		})
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("update SqlAdvisor %s info error, SqlAdvisor not exist", taskId)
	}
	if err := res.Error; err != nil {
		return fmt.Errorf("update SqlAdvisor %s info error %v", taskId, err)
	}
	return nil
}

func (dal *SQLAdvisorDal) GetTaskByID(ctx context.Context, taskId string) (*dao.SqlAdvisor, error) {
	var ret dao.SqlAdvisor
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("task_id=? ", taskId).First(&ret).Error
	return &ret, err
}
