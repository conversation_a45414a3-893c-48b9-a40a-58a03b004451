package dal

import (
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

type FavouriteSQLDal interface {
	Create(ctx context.Context, sql *dao.FavouriteSQL) error
	List(ctx context.Context, instanceId, DBName string) (sqls []*dao.FavouriteSQL, err error)
	Update(ctx context.Context, sql *dao.FavouriteSQL) error
	Get(ctx context.Context, id string) (*dao.FavouriteSQL, error)
	Delete(ctx context.Context, id string) error
}

func NewFavouriteSQLDal(dbPro DBProvider) FavouriteSQLDal {
	return &favouriteSQLDal{dbPro: dbPro}
}

type favouriteSQLDal struct {
	dbPro DBProvider
}

func (f *favouriteSQLDal) Create(ctx context.Context, sql *dao.FavouriteSQL) error {
	db := f.dbPro.GetMetaDB(ctx)
	return db.Create(sql).Error
}

func (f *favouriteSQLDal) List(ctx context.Context, instanceId, DBName string) (sqls []*dao.FavouriteSQL, err error) {
	db := f.dbPro.GetMetaDB(ctx)
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	query := db.Where(`instance_id=?`, instanceId)
	query = query.Where(`tenant_id=?`, tenantId)
	query = query.Where(`user_id=?`, userId)
	query = query.Where(`dbname=? or scope="Instance"`, DBName)
	err = query.Find(&sqls).Error
	return
}

func (f *favouriteSQLDal) Update(ctx context.Context, sql *dao.FavouriteSQL) error {
	db := f.dbPro.GetMetaDB(ctx)
	return db.Updates(sql).Error
}

func (f *favouriteSQLDal) Get(ctx context.Context, id string) (*dao.FavouriteSQL, error) {
	db := f.dbPro.GetMetaDB(ctx)
	var sql *dao.FavouriteSQL
	query := db.Where(`id=?`, id)
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	if tenantId != "" {
		query = query.Where(`tenant_id=?`, tenantId)
	}
	query = query.Where(`user_id=?`, userId)
	err := query.First(&sql).Error
	return sql, err
}

func (f *favouriteSQLDal) Delete(ctx context.Context, id string) error {
	db := f.dbPro.GetMetaDB(ctx)
	query := db.Where(`id=?`, id)
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	if tenantId != "" {
		query = query.Where(`tenant_id=?`, tenantId)
	}
	query = query.Where(`user_id=?`, userId)
	return query.Delete(&dao.FavouriteSQL{}).Error
}
