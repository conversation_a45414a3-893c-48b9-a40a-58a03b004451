package dal

import (
	"context"
	"fmt"
	"gorm.io/gorm"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

const (
	TLS_WHERE_CONDITION = "id=? and tenant_id=? and deleted=0"
)

type TlsDAL interface {
	Create(ctx context.Context, cs *dao.Tls) (int64, error)
	Get(ctx context.Context, id int64, tenantId string) (*dao.Tls, error)
	GetFromAdmin(ctx context.Context, id string) (*dao.Tls, error)
	GetByTopicId(ctx context.Context, id string, tenantId string) (*dao.Tls, error)
	Delete(ctx context.Context, id string, tenantId string) error
	GetProject(ctx context.Context, tenantId string, region string) (*dao.ProjectList, error)
	GetProjects(ctx context.Context, tenantId string, region string) ([]*dao.ProjectList, error)
	UpdateByID(ctx context.Context, TenantId string, cs *dao.Tls) error
}

func NewTlsDAL(provider DBProvider) TlsDAL {
	return &tls{dbProvider: provider}
}

type tls struct {
	dbProvider DBProvider
}

func (a tls) Create(ctx context.Context, at *dao.Tls) (int64, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.Tls{}).Create(at).Error
	return at.ID, err
}

func (a tls) Get(ctx context.Context, id int64, tenantId string) (*dao.Tls, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at dao.Tls
	err := db.Model(&dao.Tls{}).Where(TLS_WHERE_CONDITION, id, tenantId).First(&at).Error
	return &at, err
}

func (a tls) GetFromAdmin(ctx context.Context, id string) (*dao.Tls, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at dao.Tls
	err := db.Model(&dao.Tls{}).Where("id=? and deleted=0", id).First(&at).Error
	return &at, err
}

func (a tls) GetByTopicId(ctx context.Context, id string, tenantId string) (*dao.Tls, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at dao.Tls
	err := db.Model(&dao.Tls{}).Where("tls_topic_id=? and tenant_id=? and deleted=0", id, tenantId).First(&at).Error
	return &at, err
}

func (a tls) Delete(ctx context.Context, id string, tenantId string) error {
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.Tls{}).
		Where(TLS_WHERE_CONDITION, id, tenantId).Updates(map[string]interface{}{"deleted": 1}).Error
	return err
}

func (a tls) GetProject(ctx context.Context, tenantId string, region string) (*dao.ProjectList, error) {
	var pl dao.ProjectList
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Raw("select tls_project_id, count(1) as topic_num from dbw_tls "+
		"where tenant_id = ? and region = ? GROUP BY tls_project_id HAVING topic_num < 50 limit 1", tenantId, region).
		Scan(&pl).Error
	if pl.TlsProjectId == "" {
		return nil, gorm.ErrRecordNotFound
	}
	return &pl, err
}
func (a tls) GetProjects(ctx context.Context, tenantId string, region string) ([]*dao.ProjectList, error) {
	var pls []*dao.ProjectList
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Raw("select tls_project_id, count(1) as topic_num from dbw_tls "+
		"where tenant_id = ? and region = ? GROUP BY tls_project_id", tenantId, region).
		Scan(&pls).Error
	if len(pls) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	return pls, err
}

func (a tls) UpdateByID(ctx context.Context, TenantId string, cs *dao.Tls) error {
	db := a.dbProvider.GetMetaDB(ctx)
	res := db.Model(&dao.Tls{}).Where("id = ? and tenant_id=? and deleted=0", cs.ID, TenantId).Updates(map[string]interface{}{
		"TlsProjectId":   cs.TlsProjectId,
		"TlsTopicId":     cs.TlsTopicId,
		"TlsRuleId":      cs.TlsRuleId,
		"TlsHostGroupId": cs.TlsHostGroupId,
		"TlsEndpoint":    cs.TlsEndpoint,
		"Region":         cs.Region,
		"IndexVersion":   cs.IndexVersion,
		"Ttl":            cs.Ttl,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update DbwUser error %v", err)
	}
	return nil
}
