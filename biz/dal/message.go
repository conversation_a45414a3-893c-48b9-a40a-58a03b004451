package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/db"
	"context"
	"time"
)

const (
	defaultLimit = 10
)

type MessageDAL interface {
	Create(ctx context.Context, msgs []*dao.Message) error
	UpdateRate(ctx context.Context, enum model.RateModelReplyEnum, messageId int64) error
	GetMessage(ctx context.Context, messageID int64) (*dao.Message, error)
	GetLastedMessage(ctx context.Context, chatID int64) (msg *dao.Message, err error)
	GetMessages(ctx context.Context, chatID int64, cursor *int64, limit *int) (messages []*dao.Message, err error)
}

func NewMessageDAL(dbPro DBProvider) MessageDAL {
	return &messageDal{dbPro: dbPro}
}

type messageDal struct {
	dbPro DBProvider
}

func (m *messageDal) Create(ctx context.Context, msgs []*dao.Message) error {
	dbx := m.dbPro.GetMetaDB(ctx)
	return db.ExecWithinTransaction(dbx, func(d *db.DB) error {
		for _, msg := range msgs {
			if err := d.Create(msg).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

func (dal *messageDal) UpdateRate(ctx context.Context, ratedType model.RateModelReplyEnum, messageId int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.Message{}).Where("id=?", messageId).Updates(map[string]interface{}{
		"rated_type":  ratedType.String(),
		"rated_time":  time.Now().Unix(),
		"update_time": time.Now().Unix(),
	})
	if err := res.Error; err != nil {
		log.Warn(ctx, "MessageDAL UpdateRate err:%v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return nil
}

func (m *messageDal) GetMessage(ctx context.Context, messageID int64) (msg *dao.Message, err error) {
	dbx := m.dbPro.GetMetaDB(ctx)
	err = dbx.Where("id=?", messageID).First(&msg).Error
	return
}

func (m *messageDal) GetLastedMessage(ctx context.Context, chatID int64) (msg *dao.Message, err error) {
	dbx := m.dbPro.GetMetaDB(ctx)
	err = dbx.Where("chat_id=?", chatID).Order("id desc").Limit(1).First(&msg).Error
	return
}

func (m *messageDal) GetMessages(
	ctx context.Context,
	chatID int64,
	cursor *int64,
	limit *int) (messages []*dao.Message, err error) {
	dbx := m.dbPro.GetMetaDB(ctx)
	query := dbx.Where("chat_id=?", chatID)
	if cursor != nil {
		query.Where("id<?", *cursor)
	}
	query = query.Where(`state=0`).Order(`id DESC`)
	if limit == nil {
		limit = utils.IntRef(defaultLimit)
	}
	err = query.Limit(*limit).Find(&messages).Error
	return
}
