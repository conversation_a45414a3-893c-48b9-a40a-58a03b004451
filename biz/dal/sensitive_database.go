package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/framework/db"
	"context"
	"gorm.io/gorm"
)

type SensitiveDatabaseDal interface {
	Create(ctx context.Context, database *dao.SensitiveDatabase) error
	Get(ctx context.Context, instanceId, dbName string) (*dao.SensitiveDatabase, error)
	List(ctx context.Context, instanceId string) ([]*dao.SensitiveDatabase, error)
}

func NewSensitiveDatabaseDAL(dbPro DBProvider) SensitiveDatabaseDal {
	return &sensitiveDatabaseDal{dbPro: dbPro}
}

type sensitiveDatabaseDal struct {
	dbPro DBProvider
}

func (s *sensitiveDatabaseDal) Create(ctx context.Context, database *dao.SensitiveDatabase) error {
	dbx := s.dbPro.GetMetaDB(ctx)
	return db.ExecWithinTransaction(dbx, func(d *db.DB) error {
		if err := d.Model(&dao.SensitiveDatabase{}).Create(database).Error; err != nil {
			return err
		}
		if err := d.Model(&dao.DatabasePrivilege{}).Where("instance_id=?", database.InstanceId).Where("db_name=?", database.Name).Update("deleted", 1).Error; err != nil {
			return err
		}
		return nil
	})
}

func (s *sensitiveDatabaseDal) Get(ctx context.Context, instanceId, dbName string) (*dao.SensitiveDatabase, error) {
	dbx := s.dbPro.GetMetaDB(ctx)
	var db dao.SensitiveDatabase
	if err := dbx.Scopes(
		SensitiveDatabaseWithInstanceId(instanceId),
		SensitiveDatabaseWithName(dbName),
		SensitiveDatabaseWithExist).First(&db).Error; err != nil {
		return nil, err
	}
	return &db, nil
}

func (s *sensitiveDatabaseDal) List(ctx context.Context, instanceId string) ([]*dao.SensitiveDatabase, error) {
	dbx := s.dbPro.GetMetaDB(ctx)
	var dbs []*dao.SensitiveDatabase
	query := dbx.Model(&dao.SensitiveDatabase{}).Where(`deleted=0`)
	if instanceId != "" {
		query = query.Where(`instance_id=?`, instanceId)
	}
	err := query.Find(&dbs).Error
	return dbs, err
}

func SensitiveDatabaseWithInstanceId(instanceId string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(`instance_id=?`, instanceId)
	}
}

func SensitiveDatabaseWithName(dbName string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where(`name=?`, dbName)
	}
}

func SensitiveDatabaseWithExist(db *gorm.DB) *gorm.DB {
	return db.Where(`deleted=?`, 0)
}
