package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
)

type InspectionSlowLogDAL interface {
	Create(ctx context.Context, task *dao.InspectionSlowLog) error
	Get(ctx context.Context, taskId int64) ([]*dao.InspectionSlowLog, error)
}

func NewInspectionSlowLogDAL(dbPro DBProvider) InspectionSlowLogDAL {
	return &InspectionSlowLogDal{dbPro: dbPro}
}

type InspectionSlowLogDal struct {
	dbPro DBProvider
}

func (dal *InspectionSlowLogDal) Create(ctx context.Context, slowlog *dao.InspectionSlowLog) error {
	db := dal.dbPro.GetMetaDB(ctx)

	err := db.Where(" id = ? and number_id = ?  ", slowlog.ID, slowlog.NumberId).Save(&slowlog).Error
	//err := db.Create(slowlog).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *InspectionSlowLogDal) Get(ctx context.Context, taskId int64) ([]*dao.InspectionSlowLog, error) {
	var ret []*dao.InspectionSlowLog
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "select `id`, `number_id`, `instance_id`, `inspection_start_time`, " +
		" `inspection_end_time`, `sql_template`, `db_name`, `execute_user`, `execute_count`, " +
		" `total_query_time`, `avg_query_time`, `avg_lock_time`, `avg_rows_sent` " +
		" from dbw_inspection_slowlog where id = ? " // ignore_security_alert
	err := db.Raw(sqlStr, taskId).Scan(&ret).Error
	return ret, err
}
