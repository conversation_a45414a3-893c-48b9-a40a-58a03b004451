package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
)

type PreCheckDetailDAL interface {
	DescribeByTicketID(ctx context.Context, ticketId int64, tenantId string) ([]*dao.TicketPreCheckDetail, error)
	CreatePreCheckDetail(ctx context.Context, preCheckDetail []*dao.TicketPreCheckDetail) error
}

func NewPreCheckDetailDAL(provider DBProvider) PreCheckDetailDAL {
	return &PreCheckDetail{dbProvider: provider}
}

type PreCheckDetail struct {
	dbProvider DBProvider
}

func (selfWorkflow *PreCheckDetail) DescribeByTicketID(ctx context.Context, ticketId int64, tenantId string) ([]*dao.TicketPreCheckDetail, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	var result []*dao.TicketPreCheckDetail
	sqlStr := " ticket_id=? and tenant_id=? "
	if err := db.Model(&dao.TicketPreCheckDetail{}).
		Where(sqlStr, ticketId, tenantId).Scan(&result).Error; err != nil {
		return nil, fmt.Errorf("describe ticket precheck detail %d by ticketId error: %s ", ticketId, err.Error())
	}
	if len(result) == 0 {
		return []*dao.TicketPreCheckDetail{}, nil
	}
	return result, nil
}

func (selfWorkflow *PreCheckDetail) CreatePreCheckDetail(ctx context.Context, preCheckDetail []*dao.TicketPreCheckDetail) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	err := db.Save(preCheckDetail).Error
	return err
}
