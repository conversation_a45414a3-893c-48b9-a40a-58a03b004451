package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
)

type InspectionReportDAL interface {
	Create(ctx context.Context, task *dao.InspectionReport) error
	Get(ctx context.Context, taskId int64) (*dao.InspectionReport, error)
}

func NewInspectionReportDAL(dbPro DBProvider) InspectionReportDAL {
	return &InspectionReportDal{dbPro: dbPro}
}

type InspectionReportDal struct {
	dbPro DBProvider
}

func (dal *InspectionReportDal) Create(ctx context.Context, report *dao.InspectionReport) error {
	db := dal.dbPro.GetMetaDB(ctx)
	// replace into
	err := db.Where(" task_id = ? ", report.TaskID).Save(&report).Error
	//err := db.Create(report).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *InspectionReportDal) Get(ctx context.Context, taskId int64) (*dao.InspectionReport, error) {
	var ret dao.InspectionReport
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("task_id=? ", taskId).First(&ret).Error
	return &ret, err
}
