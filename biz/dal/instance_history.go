package dal

import (
	"code.byted.org/gopkg/gorm"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	. "gorm.io/gorm"
)

type InstanceHistoryDAL interface {
	Create(ctx context.Context, history *dao.InstanceHistory) error
	List(ctx context.Context, query *dao.HistoryQuery, limit int32, offset int32) (*dao.HistoryList, error)
	Get(ctx context.Context, query *dao.HistoryQuery) (*dao.InstanceHistory, error)
	Update(ctx context.Context, history *dao.InstanceHistory) error
	Delete(ctx context.Context, id string) error
	DeleteAll(ctx context.Context) error
}

func NewInstanceHistoryDAL(dbPro DBProvider) InstanceHistoryDAL {
	return &instanceHistory{dbPro: dbPro}
}

type instanceHistory struct {
	dbPro DBProvider
}

func (c *instanceHistory) Create(ctx context.Context, history *dao.InstanceHistory) error {
	dbx := c.dbPro.GetMetaDB(ctx)
	return dbx.Create(history).Error
}

func (c *instanceHistory) List(ctx context.Context, query *dao.HistoryQuery, limit int32, offset int32) (*dao.HistoryList, error) {
	var (
		Histories      []*dao.InstanceHistory
		total          int64
		baseConditions []interface{}
		baseQuery      string
		err            error
	)
	db := c.dbPro.GetMetaDB(ctx)
	orderBy := "login_time"
	sort := "desc"
	baseQuery += " user_id =? "
	baseConditions = append(baseConditions, fwctx.GetUserID(ctx))
	if query.InstanceType != "" {
		switch query.InstanceType {
		case model.DSType_ByteRDS.String():
			baseQuery += " and (instance_type = ? or instance_type = ? or instance_type = ? or instance_type = ?) "
			baseConditions = append(baseConditions, model.DSType_ByteRDS.String())
			baseConditions = append(baseConditions, model.DSType_MySQL.String())
			baseConditions = append(baseConditions, model.DSType_MySQLSharding.String())
			baseConditions = append(baseConditions, model.DSType_VeDBMySQL.String())
		case model.DSType_ByteDoc.String():
			baseQuery += " and (instance_type = ? or instance_type = ?) "
			baseConditions = append(baseConditions, model.DSType_ByteDoc.String())
			baseConditions = append(baseConditions, model.DSType_Mongo.String())
		default:
			baseQuery += " and instance_type = ? "
			baseConditions = append(baseConditions, query.InstanceType)
		}
	}
	if query.InstanceId != "" {
		baseQuery += " and instance_id like ? "
		baseConditions = append(baseConditions, "%"+query.InstanceId+"%")
	}
	if query.InstanceName != "" {
		baseQuery += " and instance_name like ? "
		baseConditions = append(baseConditions, "%"+query.InstanceName+"%")
	}
	if query.Keyword != "" {
		baseQuery += " and (instance_id like ? or instance_name like ?) "
		baseConditions = append(baseConditions, "%"+query.Keyword+"%")
		baseConditions = append(baseConditions, "%"+query.Keyword+"%")
	}
	if query.RegionId != "" {
		baseQuery += " and region_id =? "
		baseConditions = append(baseConditions, query.RegionId)
	}
	if query.SortBy != "" {
		sort = query.SortBy
	}
	if err = db.Model(Histories).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err = db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Order(orderBy + " " + sort).Find(&Histories).Error; err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Order(orderBy + " " + sort).Find(&Histories)
	}))
	ret := &dao.HistoryList{
		Total: int32(total),
		Items: Histories,
	}
	return ret, err
}

func (c *instanceHistory) Get(ctx context.Context, query *dao.HistoryQuery) (*dao.InstanceHistory, error) {
	var (
		ret            dao.InstanceHistory
		baseConditions []interface{}
		baseQuery      string
		err            error
	)
	db := c.dbPro.GetMetaDB(ctx)
	baseQuery += "user_id =? "
	baseConditions = append(baseConditions, fwctx.GetUserID(ctx))
	if query.InstanceType != "" {
		baseQuery += " and instance_type = ? "
		baseConditions = append(baseConditions, query.InstanceType)
	}
	if query.InstanceId != "" {
		baseQuery += " and instance_id like ? "
		baseConditions = append(baseConditions, "%"+query.InstanceId+"%")
	}
	err = db.Where(baseQuery, baseConditions...).First(&ret).Error
	if err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).First(&ret)
	}))
	return &ret, nil
}

func (c *instanceHistory) Update(ctx context.Context, history *dao.InstanceHistory) error {
	dbx := c.dbPro.GetMetaDB(ctx)
	return dbx.Updates(history).Error
}

func (c *instanceHistory) Delete(ctx context.Context, id string) error {
	dbx := c.dbPro.GetMetaDB(ctx)
	db := dbx.Where(`id=?`, id).Where(`user_id=?`, fwctx.GetUserID(ctx)).Delete(&dao.InstanceHistory{})
	if err := db.Error; err != nil {
		return err
	}
	if db.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

func (c *instanceHistory) DeleteAll(ctx context.Context) error {
	dbx := c.dbPro.GetMetaDB(ctx)
	db := dbx.Where(`user_id=?`, fwctx.GetUserID(ctx)).Delete(&dao.InstanceHistory{})
	if err := db.Error; err != nil {
		return err
	}
	if db.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}
