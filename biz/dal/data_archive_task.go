package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"gorm.io/gorm"
	"time"
)

type DataArchiveTaskDAL interface {
	GetDataArchiveTask(ctx context.Context, archiveTaskId int64) (*dao.ArchiveTask, error)
	UpdateTaskInfo(ctx context.Context, archiveTask *dao.ArchiveTask) error
	CreateArchiveTask(ctx context.Context, archiveTask *dao.ArchiveTask) error
	DeleteArchiveTask(ctx context.Context, archiveTaskId int64) error
	ListArchiveTasks(ctx context.Context, tenantId string, req *model.DescribeArchiveTasksReq) (*dao.ListArchiveTasksResp, error)
}

func NewDataArchiveTaskDAL(provider DBProvider) DataArchiveTaskDAL {
	return &DataArchiveTaskDal{dbProvider: provider}
}

type DataArchiveTaskDal struct {
	dbProvider DBProvider
}

func (dal *DataArchiveTaskDal) GetDataArchiveTask(ctx context.Context, archiveTaskId int64) (*dao.ArchiveTask, error) {
	db := dal.dbProvider.GetMetaDB(ctx)
	var task *dao.ArchiveTask
	err := db.Where("task_id=? and deleted = 0 ", archiveTaskId).Limit(1).Find(&task).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		log.Warn(ctx, "ErrRecordNotFound")
		return nil, nil
	}
	if err == nil && (task == nil || task.TaskId == 0) {
		return nil, nil
	}
	return task, err
}

func (dal *DataArchiveTaskDal) UpdateTaskInfo(ctx context.Context, archiveTask *dao.ArchiveTask) error {
	db := dal.dbProvider.GetMetaDB(ctx)

	err := db.Model(&dao.ArchiveTask{}).Where("task_id = ? ", archiveTask.TaskId).Updates(map[string]interface{}{
		"SqlTaskId":    archiveTask.SqlTaskId,
		"DeleteSql":    archiveTask.DeleteSql,
		"ExecTime":     archiveTask.ExecTime,
		"TaskStatus":   archiveTask.TaskStatus,
		"ErrorMsg":     archiveTask.ErrorMsg,
		"BackupTaskId": archiveTask.BackupTaskId,
		"UpdateTime":   time.Now().Unix(),
		"AffectedRows": archiveTask.AffectedRows,
	}).Error

	return err
}

func (dal *DataArchiveTaskDal) CreateArchiveTask(ctx context.Context, archiveTask *dao.ArchiveTask) error {
	db := dal.dbProvider.GetMetaDB(ctx)
	err := db.Create(archiveTask).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *DataArchiveTaskDal) DeleteArchiveTask(ctx context.Context, archiveTaskId int64) error {
	db := dal.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.ArchiveTask{}).Where("task_id = ? ", archiveTaskId).Updates(map[string]interface{}{
		"Deleted":    1,
		"UpdateTime": time.Now().Unix(),
	}).Error
	return err
}

func (dal *DataArchiveTaskDal) ListArchiveTasks(ctx context.Context, tenantId string, req *model.DescribeArchiveTasksReq) (*dao.ListArchiveTasksResp, error) {
	db := dal.dbProvider.GetMetaDB(ctx)
	whereCase := " tenant_id = ? and ticket_id = ? and deleted = 0 "

	var total int64
	err := db.Model(&dao.ArchiveTask{}).Where(whereCase, tenantId, req.TicketId).Count(&total).Error
	if err != nil {
		log.Warn(ctx, "ticketId:%s get total task error:%s", req.TicketId, err.Error())
		return nil, err
	}

	limit := int(req.PageSize)
	offset := int(req.PageSize * (req.PageNumber - 1))
	var res []*dao.ArchiveTask
	err = db.Where(whereCase, tenantId, req.TicketId).Offset(offset).Limit(limit).Order("create_time DESC").Find(&res).Error
	if err != nil {
		log.Warn(ctx, "ticketId:%s get tasks error:%s", req.TicketId, err.Error())
		return nil, err
	}
	return &dao.ListArchiveTasksResp{
		Tasks: res,
		Total: int32(total),
	}, nil
}
