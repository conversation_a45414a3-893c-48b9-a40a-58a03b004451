package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"time"
)

type SqlCCLEventDAL interface {
	Create(ctx context.Context, Event *dao.SqlCCLEvent) error
	UpdateStatusByID(ctx context.Context, ID int64, status int8) error
}

func NewSqlCCLEventDAL(dbPro DBProvider) SqlCCLEventDAL {
	return &SqlCCLEventDal{dbPro: dbPro}
}

type SqlCCLEventDal struct {
	dbPro DBProvider
}

func (dal *SqlCCLEventDal) UpdateStatusByID(ctx context.Context, ID int64, status int8) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SqlCCLEvent{}).Where("id=? ", ID).Updates(map[string]interface{}{
		"State":     status,
		"UpdatedAt": time.Now().UnixNano() / 1e6,
	})
	if res.RowsAffected == 0 {
		return fmt.Errorf("save event %d status %d info error, event not exist ", ID, status)
	}
	if err := res.Error; err != nil {
		return fmt.Errorf("save event %d status %d info error %v", ID, status, err)
	}
	return nil
}

func (dal *SqlCCLEventDal) Create(ctx context.Context, Event *dao.SqlCCLEvent) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(Event).Error
	return err
}
