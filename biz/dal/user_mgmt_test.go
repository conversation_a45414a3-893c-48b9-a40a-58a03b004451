package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockDbwUserDAL() DbwUserDAL {
	return NewDbwUserDAL(&mockDBProvider{})
}

func TestDbwUserGet(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDbwUserDAL()
	_, err := dal.Get(context.Background(), "2", "1")
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.Get(context.Background(), "2", "1")
	assert.Nil(t, err)
}

func TestDbwUserCreate(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDbwUserDAL()
	dbwUser := &dao.DbwUser{
		ID:        0,
		TenantID:  "1",
		UpdatedAt: 0,
		Deleted:   0,
	}
	err := dal.Create(context.Background(), dbwUser)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Create(context.Background(), dbwUser)
	assert.Nil(t, err)
}

func TestDbwUserDelete(t *testing.T) {
	mock0 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")
	mock3 := mockey.Mock((*gorm.DB).Exec).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*gorm.DB).Begin).Return(&gorm.DB{}).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*gorm.DB).Commit).Return(&gorm.DB{}).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*gorm.DB).Rollback).Return(&gorm.DB{}).Build()
	defer mock6.UnPatch()
	dal := mockDbwUserDAL()
	err := dal.Delete(context.Background(), "2", "1")
	//assert.NotNil(t, err)
	//mockRes.Error = nil
	//err = dal.Delete(context.Background(), "2", "1")
	assert.Nil(t, err)
}

func TestDbwUserUpdate(t *testing.T) {
	mock0 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDbwUserDAL()
	err := dal.Update(context.Background(), "2", "1", "3", 1, 1, 1, 1, "x", "x", "x", "x", "x")
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Update(context.Background(), "2", "1", "3", 1, 1, 1, 1, "x", "x", "x", "x", "x")
	assert.Nil(t, err)
}

func TestDbwUserUpdateCurCount(t *testing.T) {
	mock0 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDbwUserDAL()
	err := dal.UpdateCurCount(context.Background(), "2", "1", 1, 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.UpdateCurCount(context.Background(), "2", "1", 1, 1)
	assert.Nil(t, err)
}

func TestDbwUsrResetCurrentCount(t *testing.T) {
	mock0 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDbwUserDAL()
	err := dal.ResetCurrentCount(context.Background(), 0, 0, "3", "1")
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.ResetCurrentCount(context.Background(), 0, 0, "3", "x")
	assert.Nil(t, err)
}
func TestDbwUsrGetUserByName(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Find).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockDbwUserDAL()
	_, err := dal.GetUserByName(context.Background(), "2", "1")
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.GetUserByName(context.Background(), "2", "1")
	assert.Nil(t, err)
}

func TestDbwUsrList(t *testing.T) {

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mockRes := &gorm.DB{}
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes.Error = nil
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mockRes2 := &gorm.DB{}
	mock51 := mockey.Mock((*gorm.DB).Find).Return(mockRes2).Build()
	defer mock51.UnPatch()
	mockRes2.Error = fmt.Errorf("test_find")
	dal := mockDbwUserDAL()
	_, err := dal.List(context.Background(), "2", "1", "1", []string{}, 10, 0)
	assert.NotNil(t, err)
	mockRes2.Error = nil
	_, err = dal.List(context.Background(), "2", "1", "1", []string{}, 10, 0)
	assert.Nil(t, err)
}
