package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockInspectionTaskDAL() InspectionTaskDAL {
	return NewInspectionTaskDAL(&mockDBProvider{})
}

func TestInspectionCreate(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockInspectionTaskDAL()
	err := dal.Create(context.Background(), &dao.InspectionTask{})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Create(context.Background(), &dao.InspectionTask{})
	assert.Nil(t, err)
}

func TestInspectionGet(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockInspectionTaskDAL()
	_, err := dal.Get(context.Background(), 1, "1")
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.Get(context.Background(), 1, "1")
	assert.Nil(t, err)
}

func TestGetAll(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

}
