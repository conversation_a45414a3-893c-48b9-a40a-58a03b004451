package dal

import (
	"context"
	"gorm.io/gorm"
	"strconv"
	"testing"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"github.com/bytedance/mockey"
)

func mockInstancePrivilegeDAL() InstancePrivilegeDAL {
	return NewInstancePrivilegeDAL(&mockDBProvider{})
}
func mockDatabasePrivilegeDAL() DatabasePrivilegeDAL {
	return NewDatabasePrivilegeDAL(&mockDBProvider{})
}
func mockTablePrivilegeDAL() TablePrivilegeDAL {
	return NewTablePrivilegeDAL(&mockDBProvider{})
}
func mockColumnPrivilegeDAL() ColumnPrivilegeDAL {
	return NewColumnPrivilegeDAL(&mockDBProvider{})
}

func Test_InstancePrivilegeDalList(t *testing.T) {
	dal := mockInstancePrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	TenantId := "testTenant"
	State := model.PermState_NORMAL.String()
	UserId := "testUser"
	GroupId := int64(123)
	PermIds := []string{"1", "2", "3"}
	keyword := "testInstance"
	DatabaseType := "testType"
	limit := int32(10)
	offset := int32(0)
	InstanceId := "InstanceId"
	PrivilegeType := "testPrivilegeType"
	_, err := dal.List(context.Background(), TenantId, State, UserId, strconv.FormatInt(GroupId, 10), PermIds, keyword, DatabaseType, limit, offset, InstanceId, PrivilegeType)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func Test_DatabasePrivilegeDalList(t *testing.T) {
	dal := mockDatabasePrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	TenantId := "testTenant"
	State := model.PermState_NORMAL.String()
	UserId := "testUser"
	GroupId := int64(123)
	PermIds := []string{"1", "2", "3"}
	keyword := "testDatabase"
	DatabaseType := "testType"
	limit := int32(10)
	offset := int32(0)
	InstanceId := "InstanceId"
	PrivilegeType := "testPrivilegeType"
	_, err := dal.List(context.Background(), TenantId, State, UserId, strconv.FormatInt(GroupId, 10), PermIds, keyword, DatabaseType, limit, offset, InstanceId, PrivilegeType, true)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func Test_TablePrivilegeDalList(t *testing.T) {
	dal := mockTablePrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	TenantId := "testTenant"
	State := model.PermState_NORMAL.String()
	UserId := "testUser"
	GroupId := int64(123)
	PermIds := []string{"1", "2", "3"}
	keyword := "testTable"
	DatabaseType := "testType"
	limit := int32(10)
	offset := int32(0)
	InstanceId := "InstanceId"
	Database := "Database"
	PrivilegeType := "testPrivilegeType"
	_, err := dal.List(context.Background(), TenantId, State, UserId, strconv.FormatInt(GroupId, 10), PermIds, keyword, DatabaseType, limit, offset, InstanceId, PrivilegeType, Database, true)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func Test_ColumnPrivilegeDalList(t *testing.T) {
	dal := mockColumnPrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	TenantId := "testTenant"
	State := model.PermState_NORMAL.String()
	UserId := "testUser"
	GroupId := int64(123)
	PermIds := []string{"1", "2", "3"}
	keyword := "testColumn"
	DatabaseType := "testType"
	limit := int32(10)
	offset := int32(0)
	InstanceId := "InstanceId"
	Database := "Database"
	Table := "Table"
	PrivilegeType := "testPrivilegeType"
	_, err := dal.List(context.Background(), TenantId, State, UserId, strconv.FormatInt(GroupId, 10), PermIds, keyword, DatabaseType, limit, offset, InstanceId, PrivilegeType, Database, Table, true)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func Test_InstancePrivilegeDalListByResource(t *testing.T) {
	dal := mockInstancePrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	TenantId := "testTenant"
	InstanceId := "InstanceId"
	PrivilegeType := "testPrivilegeType"
	_, err := dal.ListByResource(context.Background(), InstanceId, PrivilegeType, TenantId, true, []string{"user1"}, []string{"group1"})
	if err != nil {
		t.Fatal("failed", err)
	}
}

func Test_DatabasePrivilegeDalListByResource(t *testing.T) {
	dal := mockDatabasePrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	TenantId := "testTenant"
	InstanceId := "InstanceId"
	Database := "Database"
	PrivilegeType := "testPrivilegeType"
	_, err := dal.ListByResource(context.Background(), InstanceId, Database, PrivilegeType, TenantId, true, []string{"user1"}, []string{"group1"})
	if err != nil {
		t.Fatal("failed", err)
	}
}

func Test_TablePrivilegeDalListByResource(t *testing.T) {
	dal := mockTablePrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	TenantId := "testTenant"
	InstanceId := "InstanceId"
	Database := "Database"
	Table := "Table"
	PrivilegeType := "testPrivilegeType"
	_, err := dal.ListByResource(context.Background(), InstanceId, Database, Table, PrivilegeType, TenantId, true, []string{"user1"}, []string{"group1"})
	if err != nil {
		t.Fatal("failed", err)
	}
}

func Test_ColumnPrivilegeDalListByResource(t *testing.T) {
	dal := mockColumnPrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	TenantId := "testTenant"
	InstanceId := "InstanceId"
	Database := "Database"
	Table := "Table"
	Column := "Column"
	PrivilegeType := "testPrivilegeType"
	_, err := dal.ListByResource(context.Background(), InstanceId, Database, Table, Column, PrivilegeType, TenantId, true, []string{"user1"}, []string{"group1"})
	if err != nil {
		t.Fatal("failed", err)
	}
}

func Test_InstancePrivilegeDalListAndDelete(t *testing.T) {
	dal := mockInstancePrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()
	mock71 := mockey.Mock((*gorm.DB).Updates).Return(&gorm.DB{}).Build()
	defer mock71.UnPatch()

	TenantId := "testTenant"
	UserId := "testUser"
	GroupId := "123"
	DatabaseType := "testType"
	InstanceId := "InstanceId"
	PrivilegeType := "testPrivilegeType"
	err := dal.ListAndDelete(context.Background(), TenantId, UserId, GroupId, DatabaseType, InstanceId, PrivilegeType)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func Test_DatabasePrivilegeDalListAndDelete(t *testing.T) {
	dal := mockDatabasePrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()
	mock71 := mockey.Mock((*gorm.DB).Updates).Return(&gorm.DB{}).Build()
	defer mock71.UnPatch()

	TenantId := "testTenant"
	UserId := "testUser"
	GroupId := "123"
	DatabaseType := "testType"
	InstanceId := "InstanceId"
	Database := "Database"
	PrivilegeType := "testPrivilegeType"
	err := dal.ListAndDelete(context.Background(), TenantId, UserId, GroupId, DatabaseType, InstanceId, Database, PrivilegeType)
	if err != nil {
		t.Fatal("failed", err)
	}
}
func Test_TablePrivilegeDalListAndDelete(t *testing.T) {
	dal := mockTablePrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()
	mock71 := mockey.Mock((*gorm.DB).Updates).Return(&gorm.DB{}).Build()
	defer mock71.UnPatch()

	TenantId := "testTenant"
	UserId := "testUser"
	GroupId := "123"
	DatabaseType := "testType"
	InstanceId := "InstanceId"
	Database := "Database"
	Table := "Table"
	PrivilegeType := "testPrivilegeType"
	err := dal.ListAndDelete(context.Background(), TenantId, UserId, GroupId, DatabaseType, InstanceId, Database, Table, PrivilegeType)
	if err != nil {
		t.Fatal("failed", err)
	}
}
func Test_ColumnPrivilegeDalListAndDelete(t *testing.T) {
	dal := mockColumnPrivilegeDAL()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()
	mock71 := mockey.Mock((*gorm.DB).Updates).Return(&gorm.DB{}).Build()
	defer mock71.UnPatch()

	TenantId := "testTenant"
	UserId := "testUser"
	GroupId := "123"
	DatabaseType := "testType"
	InstanceId := "InstanceId"
	Database := "Database"
	Table := "Table"
	Column := "Column"
	PrivilegeType := "testPrivilegeType"
	err := dal.ListAndDelete(context.Background(), TenantId, UserId, GroupId, DatabaseType, InstanceId, Database, Table, Column, PrivilegeType)
	if err != nil {
		t.Fatal("failed", err)
	}
}
