package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
)

type InstanceExtraTlsDAL interface {
	Create(ctx context.Context, task dao.InstanceExtraTls) (int64, error)
	Update(ctx context.Context, task dao.InstanceExtraTls) error
	Get(ctx context.Context, instance string) ([]*dao.InstanceExtraTls, error)
	GetByID(ctx context.Context, id int64) (*dao.InstanceExtraTls, error)
	GetByDataType(ctx context.Context, tenantId string, dataType string) (*dao.InstanceExtraTls, error)
	Delete(ctx context.Context, id string, tenantId string) error
	GetByInstanceAndType(ctx context.Context, instanceId string, dataType string) (*dao.InstanceExtraTls, error)
}

type instanceExtraTlsDal struct {
	DbProvider DBProvider
}

func (s instanceExtraTlsDal) Create(ctx context.Context, statisticSqlTls dao.InstanceExtraTls) (int64, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.InstanceExtraTls{}).Create(&statisticSqlTls).Error
	return statisticSqlTls.Id, err
}

func (s instanceExtraTlsDal) Update(ctx context.Context, statisticSqlTls dao.InstanceExtraTls) error {
	db := s.DbProvider.GetMetaDB(ctx)
	return db.Model(&dao.InstanceExtraTls{}).
		Where(" id = ? ", statisticSqlTls.Id).
		Updates(&statisticSqlTls).Error
}

func (s instanceExtraTlsDal) Get(ctx context.Context, instanceId string) ([]*dao.InstanceExtraTls, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	var statisticSqlTls []*dao.InstanceExtraTls
	err := db.Model(&dao.InstanceExtraTls{}).
		Where(" instance_id = ? ", instanceId).
		Where(" deleted = 0 ").
		Find(&statisticSqlTls).Error
	return statisticSqlTls, err
}

func (s instanceExtraTlsDal) GetByID(ctx context.Context, id int64) (*dao.InstanceExtraTls, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	var statisticSqlTls dao.InstanceExtraTls
	err := db.Model(&dao.InstanceExtraTls{}).
		Where(" id = ? ", id).
		Where(" deleted = 0 ").
		First(&statisticSqlTls).Error
	return &statisticSqlTls, err
}

func (s instanceExtraTlsDal) GetByInstanceAndType(ctx context.Context, instanceId string, dataType string) (*dao.InstanceExtraTls, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	var statisticSqlTls dao.InstanceExtraTls
	err := db.Model(&dao.InstanceExtraTls{}).
		Where(" instance_id = ? ", instanceId).
		Where(" data_type = ? ", dataType).
		Where(" deleted = 0 ").
		First(&statisticSqlTls).Error
	return &statisticSqlTls, err
}

func (s instanceExtraTlsDal) GetByDataType(ctx context.Context, tenantId string, dataType string) (*dao.InstanceExtraTls, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	var statisticSqlTls dao.InstanceExtraTls
	err := db.Model(&dao.InstanceExtraTls{}).
		Where(" tenant_id = ? ", tenantId).
		Where(" data_type = ? ", dataType).
		Where(" deleted = 0 ").
		First(&statisticSqlTls).Error
	return &statisticSqlTls, err
}

func (s instanceExtraTlsDal) Delete(ctx context.Context, id string, tenantId string) error {
	db := s.DbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.InstanceExtraTls{}).
		Where(" id = ? ", id).
		Where(" tenant_id = ? ", tenantId).
		Where(" deleted = 0 ").
		Updates(map[string]interface{}{"deleted": 1}).Error
	return err
}

func NewInstanceExtraTlsDAL(dbProvider DBProvider) InstanceExtraTlsDAL {
	return &instanceExtraTlsDal{
		DbProvider: dbProvider,
	}
}
