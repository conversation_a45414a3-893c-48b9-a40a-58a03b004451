package dal

import (
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

// AuditTlsDAL 仅限审计实例的查询
type AuditTlsDAL interface {
	Create(ctx context.Context, cs *dao.AuditTls) (int64, error)
	GetByFollowInstanceID(ctx context.Context, followInstanceID string, tenantId string) (*dao.AuditTls, error)
	GetByID(ctx context.Context, instanceID string, tenantId string) (*dao.AuditTls, error)
	GetAll(ctx context.Context) ([]*dao.AuditTls, error)
	GetByFollowInstanceIDWithoutTenant(ctx context.Context, followInstanceID string) (*dao.AuditTls, error)
	GetByTenantId(ctx context.Context, tenantId string) ([]*dao.AuditTls, error)
	UpdateByFollowInstanceID(ctx context.Context, cs *dao.AuditTls) error
	Delete(ctx context.Context, instanceId string, tenantId string) error
}

func NewAuditTlsDAL(obInstDAL ObInstDAL) AuditTlsDAL {
	return &auditTls{
		obInstDAL: obInstDAL,
	}
}

type auditTls struct {
	obInstDAL ObInstDAL
}

func (a auditTls) Create(ctx context.Context, at *dao.AuditTls) (int64, error) {
	at.ProductType = model.LogProductType_AuditLog.String()
	return a.obInstDAL.Create(ctx, at)
}

func (a auditTls) GetByFollowInstanceID(ctx context.Context, followInstanceID string, tenantId string) (*dao.AuditTls, error) {
	return a.obInstDAL.GetByFollowInstanceID(ctx, followInstanceID, tenantId, model.LogProductType_AuditLog.String())
}

func (a auditTls) GetByID(ctx context.Context, id string, tenantId string) (*dao.AuditTls, error) {
	return a.obInstDAL.GetByID(ctx, id, tenantId)
}

func (a auditTls) GetAll(ctx context.Context) ([]*dao.AuditTls, error) {
	return a.obInstDAL.GetAll(ctx, model.LogProductType_AuditLog.String())
}

func (a auditTls) GetByFollowInstanceIDWithoutTenant(ctx context.Context, followInstanceID string) (*dao.AuditTls, error) {
	return a.obInstDAL.GetByFollowInstanceIDWithoutTenant(ctx, followInstanceID, model.LogProductType_AuditLog.String())
}

func (a auditTls) GetByTenantId(ctx context.Context, tenantId string) ([]*dao.AuditTls, error) {
	return a.obInstDAL.GetByTenantId(ctx, tenantId, model.LogProductType_AuditLog.String())
}

// UpdateByFollowInstanceID status与tls_id只能更新大于0的状态值
func (a auditTls) UpdateByFollowInstanceID(ctx context.Context, cs *dao.AuditTls) error {
	cs.ProductType = model.LogProductType_AuditLog.String()
	return a.obInstDAL.UpdateByFollowInstanceID(ctx, cs)
}

func (a auditTls) Delete(ctx context.Context, followInstanceID string, tenantId string) error {
	return a.obInstDAL.Delete(ctx, followInstanceID, tenantId, model.LogProductType_AuditLog.String())
}
