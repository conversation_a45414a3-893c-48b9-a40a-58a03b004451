package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"errors"
	"fmt"
	. "gorm.io/gorm"
)

type SqlTask interface {
	Create(ctx context.Context, task dao.SqlTask) error
	UpdateSqlTask(ctx context.Context, task dao.SqlTask) error
	UpdateBySqlTaskId(ctx context.Context, task dao.SqlTask, tenantId string, userId string) error
	UpdateByOrderId(ctx context.Context, task dao.SqlTask, tenantId string, userId string) error
	UpdateTaskRunningInfo(ctx context.Context, runningInfo string, taskId int64, result string, progress int, status string) error
	SelectBySqlTaskId(ctx context.Context, task dao.SqlTask, tenantId string, userId string) (*dao.SqlTask, error)
	SelectByTenantId(ctx context.Context, DbType string, tenantId string, userId string) ([]*dao.SqlTask, error)
	SelectByInstanceId(ctx context.Context, instanceId string, dbType string, tenantId string, userId string) ([]*dao.SqlTask, error)
	SelectByCondition(ctx context.Context, m map[string]string, start *int32, end *int32, number int32, size int32, tenantId string, userId string) ([]*dao.SqlTask, error)
}

type sqlTask struct {
	dbProvider DBProvider
}

func NewSqlTaskDAL(provider DBProvider) SqlTask {
	return &sqlTask{
		dbProvider: provider,
	}
}

func (o sqlTask) Create(ctx context.Context, task dao.SqlTask) error {
	db := o.dbProvider.GetMetaDB(ctx)
	return db.Model(&dao.SqlTask{}).Create(&task).Error
}

// UpdateByOrderId 只接受查询出来的task修改字段值后再传入
func (o sqlTask) UpdateByOrderId(ctx context.Context, task dao.SqlTask, tenantId string, userId string) error {
	db := o.dbProvider.GetMetaDB(ctx)
	st := db.Model(&dao.SqlTask{}).
		Where(" order_id=? ", task.OrderId).
		Where(" deleted=0 ")
	st.Where("tenant_id=?", tenantId)
	if userId != "" {
		st.Where("user_id=?", userId)
	}
	return st.Updates(&task).Error
}

func (o sqlTask) UpdateSqlTask(ctx context.Context, task dao.SqlTask) error {
	db := o.dbProvider.GetMetaDB(ctx)
	st := db.Model(&dao.SqlTask{}).
		Where(" sql_task_id=? ", task.SqlTaskId).
		Where(" deleted=0 ")
	st.Where("tenant_id=?", task.TenantId)
	if task.UserId != "" {
		st.Where("user_id=?", task.UserId)
	}
	return st.Updates(&task).Error
}

// UpdateBySqlTaskId 只接受查询出来的task修改字段值后再传入
func (o sqlTask) UpdateBySqlTaskId(ctx context.Context, task dao.SqlTask, tenantId string, userId string) error {
	db := o.dbProvider.GetMetaDB(ctx)
	st := db.Model(&dao.SqlTask{}).
		Where(" sql_task_id=? ", task.SqlTaskId).
		Where(" deleted=0 ")
	st.Where("tenant_id=?", tenantId)
	if userId != "" {
		st.Where("user_id=?", userId)
	}
	return st.Updates(&task).Error
}

func (o sqlTask) UpdateTaskRunningInfo(ctx context.Context, runningInfo string, taskId int64, result string, progress int, status string) error {
	db := o.dbProvider.GetMetaDB(ctx)
	if err := db.Model(&dao.SqlTask{}).Where("sql_task_id = ? ", taskId).Updates(map[string]interface{}{
		"RunningInfo":   runningInfo,
		"Result":        result,
		"Progress":      progress,
		"SqlTaskStatus": status,
	}).Error; err != nil {
		return fmt.Errorf("update SqlTask error %s", err)
	}
	return nil
}

func (o sqlTask) SelectBySqlTaskId(ctx context.Context, task dao.SqlTask, tenantId string, userId string) (*dao.SqlTask, error) {
	db := o.dbProvider.GetMetaDB(ctx)
	var t *dao.SqlTask
	st := db.Model(&dao.SqlTask{}).
		Where(" sql_task_id=? ", task.SqlTaskId).
		Where(" deleted=0 ")
	st.Where("tenant_id=?", tenantId)
	if userId != "" {
		st.Where("user_id=?", userId)
	}
	log.Info(ctx, "task is %#v,taskId is %v,tenantId is %v,userId is %v", task, task.SqlTaskId, tenantId, userId)
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Model(&dao.SqlTask{}).Where(" sql_task_id=? and deleted=0 and tenant_id=?  ", task.SqlTaskId, tenantId).First(&t) // ignore_security_alert
	}))
	err := st.First(&t).Error
	if err != nil {
		return nil, err
	}
	return t, err
}

func (o sqlTask) SelectByTenantId(ctx context.Context, DbType string, tenantId string, userId string) ([]*dao.SqlTask, error) {
	db := o.dbProvider.GetMetaDB(ctx)
	var t []*dao.SqlTask
	st := db.Model(&dao.SqlTask{}).
		Where(" instance_type=? ", DbType).
		Where(" deleted=0 ")
	st.Where("tenant_id=?", tenantId)
	if userId != "" {
		st.Where("user_id=?", userId)
	}
	err := st.Find(&t).Error
	if err != nil {
		return nil, err
	}
	return t, err
}

func (o sqlTask) SelectByInstanceId(ctx context.Context, instanceId string, dbType string, tenantId string, userId string) ([]*dao.SqlTask, error) {
	db := o.dbProvider.GetMetaDB(ctx)
	var t []*dao.SqlTask
	st := db.Model(&dao.SqlTask{}).
		Where(" instance_id=? ", instanceId).
		Where(" instance_type=? ", dbType).
		Where(" deleted=0 ")
	st.Where("tenant_id=?", tenantId)
	if userId != "" {
		st.Where("user_id=?", userId)
	}
	err := st.Find(&t).Error
	if err != nil {
		return nil, err
	}
	return t, err
}

func (o sqlTask) SelectByCondition(ctx context.Context, m map[string]string, start *int32, end *int32, pageNumber int32, pageSize int32, tenantId string, userId string) ([]*dao.SqlTask, error) {
	db := o.dbProvider.GetMetaDB(ctx)
	var t []*dao.SqlTask
	st := db.Model(&dao.SqlTask{})
	if fwctx.GetTenantID(ctx) == "" {
		return nil, errors.New("tenant id is null")
	}
	st.Where("tenant_id=?", tenantId)
	if userId != "" {
		st.Where("user_id=?", userId)
	}
	if v, ok := m["sql_task_id"]; ok && v != "" {
		st.Where("sql_task_id=?", v)
	}
	if v, ok := m["instance_type"]; ok && v != "" {
		st.Where("instance_type=?", v)
	}
	if v, ok := m["instance_id"]; ok && v != "" {
		st.Where("instance_id=?", v)
	}
	if v, ok := m["sql_task_type"]; ok && v != "" {
		st.Where("sql_task_type=?", v)
	}
	if v, ok := m["sql_task_status"]; ok && v != "" {
		st.Where("sql_task_status=?", v)
	}
	if start != nil {
		st.Where("execute_time>=?", start)
	}
	if end != nil {
		st.Where("execute_time<=?", end)
	}
	st.Where("deleted=?", 0)

	st.Offset(int((pageNumber - 1) * pageSize))
	st.Limit(int(pageSize))
	err := st.Find(&t).Error
	return t, err
}
