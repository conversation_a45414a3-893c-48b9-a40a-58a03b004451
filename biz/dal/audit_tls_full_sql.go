package dal

import (
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

// FullSqlDAL 仅限审计实例的查询
type FullSqlDAL interface {
	Create(ctx context.Context, cs *dao.AuditTls) (int64, error)
	GetByFollowInstanceID(ctx context.Context, followInstanceID string, tenantId string) (*dao.AuditTls, error)
	GetByID(ctx context.Context, instanceID string, tenantId string) (*dao.AuditTls, error)
	GetByIDWithoutTenant(ctx context.Context, instanceID string) (*dao.AuditTls, error)
	GetAll(ctx context.Context) ([]*dao.AuditTls, error)
	GetByFollowInstanceIDWithoutTenant(ctx context.Context, followInstanceID string) (*dao.AuditTls, error)
	GetByTenantId(ctx context.Context, tenantId string) ([]*dao.AuditTls, error)
	UpdateByFollowInstanceID(ctx context.Context, cs *dao.AuditTls) error
	Delete(ctx context.Context, instanceId string, tenantId string) error
}

func NewFullSqlDAL(provider DBProvider, obInstDAL ObInstDAL) FullSqlDAL {
	return &fullSql{
		dbProvider: provider,
		obInstDAL:  obInstDAL,
	}
}

type fullSql struct {
	dbProvider DBProvider
	obInstDAL  ObInstDAL
}

func (a fullSql) Create(ctx context.Context, at *dao.AuditTls) (int64, error) {
	at.ProductType = model.LogProductType_FullSqlLog.String()
	return a.obInstDAL.Create(ctx, at)
}

func (a fullSql) GetByFollowInstanceID(ctx context.Context, followInstanceID string, tenantId string) (*dao.AuditTls, error) {
	return a.obInstDAL.GetByFollowInstanceID(ctx, followInstanceID, tenantId, model.LogProductType_FullSqlLog.String())
}

func (a fullSql) GetByID(ctx context.Context, id string, tenantId string) (*dao.AuditTls, error) {
	return a.obInstDAL.GetByID(ctx, id, tenantId)
}

func (a fullSql) GetByIDWithoutTenant(ctx context.Context, instanceID string) (*dao.AuditTls, error) {
	return a.obInstDAL.GetByIDWithoutTenant(ctx, instanceID)
}

func (a fullSql) GetAll(ctx context.Context) ([]*dao.AuditTls, error) {
	return a.obInstDAL.GetAll(ctx, model.LogProductType_FullSqlLog.String())
}

func (a fullSql) GetByFollowInstanceIDWithoutTenant(ctx context.Context, followInstanceID string) (*dao.AuditTls, error) {
	return a.obInstDAL.GetByFollowInstanceIDWithoutTenant(ctx, followInstanceID, model.LogProductType_FullSqlLog.String())
}

func (a fullSql) GetByTenantId(ctx context.Context, tenantId string) ([]*dao.AuditTls, error) {
	return a.obInstDAL.GetByTenantId(ctx, tenantId, model.LogProductType_FullSqlLog.String())
}

// UpdateByFollowInstanceID status与tls_id只能更新大于0的状态值
func (a fullSql) UpdateByFollowInstanceID(ctx context.Context, cs *dao.AuditTls) error {
	cs.ProductType = model.LogProductType_FullSqlLog.String()
	return a.obInstDAL.UpdateByFollowInstanceID(ctx, cs)
}

func (a fullSql) Delete(ctx context.Context, followInstanceID string, tenantId string) error {
	return a.obInstDAL.Delete(ctx, followInstanceID, tenantId, model.LogProductType_FullSqlLog.String())
}
