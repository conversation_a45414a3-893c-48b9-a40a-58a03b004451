package dal

import (
	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
)

type CopilotChatTaskDAL interface {
	Create(ctx context.Context, CopilotChatTask *dao.CopilotChatTask) error
	QueryMessageListByTaskIDList(ctx context.Context, TenantId string, taskIDList []string) ([]*dao.CopilotChatTask, error)
}

func NewCopilotChatTaskDAL(dbPro DBProvider) CopilotChatTaskDAL {
	return &CopilotChatTaskDal{dbPro: dbPro}
}

type CopilotChatTaskDal struct {
	dbPro DBProvider
}

type CopilotChatTaskParam struct {
	ID         string
	Name       string
	Role       string
	CreateTime *int64
	Limit      int
}

func (dal *CopilotChatTaskDal) Create(ctx context.Context, CopilotChatTask *dao.CopilotChatTask) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(CopilotChatTask).Error
	if err != nil {
		log.Warn(ctx, "CopilotChatTaskDal Create err:%v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return err
}

func (dal *CopilotChatTaskDal) QueryMessageListByTaskIDList(ctx context.Context, TenantId string, taskIDList []string) ([]*dao.CopilotChatTask, error) {
	db := dal.dbPro.GetMetaDB(ctx)

	var (
		CopilotTaskMessageList []*dao.CopilotChatTask
		BaseCondition          []interface{}
	)

	if len(taskIDList) == 0 {
		return CopilotTaskMessageList, nil
	}

	baseQuery := "task_id in ? "
	BaseCondition = append(BaseCondition, taskIDList)
	if !strings.IsEmpty(TenantId) {
		baseQuery += "and tenant_id = ? "
		BaseCondition = append(BaseCondition, TenantId)
	}

	if err := db.Where(baseQuery, BaseCondition...).Find(&CopilotTaskMessageList).Error; err != nil {
		return nil, err
	}

	return CopilotTaskMessageList, nil
}
