package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/framework/db"
)

type CommandSetDAL interface {
	Create(ctx context.Context, cs *dao.CommandSet, cmds []*dao.Command) error
	GetCommandSet(ctx context.Context, csID int64) (*dao.CommandSet, []*dao.Command, error)
	GetCommandSetByCmdID(ctx context.Context, cmdID int64) (*dao.CommandSet, []*dao.Command, error)
	GetCommand(ctx context.Context, cmdID int64) (*dao.Command, error)
	SaveCommandSet(ctx context.Context, cs *dao.CommandSet, cmd []*dao.Command) error
	GetExecutingCmdSetsByConnID(ctx context.Context, connID string) ([]*dao.CommandSet, error)
}

func NewCommandSetDAL(dbPro DBProvider) CommandSetDAL {
	return &commandSetDal{dbPro: dbPro}
}

type commandSetDal struct {
	dbPro DBProvider
}

func (self *commandSetDal) Create(ctx context.Context, cs *dao.CommandSet, cmds []*dao.Command) error {
	dbx := self.dbPro.GetMetaDB(ctx)
	return db.ExecWithinTransaction(dbx, func(db *db.DB) error {
		if err := db.Create(cs).Error; err != nil {
			return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
		}
		return db.Create(&cmds).Error
	})
}

func (self *commandSetDal) GetCommandSet(ctx context.Context, csID int64) (*dao.CommandSet, []*dao.Command, error) {
	dbx := self.dbPro.GetMetaDB(ctx)
	return self.getCommandSet(ctx, dbx, csID)
}

func (self *commandSetDal) GetExecutingCmdSetsByConnID(ctx context.Context, connectionID string) ([]*dao.CommandSet, error) {
	dbx := self.dbPro.GetMetaDB(ctx)
	return self.getExecutingCmdSetsByConnID(dbx, connectionID)
}

func (self *commandSetDal) GetCommandSetByCmdID(ctx context.Context, cmdID int64) (*dao.CommandSet, []*dao.Command, error) {
	dbx := self.dbPro.GetMetaDB(ctx)
	var cs dao.Command
	if err := dbx.Where(`id=?`, cmdID).Find(&cs).Error; err != nil {
		return nil, nil, err
	}
	if cs.ID == 0 {
		return nil, nil, fmt.Errorf("command %v not found", cmdID)
	}
	return self.getCommandSet(ctx, dbx, cs.CommandSetID)
}

func (self *commandSetDal) GetCommand(ctx context.Context, cmdID int64) (*dao.Command, error) {
	dbx := self.dbPro.GetMetaDB(ctx)
	var cmd dao.Command
	if err := dbx.Where(`id=?`, cmdID).Find(&cmd).Error; err != nil {
		return nil, err
	}
	if cmd.ID == 0 {
		return nil, fmt.Errorf("command %v not found", cmdID)
	}
	return &cmd, nil
}

func (self *commandSetDal) SaveCommandSet(ctx context.Context, cs *dao.CommandSet, cmds []*dao.Command) error {
	dbx := self.dbPro.GetMetaDB(ctx)
	return db.ExecWithinTransaction(dbx, func(db *db.DB) error {
		if err := db.Model(new(dao.CommandSet)).Where(`id=?`, cs.ID).
			Updates(map[string]interface{}{
				"StartTimeMS": cs.StartTimeMS,
				"EndTimeMS":   cs.EndTimeMS,
				"Progress":    cs.Progress,
			}).Error; err != nil {
			return err
		}
		for _, cmd := range cmds {
			err := db.Model(new(dao.Command)).
				Where(`id=?`, cmd.ID).
				Updates(map[string]interface{}{
					"State":        cmd.State,
					"Reason":       cmd.Reason,
					"StartTimeMS":  cmd.StartTimeMS,
					"EndTimeMS":    cmd.EndTimeMS,
					"ResultType":   cmd.ResultType,
					"Header":       cmd.Header,
					"ReasonDetail": cmd.ReasonDetail,
					"Extra":        cmd.Extra,
				}).Error
			if err != nil {
				log.Warn(ctx, "save command set failed, err: %v", err)
				return err
			}
		}
		return nil
	})
}

func (self *commandSetDal) getCommandSet(ctx context.Context, dbx *db.DB, csID int64) (*dao.CommandSet, []*dao.Command, error) {
	var cs dao.CommandSet
	if err := dbx.Where(`id=?`, csID).Find(&cs).Error; err != nil {
		return nil, nil, err
	}
	if cs.ID == 0 {
		return nil, nil, fmt.Errorf("command set %v not found", csID)
	}
	var cmds []*dao.Command
	if err := dbx.Where(`cmd_set_id=?`, csID).Order(`id asc`).Find(&cmds).Error; err != nil {
		return nil, nil, err
	}
	return &cs, cmds, nil
}

func (self *commandSetDal) getExecutingCmdSetsByConnID(dbx *db.DB, connID string) ([]*dao.CommandSet, error) {
	var cmdSets []*dao.CommandSet

	if err := dbx.Where(`connection_id=? AND progress<100 AND end_time = 0`, connID).Find(&cmdSets).Error; err != nil {
		return nil, err
	}
	return cmdSets, nil
}
