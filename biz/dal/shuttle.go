package dal

import (
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/framework/db"
	ormdb "gorm.io/gorm"
)

type ShuttleDAL interface {
	Create(ctx context.Context, db *db.DB, shuttle *dao.Shuttle) error
	Save(ctx context.Context, db *db.DB, shuttle *dao.Shuttle) error
	Get(ctx context.Context, db *db.DB, id string) (*dao.Shuttle, error)
}

func NewShuttleDAL() ShuttleDAL {
	return &shuttleDAL{}
}

type shuttleDAL struct {
}

func (dal *shuttleDAL) Create(ctx context.Context, db *db.DB, shuttle *dao.Shuttle) error {
	if err := db.Create(shuttle).Error; err != nil {
		return fmt.Errorf("create shuttle error %v", err)
	}
	return nil
}

func (dal *shuttleDAL) Save(ctx context.Context, db *db.DB, shuttle *dao.Shuttle) error {
	res := db.Model(&dao.Shuttle{}).
		Where("id = ?", shuttle.ID).
		Updates(map[string]interface{}{
			"Name":        shuttle.Name,
			"Description": shuttle.Description,
			"Clients":     shuttle.Clients,
			"Servers":     shuttle.Servers,
		})
	if err := res.Error; err != nil {
		return fmt.Errorf("save shuttle error %v", err)
	}

	return nil
}

func (dal *shuttleDAL) Get(ctx context.Context, db *db.DB, id string) (*dao.Shuttle, error) {
	shuttleList := make([]*dao.Shuttle, 0)
	if err := db.Where("id = ?", id).Find(&shuttleList).Error; err != nil {
		return nil, fmt.Errorf("get shuttle error %v", err)
	}
	log.Info(ctx, "shuttle sql is %s", db.ToSQL(func(db *ormdb.DB) *ormdb.DB {
		return db.Where("id = ?", id).Find(&shuttleList) // ignore_security_alert
	}))
	if len(shuttleList) != 0 {
		return shuttleList[0], nil
	}

	return nil, nil
}
