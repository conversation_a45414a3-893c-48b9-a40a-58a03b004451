package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

type MigrationProgressDAL interface {
	Create(ctx context.Context, task *dao.MigrationProgress) error
	List(ctx context.Context, taskID int64, tenantID string) ([]*dao.MigrationProgress, error)
	Upsert(ctx context.Context, task *dao.MigrationProgress, tenantID string) error
	UpsertExecutePt(ctx context.Context, task *dao.MigrationProgress, tenantID string) error
	Delete(ctx context.Context, taskIDs []int64, tenantID string) error
	GetTaskErrorMsg(ctx context.Context, taskID int64, tenantID string) (string, error)
}

func NewMigrationProgressDAL(dbPro DBProvider) MigrationProgressDAL {
	return &migrationProgressDal{dbPro: dbPro}
}

type migrationProgressDal struct {
	dbPro DBProvider
}

func (dal *migrationProgressDal) Create(ctx context.Context, progress *dao.MigrationProgress) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(progress).Error
	return err
}

func (dal *migrationProgressDal) List(ctx context.Context, taskID int64, tenantID string) (ret []*dao.MigrationProgress, err error) {
	db := dal.dbPro.GetMetaDB(ctx)
	err = db.Where("task_id=? and tenant_id=? and deleted=0", taskID, tenantID).Limit(1000).Order("created_at ASC").Find(&ret).Error
	return ret, err
}

func (dal *migrationProgressDal) Upsert(ctx context.Context, progress *dao.MigrationProgress, tenantID string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.MigrationProgress{}).Where("task_id=? and progress_detail=? and created_at=? and tenant_id=?", progress.TaskID, progress.ProgressDetail, progress.CreatedAt, tenantID).FirstOrCreate(&dao.MigrationProgress{}, progress)
	return res.Error
}

func (dal *migrationProgressDal) UpsertExecutePt(ctx context.Context, progress *dao.MigrationProgress, tenantID string) error {
	var ret dao.MigrationProgress
	db := dal.dbPro.GetMetaDB(ctx)
	if strings.HasPrefix(progress.ProgressDetail, "execute") {
		res := db.Model(&dao.MigrationProgress{}).Where("task_id=? and progress_detail like ? and tenant_id=?", progress.TaskID, "execute%", tenantID).Assign(dao.MigrationProgress{ProgressDetail: progress.ProgressDetail, CreatedAt: progress.CreatedAt, TaskID: progress.TaskID, TenantID: progress.TenantID}).FirstOrCreate(&dao.MigrationProgress{})
		return res.Error
	}
	if strings.HasPrefix(progress.ProgressDetail, "check") {
		//判断是否存在check记录
		err := db.Model(&dao.MigrationProgress{}).Where("task_id=? and progress_detail like ? and tenant_id=?", progress.TaskID, "check%", tenantID).First(&ret).Error
		if err != nil && err == gorm.ErrRecordNotFound {
			// 不存在则插入记录
			err := db.Create(progress).Error
			return err
		} else {
			return err
		}
	}
	return nil
}

func (dal *migrationProgressDal) Delete(ctx context.Context, taskIDs []int64, tenantID string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.MigrationProgress{}).Where("tenant_id=? and task_id in ?", tenantID, taskIDs).Updates(map[string]interface{}{
		"deleted":    1,
		"deleted_at": time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete task progress error %v", err)
	}
	return nil
}

func (dal *migrationProgressDal) GetTaskErrorMsg(ctx context.Context, taskID int64, tenantID string) (string, error) {
	whereCase := " deleted=0 AND task_id=? and tenant_id = ? and error_status = 1 limit 1 "

	db := dal.dbPro.GetMetaDB(ctx)
	var res []*dao.MigrationProgress
	err := db.Where(whereCase, taskID, tenantID).Find(&res).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return "", nil
	}
	if err != nil {
		return "", err
	}
	if len(res) == 0 {
		return "", nil
	}
	return res[0].ProgressDetail, nil
}
