package dal

import (
	bizCtx "code.byted.org/infcs/ds-lib/framework/context"
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

type UserProtocolDAL interface {
	AgreeProtocol(ctx context.Context, protocol *dao.UserProtocol) error
	GetProtocol(ctx context.Context, protocol string) (*dao.UserProtocol, error)
}

func NewUserProtocolDAL(dbPro DBProvider) UserProtocolDAL {
	return &userProtocol{dbProvider: dbPro}
}

type userProtocol struct {
	dbProvider DBProvider
}

func (u *userProtocol) AgreeProtocol(ctx context.Context, protocol *dao.UserProtocol) error {
	return u.dbProvider.GetMetaDB(ctx).Create(protocol).Error
}

func (u *userProtocol) GetProtocol(ctx context.Context, protocol string) (*dao.UserProtocol, error) {
	db := u.dbProvider.GetMetaDB(ctx)
	p := &dao.UserProtocol{}
	err := db.Model(dao.UserProtocol{}).Where("tenant_id=?", bizCtx.GetTenantID(ctx)).Where("user_id=?", bizCtx.GetUserID(ctx)).Where("protocol=?", protocol).First(p).Error
	return p, err
}
