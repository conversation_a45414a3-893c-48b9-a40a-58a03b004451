package dal

import (
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

type TenantDAL interface {
	Get(ctx context.Context, tenantId string) (*dao.Tenant, error)
	List(ctx context.Context, state *int64, offset int64, limit int64) (*dao.TenantInfos, error)
	Create(ctx context.Context, tenant *dao.Tenant) error
	Save(ctx context.Context, tenant *dao.Tenant) error
	GetAllTenants(ctx context.Context) ([]*dao.Tenant, error)
}

func NewTenantDAL(dbPro DBProvider) TenantDAL {
	return &tenantDAL{dbPro: dbPro}
}

type tenantDAL struct {
	dbPro DBProvider
}

func (t *tenantDAL) Get(ctx context.Context, tenantId string) (*dao.Tenant, error) {
	dbx := t.dbPro.GetMetaDB(ctx)
	var tenant dao.Tenant
	if err := dbx.Where(`tenant_id=?`, tenantId).Find(&tenant).Error; err != nil {
		return nil, err
	}
	return &tenant, nil
}

func (t *tenantDAL) List(ctx context.Context, state *int64, offset int64, limit int64) (*dao.TenantInfos, error) {
	db := t.dbPro.GetMetaDB(ctx).DB
	var tenants []*dao.Tenant
	var err error

	if state != nil {
		db = db.Where("state=?", state)
	}
	var total int64
	if err = db.Model(&dao.Tenant{}).Count(&total).Error; err != nil {
		return nil, err
	}
	if err = db.Offset(int(offset)).Limit(int(limit)).Find(&tenants).Error; err != nil {
		return nil, err
	}

	ret := &dao.TenantInfos{
		Total:   total,
		Tenants: tenants,
	}
	return ret, nil
}

func (t *tenantDAL) Create(ctx context.Context, tenant *dao.Tenant) error {
	dbx := t.dbPro.GetMetaDB(ctx)
	if err := dbx.Omit("id").Create(tenant).Error; err != nil {
		return err
	}
	return nil
}

func (t *tenantDAL) Save(ctx context.Context, tenant *dao.Tenant) error {
	dbx := t.dbPro.GetMetaDB(ctx)
	err := dbx.Model(new(dao.Tenant)).
		Where("tenant_id=?", tenant.TenantId).
		Updates(map[string]interface{}{
			"state":       tenant.State,
			"modify_time": tenant.ModifyTimeMS,
		}).Error
	if err != nil {
		return err
	}
	return nil
}

func (t *tenantDAL) GetAllTenants(ctx context.Context) ([]*dao.Tenant, error) {

	var tenants []*dao.Tenant
	var err error

	db := t.dbPro.GetMetaDB(ctx).DB
	db = db.Where("state = 0")
	if err = db.Find(&tenants).Error; err != nil {
		return nil, err
	}

	return tenants, nil
}
