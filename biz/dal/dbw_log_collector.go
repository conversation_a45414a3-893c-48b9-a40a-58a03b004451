package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"time"
)

type DbwLogCollectorDAL interface {
	Create(ctx context.Context, DbwLogCollector *dao.DbwLogCollector) error
	Delete(ctx context.Context, clusterName string, instanceType model.DSType) error
	ListAll(ctx context.Context, instanceType model.DSType) (*dao.DbwLogCollectors, error)
	GetByClusterName(ctx context.Context, clusterName string, instanceType model.DSType) (*dao.DbwLogCollector, error)
	UpdateImageByClustername(ctx context.Context, image string, clusterName string, instanceType model.DSType) error
	UpdateNodePoolByClustername(ctx context.Context, clusterName string, nodePool string, instanceType model.DSType) error
}

func NewDbwLogCollectorDAL(dbPro DBProvider) DbwLogCollectorDAL {
	return &DbwLogCollectorDal{dbPro: dbPro}
}

type DbwLogCollectorDal struct {
	dbPro DBProvider
}

func (dal *DbwLogCollectorDal) Delete(ctx context.Context, clusterName string, instanceType model.DSType) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DbwLogCollector{}).Where("cluster_name=? and deleted=0 and instance_type=?", clusterName, instanceType.String()).Updates(map[string]interface{}{
		"Deleted": 1,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete LogCollector error %v", err)
	}
	return nil
}

func (dal *DbwLogCollectorDal) Create(ctx context.Context, Instance *dao.DbwLogCollector) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(Instance).Error
	return err
}

func (dal *DbwLogCollectorDal) ListAll(ctx context.Context, instanceType model.DSType) (*dao.DbwLogCollectors, error) {
	var (
		DbwLogCollectors []*dao.DbwLogCollector
		baseConditions   []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "deleted=0 and instance_type=?"
	baseConditions = append(baseConditions, instanceType.String())

	if err := db.Model(&dao.DbwLogCollector{}).Where(baseQuery, baseConditions...).Find(&DbwLogCollectors).Error; err != nil {
		return nil, err
	}
	ret := &dao.DbwLogCollectors{
		Total: int32(len(DbwLogCollectors)),
		Items: DbwLogCollectors,
	}
	return ret, nil
}

func (dal *DbwLogCollectorDal) GetByClusterName(ctx context.Context, clusterName string, instanceType model.DSType) (*dao.DbwLogCollector, error) {
	var ret dao.DbwLogCollector
	db := dal.dbPro.GetMetaDB(context.TODO())
	err := db.Where("deleted=0 and cluster_name=? and instance_type=?", clusterName, instanceType.String()).First(&ret).Error
	return &ret, err
}

func (dal *DbwLogCollectorDal) UpdateImageByClustername(ctx context.Context, image string, clusterName string, instanceType model.DSType) error {
	db := dal.dbPro.GetMetaDB(context.TODO())
	res := db.Model(&dao.DbwLogCollector{}).Where("cluster_name=? and instance_type=?", clusterName, instanceType.String()).Updates(map[string]interface{}{
		"LogCollectorImage": image,
		"UpdatedAt":         time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save %s log-collector image %s info error %v", clusterName, image, err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("save %s log-collector image %s info error, Rule not exist", clusterName, image)
	}
	return nil
}

func (dal *DbwLogCollectorDal) UpdateNodePoolByClustername(ctx context.Context, clusterName string, nodePool string, instanceType model.DSType) error {
	db := dal.dbPro.GetMetaDB(context.TODO())
	res := db.Model(&dao.DbwLogCollector{}).Where("cluster_name=? and instance_type=?", clusterName, instanceType.String()).Updates(map[string]interface{}{
		"NodePool":  nodePool,
		"UpdatedAt": time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save %s log-collector NodePool %s info error %v", clusterName, nodePool, err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("save %s log-collector NodePool %s info error, Rule not exist", clusterName, nodePool)
	}
	return nil
}
