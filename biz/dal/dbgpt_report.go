package dal

import (
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

type ReportDAL interface {
	Create(ctx context.Context, reporter *dao.ChatReporter) error
}

func NewReportDAL(dbPro DBProvider) ReportDAL {
	return &reporterDal{dbPro: dbPro}
}

type reporterDal struct {
	dbPro DBProvider
}

func (r *reporterDal) Create(ctx context.Context, reporter *dao.ChatReporter) error {
	dbx := r.dbPro.GetMetaDB(ctx)
	return dbx.Create(reporter).Error
}
