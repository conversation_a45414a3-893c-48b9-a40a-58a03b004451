package dal

import (
	"context"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"gorm.io/gorm"
)

type ConsoleConnEnvDAL interface {
	Create(ctx context.Context, env *dao.ConsoleConnEnv) error
	List(ctx context.Context, instanceId string, filter map[string]interface{}) ([]*dao.ConsoleConnEnv, error)
	GetByAccountName(ctx context.Context, instanceId, accountName string) (*dao.ConsoleConnEnv, error)
	Get(ctx context.Context, id string) (*dao.ConsoleConnEnv, error)
	Update(ctx context.Context, env *dao.ConsoleConnEnv) error
	Delete(ctx context.Context, id string, instanceId string) error
}

func NewConsoleConnEnvDAL(dbPro DBProvider) ConsoleConnEnvDAL {
	return &consoleConnEnvDal{dbPro: dbPro}
}

type consoleConnEnvDal struct {
	dbPro DBProvider
}

func (c *consoleConnEnvDal) Create(ctx context.Context, env *dao.ConsoleConnEnv) error {
	dbx := c.dbPro.GetMetaDB(ctx)
	env.CreateTime = time.Now().Unix()
	env.UpdateTime = time.Now().Unix()
	env.TenantId = fwctx.GetTenantID(ctx)
	env.UserId = fwctx.GetUserID(ctx)
	return dbx.Create(env).Error
}

func (c *consoleConnEnvDal) List(ctx context.Context, instanceId string, filter map[string]interface{}) ([]*dao.ConsoleConnEnv, error) {
	dbx := c.dbPro.GetMetaDB(ctx)
	var envs []*dao.ConsoleConnEnv
	query := dbx.Model(&dao.ConsoleConnEnv{}).
		Where(`instance_id=?`, instanceId).
		Where(`tenant_id=?`, fwctx.GetTenantID(ctx)).
		Where(`user_id=?`, fwctx.GetUserID(ctx))
	if filter != nil {
		if v, ok := filter["password"]; ok {
			query = query.Not("password = ?", v)
			delete(filter, "password")
		}
		if v, ok := filter["account_name"]; ok {
			query = query.Where("account_name = ?", v)
		}
	}
	err := query.Find(&envs).Error
	return envs, err
}

func (c *consoleConnEnvDal) GetByAccountName(ctx context.Context, instanceId, accountName string) (*dao.ConsoleConnEnv, error) {
	dbx := c.dbPro.GetMetaDB(ctx)
	var env dao.ConsoleConnEnv
	err := dbx.Model(&dao.ConsoleConnEnv{}).
		Where(`instance_id=?`, instanceId).
		Where(`tenant_id=?`, fwctx.GetTenantID(ctx)).
		Where(`user_id=?`, fwctx.GetUserID(ctx)).
		Where(`account_name=?`, accountName).
		First(&env).Error
	return &env, err
}

func (c *consoleConnEnvDal) Get(ctx context.Context, id string) (*dao.ConsoleConnEnv, error) {
	dbx := c.dbPro.GetMetaDB(ctx)
	var env dao.ConsoleConnEnv
	if err := dbx.Where(`id=?`, id).
		Where(`tenant_id=?`, fwctx.GetTenantID(ctx)).
		Where(`user_id=?`, fwctx.GetUserID(ctx)).
		First(&env).Error; err != nil {
		return nil, err
	}
	return &env, nil
}

func (c *consoleConnEnvDal) Update(ctx context.Context, env *dao.ConsoleConnEnv) error {
	dbx := c.dbPro.GetMetaDB(ctx)
	return dbx.Updates(env).Error
}

func (c *consoleConnEnvDal) Delete(ctx context.Context, id string, instanceId string) error {
	dbx := c.dbPro.GetMetaDB(ctx)
	db := dbx.Where(`id=?`, id).
		Where(`instance_id=?`, instanceId).
		Where(`tenant_id=?`, fwctx.GetTenantID(ctx)).
		Where(`user_id=?`, fwctx.GetUserID(ctx)).
		Delete(&dao.ConsoleConnEnv{})
	if err := db.Error; err != nil {
		return err
	}
	if db.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}
