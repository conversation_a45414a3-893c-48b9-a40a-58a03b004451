package dal

import (
	"context"
	"strconv"
	"time"
)

type OpsDAL interface {
	GetAudit(ctx context.Context, instance AuditInstance) ([]*AuditInstance, error)
	GetByID(ctx context.Context, instanceID string, tenantId string) (*AuditInstance, error)
}

func NewOpsDAL(provider DBProvider) OpsDAL {
	return &opsDAL{dbProvider: provider}
}

type opsDAL struct {
	dbProvider DBProvider
}

func (o opsDAL) GetAudit(ctx context.Context, instance AuditInstance) ([]*AuditInstance, error) {
	db := o.dbProvider.GetMetaDB(ctx)
	query, conditions := genAuditInstanceCondition(instance)
	var auditList []*AuditInstance
	err := db.Model(&AuditInstance{}).Where(query, conditions...).Scan(&auditList).Error // ignore_security_alert
	return auditList, err
}

func (o opsDAL) GetByID(ctx context.Context, id string, tenantId string) (*AuditInstance, error) {
	db := o.dbProvider.GetMetaDB(ctx)
	var at AuditInstance
	err := db.Where(INST_WHERE_INSTANCE_ID_CONDITION, id, tenantId).First(&at).Error
	return &at, err
}

func genAuditInstanceCondition(instance AuditInstance) (string, []interface{}) {
	var conditions []interface{}

	query := " deleted=? "
	conditions = append(conditions, strconv.FormatInt(int64(instance.Deleted), 10))

	if instance.DbType != "" {
		query = query + " and db_type=? "
		conditions = append(conditions, instance.DbType)
	}
	if instance.TenantID != "" {
		query = query + " and tenant_id=? "
		conditions = append(conditions, instance.TenantID)
	}
	return query, conditions
}

type AuditInstance struct {
	ID                     int64     `gorm:"primary_key"`
	InstanceID             string    `gorm:"column:instance_id"`
	ProductType            string    `gorm:"column:product_type"`
	TlsId                  int64     `gorm:"column:tls_id"`
	FollowInstanceID       string    `gorm:"column:follow_instance_id"`
	Region                 string    `gorm:"column:region"`
	DbType                 string    `gorm:"column:db_type"`
	DeployType             string    `gorm:"column:deploy_type"`
	TenantID               string    `gorm:"column:tenant_id"`
	Status                 int32     `gorm:"column:status"`
	DefaultCloseType       string    `gorm:"column:default_close_type"`
	StorageSqlTypes        string    `gorm:"column:storage_sql_types"`
	CapabilitiesFlags      int64     `gorm:"column:capabilities_flags"`
	Deleted                int32     `gorm:"column:deleted"`
	CreateTime             time.Time `gorm:"create_time"`
	ModifyTime             time.Time `gorm:"modify_time"`
	EnableFunctions        string    `gorm:"column:enable_functions"`
	SqlDesensitizationType string    `gorm:"column:sql_desensitization_type"`
	RateSimpling           int32     `gorm:"column:rate_simpling"`
}

func (AuditInstance) TableName() string {
	return "dbw_audit_tls"
}
