package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"fmt"
	"reflect"
	"strings"
	"time"
)

type SqlReviewDAL interface {
	Create(ctx context.Context, SqlReview *dao.SqlReview) error
	List(ctx context.Context, TenantId string, condition *SqlReviewParam) (*dao.SqlReviews, error)
	UpdateStatusByID(ctx context.Context, ID int64, status int8) error
	QueryWaitReviewListByStatus(ctx context.Context, status int8) ([]*dao.SqlReview, error)
}

type SqlReviewDetailDAL interface {
	List(ctx context.Context, condition *SqlReviewDetailParam) (*dao.SqlReviewDetails, error)
	Create(ctx context.Context, SecGroup *dao.SqlReviewDetail) error
	QueryListByReviewId(ctx context.Context, ID string) ([]*dao.SqlReviewDetail, error)
	UpdateStatusByID(ctx context.Context, ID int64, status int8) error
	Update(ctx context.Context, ID string, sqlReview *dao.SqlReviewDetail) error
}

func NewSqlReviewDAL(dbPro DBProvider) SqlReviewDAL {
	return &SqlReviewDal{dbPro: dbPro}
}
func NewSqlReviewDetailDAL(dbPro DBProvider) SqlReviewDetailDAL {
	return &SqlReviewDetailDal{dbPro: dbPro}
}

type SqlReviewDal struct {
	dbPro DBProvider
}

type SqlReviewDetailDal struct {
	dbPro DBProvider
}

type SqlReviewParam struct {
	ID             string
	Name           string
	InstanceType   string
	InstanceId     string
	CreateUserId   string
	CreateUserName string
	ReviewStatus   *int8
	Limit          *int32
	Offset         *int32
	OrderBy        string
	SortBy         string
	ListType       *model.SqlReviewListType
	Comment        string
}

type SqlReviewDetailParam struct {
	ReviewID   string
	RuleLevel  string
	ActionName string
	Limit      *int32
	Offset     *int32
}

func (dal *SqlReviewDal) Create(ctx context.Context, SqlReview *dao.SqlReview) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(SqlReview).Error
	return err
}

func (dal *SqlReviewDal) List(ctx context.Context, TenantId string, condition *SqlReviewParam) (*dao.SqlReviews, error) {
	var (
		SqlReviewList []*dao.SqlReview
		total         int64
		BaseCondition []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id = ? "
	BaseCondition = append(BaseCondition, TenantId)

	//查询【我创建的】类型时，增加类型判断
	if condition.ListType != nil && condition.ListType.String() == model.TicketListType_CreatedByMe.String() {
		// 判断是子账号还是主账号
		userId := fwctx.GetTenantID(ctx)
		if fwctx.GetUserID(ctx) != "" { // 子账号
			userId = fwctx.GetUserID(ctx)
		}

		//我创建的
		baseQuery += " and create_user_id = ? "
		BaseCondition = append(BaseCondition, userId)
	}

	if condition.ID != "" {
		baseQuery += "and id = ? "
		BaseCondition = append(BaseCondition, condition.ID)
	}
	if condition.Name != "" {
		baseQuery += "and name like ? "
		BaseCondition = append(BaseCondition, "%"+condition.Name+"%")
	}
	if condition.ReviewStatus != nil {
		baseQuery += "and review_status = ? "
		BaseCondition = append(BaseCondition, condition.ReviewStatus)
	}
	if condition.InstanceType != "" {
		baseQuery += "and instance_type = ? "
		BaseCondition = append(BaseCondition, condition.InstanceType)
	}
	if condition.InstanceId != "" {
		baseQuery += "and instance_id = ? "
		BaseCondition = append(BaseCondition, condition.InstanceId)
	}
	if condition.CreateUserName != "" {
		baseQuery += "and create_user_name = ? "
		BaseCondition = append(BaseCondition, condition.CreateUserName)
	}
	if condition.CreateUserId != "" {
		baseQuery += "and create_user_id = ? "
		BaseCondition = append(BaseCondition, condition.CreateUserId)
	}
	if condition.Comment != "" {
		baseQuery += " and `comment` like ? "
		BaseCondition = append(BaseCondition, "%"+condition.Comment+"%")
	}

	if err := db.Model(SqlReviewList).Where(baseQuery, BaseCondition...).Count(&total).Error; err != nil {
		return nil, err
	}
	if condition.Limit != nil && condition.Offset != nil {
		// 分页
		if err := db.Where(baseQuery, BaseCondition...).Order(condition.OrderBy + " " + condition.SortBy).Offset(int(*condition.Offset)).Limit(int(*condition.Limit)).Find(&SqlReviewList).Error; err != nil {
			return nil, err
		}
	} else {
		// 不分页
		if err := db.Where(baseQuery, BaseCondition...).Find(&SqlReviewList).Error; err != nil {
			return nil, err
		}
	}
	ret := &dao.SqlReviews{
		Total:      total,
		SqlReviews: SqlReviewList,
	}
	return ret, nil
}

func (dal *SqlReviewDal) UpdateStatusByID(ctx context.Context, ID int64, status int8) error {
	db := dal.dbPro.GetMetaDB(ctx)

	res := db.Model(&dao.SqlReview{}).Where("id=?", ID).Updates(map[string]interface{}{
		"review_status": status,
		"update_time":   time.Now().UnixMilli(),
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("SqlReviewDal UpdateStatusByID %d status %d info error %v", ID, status, err)
	}
	return nil
}

func (dal *SqlReviewDal) QueryWaitReviewListByStatus(ctx context.Context, status int8) ([]*dao.SqlReview, error) {
	var (
		BaseCondition []interface{}
		SqlReviewList []*dao.SqlReview
	)

	db := dal.dbPro.GetMetaDB(ctx)

	query := "review_status = ?"
	BaseCondition = append(BaseCondition, status)
	if err := db.Where(query, BaseCondition...).Find(&SqlReviewList).Error; err != nil {
		return nil, err
	}

	return SqlReviewList, nil
}

func (dal *SqlReviewDetailDal) Create(ctx context.Context, SecGroup *dao.SqlReviewDetail) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(SecGroup).Error
	return err
}

func (dal *SqlReviewDetailDal) QueryListByReviewId(ctx context.Context, ID string) ([]*dao.SqlReviewDetail, error) {
	var (
		BaseCondition       []interface{}
		SqlReviewDetailList []*dao.SqlReviewDetail
	)

	db := dal.dbPro.GetMetaDB(ctx)

	query := "review_id = ?"
	BaseCondition = append(BaseCondition, ID)
	if err := db.Where(query, BaseCondition...).Find(&SqlReviewDetailList).Error; err != nil {
		return nil, err
	}

	return SqlReviewDetailList, nil
}

func (dal *SqlReviewDetailDal) UpdateStatusByID(ctx context.Context, ID int64, status int8) error {
	db := dal.dbPro.GetMetaDB(ctx)

	res := db.Model(&dao.SqlReviewDetail{}).Where("id=?", ID).Updates(map[string]interface{}{
		"review_status": status,
		"update_time":   time.Now().UnixMilli(),
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("SqlReviewDetailDal UpdateStatusByID %d status %d info error %v", ID, status, err)
	}
	return nil
}

func (dal *SqlReviewDetailDal) Update(ctx context.Context, ID string, sqlReview *dao.SqlReviewDetail) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Where("id=? ", ID).Updates(sqlReview)
	if err := res.Error; err != nil {
		return fmt.Errorf("update sqlReview error %v", err)
	}
	return nil
}

func (dal *SqlReviewDetailDal) List(ctx context.Context, condition *SqlReviewDetailParam) (*dao.SqlReviewDetails, error) {
	var (
		SqlReviewDetails []*dao.SqlReviewDetail
		total            int64
		BaseCondition    []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "1 = 1 "
	if condition.ReviewID != "" {
		baseQuery += "and review_id = ? "
		BaseCondition = append(BaseCondition, condition.ReviewID)
	}
	if condition.ActionName != "" {
		baseQuery += "and review_detail_content like ? "
		BaseCondition = append(BaseCondition, "%"+condition.ActionName+"%")
	}
	switch condition.RuleLevel {
	case model.RuleLevel_High.String():
		baseQuery += "and high_risk_count > 0 "
	case model.RuleLevel_Medium.String():
		baseQuery += "and middle_risk_count > 0 "
	case model.RuleLevel_Low.String():
		baseQuery += "and low_risk_count > 0 "
	}

	//这里单独构建一下select的条件，因为要补充一个total_sum的排序字段，因此需要单独处理一下
	detail := dao.SqlReviewDetail{}
	gormTags := getGormTags(detail)
	gormTags = append(gormTags, "high_risk_count + middle_risk_count + low_risk_count as total_sum")

	//排序规则按照告警的规则数量排序，其次按照索引推荐数量排序
	orderBy := "total_sum desc, index_advice_count desc"

	if err := db.Model(SqlReviewDetails).Where(baseQuery, BaseCondition...).Count(&total).Error; err != nil {
		return nil, err
	}
	if condition.Limit != nil && condition.Offset != nil {
		// 分页
		if err := db.Select(gormTags).Where(baseQuery, BaseCondition...).Order(orderBy).Offset(int(*condition.Offset)).Limit(int(*condition.Limit)).Find(&SqlReviewDetails).Error; err != nil {
			return nil, err
		}
	} else {
		// 不分页
		if err := db.Where(baseQuery, BaseCondition...).Find(&SqlReviewDetails).Error; err != nil {
			return nil, err
		}
	}
	ret := &dao.SqlReviewDetails{
		Total:            total,
		SqlReviewDetails: SqlReviewDetails,
	}
	return ret, nil
}

func getGormTags(s interface{}) []string {
	val := reflect.ValueOf(s)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return nil
	}

	typ := val.Type()
	gormTags := make([]string, 0, typ.NumField())

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		gormTag := field.Tag.Get("gorm")
		if gormTag != "" {
			gormTag = strings.TrimPrefix(gormTag, "column:")
			gormTags = append(gormTags, gormTag)
		}
	}

	return gormTags
}
