package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"gorm.io/gorm"
	"strconv"
)

type ApprovalFlowTemplateDAL interface {
	GetTemplate(ctx context.Context, flowTemplateId string) (*dao.ApprovalFlowTemplate, error)
	CreateFlowTemplate(ctx context.Context, template *dao.ApprovalFlowTemplate) error
	ModifyFlowTemplate(ctx context.Context, template *dao.ApprovalFlowTemplate) error
	DeleteApprovalFlowTemplate(ctx context.Context, templateId int64) error
}

func NewApprovalFlowTemplateDAL(provider DBProvider) ApprovalFlowTemplateDAL {
	return &ApprovalFlowTemplateDal{dbProvider: provider}
}

type ApprovalFlowTemplateDal struct {
	dbProvider DBProvider
}

func (dal *ApprovalFlowTemplateDal) GetTemplate(ctx context.Context, flowTemplateId string) (*dao.ApprovalFlowTemplate, error) {
	db := dal.dbProvider.GetMetaDB(ctx)
	var flowTemplate dao.ApprovalFlowTemplate
	err := db.Where(" template_id=? ", coverStringIdToInt64(flowTemplateId)).Limit(1).First(&flowTemplate).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		log.Warn(ctx, "ErrRecordNotFound")
		return nil, nil
	}
	return &flowTemplate, err
}

func coverStringIdToInt64(id string) int64 {
	num, err := strconv.ParseInt(id, 10, 64)
	if err != nil {
		return -1
	}
	return num
}

func (dal *ApprovalFlowTemplateDal) CreateFlowTemplate(ctx context.Context, template *dao.ApprovalFlowTemplate) error {
	db := dal.dbProvider.GetMetaDB(ctx)
	err := db.Create(template).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *ApprovalFlowTemplateDal) ModifyFlowTemplate(ctx context.Context, template *dao.ApprovalFlowTemplate) error {
	db := dal.dbProvider.GetMetaDB(ctx)

	err := db.Model(&dao.ApprovalFlowTemplate{}).Where("template_id = ? ", template.TemplateId).Updates(map[string]interface{}{
		"TotalStep":    template.TotalStep,
		"FlowNodes":    template.FlowNodes,
		"ModifyTime":   template.ModifyTime,
		"ModifyUserId": template.ModifyUserId,

		"NoNeedForApproval":    template.NoNeedForApproval,
		"NotAllowSelfApproval": template.NotAllowSelfApproval,
		"SelfAutoApproval":     template.SelfAutoApproval,
	}).Error

	return err
}

func (dal *ApprovalFlowTemplateDal) DeleteApprovalFlowTemplate(ctx context.Context, templateId int64) error {
	db := dal.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.ApprovalFlowTemplate{}).Where("template_id = ? ", templateId).Updates(map[string]interface{}{
		"Deleted": 1,
	}).Error
	return err
}
