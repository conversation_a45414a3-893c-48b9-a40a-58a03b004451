package dal

import (
	"context"
	"fmt"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

type SecGroupDAL interface {
	Create(ctx context.Context, SecGroup *dao.SecurityGroup) error
	List(ctx context.Context, TenantId string, condition *SecGroupParam) (*dao.SecurityGroups, error)
	Update(ctx context.Context, GroupId string, TenantId string, Group *dao.SecurityGroup) error
	Delete(ctx context.Context, GroupId string, TenantId string) error
	Get(ctx context.Context, GroupId string, TenantId string) (*dao.SecurityGroup, error)
	GetDefaultGroup(ctx context.Context, GroupName, InstanceType string, TenantId string) (*dao.SecurityGroup, error)
}

type SecRuleDAL interface {
	Create(ctx context.Context, SecRule *dao.SecurityRule) error
	BatchCreate(ctx context.Context, SecRules []*dao.SecurityRule) error
	Update(ctx context.Context, RuleId string, TenantId string, Rule *dao.SecurityRule) error
	UpdateState(ctx context.Context, RuleId string, TenantId string, Rule *dao.SecurityRule) error
	List(ctx context.Context, TenantId string, condition *SecRuleParam) (*dao.SecurityRules, error)
	Delete(ctx context.Context, RuleId string, TenantId string) error
	Get(ctx context.Context, RuleId string, TenantId string) (*dao.SecurityRule, error)
}

type SecFactorDAL interface {
	List(ctx context.Context) (*dao.SecurityFactors, error)
	Get(ctx context.Context, FactorMethod string, FactorType string, DetectPoint string) (*dao.SecurityFactor, error)
}

type SecActionDAL interface {
	List(ctx context.Context) (*dao.SecurityActions, error)
	Get(ctx context.Context, ActionMethod string, ActionType string, DetectPoint string) (*dao.SecurityAction, error)
}

type SecRuleExecuteRecordDAL interface {
	Create(ctx context.Context, record *dao.SecurityRuleExecuteRecord) error
	List(ctx context.Context, condition *SecRuleExecuteRecordParam) (*dao.SecurityRuleExecuteRecords, error)
}

func NewSecGroupDAL(dbPro DBProvider) SecGroupDAL {
	return &SecGroupDal{dbPro: dbPro}
}
func NewSecRuleDAL(dbPro DBProvider) SecRuleDAL {
	return &SecRuleDal{dbPro: dbPro}
}
func NewSecFactorDAL(dbPro DBProvider) SecFactorDAL {
	return &SecFactorDal{dbPro: dbPro}
}
func NewSecActionDAL(dbPro DBProvider) SecActionDAL {
	return &SecActionDal{dbPro: dbPro}
}
func NewSecRuleExecuteRecordDAL(dbPro DBProvider) SecRuleExecuteRecordDAL {
	return &SecRuleExecuteRecordDal{dbPro: dbPro}
}

type SecGroupDal struct {
	dbPro DBProvider
}
type SecRuleDal struct {
	dbPro DBProvider
}
type SecFactorDal struct {
	dbPro DBProvider
}
type SecActionDal struct {
	dbPro DBProvider
}
type SecRuleExecuteRecordDal struct {
	dbPro DBProvider
}

type SecRuleParam struct {
	Keyword    string
	SecGroupID string
	State      *int8
	Limit      *int32
	Offset     *int32
	SortBy     string
	OrderBy    string
	Category   string
	RuleType   string
	RuleID     string
	RuleFunc   string
}

type SecGroupParam struct {
	SecurityGroupId string
	Keyword         string
	InstanceType    string
	Category        string
	Limit           *int32
	Offset          *int32
}

type SecRuleExecuteRecordParam struct {
	OrderType  *int8
	OrderId    string
	ActionName string
	TenantId   string
	UserId     string
	Limit      *int32
	Offset     *int32
	OrderBy    string
	SortBy     string
}

func (r *SecRuleDal) Create(ctx context.Context, SecRule *dao.SecurityRule) error {
	db := r.dbPro.GetMetaDB(ctx)
	err := db.Create(SecRule).Error
	return err
}
func (r *SecRuleDal) BatchCreate(ctx context.Context, SecRules []*dao.SecurityRule) error {
	db := r.dbPro.GetMetaDB(ctx)
	res := db.CreateInBatches(SecRules, len(SecRules))
	return res.Error
}

func (r *SecRuleDal) Update(ctx context.Context, RuleId string, TenantId string, Rule *dao.SecurityRule) error {
	db := r.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SecurityRule{}).Where("id=? and tenant_id=? and deleted=0", RuleId, TenantId).Updates(Rule)
	if err := res.Error; err != nil {
		return fmt.Errorf("update SecurityRule error %v", err)
	}
	return nil
}

func (r *SecRuleDal) UpdateState(ctx context.Context, RuleId string, TenantId string, Rule *dao.SecurityRule) error {
	db := r.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SecurityRule{}).Where("id=? and tenant_id=? and deleted=0", RuleId, TenantId).Updates(map[string]interface{}{
		"rule_value": Rule.RuleValue,
		"state":      Rule.State,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update SecurityRule error %v", err)
	}
	return nil
}

func (r *SecRuleDal) Delete(ctx context.Context, RuleId string, TenantId string) error {
	db := r.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SecurityRule{}).Where("tenant_id=? and id=? and deleted=0", TenantId, RuleId).Updates(map[string]interface{}{
		"deleted": 1,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete SecurityRule error %v", err)
	}
	return nil
}

func (r *SecRuleDal) Get(ctx context.Context, RuleId string, TenantId string) (*dao.SecurityRule, error) {
	var ret dao.SecurityRule
	db := r.dbPro.GetMetaDB(ctx)
	err := db.Where("id=? and tenant_id=? and deleted=0", RuleId, TenantId).First(&ret).Error
	return &ret, err
}

func (r *SecRuleDal) List(ctx context.Context, TenantId string, condition *SecRuleParam) (*dao.SecurityRules, error) {
	var (
		SecRules       []*dao.SecurityRule
		total          int64
		baseConditions []interface{}
	)
	var baseQuery string
	if TenantId != "" {
		baseQuery = "tenant_id=? and security_group_id=? and deleted=0 "
		baseConditions = append(baseConditions, TenantId)
	} else {
		baseQuery = "security_group_id=? and deleted=0 "
	}
	baseConditions = append(baseConditions, condition.SecGroupID)
	if condition.RuleFunc != "" {
		baseQuery += "and func like ? "
		baseConditions = append(baseConditions, "%"+condition.RuleFunc+"%")
	}
	if condition.Keyword != "" {
		baseQuery += "and rule_key like ? "
		baseConditions = append(baseConditions, "%"+condition.Keyword+"%")
	}
	if condition.State != nil {
		baseQuery += "and state=? "
		baseConditions = append(baseConditions, *condition.State)
	}
	if condition.Category != "" {
		baseQuery += "and type=? "
		baseConditions = append(baseConditions, condition.Category)
	}
	if condition.RuleType != "" {
		baseQuery += "and rule_type=? "
		baseConditions = append(baseConditions, condition.RuleType)
	}
	if condition.RuleID != "" {
		baseQuery += "and id=? "
		baseConditions = append(baseConditions, condition.RuleID)
	}
	db := r.dbPro.GetMetaDB(ctx)
	if err := db.Model(SecRules).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if condition.Limit != nil && condition.Offset != nil {
		// 分页
		if err := db.Where(baseQuery, baseConditions...).Offset(int(*condition.Offset)).Limit(int(*condition.Limit)).Order(condition.OrderBy + " " + condition.SortBy).Find(&SecRules).Error; err != nil {
			return nil, err
		}
	} else {
		// 不分页
		if err := db.Where(baseQuery, baseConditions...).Order(condition.OrderBy + " " + condition.SortBy).Find(&SecRules).Error; err != nil {
			return nil, err
		}
	}
	ret := &dao.SecurityRules{
		Total:         int32(total),
		SecurityRules: SecRules,
	}
	return ret, nil
}

func (dal *SecGroupDal) Create(ctx context.Context, SecGroup *dao.SecurityGroup) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(SecGroup).Error
	return err
}

func (dal *SecGroupDal) List(ctx context.Context, TenantId string, condition *SecGroupParam) (*dao.SecurityGroups, error) {
	var (
		SecGroups      []*dao.SecurityGroup
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if condition.Keyword != "" {
		baseQuery += "and name like ? "
		baseConditions = append(baseConditions, "%"+condition.Keyword+"%")
	}
	if condition.InstanceType != "" {
		baseQuery += "and instance_type=? "
		baseConditions = append(baseConditions, condition.InstanceType)
	}
	if condition.Category != "" {
		baseQuery += "and type=? "
		baseConditions = append(baseConditions, condition.Category)
	}
	if condition.SecurityGroupId != "" {
		baseQuery += "and id=? "
		baseConditions = append(baseConditions, condition.SecurityGroupId)
	}
	if err := db.Model(SecGroups).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if condition.Limit != nil && condition.Offset != nil {
		// 分页
		if err := db.Where(baseQuery, baseConditions...).Offset(int(*condition.Offset)).Limit(int(*condition.Limit)).Find(&SecGroups).Error; err != nil {
			return nil, err
		}
	} else {
		// 不分页
		if err := db.Where(baseQuery, baseConditions...).Find(&SecGroups).Error; err != nil {
			return nil, err
		}
	}
	ret := &dao.SecurityGroups{
		Total:          int32(total),
		SecurityGroups: SecGroups,
	}
	return ret, nil

}

func (dal *SecGroupDal) Update(ctx context.Context, GroupId string, TenantId string, Group *dao.SecurityGroup) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Where("id=? and tenant_id=? and deleted=0", GroupId, TenantId).Save(Group)
	if err := res.Error; err != nil {
		return fmt.Errorf("update SecurityGroup error %v", err)
	}
	return nil
}

func (dal *SecGroupDal) Delete(ctx context.Context, GroupId string, TenantId string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SecurityGroup{}).Where("tenant_id=? and id=? and deleted=0", TenantId, GroupId).Updates(map[string]interface{}{
		"Deleted": 1,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete SecurityGroup error %v", err)
	}
	return nil
}

func (dal *SecGroupDal) Get(ctx context.Context, GroupId string, TenantId string) (*dao.SecurityGroup, error) {
	var ret dao.SecurityGroup
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("id=? and tenant_id=? and deleted=0", GroupId, TenantId).First(&ret).Error
	return &ret, err
}

func (dal *SecGroupDal) GetDefaultGroup(ctx context.Context, GroupName, InstanceType string, TenantId string) (*dao.SecurityGroup, error) {
	var ret dao.SecurityGroup
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("name=? and instance_type=? and tenant_id=? and deleted=0", GroupName, InstanceType, TenantId).First(&ret).Error
	return &ret, err
}

func (dal *SecFactorDal) List(ctx context.Context) (*dao.SecurityFactors, error) {
	var (
		SecFactors []*dao.SecurityFactor
	)
	db := dal.dbPro.GetMetaDB(ctx)
	if err := db.Model(SecFactors).Where("deleted = 0").Find(&SecFactors).Error; err != nil {
		return nil, err
	}
	ret := &dao.SecurityFactors{
		Total:           0,
		SecurityFactors: SecFactors,
	}
	return ret, nil
}

func (dal *SecActionDal) List(ctx context.Context) (*dao.SecurityActions, error) {
	var (
		SecActions []*dao.SecurityAction
		total      int64
	)
	db := dal.dbPro.GetMetaDB(ctx)
	if err := db.Model(SecActions).Where("deleted = 0").Find(&SecActions).Error; err != nil {
		return nil, err
	}
	ret := &dao.SecurityActions{
		Total:           total,
		SecurityActions: SecActions,
	}
	return ret, nil
}

func (dal *SecActionDal) Get(ctx context.Context, ActionMethod string, ActionType string, DetectPoint string) (*dao.SecurityAction, error) {

	var action dao.SecurityAction
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("action_method = ? and action_type = ? and detect_point = ? and deleted = 0", ActionMethod, ActionType, DetectPoint).First(&action).Error

	return &action, err
}

func (dal *SecFactorDal) Get(ctx context.Context, FactorMethod string, FactorType string, DetectPoint string) (*dao.SecurityFactor, error) {

	var factor dao.SecurityFactor
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("factor_method = ? and factor_type = ? and detect_point = ? and deleted = 0", FactorMethod, FactorType, DetectPoint).First(&factor).Error

	return &factor, err
}

func (dal *SecRuleExecuteRecordDal) Create(ctx context.Context, record *dao.SecurityRuleExecuteRecord) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(record).Error
	return err
}

func (dal *SecRuleExecuteRecordDal) List(ctx context.Context, condition *SecRuleExecuteRecordParam) (*dao.SecurityRuleExecuteRecords, error) {
	var (
		SecurityRuleExecuteRecords []*dao.SecurityRuleExecuteRecord
		total                      int64
		BaseCondition              []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and user_id=? "
	BaseCondition = append(BaseCondition, condition.TenantId)
	BaseCondition = append(BaseCondition, condition.UserId)
	if condition.OrderId != "" {
		baseQuery += "and order_id = ? "
		BaseCondition = append(BaseCondition, condition.OrderId)
	}
	if condition.OrderType != nil {
		baseQuery += "and order_type = ? "
		BaseCondition = append(BaseCondition, condition.OrderType)
	}
	if condition.ActionName != "" {
		baseQuery += "and execute_detail_content like ? "
		BaseCondition = append(BaseCondition, "%"+condition.ActionName+"%")
	}

	//这里单独构建一下select的条件，因为要补充一个total_sum的排序字段，因此需要单独处理一下
	detail := dao.SecurityRuleExecuteRecord{}
	gormTags := getGormTags(detail)
	gormTags = append(gormTags, "high_risk_count + middle_risk_count + low_risk_count as total_sum")

	//排序规则按照告警的规则数量排序
	orderBy := "total_sum desc"

	if err := db.Model(SecurityRuleExecuteRecords).Where(baseQuery, BaseCondition...).Count(&total).Error; err != nil {
		return nil, err
	}
	if condition.Limit != nil && condition.Offset != nil {
		// 分页
		if err := db.Select(gormTags).Where(baseQuery, BaseCondition...).Order(orderBy).Offset(int(*condition.Offset)).Limit(int(*condition.Limit)).Find(&SecurityRuleExecuteRecords).Error; err != nil {
			return nil, err
		}
	} else {
		// 不分页
		if err := db.Where(baseQuery, BaseCondition...).Find(&SecurityRuleExecuteRecords).Error; err != nil {
			return nil, err
		}
	}
	ret := &dao.SecurityRuleExecuteRecords{
		RuleExecuteRecords: SecurityRuleExecuteRecords,
		Total:              total,
	}
	return ret, nil
}
