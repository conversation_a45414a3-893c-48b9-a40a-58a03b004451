package dal

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

type InstancePrivilegeDAL interface {
	Create(ctx context.Context, InstPerm *dao.InstancePrivilege) error
	BatchSave(ctx context.Context, InstPerm []*dao.InstancePrivilege) error
	Delete(ctx context.Context, IDs []int64, TenantId string) error
	DeleteByCondition(ctx context.Context, TenantId string, GroupId string, UserId string) error
	Update(ctx context.Context, ID int64, TenantId string, State string) error
	UpdateEffectTimeByID(ctx context.Context, ID int64, TenantId string, CreatedAt int64, ExpiredAt int64, State string) error
	Get(ctx context.Context, InstanceId string, InstanceType string, TenantID string, UserId string, Privilege string, checkTime bool, selfPrivilege bool) (*dao.InstancePrivilege, error)
	GetById(ctx context.Context, TenantId string, ID int64) (*dao.InstancePrivilege, error)
	List(ctx context.Context, TenantId string, State string, UserId string, GroupId string, PermIds []string, keyword string, InstanceType string, limit int32, offset int32, InstanceId string, PrivilegeType string) (*dao.InstancePrivileges, error)
	ListByResource(ctx context.Context, InstanceId string, PrivilegeType string, TenantID string, selfPrivilege bool, UserList []string, GroupList []string) ([]*dao.InstancePrivilege, error)
	ListAndDelete(ctx context.Context, TenantId string, UserId string, GroupId string, InstanceType string, InstanceId string, PrivilegeType string) error
}

type DatabasePrivilegeDAL interface {
	Create(ctx context.Context, DbPerm *dao.DatabasePrivilege) error
	BatchSave(ctx context.Context, dbPerms []*dao.DatabasePrivilege) error
	Delete(ctx context.Context, IDs []int64, TenantId string) error
	DeleteByCondition(ctx context.Context, TenantId string, GroupId string, UserId string) error
	Update(ctx context.Context, ID int64, TenantId string, State string) error
	UpdateEffectTimeByID(ctx context.Context, ID int64, TenantId string, CreatedAt int64, ExpiredAt int64, State string) error
	Get(ctx context.Context, InstanceId string, DbName string, TenantID string, UserId string, Privilege string, checkTime bool, selfPrivilege bool) (*dao.DatabasePrivilege, error)
	GetById(ctx context.Context, TenantId string, ID int64) (*dao.DatabasePrivilege, error)
	List(ctx context.Context, TenantId string, State string, UserId string, GroupId string, PermIds []string, keyword string, InstanceType string, limit int32, offset int32, InstanceId string, PrivilegeType string, Fuzzy bool) (*dao.DatabasePrivileges, error)
	ListDatabases(ctx context.Context) (*dao.DatabasePrivileges, error)
	ListByResource(ctx context.Context, InstanceId string, database string, PrivilegeType string, TenantID string, selfPrivilege bool, UserList []string, GroupList []string) ([]*dao.DatabasePrivilege, error)
	ListByRootAccount(ctx context.Context, instanceId string, dbNames ...string) (*dao.DatabasePrivileges, error)
	ListAndDelete(ctx context.Context, TenantId string, UserId string, GroupId string, InstanceType string, InstanceId string, dbName string, PrivilegeType string) error
}

type TablePrivilegeDAL interface {
	Create(ctx context.Context, TbPerm *dao.TablePrivilege) error
	BatchSave(ctx context.Context, tablePerms []*dao.TablePrivilege) error
	Delete(ctx context.Context, IDs []int64, TenantId string) error
	DeleteByCondition(ctx context.Context, TenantId string, GroupId string, UserId string) error
	Update(ctx context.Context, ID int64, TenantId string, State string) error
	UpdateEffectTimeByID(ctx context.Context, ID int64, TenantId string, CreatedAt int64, ExpiredAt int64, State string) error
	Get(ctx context.Context, InstanceId string, DbName string, TableName string, TenantID string, UserId string, Privilege string, checkTime bool, selfPrivilege bool) (*dao.TablePrivilege, error)
	GetById(ctx context.Context, TenantId string, ID int64) (*dao.TablePrivilege, error)
	List(ctx context.Context, TenantId string, State string, UserId string, GroupId string, PermIds []string, keyword string, InstanceType string, limit int32, offset int32, InstanceId, PrivilegeType, DbName string, Fuzzy bool) (*dao.TablePrivileges, error)
	ListAndDelete(ctx context.Context, TenantId string, UserId string, GroupId string, InstanceType string, InstanceId string, dbName string, tableName string, PrivilegeType string) error
	ListByResource(ctx context.Context, InstanceId string, database string, table string, PrivilegeType string, TenantID string, selfPrivilege bool, UserList []string, GroupList []string) ([]*dao.TablePrivilege, error)
}

type ColumnPrivilegeDAL interface {
	Create(ctx context.Context, ColPerm *dao.ColumnPrivilege) error
	BatchSave(ctx context.Context, colPerms []*dao.ColumnPrivilege) error
	Delete(ctx context.Context, IDs []int64, TenantId string) error
	DeleteByCondition(ctx context.Context, TenantId string, GroupId string, UserId string) error
	Update(ctx context.Context, ID int64, TenantId string, State string) error
	UpdateEffectTimeByID(ctx context.Context, ID int64, TenantId string, CreatedAt int64, ExpiredAt int64, State string) error
	Get(ctx context.Context, InstanceId string, DbName string, TableName string, ColumnName string, TenantID string, UserId string, Privilege string, checkTime bool, selfPrivilege bool) (*dao.ColumnPrivilege, error)
	GetById(ctx context.Context, TenantId string, ID int64) (*dao.ColumnPrivilege, error)
	List(ctx context.Context, TenantId string, State string, UserId string, GroupId string, PermIds []string, keyword string, InstanceType string, limit int32, offset int32, InstanceId, PrivilegeType, DbName, TbName string, Fuzzy bool) (*dao.ColumnPrivileges, error)
	ListAndDelete(ctx context.Context, TenantId string, UserId string, GroupId string, InstanceType string, InstanceId string, dbName string, tableName string, columnName string, PrivilegeType string) error
	ListByResource(ctx context.Context, InstanceId string, database string, table string, column string, PrivilegeType string, TenantID string, selfPrivilege bool, UserList []string, GroupList []string) ([]*dao.ColumnPrivilege, error)
}

func NewInstancePrivilegeDAL(dbPro DBProvider) InstancePrivilegeDAL {
	return &InstancePrivilegeDal{dbPro: dbPro}
}

func NewDatabasePrivilegeDAL(dbPro DBProvider) DatabasePrivilegeDAL {
	return &DatabasePrivilegeDal{dbPro: dbPro}
}

func NewTablePrivilegeDAL(dbPro DBProvider) TablePrivilegeDAL {
	return &TablePrivilegeDal{dbPro: dbPro}
}

func NewColumnPrivilegeDAL(dbPro DBProvider) ColumnPrivilegeDAL {
	return &ColumnPrivilegeDal{dbPro: dbPro}
}

type InstancePrivilegeDal struct {
	dbPro DBProvider
}

type DatabasePrivilegeDal struct {
	dbPro DBProvider
}

type TablePrivilegeDal struct {
	dbPro DBProvider
}

type ColumnPrivilegeDal struct {
	dbPro DBProvider
}

func (dal *InstancePrivilegeDal) Get(ctx context.Context, InstanceId string, InstanceType string, TenantID string, UserId string, Privilege string, checkTime bool, selfPrivilege bool) (*dao.InstancePrivilege, error) {
	var ret dao.InstancePrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	var baseConditions []interface{}
	//baseQuery := "tenant_id=? and deleted=0 and instance_id=? and instance_type=? and user_id=? "
	baseQuery := "deleted=0 and instance_id=? and user_id=? "
	baseConditions = append(baseConditions, InstanceId)
	//baseConditions = append(baseConditions, InstanceType)
	baseConditions = append(baseConditions, UserId)
	if TenantID != "" {
		baseQuery += "and tenant_id=? "
		baseConditions = append(baseConditions, TenantID)
	}
	if Privilege != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, Privilege)
	}
	if checkTime {
		baseQuery += "and expired_at>? "
		baseConditions = append(baseConditions, time.Now().Unix())
	}
	if selfPrivilege {
		baseQuery += "and group_id=0 "
	}
	res := db.Model(&dao.InstancePrivilege{}).Where(baseQuery, baseConditions...).First(&ret)
	if err := res.Error; err != nil {
		return &ret, err
	}
	return &ret, nil
}

func (dal *DatabasePrivilegeDal) Get(ctx context.Context, InstanceId string, DbName string, TenantID string, UserId string, Privilege string, checkTime bool, selfPrivilege bool) (*dao.DatabasePrivilege, error) {
	var ret dao.DatabasePrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	var baseConditions []interface{}
	baseQuery := "deleted=0 and user_id=? and instance_id=? and db_name=? "
	baseConditions = append(baseConditions, UserId)
	baseConditions = append(baseConditions, InstanceId)
	baseConditions = append(baseConditions, DbName)
	if TenantID != "" {
		baseQuery += "and tenant_id=? "
		baseConditions = append(baseConditions, TenantID)
	}
	if Privilege != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, Privilege)
	}
	if checkTime {
		baseQuery += "and expired_at>? "
		baseConditions = append(baseConditions, time.Now().Unix())
	}
	if selfPrivilege {
		baseQuery += "and group_id=0 "
	}
	res := db.Model(&dao.DatabasePrivilege{}).Where(baseQuery, baseConditions...).First(&ret)
	if err := res.Error; err != nil {
		return &ret, err
	}
	return &ret, nil
}

func (dal *TablePrivilegeDal) Get(ctx context.Context, InstanceId string, DbName string, TableName string, TenantID string, UserId string, Privilege string, checkTime bool, selfPrivilege bool) (*dao.TablePrivilege, error) {
	var ret dao.TablePrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	var baseConditions []interface{}
	baseQuery := "tenant_id=? and deleted=0 and user_id=? and instance_id=? and db_name=? and table_name=? "
	baseConditions = append(baseConditions, TenantID)
	baseConditions = append(baseConditions, UserId)
	baseConditions = append(baseConditions, InstanceId)
	baseConditions = append(baseConditions, DbName)
	baseConditions = append(baseConditions, TableName)
	if Privilege != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, Privilege)
	}
	if checkTime {
		baseQuery += "and expired_at>? "
		baseConditions = append(baseConditions, time.Now().Unix())
	}
	if selfPrivilege {
		baseQuery += "and group_id=0 "
	}
	res := db.Model(&dao.TablePrivilege{}).Where(baseQuery, baseConditions...).First(&ret)
	if err := res.Error; err != nil {
		return &ret, err
	}
	return &ret, nil
}

func (dal *ColumnPrivilegeDal) Get(ctx context.Context, InstanceId string, DbName string, TableName string, ColumnName string, TenantID string, UserId string, Privilege string, checkTime bool, selfPrivilege bool) (*dao.ColumnPrivilege, error) {
	var ret dao.ColumnPrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	var baseConditions []interface{}
	baseQuery := "tenant_id=? and deleted=0 and user_id=? and instance_id=? and db_name=? and table_name=? and column_name=? "
	baseConditions = append(baseConditions, TenantID)
	baseConditions = append(baseConditions, UserId)
	baseConditions = append(baseConditions, InstanceId)
	baseConditions = append(baseConditions, DbName)
	baseConditions = append(baseConditions, TableName)
	baseConditions = append(baseConditions, ColumnName)
	if Privilege != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, Privilege)
	}
	if checkTime {
		baseQuery += "and expired_at>? "
		baseConditions = append(baseConditions, time.Now().Unix())
	}
	if selfPrivilege {
		baseQuery += "and group_id=0 "
	}
	res := db.Model(&dao.ColumnPrivilege{}).Where(baseQuery, baseConditions...).First(&ret)
	if err := res.Error; err != nil {
		return &ret, err
	}
	return &ret, nil
}

func (dal *InstancePrivilegeDal) GetById(ctx context.Context, TenantID string, Id int64) (*dao.InstancePrivilege, error) {
	var ret dao.InstancePrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("id=? and tenant_id=? and deleted=0", Id, TenantID).First(&ret).Error
	return &ret, err

}

func (dal *DatabasePrivilegeDal) GetById(ctx context.Context, TenantID string, Id int64) (*dao.DatabasePrivilege, error) {
	var ret dao.DatabasePrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("id=? and tenant_id=? and deleted=0", Id, TenantID).First(&ret).Error
	return &ret, err

}

func (dal *TablePrivilegeDal) GetById(ctx context.Context, TenantID string, Id int64) (*dao.TablePrivilege, error) {
	var ret dao.TablePrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("id=? and tenant_id=? and deleted=0", Id, TenantID).First(&ret).Error
	return &ret, err

}

func (dal *ColumnPrivilegeDal) GetById(ctx context.Context, TenantID string, Id int64) (*dao.ColumnPrivilege, error) {
	var ret dao.ColumnPrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("id=? and tenant_id=? and deleted=0", Id, TenantID).First(&ret).Error
	return &ret, err
}

func (dal *InstancePrivilegeDal) Update(ctx context.Context, ID int64, TenantId string, State string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.InstancePrivilege{}).Where("id=? and tenant_id=? and deleted=0", ID, TenantId).Updates(map[string]interface{}{
		"State":     State,
		"UpdatedAt": time.Now().Unix(),
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update %d InstancePrivilege error %v", ID, err)
	}
	return nil
}

func (dal *DatabasePrivilegeDal) Update(ctx context.Context, ID int64, TenantId string, State string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DatabasePrivilege{}).Where("id=? and tenant_id=? and deleted=0", ID, TenantId).Updates(map[string]interface{}{
		"State":     State,
		"UpdatedAt": time.Now().Unix(),
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update %d DatabasePrivilege error %v", ID, err)
	}
	return nil
}

func (dal *TablePrivilegeDal) Update(ctx context.Context, ID int64, TenantId string, State string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.TablePrivilege{}).Where("id=? and tenant_id=? and deleted=0", ID, TenantId).Updates(map[string]interface{}{
		"State":     State,
		"UpdatedAt": time.Now().Unix(),
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update %d TablePrivilege error %v", ID, err)
	}
	return nil
}

func (dal *ColumnPrivilegeDal) Update(ctx context.Context, ID int64, TenantId string, State string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.ColumnPrivilege{}).Where("id=? and tenant_id=? and deleted=0", ID, TenantId).Updates(map[string]interface{}{
		"State":     State,
		"UpdatedAt": time.Now().Unix(),
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update %d ColumnPrivilege error %v", ID, err)
	}
	return nil
}

func (dal *InstancePrivilegeDal) UpdateEffectTimeByID(ctx context.Context, ID int64, TenantId string, CreatedAt int64, ExpiredAt int64, State string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.InstancePrivilege{}).Where("id=? and tenant_id=? and deleted=0", ID, TenantId).Updates(map[string]interface{}{
		"CreatedAt": CreatedAt,
		"ExpiredAt": ExpiredAt,
		"UpdatedAt": CreatedAt,
		"State":     State,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update %d InstancePrivilege error %v", ID, err)
	}
	return nil
}

func (dal *DatabasePrivilegeDal) UpdateEffectTimeByID(ctx context.Context, ID int64, TenantId string, CreatedAt int64, ExpiredAt int64, State string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DatabasePrivilege{}).Where("id=? and tenant_id=? and deleted=0", ID, TenantId).Updates(map[string]interface{}{
		"CreatedAt": CreatedAt,
		"ExpiredAt": ExpiredAt,
		"UpdatedAt": CreatedAt,
		"State":     State,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update %d DatabasePrivilege error %v", ID, err)
	}
	return nil
}

func (dal *TablePrivilegeDal) UpdateEffectTimeByID(ctx context.Context, ID int64, TenantId string, CreatedAt int64, ExpiredAt int64, State string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.TablePrivilege{}).Where("id=? and tenant_id=? and deleted=0", ID, TenantId).Updates(map[string]interface{}{
		"CreatedAt": CreatedAt,
		"ExpiredAt": ExpiredAt,
		"UpdatedAt": CreatedAt,
		"State":     State,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update %d TablePrivilege error %v", ID, err)
	}
	return nil
}

func (dal *ColumnPrivilegeDal) UpdateEffectTimeByID(ctx context.Context, ID int64, TenantId string, CreatedAt int64, ExpiredAt int64, State string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.ColumnPrivilege{}).Where("id=? and tenant_id=? and deleted=0", ID, TenantId).Updates(map[string]interface{}{
		"CreatedAt": CreatedAt,
		"ExpiredAt": ExpiredAt,
		"UpdatedAt": CreatedAt,
		"State":     State,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update %d ColumnPrivilege error %v", ID, err)
	}
	return nil
}

func (dal *InstancePrivilegeDal) Create(ctx context.Context, InstPerm *dao.InstancePrivilege) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(InstPerm).Error
	return err
}

func (dal *DatabasePrivilegeDal) Create(ctx context.Context, DbPerm *dao.DatabasePrivilege) error {
	db := dal.dbPro.GetMetaDB(ctx)
	DbPerm.CreatedAt = time.Now().Unix()
	err := db.Create(DbPerm).Error
	return err
}

func (dal *TablePrivilegeDal) Create(ctx context.Context, TbPerm *dao.TablePrivilege) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(TbPerm).Error
	return err
}

func (dal *ColumnPrivilegeDal) Create(ctx context.Context, ColPerm *dao.ColumnPrivilege) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(ColPerm).Error
	return err
}

func (dal *InstancePrivilegeDal) Delete(ctx context.Context, IDs []int64, TenantId string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.InstancePrivilege{}).Where("tenant_id=? and deleted=0 and id in ?", TenantId, IDs).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete InstancePrivilege error %v", err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("privileges not exist")
	}
	return nil
}

func (dal *DatabasePrivilegeDal) Delete(ctx context.Context, IDs []int64, TenantId string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DatabasePrivilege{}).Where("tenant_id=? and deleted=0 and id in ?", TenantId, IDs).Updates(map[string]interface{}{
		"deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete DatabasePrivilege error %v", err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("privileges not exist")
	}
	return nil
}

func (dal *TablePrivilegeDal) Delete(ctx context.Context, IDs []int64, TenantId string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.TablePrivilege{}).Where("tenant_id=? and deleted=0 and id in ?", TenantId, IDs).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete TablePrivilege error %v", err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("privileges not exist")
	}
	return nil
}

func (dal *ColumnPrivilegeDal) Delete(ctx context.Context, IDs []int64, TenantId string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.ColumnPrivilege{}).Where("tenant_id=? and deleted=0 and id in ?", TenantId, IDs).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete ColumnPrivilege error %v", err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("privileges not exist")
	}
	return nil
}

func (dal *InstancePrivilegeDal) DeleteByCondition(ctx context.Context, TenantId string, GroupId string, UserId string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	var baseConditions []interface{}
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		baseQuery += "and group_id=? "
		baseConditions = append(baseConditions, GroupId)
	}
	res := db.Model(&dao.InstancePrivilege{}).Where(baseQuery, baseConditions...).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete InstancePrivilege error %v", err)
	}
	//if res.RowsAffected == 0 {
	//	return fmt.Errorf("privileges not exist")
	//}
	return nil
}

func (dal *DatabasePrivilegeDal) DeleteByCondition(ctx context.Context, TenantId string, GroupId string, UserId string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	var baseConditions []interface{}
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		baseQuery += "and group_id=? "
		baseConditions = append(baseConditions, GroupId)
	}
	res := db.Model(&dao.DatabasePrivilege{}).Where(baseQuery, baseConditions...).Updates(map[string]interface{}{
		"deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete DatabasePrivilege error %v", err)
	}
	//if res.RowsAffected == 0 {
	//	return fmt.Errorf("privileges not exist")
	//}
	return nil
}

func (dal *TablePrivilegeDal) DeleteByCondition(ctx context.Context, TenantId string, GroupId string, UserId string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	var baseConditions []interface{}
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		baseQuery += "and group_id=? "
		baseConditions = append(baseConditions, GroupId)
	}
	res := db.Model(&dao.TablePrivilege{}).Where(baseQuery, baseConditions...).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete TablePrivilege error %v", err)
	}
	//if res.RowsAffected == 0 {
	//	return fmt.Errorf("privileges not exist")
	//}
	return nil
}

func (dal *ColumnPrivilegeDal) DeleteByCondition(ctx context.Context, TenantId string, GroupId string, UserId string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	var baseConditions []interface{}
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		baseQuery += "and group_id=? "
		baseConditions = append(baseConditions, GroupId)
	}
	res := db.Model(&dao.ColumnPrivilege{}).Where(baseQuery, baseConditions...).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete ColumnPrivilege error %v", err)
	}
	//if res.RowsAffected == 0 {
	//	return fmt.Errorf("privileges not exist")
	//}
	return nil
}

func (dal *InstancePrivilegeDal) BatchSave(ctx context.Context, InstPerms []*dao.InstancePrivilege) error {
	if InstPerms == nil {
		return nil
	}
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Save(InstPerms).Error
	return err
}

func (dal *DatabasePrivilegeDal) BatchSave(ctx context.Context, dbPerms []*dao.DatabasePrivilege) error {
	if dbPerms == nil {
		return nil
	}
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Save(dbPerms).Error
	return err
}

func (dal *TablePrivilegeDal) BatchSave(ctx context.Context, tablePerms []*dao.TablePrivilege) error {
	if tablePerms == nil {
		return nil
	}
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Save(tablePerms).Error
	return err
}

func (dal *ColumnPrivilegeDal) BatchSave(ctx context.Context, colPerms []*dao.ColumnPrivilege) error {
	if colPerms == nil {
		return nil
	}
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Save(colPerms).Error
	return err
}

func (dal *InstancePrivilegeDal) List(ctx context.Context, TenantId string, State string, UserId string, GroupId string, PermIds []string, keyword string, InstanceType string, limit int32, offset int32, InstanceId string, PrivilegeType string) (*dao.InstancePrivileges, error) {
	now := time.Now().Unix()
	var (
		Privileges     []*dao.InstancePrivilege
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		if UserId == "" {
			baseQuery += "and user_id = '' and group_id=? "
		} else {
			baseQuery += "and group_id=? "
		}
		baseConditions = append(baseConditions, GroupId)
	}
	if State != "" {
		switch State {
		case model.PermState_EXPIRED.String():
			baseQuery += " and expired_at <=? "
			baseConditions = append(baseConditions, now)
		case model.PermState_NORMAL.String():
			baseQuery += " and expired_at >? "
			baseConditions = append(baseConditions, now)
		}
	}
	if len(PermIds) > 0 {
		baseQuery += " and id in (?) "
		baseConditions = append(baseConditions, PermIds)
	}
	if keyword != "" {
		baseQuery += " and instance_id like ? "
		baseConditions = append(baseConditions, "%"+keyword+"%")
	}
	if InstanceType != "" {
		baseQuery += " and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if InstanceId != "" {
		baseQuery += " and instance_id=? "
		baseConditions = append(baseConditions, InstanceId)
	}
	if PrivilegeType != "" {
		baseQuery += " and privilege_type=? "
		baseConditions = append(baseConditions, PrivilegeType)
	}
	if err := db.Model(Privileges).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Find(&Privileges).Error; err != nil {
		return nil, err
	}
	ret := &dao.InstancePrivileges{
		Total:      int32(total),
		Privileges: Privileges,
	}
	return ret, nil

}

func (dal *DatabasePrivilegeDal) List(ctx context.Context, TenantId string, State string, UserId string, GroupId string, PermIds []string, keyword string, InstanceType string, limit int32, offset int32, InstanceId string, PrivilegeType string, Fuzzy bool) (*dao.DatabasePrivileges, error) {
	var (
		Privileges     []*dao.DatabasePrivilege
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		if UserId == "" {
			baseQuery += "and user_id = '' and group_id=? "
		} else {
			baseQuery += "and group_id=? "
		}
		baseConditions = append(baseConditions, GroupId)
	}

	if State != "" {
		baseQuery += "and state=? "
		baseConditions = append(baseConditions, State)
	}
	if len(PermIds) > 0 {
		baseQuery += "and id in (?) "
		baseConditions = append(baseConditions, PermIds)
	}
	if keyword != "" {
		if Fuzzy {
			baseQuery += "and db_name like ? "
			baseConditions = append(baseConditions, "%"+keyword+"%")
		} else {
			baseQuery += "and db_name like ? "
			baseConditions = append(baseConditions, keyword)
		}
	}
	if InstanceType != "" {
		baseQuery += "and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if InstanceId != "" {
		baseQuery += "and instance_id=? "
		baseConditions = append(baseConditions, InstanceId)
	}
	if PrivilegeType != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, PrivilegeType)
	}
	if err := db.Model(Privileges).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Find(&Privileges).Error; err != nil {
		return nil, err
	}
	ret := &dao.DatabasePrivileges{
		Total:      int32(total),
		Privileges: Privileges,
	}
	return ret, nil

}

func (dal *TablePrivilegeDal) List(ctx context.Context, TenantId string, State string, UserId string, GroupId string, PermIds []string, keyword string, InstanceType string, limit int32, offset int32, InstanceId, PrivilegeType, DbName string, Fuzzy bool) (*dao.TablePrivileges, error) {
	var (
		Privileges     []*dao.TablePrivilege
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		if UserId == "" {
			baseQuery += "and user_id = '' and group_id=? "
		} else {
			baseQuery += "and group_id=? "
		}
		baseConditions = append(baseConditions, GroupId)
	}
	if State != "" {
		baseQuery += "and state=? "
		baseConditions = append(baseConditions, State)
	}
	if len(PermIds) > 0 {
		baseQuery += "and id in (?) "
		baseConditions = append(baseConditions, PermIds)
	}
	if keyword != "" {
		if Fuzzy {
			baseQuery += "and table_name like ? "
			baseConditions = append(baseConditions, "%"+keyword+"%")
		} else {
			baseQuery += "and table_name=? "
			baseConditions = append(baseConditions, keyword)
		}
	}
	if InstanceType != "" {
		baseQuery += "and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if InstanceId != "" {
		baseQuery += "and instance_id=? "
		baseConditions = append(baseConditions, InstanceId)
	}
	if PrivilegeType != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, PrivilegeType)
	}
	if DbName != "" {
		baseQuery += "and db_name=? "
		baseConditions = append(baseConditions, DbName)
	}
	if err := db.Model(Privileges).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Find(&Privileges).Error; err != nil {
		return nil, err
	}
	ret := &dao.TablePrivileges{
		Total:      int32(total),
		Privileges: Privileges,
	}
	return ret, nil

}

func (dal *ColumnPrivilegeDal) List(ctx context.Context, TenantId string, State string, UserId string, GroupId string, PermIds []string, keyword string, InstanceType string, limit int32, offset int32, InstanceId, PrivilegeType, DbName, TbName string, Fuzzy bool) (*dao.ColumnPrivileges, error) {
	var (
		Privileges     []*dao.ColumnPrivilege
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		if UserId == "" {
			baseQuery += "and user_id = '' and group_id=? "
		} else {
			baseQuery += "and group_id=? "
		}
		baseConditions = append(baseConditions, GroupId)
	}
	if State != "" {
		baseQuery += "and state=? "
		baseConditions = append(baseConditions, State)
	}
	if len(PermIds) > 0 {
		baseQuery += "and id in (?) "
		baseConditions = append(baseConditions, PermIds)
	}
	if keyword != "" {
		if Fuzzy {
			baseQuery += "and column_name like ? "
			baseConditions = append(baseConditions, "%"+keyword+"%")
		} else {
			baseQuery += "and column_name=? "
			baseConditions = append(baseConditions, keyword)
		}
	}
	if InstanceType != "" {
		baseQuery += "and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if InstanceId != "" {
		baseQuery += "and instance_id=? "
		baseConditions = append(baseConditions, InstanceId)
	}
	if PrivilegeType != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, PrivilegeType)
	}
	if DbName != "" {
		baseQuery += "and db_name=? "
		baseConditions = append(baseConditions, DbName)
	}
	if TbName != "" {
		baseQuery += "and table_name=? "
		baseConditions = append(baseConditions, TbName)
	}
	if err := db.Model(Privileges).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Find(&Privileges).Error; err != nil {
		return nil, err
	}
	ret := &dao.ColumnPrivileges{
		Total:      int32(total),
		Privileges: Privileges,
	}
	return ret, nil

}

func (dal *InstancePrivilegeDal) ListAndDelete(ctx context.Context, TenantId string, UserId string, GroupId string, InstanceType string, InstanceId string, PrivilegeType string) error {
	now := time.Now().Unix()
	var (
		Privileges     []*dao.InstancePrivilege
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		baseQuery += "and group_id=? "
		baseConditions = append(baseConditions, GroupId)
	}
	if InstanceType != "" {
		baseQuery += "and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if InstanceId != "" {
		baseQuery += "and instance_id=? "
		baseConditions = append(baseConditions, InstanceId)
	}
	if PrivilegeType != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, PrivilegeType)
	}

	if err := db.Where(baseQuery, baseConditions...).Find(&Privileges).Error; err != nil {
		return err
	}
	var ids []int64
	for _, privilege := range Privileges {
		ids = append(ids, privilege.ID)
	}

	res := db.Model(&dao.InstancePrivilege{}).Where("tenant_id=? and deleted=0 and id in ?", TenantId, ids).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete InstancePrivilege error %v", err)
	}
	//if res.RowsAffected == 0 {
	//	return fmt.Errorf("privileges not exist")
	//}

	return nil

}

func (dal *DatabasePrivilegeDal) ListAndDelete(ctx context.Context, TenantId string, UserId string, GroupId string, InstanceType string, InstanceId string, dbName string, PrivilegeType string) error {
	now := time.Now().Unix()
	var (
		Privileges     []*dao.DatabasePrivilege
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		baseQuery += "and group_id=? "
		baseConditions = append(baseConditions, GroupId)
	}
	if InstanceType != "" {
		baseQuery += "and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if InstanceId != "" {
		baseQuery += "and instance_id=? "
		baseConditions = append(baseConditions, InstanceId)
	}
	if dbName != "" {
		baseQuery += "and db_name=? "
		baseConditions = append(baseConditions, dbName)
	}
	if PrivilegeType != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, PrivilegeType)
	}

	if err := db.Where(baseQuery, baseConditions...).Find(&Privileges).Error; err != nil {
		return err
	}
	var ids []int64
	for _, privilege := range Privileges {
		ids = append(ids, privilege.ID)
	}

	res := db.Model(&dao.DatabasePrivilege{}).Where("tenant_id=? and deleted=0 and id in ?", TenantId, ids).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete DatabasePrivilege error %v", err)
	}
	//if res.RowsAffected == 0 {
	//	return fmt.Errorf("privileges not exist")
	//}

	return nil

}

func (dal *TablePrivilegeDal) ListAndDelete(ctx context.Context, TenantId string, UserId string, GroupId string, InstanceType string, InstanceId string, dbName string, tableName string, PrivilegeType string) error {
	now := time.Now().Unix()
	var (
		Privileges     []*dao.TablePrivilege
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		baseQuery += "and group_id=? "
		baseConditions = append(baseConditions, GroupId)
	}
	if InstanceType != "" {
		baseQuery += "and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if InstanceId != "" {
		baseQuery += "and instance_id=? "
		baseConditions = append(baseConditions, InstanceId)
	}
	if dbName != "" {
		baseQuery += "and db_name=? "
		baseConditions = append(baseConditions, dbName)
	}
	if tableName != "" {
		baseQuery += "and table_name=? "
		baseConditions = append(baseConditions, tableName)
	}
	if PrivilegeType != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, PrivilegeType)
	}

	if err := db.Where(baseQuery, baseConditions...).Find(&Privileges).Error; err != nil {
		return err
	}
	var ids []int64
	for _, privilege := range Privileges {
		ids = append(ids, privilege.ID)
	}

	res := db.Model(&dao.TablePrivilege{}).Where("tenant_id=? and deleted=0 and id in ?", TenantId, ids).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete TablePrivilege error %v", err)
	}
	//if res.RowsAffected == 0 {
	//	return fmt.Errorf("privileges not exist")
	//}
	return nil
}

func (dal *ColumnPrivilegeDal) ListAndDelete(ctx context.Context, TenantId string, UserId string, GroupId string, InstanceType string, InstanceId string, dbName string, tableName string, columnName string, PrivilegeType string) error {
	now := time.Now().Unix()
	var (
		Privileges     []*dao.ColumnPrivilege
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		baseQuery += "and group_id=? "
		baseConditions = append(baseConditions, GroupId)
	}
	if InstanceType != "" {
		baseQuery += "and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if InstanceId != "" {
		baseQuery += "and instance_id=? "
		baseConditions = append(baseConditions, InstanceId)
	}
	if dbName != "" {
		baseQuery += "and db_name=? "
		baseConditions = append(baseConditions, dbName)
	}
	if tableName != "" {
		baseQuery += "and table_name=? "
		baseConditions = append(baseConditions, tableName)
	}
	if columnName != "" {
		baseQuery += "and column_name=? "
		baseConditions = append(baseConditions, columnName)
	}
	if PrivilegeType != "" {
		baseQuery += "and privilege_type=? "
		baseConditions = append(baseConditions, PrivilegeType)
	}

	if err := db.Where(baseQuery, baseConditions...).Find(&Privileges).Error; err != nil {
		return err
	}
	var ids []int64
	for _, privilege := range Privileges {
		ids = append(ids, privilege.ID)
	}

	res := db.Model(&dao.ColumnPrivilege{}).Where("tenant_id=? and deleted=0 and id in ?", TenantId, ids).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": now,
		"UpdatedAt": now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete TablePrivilege error %v", err)
	}
	//if res.RowsAffected == 0 {
	//	return fmt.Errorf("privileges not exist")
	//}
	return nil
}

func (dal *InstancePrivilegeDal) ListByResource(ctx context.Context, InstanceId string, PrivilegeType string, TenantID string, selfPrivilege bool, UserList []string, GroupList []string) ([]*dao.InstancePrivilege, error) {
	var err error
	var privileges []*dao.InstancePrivilege

	db := dal.dbPro.GetMetaDB(ctx)
	// 用户自己的权限
	if selfPrivilege && len(UserList) > 0 {
		err = db.Where("instance_id = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id in ? and group_id = 0", InstanceId, PrivilegeType, TenantID, UserList).Find(&privileges).Error
	}
	// 组自己的权限
	if selfPrivilege && len(GroupList) > 0 {
		err = db.Where("instance_id = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id = '' and group_id in ?", InstanceId, PrivilegeType, TenantID, stringToInt64(GroupList)).Find(&privileges).Error
	}
	// 用户自己和随组的权限
	if !selfPrivilege && len(UserList) > 0 && len(GroupList) > 0 {
		err = db.Where("instance_id = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id in ? and group_id in ?", InstanceId, PrivilegeType, TenantID, UserList, stringToInt64(GroupList)).Find(&privileges).Error
	}
	if err != nil {
		return nil, err
	}
	return privileges, err
}

func (dal *DatabasePrivilegeDal) ListByResource(ctx context.Context, InstanceId string, database string, PrivilegeType string, TenantID string, selfPrivilege bool, UserList []string, GroupList []string) ([]*dao.DatabasePrivilege, error) {
	var err error
	var privileges []*dao.DatabasePrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	// 用户自己的权限
	if selfPrivilege && len(UserList) > 0 {
		err = db.Where("instance_id = ? and db_name = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id in ? and group_id = 0", InstanceId, database, PrivilegeType, TenantID, UserList).Find(&privileges).Error
	}
	// 组自己的权限
	if selfPrivilege && len(GroupList) > 0 {
		err = db.Where("instance_id = ? and db_name = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id = '' and group_id in ?", InstanceId, database, PrivilegeType, TenantID, stringToInt64(GroupList)).Find(&privileges).Error
	}
	// 用户自己和随组的权限
	if !selfPrivilege && len(UserList) > 0 && len(GroupList) > 0 {
		err = db.Where("instance_id = ? and db_name = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id in ? and group_id in ?", InstanceId, database, PrivilegeType, TenantID, UserList, stringToInt64(GroupList)).Find(&privileges).Error
	}
	if err != nil {
		return nil, err
	}
	return privileges, err
}

func (dal *TablePrivilegeDal) ListByResource(ctx context.Context, InstanceId string, database string, table string, PrivilegeType string, TenantID string, selfPrivilege bool, UserList []string, GroupList []string) ([]*dao.TablePrivilege, error) {
	var err error
	var privileges []*dao.TablePrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	// 用户自己的权限
	if selfPrivilege && len(UserList) > 0 {
		err = db.Where("instance_id = ? and db_name = ? and table_name = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id in ? and group_id = 0", InstanceId, database, table, PrivilegeType, TenantID, UserList).Find(&privileges).Error
	}
	// 组自己的权限
	if selfPrivilege && len(GroupList) > 0 {
		err = db.Where("instance_id = ? and db_name = ? and table_name = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id = '' and group_id in ?", InstanceId, database, table, PrivilegeType, TenantID, stringToInt64(GroupList)).Find(&privileges).Error
	}
	// 用户自己和随组的权限
	if !selfPrivilege && len(UserList) > 0 && len(GroupList) > 0 {
		err = db.Where("instance_id = ? and db_name = ? and table_name = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id in ? and group_id in ?", InstanceId, database, table, PrivilegeType, TenantID, UserList, stringToInt64(GroupList)).Find(&privileges).Error
	}
	if err != nil {
		return nil, err
	}
	return privileges, err
}

func (dal *ColumnPrivilegeDal) ListByResource(ctx context.Context, InstanceId string, database string, table string, column string, PrivilegeType string, TenantID string, selfPrivilege bool, UserList []string, GroupList []string) ([]*dao.ColumnPrivilege, error) {
	var err error
	var privileges []*dao.ColumnPrivilege
	db := dal.dbPro.GetMetaDB(ctx)
	// 用户自己的权限
	if selfPrivilege && len(UserList) > 0 {
		err = db.Where("instance_id = ? and db_name = ? and table_name = ? and column_name = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id in ? and group_id = 0", InstanceId, database, table, column, PrivilegeType, TenantID, UserList).Find(&privileges).Error
	}
	// 组自己的权限
	if selfPrivilege && len(GroupList) > 0 {
		err = db.Where("instance_id = ? and db_name = ? and table_name = ? and column_name = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id = '' and group_id in ?", InstanceId, database, table, column, PrivilegeType, TenantID, stringToInt64(GroupList)).Find(&privileges).Error
	}
	// 用户自己和随组的权限
	if !selfPrivilege && len(UserList) > 0 && len(GroupList) > 0 {
		err = db.Where("instance_id = ? and db_name = ? and table_name = ? and column_name = ? and privilege_type = ? and tenant_id=? and deleted = 0 and user_id in ? and group_id in ?", InstanceId, database, table, column, PrivilegeType, TenantID, UserList, stringToInt64(GroupList)).Find(&privileges).Error
	}
	if err != nil {
		return nil, err
	}
	return privileges, err
}

func (dal *DatabasePrivilegeDal) ListByRootAccount(ctx context.Context, instanceId string, dbNames ...string) (*dao.DatabasePrivileges, error) {
	var (
		privileges []*dao.DatabasePrivilege
		total      int64
	)
	db := dal.dbPro.GetMetaDB(ctx)
	query := db.Model(&dao.DatabasePrivilege{}).Where(`instance_id=?`, instanceId)

	if len(dbNames) > 0 {
		query = query.Where(`db_name in (?)`, dbNames)
	}
	if err := query.Find(&privileges).Offset(-1).Limit(-1).Count(&total).Error; err != nil {
		return nil, err
	}
	return &dao.DatabasePrivileges{
		Privileges: privileges,
		Total:      int32(total),
	}, nil
}

func (dal *DatabasePrivilegeDal) ListDatabases(ctx context.Context) (*dao.DatabasePrivileges, error) {
	var (
		privileges []*dao.DatabasePrivilege
		total      int64
	)
	db := dal.dbPro.GetMetaDB(ctx)
	query := db.Model(&dao.DatabasePrivilege{}).Where(`tenant_id=?`, fwctx.GetTenantID(ctx)).Where(`deleted=?`, 0)
	if err := query.Find(&privileges).Offset(-1).Limit(-1).Count(&total).Error; err != nil {
		return nil, err
	}
	return &dao.DatabasePrivileges{
		Privileges: privileges,
		Total:      int32(total),
	}, nil
}

func stringToInt64(strSlice []string) []int64 {
	// 创建一个 int64 切片来存储结果
	var int64Slice []int64

	// 遍历字符串切片并转换
	for _, str := range strSlice {
		intValue, err := strconv.ParseInt(str, 10, 64) // 10 是基数，64 是字长
		if err != nil {
			continue // 继续下一个字符串
		}
		int64Slice = append(int64Slice, intValue) // 添加到 int64 切片
	}
	return int64Slice
}
