package dal

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	. "gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type ScaleEventDAL interface {
	Create(ctx context.Context, event *dao.DbwAutoScaleEvent) error
	GetEventByRuleId(ctx context.Context, ruleId int64) ([]*dao.DbwAutoScaleEvent, error)
	GetScaleEvents(ctx context.Context, instanceId string, tenantId string, searchParam *model.AutoScaleSearchParam, sortBy string, pageNumber int64, pageSize int64) (*dao.DbwAutoScaleEventInfo, error)
	GetLatestFinishedEventByRuleId(ctx context.Context, ruleId int64) (*dao.DbwAutoScaleEvent, error)
	UpdateScalingEvent(ctx context.Context, eventId int64) error
	UpdateScaleEndEvent(ctx context.Context, after int, eventId int64) error
	UpdateScaleErrorEvent(ctx context.Context, memo string, eventId int64) error

	UpdateAutoScaleEndEvent(ctx context.Context, after string, eventId int64) error
	UpdateAutoScaleErrorEvent(ctx context.Context, memo string, eventId int64) error
	UpdateAutoScaleMetricValueEvent(ctx context.Context, status int, before string, after string, memo string, eventId int64) error
}

func NewScaleEventDAL(dbPro DBProvider) ScaleEventDAL {
	return &ScaleEventDal{dbPro: dbPro}
}

type ScaleEventDal struct {
	dbPro DBProvider
}

func (dal *ScaleEventDal) Create(ctx context.Context, event *dao.DbwAutoScaleEvent) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Clauses(clause.Insert{Modifier: "IGNORE"}).Create(event).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *ScaleEventDal) GetEventByRuleId(ctx context.Context, ruleId int64) ([]*dao.DbwAutoScaleEvent, error) {
	// TODO
	return []*dao.DbwAutoScaleEvent{}, nil
}

func (dal *ScaleEventDal) GetScaleEvents(ctx context.Context, instanceId string, tenantId string, searchParam *model.AutoScaleSearchParam, sortBy string, pageNumber int64, pageSize int64) (*dao.DbwAutoScaleEventInfo, error) {
	var (
		limit          = int(pageSize)
		offset         = int(pageSize * (pageNumber - 1))
		ret            []*dao.DbwAutoScaleEvent
		total          int64
		baseConditions []interface{}
		orderBy        string
	)
	db := dal.dbPro.GetMetaDB(ctx)
	//baseQuery := "instance_id=? and tenant_id=? and status=2 "
	baseQuery := "instance_id=? and tenant_id=?  "
	baseConditions = append(baseConditions, instanceId)
	baseConditions = append(baseConditions, tenantId)
	if searchParam != nil && searchParam.EventId != nil {
		baseQuery += " and event_id like ? "
		baseConditions = append(baseConditions, "%"+*searchParam.EventId+"%")
	}
	if searchParam != nil && searchParam.Action != nil {
		baseQuery += " and scaling_type= ? "
		baseConditions = append(baseConditions, conv.Int64ToStr(int64(*searchParam.Action)))
	}
	if searchParam != nil && searchParam.StartTime != nil {
		baseQuery += " and start_time > ? "
		baseConditions = append(baseConditions, conv.Int64ToStr(*searchParam.StartTime))
	}
	if searchParam != nil && searchParam.EndTime != nil {
		baseQuery += " and end_time < ? "
		baseConditions = append(baseConditions, conv.Int64ToStr(*searchParam.EndTime))
	}
	if sortBy != "" {
		orderBy = "start_time " + sortBy
	} else {
		orderBy = "start_time desc"
	}
	// 求total
	if err := db.Model(ret).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Order(orderBy).Offset(offset).Limit(limit).Find(&ret).Error; err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Order(orderBy).Offset(offset).Limit(limit).Find(&ret)
	}))
	return &dao.DbwAutoScaleEventInfo{
		Total:              total,
		DbwAutoScaleEvents: ret,
	}, nil
}

//func (dal *ScaleEventDal) GetScaleEvents(ctx context.Context, instanceId string, tenantId string, searchParam *model.AutoScaleSearchParam, sortBy string, pageNumber int64, pageSize int64) (*dao.DbwAutoScaleEventInfo, error) {
//	var limit = int(pageSize)
//	var offset = int(pageSize * (pageNumber - 1))
//	db := dal.dbPro.GetMetaDB(ctx)
//	sqlStr := fmt.Sprintf("select * from "+dao.DbwAutoScaleEventTableName+" where instance_id=\"%s\" and tenant_id=\"%s\"", instanceId, tenantId)
//	if searchParam != nil && searchParam.EventId != nil {
//		sqlStr = sqlStr + " and event_id like \"%" + *searchParam.EventId + "%\""
//	}
//	if searchParam != nil && searchParam.Action != nil {
//		sqlStr = sqlStr + " and scaling_type=" + conv.Int64ToStr(int64(*searchParam.Action))
//	}
//	if sortBy != "" {
//		sqlStr = sqlStr + " order by start_time " + sortBy
//	}
//	log.Info(ctx, "bandwidth: sqlStr is %s", sqlStr)
//	var retAll []*dao.DbwAutoScaleEvent
//	err := db.Raw(sqlStr).Scan(&retAll).Error // ignore_security_alert
//	if err != nil {
//		return nil, err
//	}
//
//	var ret []*dao.DbwAutoScaleEvent
//	sqlStr = sqlStr + " limit " + conv.Int64ToStr(int64(offset)) + "," + conv.Int64ToStr(int64(limit))
//	log.Info(ctx, "bandwidth: sqlStr is %s", sqlStr)
//	err = db.Raw(sqlStr).Scan(&ret).Error // ignore_security_alert
//	if err != nil {
//		log.Info(ctx, "bandwidth: get tasks error is :%s", err.Error())
//		return nil, err
//	}
//	return &dao.DbwAutoScaleEventInfo{
//		Total:              int64(len(retAll)),
//		DbwAutoScaleEvents: ret,
//	}, nil
//}

func (dal *ScaleEventDal) GetLatestFinishedEventByRuleId(ctx context.Context, ruleId int64) (*dao.DbwAutoScaleEvent, error) {
	var ret dao.DbwAutoScaleEvent
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where(`rule_id=? and status=? and deleted=0`, ruleId, shared.EventFinished).Order(`end_time desc`).First(&ret).Error
	if err != nil {
		return nil, err
	}
	return &ret, nil
}

func (dal *ScaleEventDal) UpdateScalingEvent(ctx context.Context, eventId int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.DbwAutoScaleEventTableName + " set status = ?, start_time = ? where event_id = ? " // ignore_security_alert
	err := db.Exec(sqlStr, int(shared.EventScaling), time.Now().Unix(), eventId).Error
	return err
}

func (dal *ScaleEventDal) UpdateScaleEndEvent(ctx context.Context, after int, eventId int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.DbwAutoScaleEventTableName + " set status = ?, end_time = ?, after_value = ? where event_id = ? " // ignore_security_alert
	err := db.Exec(sqlStr, int(shared.EventFinished), time.Now().Unix(), after, eventId).Error
	return err
}

func (dal *ScaleEventDal) UpdateScaleErrorEvent(ctx context.Context, memo string, eventId int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.DbwAutoScaleEventTableName + " set status = ?, end_time = ?, memo = ? where event_id = ? " // ignore_security_alert
	err := db.Exec(sqlStr, int(shared.EventError), time.Now().Unix(), memo, eventId).Error
	return err
}

func (dal *ScaleEventDal) UpdateAutoScaleEndEvent(ctx context.Context, after string, eventId int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.DbwAutoScaleEventTableName + " set status = ?, end_time = ?, after_metric = ? where event_id = ? " // ignore_security_alert
	err := db.Exec(sqlStr, int(shared.EventFinished), time.Now().Unix(), after, eventId).Error
	return err
}

func (dal *ScaleEventDal) UpdateAutoScaleErrorEvent(ctx context.Context, memo string, eventId int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.DbwAutoScaleEventTableName + " set status = ?, end_time = ?, memo = ? where event_id = ? " // ignore_security_alert
	err := db.Exec(sqlStr, int(shared.EventError), time.Now().Unix(), memo, eventId).Error
	return err
}

func (dal *ScaleEventDal) UpdateAutoScaleMetricValueEvent(ctx context.Context, status int, before string, after string, memo string, eventId int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.DbwAutoScaleEventTableName + " set  status = ?, end_time = ?, before_metric = ?, after_metric = ?, memo = ? where event_id = ? " // ignore_security_alert
	err := db.Exec(sqlStr, status, time.Now().Unix(), before, after, memo, eventId).Error
	return err
}
