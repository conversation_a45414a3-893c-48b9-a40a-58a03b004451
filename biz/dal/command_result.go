package dal

import (
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

type CommandResultDAL interface {
	BulkCreate(ctx context.Context, his []*dao.CommandResult) error
	List(ctx context.Context, cmdID int64, offset, limit int64) ([]*dao.CommandResult, error)
	Count(ctx context.Context, cmdID int64) int64
	MarkExpired(ctx context.Context, sessionID int64) error
}

func NewCommandResultDAL(dbPro DBProvider) CommandResultDAL {
	return &commandResultDal{dbPro: dbPro}
}

type commandResultDal struct {
	dbPro DBProvider
}

func (self *commandResultDal) BulkCreate(ctx context.Context, his []*dao.CommandResult) error {
	db := self.dbPro.GetMetaDB(ctx)
	return db.CreateInBatches(&his, 100).Error
}

func (self *commandResultDal) List(ctx context.Context, cmdID int64, offset, limit int64) (ret []*dao.CommandResult, err error) {
	db := self.dbPro.GetMetaDB(ctx)
	err = db.Where("cmd_id=?", cmdID).Offset(int(offset)).Limit(int(limit)).Find(&ret).Error
	return
}

func (self *commandResultDal) Count(ctx context.Context, cmdID int64) (total int64) {
	db := self.dbPro.GetMetaDB(ctx)
	_ = db.Model(new(dao.CommandResult)).Where("cmd_id=?", cmdID).Count(&total).Error
	return
}

func (self *commandResultDal) MarkExpired(ctx context.Context, sessionID int64) error {
	db := self.dbPro.GetMetaDB(ctx)
	return db.Model(new(dao.CommandResult)).Where(`session_id=?`, sessionID).Updates(map[string]interface{}{
		"SessionExpired": 1,
	}).Error
}
