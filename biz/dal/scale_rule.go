package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type ScaleRuleDAL interface {
	Create(ctx context.Context, task *dao.DbwAutoScaleRule) error
	GetRuleById(ctx context.Context, ruleId int64) (*dao.DbwAutoScaleRule, error)
	GetRuleByCloudAlarmId(ctx context.Context, cloudAlarmId int64) (*dao.DbwAutoScaleRule, error)
	GetRules(ctx context.Context, instanceId string, tenantId string) ([]*dao.DbwAutoScaleRule, error)
	UpdateRuleById(ctx context.Context, rule *dao.DbwAutoScaleRule) error
	GetRuleByInstanceId(ctx context.Context, instanceId string, tenantId string, scalingType int32) (*dao.DbwAutoScaleRule, error)
	DeleteRuleByInstanceId(ctx context.Context, instanceId string, tenantId string, scalingType int32) error
}

func NewScaleRuleDAL(dbPro DBProvider) ScaleRuleDAL {
	return &ScaleRuleDal{dbPro: dbPro}
}

type ScaleRuleDal struct {
	dbPro DBProvider
}

func (dal *ScaleRuleDal) Create(ctx context.Context, rules *dao.DbwAutoScaleRule) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(rules).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *ScaleRuleDal) GetRuleById(ctx context.Context, ruleId int64) (*dao.DbwAutoScaleRule, error) {
	var ret dao.DbwAutoScaleRule
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("rule_id=? and enable=1 and deleted=0 ", ruleId).First(&ret).Error
	if err != nil {
		return nil, err
	}
	return &ret, nil
}

func (dal *ScaleRuleDal) GetRuleByCloudAlarmId(ctx context.Context, cloudAlarmId int64) (*dao.DbwAutoScaleRule, error) {
	var ret dao.DbwAutoScaleRule
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("cloud_alarm_id=? and enable=1 and deleted=0 ", cloudAlarmId).First(&ret).Error
	if err != nil {
		return nil, err
	}
	return &ret, nil
}

func (dal *ScaleRuleDal) GetRules(ctx context.Context, instanceId string, tenantId string) ([]*dao.DbwAutoScaleRule, error) {
	var ret []*dao.DbwAutoScaleRule
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "select * from " + dao.DbwAutoScaleRulesTableName + " where instance_id=\"" + instanceId + "\" and tenant_id=\"" + tenantId + "\" and deleted=0"
	log.Info(ctx, "auto scale: sqlStr is %s", sqlStr)
	err := db.Raw(sqlStr).Scan(&ret).Error // ignore_security_alert
	if err != nil {
		return nil, err
	}
	return ret, nil
}

func (dal *ScaleRuleDal) GetRuleByInstanceId(ctx context.Context, instanceId string, tenantId string, scalingType int32) (*dao.DbwAutoScaleRule, error) {
	var ret dao.DbwAutoScaleRule
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("instance_id=? and tenant_id=? and scaling_type=? and deleted=0 ", instanceId, tenantId, scalingType).First(&ret).Error
	if err != nil {
		return nil, err
	}
	return &ret, nil
}

func (dal *ScaleRuleDal) DeleteRuleByInstanceId(ctx context.Context, instanceId string, tenantId string, scalingType int32) error {
	// 这里只改标记位,不是真正的物理删除
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := fmt.Sprintf("update %s  set deleted = 1, update_user =\"%s\" ,update_time = %d where instance_id = \"%s\" and tenant_id=\"%s\" and scaling_type=%d and deleted=0 ",
		dao.DbwAutoScaleRulesTableName, fwctx.GetUserID(ctx), time.Now().UnixMilli(), instanceId, tenantId, scalingType) // ignore_security_alert
	log.Info(ctx, "bandwidth: update sqlStr is:%s ", sqlStr)
	err := db.Exec(sqlStr).Error // ignore_security_alert
	return err
}

func (dal *ScaleRuleDal) UpdateRuleById(ctx context.Context, rule *dao.DbwAutoScaleRule) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.DbwAutoScaleRulesTableName + " set observation_window=?,scaling_limit=?, scaling_threshold = ?, update_user =? ,update_time = ? where rule_id=? and scaling_type=? and deleted=0 "
	err := db.Exec(sqlStr, rule.ObservationWindow, rule.ScalingLimit, rule.ScalingThreshold,
		fwctx.GetUserID(ctx), time.Now().UnixMilli(), rule.RuleId, rule.ScalingType).Error // ignore_security_alert
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *gorm.DB) *gorm.DB {
		return db.Exec(sqlStr, rule.ObservationWindow, rule.ScalingLimit, rule.ScalingThreshold,
			fwctx.GetUserID(ctx), time.Now().UnixMilli(), rule.RuleId, rule.ScalingType)
	}))
	return err
}
