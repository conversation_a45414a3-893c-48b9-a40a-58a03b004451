package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"time"
)

type ConsoleOperateRecordDAL interface {
	Create(ctx context.Context, ConsoleOperateRecord *dao.ConsoleOperateRecord) error
	List(ctx context.Context, TenantId string, condition *ConsoleOperateRecordParam) (*dao.ConsoleOperateRecords, error)
	QueryRecordByOrderID(ctx context.Context, operateOrderID int64) (*dao.ConsoleOperateRecord, error)
	UpdateStatusByID(ctx context.Context, ID int64, status int8) error
	DeleteByTime(ctx context.Context, createTime int64) error
}

type DasOperateRecordDAL interface {
	Create(ctx context.Context, DasOperateRecord *dao.DasOperateRecord) error
	List(ctx context.Context, TenantId string, condition *DasOperateRecordParam) (*dao.DasOperateRecords, error)
	DeleteByTime(ctx context.Context, createTime int64) error
}

func NewConsoleOperateRecordDAL(dbPro DBProvider) ConsoleOperateRecordDAL {
	return &ConsoleOperateRecordDal{dbPro: dbPro}
}

type ConsoleOperateRecordDal struct {
	dbPro DBProvider
}

func NewDasOperateRecordDAL(dbPro DBProvider) DasOperateRecordDAL {
	return &DasOperateRecordDal{dbPro: dbPro}
}

type DasOperateRecordDal struct {
	dbPro DBProvider
}

type ConsoleOperateRecordParam struct {
	SqlStatement      string
	StartTime         string
	EndTime           string
	InstanceID        string
	InstanceType      string
	InfluenceLinesMin string
	InfluenceLinesMax string
	ExecuteCostMax    string
	ExecuteCostMin    string
	OperationType     string
	SqlTypeList       []string
	OperationStatus   string
	CreateUserIdList  []string
	CreateUserName    string
	DbName            string
	SubAccount        string
	Limit             *int32
	Offset            *int32
	OrderBy           string
	SortBy            string
}

type DasOperateRecordParam struct {
	InstanceID           string
	InstanceType         string
	StartTime            int64
	EndTime              int64
	CreateUserName       string
	CreateUserIdList     []string
	SubAccount           string
	Limit                *int32
	Offset               *int32
	OrderBy              string
	SortBy               string
	TaskAction           string
	TriggerType          string
	OpsTaskState         string
	DasOperationCategory string
}

func (dal *ConsoleOperateRecordDal) Create(ctx context.Context, record *dao.ConsoleOperateRecord) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(record).Error
	return err
}
func (das *DasOperateRecordDal) Create(ctx context.Context, record *dao.DasOperateRecord) error {
	db := das.dbPro.GetMetaDB(ctx)
	err := db.Create(record).Error
	return err
}

func (dal *ConsoleOperateRecordDal) List(ctx context.Context, TenantId string, condition *ConsoleOperateRecordParam) (*dao.ConsoleOperateRecords, error) {
	var (
		recordList    []*dao.ConsoleOperateRecord
		total         int64
		BaseCondition []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "1 = 1 "

	if TenantId != "" {
		baseQuery += "and tenant_id = ? "
		BaseCondition = append(BaseCondition, TenantId)
	}

	if condition.InstanceID != "" {
		baseQuery += "and instance_id = ? "
		BaseCondition = append(BaseCondition, condition.InstanceID)
	}
	if condition.InstanceType != "" {
		baseQuery += "and instance_type = ? "
		BaseCondition = append(BaseCondition, condition.InstanceType)
	}
	if condition.SqlStatement != "" {
		baseQuery += "and sql_statement like ? "
		BaseCondition = append(BaseCondition, "%"+condition.SqlStatement+"%")
	}
	if condition.OperationType != "" {
		baseQuery += "and operation_type = ? "
		BaseCondition = append(BaseCondition, condition.OperationType)
	}
	if condition.OperationStatus != "" {
		baseQuery += "and operation_status = ? "
		BaseCondition = append(BaseCondition, condition.OperationStatus)
	}
	if len(condition.CreateUserIdList) > 0 {
		baseQuery += "and create_user_id in (?) "
		BaseCondition = append(BaseCondition, condition.CreateUserIdList)
	}
	if condition.SubAccount != "" {
		baseQuery += "and create_user_id = ? "
		BaseCondition = append(BaseCondition, condition.SubAccount)
	}
	if condition.CreateUserName != "" {
		baseQuery += "and create_user_name = ? "
		BaseCondition = append(BaseCondition, condition.CreateUserName)
	}
	if condition.DbName != "" {
		baseQuery += "and db_name = ? "
		BaseCondition = append(BaseCondition, condition.DbName)
	}
	if len(condition.SqlTypeList) > 0 {
		baseQuery += "and sql_type in (?)"
		BaseCondition = append(BaseCondition, condition.SqlTypeList)
	}
	if condition.InfluenceLinesMin != "" && condition.InfluenceLinesMax != "" {
		baseQuery += "and influence_lines >= ? "
		BaseCondition = append(BaseCondition, condition.InfluenceLinesMin)
		baseQuery += "and influence_lines <= ? "
		BaseCondition = append(BaseCondition, condition.InfluenceLinesMax)
	}
	if condition.ExecuteCostMin != "" && condition.ExecuteCostMax != "" {
		baseQuery += "and execute_cost >= ? "
		BaseCondition = append(BaseCondition, condition.ExecuteCostMin)
		baseQuery += "and execute_cost <= ? "
		BaseCondition = append(BaseCondition, condition.ExecuteCostMax)
	}
	if condition.StartTime != "" && condition.EndTime != "" {
		baseQuery += "and create_time >= ? "
		BaseCondition = append(BaseCondition, condition.StartTime)
		baseQuery += "and create_time <= ? "
		BaseCondition = append(BaseCondition, condition.EndTime)
	}

	if err := db.Model(recordList).Where(baseQuery, BaseCondition...).Count(&total).Error; err != nil {
		return nil, err
	}
	if condition.Limit != nil && condition.Offset != nil {
		// 分页
		if err := db.Where(baseQuery, BaseCondition...).Order(condition.OrderBy + " " + condition.SortBy).Offset(int(*condition.Offset)).Limit(int(*condition.Limit)).Find(&recordList).Error; err != nil {
			return nil, err
		}
	} else {
		// 不分页
		if err := db.Where(baseQuery, BaseCondition...).Find(&recordList).Error; err != nil {
			return nil, err
		}
	}
	ret := &dao.ConsoleOperateRecords{
		ConsoleOperateRecords: recordList,
		Total:                 total,
	}
	return ret, nil
}
func (das *DasOperateRecordDal) List(ctx context.Context, TenantId string, condition *DasOperateRecordParam) (*dao.DasOperateRecords, error) {
	var (
		recordList    []*dao.DasOperateRecord
		total         int64
		BaseCondition []interface{}
	)
	db := das.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id = ? and deleted=0 "
	BaseCondition = append(BaseCondition, TenantId)

	if condition.InstanceID != "" {
		baseQuery += "and instance_id = ? "
		BaseCondition = append(BaseCondition, condition.InstanceID)
	}
	if condition.InstanceType != "" {
		baseQuery += "and instance_type = ? "
		BaseCondition = append(BaseCondition, condition.InstanceType)
	}
	if condition.DasOperationCategory != "" {
		baseQuery += "and operation_category = ? "
		BaseCondition = append(BaseCondition, condition.DasOperationCategory)
	}
	if condition.OpsTaskState != "" {
		baseQuery += "and status = ? "
		BaseCondition = append(BaseCondition, condition.OpsTaskState)
	}
	if len(condition.CreateUserIdList) > 0 {
		baseQuery += "and user_id in (?) "
		BaseCondition = append(BaseCondition, condition.CreateUserIdList)
	}
	if condition.SubAccount != "" {
		baseQuery += "and user_id = ? "
		BaseCondition = append(BaseCondition, condition.SubAccount)
	}
	if condition.CreateUserName != "" {
		baseQuery += "and user_name = ? "
		BaseCondition = append(BaseCondition, condition.CreateUserName)
	}
	if condition.TaskAction != "" {
		baseQuery += "and action = ? "
		BaseCondition = append(BaseCondition, condition.TaskAction)
	}
	if condition.TriggerType != "" {
		baseQuery += "and trigger_type =? "
		BaseCondition = append(BaseCondition, condition.TriggerType)
	}
	if condition.StartTime != 0 && condition.EndTime != 0 {
		baseQuery += "and create_time >= ? "
		BaseCondition = append(BaseCondition, condition.StartTime)
		baseQuery += "and create_time <= ? "
		BaseCondition = append(BaseCondition, condition.EndTime)
	}

	if err := db.Model(recordList).Where(baseQuery, BaseCondition...).Count(&total).Error; err != nil {
		return nil, err
	}
	if condition.Limit != nil && condition.Offset != nil {
		// 分页
		if err := db.Where(baseQuery, BaseCondition...).Order(condition.OrderBy + " " + condition.SortBy).Offset(int(*condition.Offset)).Limit(int(*condition.Limit)).Find(&recordList).Error; err != nil {
			return nil, err
		}
	} else {
		// 不分页
		if err := db.Where(baseQuery, BaseCondition...).Find(&recordList).Error; err != nil {
			return nil, err
		}
	}
	ret := &dao.DasOperateRecords{
		DasOperateRecords: recordList,
		Total:             total,
	}
	return ret, nil
}
func (das *ConsoleOperateRecordDal) QueryRecordByOrderID(ctx context.Context, operateOrderID int64) (*dao.ConsoleOperateRecord, error) {
	var (
		BaseCondition        []interface{}
		ConsoleOperateRecord *dao.ConsoleOperateRecord
	)

	if operateOrderID == 0 {
		return nil, nil
	}

	db := das.dbPro.GetMetaDB(ctx)

	query := "operate_order_id = ?"
	BaseCondition = append(BaseCondition, operateOrderID)
	if err := db.Where(query, BaseCondition...).First(&ConsoleOperateRecord).Error; err != nil {
		return nil, err
	}

	return ConsoleOperateRecord, nil
}

func (dal *ConsoleOperateRecordDal) UpdateStatusByID(ctx context.Context, ID int64, status int8) error {
	db := dal.dbPro.GetMetaDB(ctx)

	res := db.Model(&dao.ConsoleOperateRecord{}).Where("id=?", ID).Updates(map[string]interface{}{
		"operation_status": status,
		"update_time":      time.Now().UnixMilli(),
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("ConsoleOperateRecordDAL UpdateStatusByID err, id is: %d,  status is: %d, error: %v", ID, status, err)
	}
	return nil
}

func (dal *ConsoleOperateRecordDal) DeleteByTime(ctx context.Context, createTime int64) error {
	if createTime == 0 {
		return nil
	}

	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.ConsoleOperateRecord{}).Where("create_time <= ?", createTime).Delete(&dao.ConsoleOperateRecord{})
	if err := res.Error; err != nil {
		return fmt.Errorf("DeleteByTime error %v", err)
	}
	return nil
}
func (das *DasOperateRecordDal) DeleteByTime(ctx context.Context, createTime int64) error {
	if createTime == 0 {
		return nil
	}

	db := das.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DasOperateRecord{}).Where("create_time <= ?", createTime).Delete(&dao.DasOperateRecord{})
	if err := res.Error; err != nil {
		return fmt.Errorf("DeleteByTime error %v", err)
	}
	return nil
}
