package dal

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	. "gorm.io/gorm"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

type DbwInstanceDAL interface {
	Create(ctx context.Context, Instance *dao.DbwInstance) error
	CreateBatch(ctx context.Context, Instances []*dao.DbwInstance) error
	SaveBatch(ctx context.Context, Instances []*dao.DbwInstance) error
	// UpdateDbwInstanceAndTicketByID UpdateDbwInstanceByID(ctx context.Context, ID int64, TenantId string, OwnerUid int32, DbaUid int32, DatabaseUser string, DatabasePassword string) error
	UpdateDbwInstanceAndTicketByID(ctx context.Context, InstanceId string, ID int64, TenantId string, OwnerUid string, DbaUid string, DatabaseUser string, DatabasePassword string, SecurityGroupId int64, ApprovalFlowConfigId int64) error
	Delete(ctx context.Context, InstanceId string, InstanceType string, Source string, TenantId string) error
	Get(ctx context.Context, InstanceId string, InstanceType string, Source string, TenantId string, Mode string) (*dao.DbwInstance, error)
	GetByUid(ctx context.Context, Source string, TenantId string, Uid string) (*dao.DbwInstance, error)
	List(ctx context.Context, TenantId string, InstanceType string, Query string, limit int32, offset int32) (*dao.DbwInstances, error)
	ListInstancesAssociatedSecGroup(ctx context.Context, TenantId string, InstanceType string, SecGroupId int64) (int32, error)
	ListInstancesAssociatedApprovalFlowGroup(ctx context.Context, TenantId string, configId int64) (int32, error)
	ListAll(ctx context.Context, TenantId string, Query *datasource.ListInstanceReq, OrderBy string, Sort string) (*dao.DbwInstances, error)
	ListAllInstancesAssociatedApprovalFlow(ctx context.Context, TenantId string, configId int64, limit int32, offset int32) (*dao.DbwInstances, error)
	FindAll(ctx context.Context, InstanceIds []string, InstanceType string, Source string, TenantId string, Mode string) ([]*dao.DbwInstance, error)
	GetInstance(ctx context.Context, instanceId string, linkType string, Mode string) (*dao.DbwInstance, error)
	UpdateInstance(ctx context.Context, ins *dao.DbwInstance) error
	SaveInstance(ctx context.Context, ins *dao.DbwInstance) error
	ListInstance(ctx context.Context, tenantId, source string, limit int, offset int, query dao.ListQuery) (instances []*dao.DbwInstance, total int64, err error)
	ListInstanceWithInstancePriv(ctx context.Context, instanceType string, tenantId string, userId string, source string, limit int, offset int, query dao.ListQuery) (instances []*dao.DbwInstance, total int64, err error)
}

func NewDbwInstanceDAL(dbPro DBProvider) DbwInstanceDAL {
	return &DbwInstanceDal{dbPro: dbPro}
}

type DbwInstanceDal struct {
	dbPro DBProvider
}

func (dal *DbwInstanceDal) List(ctx context.Context, TenantId string, InstanceType string, Query string, limit int32, offset int32) (*dao.DbwInstances, error) {
	var (
		DbwInstances   []*dao.DbwInstance
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and instance_type=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	baseConditions = append(baseConditions, InstanceType)
	if Query != "" {
		baseQuery += "and instance_id like ? "
		baseConditions = append(baseConditions, "%"+Query+"%")
	}
	if err := db.Model(DbwInstances).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Find(&DbwInstances).Error; err != nil {
		return nil, err
	}
	ret := &dao.DbwInstances{
		Total: int32(total),
		Items: DbwInstances,
	}
	return ret, nil
}

func (dal *DbwInstanceDal) ListAll(ctx context.Context, TenantId string, Query *datasource.ListInstanceReq, OrderBy string, Sort string) (*dao.DbwInstances, error) {
	var (
		DbwInstances   []*dao.DbwInstance
		total          int64
		baseConditions []interface{}
		baseQuery      string
	)
	orderBy := "instance_create_time"
	sort := "desc"
	db := dal.dbPro.GetMetaDB(ctx)
	if TenantId == "0" || TenantId == "1" {
		baseQuery = " deleted = 0 "
	} else {
		baseQuery = " tenant_id = ? and deleted = 0 "
		baseConditions = append(baseConditions, TenantId)
	}
	if Query.Type != shared.NoneType {
		baseQuery += " and instance_type = ? "
		baseConditions = append(baseConditions, Query.Type.String())
	}
	if Query.InstanceId != "" {
		baseQuery += " and instance_id like ? "
		baseConditions = append(baseConditions, "%"+Query.InstanceId+"%")
	}
	if Query.InstanceName != "" {
		baseQuery += " and instance_name like ? "
		baseConditions = append(baseConditions, "%"+Query.InstanceName+"%")
	}
	if Query.InstanceStatus != "" {
		baseQuery += " and status =? "
		baseConditions = append(baseConditions, Query.InstanceStatus)
	}
	if Query.DBEngineVersion != "" {
		baseQuery += " and db_engine_version =? "
		baseConditions = append(baseConditions, Query.DBEngineVersion)
	}
	if Query.ControlMode != -1 {
		baseQuery += " and control_mode =? "
		baseConditions = append(baseConditions, Query.ControlMode)
	}
	if Query.SecurityGroupId != "" {
		baseQuery += " and security_group_id =? "
		baseConditions = append(baseConditions, Query.SecurityGroupId)
	}
	if Query.Query != "" {
		baseQuery += " and (instance_id like? or instance_name like? ) "
		baseConditions = append(baseConditions, "%"+Query.Query+"%")
		baseConditions = append(baseConditions, "%"+Query.Query+"%")
	}
	if Query.ProjectName != "" {
		baseQuery += " and project_name =? "
		baseConditions = append(baseConditions, Query.ProjectName)
	}
	switch Query.DbwStatus {
	case model.DbwStatus_Normal:
		baseQuery += " and status !=? "
		baseConditions = append(baseConditions, model.DbwStatus_SyncError.String())
	case model.DbwStatus_SyncError:
		baseQuery += " and status =? "
		baseConditions = append(baseConditions, model.DbwStatus_SyncError.String())
	}
	if OrderBy != "" {
		switch OrderBy {
		case model.OrderByForInstances_CreateTime.String():
			orderBy = "instance_create_time"
		case model.OrderByForInstances_CreateTime.String():
			orderBy = "instance_id"
		case model.OrderByForInstances_InstanceName.String():
			orderBy = "instance_name"
		case model.OrderByForInstances_InstanceType.String():
			orderBy = "instance_type"
		}
	}
	if Sort != "" {
		sort = Sort
	}
	if err := db.Model(DbwInstances).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sort).Find(&DbwInstances).Error; err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sort).Find(&DbwInstances)
	}))
	ret := &dao.DbwInstances{
		Total: int32(total),
		Items: DbwInstances,
	}
	return ret, nil
}

func (dal *DbwInstanceDal) ListAllInstancesAssociatedApprovalFlow(ctx context.Context, TenantId string, configId int64, limit int32, offset int32) (*dao.DbwInstances, error) {
	var (
		DbwInstances   []*dao.DbwInstance
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and approval_flow_config_id=? and deleted=0 and control_mode=1"
	baseConditions = append(baseConditions, TenantId)
	baseConditions = append(baseConditions, configId)
	if err := db.Model(DbwInstances).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Find(&DbwInstances).Error; err != nil {
		return nil, err
	}
	ret := &dao.DbwInstances{
		Total: int32(total),
		Items: DbwInstances,
	}
	return ret, nil
}

func (dal *DbwInstanceDal) ListInstancesAssociatedSecGroup(ctx context.Context, TenantId string, InstanceType string, SecGroupId int64) (int32, error) {
	var (
		DbwInstances   []*dao.DbwInstance
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and instance_type=? and security_group_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	baseConditions = append(baseConditions, InstanceType)
	baseConditions = append(baseConditions, SecGroupId)

	if err := db.Model(DbwInstances).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return 0, err
	}
	return int32(total), nil

}

func (dal *DbwInstanceDal) ListInstancesAssociatedApprovalFlowGroup(ctx context.Context, TenantId string, configId int64) (int32, error) {
	var (
		DbwInstances   []*dao.DbwInstance
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and approval_flow_config_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	baseConditions = append(baseConditions, configId)

	if err := db.Model(DbwInstances).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return 0, err
	}
	return int32(total), nil

}

func (dal *DbwInstanceDal) UpdateDbwInstanceByID(ctx context.Context, ID int64, TenantId string, OwnerUid int32, DbaUid int32, DatabaseUser string, DatabasePassword string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DbwInstance{}).Where("id=? and tenant_id=?", ID, TenantId).Updates(map[string]interface{}{
		"OwnerUid":         OwnerUid,
		"DbaUid":           DbaUid,
		"DatabaseUser":     DatabaseUser,
		"DatabasePassword": DatabasePassword,
		"UpdatedAt":        time.Now().UnixNano() / 1e6,
	})
	//res := db.Raw("update dbw_instance set OwnerUid=%d, DbaUid=%d, DatabaseUser=%s, DatabasePassword=%s, UpdatedAt=%s where id= %d", OwnerUid, DbaUid, DatabaseUser, DatabasePassword, time.Now().UnixNano()/1e6, ID)
	if res.RowsAffected == 0 {
		return fmt.Errorf("update DbwInstance %d info error, DbwInstance not exist", ID)
	}
	if err := res.Error; err != nil {
		return fmt.Errorf("update DbwInstance %d info error %v", ID, err)
	}
	return nil
}

func (dal *DbwInstanceDal) UpdateDbwInstanceAndTicketByID(ctx context.Context, InstanceId string, ID int64, TenantId string, OwnerUid string, DbaUid string, DatabaseUser string, DatabasePassword string, SecurityGroupId int64, ApprovalFlowConfigId int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	tx := db.Begin()
	res := tx.Model(&dao.DbwInstance{}).Where("id=? and tenant_id=?", ID, TenantId).Updates(map[string]interface{}{
		"OwnerUid":             OwnerUid,
		"DbaUid":               DbaUid,
		"DatabaseUser":         DatabaseUser,
		"DatabasePassword":     DatabasePassword,
		"SecurityGroupId":      SecurityGroupId,
		"ApprovalFlowConfigId": ApprovalFlowConfigId,
		"UpdatedAt":            time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("update DbwInstance %d info error %v", ID, err)
	}
	// owner
	updateOwnerSql := "update ticket_record as t, dbw_approval_flow as d set t.current_user_ids = ?, t.update_time = ? " +
		" where t.instance_id = ? and t.tenant_id=? " +
		" and t.ticket_status = 3 and d.flow_id = t.approval_flow_id and d.approver_node_id = 1; "

	res = tx.Exec(updateOwnerSql, fmt.Sprint(OwnerUid), time.Now().UnixNano()/1e6, InstanceId, TenantId)
	//res = tx.Model(&dao.Ticket{}).Where("instance_id = ? and tenant_id=? and ticket_status = 3 and flow_step = 1", InstanceId, TenantId).Updates(map[string]interface{}{
	//	"current_user_ids": OwnerUid,
	//	"update_time":      time.Now().UnixNano() / 1e6,
	//})
	if err := res.Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("update Ticket %d info error %v", ID, err)
	}
	// dba
	updateDbaSql := "update ticket_record as t, dbw_approval_flow as d set t.current_user_ids = ?, t.update_time = ? " +
		" where t.instance_id = ? and t.tenant_id=? " +
		" and t.ticket_status = 3 and d.flow_id = t.approval_flow_id and d.approver_node_id = 0; "

	res = tx.Exec(updateDbaSql, fmt.Sprint(DbaUid), time.Now().UnixNano()/1e6, InstanceId, TenantId)
	//res = tx.Model(&dao.Ticket{}).Where("instance_id = ? and tenant_id=? and ticket_status = 3 and flow_step = 2", InstanceId, TenantId).Updates(map[string]interface{}{
	//	"current_user_ids": fmt.Sprint(DbaUid),
	//	"update_time":      time.Now().UnixNano() / 1e6,
	//})
	if err := res.Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("update Ticket %d info error %v", ID, err)
	}
	tx.Commit()
	return nil
}

func (dal *DbwInstanceDal) Delete(ctx context.Context, InstanceId string, InstanceType string, Source string, TenantId string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DbwInstance{}).Where("instance_id=? and tenant_id=? and instance_type=? and source=? and deleted=0", InstanceId, TenantId, InstanceType, Source).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": time.Now().UnixNano() / 1e6,
		"UpdatedAt": time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete DbwInstance error %v", err)
	}
	return nil
}

func (dal *DbwInstanceDal) Create(ctx context.Context, Instance *dao.DbwInstance) error {
	db := dal.dbPro.GetMetaDB(ctx)
	if Instance.Tags == "" {
		Instance.Tags = "{}"
	}
	err := db.Create(Instance).Error
	return err
}

func (dal *DbwInstanceDal) CreateBatch(ctx context.Context, Instances []*dao.DbwInstance) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(Instances).Error
	return err
}

func (dal *DbwInstanceDal) SaveBatch(ctx context.Context, Instances []*dao.DbwInstance) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Save(Instances).Error
	return err
}

func (dal *DbwInstanceDal) Get(ctx context.Context, InstanceId string, InstanceType string, Source string, TenantId string, Mode string) (*dao.DbwInstance, error) {
	var (
		ret            dao.DbwInstance
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := " instance_id=? and deleted=0 "
	baseConditions = append(baseConditions, InstanceId)
	if InstanceType != "" {
		baseQuery += " and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if Source != "" {
		baseQuery += " and source=? "
		baseConditions = append(baseConditions, Source)
	}
	if TenantId != "" && TenantId != "0" && TenantId != "1" {
		baseQuery += " and tenant_id=? "
		baseConditions = append(baseConditions, TenantId)
	}
	if Mode != "" {
		baseQuery += " and control_mode=? "
		mode, _ := model.ControlModeFromString(Mode)
		baseConditions = append(baseConditions, mode)
	}
	err := db.Where(baseQuery, baseConditions...).First(&ret).Error
	if err != nil {
		return nil, err
	}
	return &ret, err
}

func (dal *DbwInstanceDal) GetByUid(ctx context.Context, Source string, TenantId string, Uid string) (*dao.DbwInstance, error) {
	var ret dao.DbwInstance
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("source=? and deleted=0 and tenant_id=? and (owner_uid = ? or dba_uid = ?) ", Source, TenantId, Uid, Uid).First(&ret).Error
	if err != nil {
		return nil, err
	}
	return &ret, err
}

func (dal *DbwInstanceDal) FindAll(ctx context.Context, InstanceIds []string, InstanceType string, Source string, TenantId string, Mode string) ([]*dao.DbwInstance, error) {
	db := dal.dbPro.GetMetaDB(ctx)
	var (
		rets           []*dao.DbwInstance
		baseConditions []interface{}
	)
	baseQuery := " instance_type=? and deleted=0 and tenant_id=? and instance_id in ? "
	baseConditions = append(baseConditions, InstanceType)
	baseConditions = append(baseConditions, TenantId)
	baseConditions = append(baseConditions, InstanceIds)
	if Mode != "" {
		baseQuery += " and control_mode=? "
		mode, _ := model.ControlModeFromString(Mode)
		baseConditions = append(baseConditions, mode)
	}
	err := db.Where(baseQuery, baseConditions...).Find(&rets).Error
	if err != nil {
		return nil, err
	}
	return rets, err
}

func (dal *DbwInstanceDal) GetInstance(ctx context.Context, instanceId string, linkType string, Mode string) (*dao.DbwInstance, error) {
	db := dal.dbPro.GetMetaDB(ctx)
	var (
		ret            dao.DbwInstance
		baseConditions []interface{}
	)
	baseQuery := " instance_id=?  and source=? and deleted=0 and tenant_id=? "
	baseConditions = append(baseConditions, instanceId)
	baseConditions = append(baseConditions, linkType)
	baseConditions = append(baseConditions, fwctx.GetTenantID(ctx))
	if Mode != "" {
		baseQuery += " and control_mode=? "
		mode, _ := model.ControlModeFromString(Mode)
		baseConditions = append(baseConditions, mode)
	}
	err := db.Where(baseQuery, baseConditions...).First(&ret).Error
	return &ret, err
}

func (dal *DbwInstanceDal) UpdateInstance(ctx context.Context, ins *dao.DbwInstance) error {
	db := dal.dbPro.GetMetaDB(ctx)
	return db.Updates(ins).Error
}

func (dal *DbwInstanceDal) SaveInstance(ctx context.Context, ins *dao.DbwInstance) error {
	db := dal.dbPro.GetMetaDB(ctx)
	return db.Save(ins).Error
}

func (dal *DbwInstanceDal) ListInstance(ctx context.Context, tenantId string, source string, limit int, offset int, query dao.ListQuery) (instances []*dao.DbwInstance, total int64, err error) {
	db := dal.dbPro.GetMetaDB(ctx)
	var baseConditions []interface{}
	baseQuery := " deleted=0 and tenant_id=? and source=? "
	baseConditions = append(baseConditions, tenantId)
	baseConditions = append(baseConditions, source)
	if query.InstanceId != "" {
		baseQuery += " and instance_id like ? "
		baseConditions = append(baseConditions, "%"+query.InstanceId+"%")
	}
	if query.InstanceName != "" {
		baseQuery += " and instance_name like ? "
		baseConditions = append(baseConditions, "%"+query.InstanceName+"%")
	}
	if query.Mode != "" {
		baseQuery += " and control_mode=? "
		mode, _ := model.ControlModeFromString(query.Mode)
		baseConditions = append(baseConditions, mode)
	}
	err = db.Where(baseQuery, baseConditions...).Find(&instances).Limit(limit).Offset(offset).Count(&total).Error
	return
}

func (dal *DbwInstanceDal) ListInstanceWithInstancePriv(ctx context.Context, instanceType string, tenantId string, userId string, source string, limit int, offset int, query dao.ListQuery) (instances []*dao.DbwInstance, total int64, err error) {
	db := dal.dbPro.GetMetaDB(ctx)
	now := time.Now().Unix()
	subQuery := db.Model(&dao.InstancePrivilege{}).Select("DISTINCT(instance_id)").
		Where("instance_type =?", instanceType).
		Where("tenant_id = ?", tenantId).
		Where("user_id = ?", userId).
		Where("deleted = ?", 0).
		Where("expired_at > ?", now).Table("instance_privilege")
	if query.InstanceId != "" {
		subQuery = subQuery.Where("instance_id like ?", "%"+query.InstanceId+"%")
	}
	if query.InstanceName != "" {
		subQuery = subQuery.Where("instance_name like ?", "%"+query.InstanceName+"%")
	}
	// 主查询：从 dbw_instance 表中查询符合条件的记录
	res := db.Model(&dao.DbwInstance{}).
		Where("instance_type =?", instanceType).
		Where("tenant_id = ?", tenantId).
		Where("deleted = ?", 0).
		Where("source = ?", source).
		Where("instance_id IN (?)", subQuery)
	err = res.Find(&instances).Limit(limit).Offset(offset).Count(&total).Error
	return

}
