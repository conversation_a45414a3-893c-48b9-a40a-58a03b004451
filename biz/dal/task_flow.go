package dal

import (
	"context"
	"fmt"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-lib/framework/db"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type TaskFlow interface {
	Create(ctx context.Context, taskFlow *dao.TaskFlow, config *dao.TaskFlowConfig) error
	Get(ctx context.Context, taskFlowID string) (*dao.TaskFlow, *dao.TaskFlowConfig, error)
	InnerGet(ctx context.Context, taskFlowID string) (*dao.TaskFlow, *dao.TaskFlowConfig, error)
	List(ctx context.Context, option *TaskFlowFilterOption) ([]*dao.TaskFlow, int32, error)
	ListByInstanceId(ctx context.Context, instanceId string) ([]*dao.TaskFlow, error)
	Delete(ctx context.Context, taskFlowID string) error
	Update(ctx context.Context, taskFlowID string, updates map[string]interface{}) error
}

func NewTaskFlowDal(provider DBProvider) TaskFlow {
	return &taskFlow{provider}
}

type taskFlow struct {
	DBProvider
}

func (t *taskFlow) Create(ctx context.Context, flow *dao.TaskFlow, config *dao.TaskFlowConfig) error {
	dbx := t.GetMetaDB(ctx)
	return db.ExecWithinTransaction(dbx, func(db *db.DB) error {
		if err := db.Create(flow).Error; err != nil {
			return err
		}
		if err := db.Create(config).Error; err != nil {
			return err
		}
		return nil
	})
}

func (t *taskFlow) Get(ctx context.Context, taskFlowID string) (*dao.TaskFlow, *dao.TaskFlowConfig, error) {
	dbx := t.GetMetaDB(ctx)
	taskFlow := &dao.TaskFlow{}
	if err := dbx.Where(`id=?`, taskFlowID).
		Where(`tenant_id=?`, fwctx.GetTenantID(ctx)).
		Where(`deleted=?`, 0).
		First(&taskFlow).Error; err != nil {
		//if err := dbx.Where(`id=?`, taskFlowID).Where(`deleted=?`, 0).First(&taskFlow).Error; err != nil {
		return nil, nil, err
	}
	var taskFlowConfig *dao.TaskFlowConfig
	if err := dbx.Where(`task_flow_id=?`, taskFlowID).First(&taskFlowConfig).Error; err != nil {
		return nil, nil, err
	}
	return taskFlow, taskFlowConfig, nil
}

func (t *taskFlow) InnerGet(ctx context.Context, taskFlowID string) (*dao.TaskFlow, *dao.TaskFlowConfig, error) {
	dbx := t.GetMetaDB(ctx)
	taskFlow := &dao.TaskFlow{}
	if err := dbx.Where(`id=?`, taskFlowID).Where(`deleted=?`, 0).First(&taskFlow).Error; err != nil {
		//if err := dbx.Where(`id=?`, taskFlowID).Where(`deleted=?`, 0).First(&taskFlow).Error; err != nil {
		return nil, nil, err
	}

	var taskFlowConfig *dao.TaskFlowConfig
	if err := dbx.Where(`task_flow_id=?`, taskFlowID).First(&taskFlowConfig).Error; err != nil {
		return nil, nil, err
	}
	return taskFlow, taskFlowConfig, nil
}

func (t *taskFlow) List(ctx context.Context, option *TaskFlowFilterOption) ([]*dao.TaskFlow, int32, error) {
	var taskFlows []*dao.TaskFlow
	var total int64
	dbx := t.GetMetaDB(ctx)
	query := t.generateCondition(ctx, dbx, option)
	if option.PageSize == nil {
		option.PageSize = utils.Int32Ref(10)
	}
	if option.PageNumber == nil {
		option.PageNumber = utils.Int32Ref(1)
	}
	if option.OrderBy != nil {
		if option.SortBy != nil {
			query = query.Order(*option.OrderBy + " " + *option.SortBy) // ignore_security_alert
		} else {
			query = query.Order(*option.OrderBy) // ignore_security_alert
		}
	} else {
		query = query.Order("id desc")
	}
	query = query.Limit(int(*option.PageSize)).Offset(int((*option.PageNumber - 1) * (*option.PageSize)))
	//log.Info(ctx, "list taskflow query %s", dbx.ToSQL(func(dbx *gorm.DB) *gorm.DB {
	//	dbx = t.generateCondition(dbx, option)
	//	return dbx.Limit(int(*option.PageSize)).Offset(int((*option.PageNumber-1)*(*option.PageSize))).Find(&taskFlows)
	//}))
	if err := query.Find(&taskFlows).Offset(-1).Limit(-1).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	return taskFlows, int32(total), nil
}

func (t *taskFlow) ListByInstanceId(ctx context.Context, instanceId string) ([]*dao.TaskFlow, error) {
	var taskFlows []*dao.TaskFlow
	dbx := t.GetMetaDB(ctx)
	query := dbx.Model(&dao.TaskFlow{}).Where(`instance_id=?`, instanceId).Where(`deleted=0`)
	err := query.Find(&taskFlows).Error
	return taskFlows, err
}

func (t *taskFlow) count(ctx context.Context, dbx *db.DB, option *TaskFlowFilterOption) (int64, error) {
	var total int64
	query := t.generateCondition(ctx, dbx, option)
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (t *taskFlow) generateCondition(ctx context.Context, dbx *db.DB, option *TaskFlowFilterOption) *gorm.DB {
	query := dbx.Model(&dao.TaskFlow{}).Where(`tenant_id=?`, fwctx.GetTenantID(ctx)).Where(`deleted=?`, 0)
	//query := dbx.Model(&dao.TaskFlow{}).Where(`deleted=?`, 0)
	if option != nil {
		if option.LastExecuteState != nil {
			query = query.Where(`last_execute_state=?`, *option.LastExecuteState)
		}
		if option.State != nil {
			query = query.Where(`state=?`, *option.State)
		}
		if option.SearchParam != nil {
			query = query.Where(`id like ? or name like ?`, fmt.Sprintf(`%%%s%%`, *option.SearchParam), fmt.Sprintf(`%%%s%%`, *option.SearchParam))
		}
	}
	return query
}

func (t *taskFlow) Delete(ctx context.Context, taskFlowID string) error {
	dbx := t.GetMetaDB(ctx)
	return db.ExecWithinTransaction(dbx, func(db *db.DB) error {
		if err := db.Model(&dao.TaskFlow{}).Where(`id=?`, taskFlowID).Where(`tenant_id=?`, fwctx.GetTenantID(ctx)).Updates(map[string]interface{}{
			//if err := db.Model(&dao.TaskFlow{}).Where(`id=?`, taskFlowID).Updates(map[string]interface{}{
			"deleted": 1,
		}).Error; err != nil {
			return err
		}
		if err := db.Model(&dao.TaskFlowConfig{}).Where(`task_flow_id=?`, taskFlowID).Updates(map[string]interface{}{
			"deleted": 1,
		}).Error; err != nil {
			return err
		}
		if err := db.Model(&dao.TaskFlowJob{}).Where(`task_flow_id=?`, taskFlowID).Updates(map[string]interface{}{
			"deleted": 1,
		}).Error; err != nil {
			return err
		}
		return nil
	})
}

func (t *taskFlow) Update(ctx context.Context, taskFlowID string, updates map[string]interface{}) error {
	var config *dao.TaskFlowConfig
	var ok bool
	if updates["cycle_config"] != nil {
		config, ok = updates["cycle_config"].(*dao.TaskFlowConfig)
		delete(updates, "cycle_config")
	}
	dbx := t.GetMetaDB(ctx)
	query := dbx.Model(&dao.TaskFlow{}).Where(`id=?`, taskFlowID)
	if tenantID := fwctx.GetTenantID(ctx); tenantID != "" {
		query = query.Where(`tenant_id=?`, tenantID)
	}
	return db.ExecWithinTransaction(dbx, func(db *db.DB) error {
		if err := query.Updates(updates).Error; err != nil {
			return err
		}
		if ok && config != nil {
			if err := db.Model(&dao.TaskFlowConfig{}).Where(`id=?`, config.ID).Updates(*config).Error; err != nil {
				log.Warn(ctx, "update config error %s", err)
				return err
			}
		}
		return nil
	})
}

type TaskFlowFilterOption struct {
	SearchParam      *string
	State            *string
	LastExecuteState *string
	PageNumber       *int32
	PageSize         *int32
	OrderBy          *string
	SortBy           *string
}

type Job interface {
	Create(ctx context.Context, job *dao.TaskFlowJob) error
	Update(ctx context.Context, job *dao.TaskFlowJob) error
	Get(ctx context.Context, jobID string) (*dao.TaskFlowJob, error)
	List(ctx context.Context, taskFlowID string, option *JobsFilterOption) ([]*dao.TaskFlowJob, int32, error)
	MarkFailed(ctx context.Context, job *dao.TaskFlowJob) error
}

func NewTaskFlowJobDal(provider DBProvider) Job {
	return &job{provider}
}

type job struct {
	DBProvider
}

func (j *job) Create(ctx context.Context, job *dao.TaskFlowJob) error {
	dbx := j.GetMetaDB(ctx)
	return db.ExecWithinTransaction(dbx, func(d *db.DB) error {
		if err := d.Model(&dao.TaskFlowJob{}).Create(job).Error; err != nil {
			return err
		}
		var t *dao.TaskFlow
		err := d.Clauses(clause.Locking{Strength: "SHARE"}).Model(&dao.TaskFlow{}).Where(`id=?`, job.TaskFlowID).First(&t).Error
		if err != nil {
			return err
		}
		if err := d.Model(&dao.TaskFlow{}).Where(`id=?`, job.TaskFlowID).Updates(map[string]interface{}{
			"last_execute_time":  job.StartTime,
			"executed_times":     t.ExecutedTimes + 1,
			"last_execute_state": model.JobState_Executing.String()}).Error; err != nil {
			return err
		}
		return nil
	})
}

func (j *job) Update(ctx context.Context, job *dao.TaskFlowJob) error {
	//return j.GetMetaDB(ctx).Model(&dao.TaskFlowJob{}).Where(`id=?`, job.ID).Updates(job).Error
	dbx := j.GetMetaDB(ctx)
	return db.ExecWithinTransaction(dbx, func(d *db.DB) error {
		if err := dbx.Model(&dao.TaskFlowJob{}).Where(`id=?`, job.ID).Updates(job).Error; err != nil {
			return err
		}
		var t *dao.TaskFlow
		if err := d.Clauses(clause.Locking{Strength: "SHARE"}).Model(&dao.TaskFlow{}).Where(`id=?`, job.TaskFlowID).
			First(&t).Error; err != nil {
			return err
		}
		_, counter, err := j.List(ctx, job.TaskFlowID, &JobsFilterOption{State: utils.StringRef(model.JobState_Executing.String())})
		if err != nil {
			return err
		}
		if job.Status != "" && t.LastExecuteState != job.Status && counter == 0 {
			if err := d.Model(&dao.TaskFlow{}).Where(`id=?`, job.TaskFlowID).Updates(map[string]interface{}{
				"last_execute_state": job.Status}).Error; err != nil {
				return err
			}

		}
		return nil
	})
}

func (j *job) MarkFailed(ctx context.Context, job *dao.TaskFlowJob) error {
	dbx := j.GetMetaDB(ctx)
	return db.ExecWithinTransaction(dbx, func(d *db.DB) error {
		if err := dbx.Model(&dao.TaskFlowJob{}).Where(`id=?`, job.ID).Updates(job).Error; err != nil {
			return err
		}
		var t *dao.TaskFlow
		if err := d.Clauses(clause.Locking{Strength: "UPDATE"}).Model(&dao.TaskFlow{}).Where(`id=?`, job.TaskFlowID).
			First(&t).Error; err != nil {
			return err
		}
		if err := d.Model(&dao.TaskFlow{}).Where(`id=?`, job.TaskFlowID).Updates(map[string]interface{}{
			"failed_times":       t.FailedTimes + 1,
			"last_execute_state": model.JobState_Failed.String()}).Error; err != nil {
			return err
		}
		return nil
	})
}

func (j *job) Get(ctx context.Context, jobID string) (job *dao.TaskFlowJob, err error) {
	err = j.GetMetaDB(ctx).Where(`id=?`, jobID).First(&job).Error
	return job, err
}

func (j *job) List(ctx context.Context, taskFlowID string, option *JobsFilterOption) ([]*dao.TaskFlowJob, int32, error) {
	dbx := j.GetMetaDB(ctx)
	query := dbx.Model(&dao.TaskFlowJob{}).Where(`task_flow_id=?`, taskFlowID).Where(`deleted=?`, 0)
	if option.State != nil {
		query = query.Where(`status=?`, *option.State)
	}
	if option.SearchParam != nil {
		query = query.Where(`id like ?`, fmt.Sprintf(`%%%s%%`, *option.SearchParam))
	}
	if option.OrderBy != nil {
		if option.SortBy != nil {
			query = query.Order(*option.OrderBy + " " + *option.SortBy) // ignore_security_alert
		} else {
			query = query.Order(*option.OrderBy) // ignore_security_alert
		}
	}
	if option.PageSize == nil {
		option.PageSize = utils.Int32Ref(10)
	}
	if option.PageNumber == nil {
		option.PageNumber = utils.Int32Ref(1)
	}
	//if err := dbx.Count(&total).Error; err != nil {
	//	return nil, 0, err
	//}
	query = query.Limit(int(*option.PageSize)).Offset(int((*option.PageNumber - 1) * (*option.PageSize)))
	var jobs []*dao.TaskFlowJob
	var total int64
	if err := query.Find(&jobs).Offset(-1).Limit(-1).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	return jobs, int32(total), nil
}

type JobsFilterOption struct {
	SearchParam *string
	State       *string
	PageNumber  *int32
	PageSize    *int32
	OrderBy     *string
	SortBy      *string
}
