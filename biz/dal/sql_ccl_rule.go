package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
	"time"
)

type SqlCCLQueryFilter struct {
	RuleState      []model.RuleState
	SqlType        []model.SqlType
	ThrottleMode   string
	ThrottleType   string
	ThrottleTarget string
	EndpointType   string
	TaskIds        []string
}
type SqlCCLRulesDAL interface {
	Create(ctx context.Context, Rule *dao.SqlCCLRule) error
	List(ctx context.Context, instanceId string, queryFilter *SqlCCLQueryFilter, sortBy string, orderBy string, limit int32, offset int32, isAll bool) (*dao.SqlCclRulesInfo, error)
	ListCCLRuleByState(ctx context.Context, instanceId string, RuleState []model.RuleState, Deleted int8, lastTime int64) (*dao.SqlCclRulesInfo, error)
	ListInstancesByState(ctx context.Context, RuleState []model.RuleState) []string
	UpdateCCLRuleByID(ctx context.Context, ID int64, CCLRule int64) error
	UpdateStatusByID(ctx context.Context, ID int64, status int8) error
	UpdateRejectedCountByID(ctx context.Context, ID int64, Rejected int64) error
	UpdateThresholdByID(ctx context.Context, ID int64, threshold int32) error
	UpdateStatusTimeByID(ctx context.Context, ID int64, status int8) error
	Delete(ctx context.Context, ID int64) error
	Get(ctx context.Context, ID int64) (*dao.SqlCCLRule, error)
	GetDeletedRule(ctx context.Context, ID int64) (*dao.SqlCCLRule, error)
}

func NewSqlCCLRulesDAL(dbPro DBProvider) SqlCCLRulesDAL {
	return &SqlCCLRulesDal{dbPro: dbPro}
}

type SqlCCLRulesDal struct {
	dbPro DBProvider
}

func (dal *SqlCCLRulesDal) List(ctx context.Context, instanceId string, queryFilter *SqlCCLQueryFilter, sortBy string, orderBy string, limit int32, offset int32, isAll bool) (*dao.SqlCclRulesInfo, error) {
	var (
		SqlCCLRules    []*dao.SqlCCLRule
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "instance_id=? and deleted=0 and state not in (2,7) "
	baseConditions = append(baseConditions, instanceId)
	if queryFilter != nil {
		if len(queryFilter.SqlType) > 0 {
			baseQuery += "and sql_type IN (?) "
			baseConditions = append(baseConditions, queryFilter.SqlType)
		}
		if len(queryFilter.RuleState) > 0 {
			baseQuery += "and state IN (?) "
			baseConditions = append(baseConditions, queryFilter.RuleState)
		}
		if queryFilter.ThrottleMode != "" {
			baseQuery += "and throttle_mode=? "
			baseConditions = append(baseConditions, queryFilter.ThrottleMode)
		}
		if queryFilter.ThrottleType != "" {
			baseQuery += "and throttle_type=? "
			baseConditions = append(baseConditions, queryFilter.ThrottleType)
		}
		if queryFilter.ThrottleTarget != "" {
			baseQuery += "and throttle_target=? "
			baseConditions = append(baseConditions, queryFilter.ThrottleTarget)
		}
		if queryFilter.EndpointType != "" {
			baseQuery += "and endpoint_type=? "
			baseConditions = append(baseConditions, queryFilter.EndpointType)
		}
		if len(queryFilter.TaskIds) > 0 {
			baseQuery += "and id IN (?) "
			baseConditions = append(baseConditions, queryFilter.TaskIds)
		}
	}
	if err := db.Model(SqlCCLRules).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if !isAll {
		if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Order(orderBy + " " + sortBy).Find(&SqlCCLRules).Error; err != nil {
			return nil, err
		}
	} else {
		if err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Find(&SqlCCLRules).Error; err != nil {
			return nil, err
		}
	}

	ret := &dao.SqlCclRulesInfo{
		Total:       int32(total),
		SqlCCLRules: SqlCCLRules,
	}
	return ret, nil
}

func (dal *SqlCCLRulesDal) ListCCLRuleByState(ctx context.Context, instanceId string, RuleState []model.RuleState, Deleted int8, lastTime int64) (*dao.SqlCclRulesInfo, error) {
	db := dal.dbPro.GetMetaDB(ctx)
	type Result struct {
		Keywords string `gorm:"column:keywords"`
		SqlType  int8   `gorm:"column:sql_type"`
		Count    int8   `gorm:"column:count"`
	}
	var (
		SqlCCLRules, filterRules []*dao.SqlCCLRule
		total                    int64
		res                      []*Result
	)
	convertedList := make([]*dao.SqlCCLRule, 0)
	// 仅在限流巡检中适用!!，其他情况请选择一种状态过滤
	if len(RuleState) > 1 {
		if err := db.Model(SqlCCLRules).Where("state in (?) and instance_id=? and deleted=? and updated_at > ? and throttle_mode=?", RuleState, instanceId, Deleted, lastTime, model.ThrottleMode_DBThrottle.String()).Select("keywords, sql_type, COUNT(*) AS count").Group("keywords, sql_type").Having("count > 1").Find(&res).Error; err != nil {
			return nil, err
		}
		for _, rule := range res {
			// 查询是否存在相同关键字和限流状态是否存在于运行中的限流任务
			rsp := db.Model(SqlCCLRules).Where("state in (?) and instance_id=? and deleted=? and keywords=? and sql_type in (?) and updated_at > ? and throttle_mode=?", model.RuleState_ACTIVE, instanceId, Deleted, rule.Keywords, rule.SqlType, lastTime, model.ThrottleMode_DBThrottle.String()).Find(&SqlCCLRules)
			if rsp.Error != nil {
				log.Warn(ctx, "get keyword %s sql_type %s cclRule failed %+v", rule.Keywords, rule.SqlType, rsp.Error.Error())
				continue
			} else {
				// 运行中不存在相同关键字的规则，可以删除(仅针对内核限流)
				// FIXME: 需要兼容代理限流
				if len(SqlCCLRules) == 0 {
					if err := db.Where("state in (?) and instance_id=? and deleted=? and keywords=? and sql_type in (?) and updated_at > ? and throttle_mode=?", RuleState, instanceId, Deleted, rule.Keywords, rule.SqlType, lastTime, model.ThrottleMode_DBThrottle.String()).Order("updated_at ASC").First(&filterRules).Error; err != nil {
						return nil, err
					}
					convertedList = append(convertedList, filterRules...)
				}
			}
		}
		return &dao.SqlCclRulesInfo{
			Total:       int32(len(convertedList)),
			SqlCCLRules: convertedList,
		}, nil

	} else {
		if err := db.Where("state in (?) and instance_id=? and deleted=? and updated_at > ?", RuleState, instanceId, Deleted, lastTime).Order("updated_at ASC").Find(&SqlCCLRules).Error; err != nil {
			return nil, err
		}
		if err := db.Model(SqlCCLRules).Where("state in (?) and instance_id=? and deleted=? and updated_at > ?", RuleState, instanceId, Deleted, lastTime).Count(&total).Error; err != nil {
			return nil, err
		}
		return &dao.SqlCclRulesInfo{
			Total:       int32(total),
			SqlCCLRules: convertedList,
		}, nil
	}

}

func (dal *SqlCCLRulesDal) ListInstancesByState(ctx context.Context, RuleState []model.RuleState) []string {
	db := dal.dbPro.GetMetaDB(ctx)
	var instanceIds []string
	db.Model(&dao.SqlCCLRule{}).Where("deleted=0 AND state in (?) ", RuleState).Pluck("DISTINCT(instance_id)", &instanceIds)
	return instanceIds

}
func (dal *SqlCCLRulesDal) UpdateCCLRuleByID(ctx context.Context, ID int64, CCLRule int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SqlCCLRule{}).Where("id=?", ID).Updates(map[string]interface{}{
		"RuleID":    CCLRule,
		"UpdatedAt": time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save Rule %d ccl_rule %d info error %v", ID, CCLRule, err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("save Rule %d ccl_rule %d info error, Rule not exist", ID, CCLRule)
	}
	return nil
}

func (dal *SqlCCLRulesDal) UpdateStatusByID(ctx context.Context, ID int64, status int8) error {
	db := dal.dbPro.GetMetaDB(ctx)
	if status == int8(model.RuleState_ACTIVE) {
		res := db.Model(&dao.SqlCCLRule{}).Where("id=?", ID).Updates(map[string]interface{}{
			"State":     status,
			"CreatedAt": time.Now().UnixNano() / 1e6,
			"UpdatedAt": time.Now().UnixNano() / 1e6,
		})
		if err := res.Error; err != nil {
			return fmt.Errorf("save Rule %d status %d info error %v", ID, status, err)
		}
		if res.RowsAffected == 0 {
			return fmt.Errorf("save Rule %d status %d info error, Rule not exist", ID, status)
		}
	} else {
		res := db.Model(&dao.SqlCCLRule{}).Where("id=?", ID).Updates(map[string]interface{}{
			"State":     status,
			"UpdatedAt": time.Now().UnixNano() / 1e6,
		})
		if err := res.Error; err != nil {
			return fmt.Errorf("save Rule %d status %d info error %v", ID, status, err)
		}
		if res.RowsAffected == 0 {
			return fmt.Errorf("save Rule %d status %d info error, Rule not exist", ID, status)
		}
	}
	return nil
}
func (dal *SqlCCLRulesDal) UpdateRejectedCountByID(ctx context.Context, ID int64, Rejected int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SqlCCLRule{}).Where("id=?", ID).Updates(map[string]interface{}{
		"RejectedCount": Rejected,
		"UpdatedAt":     time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save rejected count %d ccl_rule %d info error %v", Rejected, ID, err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("save rejected count %d ccl_rule %d info error, Rule not exist", Rejected, ID)
	}
	return nil
}

func (dal *SqlCCLRulesDal) UpdateThresholdByID(ctx context.Context, ID int64, threshold int32) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SqlCCLRule{}).Where("id=?", ID).Updates(map[string]interface{}{
		"ThrottleThreshold": threshold,
		"UpdatedAt":         time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save threshold %d ccl_rule %d info error %v", threshold, ID, err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("save threshold %d ccl_rule %d info error, Rule not exist", threshold, ID)
	}
	return nil
}

func (dal *SqlCCLRulesDal) UpdateStatusTimeByID(ctx context.Context, ID int64, status int8) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SqlCCLRule{}).Where("id=?", ID).Updates(map[string]interface{}{
		"State":     status,
		"UpdatedAt": time.Now().UnixNano() / 1e6,
		"StoppedAt": time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save Rule %d status %d info error %v", ID, status, err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("save Rule %d status %d info error, Rule not exist", ID, status)
	}
	return nil
}

func (dal *SqlCCLRulesDal) Delete(ctx context.Context, ID int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SqlCCLRule{}).Where("id=?", ID).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": time.Now().UnixNano() / 1e6,
		"UpdatedAt": time.Now().UnixNano() / 1e6,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete SqlCCL rules error %v", err)
	}
	return nil
}

func (dal *SqlCCLRulesDal) Create(ctx context.Context, Rule *dao.SqlCCLRule) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(Rule).Error
	return err
}

func (dal *SqlCCLRulesDal) Get(ctx context.Context, ID int64) (*dao.SqlCCLRule, error) {
	var ret dao.SqlCCLRule
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("id=? and deleted=0", ID).First(&ret).Error
	return &ret, err
}

func (dal *SqlCCLRulesDal) GetDeletedRule(ctx context.Context, ID int64) (*dao.SqlCCLRule, error) {
	var ret dao.SqlCCLRule
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("id=? and deleted=1", ID).First(&ret).Error
	return &ret, err
}
