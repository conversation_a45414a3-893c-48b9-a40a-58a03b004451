package dal

import (
	"context"
	"fmt"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

type SlowQueryAdviceConfigDAL interface {
	Create(ctx context.Context, config *dao.SlowQueryAdviceConfig) error
	Update(ctx context.Context, config *dao.SlowQueryAdviceConfig) error
	Get(ctx context.Context, instanceId string, tenantId string) (*dao.SlowQueryAdviceConfig, error)
	ListAll(ctx context.Context, InstanceType string, pageNo, pageSize int) (int, []*dao.SlowQueryAdviceConfig, error)
	Count(ctx context.Context) (int, error)
}

func NewSlowQueryAdviceConfigDAL(dbPro DBProvider) SlowQueryAdviceConfigDAL {
	return &SlowQueryAdviceConfigDal{dbPro: dbPro}
}

type SlowQueryAdviceConfigDal struct {
	dbPro DBProvider
}

// Count implements SlowQueryAdviceConfigDAL.
func (dal *SlowQueryAdviceConfigDal) Count(ctx context.Context) (int, error) {
	db := dal.dbPro.GetMetaDB(ctx)
	ret := 0
	sqlStr := "select count(1) from " + dao.SlowQueryAdviceConfigTableName
	err := db.Raw(sqlStr).Scan(&ret).Error
	return ret, err
}

// Create implements SlowQueryAdviceConfigDAL.
func (dal *SlowQueryAdviceConfigDal) Create(ctx context.Context, config *dao.SlowQueryAdviceConfig) error {
	err := dal.dbPro.GetMetaDB(ctx).Create(config).Error
	return err
}

// Get implements SlowQueryAdviceConfigDAL.
func (dal *SlowQueryAdviceConfigDal) Get(ctx context.Context, instanceId string, tenantId string) (*dao.SlowQueryAdviceConfig, error) {
	var (
		BaseCondition        []interface{}
		SlowQueryAdviceConfig *dao.SlowQueryAdviceConfig
	)

	if instanceId == "" {
		return nil, nil
	}

	db := dal.dbPro.GetMetaDB(ctx)

	query := "instance_id =? AND tenant_id =? AND deleted_at =0"
	BaseCondition = append(BaseCondition, instanceId)
	BaseCondition = append(BaseCondition, tenantId)
	if err := db.Where(query, BaseCondition...).First(&SlowQueryAdviceConfig).Error; err != nil {
		return nil, err
	}
	return SlowQueryAdviceConfig, nil
}

// ListAll implements SlowQueryAdviceConfigDAL.
func (dal *SlowQueryAdviceConfigDal) ListAll(ctx context.Context, InstanceType string, offset, limit int) (int, []*dao.SlowQueryAdviceConfig, error) {
	var (
		SecGroups      []*dao.SlowQueryAdviceConfig
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "deleted_at=0 "

	if InstanceType != "" {
		baseQuery += "and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if err := db.Model(SecGroups).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return 0, nil, err
	}
	if limit > 0 {
		// 分页
		if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Find(&SecGroups).Error; err != nil {
			return 0, nil, err
		}
	} else {
		// 不分页
		if err := db.Where(baseQuery, baseConditions...).Find(&SecGroups).Error; err != nil {
			return 0, nil, err
		}
	}
	return int(total), SecGroups, nil
}

// Update implements SlowQueryAdviceConfigDAL.
func (dal *SlowQueryAdviceConfigDal) Update(ctx context.Context, config *dao.SlowQueryAdviceConfig) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.SlowQueryAdviceConfigTableName +
		" set status = ?" +
		", analysis_res_keep_days = ?" +
		", optimize_track_minute = ?" +
		" where instance_id= ? and tenant_id = ?"
	err := db.Exec(sqlStr, config.Status, fmt.Sprintf("%d", config.AnalysisResKeepDays), fmt.Sprintf("%d", config.OptimizeTrackMinute), config.InstanceID, config.TenantID).Error
	return err
}

var (
	_ SlowQueryAdviceConfigDAL = (*SlowQueryAdviceConfigDal)(nil)
)
