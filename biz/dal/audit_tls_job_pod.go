package dal

import (
	"context"
	"github.com/qjpcpu/fp"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

const (
	JOB_WHERE_CONDITION      = "instance_id=? and tenant_id=? and deleted=0"
	JOBNAME_WHERE_CONDITION  = "instance_id=? and job_name=? and deleted=0"
	PODNAME_WHERE_CONDITION  = "instance_id=? and pod_name=? and deleted=0"
	INSTANCE_WHERE_CONDITION = "instance_id=? and deleted=0"

	PODNAME_ONLY_WHERE_CONDITION     = "pod_name=?"
	PODNAME_NOT_DELETED              = "pod_name=? and deleted=0"
	DELETED_POD_ONLY_WHERE_CONDITION = "pod_name=? and status=? and deleted=?"
)

type AuditTlsJobPodDAL interface {
	Create(ctx context.Context, cs *dao.AuditTlsJobPod) error
	CreateBatch(ctx context.Context, cs []*dao.AuditTlsJobPod) error
	//Get(ctx context.Context, instanceId string, tenantId string, jobName string) (*dao.AuditTlsJobPod, error)
	GetByPod(ctx context.Context, podName string) (*dao.AuditTlsJobPod, error)
	GetPod(ctx context.Context, podName string, status int64, deleted int64) ([]*dao.AuditTlsJobPod, error)
	//DeleteJob(ctx context.Context, instanceId string, jobName string) error
	DeletePod(ctx context.Context, instanceId string, podName string) error
	DeleteByInstanceID(ctx context.Context, instanceId string) error
	List(ctx context.Context, instanceId string, tenantId string) ([]*dao.AuditTlsJobPod, error)
	ListCluster(ctx context.Context, dsType string) ([]string, error)
	ListClusterByInstanceId(ctx context.Context, instanceId string) (clustersName []string, err error)
	//ListJobByInstanceId(ctx context.Context, instanceId string) ([]*dao.AuditTlsJobPod, error)
	UpdatePod(ctx context.Context, podName string, status int32) error
}

func NewAuditTlsJobPodDAL(provider DBProvider) AuditTlsJobPodDAL {
	return &AuditTlsJobPod{dbProvider: provider}
}

type AuditTlsJobPod struct {
	dbProvider DBProvider
}

func (a *AuditTlsJobPod) CreateBatch(ctx context.Context, cs []*dao.AuditTlsJobPod) error {
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Create(cs).Error
	return err
}

func (a *AuditTlsJobPod) Create(ctx context.Context, at *dao.AuditTlsJobPod) error {
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Create(at).Error
	return err
}

func (a *AuditTlsJobPod) Get(ctx context.Context, instanceId string, tenantId string, jobName string) (*dao.AuditTlsJobPod, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at dao.AuditTlsJobPod
	err := db.Where(JOBNAME_WHERE_CONDITION, instanceId, tenantId, jobName).First(&at).Error
	return &at, err
}

func (a *AuditTlsJobPod) GetByPod(ctx context.Context, podName string) (*dao.AuditTlsJobPod, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at dao.AuditTlsJobPod
	err := db.Where(PODNAME_ONLY_WHERE_CONDITION, podName).First(&at).Error
	return &at, err
}

func (a *AuditTlsJobPod) GetPod(ctx context.Context, podName string, status int64, deleted int64) ([]*dao.AuditTlsJobPod, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at []*dao.AuditTlsJobPod
	err := db.Where(DELETED_POD_ONLY_WHERE_CONDITION, podName, status, deleted).Find(&at).Error
	return at, err
}

func (a *AuditTlsJobPod) List(ctx context.Context, instanceId string, tenantId string) ([]*dao.AuditTlsJobPod, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at []*dao.AuditTlsJobPod
	err := db.Where(JOB_WHERE_CONDITION, instanceId, tenantId).Find(&at).Error
	return at, err
}

func (a *AuditTlsJobPod) ListCluster(ctx context.Context, dsType string) ([]string, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at []*dao.AuditTlsJobPod
	if err := db.Distinct("cluster_name").Where("db_type=?", dsType).Find(&at).Error; err != nil {
		return nil, err
	}
	var clusters []string
	for _, cluster := range at {
		clusters = append(clusters, cluster.ClusterName)
	}
	return clusters, nil
}

func (a *AuditTlsJobPod) ListClusterByInstanceId(ctx context.Context, instanceId string) (clustersName []string, err error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var auditPods []*dao.AuditTlsJobPod
	if err = db.Distinct("cluster_name").Where(INSTANCE_WHERE_CONDITION, instanceId).Find(&auditPods).Error; err != nil {
		return
	}
	err = fp.Stream0Of(auditPods).Map(func(pod *dao.AuditTlsJobPod) string { return pod.ClusterName }).ToSlice(clustersName)
	return
}

func (a *AuditTlsJobPod) ListJobByInstanceId(ctx context.Context, instanceId string) ([]*dao.AuditTlsJobPod, error) {
	db := a.dbProvider.GetMetaDB(ctx)
	var at []*dao.AuditTlsJobPod
	err := db.Where("instance_id=? and deleted=0", instanceId).Find(&at).Error
	return at, err
}

func (a *AuditTlsJobPod) DeleteByInstanceID(ctx context.Context, instanceId string) error {
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.AuditTlsJobPod{}).Where(INSTANCE_WHERE_CONDITION, instanceId).Updates(map[string]interface{}{"deleted": 1}).Error
	return err
}

func (a *AuditTlsJobPod) DeleteJob(ctx context.Context, instanceId string, jobName string) error {
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.AuditTlsJobPod{}).
		Where(JOBNAME_WHERE_CONDITION, instanceId, jobName).Updates(map[string]interface{}{"deleted": 1}).Error
	return err
}

func (a *AuditTlsJobPod) DeletePod(ctx context.Context, instanceId string, podName string) error {
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.AuditTlsJobPod{}).
		Where(PODNAME_WHERE_CONDITION, instanceId, podName).Updates(map[string]interface{}{"deleted": 1}).Error
	return err
}

func (a *AuditTlsJobPod) UpdatePod(ctx context.Context, podName string, podStatus int32) error {
	db := a.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.AuditTlsJobPod{}).
		Where(PODNAME_NOT_DELETED, podName).
		Updates(map[string]interface{}{"pod_status": podStatus}).
		Error
	return err
}
