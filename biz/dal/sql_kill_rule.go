package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"strings"
	"time"
)

type SqlKillQueryFilter struct {
	RuleState string
	SqlType   string
	NodeType  string
	User      string
	TaskIds   []string
	Finger    string
	Host      string
	Keywords  string
}
type SqlKillRulesDAL interface {
	Create(ctx context.Context, Rule *dao.SqlKillRule) error
	List(ctx context.Context, instanceId string, queryFilter *SqlKillQueryFilter, sortBy string, orderBy string, limit int32, offset int32, isAll bool) (*dao.SqlKillRulesInfo, error)
	//Update(ctx context.Context, ID int64, Rule *dao.SqlKillRule) error
	UpdateStatusByID(ctx context.Context, ID int64, status string) error
	Delete(ctx context.Context, ID int64) error
	Get(ctx context.Context, ID int64) (*dao.SqlKillRule, error)
}

type SqlKillEventDAL interface {
	Create(ctx context.Context, Event *dao.SqlKillEvent) error
}

func NewSqlKillEventDAL(dbPro DBProvider) SqlKillEventDAL {
	return &SqlKillEventDal{dbPro: dbPro}
}

func NewSqlKillRulesDAL(dbPro DBProvider) SqlKillRulesDAL {
	return &SqlKillRulesDal{dbPro: dbPro}
}

type SqlKillRulesDal struct {
	dbPro DBProvider
}

type SqlKillEventDal struct {
	dbPro DBProvider
}

func (dal *SqlKillRulesDal) List(ctx context.Context, instanceId string, queryFilter *SqlKillQueryFilter, sortBy string, orderBy string, limit int32, offset int32, isAll bool) (*dao.SqlKillRulesInfo, error) {
	var (
		SqlKillRules   []*dao.SqlKillRule
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "instance_id=? and deleted=0 and state not in ('None') "
	baseConditions = append(baseConditions, instanceId)
	if queryFilter != nil {
		if queryFilter.SqlType != "" {
			convertedSql := make([]string, 0)
			SqlType := strings.Split(queryFilter.SqlType, ",")
			for _, st := range SqlType {
				convertedSql = append(convertedSql, "sql_type LIKE ? ")
				baseConditions = append(baseConditions, "%"+st+"%")
			}
			queryStr := strings.Join(convertedSql, "or ")
			baseQuery += "and (" + queryStr + ") "
		}
		if queryFilter.NodeType != "" {
			convertedSql := make([]string, 0)
			nodeType := strings.Split(queryFilter.NodeType, ",")
			for _, st := range nodeType {
				convertedSql = append(convertedSql, "node_type LIKE ? ")
				baseConditions = append(baseConditions, "%"+st+"%")
			}
			queryStr := strings.Join(convertedSql, "or ")
			baseQuery += "and (" + queryStr + ") "
		}
		if queryFilter.RuleState != "" {
			baseQuery += "and state IN (?) "
			stateSet := strings.Split(queryFilter.RuleState, ",")
			baseConditions = append(baseConditions, stateSet)
		}
		if queryFilter.User != "" {
			baseQuery += "and user_id like ? "
			baseConditions = append(baseConditions, "%"+queryFilter.User+"%")
		}
		if queryFilter.Keywords != "" {
			baseQuery += "and keywords like ? "
			baseConditions = append(baseConditions, "%"+queryFilter.Keywords+"%")
		}
		if queryFilter.Host != "" {
			baseQuery += "and host like ? "
			baseConditions = append(baseConditions, "%"+queryFilter.Host+"%")
		}
		if queryFilter.Finger != "" {
			baseQuery += "and fingerprint like ? "
			baseConditions = append(baseConditions, "%"+queryFilter.Finger+"%")
		}
		if len(queryFilter.TaskIds) > 0 {
			baseQuery += "and id IN (?) "
			baseConditions = append(baseConditions, queryFilter.TaskIds)
		}
	}
	if err := db.Model(SqlKillRules).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if !isAll {
		if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Order(orderBy + " " + sortBy).Find(&SqlKillRules).Error; err != nil {
			return nil, err
		}
	} else {
		if err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Find(&SqlKillRules).Error; err != nil {
			return nil, err
		}
	}

	ret := &dao.SqlKillRulesInfo{
		Total:        int32(total),
		SqlKillRules: SqlKillRules,
	}
	return ret, nil
}

//func (dal *SqlKillRulesDal) Update(ctx context.Context, ID int64, Rule *dao.SqlKillRule) error {
//	db := dal.dbPro.GetMetaDB(ctx)
//	res := db.Model(&dao.SqlKillRule{}).Where("id=?", ID).Updates(map[string]interface{}{
//		//"RuleID":    SqlKillRule,
//		"UpdatedAt": time.Now().UnixMilli(),
//	})
//	if err := res.Error; err != nil {
//		return fmt.Errorf("save Rule %d sql_kill rule info error %v", ID, err)
//	}
//	if res.RowsAffected == 0 {
//		return fmt.Errorf("save Rule %d sql_kill rule info error, Rule not exist", ID)
//	}
//	return nil
//}

func (dal *SqlKillRulesDal) Delete(ctx context.Context, ID int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SqlKillRule{}).Where("id=?", ID).Updates(map[string]interface{}{
		"Deleted":   1,
		"DeletedAt": time.Now().UnixMilli(),
		"UpdatedAt": time.Now().UnixMilli(),
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("delete SqlKill rules error %v", err)
	}
	return nil
}

func (dal *SqlKillRulesDal) Create(ctx context.Context, Rule *dao.SqlKillRule) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(Rule).Error
	return err
}

func (dal *SqlKillRulesDal) Get(ctx context.Context, ID int64) (*dao.SqlKillRule, error) {
	var ret dao.SqlKillRule
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("id=? and deleted=0", ID).First(&ret).Error
	return &ret, err
}

func (dal *SqlKillRulesDal) UpdateStatusByID(ctx context.Context, ID int64, status string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.SqlKillRule{}).Where("id=?", ID).Updates(map[string]interface{}{
		"State":     status,
		"UpdatedAt": time.Now().UnixMilli(),
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("save Rule %d status %s info error %v", ID, status, err)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("save Rule %d status %s info error, Rule not exist", ID, status)
	}
	return nil
}

func (dal *SqlKillEventDal) Create(ctx context.Context, Event *dao.SqlKillEvent) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(Event).Error
	return err
}
