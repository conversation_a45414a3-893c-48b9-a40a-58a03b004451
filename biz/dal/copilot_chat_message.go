package dal

import (
	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	. "gorm.io/gorm"
	"time"
)

type CopilotChatMessageDAL interface {
	Create(ctx context.Context, CopilotChatMessage *dao.CopilotChatMessage) error
	QueryMessageByMessageID(ctx context.Context, TenantId string, messageId string) (*dao.CopilotChatMessage, error)
	QueryMessageListByChatID(ctx context.Context, TenantId string, chatID string) ([]*dao.CopilotChatMessage, error)
	QueryMessageListByAfterMessageID(ctx context.Context, TenantId string, messageID string, chatID string) ([]*dao.CopilotChatMessage, error)
	DeleteMessageByChatID(ctx context.Context, TenantId string, chatID string) error
	QueryMessageByCondition(ctx context.Context, TenantId string, chatID string, condition CopilotChatMessageParam) ([]*dao.CopilotChatMessage, error)
	UpdateRateType(ctx context.Context, messageId string, ratedType model.RateModelReplyEnum) error
	UpdateTaskID(ctx context.Context, messageId string, taskID string) error
}

func NewCopilotChatMessageDAL(dbPro DBProvider) CopilotChatMessageDAL {
	return &CopilotChatMessageDal{dbPro: dbPro}
}

type CopilotChatMessageDal struct {
	dbPro DBProvider
}

type CopilotChatMessageParam struct {
	ID         string
	Name       string
	Role       string
	CreateTime *int64
	Limit      int
}

func (dal *CopilotChatMessageDal) Create(ctx context.Context, CopilotChatMessage *dao.CopilotChatMessage) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(CopilotChatMessage).Error
	if err != nil {
		log.Warn(ctx, "CopilotChatMessageDal Create err:%v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return err
}

func (dal *CopilotChatMessageDal) QueryMessageListByChatID(ctx context.Context, TenantId string, chatID string) ([]*dao.CopilotChatMessage, error) {
	db := dal.dbPro.GetMetaDB(ctx)

	var (
		CopilotChatMessageList []*dao.CopilotChatMessage
		BaseCondition          []interface{}
	)

	baseQuery := "chat_id = ? "
	BaseCondition = append(BaseCondition, chatID)
	if !strings.IsEmpty(TenantId) {
		baseQuery += "and tenant_id = ? "
		BaseCondition = append(BaseCondition, TenantId)
	}

	if err := db.Where(baseQuery, BaseCondition...).Find(&CopilotChatMessageList).Error; err != nil {
		return nil, err
	}

	return CopilotChatMessageList, nil
}

func (dal *CopilotChatMessageDal) QueryMessageByMessageID(ctx context.Context, TenantId string, messageId string) (*dao.CopilotChatMessage, error) {
	db := dal.dbPro.GetMetaDB(ctx)

	var (
		CopilotChatMessageList []*dao.CopilotChatMessage
		BaseCondition          []interface{}
	)

	baseQuery := " id = ? "
	BaseCondition = append(BaseCondition, messageId)
	if !strings.IsEmpty(TenantId) {
		baseQuery += "and tenant_id = ? "
		BaseCondition = append(BaseCondition, TenantId)
	}

	log.Debug(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, BaseCondition...).Find(&CopilotChatMessageList)
	}))

	if err := db.Where(baseQuery, BaseCondition...).Find(&CopilotChatMessageList).Error; err != nil {
		return nil, err
	}

	if len(CopilotChatMessageList) == 0 {
		return nil, nil
	}

	return CopilotChatMessageList[0], nil
}

// QueryMessageListByAfterMessageID 根据messageID查询当前messageID之后的所有消息
func (dal *CopilotChatMessageDal) QueryMessageListByAfterMessageID(ctx context.Context, TenantId string, messageID string, chatID string) ([]*dao.CopilotChatMessage, error) {
	db := dal.dbPro.GetMetaDB(ctx)

	var (
		CopilotChatMessageList []*dao.CopilotChatMessage
		BaseCondition          []interface{}
	)

	baseQuery := "chat_id = ? "
	BaseCondition = append(BaseCondition, chatID)
	baseQuery += "and id >= ? "
	BaseCondition = append(BaseCondition, messageID)
	if !strings.IsEmpty(TenantId) {
		baseQuery += "and tenant_id = ? "
		BaseCondition = append(BaseCondition, TenantId)
	}

	if err := db.Where(baseQuery, BaseCondition...).Find(&CopilotChatMessageList).Error; err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_MetaDataBaseError, err.Error())
	}

	return CopilotChatMessageList, nil
}

func (dal *CopilotChatMessageDal) DeleteMessageByChatID(ctx context.Context, TenantId string, chatID string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.CopilotChatMessage{}).Where("id=? and tenant_id=?", chatID, TenantId).Updates(map[string]interface{}{
		"IsDelete": 1,
	})
	if err := res.Error; err != nil {
		log.Warn(ctx, "CopilotChatDal UpdateChatName err:%v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return nil
}

func (dal *CopilotChatMessageDal) UpdateRateType(ctx context.Context, messageId string, ratedType model.RateModelReplyEnum) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.CopilotChatMessage{}).Where("id=?", messageId).Updates(map[string]interface{}{
		"RatedType":  ratedType.String(),
		"RatedTime":  time.Now().Unix(),
		"UpdateTime": time.Now().Unix(),
	})
	if err := res.Error; err != nil {
		log.Warn(ctx, "CopilotChatDal UpdateChatName err:%v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return nil
}

func (dal *CopilotChatMessageDal) QueryMessageByCondition(ctx context.Context, TenantId string, chatID string, condition CopilotChatMessageParam) ([]*dao.CopilotChatMessage, error) {
	db := dal.dbPro.GetMetaDB(ctx)
	var (
		CopilotChatMessageList []*dao.CopilotChatMessage
		BaseCondition          []interface{}
	)

	baseQuery := "chat_id = ? "
	BaseCondition = append(BaseCondition, chatID)

	if !strings.IsEmpty(TenantId) {
		baseQuery += "and tenant_id = ? "
		BaseCondition = append(BaseCondition, TenantId)
	}
	if !strings.IsEmpty(condition.Role) {
		baseQuery += "and role = ? "
		BaseCondition = append(BaseCondition, condition.Role)
	}
	if condition.CreateTime != nil {
		baseQuery += "and create_time >= ? "
		BaseCondition = append(BaseCondition, *condition.CreateTime)
	}

	if condition.Limit > 0 {
		log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
			return db.Where(baseQuery, BaseCondition...).Order("create_time DESC").Limit(condition.Limit).Find(&CopilotChatMessageList)
		}))
		if err := db.Where(baseQuery, BaseCondition...).Order("create_time DESC").Limit(condition.Limit).Find(&CopilotChatMessageList).Error; err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_MetaDataBaseError, err.Error())
		}
	} else {
		log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
			return db.Where(baseQuery, BaseCondition...).Find(&CopilotChatMessageList)
		}))
		if err := db.Where(baseQuery, BaseCondition...).Find(&CopilotChatMessageList).Error; err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_MetaDataBaseError, err.Error())
		}
	}

	return CopilotChatMessageList, nil
}

func (dal *CopilotChatMessageDal) UpdateTaskID(ctx context.Context, messageId string, taskID string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.CopilotChatMessage{}).Where("id=?", messageId).Updates(map[string]interface{}{
		"TaskID":     taskID,
		"UpdateTime": time.Now().Unix(),
	})
	if err := res.Error; err != nil {
		log.Warn(ctx, "CopilotChatDal UpdateChatName err:%v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return nil
}
