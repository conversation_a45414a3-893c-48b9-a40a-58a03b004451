package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockSecGroupDAL() SecGroupDAL {
	return NewSecGroupDAL(&mockDBProvider{})
}
func mockSecRuleDAL() SecRuleDAL {
	return NewSecRuleDAL(&mockDBProvider{})
}
func mockSecFactorDAL() SecFactorDAL {
	return NewSecFactorDAL(&mockDBProvider{})
}
func mockSecActionDAL() SecActionDAL {
	return NewSecActionDAL(&mockDBProvider{})
}

func TestSecGroupGet(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecGroupDAL()
	_, err := dal.Get(context.Background(), "2", "1")
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.Get(context.Background(), "2", "1")
	assert.Nil(t, err)
}

func TestSecGroupCreate(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecGroupDAL()
	secGroup := &dao.SecurityGroup{
		ID:           0,
		TenantID:     "1",
		InstanceType: "MySQL",
		Name:         "CREATE",
		Type:         "",
		Comment:      "",
		CreatedAt:    0,
		UpdatedAt:    0,
		Deleted:      0,
	}
	err := dal.Create(context.Background(), secGroup)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Create(context.Background(), secGroup)
	assert.Nil(t, err)
}

func TestSecGroupDelete(t *testing.T) {
	mock0 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecGroupDAL()
	err := dal.Delete(context.Background(), "2", "1")
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Delete(context.Background(), "2", "1")
	assert.Nil(t, err)
}

func TestSecGroupUpdate(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Save).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecGroupDAL()
	err := dal.Update(context.Background(), "2", "1", &dao.SecurityGroup{})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Update(context.Background(), "2", "1", &dao.SecurityGroup{})
	assert.Nil(t, err)
}

func TestSecGroupGetDefaultGroup(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecGroupDAL()
	_, err := dal.GetDefaultGroup(context.Background(), "2", "1", "3")
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.GetDefaultGroup(context.Background(), "2", "1", "3")
	assert.Nil(t, err)
}

func TestSecGroupList(t *testing.T) {

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mockRes := &gorm.DB{}
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes.Error = nil
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mockRes2 := &gorm.DB{}
	mock51 := mockey.Mock((*gorm.DB).Find).Return(mockRes2).Build()
	defer mock51.UnPatch()
	mockRes2.Error = fmt.Errorf("test_find")
	dal := mockSecGroupDAL()
	_, err := dal.List(context.Background(), "2", &SecGroupParam{})
	assert.NotNil(t, err)
	mockRes2.Error = nil
	_, err = dal.List(context.Background(), "2", &SecGroupParam{})
	assert.Nil(t, err)
}

func TestSecRuleGet(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecRuleDAL()
	_, err := dal.Get(context.Background(), "2", "1")
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.Get(context.Background(), "2", "1")
	assert.Nil(t, err)
}
func TestSecRuleCreate(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecRuleDAL()
	secRule := &dao.SecurityRule{
		ID:        0,
		TenantID:  "1",
		Type:      "",
		Comment:   "",
		CreatedAt: 0,
		UpdatedAt: 0,
		Deleted:   0,
	}
	err := dal.Create(context.Background(), secRule)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Create(context.Background(), secRule)
	assert.Nil(t, err)
}

func TestSecRuleBatchCreate(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).CreateInBatches).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecRuleDAL()
	secRules := []*dao.SecurityRule{
		{
			ID:        0,
			TenantID:  "1",
			Type:      "",
			Comment:   "",
			CreatedAt: 0,
			UpdatedAt: 0,
			Deleted:   0,
		},
	}
	err := dal.BatchCreate(context.Background(), secRules)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.BatchCreate(context.Background(), secRules)
	assert.Nil(t, err)
}

func TestSecRuleDelete(t *testing.T) {
	mock0 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecRuleDAL()
	err := dal.Delete(context.Background(), "2", "1")
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Delete(context.Background(), "2", "1")
	assert.Nil(t, err)
}

func TestSecRuleUpdate(t *testing.T) {
	mock0 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecRuleDAL()
	err := dal.Update(context.Background(), "2", "1", &dao.SecurityRule{State: 1})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Update(context.Background(), "2", "1", &dao.SecurityRule{State: 1})
	assert.Nil(t, err)
}

func TestSecRuleUpdateStatus(t *testing.T) {
	mock0 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockSecRuleDAL()
	err := dal.Update(context.Background(), "2", "1", &dao.SecurityRule{State: 1})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Update(context.Background(), "2", "1", &dao.SecurityRule{State: 1})
	assert.Nil(t, err)
}

func TestSecRuleList(t *testing.T) {

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mockRes := &gorm.DB{}
	mockRes.Error = nil
	mock3 := mockey.Mock((*gorm.DB).Count).Return(mockRes).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mockRes2 := &gorm.DB{}
	mock51 := mockey.Mock((*gorm.DB).Find).Return(mockRes2).Build()
	defer mock51.UnPatch()
	mock10 := mockey.Mock((*gorm.DB).ToSQL).Return("").Build()
	defer mock10.UnPatch()

	mockRes2.Error = fmt.Errorf("test_find")
	dal := mockSecRuleDAL()
	_, err := dal.List(context.Background(), "2", &SecRuleParam{})
	assert.NotNil(t, err)
	mockRes2.Error = nil
	_, err = dal.List(context.Background(), "2", &SecRuleParam{})
	assert.Nil(t, err)
}

func TestFactorsList(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	dal := mockSecFactorDAL()
	_, err := dal.List(context.Background())
	assert.Nil(t, err)
}

func TestFactorsGet(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).First).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	dal := mockSecFactorDAL()
	_, err := dal.Get(context.Background(), "1", "2", "3")
	assert.Nil(t, err)
}

func TestActionsList(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	dal := mockSecActionDAL()
	_, err := dal.List(context.Background())
	assert.Nil(t, err)
}

func TestActionsGet(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).First).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	dal := mockSecActionDAL()
	_, err := dal.Get(context.Background(), "1", "2", "3")
	assert.Nil(t, err)
}
