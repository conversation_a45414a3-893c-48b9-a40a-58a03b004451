package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-lib/framework/db"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"gorm.io/gorm"
	"testing"
)

func initWorkFlowDal() *Workflow {
	return &Workflow{
		dbProvider: &mockDBProvider{},
	}
}

func TestIsInstanceAvailable(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()

	_, err := dal.IsInstanceAvailable(context.Background(), "1", "instanceId")
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestIsUpperAccount(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	_, err := dal.IsUpperAccount(context.Background(), "1", "1", "instanceId")
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestReplaceIntoPreCheckResult(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Exec).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()

	err := dal.ReplaceIntoPreCheckResult(context.Background(), 1, []*model.CheckItem{{Item: "a"}})
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestUpdateWorkStatusAndOperator(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Exec).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()

	err := dal.UpdateWorkStatusAndOperator(context.Background(), 1, 1, &dao.UserRole{})
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestIsUserInTenant(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	_, err := dal.IsUserInTenant(context.Background(), "1", 1)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestGetInstanceDbaIds(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	_, err := dal.GetInstanceDbaIds(context.Background(), "1", "1")

	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestGetInstanceOwnerIds(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	_, err := dal.GetInstanceOwnerIds(context.Background(), "1", "1")

	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestDescribeTicketsForOperateRecord(t *testing.T) {
	dal := initWorkFlowDal()
	mock04 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	defer mock04.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	number := int32(1)
	size := int32(10)
	UptdateStartTime := "1243532"
	UptdateEndTime := "132456432"
	InstanceID := "12323sdfsdf"
	CreateUserId := "123sdfsdfsd"
	CreateUserName := "asfsfsdf"
	DbName := "fisher"
	TicketID := "12342345234"
	TicketStatus := model.TicketStatus_TicketCancel
	TicketType := model.TicketType_NormalSqlChange
	TicketRecordSearchParam := model.TicketRecordSearchParam{
		UptdateStartTime: &UptdateStartTime,
		UptdateEndTime:   &UptdateEndTime,
		InstanceID:       &InstanceID,
		CreateUserId:     &CreateUserId,
		CreateUserName:   &CreateUserName,
		DbName:           &DbName,
		TicketID:         &TicketID,
		TicketStatus:     &TicketStatus,
		TicketType:       &TicketType,
	}

	SortBy := model.SortBy_ASC
	OrderBy := model.OrderByForTicket_CreateTime
	req := model.DescribeTicketRecordListReq{
		PageNumber:              &number,
		PageSize:                &size,
		TicketRecordSearchParam: &TicketRecordSearchParam,
		SortBy:                  &SortBy,
		OrderBy:                 &OrderBy,
	}

	_, _, err := dal.DescribeTicketsForOperateRecord(context.Background(), &req)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestDescribeByTicketID(t *testing.T) {
	dal := initWorkFlowDal()
	mock0 := mockey.Mock((*mockDBProvider).GetMetaDB).Return(&db.DB{DB: &gorm.DB{}}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()
	mock04 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	defer mock04.UnPatch()
	_, err := dal.DescribeByTicketID(context.Background(), 1)
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestGetTicketByInstanceID(t *testing.T) {
	dal := initWorkFlowDal()
	mock0 := mockey.Mock((*mockDBProvider).GetMetaDB).Return(&db.DB{DB: &gorm.DB{}}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()
	mock04 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	defer mock04.UnPatch()
	dal.GetTicketByInstanceID(context.Background(), "xx")
}

func TestDescribeTickets(t *testing.T) {
	dal := initWorkFlowDal()
	mock04 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	defer mock04.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	number := int32(1)
	size := int32(10)
	TicketID := "12342345234"
	TicketRecordSearchParam := &model.TicketSearchParam{
		TicketId: utils.StringRef(TicketID),
		Memo:     utils.StringRef("111"),
		Title:    utils.StringRef("xxx"),
	}

	SortBy := model.SortBy_ASC
	OrderBy := model.OrderByForTicket_CreateTime
	req := model.DescribeTicketsReq{
		ListType:    model.TicketListTypePtr(model.TicketListType_CreatedByMe),
		PageNumber:  &number,
		PageSize:    &size,
		SortBy:      &SortBy,
		OrderBy:     &OrderBy,
		SearchParam: TicketRecordSearchParam,
	}

	dal.DescribeTickets(context.Background(), &req, model.DbwRoleType_ADMIN.String())

	req.ListType = model.TicketListTypePtr(model.TicketListType_All)
	dal.DescribeTickets(context.Background(), &req, model.DbwRoleType_ADMIN.String())

	req.ListType = model.TicketListTypePtr(model.TicketListType_ApprovedByMe)
	dal.DescribeTickets(context.Background(), &req, model.DbwRoleType_ADMIN.String())

}
