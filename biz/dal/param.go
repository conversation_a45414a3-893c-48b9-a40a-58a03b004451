package dal

import (
	"context"
	"fmt"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/framework/db"

	"gorm.io/gorm"
)

type ParamDAL interface {
	Set(ctx context.Context, db *db.DB, p *dao.Param) error
	Get(ctx context.Context, db *db.DB, key string) (*dao.Param, error)
	List(ctx context.Context, db *db.DB) ([]*dao.Param, error)
}

func NewParamDAL() ParamDAL {
	return &paramDal{&dao.Param{}}
}

type paramDal struct {
	ins *dao.Param
}

func (self *paramDal) Set(ctx context.Context, db *db.DB, p *dao.Param) error {
	if param, err := self.Get(ctx, db, p.Key); err == nil && param != nil {
		if param.Value != p.Value || param.Desc != p.Desc {
			sql := fmt.Sprintf("UPDATE %s SET `value`=?,`desc`=? WHERE `key`=?", p.TableName())
			return db.Exec(sql, p.Value, p.Desc, p.Key).Error // ignore_security_alert
		}
		return nil
	}
	sql := fmt.Sprintf("INSERT INTO %s (`key`,`value`,`desc`) VALUES (?,?,?)", p.TableName())
	return db.Exec(sql, p.Key, p.Value, p.Desc).Error // ignore_security_alert
}

func (self *paramDal) Get(ctx context.Context, db *db.DB, key string) (*dao.Param, error) {
	param := &dao.Param{}
	if err := db.Where("`key`=?", key).First(param).Error; err == gorm.ErrRecordNotFound {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return param, nil
}

func (*paramDal) List(ctx context.Context, db *db.DB) ([]*dao.Param, error) {
	var list []*dao.Param
	return list, db.Model(new(dao.Param)).Find(&list).Error
}
