package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockKillRuleDAL() SqlKillRulesDAL {
	return NewSqlKillRulesDAL(&mockDBProvider{})
}
func mockKillEventDAL() SqlKillEventDAL {
	return NewSqlKillEventDAL(&mockDBProvider{})
}

func TestKillRuleCreate(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockKillRuleDAL()
	killRule := &dao.SqlKillRule{
		ID:             0,
		TenantID:       "1",
		InstanceID:     "mysql-111",
		InstanceType:   "MySQL",
		ProtectedUsers: "byte_rds",
		Duration:       10,
		MaxExecTime:    10,
		State:          "None",
		CreatedAt:      0,
		UpdatedAt:      0,
		Deleted:        0,
	}
	err := dal.Create(context.Background(), killRule)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Create(context.Background(), killRule)
	assert.Nil(t, err)
}

func TestKillEventCreate(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockKillEventDAL()
	killEvent := &dao.SqlKillEvent{
		ID:            0,
		SqlKillRuleID: 2454,
		EventType:     "Add",
		Operator:      "test1",
		TenantID:      "1",
		InstanceID:    "mysql-111",
		InstanceType:  "MySQL",
		CreatedAt:     0,
	}
	err := dal.Create(context.Background(), killEvent)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Create(context.Background(), killEvent)
	assert.Nil(t, err)
}

func TestKillRuleDelete(t *testing.T) {
	mock0 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockKillRuleDAL()
	err := dal.Delete(context.Background(), 2)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Delete(context.Background(), 2)
	assert.Nil(t, err)
}

func TestKillRuleList(t *testing.T) {

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mockRes := &gorm.DB{}
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mockRes.Error = nil
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mockRes2 := &gorm.DB{}
	mock51 := mockey.Mock((*gorm.DB).Find).Return(mockRes2).Build()
	defer mock51.UnPatch()
	mockRes2.Error = fmt.Errorf("test_find")
	dal := mockKillRuleDAL()
	_, err := dal.List(context.Background(), "mysql-xxx", &SqlKillQueryFilter{SqlType: "select", RuleState: "DONE", User: "dbw"}, "ASC", "CreatedAt", 10, 1, false)
	assert.NotNil(t, err)
	mockRes2.Error = nil
	_, err = dal.List(context.Background(), "mysql-xxx", &SqlKillQueryFilter{}, "ASC", "CreatedAt", 10, 1, true)
	assert.Nil(t, err)
}

func TestKillRuleGet(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockKillRuleDAL()
	_, err := dal.Get(context.Background(), 2)
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.Get(context.Background(), 2)
	assert.Nil(t, err)
}

func TestKillRuleUpdateStatusByID(t *testing.T) {
	mock0 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockKillRuleDAL()
	err := dal.UpdateStatusByID(context.Background(), 2, "DONE")
	assert.NotNil(t, err)
	mockRes.Error = nil
	mockRes.RowsAffected = 1
	err = dal.UpdateStatusByID(context.Background(), 2, "ACTIVE")
	assert.Nil(t, err)
}
