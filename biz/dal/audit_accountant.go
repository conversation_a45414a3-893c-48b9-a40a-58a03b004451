package dal

import (
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
)

type AuditAccountantDAL interface {
	Create(ctx context.Context, acc *dao.AuditAccountant) (int64, error)
	Update(ctx context.Context, acc *dao.AuditAccountant) error
	UpdateStatusBatch(ctx context.Context, accountantTime int64, status int64, newStatus int64, chargeItemCode string) error
	GetInstanceTopics(ctx context.Context, tenantID string, status int64, accountantTime int64, chargeItemCode string) ([]InstanceTopic, error)
	GetByAccountantTime(ctx context.Context, accountantTime int64, chargeItemCode string) ([]*dao.AuditAccountant, error)
	GetByAccountantTimeStatus(ctx context.Context, accountantTime int64, status int64, chargeItemCode string) ([]*dao.AuditAccountant, error)
	Get(ctx context.Context, accountantTime int64, instanceID string, chargeItemCode string) (*dao.AuditAccountant, error)
}

type auditAccountant struct {
	provider      DBProvider
	accountantDAL AccountantDAL
}

func NewAuditAccountantDAL(provider DBProvider, accountantDAL AccountantDAL) AuditAccountantDAL {
	return &auditAccountant{
		provider:      provider,
		accountantDAL: accountantDAL,
	}
}

func (a auditAccountant) Create(ctx context.Context, acc *dao.AuditAccountant) (int64, error) {
	return a.accountantDAL.Create(ctx, acc)
}

func (a auditAccountant) Update(ctx context.Context, acc *dao.AuditAccountant) error {
	acc.ProductType = model.LogProductType_AuditLog.String()
	return a.accountantDAL.Update(ctx, acc)
}

func (a auditAccountant) UpdateStatusBatch(ctx context.Context, accountantTime int64, status int64, newStatus int64, chargeItemCode string) error {
	return a.accountantDAL.UpdateStatusBatch(ctx, accountantTime, status, newStatus, model.LogProductType_AuditLog.String(), chargeItemCode)
}

type InstanceTopic struct {
	InstanceID       string `gorm:"column:instance_id"`
	TlsTopicID       string `gorm:"column:tls_topic_id"`
	TlsProjectId     string `gorm:"column:tls_project_id"`
	InstanceTenantId string `gorm:"column:tenant_id"`
	TlsTenantID      string `gorm:"column:tls_tenant_id"`
}

func (a auditAccountant) Get(ctx context.Context, accountantTime int64, instanceID string, chargeItemCode string) (*dao.AuditAccountant, error) {
	return a.accountantDAL.Get(ctx, accountantTime, instanceID, model.LogProductType_AuditLog.String(), chargeItemCode)
}

func (a auditAccountant) GetInstanceTopics(ctx context.Context, tenantID string, status int64, accountantTime int64, chargeItemCode string) ([]InstanceTopic, error) {
	return a.accountantDAL.GetInstanceTopics(ctx, tenantID, status, accountantTime,
		model.LogProductType_AuditLog.String(), chargeItemCode)
}

func (a auditAccountant) GetByAccountantTime(ctx context.Context, accountantTime int64, chargeItemCode string) ([]*dao.AuditAccountant, error) {
	return a.accountantDAL.GetByAccountantTime(ctx, accountantTime, model.LogProductType_AuditLog.String(), chargeItemCode)
}
func (a auditAccountant) GetByAccountantTimeStatus(ctx context.Context, accountantTime int64, status int64, chargeItemCode string) ([]*dao.AuditAccountant, error) {
	return a.accountantDAL.GetByAccountantTimeStatus(ctx, accountantTime, status, model.LogProductType_AuditLog.String(), chargeItemCode)
}
