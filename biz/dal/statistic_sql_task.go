package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
)

type StatisticSqlTaskDal interface {
	Create(ctx context.Context, task dao.StatisticSqlTask) error
	Update(ctx context.Context, task dao.StatisticSqlTask) error
	Get(ctx context.Context, instanceID string, tenantId string) ([]*dao.StatisticSqlTask, error)
	GetByStatisticId(ctx context.Context, statisticId string, tenantId string) ([]*dao.StatisticSqlTask, error)
	GetByInstanceId(ctx context.Context, instanceId string, statisticId string, tenantId string) ([]*dao.StatisticSqlTask, error)
	Delete(ctx context.Context, id string, tenantId string) error
}

func NewStatisticSqlTaskDal(dbProvider DBProvider) StatisticSqlTaskDal {
	return statisticSqlTaskDal{
		DbProvider: dbProvider,
	}
}

type statisticSqlTaskDal struct {
	DbProvider DBProvider
}

func (s statisticSqlTaskDal) Create(ctx context.Context, task dao.StatisticSqlTask) error {
	db := s.DbProvider.GetMetaDB(ctx)
	return db.Model(&dao.StatisticSqlTask{}).Create(&task).Error
}

func (s statisticSqlTaskDal) Update(ctx context.Context, task dao.StatisticSqlTask) error {
	db := s.DbProvider.GetMetaDB(ctx)
	return db.Model(&dao.StatisticSqlTask{}).
		Where(" id = ? ", task.Id).
		Updates(&task).Error
}

func (s statisticSqlTaskDal) Get(ctx context.Context, instanceId string, tenantId string) ([]*dao.StatisticSqlTask, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	var sqlTask []*dao.StatisticSqlTask
	err := db.Model(&dao.StatisticSqlTask{}).
		Where(" instance_id = ? ", instanceId).
		Where(" tenant_id = ? ", tenantId).
		Where(" deleted = 0 ").
		Find(&sqlTask).Error
	return sqlTask, err
}

func (s statisticSqlTaskDal) GetByStatisticId(ctx context.Context, statisticId string, tenantId string) ([]*dao.StatisticSqlTask, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	var sqlTask []*dao.StatisticSqlTask
	err := db.Model(&dao.StatisticSqlTask{}).
		Where(" statistic_id = ? ", statisticId).
		Where(" tenant_id = ? ", tenantId).
		Where(" deleted = 0 ").
		Find(&sqlTask).Error
	return sqlTask, err
}

func (s statisticSqlTaskDal) GetByInstanceId(ctx context.Context, instanceId string, statisticId string, tenantId string) ([]*dao.StatisticSqlTask, error) {
	db := s.DbProvider.GetMetaDB(ctx)
	var sqlTask []*dao.StatisticSqlTask
	err := db.Model(&dao.StatisticSqlTask{}).
		Where(" instance_id = ? ", instanceId).
		Where(" statistic_id = ? ", statisticId).
		Where(" tenant_id = ? ", tenantId).
		Where(" deleted = 0 ").
		Find(&sqlTask).Error
	return sqlTask, err
}

func (s statisticSqlTaskDal) Delete(ctx context.Context, id string, tenantId string) error {
	db := s.DbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.StatisticSqlTask{}).
		Where(" id = ? ", id).
		Where(" tenant_id = ? ", tenantId).
		Where(" deleted = 0 ").
		Updates(map[string]interface{}{"deleted": 1}).Error
	return err
}
