package dao

const SlowQueryAdviceConfigTableName = "slow_query_advice_config"

type SlowQueryAdviceConfig struct {
	ID                    int64  `gorm:"column:id"`
	InstanceID            string `gorm:"column:instance_id"`
	InstanceType          string `gorm:"column:instance_type"`
	TenantID              string `gorm:"column:tenant_id"`
	Status                string `gorm:"column:status"`
	AnalysisResKeepDays   int    `gorm:"column:analysis_res_keep_days"`
	OptimizeTrackMinute   int    `gorm:"column:optimize_track_minute"`
	CreatedAt             int64  `gorm:"column:created_at"`
	UpdatedAt             int64  `gorm:"column:updated_at"`
	DeletedAt             int64  `gorm:"column:deleted_at"`
}

func (SlowQueryAdviceConfig) TableName() string {
	return "slow_query_advice_config"
}

type ListSlowQueryAdviceConfigsResp struct {
	Configs []*ArchiveConfig
	Total   int32
}