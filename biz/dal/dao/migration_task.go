package dao

type MigrationTask struct {
	ID          int64  `gorm:"column:id;primary_key"`
	UserID      string `gorm:"column:user_id"`
	UserName    string `gorm:"column:user_name"`
	TenantID    string `gorm:"column:tenant_id"`
	Type        string `gorm:"column:type"`
	InstanceID  string `gorm:"column:instance_id"`
	DBType      string `gorm:"column:ds_type"`
	DbName      string `gorm:"column:db"`
	Tables      string `gorm:"column:table_list;type:text"`
	Status      uint8  `gorm:"column:status"`
	IgnoreError uint8  `gorm:"column:ignore_error"`
	JobName     string `gorm:"column:job_name"`
	ClusterName string `gorm:"column:cluster_name"`
	ObjectName  string `gorm:"column:object_name"`
	ProgressPt  uint8  `gorm:"column:progress_meter_pt"`
	Description string `gorm:"column:description;type:text"`
	Config      string `gorm:"column:config;type:longtext"`
	CreatedAt   int64  `gorm:"column:created_at"`
	ExecutedAt  int64  `gorm:"column:executed_at"`
	UpdatedAt   int64  `gorm:"column:updated_at"`
	DeletedAt   int64  `gorm:"column:deleted_at"`
	Deleted     uint8  `gorm:"column:deleted"`

	FromType int8 `gorm:"column:from_type"`
}

type MigrationTasksInfo struct {
	Total          int64
	MigrationTasks []*MigrationTask
}

func (MigrationTask) TableName() string {
	return "dbw_data_migration_tasks"
}
