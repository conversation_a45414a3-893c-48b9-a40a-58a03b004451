package dao

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

type CommandSet struct {
	ID           int64  `gorm:"primary_key"`
	TenantID     string `gorm:"column:tenant_id"`
	SessionID    int64  `gorm:"column:session_id"`
	ConnectionID int64  `gorm:"column:connection_id"`
	Content      string `gorm:"column:content"`
	StartTimeMS  int64  `gorm:"column:start_time"`
	EndTimeMS    int64  `gorm:"column:end_time"`
	CreateTimeMS int64  `gorm:"column:create_time"`
	Progress     int    `gorm:"column:progress"`
}

func (CommandSet) TableName() string {
	return "dbw_cmd_set"
}

type Map map[string]interface{}

func (m *Map) Scan(input interface{}) error {
	if input == nil {
		return nil
	}
	b, ok := input.([]byte)
	if !ok {
		return fmt.Errorf("value is not []byte, value: %v", input)
	}
	return json.Unmarshal(b, &m)
}

func (m Map) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return json.Marshal(m)
}

// ToString 方法将 Map 转换为 JSON 字符串
func (m Map) ToString() (string, error) {
	if m == nil {
		return "{}", nil
	}
	b, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(b), nil
}

type Command struct {
	ID           int64   `gorm:"primary_key"`
	TenantID     string  `gorm:"column:tenant_id"`
	CommandSetID int64   `gorm:"column:cmd_set_id"`
	State        int     `gorm:"column:state"`
	Reason       *int    `gorm:"column:reason"`
	Content      string  `gorm:"column:content"`
	StartTimeMS  int64   `gorm:"column:start_time"`
	EndTimeMS    int64   `gorm:"column:end_time"`
	ResultType   *int    `gorm:"column:result_type"`
	Header       *string `gorm:"column:header"`
	ReasonDetail *string `gorm:"column:reason_detail"`
	Extra        Map     `gorm:"TYPE:json,column:extra"`
}

func (Command) TableName() string {
	return "dbw_cmd"
}
