package dao

type ArchiveTask struct {
	TaskId          int64  `gorm:"column:task_id"`
	ArchiveConfigId int64  `gorm:"column:archive_config_id"`
	IsBackUp        bool   `gorm:"column:is_back_up"`
	InstanceId      string `gorm:"column:instance_id"`
	InstanceType    string `gorm:"column:instance_type"`
	Database        string `gorm:"column:db_name"`
	Table           string `gorm:"column:table_name"`
	TenantId        string `gorm:"column:tenant_id"`
	CreateTime      int64  `gorm:"column:create_time"`
	TicketId        int64  `gorm:"column:ticket_id"`
	SqlTaskId       string `gorm:"column:sql_task_id"`
	DeleteSql       string `gorm:"column:delete_sql"`
	ExecTime        int64  `gorm:"column:exec_time"`
	TaskStatus      int8   `gorm:"column:task_status"`
	ErrorMsg        string `gorm:"column:error_msg"`
	UpdateTime      int64  `gorm:"column:update_time"`
	Deleted         int8   `gorm:"column:deleted"`

	BackupTaskId string `gorm:"column:backup_task_id"`

	AffectedRows string `gorm:"column:affected_rows"`
	UserName     string `gorm:"column:user_name"`
}

type ArchiveConfig struct {
	ConfigId          int64  `gorm:"column:config_id"`
	TicketId          int64  `gorm:"column:ticket_id"`
	InstanceId        string `gorm:"column:instance_id"`
	InstanceType      string `gorm:"column:instance_type"`
	TenantId          string `gorm:"column:tenant_id"`
	CronStr           string `gorm:"column:cron_str"`
	BizTimes          string `gorm:"column:biz_times"`
	IsBackUp          int8   `gorm:"column:is_back_up"`
	IsOpen            int8   `gorm:"column:is_open"`
	ArchiveType       int8   `gorm:"column:archive_type"`
	LastArchiveStatus int8   `gorm:"column:last_archive_status"`
	Database          string `gorm:"column:db_name"`
	Table             string `gorm:"column:table_name"`
	OtherCase         string `gorm:"column:other_case"`
	CreateTime        int64  `gorm:"column:create_time"`
	UpdateTime        int64  `gorm:"column:update_time"`
	Deleted           int8   `gorm:"column:deleted"`

	CreateUserId string `gorm:"column:create_user_id"`
	CreateUser   string `gorm:"column:create_user"`

	BackUpType int8 `gorm:"column:backup_type"`
}

func (ArchiveConfig) TableName() string {
	return "dbw_data_archive_config"
}

func (ArchiveTask) TableName() string {
	return "dbw_data_archive_task"
}

type ListArchiveTasksResp struct {
	Tasks []*ArchiveTask
	Total int32
}

type ListArchiveConfigsResp struct {
	Configs []*ArchiveConfig
	Total   int32
}
