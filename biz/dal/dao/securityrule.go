package dao

type SecurityGroup struct {
	ID           int64  `gorm:"primary_key"`
	TenantID     string `gorm:"column:tenant_id"`
	InstanceType string `gorm:"column:instance_type"`
	Name         string `gorm:"column:name"`
	Type         string `gorm:"column:type"`
	Comment      string `gorm:"comment"`
	CreatedAt    int64  `gorm:"column:created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at"`
	Deleted      int8   `gorm:"column:deleted"`
}

type SecurityRule struct {
	ID              int64  `gorm:"primary_key"`
	TenantID        string `gorm:"column:tenant_id"`
	RuleType        int8   `gorm:"column:rule_type"`
	SecurityGroupID int64  `gorm:"column:security_group_id"`
	RuleKey         string `gorm:"column:rule_key"`
	RuleValue       string `gorm:"column:rule_value"`
	RuleLevel       string `gorm:"column:rule_level"`
	Comment         string `gorm:"comment"`
	Func            string `gorm:"func"`
	DslFlow         string `gorm:"dsl_flow"`
	Type            string `gorm:"column:type"`
	DetectPoint     string `gorm:"column:detect_point"`
	State           int8   `gorm:"column:state"`
	CreatedAt       int64  `gorm:"column:created_at"`
	UpdatedAt       int64  `gorm:"column:updated_at"`
	Deleted         int8   `gorm:"column:deleted"`
}

type SecurityFactor struct {
	ID           int64  `gorm:"primary_key"`
	Name         string `gorm:"column:name"`          //因子名称
	FactorMethod string `gorm:"column:factor_method"` //因子对应的方法名
	ResponseType string `gorm:"column:response_type"` //因子的返回值类型
	FactorType   string `gorm:"column:factor_type"`   //因子所属的场景类型
	DetectPoint  string `gorm:"column:detect_point"`  //因子所属的监测点(二级分类)
	Comment      string `gorm:"column:comment"`       //因子描述
	InputComment string `gorm:"column:input_comment"` //输入框备注说明
	Deleted      string `gorm:"column:deleted"`       //是否删除 @0否 @1是
	CreateTime   string `gorm:"column:type"`          //创建时间
	UpdateTime   string `gorm:"column:detect_point"`  //更新时间
}

type SecurityAction struct {
	ID           int64  `gorm:"primary_key"`
	Name         string `gorm:"column:name"`          //动作名称
	ActionMethod string `gorm:"column:action_method"` //动作对应的方法名
	ActionType   string `gorm:"column:action_type"`   //动作所属的场景类型
	DetectPoint  string `gorm:"column:detect_point"`  //动作所属的监测点(二级分类)
	Comment      string `gorm:"column:comment"`       //动作描述
	Deleted      string `gorm:"column:deleted"`       //是否删除 @0否 @1是
	CreateTime   string `gorm:"column:type"`          //创建时间
	UpdateTime   string `gorm:"column:detect_point"`  //更新时间
}

type SecurityRuleExecuteRecord struct {
	ID                   string `gorm:"column:id"`
	TenantId             string `gorm:"column:tenant_id"`
	UserId               string `gorm:"column:user_id"`
	OrderType            int8   `gorm:"column:order_type"`             //单据类型 @0 SQL工作台 @1 工单管理
	OrderId              int64  `gorm:"column:order_id"`               //SQL审核详情单id
	SqlStatement         string `gorm:"column:sql_statement"`          //sql语句
	HighRiskCount        int8   `gorm:"column:high_risk_count"`        //高风险数量
	MiddleRiskCount      int8   `gorm:"column:middle_risk_count"`      //中风险数量
	LowRiskCount         int8   `gorm:"column:low_risk_count"`         //低风险数量
	ExecuteDetailContent string `gorm:"column:execute_detail_content"` //SQL审核结果详情
	CreateTime           int64  `gorm:"column:create_time"`            //创建时间
	UpdateTime           int64  `gorm:"column:update_time"`            //更新时间
}

type SecurityGroups struct {
	SecurityGroups []*SecurityGroup
	Total          int32
}

type SecurityRules struct {
	SecurityRules []*SecurityRule
	Total         int32
}

type SecurityFactors struct {
	SecurityFactors []*SecurityFactor
	Total           int64
}

type SecurityActions struct {
	SecurityActions []*SecurityAction
	Total           int64
}

type SecurityRuleExecuteRecords struct {
	RuleExecuteRecords []*SecurityRuleExecuteRecord
	Total              int64
}

func (SecurityGroup) TableName() string {
	return "dbw_security_group"
}

func (SecurityRule) TableName() string {
	return "dbw_security_rule"
}

func (SecurityFactor) TableName() string {
	return "dbw_security_factor"
}

func (SecurityAction) TableName() string {
	return "dbw_security_action"
}

func (SecurityRuleExecuteRecord) TableName() string {
	return "dbw_security_rule_execute_record"
}
