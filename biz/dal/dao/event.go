package dao

type Event struct {
	ID            uint64  `gorm:"primary_key"`
	UUID          string  `gorm:"column:uuid"`
	InstanceID    string  `gorm:"column:instance_id"`
	Type          string  `gorm:"column:type"`
	SubType       string  `gorm:"column:sub_type"`
	Result        int8    `gorm:"column:result"`
	OperationTime *int64  `gorm:"column:operation_time"`
	Payload       *string `gorm:"column:payload"`
	TenantID      string  `gorm:"column:tenant_id" json:"tenant_id"`
	UserID        string  `gorm:"column:user_id" json:"user_id"`
}

func (Event) TableName() string {
	return "dbw_event"
}
