package dao

type StatisticSqlTask struct {
	Id          int64  `gorm:"column:id;type:BIGINT(20);AUTO_INCREMENT;NOT NULL"`
	InstanceId  string `gorm:"column:instance_id;type:VARCHAR(64);NOT NULL"`
	StatisticId int32  `gorm:"column:statistic_id;type:INT(20);NOT NULL"`
	TenantId    string `gorm:"column:tenant_id;type:VARCHAR(32);NOT NULL"`
	Type        string `gorm:"column:type;type:VARCHAR(64);NOT NULL"`
	DsType      string `gorm:"column:ds_type;type:VARCHAR(32);NOT NULL"`
	SqlTaskId   string `gorm:"column:sql_task_id;type:VARCHAR(64);"`
	Version     int32  `gorm:"column:version;type:INT(10);"`
	Deleted     int8   `gorm:"column:deleted;type:TINYINT(1);"`
}

func (StatisticSqlTask) TableName() string {
	return "dbw_statistic_sql_task"
}
