package dao

type Message struct {
	ID         int64  `gorm:"primary_key"`
	ChatID     int64  `gorm:"column:chat_id"`
	Text       string `gorm:"column:text"`
	ReplyID    int64  `gorm:"reply_id"`
	CreateTime int64  `gorm:"create_time"`
	State      int    `gorm:"state"`
	RatedType  string `gorm:"rated_type"`
	RatedTime  int64  `gorm:"rated_time"`
	UpdateTime int64  `gorm:"update_time"`
}

func (Message) TableName() string {
	return "dbw_dbgpt_message"
}
