package dao

type SqlCCLRule struct {
	ID                  int64  `gorm:"primary_key"`
	RuleID              int64  `gorm:"column:rule_id"`
	UserID              string `gorm:"column:user_id"`
	TenantID            string `gorm:"column:tenant_id"`
	InstanceID          string `gorm:"column:instance_id"`
	InstanceType        string `gorm:"column:instance_type"`
	LinkType            string `gorm:"column:link_type"`
	Duration            int64  `gorm:"column:duration"`
	Keywords            string `gorm:"column:keywords;type:text"` // 关键字
	SqlType             int8   `gorm:"column:sql_type"`           // SQL类型
	State               int8   `gorm:"column:state"`
	ConcurrencyCount    int32  `gorm:"column:concurrency_count"` // 并发数
	RejectedCount       int64  `gorm:"column:rejected_count"`
	CreatedAt           int64  `gorm:"column:created_at"`
	StoppedAt           int64  `gorm:"column:stopped_at"`
	UpdatedAt           int64  `gorm:"column:updated_at"`
	DeletedAt           int64  `gorm:"column:deleted_at"`
	Deleted             int8   `gorm:"column:deleted"`
	ThrottleType        string `gorm:"column:throttle_type"`        // 限流类型(方式)
	ThrottleTarget      string `gorm:"column:throttle_target"`      // 限流类型(方式)
	ThrottleThreshold   int32  `gorm:"column:throttle_threshold"`   // 限流触发阈值(QPS or 前端连接数)
	EndpointID          string `gorm:"column:endpoint_id"`          // 实例终端ID
	EndpointType        string `gorm:"column:endpoint_type"`        // 实例终端类型（读/写）
	ThrottleDB          string `gorm:"column:throttle_db"`          // 实例DB
	ThrottleHost        string `gorm:"column:throttle_host"`        // 被限流的host(内场为PSM，火山为域名或IP)
	ThrottleSqlText     string `gorm:"column:throttle_sql_text"`    // 被限流的sql全文
	ThrottleFingerPrint string `gorm:"column:throttle_fingerprint"` // 被限流的SQL指纹
	ThrottleMode        string `gorm:"column:throttle_mode"`        // 限流模式(Proxy or DB)
	ThrottleGroupIds    string `gorm:"column:throttle_group_ids"`   // 被限流的分片(sharding)
	RegionId            string `gorm:"column:region_id"`            // region
	Description         string `gorm:"column:description"`          // 规则描述
	ThrottleObjId       string `gorm:"column:throttle_object_id"`   // 内场proxy限流规则id
}

type SqlCCLEvent struct {
	ID         int64  `gorm:"primary_key"`
	CclRuleID  int64  `gorm:"column:ccl_rule_id"`
	EventType  int8   `gorm:"column:event"`
	Operator   string `gorm:"column:operator"`
	State      int8   `gorm:"column:state"`
	CreatedAt  int64  `gorm:"column:created_at"`
	UpdatedAt  int64  `gorm:"column:updated_at"`
	InstanceID string `gorm:"column:instance_id"`
	TenantID   string `gorm:"column:tenant_id"`
}

type SqlCclRulesInfo struct {
	Total       int32
	SqlCCLRules []*SqlCCLRule
}

func (SqlCCLRule) TableName() string {
	return "dbw_sql_ccl_rules"
}

func (SqlCCLEvent) TableName() string {
	return "dbw_sql_ccl_event"
}
