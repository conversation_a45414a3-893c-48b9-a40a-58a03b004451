package dao

type Tls struct {
	ID             int64  `gorm:"primary_key"`
	TlsProjectId   string `gorm:"column:tls_project_id"`
	TlsTopicId     string `gorm:"column:tls_topic_id"`
	TlsRuleId      string `gorm:"column:tls_rule_id"`
	TlsHostGroupId string `gorm:"column:tls_hostgroup_id"`
	TlsEndpoint    string `gorm:"column:tls_endpoint"`
	IndexVersion   int32  `gorm:"column:index_version"`
	Region         string `gorm:"column:region"`
	TenantID       string `gorm:"column:tenant_id"`
	Ttl            int32  `gorm:"column:ttl"`
	Deleted        int32  `gorm:"column:deleted"`
}

type ProjectList struct {
	TlsProjectId string `gorm:"column:tls_project_id"`
	TopicNum     string `gorm:"column:topic_num"`
}

type ListAll struct {
	TlsList []*Tls
	Total   int64
}

func (Tls) TableName() string {
	return "dbw_tls"
}
