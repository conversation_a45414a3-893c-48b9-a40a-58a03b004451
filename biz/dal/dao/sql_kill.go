package dao

type SqlKillRule struct {
	ID             int64  `gorm:"primary_key"`
	UserID         string `gorm:"column:user_id"`
	TenantID       string `gorm:"column:tenant_id"`
	InstanceID     string `gorm:"column:instance_id"`
	InstanceType   string `gorm:"column:instance_type"`
	ProtectedUsers string `gorm:"column:protected_users"`
	Duration       int64  `gorm:"column:duration"`
	Keywords       string `gorm:"column:keywords;type:text"`
	FingerPrint    string `gorm:"column:fingerprint"`
	SqlText        string `gorm:"column:sql_text"` // sql全文
	DB             string `gorm:"column:db"`       // 实例DB
	Host           string `gorm:"column:host"`     // host(内场为PSM，火山为域名或IP)
	MaxExecTime    int64  `gorm:"column:max_exec_time"`
	SqlType        string `gorm:"column:sql_type"`
	NodeType       string `gorm:"column:node_type"`
	State          string `gorm:"column:state"`
	CreatedAt      int64  `gorm:"column:created_at"`
	StoppedAt      int64  `gorm:"column:stopped_at"`
	UpdatedAt      int64  `gorm:"column:updated_at"`
	DeletedAt      int64  `gorm:"column:deleted_at"`
	Deleted        int8   `gorm:"column:deleted"`
}

type SqlKillEvent struct {
	ID            int64  `gorm:"primary_key"`
	SqlKillRuleID int64  `gorm:"column:sqlkill_rule_id"`
	TenantID      string `gorm:"column:tenant_id"`
	InstanceID    string `gorm:"column:instance_id"`
	InstanceType  string `gorm:"column:instance_type"`
	EventType     string `gorm:"column:event"`
	Operator      string `gorm:"column:operator"`
	CreatedAt     int64  `gorm:"column:created_at"`
}

type SqlKillRulesInfo struct {
	Total        int32
	SqlKillRules []*SqlKillRule
}

func (SqlKillRule) TableName() string {
	return "dbw_sql_kill_rule"
}

func (SqlKillEvent) TableName() string {
	return "dbw_sql_kill_event"
}
