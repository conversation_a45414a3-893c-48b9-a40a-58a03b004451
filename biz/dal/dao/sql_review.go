package dao

type SqlReview struct {
	ID             string `gorm:"column:id"`
	Name           string `gorm:"column:name"`
	TenantID       string `gorm:"column:tenant_id"`
	InstanceID     string `gorm:"column:instance_id"`
	InstanceType   string `gorm:"column:instance_type"`
	DbName         string `gorm:"column:db_name"`
	ReviewSource   int8   `gorm:"column:review_source"`
	ReviewContent  string `gorm:"review_content"`
	ReviewStatus   int8   `gorm:"column:review_status"`
	Comment        string `gorm:"column:comment"`
	CreateUserName string `gorm:"column:create_user_name"`
	CreateUserID   string `gorm:"column:create_user_id"`
	CreateTime     int64  `gorm:"column:create_time"`
	UpdateTime     int64  `gorm:"column:update_time"`
}

type SqlReviewDetail struct {
	ID                  string `gorm:"column:id"`
	ReviewID            string `gorm:"column:review_id"`
	SqlStatement        string `gorm:"column:sql_statement"`
	Status              int8   `gorm:"column:status"`
	HighRiskCount       int8   `gorm:"column:high_risk_count"`
	MiddleRiskCount     int8   `gorm:"column:middle_risk_count"`
	LowRiskCount        int8   `gorm:"column:low_risk_count"`
	IndexAdviceCount    int8   `gorm:"column:index_advice_count"`
	ReviewDetailContent string `gorm:"column:review_detail_content"`
	CreateTime          int64  `gorm:"column:create_time"`
	UpdateTime          int64  `gorm:"column:update_time"`
}

type SqlReviews struct {
	SqlReviews []*SqlReview
	Total      int64
}

type SqlReviewDetails struct {
	SqlReviewDetails []*SqlReviewDetail
	Total            int64
}

func (SqlReview) TableName() string {
	return "dbw_sql_review"
}

func (SqlReviewDetail) TableName() string {
	return "dbw_sql_review_detail"
}
