package dao

type InspectionSlowLog struct {
	ID                  int64   `gorm:"column:id;primary_key"`
	NumberId            int32   `gorm:"column:number_id;primary_key"`
	InstanceId          string  `gorm:"column:instance_id"`
	InstanceName        string  `gorm:"column:instance_name"`
	InspectionStartTime int64   `gorm:"column:inspection_start_time"`
	InspectionEndTime   int64   `gorm:"column:inspection_end_time"`
	SqlTemplate         string  `gorm:"column:sql_template"`
	DBName              string  `gorm:"column:db_name"`
	ExecuteUser         string  `gorm:"column:execute_user"`
	ExecuteCount        int64   `gorm:"column:execute_count"`
	TotalQueryTime      float64 `gorm:"column:total_query_time"`
	AvgQueryTime        float64 `gorm:"column:avg_query_time"`
	AvgLockTime         float64 `gorm:"column:avg_lock_time"`
	AvgRowsSent         float64 `gorm:"column:avg_rows_sent"`
}

type InspectionSlowLogInfo struct {
	Total             int64
	InspectionSlowLog []*InspectionSlowLog
}

func (InspectionSlowLog) TableName() string {
	return "dbw_inspection_slowlog"
}
