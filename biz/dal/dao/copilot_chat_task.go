package dao

type CopilotChatTask struct {
	ID           string `gorm:"column:id"`
	Chat<PERSON>       string `gorm:"column:chat_id"`
	TaskID       string `gorm:"column:task_id"`
	TenantID     string `gorm:"column:tenant_id"`
	UserID       string `gorm:"column:user_id"`
	Agent<PERSON>ame    string `gorm:"column:agent_name"`
	Role         string `gorm:"column:role"`
	Content      string `gorm:"column:content"`
	Tool<PERSON>alls    string `gorm:"column:tool_calls"`
	ToolCallID   string `gorm:"column:tool_call_id"`
	FunctionName string `gorm:"column:function_name"`
	ExtraInfo    string `gorm:"column:extra_info"`
	IsDelete     int8   `gorm:"column:is_delete"`
	CreateTime   int64  `gorm:"column:create_time"`
	UpdateTime   int64  `gorm:"column:update_time"`
}

type CopilotChatTasks struct {
	CopilotChatTasks []*CopilotChatTask
	Total            int64
}

func (CopilotChatTask) TableName() string {
	return "dbw_copilot_chat_task"
}
