package dao

type PrivilegeTicket struct {
	ID             string `gorm:"primary_key"`
	TenantID       string `gorm:"column:tenant_id"`
	Status         string `gorm:"column:status"`
	ResourceType   string `gorm:"column:resource_type"`
	CreateUser     string `gorm:"column:create_user"`
	CreateUserID   string `gorm:"column:create_user_id"`
	ApprovalUser   string `gorm:"column:approval_user"`
	ApprovalUserID string `gorm:"column:approval_user_id"`
	UserMemo       string `gorm:"column:user_memo"`
	ApprovalMemo   string `gorm:"column:approval_memo"`
	CreateTime     int64  `gorm:"column:create_time"`
	UpdateTime     int64  `gorm:"column:update_time"`
	Title          string `gorm:"column:title"`
}

type PrivilegeTicketResource struct {
	ID                       string `gorm:"primary_key"`
	ResourceID               string `gorm:"column:resource_id"`
	ResourceType             string `gorm:"column:resource_type"`
	InstanceType             string `gorm:"column:instance_type"`
	PrivilegeType            string `gorm:"column:privilege_type"`
	PrivilegeValidityPeriod  int32  `gorm:"column:privilege_validity_period"`
	InstanceID               string `gorm:"column:instance_id"`
	DB                       string `gorm:"column:db"`
	Table                    string `gorm:"column:table"`
	Column                   string `gorm:"column:column"`
	RoleType                 string `gorm:"column:role_type"`
	MaxExecuteCount          int32  `gorm:"column:max_execute_count"`
	MaxExecuteValidityPeriod int32  `gorm:"column:max_execute_validity_period"`
	MaxExecuteDuration       string `gorm:"column:max_execute_duration"`
	MaxResultCount           int32  `gorm:"column:max_result_count"`
	MaxResultValidityPeriod  int32  `gorm:"column:max_result_validity_period"`
	MaxResultDuration        string `gorm:"column:max_result_duration"`
}

func (PrivilegeTicket) TableName() string {
	return "dbw_privilege_ticket"
}

func (PrivilegeTicketResource) TableName() string {
	return "dbw_privilege_ticket_resource"
}

type DbwPrivilegeTickets struct {
	Total            int64
	PrivilegeTickets []*PrivilegeTicket
}
