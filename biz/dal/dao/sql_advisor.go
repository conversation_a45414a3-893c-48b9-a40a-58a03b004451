package dao

type SqlAdvisor struct {
	TaskID       string `gorm:"column:task_id"`
	InstanceID   string `gorm:"column:instance_id"`
	InstanceType string `gorm:"column:instance_type"`
	TenantID     string `gorm:"column:tenant_id"`
	TaskStatus   string `gorm:"column:task_status"`
	DbName       string `gorm:"column:db_name"`
	SqlText      string `gorm:"column:sql_text;type:text"`
	Result       string `gorm:"column:result;type:text"`
	Memo         string `gorm:"column:memo"`
	Deleted      int8   `gorm:"column:deleted"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifyTime   int64  `gorm:"column:modify_time"`
}

type SqlAdvisorInfo struct {
	Total       int32
	SqlAdvisors []*SqlAdvisor
}

func (SqlAdvisor) TableName() string {
	return "dbw_sql_advisor"
}
