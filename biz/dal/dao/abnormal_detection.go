package dao

import (
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

const DbwAbnormalDetectionConfigTableName = "dbw_abnormal_detection_config"
const DbwAbnormalDetectionEventTableName = "dbw_abnormal_detection_event"

type DbwAbnormalDetectionConfig struct {
	ConfigId      int64  `gorm:"column:config_id;type:BIGINT;NOT NULL"`
	InstanceId    string `gorm:"column:instance_id;type:VARCHAR(64);"`
	InstanceType  string `gorm:"column:instance_type;type:VARCHAR(16);"`
	RegionId      string `gorm:"column:region_id;type:VARCHAR(64);"`
	TenantId      string `gorm:"column:tenant_id;type:VARCHAR(64);"`
	Enable        int8   `gorm:"column:enable;type:TINYINT;"`
	Deleted       int8   `gorm:"column:deleted;type:TINYINT;"`
	UpdateTime    int64  `gorm:"column:update_time;type:BIGINT;"`
	LastAlarmTime int64  `gorm:"column:last_alarm_time;type:BIGINT;"`
}

type DbwAbnormalDetectionEvent struct {
	EventId     int64  `gorm:"column:event_id;type:BIGINT;NOT NULL"`
	ConfigId    int64  `gorm:"column:config_id;type:BIGINT;"`
	DateTime    int64  `gorm:"column:date_time;type:BIGINT;"`
	Item        string `gorm:"column:item;type:VARCHAR(64);"`
	ItemValue   string `gorm:"column:item_value;type:VARCHAR(64);"`
	ErrorDetail string `gorm:"column:error_detail;type:LONGTEXT;"`
	Deleted     int8   `gorm:"column:deleted;type:TINYINT;"`
	Score       string `gorm:"column:score;type:VARCHAR(64);"`
}

type GetDetectionEventsReq struct {
	Items        []model.MetricDataItem
	TenantId     string
	InstanceId   string
	InstanceType string
	StartTime    int64
	EndTime      int64
	Offset       int
	Limit        int
	Asc          bool
}

func (DbwAbnormalDetectionConfig) TableName() string {
	return DbwAbnormalDetectionConfigTableName
}

func (DbwAbnormalDetectionEvent) TableName() string {
	return DbwAbnormalDetectionEventTableName
}
