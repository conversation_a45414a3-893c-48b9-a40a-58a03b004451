package dao

type FavouriteSQL struct {
	ID           int64  `gorm:"id"`
	Title        string `gorm:"column:title"`
	InstanceType string `gorm:"column:instance_type"`
	InstanceId   string `gorm:"column:instance_id"`
	TenantId     string `gorm:"column:tenant_id"`
	UserId       string `gorm:"column:user_id"`
	DB<PERSON>ame       string `gorm:"column:dbname"`
	Scope        string `gorm:"column:scope"`
	SQLText      string `gorm:"column:sql_text"`
	UseTimes     int64  `gorm:"column:use_times"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifyTime   int64  `gorm:"column:modify_time"`
}

func (FavouriteSQL) TableName() string {
	return "dbw_favourite_sql"
}
