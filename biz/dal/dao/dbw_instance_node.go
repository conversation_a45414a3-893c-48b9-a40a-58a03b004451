package dao

type InstanceExtraNode struct {
	Id          int64  `gorm:"column:id;type:BIGINT(20);AUTO_INCREMENT;NOT NULL"`
	InstanceId  string `gorm:"column:instance_id;type:VARCHAR(64);NOT NULL"`
	NodeId      string `gorm:"column:node_id;type:VARCHAR(256);NOT NULL"`
	TenantId    string `gorm:"column:tenant_id;type:VARCHAR(32);NOT NULL"`
	Region      string `gorm:"column:region;type:VARCHAR(64);NOT NULL"`
	ProductType string `gorm:"column:product_type;type:VARCHAR(64);NOT NULL"`
	Deleted     int8   `gorm:"column:deleted;type:TINYINT(1);"`
}

func (InstanceExtraNode) TableName() string {
	return "dbw_instance_extra_node"
}
