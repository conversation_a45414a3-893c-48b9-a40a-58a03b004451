package dao

type MigrationProgress struct {
	ID             int64  `gorm:"primary_key"`
	TaskID         int64  `gorm:"column:task_id"`
	TenantID       string `gorm:"column:tenant_id"`
	ProgressDetail string `gorm:"column:progress_detail;type:text"`
	ErrorStatus    int    `gorm:"column:error_status"`
	CreatedAt      int64  `gorm:"column:created_at"`
	DeletedAt      int64  `gorm:"column:deleted_at"`
	Deleted        uint8  `gorm:"column:deleted"`
}

func (MigrationProgress) TableName() string {
	return "dbw_data_migration_progress"
}
