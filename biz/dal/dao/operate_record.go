package dao

type ConsoleOperateRecord struct {
	ID              int64  `gorm:"column:id"`
	TenantID        string `gorm:"column:tenant_id"`
	InstanceID      string `gorm:"column:instance_id"`
	InstanceType    string `gorm:"column:instance_type"`
	DbName          string `gorm:"column:db_name"`
	SqlStatement    string `gorm:"column:sql_statement"`
	SqlType         string `gorm:"column:sql_type"`
	OperationType   int8   `gorm:"column:operation_type"`
	OperateOrderID  int64  `gorm:"column:operate_order_id"`
	OperationStatus int8   `gorm:"column:operation_status"`
	InfluenceLines  int64  `gorm:"column:influence_lines"`
	ExecuteCost     int64  `gorm:"column:execute_cost"`
	CreateUserName  string `gorm:"column:create_user_name"`
	CreateUserID    string `gorm:"column:create_user_id"`
	CreateTime      int64  `gorm:"column:create_time"`
	UpdateTime      int64  `gorm:"column:update_time"`
}

type ConsoleOperateRecords struct {
	ConsoleOperateRecords []*ConsoleOperateRecord
	Total                 int64
}

func (ConsoleOperateRecord) TableName() string {
	return "dbw_console_operate_record"
}

type DasOperateRecord struct {
	ID             int64  `gorm:"column:id"`
	TenantID       string `gorm:"column:tenant_id"`
	InstanceID     string `gorm:"column:instance_id"`
	InstanceType   string `gorm:"column:instance_type"`
	TriggerType    string `gorm:"column:trigger_type"`
	OperationType  string `gorm:"column:operation_category"`
	Action         string `gorm:"column:action"`
	TaskId         string `gorm:"column:task_id"`
	Status         string `gorm:"column:status"`
	CreateUserName string `gorm:"column:user_name"`
	CreateUserID   string `gorm:"column:user_id"`
	Extra          string `gorm:"column:extra"`
	CreateTime     int64  `gorm:"column:create_time"`
	Deleted        int8   `gorm:"column:deleted"`
}

type DasOperateRecords struct {
	DasOperateRecords []*DasOperateRecord
	Total             int64
}

func (DasOperateRecord) TableName() string {
	return "dbw_operation_record_das"
}
