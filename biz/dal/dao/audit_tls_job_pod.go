package dao

type AuditTlsJobPod struct {
	ID                int64  `gorm:"primary_key"`
	AuditTlsId        string `gorm:"column:audit_tls_id"`
	InstanceId        string `gorm:"column:instance_id"` // rds instance id
	TenantId          string `gorm:"column:tenant_id"`
	DbType            string `gorm:"column:db_type"`
	ClusterName       string `gorm:"column:cluster_name"`
	JobName           string `gorm:"column:job_name"`
	JobStatus         int32  `gorm:"column:job_status"`
	ListenPort        string `gorm:"column:listen_port"`
	ConfigRenderImage string `gorm:"column:config_render_image"`
	PacketbeatImage   string `gorm:"column:packetbeat_image"`
	CPU               string `gorm:"column:cpu"`
	Memory            string `gorm:"column:memory"`
	PodName           string `gorm:"column:pod_name"`
	PodStatus         int32  `gorm:"column:pod_status"`
	Status            int32  `gorm:"column:status"`
	Deleted           int32  `gorm:"column:deleted"`
}

func (AuditTlsJobPod) TableName() string {
	return "dbw_audit_tls_job_pod"
}
