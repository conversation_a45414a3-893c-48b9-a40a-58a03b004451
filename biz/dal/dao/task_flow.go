package dao

type TaskFlow struct {
	ID               string `gorm:"column:id"`
	Name             string `gorm:"column:name"`
	TaskType         string `gorm:"column:task_type"`
	InstanceType     string `gorm:"column:instance_type"`
	InstanceId       string `gorm:"column:instance_id"`
	ExecuteMethod    string `gorm:"column:execute_method"`
	TenantID         string `gorm:"column:tenant_id"`
	Creator          string `gorm:"column:creator"`
	CreateTime       int64  `gorm:"column:create_time"`
	Statement        string `gorm:"column:statement"`
	LastExecuteTime  int64  `gorm:"column:last_execute_time"`
	LastExecuteState string `gorm:"column:last_execute_state"`
	RelatedTaskFlow  string `gorm:"column:related_task_flow"`
	State            string `gorm:"column:state"`
	ExecutedTimes    int64  `gorm:"column:executed_times"`
	FailedTimes      int64  `gorm:"column:failed_times"`
	Deleted          int    `gorm:"column:deleted"`
	Comment          string `gorm:"column:comment"`
}

func (TaskFlow) TableName() string {
	return "dbw_task_flow"
}

type TaskFlowConfig struct {
	ID                 string `gorm:"column:id"`
	TaskFlowID         string `gorm:"column:task_flow_id"`
	ExecuteTime        int64  `gorm:"column:execute_time"`
	UserName           string `gorm:"column:user_name"`
	Password           string `gorm:"column:password"`
	Database           string `gorm:"column:database_name"`
	StartTime          *int64 `gorm:"column:start_time"`
	EndTime            *int64 `gorm:"column:end_time"`
	EnableTransaction  bool   `gorm:"column:enable_transaction"`
	IgnoreError        bool   `gorm:"column:ignore_error"`
	Interval           int    `gorm:"column:interval"`
	IntervalUnit       string `gorm:"column:interval_unit"`
	StillRunningPolicy int    `gorm:"column:still_running_policy"`
	Deleted            int    `gorm:"deleted"`
}

func (TaskFlowConfig) TableName() string {
	return "dbw_task_flow_config"
}

type TaskFlowJob struct {
	ID         string `gorm:"column:id"`
	TaskFlowID string `gorm:"column:task_flow_id"`
	StartTime  int64  `gorm:"column:start_time"`
	EndTime    int64  `gorm:"column:end_time"`
	Status     string `gorm:"column:status"`
	Reason     string `gorm:"column:reason"`
	Deleted    int    `gorm:"column:deleted"`
}

func (TaskFlowJob) TableName() string {
	return "dbw_task_flow_job"
}
