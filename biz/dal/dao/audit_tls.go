package dao

type AuditTls struct {
	ID                int64  `gorm:"primary_key"`
	InstanceID        string `gorm:"column:instance_id"`
	ProductType       string `gorm:"column:product_type"`
	TlsId             int64  `gorm:"column:tls_id"`
	FollowInstanceID  string `gorm:"column:follow_instance_id"`
	Region            string `gorm:"column:region"`
	DbType            string `gorm:"column:db_type"`
	DeployType        string `gorm:"column:deploy_type"`
	TenantID          string `gorm:"column:tenant_id"`
	Status            int32  `gorm:"column:status"`
	DefaultCloseType  string `gorm:"column:default_close_type"`
	StorageSqlTypes   string `gorm:"column:storage_sql_types"`
	CapabilitiesFlags int64  `gorm:"column:capabilities_flags"`
	Deleted           int32  `gorm:"column:deleted"`

	EnableFunctions        string `gorm:"column:enable_functions"`
	SqlDesensitizationType string `gorm:"column:sql_desensitization_type"`
	RateSimpling           int32  `gorm:"column:rate_simpling"`
}

func (AuditTls) TableName() string {
	return "dbw_audit_tls"
}
