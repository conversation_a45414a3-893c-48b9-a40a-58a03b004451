package dao

const InspectionTaskTableName = "dbw_inspection_tasks"

type InspectionTask struct {
	TaskID                int64   `gorm:"column:task_id;primary_key"`
	InstanceType          string  `gorm:"column:instance_type"`
	InstanceId            string  `gorm:"column:instance_id"`
	InstanceName          string  `gorm:"column:instance_name"`
	TaskStatus            int8    `gorm:"column:task_status"`
	TaskType              int8    `gorm:"column:task_type"`
	InspectionStartTime   int64   `gorm:"column:inspection_start_time"`
	InspectionEndTime     int64   `gorm:"column:inspection_end_time"`
	InspectionExecuteTime int64   `gorm:"column:execute_time"`
	HealthScore           int8    `gorm:"column:health_score"`
	CpuUsage              float64 `gorm:"column:cpu_usage"`
	MemUsage              float64 `gorm:"column:mem_usage"`
	DiskUsage             float64 `gorm:"column:disk_usage"`
	ConnUsage             float64 `gorm:"column:conn_usage"`
	Qps                   float64 `gorm:"column:qps"`
	Tps                   float64 `gorm:"column:tps"`
	SlowLog               int32   `gorm:"column:slow_sql_num"`
	TenantId              string  `gorm:"column:tenant_id"`
	CreatedAt             int64   `gorm:"column:create_at"`
	UpdatedAt             int64   `gorm:"column:update_at"`
	Deleted               int8    `gorm:"column:deleted"`
	Memo                  string  `gorm:"column:memo;type:text"`
	RegionId              string  `gorm:"column:region_id"`
	LastOneNodeAgg        string  `gorm:"column:last_one_node_agg"`
}

type InspectionTasksInfo struct {
	Total           int64
	InspectionTasks []*InspectionTask
}

func (InspectionTask) TableName() string {
	return InspectionTaskTableName
}
