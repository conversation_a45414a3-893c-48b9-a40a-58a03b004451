package dao

const InspectionParamConfigTableName = "dbw_inspection_param_config"

type InspectionParamConfig struct {
	ID                    int64
	InstanceType                  string `gorm:"column:instance_type"`
	InstanceId                    string `gorm:"column:instance_id"`
	RegionId                      string `gorm:"column:region_id"`
	TenantId                      string `gorm:"column:tenant_id"`
	NodeId                        string `gorm:"column:node_id"`
	Config                        string `gorm:"column:config"`
	CreatedAt                     int64  `gorm:"column:created_at"`
	UpdatedAt                     int64  `gorm:"column:updated_at"`
	Deleted                       int8   `gorm:"column:deleted"`
}

func (InspectionParamConfig) TableName() string {
	return "dbw_inspection_param_config"
}
