package dao

import "testing"

func Test_TicketTableName(t *testing.T) {
	tt := Ticket{
		TicketId: 0,
	}
	got := tt.TableName()
	if got != "ticket_record" {
		t.<PERSON><PERSON><PERSON>("got %s,want ticket_record", got)
	}
}

func Test_TicketRecordTableName(t *testing.T) {
	tt := TicketPreCheckResult{
		TicketId: 0,
	}
	got := tt.TableName()
	if got != "ticket_pre_check_result" {
		t.<PERSON><PERSON><PERSON>("got %s,want ticket_pre_check_result", got)
	}
}
