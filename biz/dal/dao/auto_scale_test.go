package dao

import "testing"

func TestDbwAutoScaleEvent_TableName(t *testing.T) {
	type fields struct {
		EventId           int64
		RuleId            int64
		InstanceId        string
		TenantId          string
		ScalingType       int8
		CreateTime        int64
		StartTime         int64
		EndTime           int64
		BeforeValue       int32
		TargetUtilization string
		AfterValue        int32
		Status            int8
		Memo              string
		Deleted           int8
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "test1",
			fields: fields{
				EventId: 10,
			},
			want: "dbw_auto_scale_event",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := DbwAutoScaleEvent{
				EventId:           tt.fields.EventId,
				RuleId:            tt.fields.RuleId,
				InstanceId:        tt.fields.InstanceId,
				TenantId:          tt.fields.TenantId,
				ScalingType:       tt.fields.ScalingType,
				CreateTime:        tt.fields.CreateTime,
				StartTime:         tt.fields.StartTime,
				EndTime:           tt.fields.EndTime,
				BeforeValue:       tt.fields.BeforeValue,
				TargetUtilization: tt.fields.TargetUtilization,
				AfterValue:        tt.fields.AfterValue,
				Status:            tt.fields.Status,
				Memo:              tt.fields.Memo,
				Deleted:           tt.fields.Deleted,
			}
			if got := db.TableName(); got != tt.want {
				t.Errorf("TableName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDbwAutoScaleRule_TableName(t *testing.T) {
	type fields struct {
		RuleId            int64
		InstanceId        string
		InstanceType      string
		ScaleTarget       string
		TenantId          string
		RegionId          string
		ScalingType       int8
		ScalingThreshold  float64
		Enable            int8
		ObservationWindow int32
		CloudAlarmId      string
		CreateTime        int64
		UpdateTime        int64
		UpdateUser        string
		Deleted           int8
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "test1",
			fields: fields{
				RuleId: 10,
			},
			want: "dbw_auto_scale_rules",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := DbwAutoScaleRule{
				RuleId:            tt.fields.RuleId,
				InstanceId:        tt.fields.InstanceId,
				InstanceType:      tt.fields.InstanceType,
				ScaleTarget:       tt.fields.ScaleTarget,
				TenantId:          tt.fields.TenantId,
				RegionId:          tt.fields.RegionId,
				ScalingType:       tt.fields.ScalingType,
				ScalingThreshold:  tt.fields.ScalingThreshold,
				Enable:            tt.fields.Enable,
				ObservationWindow: tt.fields.ObservationWindow,
				CloudAlarmId:      tt.fields.CloudAlarmId,
				CreateTime:        tt.fields.CreateTime,
				UpdateTime:        tt.fields.UpdateTime,
				UpdateUser:        tt.fields.UpdateUser,
				Deleted:           tt.fields.Deleted,
			}
			if got := db.TableName(); got != tt.want {
				t.Errorf("TableName() = %v, want %v", got, tt.want)
			}
		})
	}
}
