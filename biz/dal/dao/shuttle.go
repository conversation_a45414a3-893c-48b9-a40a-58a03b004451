package dao

import "code.byted.org/infcs/ds-lib/framework/db"

type Shuttle struct {
	db.BizModel
	ID          string  `gorm:"column:id"`
	Name        string  `gorm:"column:name"`
	Type        string  `gorm:"column:type"`
	AccountID   string  `gorm:"column:account_id"`
	Description string  `gorm:"column:description"`
	Clients     *string `gorm:"column:clients"`
	Servers     *string `gorm:"column:servers"`
}

func (Shuttle) TableName() string {
	return "dbw_shuttle"
}
