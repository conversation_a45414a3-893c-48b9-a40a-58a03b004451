package dao

type InstanceExtraTls struct {
	Id         int64  `gorm:"column:id;type:BIGINT(20);AUTO_INCREMENT;NOT NULL"`
	InstanceId string `gorm:"column:instance_id;type:VARCHAR(64);NOT NULL"`
	TenantId   string `gorm:"column:tenant_id;type:VARCHAR(32);NOT NULL"`
	DataType   string `gorm:"column:data_type;type:VARCHAR(64);"`
	TlsId      int64  `gorm:"column:tls_id;type:BIGINT(20);"`
	Deleted    int32  `gorm:"column:deleted"`
}

func (InstanceExtraTls) TableName() string {
	return "dbw_instance_extra_tls"
}
