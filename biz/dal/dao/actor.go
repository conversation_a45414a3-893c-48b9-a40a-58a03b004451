package dao

type Actor struct {
	ID         int64  `gorm:"primary_key"`
	Kind       string `gorm:"column:kind"`
	Name       string `gorm:"column:name"`
	State      int32  `gorm:"column:state"`
	UserState  string `gorm:"column:user_state"`
	CreateTime string `gorm:"column:create_time"`
	ModifyTime int64  `gorm:"column:modify_time"`
	Version    int32  `gorm:"column:version"`
}

func (Actor) TableName() string {
	return "dbw_actor"
}
