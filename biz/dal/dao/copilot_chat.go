package dao

type Copilot<PERSON>hat struct {
	ID         string `gorm:"column:id"`
	TenantID   string `gorm:"column:tenant_id"`
	InstanceID string `gorm:"column:instance_id"`
	UserID     string `gorm:"column:user_id"`
	SceneType  int8   `gorm:"column:scene_type"`
	ChatName   string `gorm:"column:chat_name"`
	IsDelete   int8   `gorm:"column:is_delete"`
	CreateTime int64  `gorm:"column:create_time"`
}

type CopilotChats struct {
	CopilotChats []*CopilotChat
	Total        int64
}

func (CopilotChat) TableName() string {
	return "dbw_copilot_chat"
}
