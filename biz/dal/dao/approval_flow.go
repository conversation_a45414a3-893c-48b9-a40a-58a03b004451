package dao

const (
	ApprovalFlowTableName         = "dbw_approval_flow"
	ApprovalFlowConfigTableName   = "dbw_approval_flow_config"
	ApprovalNodeTableName         = "dbw_approval_flow_node"
	ApprovalFlowTemplateTableName = "dbw_approval_flow_template"
	ApprovalFlowHistoryTableName  = "dbw_approval_flow_history"
)

type ApprovalFlow struct {
	FlowId         int64  `gorm:"column:flow_id"`
	InstanceId     string `gorm:"column:instance_id"`
	TenantId       string `gorm:"column:tenant_id"`
	FlowTemplateId int64  `gorm:"column:flow_template_id"`
	Status         int8   `gorm:"column:status"`
	Step           int8   `gorm:"column:step"`
	ApproverNodeId int64  `gorm:"column:approver_node_id"`
	CreateTime     int64  `gorm:"column:create_time"`
	Deleted        int8   `gorm:"column:deleted"`
}

type ApprovalFlowConfig struct {
	ConfigId       int64  `gorm:"column:config_id"`
	ConfigName     string `gorm:"column:config_name"`
	ConfigType     int8   `gorm:"column:config_type"`
	FlowScenes     string `gorm:"column:flow_scenes"`
	TenantId       string `gorm:"column:tenant_id"`
	CreateUserId   string `gorm:"column:create_user_id"`
	CreateUserName string `gorm:"column:create_user_name"`
	ModifyUserId   string `gorm:"column:modify_user_id"`
	CreateTime     int64  `gorm:"column:create_time"`
	ModifyTime     int64  `gorm:"column:modify_time"`
	Memo           string `gorm:"column:memo"`
	Deleted        string `gorm:"column:deleted"`
}

type ApprovalNode struct {
	NodeId         int64  `gorm:"column:node_id"`
	NodeType       int8   `gorm:"column:node_type"`
	NodeName       string `gorm:"column:node_name"`
	ApproverIds    string `gorm:"column:approver_ids"` // ,隔开
	Approvers      string `gorm:"column:approvers"`
	TenantId       string `gorm:"column:tenant_id"`
	Memo           string `gorm:"column:memo"`
	CreateUser     string `gorm:"column:create_user"`
	CreateUserName string `gorm:"column:create_user_name"`
	ModifyUser     string `gorm:"column:modify_user"`
	CreateTime     int64  `gorm:"column:create_time"`
	ModifyTime     int64  `gorm:"column:modify_time"`
	Deleted        int8   `gorm:"column:deleted"`
}

type ApprovalFlowTemplate struct {
	TemplateId   int64  `gorm:"column:template_id"`
	TotalStep    int64  `gorm:"column:total_step"`
	FlowNodes    string `gorm:"column:flow_nodes"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifyTime   int64  `gorm:"column:modify_time"`
	CreateUserId string `gorm:"column:create_user_id"`
	ModifyUserId string `gorm:"column:modify_user_id"`
	TenantId     string `gorm:"column:tenant_id"`
	Deleted      int8   `gorm:"column:deleted"`

	NoNeedForApproval    int8 `gorm:"column:no_need_for_approval"`
	NotAllowSelfApproval int8 `gorm:"column:not_allow_self_approval"`
	SelfAutoApproval     int8 `gorm:"column:self_auto_approval"`
}

type ApprovalFlowHistory struct {
	Id             int64  `gorm:"column:id"`
	FlowTemplateId int64  `gorm:"column:flow_template_id"`
	FlowId         int64  `gorm:"column:flow_id"`
	Step           int8   `gorm:"column:step"`
	OperatorId     string `gorm:"column:operator_id"`
	OperateType    int8   `gorm:"column:operate_type"`
	CreateTime     int64  `gorm:"column:create_time"`
	Deleted        int8   `gorm:"column:deleted"`
}

func (ApprovalFlow) TableName() string {
	return ApprovalFlowTableName
}

func (ApprovalFlowConfig) TableName() string {
	return ApprovalFlowConfigTableName
}

func (ApprovalNode) TableName() string {
	return ApprovalNodeTableName
}

func (ApprovalFlowTemplate) TableName() string {
	return ApprovalFlowTemplateTableName
}

func (ApprovalFlowHistory) TableName() string {
	return ApprovalFlowHistoryTableName
}
