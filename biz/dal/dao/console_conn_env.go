package dao

type ConsoleConnEnv struct {
	ID           string `gorm:"primary_key"`
	InstanceType string `gorm:"instance_type"`
	InstanceId   string `gorm:"instance_id"`
	TenantId     string `gorm:"tenant_id"`
	UserId       string `gorm:"user_id"`
	AccountName  string `gorm:"account_name"`
	Password     string `gorm:"password"`
	CreateTime   int64  `gorm:"create_time"`
	UpdateTime   int64  `gorm:"update_time"`
	Env          string `gorm:"env"`
}

func (ConsoleConnEnv) TableName() string {
	return "dbw_console_conn_env"
}
