package dao

type DbwUser struct {
	ID                    int64  `gorm:"primary_key"`
	TenantID              string `gorm:"column:tenant_id"`
	UserID                string `gorm:"column:user_id"`
	UserName              string `gorm:"column:user_name"`
	Role                  string `gorm:"column:role"`
	AccountType           string `gorm:"account_type"`
	MaxExecuteCount       int32  `gorm:"column:max_execute_count"`
	CurExecuteCount       int32  `gorm:"column:cur_execute_count"`
	MaxExecuteExpiredTime int32  `gorm:"column:max_execute_expired_time"`
	MaxExecuteDuration    string `gorm:"column:max_execute_duration"`
	MaxResultCount        int32  `gorm:"column:max_result_count"`
	CurResultCount        int32  `gorm:"column:cur_result_count"`
	MaxResultExpiredTime  int32  `gorm:"column:max_result_expired_time"`
	MaxResultDuration     string `gorm:"column:max_result_duration"`
	FeishuWebhook         string `gorm:"column:feishu_webhook"`
	State                 string `gorm:"column:state"`
	GroupType             string `gorm:"column:group_type"`
	CreatedAt             int64  `gorm:"column:created_at"`
	DeletedAt             int64  `gorm:"column:deleted_at"`
	UpdatedAt             int64  `gorm:"column:updated_at"`
	Deleted               int8   `gorm:"column:deleted"`
}

type DbwUserGroup struct {
	ID                    int64  `gorm:"primary_key"`                     // 用户组id
	TenantID              string `gorm:"column:tenant_id"`                // 租户id
	GroupName             string `gorm:"column:group_name"`               // 用户组名称
	Memo                  string `gorm:"column:memo"`                     // 用户组备注
	Role                  string `gorm:"column:role"`                     // DBW角色
	MaxExecuteCount       int32  `gorm:"column:max_execute_count"`        // 每日执行次数上限
	MaxExecuteExpiredTime int32  `gorm:"column:max_execute_expired_time"` // 每日执行次数到期时间
	MaxExecuteDuration    string `gorm:"column:max_execute_duration"`     // 每日执行次数有效期枚举
	MaxResultCount        int32  `gorm:"column:max_result_count"`         // 每日查询行数上限
	MaxResultExpiredTime  int32  `gorm:"column:max_result_expired_time"`  // 每日查询行数到期时间
	MaxResultDuration     string `gorm:"column:max_result_duration"`      // 每日查询行数有效期枚举
	ByProject             bool   `gorm:"column:by_project"`               // 是否关联project
	Sync                  bool   `gorm:"column:sync"`                     // 是否定期同步用户
	CreatedAt             int64  `gorm:"column:created_at"`               // 创建时间
	UpdatedAt             int64  `gorm:"column:updated_at"`               // 更新时间
	DeletedAt             int64  `gorm:"column:deleted_at"`               // 删除时间
	Deleted               bool   `gorm:"column:deleted"`                  // 删除状态
}

type ProjectGroupRelation struct {
	ID        int64  `gorm:"primary_key"` // 主键
	TenantID  string `gorm:"column:tenant_id"`
	GroupID   int64  `gorm:"column:group_id"`   // 用户组id
	Project   string `gorm:"column:project"`    // 用户组关联的project
	IAMGroups string `gorm:"column:iam_groups"` // 用户组关联的project下用户组
	IAMUser   bool   `gorm:"column:iam_user"`   // 用户组是否关联了project下的非组用户
	CreatedAt int64  `gorm:"column:created_at"` // 创建时间
	UpdatedAt int64  `gorm:"column:updated_at"` // 更新时间
	DeletedAt int64  `gorm:"column:deleted_at"` // 删除时间
	Deleted   bool   `gorm:"column:deleted"`    // 删除状态
}

type UserGroupRelation struct {
	ID        int64  `gorm:"primaryKey"` // 主键
	TenantID  string `gorm:"column:tenant_id"`
	GroupID   int64  `gorm:"column:group_id"`   // 用户组id
	UserID    string `gorm:"column:user_id"`    // 用户id
	CreatedAt int64  `gorm:"column:created_at"` // 创建时间
	UpdatedAt int64  `gorm:"column:updated_at"` // 更新时间
	DeletedAt int64  `gorm:"column:deleted_at"` // 删除时间
	Deleted   bool   `gorm:"column:deleted"`    // 删除状态
}

type InstancePrivilege struct {
	ID            int64  `gorm:"primary_key"`
	TenantID      string `gorm:"column:tenant_id"`
	InstanceId    string `gorm:"column:instance_id"`
	InstanceType  string `gorm:"column:instance_type"`
	UserID        string `gorm:"column:user_id"`
	UserName      string `gorm:"column:user_name"`
	GroupId       int64  `gorm:"column:group_id"`
	PrivilegeType string `gorm:"column:privilege_type"`
	Grantor       string `gorm:"column:grantor"`
	State         string `gorm:"column:state"`
	CreatedAt     int64  `gorm:"column:created_at"`
	UpdatedAt     int64  `gorm:"column:updated_at"`
	ExpiredAt     int64  `gorm:"column:expired_at"`
	DeletedAt     int64  `gorm:"column:deleted_at"`
	Deleted       int8   `gorm:"column:deleted"`
}

type DatabasePrivilege struct {
	ID            int64  `gorm:"primary_key"`
	TenantID      string `gorm:"column:tenant_id"`
	InstanceId    string `gorm:"column:instance_id"`
	InstanceType  string `gorm:"column:instance_type"`
	DbName        string `gorm:"column:db_name"`
	UserID        string `gorm:"column:user_id"`
	UserName      string `gorm:"column:user_name"`
	GroupId       int64  `gorm:"column:group_id"`
	PrivilegeType string `gorm:"column:privilege_type"`
	Grantor       string `gorm:"column:grantor"`
	State         string `gorm:"column:state"`
	CreatedAt     int64  `gorm:"column:created_at"`
	UpdatedAt     int64  `gorm:"column:updated_at"`
	ExpiredAt     int64  `gorm:"column:expired_at"`
	DeletedAt     int64  `gorm:"column:deleted_at"`
	Deleted       int8   `gorm:"column:deleted"`
}

type TablePrivilege struct {
	ID            int64  `gorm:"primary_key"`
	TenantID      string `gorm:"column:tenant_id"`
	InstanceId    string `gorm:"column:instance_id"`
	InstanceType  string `gorm:"column:instance_type"`
	DbName        string `gorm:"column:db_name"`
	TbName        string `gorm:"column:table_name;type:text"`
	GroupId       int64  `gorm:"column:group_id"`
	UserID        string `gorm:"column:user_id"`
	UserName      string `gorm:"column:user_name"`
	PrivilegeType string `gorm:"column:privilege_type"`
	Grantor       string `gorm:"column:grantor"`
	State         string `gorm:"column:state"`
	CreatedAt     int64  `gorm:"column:created_at"`
	UpdatedAt     int64  `gorm:"column:updated_at"`
	ExpiredAt     int64  `gorm:"column:expired_at"`
	DeletedAt     int64  `gorm:"column:deleted_at"`
	Deleted       int8   `gorm:"column:deleted"`
}

type ColumnPrivilege struct {
	ID            int64  `gorm:"primary_key"`
	TenantID      string `gorm:"column:tenant_id"`
	InstanceId    string `gorm:"column:instance_id"`
	InstanceType  string `gorm:"column:instance_type"`
	DbName        string `gorm:"column:db_name"`
	TbName        string `gorm:"column:table_name;type:text"`
	ColumnName    string `gorm:"column:column_name;type:text"`
	UserID        string `gorm:"column:user_id"`
	UserName      string `gorm:"column:user_name"`
	GroupId       int64  `gorm:"column:group_id"`
	PrivilegeType string `gorm:"column:privilege_type"`
	Grantor       string `gorm:"column:grantor"`
	State         string `gorm:"column:state"`
	CreatedAt     int64  `gorm:"column:created_at"`
	UpdatedAt     int64  `gorm:"column:updated_at"`
	ExpiredAt     int64  `gorm:"column:expired_at"`
	DeletedAt     int64  `gorm:"column:deleted_at"`
	Deleted       int8   `gorm:"column:deleted"`
}

type DbwUsers struct {
	Users []*DbwUser
	Total int32
}

type DbwUserGroups struct {
	Groups []*DbwUserGroup
	Total  int32
}

type UserGroupRelations struct {
	GroupRelations []*UserGroupRelation
	Total          int32
}

type ProjectGroupRelations struct {
	ProjectGroupRelations []*ProjectGroupRelation
	Total                 int32
}

type InstancePrivileges struct {
	Privileges []*InstancePrivilege
	Total      int32
}

type DatabasePrivileges struct {
	Privileges []*DatabasePrivilege
	Total      int32
}

type TablePrivileges struct {
	Privileges []*TablePrivilege
	Total      int32
}

type ColumnPrivileges struct {
	Privileges []*ColumnPrivilege
	Total      int32
}

func (DbwUser) TableName() string {
	return "dbw_user"
}

func (DbwUserGroup) TableName() string {
	return "dbw_user_group"
}

func (UserGroupRelation) TableName() string {
	return "dbw_user_group_relation"
}

func (ProjectGroupRelation) TableName() string {
	return "dbw_project_group_relation"
}

func (InstancePrivilege) TableName() string {
	return "instance_privilege"
}

func (DatabasePrivilege) TableName() string {
	return "database_privilege"
}

func (TablePrivilege) TableName() string {
	return "table_privilege"
}

func (ColumnPrivilege) TableName() string {
	return "column_privilege"
}
