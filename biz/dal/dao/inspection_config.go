package dao

const InspectionConfigTableName = "dbw_inspection_config"

type InspectionConfig struct {
	InstanceType                  string `gorm:"column:instance_type"`
	InstanceId                    string `gorm:"column:instance_id"`
	InstanceName                  string `gorm:"column:instance_name"`
	RegionId                      string `gorm:"column:region_id"`
	IsOpen                        int8   `gorm:"column:is_open"`
	InspectionStartTime           int64  `gorm:"column:inspection_start_time"`
	InspectionEndTime             int64  `gorm:"column:inspection_end_time"`
	InspectionExecutableStartTime int64  `gorm:"column:inspection_executable_start_time"`
	InspectionExecutableEndTime   int64  `gorm:"column:inspection_executable_end_time"`
	TenantId                      string `gorm:"column:tenant_id"`
}

type InspectionConfigInfo struct {
	Total           int64
	InspectionTasks []*InspectionConfig
}

func (InspectionConfig) TableName() string {
	return "dbw_inspection_config"
}
