package dao

type StatisticSqlTls struct {
	Id       int64  `gorm:"column:id;type:BIGINT(20);AUTO_INCREMENT;NOT NULL"`
	TenantId string `gorm:"column:tenant_id;type:VARCHAR(32);NOT NULL"`
	Region   string `gorm:"column:region;type:VARCHAR(64);NOT NULL"`
	DbType   string `gorm:"column:db_type;type:VARCHAR(32);"`
	DataType string `gorm:"column:data_type;type:VARCHAR(64);"`
	TlsId    int64  `gorm:"column:tls_id;type:BIGINT(20);"`
	Deleted  int8   `gorm:"column:deleted;type:TINYINT(1);"`
}

func (StatisticSqlTls) TableName() string {
	return "dbw_statistic_sql_tls"
}
