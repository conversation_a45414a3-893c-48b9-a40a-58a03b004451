package dao

type CommandResult struct {
	ID             uint64  `gorm:"primary_key"`
	TenantID       string  `gorm:"column:tenant_id"`
	SessionID      int64   `gorm:"column:session_id"`
	ConnectionID   int64   `gorm:"column:connection_id"`
	CommandID      int64   `gorm:"column:cmd_id"`
	SessionExpired int     `gorm:"column:session_expired"`
	Offset         int64   `gorm:"column:moffset"`
	Row            *string `gorm:"column:mrow"`
	CreateTimeMS   int64   `gorm:"column:create_time"`
}

func (CommandResult) TableName() string {
	return "dbw_cmd_result"
}
