package dao

type MysqlInformationSchemaTable struct {
	ID           int64  `gorm:"primary_key"`
	InstanceId   string `gorm:"column:instance_id"`
	InstanceType string `gorm:"column:instance_type"`
	InstanceName string `gorm:"column:instance_name"`
	TenantId     string `gorm:"column:tenant_id"`
	Region       string `gorm:"column:region"`
	Source       string `gorm:"column:source"`

	TableCatalog       string `gorm:"column:table_catalog"`    // use
	TableSchema        string `gorm:"column:table_schema"`     // use
	TableNameValue     string `gorm:"column:table_name_value"` // use
	TableType          string `gorm:"column:table_type"`
	Engine             string `gorm:"column:engine"` // use
	Version            uint64 `gorm:"column:version"`
	RowFormat          string `gorm:"column:row_format"`
	TableRows          uint64 `gorm:"column:table_rows"`     // use
	AvgRowLength       uint64 `gorm:"column:avg_row_length"` // use
	DataLength         uint64 `gorm:"column:data_length"`    // use
	MaxDataLength      uint64 `gorm:"column:max_data_length"`
	IndexLength        uint64 `gorm:"column:index_length"`         // use
	DataFree           uint64 `gorm:"column:data_free"`            // use
	AutoIncrementValue uint64 `gorm:"column:auto_increment_value"` // use
	CreateTime         string `gorm:"column:create_time"`
	UpdateTime         string `gorm:"column:update_time"`
	CheckTime          string `gorm:"column:check_time"`
	TableCollation     string `gorm:"column:table_collation"`
	Checksum           uint64 `gorm:"column:checksum"`
	CreateOptions      string `gorm:"column:create_options"`
	TableComment       string `gorm:"column:table_comment"`

	CollectTime int64 `gorm:"column:collect_time"`
	CreatedAt   int64 `gorm:"column:created_at"`
	UpdatedAt   int64 `gorm:"column:updated_at"`
	DeletedAt   int64 `gorm:"column:deleted_at"`
	Deleted     int8  `gorm:"column:deleted"`
}

type MysqlInformationSchemaTables struct {
	Total int32
	Items []*MysqlInformationSchemaTable
}

func (MysqlInformationSchemaTable) TableName() string {
	return "mysql_information_schema_tables"
}

type TableIoWaitsSummaryByIndexUsage struct {
	ID           int64  `gorm:"primary_key;column:id"`
	InstanceId   string `gorm:"column:instance_id"` // 实例ID
	InstanceName string `gorm:"column:instance_name"`
	InstanceType string `gorm:"column:instance_type"` // 实例类型
	TenantId     string `gorm:"column:tenant_id"`     // 租户ID
	Region       string `gorm:"column:region"`        // 区域
	Source       string `gorm:"column:source"`        // 数据来源

	// 性能统计字段
	ObjectType   string `gorm:"column:object_type"`
	ObjectSchema string `gorm:"column:object_schema"` // use
	ObjectName   string `gorm:"column:object_name"`   // use
	IndexName    string `gorm:"column:index_name"`    // use

	// 聚合统计
	CountStar    uint64 `gorm:"column:count_star"`     // use
	SumTimerWait uint64 `gorm:"column:sum_timer_wait"` // 总等待时间
	MinTimerWait uint64 `gorm:"column:min_timer_wait"`
	AvgTimerWait uint64 `gorm:"column:avg_timer_wait"`
	MaxTimerWait uint64 `gorm:"column:max_timer_wait"`

	// 读写统计
	CountRead      uint64 `gorm:"column:count_read"`     // 读次数
	SumTimerRead   uint64 `gorm:"column:sum_timer_read"` // 读耗时
	MinTimerRead   uint64 `gorm:"column:min_timer_read"`
	AvgTimerRead   uint64 `gorm:"column:avg_timer_read"`
	MaxTimerRead   uint64 `gorm:"column:max_timer_read"`
	CountWrite     uint64 `gorm:"column:count_write"`     // 写次数
	SumTimerWrite  uint64 `gorm:"column:sum_timer_write"` // 写耗时
	MinTimerWrite  uint64 `gorm:"column:min_timer_write"`
	AvgTimerWrite  uint64 `gorm:"column:avg_timer_write"`
	MaxTimerWrite  uint64 `gorm:"column:max_timer_write"`
	CountFetch     uint64 `gorm:"column:count_fetch"`
	SumTimerFetch  uint64 `gorm:"column:sum_timer_fetch"`
	MinTimerFetch  uint64 `gorm:"column:min_timer_fetch"`
	AvgTimerFetch  uint64 `gorm:"column:avg_timer_fetch"`
	MaxTimerFetch  uint64 `gorm:"column:max_timer_fetch"`
	CountInsert    uint64 `gorm:"column:count_insert"`
	SumTimerInsert uint64 `gorm:"column:sum_timer_insert"`
	MinTimerInsert uint64 `gorm:"column:min_timer_insert"`
	AvgTimerInsert uint64 `gorm:"column:avg_timer_insert"`
	MaxTimerInsert uint64 `gorm:"column:max_timer_insert"`
	CountUpdate    uint64 `gorm:"column:count_update"`
	SumTimerUpdate uint64 `gorm:"column:sum_timer_update"`
	MinTimerUpdate uint64 `gorm:"column:min_timer_update"`
	AvgTimerUpdate uint64 `gorm:"column:avg_timer_update"`
	MaxTimerUpdate uint64 `gorm:"column:max_timer_update"`
	CountDelete    uint64 `gorm:"column:count_delete"`
	SumTimerDelete uint64 `gorm:"column:sum_timer_delete"`
	MinTimerDelete uint64 `gorm:"column:min_timer_delete"`
	AvgTimerDelete uint64 `gorm:"column:avg_timer_delete"`
	MaxTimerDelete uint64 `gorm:"column:max_timer_delete"`

	CollectTime int64 `gorm:"column:collect_time"`
	CreatedAt   int64 `gorm:"column:created_at"` // 自动时间戳
	UpdatedAt   int64 `gorm:"column:updated_at"`
	DeletedAt   int64 `gorm:"column:deleted_at"`
	Deleted     int8  `gorm:"column:deleted"` // 软删除标记
}

type TableIoWaitsSummaryByIndexUsages struct {
	Total int32                              `json:"total"` // 总数
	Items []*TableIoWaitsSummaryByIndexUsage `json:"items"` // 数据列表
}

func (TableIoWaitsSummaryByIndexUsage) TableName() string {
	return "table_io_waits_summary_by_index_usage" // 实际存储表名
}

type TableIoWaitsSummaryByTable struct {
	// 业务扩展字段
	ID           int64  `gorm:"primaryKey;column:id"` // 主键
	InstanceId   string `gorm:"column:instance_id"`   // 实例ID
	InstanceName string `gorm:"column:instance_name"`
	InstanceType string `gorm:"column:instance_type"` // 实例类型
	TenantId     string `gorm:"column:tenant_id"`     // 租户ID
	Region       string `gorm:"column:region"`        // 区域
	Source       string `gorm:"column:source"`        // 来源标识

	// 标准字段
	ObjectType   string `gorm:"column:object_type"`   // varchar(64), 对象类型
	ObjectSchema string `gorm:"column:object_schema"` // varchar(64), 数据库名
	ObjectName   string `gorm:"column:object_name"`   // varchar(64), 表名

	// 聚合统计
	CountStar    uint64 `gorm:"column:count_star"`     // 总操作次数
	SumTimerWait uint64 `gorm:"column:sum_timer_wait"` // 总等待时间 (皮秒)
	MinTimerWait uint64 `gorm:"column:min_timer_wait"`
	AvgTimerWait uint64 `gorm:"column:avg_timer_wait"`
	MaxTimerWait uint64 `gorm:"column:max_timer_wait"`

	// 读操作统计
	CountRead    uint64 `gorm:"column:count_read"`
	SumTimerRead uint64 `gorm:"column:sum_timer_read"`
	MinTimerRead uint64 `gorm:"column:min_timer_read"`
	AvgTimerRead uint64 `gorm:"column:avg_timer_read"`
	MaxTimerRead uint64 `gorm:"column:max_timer_read"`

	// 写操作统计
	CountWrite    uint64 `gorm:"column:count_write"`
	SumTimerWrite uint64 `gorm:"column:sum_timer_write"`
	MinTimerWrite uint64 `gorm:"column:min_timer_write"`
	AvgTimerWrite uint64 `gorm:"column:avg_timer_write"`
	MaxTimerWrite uint64 `gorm:"column:max_timer_write"`

	// 细化操作类型 (SELECT/INSERT/UPDATE/DELETE)
	CountFetch    uint64 `gorm:"column:count_fetch"` // SELECT 操作次数
	SumTimerFetch uint64 `gorm:"column:sum_timer_fetch"`
	MinTimerFetch uint64 `gorm:"column:min_timer_fetch"`
	AvgTimerFetch uint64 `gorm:"column:avg_timer_fetch"`
	MaxTimerFetch uint64 `gorm:"column:max_timer_fetch"`

	CountInsert    uint64 `gorm:"column:count_insert"` // INSERT 操作次数
	SumTimerInsert uint64 `gorm:"column:sum_timer_insert"`
	MinTimerInsert uint64 `gorm:"column:min_timer_insert"`
	AvgTimerInsert uint64 `gorm:"column:avg_timer_insert"`
	MaxTimerInsert uint64 `gorm:"column:max_timer_insert"`

	CountUpdate    uint64 `gorm:"column:count_update"` // UPDATE 操作次数
	SumTimerUpdate uint64 `gorm:"column:sum_timer_update"`
	MinTimerUpdate uint64 `gorm:"column:min_timer_update"`
	AvgTimerUpdate uint64 `gorm:"column:avg_timer_update"`
	MaxTimerUpdate uint64 `gorm:"column:max_timer_update"`

	CountDelete    uint64 `gorm:"column:count_delete"` // DELETE 操作次数
	SumTimerDelete uint64 `gorm:"column:sum_timer_delete"`
	MinTimerDelete uint64 `gorm:"column:min_timer_delete"`
	AvgTimerDelete uint64 `gorm:"column:avg_timer_delete"`
	MaxTimerDelete uint64 `gorm:"column:max_timer_delete"`

	// 审计字段
	CollectTime int64 `gorm:"column:collect_time"`
	CreatedAt   int64 `gorm:"column:created_at;autoCreateTime:milli"` // 创建时间（毫秒）
	UpdatedAt   int64 `gorm:"column:updated_at;autoUpdateTime:milli"` // 更新时间
	DeletedAt   int64 `gorm:"column:deleted_at"`                      // 删除时间
	Deleted     int8  `gorm:"column:deleted;default:0"`               // 删除标记 (0-未删)
}

type TableIoWaitsSummaryByTableList struct {
	Total int32                         `json:"total"`
	Items []*TableIoWaitsSummaryByTable `json:"items"`
}

func (TableIoWaitsSummaryByTable) TableName() string {
	return "table_io_waits_summary_by_table" // 自定义实际表名
}

type MysqlInnodbIndexStats struct {
	// 业务扩展字段
	ID           int64  `gorm:"primaryKey;column:id"` // 主键
	InstanceId   string `gorm:"column:instance_id"`   // 实例ID
	InstanceName string `gorm:"column:instance_name"`
	InstanceType string `gorm:"column:instance_type"` // 实例类型
	TenantId     string `gorm:"column:tenant_id"`     // 租户ID
	Region       string `gorm:"column:region"`        // 区域
	Source       string `gorm:"column:source"`        // 来源标识

	// 标准字段
	DatabaseName    string `gorm:"column:database_name"`    // use
	TableNameValue  string `gorm:"column:table_name_value"` // use
	IndexName       string `gorm:"column:index_name"`       // use
	StatName        string `gorm:"column:stat_name"`        // use
	StatValue       uint64 `gorm:"column:stat_value"`       // use
	SampleSize      uint64 `gorm:"column:sample_size"`
	StatDescription string `gorm:"column:stat_description"`

	// 审计字段
	CollectTime int64 `gorm:"column:collect_time"`
	CreatedAt   int64 `gorm:"column:created_at;autoCreateTime:milli"` // 创建时间（毫秒）
	UpdatedAt   int64 `gorm:"column:updated_at;autoUpdateTime:milli"` // 更新时间
	DeletedAt   int64 `gorm:"column:deleted_at"`                      // 删除时间
	Deleted     int8  `gorm:"column:deleted;default:0"`               // 删除标记 (0-未删)
}

type MysqlInnodbIndexStatsList struct {
	Total int32                    `json:"total"`
	Items []*MysqlInnodbIndexStats `json:"items"`
}

func (MysqlInnodbIndexStats) TableName() string {
	return "mysql_innodb_index_stats" // 自定义实际表名
}

type DataQuery struct {
	DbName     string
	TableName  string
	DataTime   int64
	AfterTime  int64
	BeforeTime int64
}
