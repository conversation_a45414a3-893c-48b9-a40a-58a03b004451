package dao

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

const TicketPrimaryWhereCase = "ticket_id=?"

const TenantIdWhereCase = "tenant_id = ?"

const InstanceIdWhereCase = "instance_id = ?"

const TicketTableName = "ticket_record"

const RecheckResultTableName = "ticket_pre_check_result"
const PreCheckResultTableName = "ticket_pre_check_detail"

const UserTableName = "dbw_user"

const InstanceTableName = "dbw_instance"

const InstancePrivilegeTableName = "instance_privilege"

const DatabasePrivilegeTableName = "database_privilege"

const TablePrivilegeTableName = "table_privilege"

const ColumnPrivilegeTableName = "column_privilege"

const (
	AdminUser  = "admin"
	NormalUser = "user"
	DbaUser    = "dba"
	OwnerUser  = "owner"
	Other      = "other"
)

type FlowInfo struct {
	TicketId      int64
	UserIds       string
	UserRole      string
	Status        int
	FlowStep      int
	Comment       string
	PreOperatorId string
}

type UserRole struct {
	Id       string
	TenantId string
	Role     string
}

type TicketUserInfo struct {
	UserId   string `gorm:"column:user_id;type:varchar(64);"`
	UserName string `gorm:"column:user_name;type:varchar(64);"`
}

type Extra struct {
	BatchSize           int64
	SleepTimeMs         int64
	IsEnableDelayCheck  int8
	SecondsBehindMaster int8
}

type BpmFlowInfo struct {
	TicketId       int64  `gorm:"column:ticket_id;type:BIGINT;NOT NULL"`
	WorkflowId     int64  `gorm:"column:workflow_id;type:BIGINT;"`
	CurrentUserIds string `gorm:"column:current_user_ids;type:varchar(64);"`
	CreateUserId   string `gorm:"column:create_user_id;type:varchar(64);"`
	FlowStep       int32  `gorm:"column:flow_step;type:INT;"`
	InstanceId     string `gorm:"column:instance_id;type:VARCHAR(64);"`
	TenantId       string `gorm:"column:tenant_id;type:VARCHAR(64);"`
}

type MapType map[string]interface{}

func (m *MapType) Scan(input interface{}) error {
	if input == nil {
		return nil
	}
	b, ok := input.([]byte)
	if !ok {
		return fmt.Errorf("value is not []byte, value: %v", input)
	}
	return json.Unmarshal(b, &m)
}

func (m MapType) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return json.Marshal(m)
}

type Ticket struct {
	TicketId            int64   `gorm:"column:ticket_id;type:BIGINT;NOT NULL"`
	FlowConfigId        int64   `gorm:"column:flow_config_id;type:BIGINT;"`
	WorkflowId          int64   `gorm:"column:workflow_id;type:BIGINT;"`
	ApprovalFlowId      int64   `gorm:"column:approval_flow_id"`
	TicketType          int8    `gorm:"column:ticket_type;type:TINYINT(1);"`
	TicketStatus        int8    `gorm:"column:ticket_status;type:TINYINT(1);"`
	FlowStep            int32   `gorm:"column:flow_step;type:INT;"`
	ExecuteType         int8    `gorm:"column:execute_type;type:TINYINT(1);"`
	CreatedFrom         string  `gorm:"column:created_from;type:VARCHAR(64);"`
	CreateTime          int64   `gorm:"column:create_time;type:BIGINT;"`
	UpdateTime          int64   `gorm:"column:update_time;type:BIGINT;"`
	CreateUserId        string  `gorm:"column:create_user_id;type:varchar(64);"`
	TenantId            string  `gorm:"column:tenant_id;type:VARCHAR(64);"`
	CurrentUserIds      string  `gorm:"column:current_user_ids;type:TEXT;"`
	CurrentUserRole     string  `gorm:"column:current_user_role;type:VARCHAR(64);"`
	AllOperatorId       string  `gorm:"column:all_operator_id;type:TEXT;"`
	InstanceType        string  `gorm:"column:instance_type;type:VARCHAR(64);"`
	InstanceId          string  `gorm:"column:instance_id;type:VARCHAR(64);"`
	SqlText             string  `gorm:"column:sql_text;type:LongText;"`
	Description         string  `gorm:"column:description;type:TEXT;"`
	DbName              string  `gorm:"column:db_name;type:VARCHAR(64);"`
	TaskId              string  `gorm:"column:task_id;type:VARCHAR(64);"`
	Progress            int32   `gorm:"column:progress;type:INT;"`
	ExecutableStartTime int64   `gorm:"column:executable_start_time;type:INT;"`
	ExecutableEndTime   int64   `gorm:"column:executable_end_time;type:INT;"`
	Extra               MapType `gorm:"column:extra;type:json"`
	DataArchiveConfig   string  `gorm:"column:data_archive_config;type:TEXT;"`
	Memo                string  `gorm:"column:memo;type:VARCHAR(1024);"`
	Title               string  `gorm:"column:title;type:VARCHAR(1024);"`
	TicketConfig        string  `gorm:"column:ticket_config;type:LONGTEXT;"`
	Submitted           int8    `gorm:"column:submitted;type:TINYINT(1);"`
	AffectedRows        string  `gorm:"column:affected_rows;type:VARCHAR(128);"`
	CreateUserName      string  `gorm:"column:create_user_name;type:varchar(256);"`
}

type PreCheckDetail struct {
	ItemDetail     *ItemDetail
	ItemSqlDetails []*ItemSqlDetail
}

type ItemDetail struct {
	Id         int64  `gorm:"column:id;type:bigint;"`
	TicketId   int64  `gorm:"column:ticket_id;type:BIGINT;NOT NULL;"`
	TenantId   string `gorm:"column:tenant_id;type:VARCHAR(64);"`
	ItemNameCn string `gorm:"column:item_name_cn;type:VARCHAR(512);"`
	ItemNameEn string `gorm:"column:item_name_en;type:VARCHAR(512);"`
	State      int32  `gorm:"column:state;type:INT;"`
	Result     string `gorm:"column:result;type:LONGTEXT;NOT NULL"`
	CreateAt   int64  `gorm:"column:create_at;type:BIGINT;"`
}

type ItemSqlDetail struct {
	PreCheckDetailId int64  `gorm:"column:pre_check_detail_id;type:BIGINT;NOT NULL;"`
	SqlText          string `gorm:"column:sql_text;type:LONGTEXT;NOT NULL"`
	Result           string `gorm:"column:result;type:LONGTEXT;NOT NULL"`
	CreateAt         int64  `gorm:"column:create_at;type:BIGINT;"`
}

type TicketRunningInfo struct {
	TicketId     int64  `gorm:"column:ticket_id;type:BIGINT;NOT NULL"`
	TicketStatus int8   `gorm:"column:ticket_status;type:TINYINT(1);"`
	Process      int32  `gorm:"column:progress;type:INT;"`
	Description  string `gorm:"column:description;type:TEXT;"`
	TicketConfig string `gorm:"column:ticket_config;type:TEXT;"`
	Submitted    int8   `gorm:"column:submitted;type:TINYINT(1);"`
}

type TicketPreCheckResult struct {
	TicketId int64  `gorm:"column:ticket_id;type:BIGINT;NOT NULL"`
	Item     string `gorm:"column:item;type:VARCHAR(32);NOT NULL"`
	Status   int8   `gorm:"column:status;type:TINYINT;"`
	Memo     string `gorm:"column:memo;type:TEXT;"`
}

type TicketPreCheckDetail struct {
	TicketId              int64  `gorm:"column:ticket_id;type:BIGINT;NOT NULL"`
	TenantId              string `gorm:"column:tenant_id;type:VARCHAR(64);NOT NULL"`
	SqlText               string `gorm:"column:sql_text;type:LONGTEXT;NOT NULL"`
	ExplainResult         string `gorm:"column:explain_result;type:LONGTEXT;NOT NULL"`
	SyntaxCheckResult     string `gorm:"column:syntax_check_result;type:LONGTEXT;NOT NULL"`
	PermissionCheckResult string `gorm:"column:permission_check_result;type:LONGTEXT;NOT NULL"`
	SecurityCheckResult   string `gorm:"column:security_check_result;type:LONGTEXT;NOT NULL"`
}

type UserInstancePrivilege struct {
	InstanceId string `gorm:"column:instance_id;type:VARCHAR(64);"`
}

type UserDatabasePrivilege struct {
	DbName string `gorm:"column:db_name;type:VARCHAR(64);"`
}

type UserTablePrivilege struct {
	DbName string `gorm:"column:db_name;type:VARCHAR(64);"`
	TbName string `gorm:"column:table_name;type:VARCHAR(64);"`
}

type UserColumnPrivilege struct {
	DbName     string `gorm:"column:db_name;type:VARCHAR(64);"`
	TbName     string `gorm:"column:table_name;type:VARCHAR(64);"`
	ColumnName string `gorm:"column:column_name;type:VARCHAR(64);"`
}

func (Ticket) TableName() string {
	return TicketTableName
}

func (TicketPreCheckResult) TableName() string {
	return RecheckResultTableName
}

func (TicketPreCheckDetail) TableName() string {
	return PreCheckResultTableName
}

func (ItemDetail) TableName() string {
	return "dbw_ticket_pre_check_detail"
}
func (ItemSqlDetail) TableName() string {
	return "dbw_ticket_pre_check_sql_detail"
}
