package dao

type DbwInstance struct {
	ID                   int64   `gorm:"primary_key"`
	InstanceId           string  `gorm:"column:instance_id"`
	InstanceType         string  `gorm:"column:instance_type"`
	InstanceName         string  `gorm:"column:instance_name"`
	TenantId             string  `gorm:"column:tenant_id"`
	Region               string  `gorm:"column:region"`
	Source               string  `gorm:"column:source"`
	Status               string  `gorm:"column:status"`
	Zone                 string  `gorm:"column:zone"`
	DBEngineVersion      string  `gorm:"column:db_engine_version"`
	SubInstanceType      string  `gorm:"column:sub_instance_type"`
	StorageType          string  `gorm:"column:storage_type"`
	InstanceCreateTime   string  `gorm:"column:instance_create_time"`
	CpuNum               int     `gorm:"column:cpu_num"`
	MemInGiB             float64 `gorm:"column:mem_in_gib"`
	NodeNumber           int     `gorm:"column:node_number"`
	ShardNumber          int     `gorm:"column:shard_number"`
	ProjectName          string  `gorm:"column:project_name"`
	Tags                 string  `gorm:"TYPE:json,column:tags"`
	DatabaseUser         string  `gorm:"column:database_user"`
	DatabasePassword     string  `gorm:"column:database_password"`
	Address              string  `gorm:"column:address"`
	OwnerUid             string  `gorm:"column:owner_uid"`
	DbaUid               string  `gorm:"column:dba_uid"`
	WorkflowTemplateId   int64   `gorm:"column:workflow_template_id"`
	SecurityGroupId      int64   `gorm:"column:security_group_id"`
	ApprovalFlowConfigId int64   `gorm:"column:approval_flow_config_id"`
	ControlMode          int     `gorm:"column:control_mode"`
	NoAuthMode           string  `gorm:"column:no_auth_mode"`
	CreatedAt            int64   `gorm:"column:created_at"`
	UpdatedAt            int64   `gorm:"column:updated_at"`
	DeletedAt            int64   `gorm:"column:deleted_at"`
	Extra                Map     `gorm:"TYPE:json,column:extra"`
	Deleted              int8    `gorm:"column:deleted"`
}

type DbwInstances struct {
	Total int32
	Items []*DbwInstance
}

func (DbwInstance) TableName() string {
	return "dbw_instance"
}

type ListQuery struct {
	InstanceId   string
	InstanceName string
	Mode         string
}
