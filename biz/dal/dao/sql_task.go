package dao

type SqlTask struct {
	Id            int32  `gorm:"column:id;type:INT(11);AUTO_INCREMENT;NOT NULL"`
	SqlTaskId     string `gorm:"column:sql_task_id;type:VARCHAR(64);NOT NULL"`
	TenantId      string `gorm:"column:tenant_id;type:VARCHAR(32);NOT NULL"`
	UserId        string `gorm:"column:user_id;type:VARCHAR(32);"`
	OrderId       string `gorm:"column:order_id;type:VARCHAR(255);"`
	SqlTaskType   string `gorm:"column:sql_task_type;type:VARCHAR(64);NOT NULL"`
	SqlTaskStatus string `gorm:"column:sql_task_status;type:VARCHAR(64);"`
	InstanceId    string `gorm:"column:instance_id;type:VARCHAR(255);"`
	InstanceType  string `gorm:"column:instance_type;type:VA<PERSON>HAR(32);"`
	DbName        string `gorm:"column:db_name;type:VA<PERSON>HAR(64);"`
	TbName        string `gorm:"column:tb_name;type:VARCHAR(64);"`
	ExecSql       string `gorm:"column:exec_sql;type:VARCHAR(5120);"`
	Comment       string `gorm:"column:comment;type:VARCHAR(512);"`
	ExecuteTime   int32  `gorm:"column:execute_time;type:INT(20);"`
	FinishTime    int32  `gorm:"column:finish_time;type:INT(20);"`
	DeadlineTime  int32  `gorm:"column:deadline_time;type:INT(20);"`
	Result        string `gorm:"column:result;type:TEXT;"`
	RunningInfo   string `gorm:"column:running_info;type:LONGTEXT;"`
	Progress      int32  `gorm:"column:progress;type:INT(11);"`
	Deleted       int32  `gorm:"column:deleted;type:INT(1);"`
	AffectedRows  int32  `gorm:"column:affected_rows;type:VARCHAR(128);"`
}

func (SqlTask) TableName() string {
	return "dbw_sql_task"
}
