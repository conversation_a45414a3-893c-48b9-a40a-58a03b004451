package dao

type InstanceHistory struct {
	ID           string `gorm:"primary_key"`
	InstanceId   string `gorm:"instance_id"`
	InstanceName string `gorm:"instance_name"`
	Psm          string `gorm:"psm"`
	InstanceType string `gorm:"instance_type"`
	RegionId     string `gorm:"region_id"`
	TenantId     string `gorm:"tenant_id"`
	UserId       string `gorm:"user_id"`
	CreateTime   int64  `gorm:"create_time"`
	UpdateTime   int64  `gorm:"update_time"`
	LoginTime    int64  `gorm:"login_time"`
}

func (InstanceHistory) TableName() string {
	return "dbw_instance_history"
}

type HistoryList struct {
	Total int32
	Items []*InstanceHistory
}

type HistoryQuery struct {
	InstanceId   string
	InstanceName string
	InstanceType string
	Keyword      string
	RegionId     string
	SortBy       string
	TenantId     string
}
