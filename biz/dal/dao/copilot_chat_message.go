package dao

type CopilotChatMessage struct {
	ID           string `gorm:"column:id"`
	ChatID       string `gorm:"column:chat_id"`
	TaskID       string `gorm:"column:task_id"`
	TenantID     string `gorm:"column:tenant_id"`
	UserID       string `gorm:"column:user_id"`
	Agent<PERSON><PERSON>    string `gorm:"column:agent_name"`
	Role         string `gorm:"column:role"`
	Content      string `gorm:"column:content"`
	Tool<PERSON>alls    string `gorm:"column:tool_calls"`
	ToolCallID   string `gorm:"column:tool_call_id"`
	FunctionName string `gorm:"column:function_name"`
	ExtraInfo    string `gorm:"column:extra_info"`
	RatedType    string `gorm:"column:rated_type"`
	IsDelete     int8   `gorm:"column:is_delete"`
	RatedTime    int64  `gorm:"column:rated_time"`
	CreateTime   int64  `gorm:"column:create_time"`
	UpdateTime   int64  `gorm:"column:update_time"`
}

type ExtraInfo struct {
	InstanceID string
}

type CopilotChatMessages struct {
	CopilotChatMessages []*CopilotChatMessage
	Total               int64
}

func (CopilotChatMessage) TableName() string {
	return "dbw_copilot_chat_message"
}
