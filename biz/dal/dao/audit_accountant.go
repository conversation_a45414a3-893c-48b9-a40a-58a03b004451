package dao

type AuditAccountant struct {
	ID             int64  `gorm:"primary_key"`
	InstanceID     string `gorm:"column:instance_id"`
	AccountantTime int64  `gorm:"column:accountant_time"`
	TenantID       string `gorm:"column:tenant_id"`
	ProductType    string `gorm:"column:product_type"`
	ChargeItemCode string `gorm:"column:charge_item_code"`
	TlsUsage       int64  `gorm:"column:tls_usage"`
	Status         int32  `gorm:"column:status"`
	Deleted        int32  `gorm:"column:deleted"`
}

func (AuditAccountant) TableName() string {
	return "dbw_audit_accountant"
}
