package dao

type InspectionReport struct {
	TaskID              int64   `gorm:"column:task_id;primary_key"`
	InstanceType        string  `gorm:"column:instance_type"`
	InstanceId          string  `gorm:"column:instance_id"`
	InstanceSpec        string  `gorm:"column:instance_specification"`
	InstanceVersion     string  `gorm:"column:instance_version"`
	InspectionStartTime int64   `gorm:"column:inspection_start_time"`
	InspectionEndTime   int64   `gorm:"column:inspection_end_time"`
	MaxCpuUsage         float64 `gorm:"column:max_cpu_usage"`
	MinCpuUsage         float64 `gorm:"column:min_cpu_usage"`
	AvgCpuUsage         float64 `gorm:"column:avg_cpu_usage"`
	MaxMemUsage         float64 `gorm:"column:max_mem_usage"`
	MinMemUsage         float64 `gorm:"column:min_mem_usage"`
	AvgMemUsage         float64 `gorm:"column:avg_mem_usage"`
	MaxDiskUsage        float64 `gorm:"column:max_disk_usage"`
	MinDiskUsage        float64 `gorm:"column:min_disk_usage"`
	AvgDiskUsage        float64 `gorm:"column:avg_disk_usage"`
	MaxConnUsage        float64 `gorm:"column:max_conn_usage"`
	MinConnUsage        float64 `gorm:"column:min_conn_usage"`
	AvgConnUsage        float64 `gorm:"column:avg_conn_usage"`
	MaxConnected        int64   `gorm:"column:max_connected"`
	ThreadConnected     int64   `gorm:"column:thread_connected"`
	ThreadRunning       int64   `gorm:"column:thread_running"`
}

type InspectionReportInfo struct {
	Total            int64
	InspectionReport []*InspectionReport
}

func (InspectionReport) TableName() string {
	return "dbw_inspection_report"
}
