package dao

type DbwLogCollector struct {
	ID                int64  `gorm:"primary_key"`
	ClusterName       string `gorm:"column:cluster_name"`
	NodePool          string `gorm:"column:node_pool;type:text"`
	LogCollectorImage string `gorm:"column:log_collector_image"`
	InstanceType      string `gorm:"column:instance_type"`
	CreatedAt         int64  `gorm:"column:created_at"`
	UpdatedAt         int64  `gorm:"column:updated_at"`
	DeletedAt         int64  `gorm:"column:deleted_at"`
	Deleted           int8   `gorm:"column:deleted"`
}

type DbwLogCollectors struct {
	Total int32
	Items []*DbwLogCollector
}

func (DbwLogCollector) TableName() string {
	return "dbw_log_collector"
}
