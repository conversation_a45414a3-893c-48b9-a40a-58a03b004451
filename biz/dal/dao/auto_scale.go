package dao

const DbwAutoScaleRulesTableName = "dbw_auto_scale_rules"

const DbwAutoScaleEventTableName = "dbw_auto_scale_event"

type DbwAutoScaleRule struct {
	RuleId            int64   `gorm:"column:rule_id;type:BIGINT;AUTO_INCREMENT;NOT NULL"`
	InstanceId        string  `gorm:"column:instance_id;type:VARCHAR(64);"`
	InstanceType      string  `gorm:"column:instance_type;type:VARCHAR(16);"`
	ScaleTarget       string  `gorm:"column:scale_target;type:VARCHAR(16);"`
	TenantId          string  `gorm:"column:tenant_id;type:VARCHAR(16);"`
	RegionId          string  `gorm:"column:region_id;type:VARCHAR(64);NOT NULL"`
	ScalingType       int8    `gorm:"column:scaling_type;type:TINYINT;"`
	ScalingThreshold  float64 `gorm:"column:scaling_threshold;type:INT;"`
	Enable            int8    `gorm:"column:enable;type:TINYINT;"`
	ObservationWindow int32   `gorm:"column:observation_window;type:INT;"`
	CloudAlarmId      string  `gorm:"column:cloud_alarm_id;type:VARCHAR(64);"`
	CreateTime        int64   `gorm:"column:create_time;type:BIGINT;"`
	UpdateTime        int64   `gorm:"column:update_time;type:BIGINT;"`
	UpdateUser        string  `gorm:"column:update_user;type:VARCHAR(16);"`
	Deleted           int8    `gorm:"column:deleted;type:TINYINT;"`
	ScalingLimit      string  `gorm:"column:scaling_limit;type:VARCHAR(64);"`
}

type DbwAutoScaleEvent struct {
	EventId           int64  `gorm:"column:event_id;type:BIGINT;NOT NULL"`
	RuleId            int64  `gorm:"column:rule_id;type:BIGINT;NOT NULL"`
	InstanceId        string `gorm:"column:instance_id;type:VARCHAR(64);"`
	TenantId          string `gorm:"column:tenant_id;type:VARCHAR(64);"`
	ScalingType       int8   `gorm:"column:scaling_type;type:TINYINT;"`
	CreateTime        int64  `gorm:"column:create_time;type:BIGINT;"`
	StartTime         int64  `gorm:"column:start_time;type:BIGINT;"`
	EndTime           int64  `gorm:"column:end_time;type:BIGINT;"`
	BeforeValue       int32  `gorm:"column:before_value;type:INT;"`
	TargetUtilization string `gorm:"column:target_utilization;type:VARCHAR(16);"`
	AfterValue        int32  `gorm:"column:after_value;type:INT;"`
	Status            int8   `gorm:"column:status;type:TINYINT;"`
	Memo              string `gorm:"column:memo;type:TEXT;NOT NULL"`
	Deleted           int8   `gorm:"column:deleted;type:TINYINT;"`
	BeforeMetric      string `gorm:"column:before_metric;type:VARCHAR(64);"`
	AfterMetric       string `gorm:"column:after_metric;type:VARCHAR(64);"`
}

func (DbwAutoScaleRule) TableName() string {
	return DbwAutoScaleRulesTableName
}

func (DbwAutoScaleEvent) TableName() string {
	return DbwAutoScaleEventTableName
}

type DbwAutoScaleEventInfo struct {
	Total              int64
	DbwAutoScaleEvents []*DbwAutoScaleEvent
}
