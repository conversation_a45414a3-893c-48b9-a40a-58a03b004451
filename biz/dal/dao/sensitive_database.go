package dao

type SensitiveDatabase struct {
	ID           int64   `gorm:"primary_key"`
	TenantID     string  `gorm:"column:tenant_id"`
	InstanceId   string  `gorm:"column:instance_id"`
	InstanceType string  `gorm:"column:instance_type"`
	LinkType     string  `gorm:"column:link_type"`
	Name         string  `gorm:"column:name"`
	Comment      *string `gorm:"column:comment"`
	Deleted      int8    `gorm:"column:deleted"`
}

func (SensitiveDatabase) TableName() string {
	return "dbw_security_sensitive_database"
}
