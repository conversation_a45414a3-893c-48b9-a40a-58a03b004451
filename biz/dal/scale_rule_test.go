package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
)

func mockScaleRuleDAL() ScaleRuleDAL {
	return NewScaleRuleDAL(&mockDBProvider{})
}

func TestScaleRuleCreate(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Clauses).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleRuleDAL()
	err := dal.Create(context.Background(), &dao.DbwAutoScaleRule{})
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.Create(context.Background(), &dao.DbwAutoScaleRule{})
	assert.Nil(t, err)
}

func TestGetRuleById(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleRuleDAL()
	_, err := dal.GetRuleById(context.Background(), 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.GetRuleById(context.Background(), 1)
	assert.Nil(t, err)
}

func TestGetRules(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).Scan).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleRuleDAL()
	_, err := dal.GetRules(context.Background(), "", "")
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.GetRules(context.Background(), "", "")
	assert.Nil(t, err)
}

func TestUpdateRuleById(t *testing.T) {
	//mockRes := &gorm.DB{}
	//mock1 := mockey.Mock((*gorm.DB).Exec).Return(mockRes).Build()
	//defer mock1.UnPatch()
	//mockRes.Error = fmt.Errorf("test")
	//mock2 := mockey.Mock((*gorm.DB).ToSQL).Return("").Build()
	//defer mock2.UnPatch()
	//dal := mockScaleRuleDAL()
	//err := dal.UpdateRuleById(context.Background(), &dao.DbwAutoScaleRule{})
	////	assert.NotNil(t, err)
	//mockRes.Error = nil
	//err = dal.UpdateRuleById(context.Background(), &dao.DbwAutoScaleRule{})
	//assert.Nil(t, err)
}

func TestGetRuleByInstanceId(t *testing.T) {
	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mockRes := &gorm.DB{}
	mock2 := mockey.Mock((*gorm.DB).First).Return(mockRes).Build()
	defer mock2.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleRuleDAL()
	_, err := dal.GetRuleByInstanceId(context.Background(), "", "", 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	_, err = dal.GetRuleByInstanceId(context.Background(), "", "", 1)
	assert.Nil(t, err)
}

func TestDeleteRuleByInstanceId(t *testing.T) {
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Exec).Return(mockRes).Build()
	defer mock1.UnPatch()
	mockRes.Error = fmt.Errorf("test")

	dal := mockScaleRuleDAL()
	err := dal.DeleteRuleByInstanceId(context.Background(), "", "", 1)
	assert.NotNil(t, err)
	mockRes.Error = nil
	err = dal.DeleteRuleByInstanceId(context.Background(), "", "", 1)
	assert.Nil(t, err)
}
