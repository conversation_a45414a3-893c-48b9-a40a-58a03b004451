package dal

import (
	"fmt"
	"reflect"
	"testing"
)

func Test_generateConditions(t *testing.T) {
	type args struct {
		instanceId string
		tenantId   string
		taskType   string
		keyWords   string
		userId     string
		taskId     string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 []interface{}
	}{
		{
			name: "",
			args: args{
				instanceId: "x1",
				tenantId:   "x2",
				taskType:   "x3",
				keyWords:   "x4",
				userId:     "x5",
				taskId:     "x6",
			},
			want:  "tenant_id=? and deleted=0 and from_type=0 and instance_id=? and type=? and (db like ? or table_list like ?) and user_id=? and id=? ",
			want1: []interface{}{"x2", "x1", "x3", "%x4%", "%x4%", "x5", "x6"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := generateConditions(tt.args.instanceId, tt.args.tenantId, tt.args.taskType, tt.args.keyWords, tt.args.userId, tt.args.taskId, "")
			fmt.Println(got)
			if got != tt.want {
				t.Errorf("generateConditions() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("generateConditions() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
