package dal

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

type DbwUserDAL interface {
	Create(ctx context.Context, User *dao.DbwUser) error
	CreateBatch(ctx context.Context, Users []*dao.DbwUser) error
	SaveBatch(ctx context.Context, Users []*dao.DbwUser) error
	List(ctx context.Context, TenantId string, UserName string, UserId string, RoleType []string, limit int32, offset int32) (*dao.DbwUsers, error)
	Update(ctx context.Context, UserId string, TenantId string, Role string, MaxExecuteCount int32, MaxResultCount int32, MaxExecuteExpiredTime int32, MaxResultExpiredTime int32, MaxExecuteDuration string, MaxResultDuration string, FeishuWebhook string, userGroupType string, userState string) error
	UpdateCurCount(ctx context.Context, UserId string, TenantId string, CurExecuteCount int32, CurResultCount int32) error
	ResetCurrentCount(ctx context.Context, CurrentExecuteCount int32, CurrentResultCount int32, UserID, TenantID string) error
	UpdateState(ctx context.Context, TenantId string, UserId string, State string) error
	UpdateUserGroupType(ctx context.Context, TenantId string, UserId string, UserGroupType string) error
	Delete(ctx context.Context, UerId string, TenantId string) error
	Get(ctx context.Context, UserId string, TenantId string) (*dao.DbwUser, error)
	GetByName(ctx context.Context, UserName string, TenantId string) (*dao.DbwUser, error)
	GetUserByName(ctx context.Context, UserName string, TenantId string) ([]*dao.DbwUser, error)
	GetIncludeDeleted(ctx context.Context, UserId string, TenantId string) ([]*dao.DbwUser, error)
	GetDeletedReason(ctx context.Context, UserId string, TenantId string) (*dao.DbwUser, error)
}

type DbwUserGroupDAL interface {
	Create(ctx context.Context, Group *dao.DbwUserGroup) error
	Save(ctx context.Context, Group *dao.DbwUserGroup) error
	Update(ctx context.Context, TenantId string, Group *dao.DbwUserGroup) error
	Delete(ctx context.Context, TenantId string, GroupId string) error
	Get(ctx context.Context, TenantId string, GroupId string) (*dao.DbwUserGroup, error)
	List(ctx context.Context, TenantId string, limit int32, offset int32, GroupName string, GroupId string, Memo string, GroupList []string) (*dao.DbwUserGroups, error)
	ResetCurrentCount(ctx context.Context, CurrentExecuteCount int32, CurrentResultCount int32, GroupId, TenantID string) error
}

type ProjectGroupRelationDAL interface {
	Create(ctx context.Context, Group *dao.ProjectGroupRelation) error
	Save(ctx context.Context, Group *dao.ProjectGroupRelation) error
	Update(ctx context.Context, TenantId string, Group *dao.ProjectGroupRelation) error
	Delete(ctx context.Context, TenantId string, GroupId int64) error
	Get(ctx context.Context, TenantId string, ProjectGroupRelationId string) (*dao.ProjectGroupRelation, error)
	GetByGroup(ctx context.Context, TenantId string, GroupId int64) (*dao.ProjectGroupRelation, error)
	ListAll(ctx context.Context, TenantId string) (*dao.ProjectGroupRelations, error)
}

type UserGroupRelationDAL interface {
	CreateBatch(ctx context.Context, Groups []*dao.UserGroupRelation) error
	SaveBatch(ctx context.Context, Groups []*dao.UserGroupRelation) error
	Update(ctx context.Context, TenantId string, Group *dao.UserGroupRelation) error
	DeleteByCondition(ctx context.Context, TenantId string, GroupId string, UserId string) error
	Get(ctx context.Context, TenantId string, UserGroupRelationId string) (*dao.UserGroupRelation, error)
	GetByUserAndGroup(ctx context.Context, TenantId string, UserId string, groupId int64) (*dao.UserGroupRelation, error)
	ListAllByUser(ctx context.Context, TenantId string, UserId string) (*dao.UserGroupRelations, error)
	ListAllByGroup(ctx context.Context, TenantId string, GroupId string, NotInUsers []string) (*dao.UserGroupRelations, error)
}

func NewDbwUserDAL(dbPro DBProvider) DbwUserDAL {
	return &DbwUserDal{dbPro: dbPro}
}

type DbwUserDal struct {
	dbPro DBProvider
}

func NewDbwUserGroupDAL(dbPro DBProvider) DbwUserGroupDAL {
	return &DbwUserGroupDal{dbPro: dbPro}
}

type DbwUserGroupDal struct {
	dbPro DBProvider
}

func NewProjectGroupRelation(dbPro DBProvider) ProjectGroupRelationDAL {
	return &ProjectGroupRelationDal{dbPro: dbPro}
}

type ProjectGroupRelationDal struct {
	dbPro DBProvider
}

func NewUserGroupRelation(dbPro DBProvider) UserGroupRelationDAL {
	return &UserGroupRelationDal{dbPro: dbPro}
}

type UserGroupRelationDal struct {
	dbPro DBProvider
}

func (dal *DbwUserDal) Create(ctx context.Context, User *dao.DbwUser) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(User).Error
	return err
}

func (dal *DbwUserDal) CreateBatch(ctx context.Context, Users []*dao.DbwUser) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(Users).Error
	return err
}

func (dal *DbwUserDal) SaveBatch(ctx context.Context, Users []*dao.DbwUser) error {
	if Users == nil {
		return nil
	}
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Save(Users).Error
	return err
}

func (dal *DbwUserDal) List(ctx context.Context, TenantId string, UserName string, UserId string, RoleType []string, limit int32, offset int32) (*dao.DbwUsers, error) {
	var (
		Users          []*dao.DbwUser
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserName != "" {
		baseQuery += "and user_name like ? "
		baseConditions = append(baseConditions, "%"+UserName+"%")
	}
	if UserId != "" {
		baseQuery += "and user_id like ? "
		baseConditions = append(baseConditions, "%"+UserId+"%")
	}
	if len(RoleType) > 0 {
		baseQuery += "and role in (?) "
		baseConditions = append(baseConditions, RoleType)
	}
	if err := db.Model(Users).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Find(&Users).Error; err != nil {
		return nil, err
	}
	ret := &dao.DbwUsers{
		Total: int32(total),
		Users: Users,
	}
	return ret, nil

}

func (dal *DbwUserDal) Update(ctx context.Context, UserId string, TenantId string, Role string, MaxExecuteCount int32, MaxResultCount int32, MaxExecuteExpiredTime int32, MaxResultExpiredTime int32, MaxExecuteDuration string, MaxResultDuration string, FeishuWebhook string, userGroupType string, userState string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DbwUser{}).Where("user_id=? and tenant_id=? and deleted=0", UserId, TenantId).Updates(map[string]interface{}{
		"Role":                  Role,
		"CurExecuteCount":       0,
		"CurResultCount":        0,
		"MaxExecuteCount":       MaxExecuteCount,
		"MaxResultCount":        MaxResultCount,
		"MaxExecuteDuration":    MaxExecuteDuration,
		"MaxResultDuration":     MaxResultDuration,
		"MaxExecuteExpiredTime": MaxExecuteExpiredTime,
		"MaxResultExpiredTime":  MaxResultExpiredTime,
		"FeishuWebhook":         FeishuWebhook,
		"GroupType":             userGroupType,
		"State":                 userState,
		"UpdatedAt":             now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update DbwUser error %v", err)
	}
	return nil
}

func (dal *DbwUserDal) UpdateCurCount(ctx context.Context, UserId string, TenantId string, CurExecuteCount int32, CurResultCount int32) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DbwUser{}).Where("user_id=? and tenant_id=? and deleted=0", UserId, TenantId).Updates(map[string]interface{}{
		"CurResultCount":  CurResultCount,
		"CurExecuteCount": CurExecuteCount,
		"UpdatedAt":       now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("update DbwUser error %v", err)
	}
	return nil
}

func (dal *DbwUserDal) ResetCurrentCount(ctx context.Context, CurrentExecuteCount int32, CurrentResultCount int32, UserID, TenantID string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DbwUser{}).Where("user_id=? and tenant_id=? and deleted=?", UserID, TenantID, 0).Updates(map[string]interface{}{
		"CurExecuteCount": CurrentExecuteCount,
		"CurResultCount":  CurrentResultCount,
		"UpdatedAt":       now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("reset dbwUser error %v", err)
	}
	return nil
}
func (dal *DbwUserDal) Delete(ctx context.Context, UserId string, TenantId string) error {
	// TODO 开事务更新工单记录表，将删除用户从当前处理人中删除
	// explain update ticket_record set current_user_ids = replace(current_user_ids, ?, ""), update_time = ? where tenant_id = ? and current_user_ids like ;
	// 拼接userId
	db := dal.dbPro.GetMetaDB(ctx)
	var err error
	db.Begin()
	defer func() {
		if err != nil {
			db.Rollback()
			return
		}
		db.Commit()
	}()
	err = db.Model(&dao.DbwUser{}).Where("tenant_id=? and user_id=? and deleted=0", TenantId, UserId).Updates(map[string]interface{}{
		"DeletedAt": time.Now().Unix(),
		"Deleted":   1,
	}).Error

	sqlStr := "update " + dao.TicketTableName + " set current_user_ids = replace(current_user_ids, ?, ''), update_time = ? where tenant_id = ? and current_user_ids like ? ;"
	err = db.Exec(sqlStr, UserId, time.Now().UnixMilli(), TenantId, "%"+UserId+"%").Error
	return err
}

func (dal *DbwUserDal) UpdateState(ctx context.Context, TenantId string, UserId string, State string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	var err error
	now := time.Now().Unix()
	switch State {
	case model.UserState_NORMAL.String(), model.UserState_DISABLE.String():
		err = db.Model(&dao.DbwUser{}).Where("tenant_id=? and user_id=? and deleted=0", TenantId, UserId).Updates(map[string]interface{}{
			"State":     State,
			"UpdatedAt": now,
		}).Error
	case model.UserState_DELETEDBYGROUP.String():
		err = db.Model(&dao.DbwUser{}).Where("tenant_id=? and user_id=? and deleted=0", TenantId, UserId).Updates(map[string]interface{}{
			"State":     State,
			"UpdatedAt": now,
			"DeletedAt": now,
			"Deleted":   1,
		}).Error
	}
	return err
}

func (dal *DbwUserDal) UpdateUserGroupType(ctx context.Context, TenantId string, UserId string, UserGroupType string) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Model(&dao.DbwUser{}).Where("tenant_id=? and user_id=? and deleted=0", TenantId, UserId).Updates(map[string]interface{}{
		"GroupType": UserGroupType,
		"UpdatedAt": time.Now().Unix(),
	}).Error
	return err
}

func (dal *DbwUserDal) Get(ctx context.Context, UserId string, TenantId string) (*dao.DbwUser, error) {
	var ret dao.DbwUser
	db := dal.dbPro.GetMetaDB(ctx)
	query := db.Where("user_id=?  and deleted=0", UserId)
	if TenantId != "" {
		query = query.Where(`tenant_id=?`, TenantId)
	}
	err := query.First(&ret).Error
	return &ret, err
}

func (dal *DbwUserDal) GetByName(ctx context.Context, UserName string, TenantId string) (*dao.DbwUser, error) {
	var ret dao.DbwUser
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("user_name=? and tenant_id=? and deleted=0", UserName, TenantId).First(&ret).Error
	return &ret, err
}

func (dal *DbwUserDal) GetUserByName(ctx context.Context, UserName string, TenantId string) ([]*dao.DbwUser, error) {
	var ret []*dao.DbwUser
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("user_name=? and tenant_id=? and deleted=0", UserName, TenantId).Find(&ret).Error
	return ret, err
}

func (dal *DbwUserDal) GetIncludeDeleted(ctx context.Context, UserId string, TenantId string) ([]*dao.DbwUser, error) {
	var ret []*dao.DbwUser
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("user_id=? and tenant_id=?", UserId, TenantId).Find(&ret).Error
	return ret, err
}

func (dal *DbwUserDal) GetDeletedReason(ctx context.Context, UserId string, TenantId string) (*dao.DbwUser, error) {
	var ret dao.DbwUser
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("user_id=? and tenant_id=? and deleted=1", UserId, TenantId).Group("deleted_at").First(&ret).Error
	return &ret, err
}

func (dal *DbwUserGroupDal) Create(ctx context.Context, Group *dao.DbwUserGroup) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(Group).Error
	return err
}

func (dal *DbwUserGroupDal) Save(ctx context.Context, Group *dao.DbwUserGroup) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Save(Group).Error
	return err
}
func (dal *DbwUserGroupDal) Update(ctx context.Context, TenantId string, Group *dao.DbwUserGroup) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	return db.Model(&dao.DbwUserGroup{}).Where("deleted=0 and tenant_id=? and group_id=?", TenantId, Group.ID).Updates(map[string]interface{}{
		"group_name":               Group.GroupName,
		"memo":                     Group.Memo,
		"role":                     Group.Role,
		"max_execute_count":        Group.MaxExecuteCount,
		"max_execute_expired_time": Group.MaxExecuteExpiredTime,
		"max_execute_duration":     Group.MaxExecuteDuration,
		"max_result_expired_time":  Group.MaxResultExpiredTime,
		"max_result_duration":      Group.MaxResultDuration,
		"max_result_count":         Group.MaxResultCount,
		"by_project":               Group.ByProject,
		"sync":                     Group.Sync,
		"updated_at":               now,
	}).Error
}

func (dal *DbwUserGroupDal) Delete(ctx context.Context, TenantId string, GroupId string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	return db.Model(&dao.DbwUserGroup{}).Where("deleted=0 and tenant_id=? and id=?", TenantId, GroupId).Updates(map[string]interface{}{
		"updated_at": now,
		"deleted_at": now,
		"deleted":    1,
	}).Error
}

func (dal *DbwUserGroupDal) Get(ctx context.Context, TenantId string, GroupId string) (*dao.DbwUserGroup, error) {
	var ret dao.DbwUserGroup
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("deleted=0 and tenant_id=? and id=?", TenantId, GroupId).First(&ret).Error
	return &ret, err
}

func (dal *DbwUserGroupDal) List(ctx context.Context, TenantId string, limit int32, offset int32, GroupName string, GroupId string, Memo string, GroupList []string) (*dao.DbwUserGroups, error) {
	var (
		Groups         []*dao.DbwUserGroup
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if GroupName != "" {
		baseQuery += "and group_name like ? "
		baseConditions = append(baseConditions, "%"+GroupName+"%")
	}
	if GroupId != "" {
		baseQuery += "and id like ? "
		baseConditions = append(baseConditions, "%"+GroupId+"%")
	}
	if Memo != "" {
		baseQuery += "and memo like ? "
		baseConditions = append(baseConditions, "%"+Memo+"%")
	}
	if len(GroupList) != 0 {
		baseQuery += "and id in (?) "
		baseConditions = append(baseConditions, GroupList)
	}

	if err := db.Model(Groups).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Offset(int(offset)).Limit(int(limit)).Find(&Groups).Error; err != nil {
		return nil, err
	}
	ret := &dao.DbwUserGroups{
		Total:  int32(total),
		Groups: Groups,
	}
	return ret, nil
}

func (dal *DbwUserGroupDal) ResetCurrentCount(ctx context.Context, CurrentExecuteCount int32, CurrentResultCount int32, GroupId, TenantID string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	res := db.Model(&dao.DbwUserGroup{}).Where("id=? and tenant_id=? and deleted=?", GroupId, TenantID, 0).Updates(map[string]interface{}{
		"CurExecuteCount": CurrentExecuteCount,
		"CurResultCount":  CurrentResultCount,
		"UpdatedAt":       now,
	})
	if err := res.Error; err != nil {
		return fmt.Errorf("reset dbwUserGroup error %v", err)
	}
	return nil
}

func (dal *ProjectGroupRelationDal) Create(ctx context.Context, Group *dao.ProjectGroupRelation) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(Group).Error
	return err
}

func (dal *ProjectGroupRelationDal) Save(ctx context.Context, Group *dao.ProjectGroupRelation) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Save(Group).Error
	return err
}

func (dal *ProjectGroupRelationDal) Delete(ctx context.Context, TenantId string, GroupId int64) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	return db.Model(&dao.ProjectGroupRelation{}).Where("deleted=0 and tenant_id=? and group_id=?", TenantId, GroupId).Updates(map[string]interface{}{
		"updated_at": now,
		"deleted_at": now,
		"deleted":    1,
	}).Error
}

func (dal *ProjectGroupRelationDal) Get(ctx context.Context, TenantId string, ProjectGroupRelationId string) (*dao.ProjectGroupRelation, error) {
	var ret dao.ProjectGroupRelation
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("deleted=0 and tenant_id=? and id=?", TenantId, ProjectGroupRelationId).First(&ret).Error
	return &ret, err
}

func (dal *ProjectGroupRelationDal) GetByGroup(ctx context.Context, TenantId string, GroupId int64) (*dao.ProjectGroupRelation, error) {
	var ret dao.ProjectGroupRelation
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("deleted=0 and tenant_id=? and group_id=?", TenantId, GroupId).First(&ret).Error
	return &ret, err
}

func (dal *ProjectGroupRelationDal) Update(ctx context.Context, TenantId string, Group *dao.ProjectGroupRelation) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	return db.Model(&dao.ProjectGroupRelation{}).Where("deleted=0 and tenant_id=? and id=?", TenantId, Group.ID).Updates(map[string]interface{}{
		"group_id":   Group.GroupID,
		"project":    Group.Project,
		"iam_groups": Group.IAMGroups,
		"iam_user":   Group.IAMUser,
		"updated_at": now,
	}).Error
}

func (dal *ProjectGroupRelationDal) ListAll(ctx context.Context, TenantId string) (*dao.ProjectGroupRelations, error) {
	var (
		Groups         []*dao.ProjectGroupRelation
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "deleted=0 "
	if TenantId != "" {
		baseQuery += "and tenant_id = ? "
		baseConditions = append(baseConditions, TenantId)
	}
	if err := db.Model(Groups).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Find(&Groups).Error; err != nil {
		return nil, err
	}
	ret := &dao.ProjectGroupRelations{
		Total:                 int32(total),
		ProjectGroupRelations: Groups,
	}
	return ret, nil
}

func (dal *UserGroupRelationDal) CreateBatch(ctx context.Context, Groups []*dao.UserGroupRelation) error {
	if Groups == nil {
		return nil
	}
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(Groups).Error
	return err
}

func (dal *UserGroupRelationDal) SaveBatch(ctx context.Context, Groups []*dao.UserGroupRelation) error {
	if Groups == nil {
		return nil
	}
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Save(Groups).Error
	return err
}

func (dal *UserGroupRelationDal) DeleteByCondition(ctx context.Context, TenantId string, GroupId string, UserId string) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	var baseConditions []interface{}
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if UserId != "" {
		baseQuery += "and user_id=? "
		baseConditions = append(baseConditions, UserId)
	}
	if GroupId != "" {
		baseQuery += "and group_id=? "
		baseConditions = append(baseConditions, GroupId)
	}
	return db.Model(&dao.UserGroupRelation{}).Where(baseQuery, baseConditions...).Updates(map[string]interface{}{
		"updated_at": now,
		"deleted_at": now,
		"deleted":    1,
	}).Error
}

func (dal *UserGroupRelationDal) Get(ctx context.Context, TenantId string, UserGroupRelationId string) (*dao.UserGroupRelation, error) {
	var ret dao.UserGroupRelation
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("deleted=0 and tenant_id=? and id=?", TenantId, UserGroupRelationId).First(&ret).Error
	return &ret, err
}

func (dal *UserGroupRelationDal) GetByUserAndGroup(ctx context.Context, TenantId string, UserId string, groupId int64) (*dao.UserGroupRelation, error) {
	var ret dao.UserGroupRelation
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Where("deleted=0 and tenant_id=? and user_id=? and group_id=?", TenantId, UserId, groupId).First(&ret).Error
	return &ret, err
}

func (dal *UserGroupRelationDal) Update(ctx context.Context, TenantId string, Group *dao.UserGroupRelation) error {
	now := time.Now().Unix()
	db := dal.dbPro.GetMetaDB(ctx)
	return db.Model(&dao.UserGroupRelation{}).Where("deleted=0 and tenant_id=? and id=?", TenantId, Group.ID).Updates(map[string]interface{}{
		"group_id":   Group.GroupID,
		"user_id":    Group.UserID,
		"updated_at": now,
	}).Error
}

func (dal *UserGroupRelationDal) ListAllByUser(ctx context.Context, TenantId string, UserId string) (*dao.UserGroupRelations, error) {
	var (
		Groups         []*dao.UserGroupRelation
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "deleted=0 "
	if TenantId != "" {
		baseQuery += "and tenant_id =? "
		baseConditions = append(baseConditions, TenantId)
	}
	if UserId != "" {
		baseQuery += "and user_id = ? "
		baseConditions = append(baseConditions, UserId)
	}
	if err := db.Model(Groups).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Find(&Groups).Error; err != nil {
		return nil, err
	}
	ret := &dao.UserGroupRelations{
		Total:          int32(total),
		GroupRelations: Groups,
	}
	return ret, nil
}

func (dal *UserGroupRelationDal) ListAllByGroup(ctx context.Context, TenantId string, GroupId string, NotInUsers []string) (*dao.UserGroupRelations, error) {
	var (
		Groups         []*dao.UserGroupRelation
		total          int64
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and deleted=0 "
	baseConditions = append(baseConditions, TenantId)
	if GroupId != "" {
		baseQuery += "and group_id = ? "
		baseConditions = append(baseConditions, GroupId)
	}
	if len(NotInUsers) > 0 {
		baseQuery += "and user_id not in (?) "
		baseConditions = append(baseConditions, NotInUsers)
	}

	if err := db.Model(Groups).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Find(&Groups).Error; err != nil {
		return nil, err
	}
	ret := &dao.UserGroupRelations{
		Total:          int32(total),
		GroupRelations: Groups,
	}
	return ret, nil
}
