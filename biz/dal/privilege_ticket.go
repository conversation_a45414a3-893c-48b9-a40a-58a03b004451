package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	. "gorm.io/gorm"
)

type PriTicketSearchParam struct {
	SortBy                string
	OrderBy               string
	UserId                string
	UserName              string
	TicketID              string
	PrivilegeResourceType string
	PriTicketListType     string
	PriTicketStatus       string
	PriTicketAction       string
	ResourceType          string
	ResourceName          string
	ResourceInstanceId    string
	UserMemo              string
	Title                 string
}

type PrivilegeTicketDAL interface {
	Create(ctx context.Context, ticket *dao.PrivilegeTicket) error
	BulkCreate(ctx context.Context, his []*dao.PrivilegeTicketResource) error
	List(ctx context.Context, userId, tenantID string, searchParam *PriTicketSearchParam, pageNumber, pageSize int32) (*dao.DbwPrivilegeTickets, error)
	Update(ctx context.Context, ID string, data map[string]interface{}) error
	Get(ctx context.Context, ID, tenantID string) (*dao.PrivilegeTicket, error)
	GetResourcesByTicketID(ctx context.Context, ticketID string) ([]*dao.PrivilegeTicketResource, error)
}

func NewPrivilegeTicketDAL(dbPro DBProvider) PrivilegeTicketDAL {
	return &PrivilegeTicketDal{dbPro: dbPro}
}

type PrivilegeTicketDal struct {
	dbPro DBProvider
}

func (dal *PrivilegeTicketDal) Create(ctx context.Context, ticket *dao.PrivilegeTicket) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(ticket).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *PrivilegeTicketDal) BulkCreate(ctx context.Context, his []*dao.PrivilegeTicketResource) error {
	db := dal.dbPro.GetMetaDB(ctx)
	return db.CreateInBatches(&his, 100).Error
}

func (dal *PrivilegeTicketDal) List(ctx context.Context, userId, tenantID string, searchParam *PriTicketSearchParam, pageNumber, pageSize int32) (*dao.DbwPrivilegeTickets, error) {
	db := dal.dbPro.GetMetaDB(ctx)
	var (
		limit          = int(pageSize)
		offset         = int(pageSize * (pageNumber - 1))
		ret            []*dao.PrivilegeTicket
		total          int64
		baseConditions []interface{}
		orderBy        string
	)
	baseQuery := "tenant_id=?"
	baseConditions = append(baseConditions, tenantID)

	orderBy = "create_time"
	if searchParam != nil && searchParam.OrderBy != "" {
		switch searchParam.OrderBy {
		case model.OrderByTimeType_CreateTime.String():
			orderBy = "create_time"
		case model.OrderByTimeType_UpdateTime.String():
			orderBy = "update_time"
		}
	}

	if searchParam != nil && searchParam.PriTicketListType != "" {
		switch searchParam.PriTicketListType {
		case model.PriTicketListType_CreatedByMe.String():
			baseQuery += " and create_user_id=?"
			baseConditions = append(baseConditions, userId)
		case model.PriTicketListType_ApprovedByMe.String():
			baseQuery += " and status=?"
			baseConditions = append(baseConditions, model.PriTicketStatus_TicketWait.String())
		}
	}
	if searchParam != nil && searchParam.UserName != "" {
		baseQuery += " and create_user like ?"
		baseConditions = append(baseConditions, "%"+searchParam.UserName+"%")
	}
	if searchParam != nil && searchParam.UserId != "" {
		baseQuery += " and create_user_id = ?"
		baseConditions = append(baseConditions, searchParam.UserId)
	}
	if searchParam != nil && searchParam.TicketID != "" {
		baseQuery += " and id like ?"
		baseConditions = append(baseConditions, "%"+searchParam.TicketID+"%")
	}
	if searchParam != nil && searchParam.PriTicketStatus != "" {
		baseQuery += " and status = ?"
		baseConditions = append(baseConditions, searchParam.PriTicketStatus)
	}
	if searchParam != nil && searchParam.PrivilegeResourceType != "" {
		baseQuery += " and resource_type = ?"
		baseConditions = append(baseConditions, searchParam.PrivilegeResourceType)
	}
	if searchParam != nil && searchParam.UserMemo != "" {
		baseQuery += " and user_memo = ?"
		baseConditions = append(baseConditions, searchParam.UserMemo)
	}
	if searchParam != nil && searchParam.Title != "" {
		baseQuery += " and title = ?"
		baseConditions = append(baseConditions, searchParam.Title)
	}
	// 求total
	if err := db.Model(ret).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		return nil, err
	}
	if err := db.Where(baseQuery, baseConditions...).Offset(offset).Limit(limit).Order(orderBy + " " + searchParam.SortBy).Find(&ret).Error; err != nil {
		return nil, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Offset(offset).Limit(limit).Order(orderBy + " " + searchParam.SortBy).Find(&ret)
	}))
	return &dao.DbwPrivilegeTickets{
		Total:            total,
		PrivilegeTickets: ret,
	}, nil
}

func (dal *PrivilegeTicketDal) Update(ctx context.Context, ID string, data map[string]interface{}) error {
	db := dal.dbPro.GetMetaDB(ctx)
	return db.Model(new(dao.PrivilegeTicket)).Where(`id=?`, ID).Updates(data).Error
}

func (dal *PrivilegeTicketDal) Get(ctx context.Context, ID, tenantID string) (*dao.PrivilegeTicket, error) {
	db := dal.dbPro.GetMetaDB(ctx)
	var ret dao.PrivilegeTicket
	err := db.Where(`id=? and tenant_id=?`, ID, tenantID).First(&ret).Error
	if err != nil {
		return nil, err
	}
	return &ret, nil
}

func (dal *PrivilegeTicketDal) GetResourcesByTicketID(ctx context.Context, ticketID string) ([]*dao.PrivilegeTicketResource, error) {
	db := dal.dbPro.GetMetaDB(ctx)
	var ret []*dao.PrivilegeTicketResource
	err := db.Where(`resource_id=?`, ticketID).Find(&ret).Error
	return ret, err
}
