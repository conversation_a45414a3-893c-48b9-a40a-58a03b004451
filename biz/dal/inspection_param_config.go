package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
)

type InspectionParamConfigDAL interface {
	Create(ctx context.Context, task *dao.InspectionParamConfig) error
	Get(ctx context.Context, instanceType string, instanceId string, tenantId, regionId string) (*dao.InspectionParamConfig, error)
	GetAll(ctx context.Context, instanceType string, instanceId string, tenantId, regionId string) ([]*dao.InspectionParamConfig, error)
	DeleteConfig(ctx context.Context, tenantId, regionId string, id int64) error
	Update(ctx context.Context, config string, param InspectionParamConfigParam) error
	ListAll(ctx context.Context, TenantId string, InstanceType string) ([]*dao.InspectionParamConfig, error)
}

func NewInspectionParamConfigDAL(dbPro DBProvider) InspectionParamConfigDAL {
	return &InspectionParamConfigDal{dbPro: dbPro}
}

type InspectionParamConfigDal struct {
	dbPro DBProvider
}

type InspectionParamConfigParam struct {
	InstanceType string
	InstanceId   *string
	TenantId     string
	RegionId     string
	Config       *string
	NodeId       *string
}

func (dal *InspectionParamConfigDal) Create(ctx context.Context, conf *dao.InspectionParamConfig) error {
	db := dal.dbPro.GetMetaDB(ctx)
	err := db.Create(conf).Error
	if err != nil {
		return err
	}
	return nil
}

func (dal *InspectionParamConfigDal) Get(ctx context.Context, instanceType string, instanceId string, tenantId, regionId string) (*dao.InspectionParamConfig, error) {
	var ret dao.InspectionParamConfig
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "select * from " + dao.InspectionParamConfigTableName + " where instance_id=\"" + instanceId + "\" and instance_type=\"" + instanceType +
		"\" and tenant_id=\"" + tenantId + "\" region_id=\"" + regionId + "\""
	err := db.Raw(sqlStr).Scan(&ret).Error // ignore_security_alert
	return &ret, err
}

func (dal *InspectionParamConfigDal) GetAll(ctx context.Context, instanceType string, instanceId string, tenantId, regionId string) ([]*dao.InspectionParamConfig, error) {
	var ret []*dao.InspectionParamConfig
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "select * from " + dao.InspectionParamConfigTableName + " where instance_id=\"" + instanceId + "\" and instance_type=\"" + instanceType +
		"\" and tenant_id=\"" + tenantId + "\" and region_id=\"" + regionId + "\""
	log.Info(ctx, "inspection: sqlStr is %s", sqlStr)
	err := db.Raw(sqlStr).Scan(&ret).Error // ignore_security_alert
	return ret, err
}

func (dal *InspectionParamConfigDal) DeleteConfig(ctx context.Context, tenantId, regionId string, id int64) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "delete from " + dao.InspectionParamConfigTableName +
		" where region_id=\"" + regionId + "\"" + " and tenant_id=\"" + tenantId + "\" "
	sqlStr += " and id=" + fmt.Sprintf("%d", id)
	log.Info(ctx, "delete sql is %s", sqlStr)
	err := db.Exec(sqlStr).Error // ignore_security_alert
	return err
}

func (dal *InspectionParamConfigDal) Update(ctx context.Context, config string, param InspectionParamConfigParam) error {
	db := dal.dbPro.GetMetaDB(ctx)
	sqlStr := "update " + dao.InspectionParamConfigTableName +
		" set config='" + config +
		"' where region_id=\"" + param.RegionId + "\"" + " and tenant_id=\"" + param.TenantId + "\" "
	sqlStr += " and instance_type=\"" + param.InstanceType + "\" "
	if param.Config != nil {
		sqlStr += " and config='" + *param.Config + "' "
	}
	if param.InstanceId!= nil {
		sqlStr += " and instance_id=\"" + *param.InstanceId + "\" "
	}
	if param.NodeId!= nil {
		sqlStr += " and node_id=\"" + *param.NodeId + "\" "
	}
	log.Info(ctx, "update sql is %s", sqlStr)
	err := db.Exec(sqlStr).Error // ignore_security_alert
	return err
}

func (dal *InspectionParamConfigDal) ListAll(ctx context.Context, TenantId string, InstanceType string) ([]*dao.InspectionParamConfig, error) {
	var (
		ret            []*dao.InspectionParamConfig
		baseConditions []interface{}
	)
	db := dal.dbPro.GetMetaDB(ctx)
	baseQuery := "tenant_id=? and is_open=1 "
	baseConditions = append(baseConditions, TenantId)
	if InstanceType != "" {
		baseQuery += " and instance_type=? "
		baseConditions = append(baseConditions, InstanceType)
	}
	if err := db.Where(baseQuery, baseConditions...).Find(&ret).Error; err != nil {
		return nil, err
	}
	return ret, nil
}
