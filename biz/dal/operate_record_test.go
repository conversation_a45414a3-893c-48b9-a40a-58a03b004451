package dal

import (
	"context"
	"github.com/bytedance/mockey"
	"gorm.io/gorm"
	"testing"
)

func initOperateRecordDal() *ConsoleOperateRecordDal {
	return &ConsoleOperateRecordDal{
		dbPro: &mockDBProvider{},
	}
}
func initDasOperateRecordDal() *DasOperateRecordDal {
	return &DasOperateRecordDal{
		dbPro: &mockDBProvider{},
	}
}

func TestCreate(t *testing.T) {
	dal := initOperateRecordDal()
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock1.UnPatch()

	err := dal.Create(context.Background(), nil)
	if err != nil {
		t.Fatal("failed", err)
	}
}
func TestDasCreate(t *testing.T) {
	dal := initDasOperateRecordDal()
	mockRes := &gorm.DB{}
	mock1 := mockey.Mock((*gorm.DB).Create).Return(mockRes).Build()
	defer mock1.UnPatch()

	err := dal.Create(context.Background(), nil)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestList(t *testing.T) {
	dal := initOperateRecordDal()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	StartTime := "12345321"
	EndTime := "12345321"
	InfluenceLinesMin := "12345321"
	InfluenceLinesMax := "12345321"
	ExecuteCostMax := "12345321"
	ExecuteCostMin := "12345321"
	SqlTypeList := []string{"12345321"}
	CreateUserIdList := []string{"12345321"}
	Limit := int32(12345321)
	Offset := int32(12345321)
	ConsoleOperateRecordParam := ConsoleOperateRecordParam{
		SqlStatement:      "12345321",
		StartTime:         StartTime,
		EndTime:           EndTime,
		InstanceID:        "123434",
		InstanceType:      "123422",
		InfluenceLinesMin: InfluenceLinesMin,
		InfluenceLinesMax: InfluenceLinesMax,
		ExecuteCostMax:    ExecuteCostMax,
		ExecuteCostMin:    ExecuteCostMin,
		OperationType:     "123442",
		SqlTypeList:       SqlTypeList,
		OperationStatus:   "123234234",
		CreateUserIdList:  CreateUserIdList,
		CreateUserName:    "*********",
		DbName:            "345345345",
		Limit:             &Limit,
		Offset:            &Offset,
		OrderBy:           "********",
		SortBy:            "564334",
	}
	_, err := dal.List(context.Background(), "123", &ConsoleOperateRecordParam)
	if err != nil {
		t.Fatal("failed", err)
	}
}
func TestDasList(t *testing.T) {
	dal := initDasOperateRecordDal()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	StartTime := 12345321
	EndTime := 12345421
	dasOperationCategory := "CCL"
	CreateUserIdList := []string{"12345321"}
	Limit := int32(10)
	Offset := int32(1)
	dasOperateRecordParam := DasOperateRecordParam{
		DasOperationCategory: dasOperationCategory,
		StartTime:            int64(StartTime),
		EndTime:              int64(EndTime),
		InstanceID:           "123434",
		InstanceType:         "123422",
		OpsTaskState:         "Done",
		TaskAction:           "CreateCCL",
		TriggerType:          "Manual",
		CreateUserIdList:     CreateUserIdList,
		CreateUserName:       "*********",
		Limit:                &Limit,
		Offset:               &Offset,
		OrderBy:              "********",
		SubAccount:           "xxx",
		SortBy:               "564334",
	}
	_, err := dal.List(context.Background(), "123", &dasOperateRecordParam)
	if err != nil {
		t.Fatal("failed", err)
	}
}
func TestDasDeleteByTime(t *testing.T) {
	dal := initDasOperateRecordDal()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Delete).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	err := dal.DeleteByTime(context.Background(), 123123)
	if err != nil {
		t.Fatal("failed", err)
	}
}
func TestQueryRecordByOrderID(t *testing.T) {
	dal := initOperateRecordDal()

	mock1 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).First).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()

	_, err := dal.QueryRecordByOrderID(context.Background(), 123123)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestUpdateStatusByID(t *testing.T) {

	dal := initOperateRecordDal()

	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mockRes := &gorm.DB{}
	mock4 := mockey.Mock((*gorm.DB).Updates).Return(mockRes).Build()
	defer mock4.UnPatch()

	err := dal.UpdateStatusByID(context.Background(), int64(1), int8(2))
	if err != nil {
		t.Fatal("failed", err)
	}
}
