package actors

import (
	"code.byted.org/infcs/protoactor-go/actor"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

func NewCounter(config config.ConfigProvider) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.CounterKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &Counter{config: config}
		}),
	}
}

type Counter struct {
	config config.ConfigProvider
	count  int64
}

func (a *Counter) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(a.getCD(ctx))
	switch msg := ctx.Message().(type) {
	case *shared.GetCounter:
		ctx.Respond(&shared.CounterInfo{Count: a.count})
	case *shared.IncrCounter:
		a.count += msg.Num
	case *shared.ResetCounter:
		a.count = 0
	case *actor.ReceiveTimeout:
		a.count = 0
		ctx.Stop(ctx.Self())
	}
}

func (a *Counter) getCD(ctx types.Context) time.Duration {
	sec := a.config.Get(ctx).MaxLoginFailCDSeconds
	if sec == 0 {
		sec = 60
	}
	return time.Second * time.Duration(sec)
}
