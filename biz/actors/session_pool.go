package actors

import (
	"errors"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/metrics"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

	"github.com/qjpcpu/fp"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

func NewInstanceSessionPool(
	ds *model.DataSource,
	maxIdleTimeout int64,
	maxUsedSeconds int64,
	idGen idgen.Service,
	cnf config.ConfigProvider,
	threshold metrics.ConnectionMetrics,
) *instanceSessionPool {
	return &instanceSessionPool{
		DataSource:     ds,
		MaxIdleSeconds: maxIdleTimeout,
		MaxUsedSeconds: maxUsedSeconds,
		idg:            idGen,
		cnf:            cnf,
		UsedSessions:   make(map[string]*SessionInfo),
		threshold:      threshold,
	}
}

type instanceSessionPool struct {
	DataSource *model.DataSource `json:"data_source,omitempty"`

	MaxIdleSeconds int64 `json:"max_idle_seconds"` // session的最大空闲时间
	MaxUsedSeconds int64 `json:"max_used_seconds"` // session的最大使用时间，如果超时未归还则从池中删掉

	AvailableSessions []*SessionInfo          `json:"available_sessions"`
	UsedSessions      map[string]*SessionInfo `json:"used_sessions"`
	threshold         metrics.ConnectionMetrics

	idg idgen.Service
	cnf config.ConfigProvider
}

type SessionState int64

const (
	SessionState_Idle SessionState = 0
	SessionState_Used SessionState = 1
)

type SessionInfo struct {
	SessionId        string       `json:"session_id"`
	State            SessionState `json:"state"`
	StateChangedTime int64        `json:"state_changed_time"`
	ConnectionId     string       `json:"connection_id"`
}

func (sp *instanceSessionPool) Take(ctx types.Context, source *model.DataSource) (*SessionInfo, error) {
	err := sp.checkConnectionThreshold(ctx, source)
	if err != nil {
		log.Warn(ctx, "failed to create session, checkConnectionThreshold err=%v", err)
		return nil, err
	}

	if len(sp.AvailableSessions) > 0 {
		//session := sp.AvailableSessions[0]
		//sp.AvailableSessions = sp.AvailableSessions[1:]
		var session *SessionInfo
		for index, s := range sp.AvailableSessions {
			if sp.isAvailableSession(ctx, s, source) && errors.Is(nil, sp.checkSessionConn(ctx, s.SessionId)) {
				session = s
				sp.AvailableSessions = append(sp.AvailableSessions[0:index], sp.AvailableSessions[index+1:]...)
				break
			}
		}
		if session == nil {
			goto createSession
		}
		// 每次取之前都检查一下连接，白名单错误不算错误，非白名单错误则关闭session
		sp.UsedSessions[session.SessionId] = session
		session.State = SessionState_Used
		session.StateChangedTime = time.Now().Unix()
		log.Info(ctx, "Take session %s from instanceSessionPool", session.SessionId)
		return session, nil
	}

createSession:
	//sp.ensureTenant(ctx, "1")
	sessionId, connectionId, err := sp.createSession(ctx, source)
	if err != nil {
		log.Warn(ctx, "failed to create session, err=%v", err)
		return nil, err
	}

	// check session, if session can not connect to db, account and password maybe wrong.
	time.Sleep(100 * time.Millisecond)
	newSession := &SessionInfo{
		SessionId:        sessionId,
		State:            SessionState_Used,
		StateChangedTime: time.Now().Unix(),
		ConnectionId:     connectionId,
	}
	sp.UsedSessions[newSession.SessionId] = newSession
	log.Info(ctx, "create session %s instead of instanceSessionPool", sessionId)
	return newSession, nil
}

func (sp *instanceSessionPool) checkConnectionThreshold(ctx types.Context, ds *model.DataSource) error {
	cnf := sp.cnf.Get(ctx)
	if cnf.MaxConnectionPerDataSource == 0 {
		return nil
	}
	connections, err := sp.threshold.GetActiveConnections(ctx, conv.ToSharedDataSource(cnf, ds))
	if err != nil {
		log.Warn(ctx, "get active connections fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailed, "get active connections fail", ds.InstanceId)
	}
	if len(connections) >= int(cnf.MaxConnectionPerDataSource) {
		log.Warn(ctx, "%v,当前连接数%v,最大连接数%v", ds, len(connections), cnf.MaxConnectionPerDataSource)
		return consts.ErrorWithParam(model.ErrorCode_TooManyConnectionsError, ds.InstanceId)
	}
	return nil
}

func (sp *instanceSessionPool) whiteListNotReady(err error) bool {
	return err != nil && strings.Contains(err.Error(), `IP NOT IN white List`)
}

func (sp *instanceSessionPool) GiveBack(ctx types.Context, sessionId string) error {
	log.Debug(ctx, "give back session %s", sessionId)
	session, ok := sp.UsedSessions[sessionId]
	if !ok {
		log.Warn(ctx, "session %s is not in use", sessionId)
		return errors.New("session is not in use")
	}

	session.State = SessionState_Idle
	session.StateChangedTime = time.Now().Unix()
	sp.AvailableSessions = append(sp.AvailableSessions, session)
	delete(sp.UsedSessions, sessionId)

	return nil
}

func (sp *instanceSessionPool) CloseSession(ctx types.Context, sessionId string) {
	delete(sp.UsedSessions, sessionId)
	_ = ctx.ClientOf(consts.SessionActorKind).Send(ctx, sessionId, &shared.CloseSession{})
}

func (sp *instanceSessionPool) createSession(ctx types.Context, ds *model.DataSource) (string, string, error) {
	sessionID, err := sp.idg.NextIDStr(ctx)
	if err != nil {
		return "", "", err
	}

	cnf := sp.cnf.Get(ctx)
	src := conv.ToSharedDataSource(cnf, sp.addDsn(ds))
	src.MaxOpenConns = 1
	src.MaxIdleConns = 0
	msg := &shared.CreateSession{
		Source:                src,
		SessionTimeoutSeconds: cnf.SessionTimeout,
	}
	resp, err := ctx.ClientOf(consts.SessionActorKind).
		Call(ctx, sessionID, msg)
	if err != nil {
		return "", "", consts.ErrorWithParam(model.ErrorCode_CreateSessionError, " with datasource: ", src.InstanceId, err.Error())
	}
	switch rsp := resp.(type) {
	case *shared.SessionCreated:
		if rsp.Code == shared.CreatedOK {
			return rsp.SessionId, rsp.DefaultConnectionId, nil
		}
		log.Warn(ctx, "create session fail %v", rsp.ErrorMessage)
		return "", "", consts.ErrorWithParam(model.ErrorCode_UnknownCreateSessionError, " with datasource: ", src.InstanceId, rsp.ErrorMessage)
	}
	log.Warn(ctx, "create session fail %#v", resp)
	return "", "", consts.ErrorWithParam(model.ErrorCode_CreateSessionError, " with datasource: ", src.InstanceId)
}

func (sp *instanceSessionPool) CheckAccount(ctx types.Context, sessionId string) error {

	req := &shared.CheckInternalSession{}
	resp, err := ctx.ClientOf(consts.SessionActorKind).
		Call(ctx, sessionId, req)
	if err != nil {
		return err
	}
	switch rsp := resp.(type) {
	case *shared.CheckInternalSessionSucceed:
		return nil
	case *shared.CheckInternalSessionFailed:
		return consts.ErrorWithParam(model.ErrorCode_CreateSessionError, "check session failed,please retry.")
	default:
		log.Warn(ctx, "unknown CheckInternalSession response, rsp=%v", rsp)
	}
	return consts.ErrorWithParam(model.ErrorCode_CreateSessionError, "check session failed,please retry.")
}

func (sp *instanceSessionPool) addDsn(ds *model.DataSource) *model.DataSource {
	if ds == nil {
		return ds
	}
	if ds.ExtraDsn == nil {
		ds.ExtraDsn = map[string]string{}
	}
	switch ds.Type {
	case model.DSType_MySQL:
		ds.ExtraDsn["multiStatements"] = "true"
	}
	return ds
}

func (sp *instanceSessionPool) CheckSessions(ctx types.Context) {
	now := time.Now().Unix()
	fp.StreamOf(sp.AvailableSessions).Reject(func(session *SessionInfo) bool {
		if now-session.StateChangedTime > sp.MaxIdleSeconds {
			log.Info(ctx, "session %s idle time timeout, close it", session.SessionId)
			sp.closeSession(ctx, session.SessionId)
			return true
		}
		return false
	}).ToSlice(&sp.AvailableSessions)

	for id, session := range sp.UsedSessions {
		if now-session.StateChangedTime > sp.MaxUsedSeconds {
			log.Info(ctx, "session %s used time timeout, close it", session.SessionId)
			sp.closeSession(ctx, id)
			delete(sp.UsedSessions, id)
		}
	}
}

func (sp *instanceSessionPool) Close(ctx types.Context) {
	log.Info(ctx, "close pool, close all sessions")
	fp.StreamOf(sp.AvailableSessions).Foreach(func(session *SessionInfo) {
		sp.closeSession(ctx, session.SessionId)
	}).Run()
	fp.KVStreamOf(sp.UsedSessions).Foreach(func(sessionId string, session *SessionInfo) {
		sp.closeSession(ctx, session.SessionId)
	}).Run()
}

func (sp *instanceSessionPool) ensureTenant(ctx types.Context, tenantId string) {
	if currentTenant := fwctx.GetTenantID(ctx); currentTenant == "" {
		ctx.WithValue(fwctx.BIZ_CONTEXT_KEY, fwctx.NewBizContext())
		if c := fwctx.GetBizContext(ctx); c != nil {
			c.TenantID = tenantId
		}
	}
}

func (sp *instanceSessionPool) closeSession(ctx types.Context, sessionId string) {
	sp.ensureTenant(ctx, "1")
	ctx.ClientOf(consts.SessionActorKind).Send(ctx, sessionId, &shared.CloseSession{})
}

func (sp *instanceSessionPool) checkSessionConn(ctx types.Context, sessionID string) error {
	resp, err := ctx.ClientOf(consts.SessionActorKind).Call(ctx, sessionID, &shared.CheckInternalSession{})
	if err != nil {
		log.Warn(ctx, "check session %s error %s", sessionID, err)
		return err
	}
	switch resp.(type) {
	case *shared.CheckInternalSessionSucceed:
		return nil
	}
	return errors.New("unknown error")
}

func (sp *instanceSessionPool) isAvailableSession(ctx types.Context, session *SessionInfo, source *model.DataSource) bool {
	msg := &shared.CheckSession{ConnectionId: session.ConnectionId}
	resp, err := ctx.ClientOf(consts.SessionActorKind).
		Call(ctx, session.SessionId, msg)
	if err != nil {
		log.Warn(ctx, "check session error, session id:%s err:%s", session.SessionId, err)
		return false
	}
	switch rsp := resp.(type) {
	case *shared.CheckSessionSuccess:
		if rsp.Source.User != *source.Username ||
			(source.Address != nil && rsp.Source.Address != *source.Address) ||
			(source.DBName != nil && rsp.Source.Db != *source.DBName) {
			return false
		}
		return true
	case *shared.CheckInternalSessionFailed:
		return false
	default:
		return false
	}
}

func (sp *instanceSessionPool) SetMaxIdleSeconds(maxIdleSeconds int64) {
	sp.MaxIdleSeconds = maxIdleSeconds
}

func (sp *instanceSessionPool) SetMaxUsedSeconds(maxUsedSeconds int64) {
	sp.MaxUsedSeconds = maxUsedSeconds
}

func (sp *instanceSessionPool) SetIdGenerator(idg idgen.Service) {
	sp.idg = idg
}

func (sp *instanceSessionPool) SetConfigProvider(cnf config.ConfigProvider) {
	sp.cnf = cnf
}

func (sp *instanceSessionPool) GetDataSource() *model.DataSource {
	return sp.DataSource
}
