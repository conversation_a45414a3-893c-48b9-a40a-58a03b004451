package abnormal_detection

type DetectionMetricInfo struct {
	MetricName  string
	ServerId    string
	MetricValue float64
	Timestamp   int64
	Cluster     string
	Region      string
	DeviceName  string
}

type DetectionMetricScore struct {
	Id    int64
	Value []*DetectionScoreDetail
}

type TlSScoreStruct struct {
	Id    string
	Value string
}

type DetectionScoreDetail struct {
	MetricName   string
	ServerId     string
	MetricValue  float64
	Timestamp    int64
	Cluster      string
	Region       string
	DeviceName   string
	AnomalyScore float64
	Detail       string
}

const (
	ConsumerGroupID = "dbw"

	Interval = 30

	AlarmInterval = 600

	ErrorPoint                 = "error_point"
	DetectionMetricCloudPrefix = "dbw"

	InstanceId   = "InstanceId"
	InstanceType = "InstanceType"
	TenantId     = "TenantId"
	Detail       = "Detail"
)
