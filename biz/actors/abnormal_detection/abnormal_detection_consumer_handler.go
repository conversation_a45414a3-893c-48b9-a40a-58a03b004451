package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/metric_data"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"encoding/json"
	"fmt"
	"github.com/Shopify/sarama"
	"github.com/qjpcpu/fp"
	"runtime/debug"
	"strconv"
	"time"
)

type ConsumerGroupHandler struct {
	Repo        repository.AbnormalDetectionRepo
	ActorClient cli.ActorClient
	IdSvc       idgen.Service
	Ctx         types.Context
	MetricItem  metric_data.RootMetricDataItem
}

func (ConsumerGroupHandler) Setup(_ sarama.ConsumerGroupSession) error {
	return nil
}

func (ConsumerGroupHandler) Cleanup(_ sarama.ConsumerGroupSession) error {
	return nil
}

func (h ConsumerGroupHandler) ConsumeClaim(sess sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	// 这里需要注意，consumer会阻塞在claim.Messages中，如果现在没有消息，它不会超时拉取空消息，而是阻塞在这里
	// 换句话说，只要consumer是活着的，就会一直在这里'转'
	log.Warn(h.Ctx, "ConsumerGroupHandler start consumer")
	for msg := range claim.Messages() {
		metricScore, err := h.unmarshalJson(msg.Value)
		if err == nil {
			h.insertDb(metricScore)
			h.protectUserCall(func() {
				h.sendToCloud(metricScore)
			})
		}
		sess.MarkMessage(msg, "")
		sess.Commit()
	}
	return nil
}

func (h ConsumerGroupHandler) unmarshalJson(bytes []byte) (*DetectionMetricScore, error) {
	var ret TlSScoreStruct
	err := json.Unmarshal(bytes, &ret)
	if err != nil {
		log.Warn(h.Ctx, "DetectionConsumerActor unmarshal tls Json error: %s, json:%s", err.Error(), bytes)
		return nil, err
	}
	configId, err := strconv.ParseInt(ret.Id, 10, 64)
	if err != nil {
		log.Warn(h.Ctx, "DetectionConsumerActor configId is not int configId:%s", ret.Id)
		return nil, err
	}
	var value []*DetectionScoreDetail
	err = json.Unmarshal([]byte(ret.Value), &value)
	if err != nil {
		log.Warn(h.Ctx, "DetectionConsumerActor unmarshal tls Json error: %s, json:%s", err.Error(), ret.Value)
		return nil, err
	}
	return &DetectionMetricScore{
		Id:    configId,
		Value: value,
	}, nil
}

func (h ConsumerGroupHandler) protectUserCall(fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(h.Ctx, "ConsumerGroupHandler panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (h ConsumerGroupHandler) sendToCloud(metricScore *DetectionMetricScore) {
	config, err := h.Repo.GetDetectionConfigById(h.Ctx, metricScore.Id)
	if err != nil {
		log.Warn(h.Ctx, "detectionConsumerHandler GetDetectionConfigById error:%s", err.Error())
		return
	}
	if metricScore.Value == nil || len(metricScore.Value) == 0 ||
		config == nil || config.Enable == 0 ||
		(time.Now().Unix()-config.LastAlarmTime) < AlarmInterval || !h.hasSeriousAbnormalInfo(metricScore) {
		return
	}
	// detail := h.formatAlarmDetail(metricScore)
	detail := "None"
	if err = h.sendMetric(config, detail); err != nil {
		log.Warn(h.Ctx, "DetectionConsumerActor send to could error, config:%s detail:%s", utils.Show(config), err.Error())
		return
	}
	_ = h.Repo.UpdateAlarmTime(h.Ctx, config.ConfigId)
}

func (h ConsumerGroupHandler) hasSeriousAbnormalInfo(metricScore *DetectionMetricScore) bool {
	for _, scoreValue := range metricScore.Value {
		if _, ok := h.MetricItem.Items[scoreValue.MetricName]; !ok {
			continue
		}
		if h.MetricItem.Items[scoreValue.MetricName].IsSeriousAbnormality(scoreValue.AnomalyScore, scoreValue.MetricValue) {
			return true
		}
	}
	return false
}

func (h ConsumerGroupHandler) sendMetric(config *entity.DbwAbnormalDetectionConfig, errorDetail string) error {
	log.Info(h.Ctx, "start to send metric, detail:%s, config:%s", errorDetail, utils.Show(config))
	resp, err := h.ActorClient.KindOf(consts.CloudMonitorActorKind).Call(h.Ctx, consts.SingletonActorName, &shared.SendErrorPointMetric{
		InstanceId:   config.InstanceId,
		InstanceType: config.InstanceType,
		TenantId:     config.TenantId,
		Detail:       errorDetail,
	})
	if err != nil {
		log.Warn(h.Ctx, "detectionId:%d, ConsumerGroupHandler call CloudMonitorActorKind error %s", config.ConfigId, err.Error())
		return err
	}
	switch rsp := resp.(type) {
	case *shared.SendCloudOk:
		return nil
	case *shared.SendCloudError:
		log.Warn(h.Ctx, " send cloud error:%s", rsp.Detail)
		return fmt.Errorf(rsp.Detail)
	}
	return nil
}

func (h ConsumerGroupHandler) formatAlarmDetail(metricScore *DetectionMetricScore) string {
	items := fp.StreamOf(metricScore.Value).Map(func(scoreDetail *DetectionScoreDetail) string {
		return scoreDetail.MetricName
	}).JoinStrings(",")
	return fmt.Sprintf("those item detection error: %s", items)
}

func (h ConsumerGroupHandler) insertDb(metricScore *DetectionMetricScore) {
	events := h.formatEvents(metricScore)
	if events == nil || len(events) == 0 {
		return
	}
	for _, event := range events {
		if err := h.Repo.AddAbnormalDetectionEvent(h.Ctx, event); err != nil {
			log.Warn(h.Ctx, "DetectionConsumerActor DetectionEvent insert error, event:%s, error:%s", utils.Show(event), err.Error())
		}
	}
}

func (h ConsumerGroupHandler) formatEvents(metricScore *DetectionMetricScore) []*entity.DbwAbnormalDetectionEvent {
	if metricScore.Value == nil || len(metricScore.Value) == 0 {
		return nil
	}
	var events []*entity.DbwAbnormalDetectionEvent
	for _, value := range metricScore.Value {
		eventId, _ := h.IdSvc.NextID(h.Ctx)
		event := &entity.DbwAbnormalDetectionEvent{
			ConfigId:    metricScore.Id,
			EventId:     eventId,
			DateTime:    value.Timestamp,
			Item:        value.MetricName,
			ItemValue:   strconv.FormatFloat(value.MetricValue, 'f', 2, 64),
			ErrorDetail: value.Detail,
			Score:       strconv.FormatFloat(value.AnomalyScore, 'f', 2, 64),
		}
		events = append(events, event)
	}
	return events
}
