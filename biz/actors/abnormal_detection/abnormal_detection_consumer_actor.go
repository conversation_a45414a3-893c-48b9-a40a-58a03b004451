package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/metric_data"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"fmt"
	"github.com/Shopify/sarama"
	"go.uber.org/dig"
	"runtime/debug"
	"time"
)

type NewDetectionConsumerActorIn struct {
	dig.In
	Repo           repository.AbnormalDetectionRepo
	IdSvc          idgen.Service
	ActorClient    cli.ActorClient
	Ds             datasource.DataSourceService
	Cnf            config.ConfigProvider
	C3ConfProvider c3.ConfigProvider
	MetricItem     metric_data.RootMetricDataItem
}

func NewDetectionConsumerActor(p NewDetectionConsumerActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.DetectionConsumerActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			newDetectionConsumerActor := &DetectionConsumerActor{
				repo:           p.Repo,
				state:          &DetectionConsumerActorState{},
				idSvc:          p.IdSvc,
				actorClient:    p.ActorClient,
				ds:             p.Ds,
				cnf:            p.Cnf,
				c3ConfProvider: p.C3ConfProvider,
				consumerState:  &ConsumerState{},
				metricItem:     p.MetricItem,
			}
			return newDetectionConsumerActor
		}),
	}
}

func (d *DetectionConsumerActor) GetState() []byte {
	state, _ := json.Marshal(d.state)
	return state
}

type DetectionConsumerActor struct {
	repo           repository.AbnormalDetectionRepo
	state          *DetectionConsumerActorState
	idSvc          idgen.Service
	actorClient    cli.ActorClient
	ds             datasource.DataSourceService
	cnf            config.ConfigProvider
	c3ConfProvider c3.ConfigProvider
	consumerState  *ConsumerState
	metricItem     metric_data.RootMetricDataItem
}

type DetectionConsumerActorState struct {
}

// ConsumerState 不需要持久化
type ConsumerState struct {
	consumerGroup     sarama.ConsumerGroup
	isPointMetricInit bool
}

func (d *DetectionConsumerActor) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		d.OnStart(ctx, msg)
	case *shared.ConsumeEvent:
		d.protectUserCall(ctx, func() {
			d.ConsumeEvent(ctx)
		})
	case *shared.InitActor:
		d.protectUserCall(ctx, func() {
			d.init(ctx)
		})
	case *shared.AreYouOK:
		ctx.Respond(&shared.OK{})
	case *actor.Stopping:
		d.Close(ctx)
	}
}

func (d *DetectionConsumerActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call DetectionConsumerActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (d *DetectionConsumerActor) OnStart(ctx types.Context, msg *actor.Started) {
	// 发个初始化信息
	d.consumerState.isPointMetricInit = false
	ctx.Send(ctx.Self(), &shared.InitActor{})
	log.Info(ctx, "DetectionConsumerActor Started")
}

func (d *DetectionConsumerActor) init(ctx types.Context) {
	// 初始化消费者
	log.Info(ctx, "start init consumer")
	if err := d.initConsumer(ctx); err != nil {
		log.Warn(ctx, "init consumer error:%s", err.Error())
		time.Sleep(Interval * time.Second)
		ctx.Send(ctx.Self(), &shared.InitActor{})
		return
	}
	log.Info(ctx, "finish init consumer")
	// 发送消费请求
	ctx.Send(ctx.Self(), &shared.ConsumeEvent{})
}

func (d *DetectionConsumerActor) initConsumer(ctx types.Context) error {
	cnf := d.cnf.Get(ctx)
	consumerConfig := d.initConsumerConfig(ctx, cnf.TlsProjectId)
	// groupID 固定就行了，不需要改
	group, err := sarama.NewConsumerGroup([]string{cnf.TLSKafkaEndpoint}, ConsumerGroupID, consumerConfig)
	if err != nil {
		log.Warn(ctx, "init consumer error: %s", err.Error())
		return err
	}
	d.consumerState.consumerGroup = group
	return nil
}

func (d *DetectionConsumerActor) initConsumerConfig(ctx types.Context, projectId string) *sarama.Config {
	consumerConfig := sarama.NewConfig()
	consumerConfig.Version = sarama.V2_0_0_0 // specify appropriate version
	consumerConfig.ApiVersionsRequest = true
	consumerConfig.Consumer.Return.Errors = true
	consumerConfig.Net.SASL.Mechanism = "PLAIN"
	consumerConfig.Net.SASL.Version = int16(0)
	consumerConfig.Net.SASL.Enable = true
	consumerConfig.Net.TLS.Enable = true
	// 把自动提交关闭，改为手动提交，避免消息丢失
	consumerConfig.Consumer.Offsets.AutoCommit.Enable = false
	consumerConfig.Consumer.Offsets.Initial = sarama.OffsetNewest
	// projectId
	consumerConfig.Net.SASL.User = projectId
	// 设置每次拉取消息数量
	consumerConfig.Consumer.Fetch.Min = 3
	c3Cfg := d.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	Ak := c3Cfg.TLSServiceAccessKey
	Sk := c3Cfg.TLSServiceSecretKey
	// 火山引擎账号的密钥，或具备对应权限的子账号密钥。不支持STS临时安全令牌。
	consumerConfig.Net.SASL.Password = fmt.Sprintf("%s#%s", Ak, Sk)
	return consumerConfig
}

func (d *DetectionConsumerActor) ConsumeEvent(ctx types.Context) {
	if d.consumerState == nil || d.consumerState.consumerGroup == nil {
		// 如果消费者没有创建出来，就不需要往下走
		return
	}
	cnf := d.cnf.Get(ctx)
	log.Info(ctx, "DetectionConsumerActor start consume")
	for {
		// ${out-TopicID}为Kafka协议消费主题ID，格式为out+日志主题ID，例如"out-0fdaa6b6-3c9f-424c-8664-fc0d222c****"。
		topic := "out-" + cnf.DetectionErrorTopicId
		topics := []string{topic}
		handler := d.initConsumerGroupHandler(ctx)
		// `Consume` should be called inside an infinite loop, when a
		// server-side rebalance happens, the consumer session will need to be
		// recreated to get the new claims
		err := d.consumerState.consumerGroup.Consume(ctx, topics, handler)
		if err != nil {
			log.Warn(ctx, "consumer error:%s", err.Error())
			// reblance是不会产生这个异常的，如果这里发生了异常，我们就关闭消费者，重新初始化
			d.reInitConsumer(ctx)
			return
		}
	}
}

func (d *DetectionConsumerActor) initConsumerGroupHandler(ctx types.Context) ConsumerGroupHandler {
	return ConsumerGroupHandler{
		Repo:        d.repo,
		IdSvc:       d.idSvc,
		ActorClient: d.actorClient,
		Ctx:         ctx,
		MetricItem:  d.metricItem,
	}
}

func (d *DetectionConsumerActor) reInitConsumer(ctx types.Context) {
	// 关闭消费者，停顿一段时间后，重新创建
	d.Close(ctx)
	time.Sleep(Interval * time.Second)
	ctx.Send(ctx.Self(), &shared.InitActor{})
}

func (d *DetectionConsumerActor) Close(ctx types.Context) {
	if d.consumerState == nil || d.consumerState.consumerGroup == nil {
		return
	}
	if err := d.consumerState.consumerGroup.Close(); err != nil {
		log.Warn(ctx, "DetectionConsumerActor close consumer error:%s", err)
	}
}
