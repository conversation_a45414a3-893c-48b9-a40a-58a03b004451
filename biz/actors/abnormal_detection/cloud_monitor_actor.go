package abnormal_detection

import (
	metricV3 "code.byted.org/gopkg/metrics/v3"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"go.uber.org/dig"
	"runtime/debug"
)

type NewCloudMonitorActorIn struct {
	dig.In
	Repo           repository.AbnormalDetectionRepo
	IdSvc          idgen.Service
	ActorClient    cli.ActorClient
	Ds             datasource.DataSourceService
	Cnf            config.ConfigProvider
	C3ConfProvider c3.ConfigProvider
}

func NewCloudMonitorActor(p NewCloudMonitorActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.CloudMonitorActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			newCloudMonitorActor := &CloudMonitorActor{
				repo:           p.Repo,
				state:          &CloudMonitorActorState{},
				idSvc:          p.IdSvc,
				actorClient:    p.ActorClient,
				ds:             p.Ds,
				cnf:            p.Cnf,
				c3ConfProvider: p.C3ConfProvider,
			}
			return newCloudMonitorActor
		}),
	}
}

func (c *CloudMonitorActor) GetState() []byte {
	state, _ := json.Marshal(c.state)
	return state
}

type CloudMonitorActor struct {
	repo           repository.AbnormalDetectionRepo
	state          *CloudMonitorActorState
	idSvc          idgen.Service
	actorClient    cli.ActorClient
	ds             datasource.DataSourceService
	cnf            config.ConfigProvider
	c3ConfProvider c3.ConfigProvider
}

// CloudMonitorActorState 不需要缓存
type CloudMonitorActorState struct {
	errorPointMetric  metricV3.Metric
	isPointMetricInit bool
}

func (c *CloudMonitorActor) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		c.OnStart(ctx, msg)
	case *shared.SendErrorPointMetric:
		c.protectUserCall(ctx, func() {
			c.sendErrorPointMetric(ctx, msg)
		})
	case *shared.InitActor:
		c.protectUserCall(ctx, func() {
			c.init(ctx)
		})
	case *shared.AreYouOK:
		ctx.Respond(&shared.OK{})
	case *actor.Stopping:
		c.Close(ctx)
	}
}

func (c *CloudMonitorActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call CloudMonitorActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (c *CloudMonitorActor) OnStart(ctx types.Context, msg *actor.Started) {
	// 发个初始化信息
	c.state.isPointMetricInit = false
	ctx.Send(ctx.Self(), &shared.InitActor{})
	log.Info(ctx, "CloudMonitorActor Started")
}

func (c *CloudMonitorActor) init(ctx types.Context) {
	// 初始化pointMetric，这个东西仅初始化一次，所以采用actor模型来实现
	// 因为actor内执行是串行的，所以不需要用单例模式进行判断
	if c.state.errorPointMetric != nil || !c.state.isPointMetricInit {
		metricClient := metricV3.NewClient(DetectionMetricCloudPrefix, metricV3.SetGlobalTags(metricV3.T{Name: "MetricNamespace", Value: "VCM_DBW"}))
		c.state.errorPointMetric = metricClient.NewMetric(ErrorPoint, []string{InstanceId, InstanceType, TenantId, Detail}...)
		c.state.isPointMetricInit = true
		log.Info(ctx, "init cloud metric")
	}
}

func (c *CloudMonitorActor) sendErrorPointMetric(ctx types.Context, metricInfo *shared.SendErrorPointMetric) {

	metricTag := []metricV3.T{
		{Name: InstanceId, Value: metricInfo.InstanceId},
		{Name: InstanceType, Value: metricInfo.InstanceType},
		{Name: TenantId, Value: metricInfo.TenantId},
		{Name: Detail, Value: metricInfo.Detail},
	}

	err := c.state.errorPointMetric.WithTags(metricTag...).Emit(
		// A rate counter metric with the default suffix ""
		metricV3.Store(1),
	)
	if err != nil {
		log.Warn(ctx, "CloudMonitorActor send to error_point metric error,  detail:%s", err.Error())
		ctx.Respond(&shared.SendCloudError{
			Detail: "send to error_point metric error",
		})
		return
	}
	ctx.Respond(&shared.SendCloudOk{})
}

func (c *CloudMonitorActor) Close(ctx types.Context) {
	if c.state.errorPointMetric != nil {
		c.state.errorPointMetric.Close()
	}
	log.Warn(ctx, "CloudMonitorActor stop")
}
