package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
)

func mockConsumerGroupHandler() *ConsumerGroupHandler {
	return &ConsumerGroupHandler{
		MetricItem: mockRootMetricDataItem(),
		Repo:       &repository.AbnormalDetectionRepoImpl{},
		IdSvc:      &mocks.MockService{},
	}
}

func TestHasSeriousAbnormalInfo(t *testing.T) {
	handler := mockConsumerGroupHandler()
	mockScore := mockDetectionMetricScore()
	res := handler.hasSeriousAbnormalInfo(mockScore)
	assert.True(t, res)
	mockScore.Value[0].AnomalyScore = 0.1
	res = handler.hasSeriousAbnormalInfo(mockScore)
	assert.False(t, res)
}

func mockDetectionMetricScore() *DetectionMetricScore {
	detail := &DetectionScoreDetail{
		AnomalyScore: 1.00,
		MetricValue:  70.00,
		MetricName:   "CpuUsage",
	}
	return &DetectionMetricScore{
		Id:    1,
		Value: []*DetectionScoreDetail{detail},
	}
}

func TestUnmarshalJson(t *testing.T) {
	handler := mockConsumerGroupHandler()
	jsonStr := "{\"Id\":\"1717450697974251520\",\"Value\":\"[{\\\"MetricName\\\": \\\"MemUsage\\\", \\\"ServerId\\\": \\\"mysql-844bfb297ff9\\\", \\\"MetricValue\\\": 6.97, \\\"Timestamp\\\": 1699520150, \\\"Cluster\\\": \\\"MySQL\\\", \\\"Region\\\": \\\"cn-chongqing-sdv\\\", \\\"DeviceName\\\": \\\"2000001190\\\", \\\"AnomalyScore\\\": 0.0}, {\\\"MetricName\\\": \\\"ConnectionRatio\\\", \\\"ServerId\\\": \\\"mysql-844bfb297ff9\\\", \\\"MetricValue\\\": 1.25, \\\"Timestamp\\\": 1699520150, \\\"Cluster\\\": \\\"MySQL\\\", \\\"Region\\\": \\\"cn-chongqing-sdv\\\", \\\"DeviceName\\\": \\\"2000001190\\\", \\\"AnomalyScore\\\": 0.0}, {\\\"MetricName\\\": \\\"DiskUsage\\\", \\\"ServerId\\\": \\\"mysql-844bfb297ff9\\\", \\\"MetricValue\\\": 4.12, \\\"Timestamp\\\": 1699520150, \\\"Cluster\\\": \\\"MySQL\\\", \\\"Region\\\": \\\"cn-chongqing-sdv\\\", \\\"DeviceName\\\": \\\"2000001190\\\", \\\"AnomalyScore\\\": 0.0}, {\\\"MetricName\\\": \\\"CpuUsage\\\", \\\"ServerId\\\": \\\"mysql-844bfb297ff9\\\", \\\"MetricValue\\\": 0.81, \\\"Timestamp\\\": 1699520150, \\\"Cluster\\\": \\\"MySQL\\\", \\\"Region\\\": \\\"cn-chongqing-sdv\\\", \\\"DeviceName\\\": \\\"2000001190\\\", \\\"AnomalyScore\\\": 0.0}]\",\"__tag____client_ip__\":\"************\",\"__tag____receive_time__\":\"1699520152930\"}"
	res, err := handler.unmarshalJson([]byte(jsonStr))
	assert.Nil(t, err)
	assert.Equal(t, res.Id, int64(1717450697974251520))
	assert.Equal(t, len(res.Value), 4)
}

func TestInsertDb(t *testing.T) {
	mockDetectionMetricScore := &DetectionMetricScore{
		Id:    12345,
		Value: []*DetectionScoreDetail{{MetricValue: 0, AnomalyScore: 0}},
	}
	handler := mockConsumerGroupHandler()

	mock1 := mockey.Mock((*repository.AbnormalDetectionRepoImpl).AddAbnormalDetectionEvent).Return(fmt.Errorf("error")).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockService).NextID).Return(1, nil).Build()
	defer mock2.UnPatch()

	handler.insertDb(&DetectionMetricScore{})
	handler.insertDb(mockDetectionMetricScore)
}
