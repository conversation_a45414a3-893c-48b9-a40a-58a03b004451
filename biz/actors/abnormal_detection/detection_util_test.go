package abnormal_detection

import (
	"context"
	"github.com/Shopify/sarama"
)

type MockConsumerGroup struct {
}

func (m *MockConsumerGroup) Consume(ctx context.Context, topics []string, handler sarama.ConsumerGroupHandler) error {
	return nil
}

// Errors returns a read channel of errors that occurred during the consumer life-cycle.
// By default, errors are logged and not returned over this channel.
// If you want to implement any custom error handling, set your config's
// Consumer.Return.Errors setting to true, and read from this channel.
func (m *MockConsumerGroup) Errors() <-chan error {
	return nil
}

// Close stops the ConsumerGroup and detaches any running sessions. It is required to call
// this function before the object passes out of scope, as it will otherwise leak memory.
func (m *MockConsumerGroup) Close() error {
	return nil
}
