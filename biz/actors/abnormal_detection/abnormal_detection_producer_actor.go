package abnormal_detection

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/metric_data"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"github.com/qjpcpu/fp"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"
	"go.uber.org/dig"
	"math/rand"
	"runtime/debug"
	"strconv"
	"time"
)

type NewDetectionProducerActorIn struct {
	dig.In
	Repo           repository.AbnormalDetectionRepo
	IdSvc          idgen.Service
	ActorClient    cli.ActorClient
	Ds             datasource.DataSourceService
	Loca           location.Location
	C3ConfProvider c3.ConfigProvider
	Cnf            config.ConfigProvider
	MetricItem     metric_data.RootMetricDataItem
}

func NewDetectionProducerActor(p NewDetectionProducerActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.DetectionProducerActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			newDetectionProducerActor := &DetectionProducerActor{
				repo:           p.Repo,
				state:          &DetectionProducerActorState{},
				idSvc:          p.IdSvc,
				actorClient:    p.ActorClient,
				ds:             p.Ds,
				loca:           p.Loca,
				c3ConfProvider: p.C3ConfProvider,
				cnf:            p.Cnf,
				metricItem:     p.MetricItem,
			}
			return newDetectionProducerActor
		}),
	}
}

func (d *DetectionProducerActor) GetState() []byte {
	state, _ := json.Marshal(d.state)
	return state
}

type DetectionProducerActor struct {
	repo           repository.AbnormalDetectionRepo
	state          *DetectionProducerActorState
	idSvc          idgen.Service
	actorClient    cli.ActorClient
	ds             datasource.DataSourceService
	loca           location.Location
	c3ConfProvider c3.ConfigProvider
	cnf            config.ConfigProvider
	metricItem     metric_data.RootMetricDataItem
}

type DetectionProducerActorState struct {
	regionId string
}

const (
	TimeInterval uint64 = 2 * 60 // 2分钟
)

func (d *DetectionProducerActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(time.Duration(TimeInterval) * time.Second)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		d.OnStart(ctx, msg)
	case *actor.ReceiveTimeout:
		d.protectUserCall(ctx, func() {
			d.doWhenIdle(ctx)
		})
	case *shared.AreYouOK:
		ctx.Respond(&shared.OK{})
	}
}

func (d *DetectionProducerActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call DetectionProducerActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (d *DetectionProducerActor) OnStart(ctx types.Context, msg *actor.Started) {
	d.state.regionId = d.loca.RegionID()
	log.Info(ctx, "DetectionProducerActor Start")
}

// 利用actor的timeout来做定时任务
func (d *DetectionProducerActor) doWhenIdle(ctx types.Context) {
	currentTime := time.Now().Unix()
	// 1.从数据库读出需要进行异常检测的实例
	res, err := d.repo.GetEnableDetectionConfigs(ctx)
	if err != nil {
		log.Warn(ctx, "读取异常检测实例失败：%s", err.Error())
		return
	}
	// 2.对每个实例进行并发
	for _, detectionConfig := range res {
		go d.detection(ctx, detectionConfig, currentTime)
	}
}

func (d *DetectionProducerActor) detection(ctx types.Context, detectionConfig *entity.DbwAbnormalDetectionConfig, currentTime int64) {
	req, err := d.formatReq(ctx, detectionConfig, currentTime)
	if err != nil {
		return
	}
	// 1.采集信息
	// 在0-3s中随机sleep，防止大量请求influxdb报错
	rand.Seed(time.Now().UnixNano())
	randMill := rand.Intn(3000)
	time.Sleep(time.Duration(randMill) * time.Millisecond)
	metricInfos, err := d.collectData(ctx, req, currentTime)
	if err != nil {
		return
	}
	// 2.发送数据
	d.ProducerMetricData(ctx, metricInfos, detectionConfig)
}

func (d *DetectionProducerActor) collectData(ctx types.Context, req *model.DescribeMetricDataItemDetailReq, currentTime int64) ([]*DetectionMetricInfo, error) {
	var metricInfos []*DetectionMetricInfo
	// 采集连接数使用率，cpu，内存，磁盘
	collectItems := []model.MetricDataItem{model.MetricDataItem_CpuUsage, model.MetricDataItem_MemUsage,
		model.MetricDataItem_DiskUsage, model.MetricDataItem_ConnectionRatio}

	for _, metricItem := range d.metricItem.Items {
		isCollect := fp.StreamOf(collectItems).Filter(func(collectItem model.MetricDataItem) bool {
			return collectItem == metricItem.Type()
		}).Exists()
		if !isCollect {
			continue
		}
		metricInfo, err := d.getItemMetricInfo(ctx, metricItem, req)
		if err != nil {
			return metricInfos, err
		}
		metricInfos = append(metricInfos, metricInfo)
	}

	d.formatBasicMetricInfo(metricInfos, req, currentTime)
	return metricInfos, nil
}

func (d *DetectionProducerActor) getItemMetricInfo(ctx types.Context, item metric_data.MetricDataItem, req *model.DescribeMetricDataItemDetailReq) (*DetectionMetricInfo, error) {
	metricInfo := &DetectionMetricInfo{}
	value, err := item.Avg(ctx, req)
	if err != nil {
		log.Warn(ctx, "get avg %s error: %s", item.Name(), err.Error())
		return nil, err
	}
	metricInfo.MetricName = item.Name()
	metricInfo.MetricValue = value
	return metricInfo, nil
}

func (d *DetectionProducerActor) formatBasicMetricInfo(metricInfos []*DetectionMetricInfo, req *model.DescribeMetricDataItemDetailReq, currentTime int64) {
	for _, metric := range metricInfos {
		metric.Timestamp = currentTime
		metric.Region = d.state.regionId
		// serviceId 设置为 instanceId
		metric.ServerId = req.InstanceId
		// deviceName 设置为 租户ID
		metric.DeviceName = req.TenantId
		// cluster 设置为 集群类型
		metric.Cluster = req.InstanceType.String()
	}
}

func (d *DetectionProducerActor) ProducerMetricData(ctx types.Context, metricInfos []*DetectionMetricInfo, detectionConfig *entity.DbwAbnormalDetectionConfig) {
	if len(metricInfos) == 0 || metricInfos[0].MetricValue == float64(0) {
		// 如果metric没有信息，则说明这个实例可能已经没有了
		// 这个时候我们判断一下这个实例是不是还存在，如果不存在了，就从config表中删除
		if !d.isInstanceExist(ctx, detectionConfig) {
			_ = d.repo.DeleteDetectionConfig(ctx, detectionConfig.ConfigId)
		}
		return
	}
	// 写入TLS
	d.ProducerToTls(ctx, metricInfos, detectionConfig.ConfigId, detectionConfig.InstanceId)
}

func (d *DetectionProducerActor) ProducerToTls(ctx types.Context, metricInfos []*DetectionMetricInfo, configId int64, instanceId string) {
	client := d.createTlsClient(ctx)
	_, err := client.PutLogs(d.formatTlsLogs(ctx, metricInfos, configId, instanceId))
	if err != nil {
		log.Warn(ctx, "DetectionProducerActor: write tls error:%s", err.Error())
	}
}

func (d *DetectionProducerActor) formatTlsLogs(ctx types.Context, metricInfos []*DetectionMetricInfo, configId int64, instanceId string) *tls.PutLogsRequest {
	metricInfoJsonBytes, _ := json.Marshal(metricInfos)
	metricLog := &pb.Log{
		Time: time.Now().Unix(),
		Contents: []*pb.LogContent{
			{Key: "Id", Value: strconv.FormatInt(configId, 10)},
			{Key: "Value", Value: string(metricInfoJsonBytes)},
		},
	}
	logs := []*pb.Log{metricLog}
	logGroup := &pb.LogGroup{
		Logs: logs,
	}
	cnf := d.cnf.Get(ctx)
	request := &tls.PutLogsRequest{
		TopicID: cnf.DetectionMetricTopicId,
		LogBody: &pb.LogGroupList{LogGroups: []*pb.LogGroup{logGroup}},
		HashKey: d.getHash(instanceId),
	}
	return request
}

func (d *DetectionProducerActor) getHash(value string) string {
	b := []byte(value)
	return fmt.Sprintf("%x", md5.Sum(b))
}

func (d *DetectionProducerActor) createTlsClient(ctx types.Context) tls.Client {
	c3Cfg := d.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	Ak := c3Cfg.TLSServiceAccessKey
	Sk := c3Cfg.TLSServiceSecretKey
	cnf := d.cnf.Get(ctx)
	regionId := cnf.TlsZone
	if regionId == "" {
		// 如果没有配置就实时读取
		regionId = d.state.regionId
	}
	tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, regionId)
	return tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)
}

func (d *DetectionProducerActor) isInstanceExist(ctx types.Context, detectionConfig *entity.DbwAbnormalDetectionConfig) bool {
	res, err := d.ds.ListInstance(ctx, &datasource.ListInstanceReq{
		Type:       shared.DataSourceType(shared.DataSourceType_value[detectionConfig.InstanceType]),
		InstanceId: detectionConfig.InstanceId,
		PageSize:   1,
		PageNumber: 1,
	})
	// 如果调用接口失败了，就默认为true，下次重试即可
	if err != nil {
		log.Warn(ctx, "调用DescribeDBInstances报错: %s", err.Error())
		return true
	}
	return res.Total != 0
}

func (d *DetectionProducerActor) formatReq(ctx types.Context, detectionConfig *entity.DbwAbnormalDetectionConfig, currentTime int64) (*model.DescribeMetricDataItemDetailReq, error) {
	instanceType, err := model.InstanceTypeFromString(detectionConfig.InstanceType)
	if err != nil {
		log.Warn(ctx, "InstanceId:%s, InstanceType:%s is unsupported", detectionConfig.InstanceId, detectionConfig.InstanceType)
		return nil, err
	}
	return &model.DescribeMetricDataItemDetailReq{
		InstanceType: instanceType,
		InstanceId:   detectionConfig.InstanceId,
		TenantId:     detectionConfig.TenantId,
		StartTime:    int32(d.get2MinAgoTime(currentTime)),
		EndTime:      int32(currentTime),
		Region:       detectionConfig.RegionId,
	}, nil
}

func (d *DetectionProducerActor) dsTypeStringToDatasourceType(dsType string) (shared.DataSourceType, error) {
	dType, err := model.DSTypeFromString(dsType)
	if err != nil {
		return shared.MySQL, err
	}
	return shared.DataSourceType(dType), nil
}

func (d *DetectionProducerActor) get2MinAgoTime(currentTime int64) int64 {
	agoTime := currentTime - 2*60
	return agoTime
}
