package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"fmt"
)

var pgCreateSteps = map[string]IdempotentStep{
	// 创建实例
	// PreCheck 实例状态
	"PrepareInstanceStateStep": &PrepareInstanceStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				switch a.state.InstanceStatus {
				case model.AuditStatus_Running:
					// 补单情况
					return a.idempotentSteps["NotifyCreateOrderStep"]
				case model.AuditStatus_OrderNew:
					// 更新实例状态
					return a.idempotentSteps["UpdateInstanceStatusCreatingStep"]
				default:
					// 其他状态直接返回
				}
				return nil
			},
		},
	},
	// UpdateCreatingStatue
	"UpdateInstanceStatusCreatingStep": &UpdateInstanceStatusCreatingStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareIAMProjectStep"]
			},
		},
	},
	// AddProject
	"PrepareIAMProjectStep": &PrepareIAMProjectStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareIAMProjectTagStep"]
			},
		},
	},
	// AddSystemTag
	"PrepareIAMProjectTagStep": &PrepareIAMProjectTagStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["TagResourceStep"]
		}},
	},
	// Add custom tag 添加用户标签
	"TagResourceStep": &TagResourceStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["PrepareTLSProjectStep"]
		}},
	},
	// 获取或者创建TLS Project
	"PrepareTLSProjectStep": &PrepareTLSProjectStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["PrepareTLSTopicStep"]
		}},
	},
	// 创建TLS topic
	"PrepareTLSTopicStep": &PrepareTLSTopicStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["SaveTLSInfoStep"]
		}},
	},
	// 保存TLS入库
	"SaveTLSInfoStep": &SaveTLSInfoStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			switch a.state.DSType {
			case model.DSType_Postgres:
				return a.idempotentSteps["PrepareTLSIndexStep"]
			default:
				panic(fmt.Sprintf("未兼容%s", a.state.DSType))
			}
			return nil
		}},
	},
	// 创建索引
	"PrepareTLSIndexStep": &PrepareTLSIndexStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["CreateTLSHostGroupStep"]
		}},
	},
	// 创建机器组
	"CreateTLSHostGroupStep": &CreateTLSHostGroupStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["UpdateTLSHostGroupStep"]
		}},
	},
	"UpdateTLSHostGroupStep": &UpdateTLSHostGroupStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["CreateTLSRuleStep"]
		}},
	},
	// 创建采集规则
	"CreateTLSRuleStep": &CreateTLSRuleStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["UpdateTLSRuleStep"]
		}},
	},
	"UpdateTLSRuleStep": &UpdateTLSRuleStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["ApplyRuleToHostGroupsStep"]
		}},
	},
	// 绑定采集规则到机器组
	"ApplyRuleToHostGroupsStep": &ApplyRuleToHostGroupsStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["CreateLogCollectorResourceStep"]
		}},
	},
	// 创建资源 Daemonset & CM 部署log-collector
	"CreateLogCollectorResourceStep": &CreateLogCollectorResourceStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["OpenDBInstanceAuditLogStep"]
			},
		},
	},
	"OpenDBInstanceAuditLogStep": &OpenDBInstanceAuditLogStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				a.state.InstanceStatus = model.AuditStatus_Running
				return a.idempotentSteps["UpdateInstanceStatusStep"]
			},
		},
	},
	// 等待采集资源部署完成
	//"WaitCollectorReadyStep": &WaitCollectorReadyStep{},
	// 修改实例状态
	"UpdateInstanceStatusStep": &UpdateInstanceStatusStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["NotifyCreateOrderStep"]
			},
		},
	},
	// 通知订单完成
	"NotifyCreateOrderStep": &NotifyCreateOrderStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}

func NewAuditCreateLifecycleActor(in NewAuditCreateLifecycleActorIn) types.VirtualPersistenceProducer {
	// 组装垃圾回收子流
	GlobalStepLock.Lock()
	for s, step := range auditCreateGCSteps {
		pgCreateSteps[s] = step
	}
	GlobalStepLock.Unlock()

	return types.VirtualPersistenceProducer{
		Kind: consts.PgAuditCreateActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &AuditLifecycleActor{
				state:           newTaskState(state),
				idempotentSteps: pgCreateSteps,

				source:          in.DataSource,
				crossAuthSvc:    in.CrossAuthSvc,
				conf:            in.Conf,
				c3Conf:          in.C3Conf,
				loc:             in.Loc,
				auditTlsDAL:     in.AuditTlsDAL,
				tagSvc:          in.TagSvc,
				projectSvc:      in.ProjectSvc,
				auditService:    in.AuditService,
				genTlsSvc:       in.GenTlsSvc,
				tlsDAL:          in.TlsDAL,
				lcSvc:           in.LCSvc,
				billSvc:         in.BillSvc,
				publishEventSvc: in.PublishEventSvc,
			}
			return actor
		}),
	}
}
