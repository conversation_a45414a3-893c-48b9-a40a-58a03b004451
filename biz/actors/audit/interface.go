package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	tls_sdk "github.com/volcengine/volc-sdk-golang/service/tls"
	"strings"
	"time"
)

type IdempotentStep interface {
	// PreCheck
	// 预检查返回 false，nil则执行ProtectExec
	// 预检查返回 false，err则等待重试PreCheck，如果达到重试上限则进入失处理流程
	// 预检查返回 true，nil则跳过ProtextExec直接执行NextStep
	PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error)
	// ProtectExec
	// 对应actor执行为串行则没有锁问题
	// 没有返回err则进入postcheck
	// 执行达到最大检查次数则err
	ProtectExec(ctx types.Context, a *AuditLifecycleActor) error
	// NextStep 根据当前state选择下一步怎么走
	NextStep(ctx types.Context, a *AuditLifecycleActor) IdempotentStep

	// MaxExecRetry 不能重试的设置为1
	MaxExecRetry() int
	// ErrorWait 错误重试间隔时间
	ErrorWait() time.Duration
	// GetStepName 单元名，设置为结构体名
	GetStepName() string
	// Trace 记录当前Step操作
	Trace(ctx types.Context, a *AuditLifecycleActor, comment ...string)
	// GetCtxVariable 获取上下文变量
	//GetCtxVariable() interface{}
}

type BaseStep struct {
	IdempotentStep
	NextStepFunc    func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep
	GetNameFunc     func() string
	GetVariableFunc func() interface{}
}

func (b BaseStep) GetTlsClient(ctx types.Context, a *AuditLifecycleActor) (tls_sdk.Client, error) {
	return a.auditService.GetTlsClient(ctx, a.state.TlsTenant)
}

func (b BaseStep) GetInnerAccountTlsClient(ctx types.Context, a *AuditLifecycleActor) (tls_sdk.Client, error) {
	return GetInnerAccountTlsClient(ctx, a)
}

func GetInnerAccountTlsClient(ctx types.Context, a *AuditLifecycleActor) (tls_sdk.Client, error) {
	c3Cfg := a.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	tlsAK := c3Cfg.TLSServiceAccessKey
	tlsSK := c3Cfg.TLSServiceSecretKey
	tlsEndpoint := a.conf.Get(ctx).GetTlsRegionEndpoint(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId))
	region, err := a.conf.Get(ctx).GetTLSRegion(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId))
	if err != nil {
		log.Error(ctx, "请检查运维面参数:TlsServiceEndpoint, tls region err:%s", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	log.Info(ctx, "tlsEndpoint: %s, tlsAK: %s, tlsSK: %s, token: %s, region: %s, c3.TOPServiceAccessKey: %s, c3.TOPServiceSecretKey: %s", tlsEndpoint, tlsSK, tlsAK, "", region)
	client := tls_sdk.NewClient(tlsEndpoint, tlsAK, tlsSK, "", region)
	return client, nil
}

func (b BaseStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (b BaseStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	log.Error(ctx, "WTF")
	return nil
}

func (b BaseStep) MaxExecRetry() int {
	return 1
}

func (b BaseStep) NextStep(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
	if b.NextStepFunc != nil {
		return b.NextStepFunc(ctx, a)
	}
	return nil
}

func (b BaseStep) ErrorWait() time.Duration {
	return time.Second * 10
}

func (b BaseStep) GetStepName() string {
	if b.GetNameFunc != nil {
		return b.GetNameFunc()
	}
	return "BaseStep"
}

//func (b BaseStep) GetCtxVariable() interface{} {
//	if b.GetVariableFunc != nil {
//		return b.GetVariableFunc()
//	}
//	return nil
//}

// Trace comment 仅接受俩参数
func (b BaseStep) Trace(ctx types.Context, a *AuditLifecycleActor, comment ...string) {
	log.Info(ctx, "%s", strings.Join(comment, ", "))
	comment = append(comment, time.Now().Format("2006-01-02 15:04:05.000"))
	a.state.LifeState.HistoryStepsTrace = append(a.state.LifeState.HistoryStepsTrace, comment)
}
