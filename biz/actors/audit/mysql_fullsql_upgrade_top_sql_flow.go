package audit

import (
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

// 升级全量SQL
var upgradeFullSqlUpgradeTopSqlSteps = map[string]IdempotentStep{
	// 创建实例
	// PreCheck 实例状态
	"PrepareFullSqlInstanceUpgradeStateStep": &PrepareFullSqlInstanceUpgradeStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ModifyFullSqlTLSIndexTableStatisticStep"]
			},
		},
	},

	// 修改表分析的索引
	"ModifyFullSqlTLSIndexTableStatisticStep": &ModifyFullSqlTLSIndexTableStatisticStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ModifyFullSqlTLSIndexDetailStep"]
			},
		},
	},

	// 修改详情的索引
	"ModifyFullSqlTLSIndexDetailStep": &ModifyFullSqlTLSIndexDetailStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectTopSqlStep"]
			},
		},
	},

	// 创建sql指标TLS资源
	// 获取或者创建TLS Project
	"FetchInnerProjectTopSqlStep": &FetchInnerProjectTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicTopSqlStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicTopSqlStep": &CreateInnerTopicTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoTopSqlStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoTopSqlStep": &SaveInnerTLSInfoTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareFullSqlTLSIndexTopSqlStep"]
			},
		},
	},
	// 创建索引
	"PrepareFullSqlTLSIndexTopSqlStep": &PrepareFullSqlTLSIndexTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ConfigStatisticPipelineStep"]
			},
		},
	},

	// 配置统计TLS信息（配置zookeeper）
	"ConfigStatisticPipelineStep": &ConfigStatisticPipelineStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}
