package audit

import (
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

type FetchInnerProjectTopSqlStep struct {
	BaseStep
}

func (p FetchInnerProjectTopSqlStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	TopSqlAggrTls := a.state.SqlTopTls
	return projectPreCheck(ctx, a, TopSqlAggrTls)
}

func (p FetchInnerProjectTopSqlStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	TopSqlAggrTls := a.state.SqlTopTls
	return projectProtectExec(ctx, a, TopSqlAggrTls)
}

func (p FetchInnerProjectTopSqlStep) MaxExecRetry() int {
	return 10
}

func (p FetchInnerProjectTopSqlStep) GetStepName() string {
	return "FetchInnerProjectTopSqlStep"
}
