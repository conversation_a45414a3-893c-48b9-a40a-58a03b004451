package audit

import (
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

type PrepareFullSqlTLSIndexTopSqlStep struct {
	BaseStep
}

func (p PrepareFullSqlTLSIndexTopSqlStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	TopSqlAggrTls := a.state.SqlTopTls
	return indexPreCheck(ctx, a, TopSqlAggrTls)
}

func (p PrepareFullSqlTLSIndexTopSqlStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	TopSqlAggrTls := a.state.SqlTopTls
	return indexProtectExec(ctx, a, TopSqlAggrTls, a.state.DSType)
}

func (p PrepareFullSqlTLSIndexTopSqlStep) MaxExecRetry() int {
	return 10
}

func (p PrepareFullSqlTLSIndexTopSqlStep) GetStepName() string {
	return "PrepareFullSqlTLSIndexTopSqlStep"
}
