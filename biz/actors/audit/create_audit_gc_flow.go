package audit

import (
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

var auditCreateGCSteps = map[string]IdempotentStep{
	"NotifyCreateOrderFailStep": &NotifyCreateOrderFailStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["UpdateInstanceStatusDeletingStep"]
			},
		},
	},
	"UpdateInstanceStatusDeletingStep": &UpdateInstanceStatusDeletingStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				switch a.state.DSType {
				case model.DSType_MySQL:
					return a.idempotentSteps["CleanScheduleSqlTaskStep"]
				case model.DSType_VeDBMySQL:
					return a.idempotentSteps["CleanScheduleSqlTaskStep"]
				}
				return a.idempotentSteps["RemoveResourceStep"]
			},
		},
	},
	"CleanScheduleSqlTaskStep": &CleanScheduleSqlTaskStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["RemoveResourceStep"]
			},
		},
	},
	"RemoveResourceStep": &RemoveResourceStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["AuditResourceGCStep"]
			},
		},
	},
	// 垃圾回收
	"AuditResourceGCStep": &AuditResourceGCStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}
