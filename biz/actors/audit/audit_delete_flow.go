package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

var deleteSteps = map[string]IdempotentStep{
	"PrepareDeleteInstanceStateStep": &PrepareDeleteInstanceStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				switch a.state.InstanceStatus {
				case model.AuditStatus_Deleting:
					// 已经在删除的流程中了
					log.Info(ctx, "instance is in deleting process.")
					return nil
				case model.AuditStatus_Deleted:
					log.Info(ctx, "instance is in deleting process.")
					return nil
				}
				return a.idempotentSteps["UpdateInstanceStatusDeletingStep"]
			},
		},
	},
	"UpdateInstanceStatusDeletingStep": &UpdateInstanceStatusDeletingStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["NotifyDeleteOrderStep"]
			},
		},
	},
	"NotifyDeleteOrderStep": &NotifyDeleteOrderStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				switch a.state.DSType {
				case model.DSType_MySQL:
					return a.idempotentSteps["CleanScheduleSqlTaskStep"]
				case model.DSType_VeDBMySQL:
					return a.idempotentSteps["CleanScheduleSqlTaskStep"]
				}
				return a.idempotentSteps["RemoveResourceStep"]
			},
		},
	},
	"CleanScheduleSqlTaskStep": &CleanScheduleSqlTaskStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["RemoveResourceStep"]
			},
		},
	},
	"RemoveResourceStep": &RemoveResourceStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["AuditResourceGCStep"]
			},
		},
	},
	"AuditResourceGCStep": &AuditResourceGCStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}

func NewAuditDeleteLifecycleActor(in NewAuditCreateLifecycleActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.AuditDeleteActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &AuditLifecycleActor{
				state:           newTaskState(state),
				idempotentSteps: deleteSteps,

				source:          in.DataSource,
				crossAuthSvc:    in.CrossAuthSvc,
				conf:            in.Conf,
				c3Conf:          in.C3Conf,
				loc:             in.Loc,
				auditTlsDAL:     in.AuditTlsDAL,
				tagSvc:          in.TagSvc,
				projectSvc:      in.ProjectSvc,
				auditService:    in.AuditService,
				tlsDAL:          in.TlsDAL,
				lcSvc:           in.LCSvc,
				billSvc:         in.BillSvc,
				publishEventSvc: in.PublishEventSvc,

				statisticSqlTlsDAL:  in.StatisticSqlTlsDAL,
				statisticSqlTaskDAL: in.StatisticSqlTaskDAL,
			}
			return actor
		}),
	}
}
