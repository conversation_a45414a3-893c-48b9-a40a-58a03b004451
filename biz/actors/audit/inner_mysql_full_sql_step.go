package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql/zkconfig"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"context"
	"errors"
	"fmt"
	"strings"
	"time"
)

type PrepareInnerFullSqlInstanceStateStep struct {
	BaseStep
}

func (s *PrepareInnerFullSqlInstanceStateStep) ProtectExec(ctx types.Context, actor *AuditLifecycleActor) error {
	log.Info(ctx, "ProtectExec InstanceId: %s, OrderID: %s", actor.state.InstanceId, actor.state.OrderId)

	audit, err := actor.fullSqlInstDAL.GetByID(ctx, actor.state.InstanceId, actor.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return err
	}
	actor.state.InstanceStatus = model.AuditStatus(audit.Status)
	actor.state.FollowInstanceId = audit.FollowInstanceID
	ss := strings.Split(audit.FollowInstanceID, ".")
	if len(ss) != 2 {
		log.Warn(ctx, "FollowInstanceID is not match error:%s", ss)
		return errors.New("dbname is unknown")
	}
	actor.state.DBName = ss[0]
	actor.state.DSType, err = model.DSTypeFromString(audit.DbType)
	if err != nil {
		log.Error(ctx, "DSType fail, error:%s", err)
		return err
	}
	actor.state.DeployType, err = model.LabelTypeFromString(audit.DeployType)
	if err != nil {
		log.Error(ctx, "DeployType fail, error:%s", err)
		return err
	}
	actor.state.LogProductType, err = model.LogProductTypeFromString(audit.ProductType)
	if err != nil {
		log.Error(ctx, "LogProductType fail, error:%s", err)
		return err
	}
	actor.state.StorageSqlTypes = strings.Split(audit.StorageSqlTypes, ",")
	actor.state.Region = audit.Region
	tlsRegion, err := actor.conf.Get(ctx).GetTLSRegion(i18n.GetSiteName(ctx, actor.conf.Get(ctx).FullSqlInnerAccountId))
	if err != nil {
		log.Error(ctx, "请检查运维面参数:TlsServiceEndpoint, tls region err:%s", err)
		return err
	}
	actor.state.TlsRegion = tlsRegion
	tlsEndpoint := actor.conf.Get(ctx).GetTlsRegionEndpoint(i18n.GetSiteName(ctx, actor.conf.Get(ctx).FullSqlInnerAccountId))
	actor.state.TlsEndpoint = tlsEndpoint
	actor.state.TlsDataType = model.StatisticDataType_FullSqlDetail
	actor.state.TlsIndexVersion = FullSqlCurrentTlsVersion

	actor.state.SqlTemplateTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(actor.state.TlsTopicTTL),
		TlsDataType:     model.StatisticDataType_FullSqlTemplateAggr,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	actor.state.SqlCountTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(actor.state.TlsTopicTTL),
		TlsDataType:     model.StatisticDataType_FullSqlExecCount,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	actor.state.SqlTableAggrTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(actor.state.TlsTopicTTL),
		TlsDataType:     model.StatisticDataType_FullSqlTableAggr,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	actor.state.SqlTopTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(actor.state.TlsTopicTTL),
		TlsDataType:     model.StatisticDataType_FullSqlTopSql,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}

	detail, err := actor.source.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		InstanceId: actor.state.DBName,
		Type:       shared.DataSourceType(actor.state.DSType),
		RegionId:   actor.state.Region,
	})
	if err != nil {
		log.Warn(ctx, "Action_DescribeDBInstanceDetail call error: %s", err)
		return err
	}
	if detail.InstanceStatus != model.InstanceStatus_Running.String() {
		log.Warn(ctx, "Please try later. RDS instance is not running, %s", detail.InstanceStatus)
		return err
	}
	actor.state.SecretLevel = detail.SensitiveLevel // 从接口中获取保密级别
	log.Info(ctx, fmt.Sprintf("Init params:[%s]", libutils.Show(actor.state)))
	return nil
}

func (s *PrepareInnerFullSqlInstanceStateStep) MaxExecRetry() int {
	return 10
}

func (s *PrepareInnerFullSqlInstanceStateStep) GetStepName() string {
	return "PrepareInnerFullSqlInstanceStateStep"
}

type SaveInnerFullSqlInstancesStep struct {
	BaseStep
}

func (s *SaveInnerFullSqlInstancesStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (s *SaveInnerFullSqlInstancesStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	log.Info(ctx, "save nodes to meta, nodes:%s", a.state.Nodes)
	for _, node := range a.state.Nodes {
		err := a.instanceExtraNodeDAL.Create(context.TODO(), &dao.InstanceExtraNode{
			InstanceId:  a.state.InstanceId,
			NodeId:      node,
			TenantId:    a.state.TenantId,
			Region:      a.state.Region,
			ProductType: a.state.LogProductType.String(),
			Deleted:     0,
		})
		if err != nil {
			log.Warn(ctx, "instance extra node create meta fail, error:%s", err)
			return err
		}
	}
	return nil
}

func (s *SaveInnerFullSqlInstancesStep) MaxExecRetry() int {
	return 1
}

func (s *SaveInnerFullSqlInstancesStep) GetStepName() string {
	return "SaveInnerFullSqlInstancesStep"
}

type OpenInnerFullSqlLogSwitchStep struct {
	BaseStep
}

func (s *OpenInnerFullSqlLogSwitchStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	for _, node := range a.state.Nodes {
		_, err := a.source.ModifyFullSQLLogConfig(ctx, &datasource.ModifyFullSQLLogConfigReq{
			InstanceID:         node,
			DryRun:             true,
			SQLCollectorStatus: datasource.SQLCollectorStatus_Enable,
			InstanceType:       shared.DataSourceType(a.state.DSType),
			BmqCluster:         a.conf.Get(ctx).FullSqlInnerBmqCluster,
			BmqTopic:           a.conf.Get(ctx).FullSqlInnerBmqTopic,
			RegionId:           a.state.Region,
			DBName:             a.state.DBName,
		})
		if err != nil {
			log.Warn(ctx, "ModifyFullSQLLogConfig error:%s", err)
			return false, err
		}
	}
	return false, nil
}

func (s *OpenInnerFullSqlLogSwitchStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	for _, node := range a.state.Nodes {
		_, err := a.source.ModifyFullSQLLogConfig(ctx, &datasource.ModifyFullSQLLogConfigReq{
			InstanceID:         node,
			DryRun:             false,
			SQLCollectorStatus: datasource.SQLCollectorStatus_Enable,
			InstanceType:       shared.DataSourceType(a.state.DSType),
			BmqCluster:         a.conf.Get(ctx).FullSqlInnerBmqCluster,
			BmqTopic:           a.conf.Get(ctx).FullSqlInnerBmqTopic,
			RegionId:           a.state.Region,
			DBName:             a.state.DBName,
		})
		if err != nil {
			log.Warn(ctx, "ModifyFullSQLLogConfig error:%s", err)
			return err
		}
	}

	return nil
}

func (s *OpenInnerFullSqlLogSwitchStep) MaxExecRetry() int {
	return 10
}

func (s *OpenInnerFullSqlLogSwitchStep) GetStepName() string {
	return "OpenInnerFullSqlLogSwitchStep"
}

type CloseInnerFullSqlLogSwitchStep struct {
	BaseStep
}

func (s *CloseInnerFullSqlLogSwitchStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (s *CloseInnerFullSqlLogSwitchStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	for _, node := range a.state.Nodes {
		_, err := a.source.ModifyFullSQLLogConfig(ctx, &datasource.ModifyFullSQLLogConfigReq{
			InstanceID:         node,
			DryRun:             false,
			SQLCollectorStatus: datasource.SQLCollectorStatus_Disabled,
			InstanceType:       shared.DataSourceType(a.state.DSType),
			RegionId:           a.state.Region,
			DBName:             a.state.DBName,
		})
		if err != nil {
			log.Warn(ctx, "ModifyFullSQLLogConfig error:%s", err)
			return err
		}
		err = a.instanceExtraNodeDAL.DeleteNode(ctx, a.state.InstanceId, node)
		if err != nil {
			log.Warn(ctx, "ModifyFullSQLLogConfig clean meta data error:%s", err)
			return err
		}
	}
	return nil
}

func (s *CloseInnerFullSqlLogSwitchStep) MaxExecRetry() int {
	return 10
}

func (s *CloseInnerFullSqlLogSwitchStep) GetStepName() string {
	return "CloseInnerFullSqlLogSwitchStep"
}

type ConfigInnerConsumerPipelineStep struct {
	BaseStep
}

func (s *ConfigInnerConsumerPipelineStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (s *ConfigInnerConsumerPipelineStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	var dbBlackList map[string]bool
	var userBlackList map[string]bool
	c3Conf := a.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	conf := a.conf.Get(ctx)
	tlsRegion, _ := conf.GetTLSRegion(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId))
	blackStorageSqlTypes := getBlackSqlTypes(a.state.StorageSqlTypes)
	switch a.state.DSType {
	case model.DSType_MySQL:
		dbBlackList = full_sql.DefaultDatabaseFilterMap
		userBlackList = full_sql.DefaultUserFilterMap
	case model.DSType_VeDBMySQL:
		dbBlackList = full_sql.DefaultVedbUserFilterMap
		userBlackList = full_sql.DefaultVedbUserFilterMap
	case model.DSType_MetaMySQL:
		dbBlackList = full_sql.EmptyFilterMap
		userBlackList = full_sql.EmptyFilterMap
	}
	log.Info(ctx, "storage sql type black list:", libutils.Show(blackStorageSqlTypes))
	output := &zkconfig.TlsEtlConfig{
		PipeMeta: &zkconfig.PipeMeta{
			InstanceId:             a.state.FollowInstanceId,
			TenantId:               a.state.TenantId,
			DataType:               model.LogProductType_FullSqlLog.String(),
			Filter:                 true,
			ReportError:            conf.FullSqlReportErrorRecord,
			DBBlackListFilter:      dbBlackList,
			UserBlackListFilter:    userBlackList,
			SqlTypeBlackListFilter: blackStorageSqlTypes,
			SecretLevel:            int(a.state.SecretLevel),
			SimplingRate:           a.state.SimplingRate,
		},
		TlsTarget: &zkconfig.TlsConfig{
			Endpoint: conf.GetTlsRegionEndpoint(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId)),
			Ak:       c3Conf.TLSServiceAccessKey,
			Sk:       c3Conf.TLSServiceSecretKey,
			Project:  a.state.TlsProject,
			Topic:    a.state.TlsTopic,
			Region:   tlsRegion,
		},
		KafkaOutput:    true,
		TlsOutput:      true,
		TemplateOutput: true,
	}
	log.Info(ctx, "Pipe Output Config: %s", libutils.Show(output))
	err := a.fullSqlConfigService.PipeInnerConfigX(ctx, zkconfig.ZKConfig{
		Hosts:      conf.FullSqlZkHosts,
		AuthScheme: conf.FullSqlZkAuthScheme,
		User:       conf.FullSqlZkUser,
		Password:   conf.FullSqlZkPassword,
	}, output)
	if err != nil {
		log.Warn(ctx, "zk pipe config error:%s", err)
		return err
	}

	return nil
}

func (s *ConfigInnerConsumerPipelineStep) MaxExecRetry() int {
	return 10
}

func (s *ConfigInnerConsumerPipelineStep) GetStepName() string {
	return "ConfigInnerConsumerPipelineStep"
}

type FullSqlInnerInstanceGCStep struct {
	BaseStep
}

func (G FullSqlInnerInstanceGCStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	log.Info(ctx, "audit instance resource gc, instanceId:%s", a.state.InstanceId)
	var CloseType model.CloseType
	if a.state.CloseType == nil {
		CloseType = model.CloseType_CloseAuditCollectAndTls
	} else {
		// 删除实例的消息有这个值
		CloseType = *a.state.CloseType
	}
	err := a.fullSqlSvc.DeleteInnerFullSqlResource(ctx, a.state.InstanceId, CloseType, a.state.DSType)
	if err != nil {
		log.Warn(ctx, "delete inner fullsql resource error:%s", err)
	}
	return err
}

func (G FullSqlInnerInstanceGCStep) MaxExecRetry() int {
	return 20
}

func (G FullSqlInnerInstanceGCStep) ErrorWait() time.Duration {
	return time.Minute * 3
}

func (G FullSqlInnerInstanceGCStep) GetStepName() string {
	return "FullSqlInnerInstanceGCStep"
}

type ClearSqlTemplateInnerCacheStep struct {
	BaseStep
}

func (c ClearSqlTemplateInnerCacheStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	// TODO 目前跳过这个方法 内场无法跨region访问
	return true, nil
}

func (c ClearSqlTemplateInnerCacheStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	conf := a.conf.Get(ctx)
	err := a.fullSqlConfigService.ClearInstanceInnerCache(ctx, conf.FullSqlRedisCluster, a.state.FollowInstanceId)
	if err != nil {
		log.Warn(ctx, "clear sql template in redis cache error:%s", err)
		return err
	}
	return nil
}

func (c ClearSqlTemplateInnerCacheStep) MaxExecRetry() int {
	return 10
}

func (c ClearSqlTemplateInnerCacheStep) ErrorWait() time.Duration {
	return time.Second * 10
}

func (c ClearSqlTemplateInnerCacheStep) GetStepName() string {
	return "ClearSqlTemplateInnerCacheStep"
}

type PrepareDeleteInnerFullSqlInstanceStateStep struct {
	BaseStep
}

func (p PrepareDeleteInnerFullSqlInstanceStateStep) ProtectExec(ctx types.Context, actor *AuditLifecycleActor) error {
	log.Info(ctx, "InstanceId:%s, TenantId:%s", actor.state.InstanceId, actor.state.TenantId)
	audit, err := actor.fullSqlInstDAL.GetByID(ctx, actor.state.InstanceId, actor.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return err
	}
	actor.state.InstanceStatus = model.AuditStatus(audit.Status)
	actor.state.FollowInstanceId = audit.FollowInstanceID
	ss := strings.Split(audit.FollowInstanceID, ".")
	if len(ss) != 2 {
		log.Warn(ctx, "FollowInstanceID is not match error:%s", ss)
		return errors.New("dbname is unknown")
	}
	actor.state.DBName = ss[0]
	actor.state.DSType, err = model.DSTypeFromString(audit.DbType)
	if err != nil {
		log.Error(ctx, "DSType fail, error:%s", err)
		return err
	}
	actor.state.Region = audit.Region
	tlsRegion, err := actor.conf.Get(ctx).GetTLSRegion(i18n.GetSiteName(ctx, actor.conf.Get(ctx).FullSqlInnerAccountId))
	if err != nil {
		log.Error(ctx, "请检查运维面参数:TlsServiceEndpoint, tls region err:%s", err)
		return err
	}
	actor.state.TlsRegion = tlsRegion
	tlsEndpoint := actor.conf.Get(ctx).GetTlsRegionEndpoint(i18n.GetSiteName(ctx, actor.conf.Get(ctx).FullSqlInnerAccountId))
	actor.state.TlsEndpoint = tlsEndpoint
	actor.state.TlsDataType = model.StatisticDataType_FullSqlDetail
	nodes, err := actor.instanceExtraNodeDAL.GetNodes(ctx, audit.InstanceID)
	if err != nil {
		log.Error(ctx, "get instance extra nodes fail, error:%s", err)
		return err
	}
	var extraNodes []string
	for _, node := range nodes {
		extraNodes = append(extraNodes, node.NodeId)
	}
	actor.state.Nodes = extraNodes
	log.Info(ctx, fmt.Sprintf("Init params:[%s]", libutils.Show(actor.state)))
	return nil
}

func (p PrepareDeleteInnerFullSqlInstanceStateStep) MaxExecRetry() int {
	return 10
}

func (p PrepareDeleteInnerFullSqlInstanceStateStep) GetStepName() string {
	return "PrepareDeleteInnerFullSqlInstanceStateStep"
}
