package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

var deleteFullSqlSteps = map[string]IdempotentStep{
	"PrepareDeleteFullSqlInstanceStateStep": &PrepareDeleteFullSqlInstanceStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				switch a.state.InstanceStatus {
				case model.AuditStatus_Deleting:
					// 已经在删除的流程中了
					log.Info(ctx, "instance is in deleting process.")
					return nil
				case model.AuditStatus_Deleted:
					log.Info(ctx, "instance is in deleting process.")
					return nil
				}
				return a.idempotentSteps["UpdateFullSqlInstanceStatusDeletingStep"]
			},
		},
	},
	"UpdateFullSqlInstanceStatusDeletingStep": &UpdateFullSqlInstanceStatusDeletingStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["NotifyDeleteOrderStep"]
			},
		},
	},
	"NotifyDeleteOrderStep": &NotifyDeleteOrderStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CancelConfigStatisticPipelineStep"]
			},
		},
	},
	"CancelConfigStatisticPipelineStep": &CancelConfigStatisticPipelineStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ClearSqlTemplateCacheStep"]
			},
		},
	},
	"ClearSqlTemplateCacheStep": &ClearSqlTemplateCacheStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["RemoveResourceStep"]
			},
		},
	},
	"RemoveResourceStep": &RemoveResourceStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["RemoveCollectionFingerprintStep"]
			},
		},
	},
	"RemoveCollectionFingerprintStep": &RemoveCollectionFingerprintStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FullSqlInstanceGCStep"]
			},
		},
	},
	"FullSqlInstanceGCStep": &FullSqlInstanceGCStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}

func NewFullSqlDeleteLifecycleActor(in NewAuditCreateLifecycleActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.MysqlFullSqlDeleteActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &AuditLifecycleActor{
				state:           newTaskState(state),
				idempotentSteps: deleteFullSqlSteps,

				source:               in.DataSource,
				crossAuthSvc:         in.CrossAuthSvc,
				conf:                 in.Conf,
				c3Conf:               in.C3Conf,
				loc:                  in.Loc,
				tagSvc:               in.TagSvc,
				projectSvc:           in.ProjectSvc,
				tlsDAL:               in.TlsDAL,
				lcSvc:                in.LCSvc,
				billSvc:              in.BillSvc,
				publishEventSvc:      in.PublishEventSvc,
				fullSqlInstDAL:       in.FullSqlInstDAL,
				fullSqlSvc:           in.FullSqlSvc,
				fullSqlConfigService: in.FullSqlConfigService,
			}
			return actor
		}),
	}
}
