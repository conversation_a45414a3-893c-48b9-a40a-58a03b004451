package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

var deleteInnerFullSqlSteps = map[string]IdempotentStep{
	"PrepareDeleteInnerFullSqlInstanceStateStep": &PrepareDeleteInnerFullSqlInstanceStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				switch a.state.InstanceStatus {
				case model.AuditStatus_Deleting:
					// 已经在删除的流程中了
					log.Info(ctx, "instance is in deleting process.")
					return nil
				case model.AuditStatus_Deleted:
					log.Info(ctx, "instance is in deleting process.")
					return nil
				}
				return a.idempotentSteps["UpdateFullSqlInstanceStatusDeletingStep"]
			},
		},
	},
	"UpdateFullSqlInstanceStatusDeletingStep": &UpdateFullSqlInstanceStatusDeletingStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CancelConfigStatisticPipelineStep"]
			},
		},
	},
	"CancelConfigStatisticPipelineStep": &CancelConfigStatisticPipelineStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CancelConfigPipelineStep"]
			},
		},
	},
	"CancelConfigPipelineStep": &CancelConfigPipelineStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ClearSqlTemplateInnerCacheStep"]
			},
		},
	},
	"ClearSqlTemplateInnerCacheStep": &ClearSqlTemplateInnerCacheStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CloseInnerFullSqlLogSwitchStep"]
			},
		},
	},
	"CloseInnerFullSqlLogSwitchStep": &CloseInnerFullSqlLogSwitchStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FullSqlInnerInstanceGCStep"]
			},
		},
	},
	"FullSqlInnerInstanceGCStep": &FullSqlInnerInstanceGCStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}

func NewInnerFullSqlDeleteLifecycleActor(in NewInnerFullSqlLifecycleActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.InnerMysqlFullSqlDeleteActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &AuditLifecycleActor{
				state:           newTaskState(state),
				idempotentSteps: deleteInnerFullSqlSteps,

				source:               in.DataSource,
				crossAuthSvc:         in.CrossAuthSvc,
				conf:                 in.Conf,
				c3Conf:               in.C3Conf,
				loc:                  in.Loc,
				tlsDAL:               in.TlsDAL,
				publishEventSvc:      in.PublishEventSvc,
				fullSqlInstDAL:       in.FullSqlInstDAL,
				fullSqlSvc:           in.FullSqlSvc,
				fullSqlConfigService: in.FullSqlConfigService,
				instanceExtraNodeDAL: in.InstanceExtraNodeDAL,
				genTlsSvc:            in.GenTlsSvc,
			}
			return actor
		}),
	}
}
