package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

var createInnerFullSqlSteps = map[string]IdempotentStep{
	// 创建实例
	// PreCheck 实例状态
	"PrepareInnerFullSqlInstanceStateStep": &PrepareInnerFullSqlInstanceStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["UpdateFullSqlInstanceStatusCreatingStep"]
			},
		},
	},
	// UpdateCreatingStatue
	"UpdateFullSqlInstanceStatusCreatingStep": &UpdateFullSqlInstanceStatusCreatingStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectStep"]
			},
		},
	},

	// 获取或者创建TLS Project
	"FetchInnerProjectStep": &FetchInnerProjectStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicStep": &CreateInnerTopicStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoStep": &SaveInnerTLSInfoStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareInnerTLSIndexStep"]
			},
		},
	},
	// 创建索引
	"PrepareInnerTLSIndexStep": &PrepareInnerTLSIndexStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectSqlTemplateStep"]
			},
		},
	},

	// 创建sql模版TLS资源
	// 获取或者创建TLS Project
	"FetchInnerProjectSqlTemplateStep": &FetchInnerProjectSqlTemplateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicSqlTemplateStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicSqlTemplateStep": &CreateInnerTopicSqlTemplateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoSqlTemplateStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoSqlTemplateStep": &SaveInnerTLSInfoSqlTemplateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareFullSqlTLSIndexSqlTemplateStep"]
			},
		},
	},
	// 创建索引
	"PrepareFullSqlTLSIndexSqlTemplateStep": &PrepareFullSqlTLSIndexSqlTemplateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectSqlCountStep"]
			},
		},
	},

	// 创建sql指标TLS资源
	// 获取或者创建TLS Project
	"FetchInnerProjectSqlCountStep": &FetchInnerProjectSqlCountStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicSqlCountStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicSqlCountStep": &CreateInnerTopicSqlCountStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoSqlCountStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoSqlCountStep": &SaveInnerTLSInfoSqlCountStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareFullSqlTLSIndexSqlCountStep"]
			},
		},
	},
	// 创建索引
	"PrepareFullSqlTLSIndexSqlCountStep": &PrepareFullSqlTLSIndexSqlCountStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectSqlTableStep"]
			},
		},
	},

	// 创建sql指标TLS资源
	// 获取或者创建TLS Project
	"FetchInnerProjectSqlTableStep": &FetchInnerProjectSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicSqlTableStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicSqlTableStep": &CreateInnerTopicSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoSqlTableStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoSqlTableStep": &SaveInnerTLSInfoSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareFullSqlTLSIndexSqlTableStep"]
			},
		},
	},
	// 创建索引
	"PrepareFullSqlTLSIndexSqlTableStep": &PrepareFullSqlTLSIndexSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ConfigInnerConsumerPipelineStep"]
			},
		},
	},
	// 配置消费链路信息（配置zookeeper）仅tls output
	"ConfigInnerConsumerPipelineStep": &ConfigInnerConsumerPipelineStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectTopSqlStep"]
			},
		},
	},

	// 创建TopN tls的信息
	// 获取或者创建TLS Project
	"FetchInnerProjectTopSqlStep": &FetchInnerProjectTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicTopSqlStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicTopSqlStep": &CreateInnerTopicTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoTopSqlStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoTopSqlStep": &SaveInnerTLSInfoTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareFullSqlTLSIndexTopSqlStep"]
			},
		},
	},
	// 创建索引
	"PrepareFullSqlTLSIndexTopSqlStep": &PrepareFullSqlTLSIndexTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ConfigStatisticPipelineStep"]
			},
		},
	},

	// 配置统计TLS信息（配置zookeeper）
	"ConfigStatisticPipelineStep": &ConfigStatisticPipelineStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["OpenInnerFullSqlLogSwitchStep"]
			},
		},
	},

	// 开启dbname对应实例列表内核-RdsAgent采集并获取RdsTLS资源
	"OpenInnerFullSqlLogSwitchStep": &OpenInnerFullSqlLogSwitchStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerFullSqlInstancesStep"]
			},
		},
	},
	// 记录内场开启实例列表 [ip:port]
	"SaveInnerFullSqlInstancesStep": &SaveInnerFullSqlInstancesStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["UpdateInstanceStatusRunningStep"]
			},
		},
	},

	"UpdateInstanceStatusRunningStep": &UpdateInstanceStatusRunningStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}

// NewFullSqlCreateLifecycleActor MySQL 创建
func NewInnerFullSqlCreateLifecycleActor(in NewInnerFullSqlLifecycleActorIn) types.VirtualPersistenceProducer {

	return types.VirtualPersistenceProducer{
		Kind: consts.InnerMysqlFullSqlCreateActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &AuditLifecycleActor{
				state:           newTaskState(state),
				idempotentSteps: createInnerFullSqlSteps,

				source:               in.DataSource,
				crossAuthSvc:         in.CrossAuthSvc,
				conf:                 in.Conf,
				c3Conf:               in.C3Conf,
				loc:                  in.Loc,
				tlsDAL:               in.TlsDAL,
				publishEventSvc:      in.PublishEventSvc,
				fullSqlInstDAL:       in.FullSqlInstDAL,
				fullSqlSvc:           in.FullSqlSvc,
				fullSqlConfigService: in.FullSqlConfigService,
				instanceExtraTlsDAL:  in.InstanceExtraTlsDAL,
				instanceExtraNodeDAL: in.InstanceExtraNodeDAL,
				genTlsSvc:            in.GenTlsSvc,
			}
			return actor
		}),
	}
}
