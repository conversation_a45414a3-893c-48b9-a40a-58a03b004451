package audit

import (
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	tls_sdk "github.com/volcengine/volc-sdk-golang/service/tls"
	"testing"

	local_dal "code.byted.org/infcs/dbw-mgr/biz/test/dal"
	mocks_def "code.byted.org/infcs/dbw-mgr/biz/test/service"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"

	"github.com/stretchr/testify/suite"
)

type FullSqlStepTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *FullSqlStepTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}
func (suite *FullSqlStepTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}
func TestFullSqlStepSuite(t *testing.T) {
	suite.Run(t, new(FullSqlStepTestSuite))
}

func (suite *FullSqlStepTestSuite) TestNewFullSqlStep() {
	PatchConvey("", suite.T(), func() {
		ctx := context.TODO()
		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "2100000746"})

		local := &local_dal.LocalAudit{}
		local.Init(ctx, suite.ctrl)

		def := mocks_def.DefaultMock{}
		def.Init(suite.ctrl)
		So(ctx, ShouldNotBeNil)
	})
}

func TestConfigConsumerPipelineStep_getTlsKafkaEndpoint(t *testing.T) {
	type fields struct {
		BaseStep BaseStep
	}
	type args struct {
		tlsConsumerEndpoint string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "",
			fields: fields{
				BaseStep: BaseStep{},
			},
			args: args{
				tlsConsumerEndpoint: "http://tls-cn-chongqing-sdv-inner.ivolces.com",
			},
			want: "tls-cn-chongqing-sdv-inner.ivolces.com:9093",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getTlsKafkaEndpoint(tt.args.tlsConsumerEndpoint); got != tt.want {
				t.Errorf("getTlsKafkaEndpoint() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCleanCloseIndexKey(t *testing.T) {
	type args struct {
		value  *[]tls_sdk.KeyValueInfo
		dsType model.DSType
		flag   model.CapabilitiesFlag
	}
	redisIndexReq := &tls_sdk.CreateIndexRequest{
		TopicID: "topic-xxx",
		KeyValue: &[]tls_sdk.KeyValueInfo{
			{
				Key: "time",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "latency",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "type",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "account",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "cmd",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "!@#%^&*()-_=\\\"', <>/?|;: \\n\\t\\r[]{}",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "db",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "ip",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			// 预留字段
			//{
			//	Key: "__pod_name__",
			//	Value: tls_sdk.Value{
			//		ValueType:      "text",
			//		Delimiter:      "",
			//		CasSensitive:   false,
			//		IncludeChinese: false,
			//		SQLFlag:        true,
			//	},
			//},
			// tag
			{
				Key: "__tag__ResourceId__",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "__tag__component__",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
		},
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "",
			args: args{
				value:  redisIndexReq.KeyValue,
				dsType: model.DSType_Redis,
				flag:   model.CapabilitiesFlag_QueryDetailKeyword,
			},
			want: "cmd",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			KeyValue := CleanCloseIndexKey(tt.args.value, tt.args.dsType, tt.args.flag)
			for _, info := range *KeyValue {
				if info.Key == tt.want {
					t.Error("clean failed")
				}
			}
		})
	}
}
