package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

// 升级全量SQL
var upgradeFullSqlUpgradeTableAggrSteps = map[string]IdempotentStep{
	// 创建实例
	// PreCheck 实例状态
	"PrepareFullSqlInstanceUpgradeStateStep": &PrepareFullSqlInstanceUpgradeStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ModifyFullSqlTLSIndexSqlTemplateStep"]
			},
		},
	},

	// 修改索引
	"ModifyFullSqlTLSIndexSqlTemplateStep": &ModifyFullSqlTLSIndexSqlTemplateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ModifyFullSqlTLSIndexSqlCountStep"]
			},
		},
	},
	// 修改索引
	"ModifyFullSqlTLSIndexSqlCountStep": &ModifyFullSqlTLSIndexSqlCountStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectSqlTableStep"]
			},
		},
	},

	// 创建sql指标TLS资源
	// 获取或者创建TLS Project
	"FetchInnerProjectSqlTableStep": &FetchInnerProjectSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicSqlTableStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicSqlTableStep": &CreateInnerTopicSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoSqlTableStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoSqlTableStep": &SaveInnerTLSInfoSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareFullSqlTLSIndexSqlTableStep"]
			},
		},
	},
	// 创建索引
	"PrepareFullSqlTLSIndexSqlTableStep": &PrepareFullSqlTLSIndexSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ConfigStatisticPipelineStep"]
			},
		},
	},

	// 配置统计TLS信息（配置zookeeper）
	"ConfigStatisticPipelineStep": &ConfigStatisticPipelineStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}

// NewFullSqlCreateLifecycleActor MySQL 创建
func NewMysqlFullSqlUpgradeTableAggrLifecycleActor(in NewAuditCreateLifecycleActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.MysqlFullSqlUpgradeTableAggrActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &AuditLifecycleActor{
				state:           newTaskState(state),
				idempotentSteps: upgradeFullSqlUpgradeTopSqlSteps,

				source:               in.DataSource,
				crossAuthSvc:         in.CrossAuthSvc,
				conf:                 in.Conf,
				c3Conf:               in.C3Conf,
				loc:                  in.Loc,
				tagSvc:               in.TagSvc,
				projectSvc:           in.ProjectSvc,
				tlsDAL:               in.TlsDAL,
				lcSvc:                in.LCSvc,
				billSvc:              in.BillSvc,
				publishEventSvc:      in.PublishEventSvc,
				auditService:         in.AuditService,
				genTlsSvc:            in.GenTlsSvc,
				fullSqlInstDAL:       in.FullSqlInstDAL,
				fullSqlSvc:           in.FullSqlSvc,
				fullSqlConfigService: in.FullSqlConfigService,
				instanceExtraTlsDAL:  in.InstanceExtraTlsDAL,
			}
			return actor
		}),
	}
}
