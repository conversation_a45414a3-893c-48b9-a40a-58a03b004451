package audit

import (
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

type ModifyFullSqlTLSIndexTableStatisticStep struct {
	BaseStep
}

func (p ModifyFullSqlTLSIndexTableStatisticStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTemplateTlsConf := a.state.SqlTableAggrTls
	return modifyIndexProtectExec(ctx, a, SqlTemplateTlsConf, a.state.DSType)
}

func (p ModifyFullSqlTLSIndexTableStatisticStep) MaxExecRetry() int {
	return 10
}

func (p ModifyFullSqlTLSIndexTableStatisticStep) GetStepName() string {
	return "ModifyFullSqlTLSIndexTableStatisticStep"
}

type ModifyFullSqlTLSIndexTopSqlStep struct {
	BaseStep
}

func (p ModifyFullSqlTLSIndexTopSqlStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTopTlsConf := a.state.SqlTopTls
	return modifyIndexProtectExec(ctx, a, SqlTopTlsConf, a.state.DSType)
}

func (p ModifyFullSqlTLSIndexTopSqlStep) MaxExecRetry() int {
	return 10
}

func (p ModifyFullSqlTLSIndexTopSqlStep) GetStepName() string {
	return "ModifyFullSqlTLSIndexTopSqlStep"
}
