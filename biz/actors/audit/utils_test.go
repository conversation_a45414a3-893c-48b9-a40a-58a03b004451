package audit

import (
	"fmt"
	"testing"
)

func TestDrawStepTrace(t *testing.T) {
	type args struct {
		data [][]string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "",
			args: args{
				data: [][]string{
					{"AStep", "Doing something", "2023-10-18 00:00:00.000000"},
					{"BStep", "Doing another things", "2023-10-18 00:00:00.123456"},
				},
			},
			want: "+-------+----------------------+----------------------------+\n| STEP  |       COMMENT        |         TIMESTAMP          |\n+-------+----------------------+----------------------------+\n| AStep | Doing something      | 2023-10-18 00:00:00.000000 |\n| BStep | Doing another things | 2023-10-18 00:00:00.123456 |\n+-------+----------------------+----------------------------+\n",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := DrawStepTrace(tt.args.data)
			fmt.Println(got)
			if got != tt.want {
				t.Errorf("DrawStepTrace() = \n%s, want %s", got, tt.want)
			}
		})
	}
}
