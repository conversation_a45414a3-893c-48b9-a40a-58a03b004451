package audit

import (
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

type CreateInnerTopicTopSqlStep struct {
	BaseStep
}

func (p CreateInnerTopicTopSqlStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.SqlTopTls.TlsTopic == "" {
		return false, nil
	} else {
		return true, nil
	}
}

func (p CreateInnerTopicTopSqlStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTopTls := a.state.SqlTopTls
	return topicProectExec(ctx, a, SqlTopTls)
}

func (p CreateInnerTopicTopSqlStep) MaxExecRetry() int {
	return 10
}

func (p CreateInnerTopicTopSqlStep) GetStepName() string {
	return "CreateInnerTopicTopSqlStep"
}
