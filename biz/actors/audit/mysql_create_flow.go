package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

var createSteps = map[string]IdempotentStep{
	// 创建实例
	// PreCheck 实例状态
	"PrepareInstanceStateStep": &PrepareInstanceStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				switch a.state.InstanceStatus {
				case model.AuditStatus_Running:
					// 补单情况
					return a.idempotentSteps["NotifyCreateOrderStep"]
				case model.AuditStatus_OrderNew:
					// 更新实例状态
					return a.idempotentSteps["UpdateInstanceStatusCreatingStep"]
				default:
					// 其他状态直接返回
				}
				return nil
			},
		},
	},
	// UpdateCreatingStatue
	"UpdateInstanceStatusCreatingStep": &UpdateInstanceStatusCreatingStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareIAMProjectStep"]
			},
		},
	},
	// AddProject
	"PrepareIAMProjectStep": &PrepareIAMProjectStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareIAMProjectTagStep"]
			},
		},
	},
	// AddSystemTag
	"PrepareIAMProjectTagStep": &PrepareIAMProjectTagStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["TagResourceStep"]
		}},
	},
	// Add custom tag 添加用户标签
	"TagResourceStep": &TagResourceStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["PrepareTLSProjectStep"]
		}},
	},
	// 获取或者创建TLS Project
	"PrepareTLSProjectStep": &PrepareTLSProjectStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["PrepareTLSTopicStep"]
		}},
	},
	// 创建TLS topic
	"PrepareTLSTopicStep": &PrepareTLSTopicStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["SaveTLSInfoStep"]
		}},
	},
	// 保存TLS入库
	"SaveTLSInfoStep": &SaveTLSInfoStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["PrepareTLSIndexStep"]
		}},
	},
	// 创建索引
	"PrepareTLSIndexStep": &PrepareTLSIndexStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			if utils.InWriteList(a.state.FollowInstanceId, a.conf.Get(ctx).AuditSqlInsightInstanceList) {
				return a.idempotentSteps["SqlStatisticStep"]
			} else {
				return a.idempotentSteps["CreateNetworkCollectorPodResourceStep"]
			}
		}},
	},
	// 全量SQL统计
	"SqlStatisticStep": &SqlStatisticStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateNetworkCollectorPodResourceStep"]
			},
		},
	},
	// 创建网络采集器
	"CreateNetworkCollectorPodResourceStep": &CreateNetworkCollectorPodResourceStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["UpdateInstanceStatusBuildStep"]
			},
		},
	},
	//
	"UpdateInstanceStatusBuildStep": &UpdateInstanceStatusBuildStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["WaitCollectorReadyStep"]
			},
		},
	},

	// 等待采集资源部署完成
	"WaitCollectorReadyStep": &WaitCollectorReadyStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["NotifyCreateOrderStep"]
			},
		},
	},

	// 通知订单完成
	"NotifyCreateOrderStep": &NotifyCreateOrderStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}

// 创建审计日志（网络采集器版）写入内部TLS TOPIC
//var createStepsV2 = map[string]IdempotentStep{
//	// 创建实例
//	// PreCheck 实例状态
//	"PrepareInstanceStateStep": &PrepareInstanceStateStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				switch a.state.InstanceStatus {
//				case model.AuditStatus_Running:
//					// 补单情况
//					return a.idempotentSteps["NotifyCreateOrderStep"]
//				case model.AuditStatus_OrderNew:
//					// 更新实例状态
//					return a.idempotentSteps["UpdateInstanceStatusCreatingStep"]
//				default:
//					// 其他状态直接返回
//				}
//				return nil
//			},
//		},
//	},
//	// UpdateCreatingStatue
//	"UpdateInstanceStatusCreatingStep": &UpdateInstanceStatusCreatingStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				return a.idempotentSteps["PrepareIAMProjectStep"]
//			},
//		},
//	},
//	// AddProject
//	"PrepareIAMProjectStep": &PrepareIAMProjectStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				return a.idempotentSteps["PrepareIAMProjectTagStep"]
//			},
//		},
//	},
//	// AddSystemTag
//	"PrepareIAMProjectTagStep": &PrepareIAMProjectTagStep{
//		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//			return a.idempotentSteps["TagResourceStep"]
//		}},
//	},
//	// Add custom tag 添加用户标签
//	"TagResourceStep": &TagResourceStep{
//		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//			return a.idempotentSteps["FetchInnerProjectStep"]
//		}},
//	},
//	// 获取或者创建TLS Project
//	"FetchInnerProjectStep": &FetchInnerProjectStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				return a.idempotentSteps["CreateInnerTopicStep"]
//			},
//		},
//	},
//
//	// 创建TLS topic
//	"CreateInnerTopicStep": &CreateInnerTopicStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				return a.idempotentSteps["SaveInnerTLSInfoStep"]
//			},
//		},
//	},
//
//	// 保存TLS入库
//	"SaveInnerTLSInfoStep": &SaveInnerTLSInfoStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				return a.idempotentSteps["PrepareInnerTLSIndexStep"]
//			},
//		},
//	},
//	// 创建索引
//	"PrepareInnerTLSIndexStep": &PrepareInnerTLSIndexStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				return a.idempotentSteps["CreateNetworkCollectorPodResourceStep"]
//			},
//		},
//	},
//
//	// 创建网络采集器
//	"CreateNetworkCollectorPodResourceStep": &CreateNetworkCollectorPodResourceStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				return a.idempotentSteps["UpdateInstanceStatusBuildStep"]
//			},
//		},
//	},
//	//
//	"UpdateInstanceStatusBuildStep": &UpdateInstanceStatusBuildStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				return a.idempotentSteps["WaitCollectorReadyStep"]
//			},
//		},
//	},
//
//	// 等待采集资源部署完成
//	"WaitCollectorReadyStep": &WaitCollectorReadyStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				return a.idempotentSteps["NotifyCreateOrderStep"]
//			},
//		},
//	},
//
//	// 通知订单完成
//	"NotifyCreateOrderStep": &NotifyCreateOrderStep{
//		BaseStep: BaseStep{
//			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
//				return nil
//			},
//		},
//	},
//}

// NewAuditMysqlCreateLifecycleActor MySQL/VeDB 创建
func NewAuditMysqlCreateLifecycleActor(in NewAuditCreateLifecycleActorIn) types.VirtualPersistenceProducer {
	// 组装垃圾回收子流
	GlobalStepLock.Lock()
	for s, step := range auditCreateGCSteps {
		createSteps[s] = step
	}
	GlobalStepLock.Unlock()

	return types.VirtualPersistenceProducer{
		Kind: consts.MysqlAuditCreateActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &AuditLifecycleActor{
				state:           newTaskState(state),
				idempotentSteps: createSteps,

				source:          in.DataSource,
				crossAuthSvc:    in.CrossAuthSvc,
				conf:            in.Conf,
				c3Conf:          in.C3Conf,
				loc:             in.Loc,
				auditTlsDAL:     in.AuditTlsDAL,
				genTlsSvc:       in.GenTlsSvc,
				tagSvc:          in.TagSvc,
				projectSvc:      in.ProjectSvc,
				auditService:    in.AuditService,
				tlsDAL:          in.TlsDAL,
				lcSvc:           in.LCSvc,
				billSvc:         in.BillSvc,
				publishEventSvc: in.PublishEventSvc,

				statisticSqlTlsDAL:  in.StatisticSqlTlsDAL,
				statisticSqlTaskDAL: in.StatisticSqlTaskDAL,
			}
			return actor
		}),
	}
}
