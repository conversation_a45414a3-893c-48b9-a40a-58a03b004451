package audit

import (
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

type SaveInnerTLSInfoTopSqlStep struct {
	BaseStep
}

func (s SaveInnerTLSInfoTopSqlStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (s SaveInnerTLSInfoTopSqlStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	TopSqlAggrTls := a.state.SqlTopTls
	return tlsInfoSaveProtectExec(ctx, a, TopSqlAggrTls, a.state.InstanceId, a.state.TenantId)
}

func (s SaveInnerTLSInfoTopSqlStep) MaxExecRetry() int {
	return 10
}

func (s SaveInnerTLSInfoTopSqlStep) GetStepName() string {
	return "SaveInnerTLSInfoTopSqlStep"
}
