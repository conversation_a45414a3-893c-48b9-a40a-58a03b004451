package audit

import (
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

var CreateFullSqlGCSteps = map[string]IdempotentStep{
	"NotifyCreateOrderFailStep": &NotifyCreateOrderFailStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["UpdateFullSqlInstanceStatusDeletingStep"]
			},
		},
	},
	"UpdateFullSqlInstanceStatusDeletingStep": &UpdateFullSqlInstanceStatusDeletingStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CancelConfigStatisticPipelineStep"]
			},
		},
	},
	"CancelConfigStatisticPipelineStep": &CancelConfigStatisticPipelineStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ClearSqlTemplateCacheStep"]
			},
		},
	},
	"ClearSqlTemplateCacheStep": &ClearSqlTemplateCacheStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["RemoveResourceStep"]
			},
		},
	},
	// Remove IAM Project
	"RemoveResourceStep": &RemoveResourceStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FullSqlRemoveTopicsStep"]
			},
		},
	},
	"FullSqlRemoveTopicsStep": &FullSqlRemoveTopicsStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["RemoveCollectionFingerprintStep"]
			},
		},
	},
	"RemoveCollectionFingerprintStep": &RemoveCollectionFingerprintStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FullSqlInstanceGCStep"]
			},
		},
	},
	"FullSqlInstanceGCStep": &FullSqlInstanceGCStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}
