package audit

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls/tls_enhance"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/google/uuid"
	tls_sdk "github.com/volcengine/volc-sdk-golang/service/tls"
	"gorm.io/gorm"
)

type WaitCollectorReadyStep struct {
	BaseStep
}

func (b WaitCollectorReadyStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (b WaitCollectorReadyStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	// 更新实例状态的逻辑在控制器侧，这里只检查元数据状态
	var status model.AuditStatus
	err := libutils.Retry(ctx, func(ctx context.Context) error {
		auditInst, err := a.auditTlsDAL.GetByID(ctx, a.state.InstanceId, a.state.TenantId)
		if err != nil {
			log.Warn(ctx, "get audit instance failed. %s", err)
			return err
		}
		status = model.AuditStatus(auditInst.Status)
		a.state.InstanceStatus = status
		if auditInst.Status < int32(model.AuditStatus_Running) {
			return consts.ErrorOf(model.ErrorCode_WaitForRetryError)
		}
		return nil
	}, func(params *libutils.RetryParams) {
		params.RetryFailedInterval = time.Second * 5
		params.MaxTimes = 1440
	})
	if err == nil && int32(status) > int32(model.AuditStatus_Running) {
		return errors.New("audit instance statue larger than Running")
	}
	return err
}

func (b WaitCollectorReadyStep) MaxExecRetry() int {
	return 5
}

func (b WaitCollectorReadyStep) ErrorWait() time.Duration {
	return time.Second * 60
}

func (b WaitCollectorReadyStep) GetStepName() string {
	return "WaitCollectorReadyStep"
}

type CreateNetworkCollectorPodResourceStep struct {
	BaseStep
}

func (b CreateNetworkCollectorPodResourceStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (b CreateNetworkCollectorPodResourceStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	for _, clusterName := range a.state.AzClusterMap {
		err := a.auditService.CreatePodInCluster(ctx, a.state.FollowInstanceId, clusterName,
			a.state.CollectPort, a.state.CollectPodCpuRequest, a.state.DSType)
		if err != nil {
			log.Error(ctx, "create resouce error, %s", err)
			return err
		}
	}
	return nil
}

func (b CreateNetworkCollectorPodResourceStep) MaxExecRetry() int {
	return 1
}

func (b CreateNetworkCollectorPodResourceStep) ErrorWait() time.Duration {
	return time.Second * 1
}

func (b CreateNetworkCollectorPodResourceStep) GetStepName() string {
	return "CreateNetworkCollectorPodResourceStep"
}

type UpdateInstanceStatusBuildStep struct {
	BaseStep
}

func (u UpdateInstanceStatusBuildStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	a.state.InstanceStatus = model.AuditStatus_Build
	err := a.auditTlsDAL.UpdateByFollowInstanceID(ctx, &dao.AuditTls{
		FollowInstanceID: a.state.FollowInstanceId,
		TenantID:         a.state.TenantId,
		Status:           int32(a.state.InstanceStatus),
	})
	return err
}

func (u UpdateInstanceStatusBuildStep) MaxExecRetry() int {
	return 10
}

func (u UpdateInstanceStatusBuildStep) GetStepName() string {
	return "UpdateInstanceStatusBuildStep"
}

type SqlStatisticStep struct {
	BaseStep
}
type StatisticTlsMeta struct {
	Id           int64
	Region       string
	TenantId     string
	DataType     model.StatisticDataType
	TlsId        int64
	ProjectId    string
	TopicId      string
	IndexVersion int64
	DsType       model.DSType
	SqlTasks     map[model.ScheduleSqlTaskType]*ScheduleSqlTaskMeta // k--任务名称 v--任务元信息
}

type ScheduleSqlTaskMeta struct {
	Type    model.ScheduleSqlTaskType
	Id      string
	Version int64

	SqlTaskId string
}

var CurProcessSqlDelay int64 = 60
var CurProcessTimeWindow = "@m-6m,@m-5m"
var RequestCycleTime int64 = 1
var StatisticTlsTTL uint16 = 14

var currentStatisticTlsMeta = map[model.DSType]map[model.StatisticDataType]*StatisticTlsMeta{
	model.DSType_MySQL: { // DsType
		model.StatisticDataType_SqlExecCount: { // sql 耗时分布
			IndexVersion: 1,
			SqlTasks: map[model.ScheduleSqlTaskType]*ScheduleSqlTaskMeta{
				model.ScheduleSqlTaskType_SqlExecTotolCount: { // 每分钟SQL总数
					Type:    model.ScheduleSqlTaskType_SqlExecTotolCount,
					Version: 1,
				},
				//model.ScheduleSqlTaskType_SqlExecL1: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL1,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecL2: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL2,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecL3: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL3,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecL10: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL10,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecL100: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL100,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecL1000: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL1000,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecG1000: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecG1000,
				//	Version: 1,
				//},
				model.ScheduleSqlTaskType_SqlExecDistribution: {
					Type:    model.ScheduleSqlTaskType_SqlExecDistribution,
					Version: 1,
				},
			},
		},
		model.StatisticDataType_SqlTemplateAggregate: { // 耗时分布统计
			IndexVersion: 1,
			SqlTasks: map[model.ScheduleSqlTaskType]*ScheduleSqlTaskMeta{
				model.ScheduleSqlTaskType_SqlTemplateAggregate: {
					Type:    model.ScheduleSqlTaskType_SqlTemplateAggregate,
					Version: 1,
				},
			},
		},
	},
	model.DSType_VeDBMySQL: { // DsType
		model.StatisticDataType_SqlExecCount: { // sql 耗时分布
			IndexVersion: 1,
			SqlTasks: map[model.ScheduleSqlTaskType]*ScheduleSqlTaskMeta{
				model.ScheduleSqlTaskType_SqlExecTotolCount: { // 每分钟SQL总数
					Type:    model.ScheduleSqlTaskType_SqlExecTotolCount,
					Version: 1,
				},
				//model.ScheduleSqlTaskType_SqlExecL1: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL1,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecL2: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL2,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecL3: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL3,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecL10: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL10,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecL100: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL100,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecL1000: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecL1000,
				//	Version: 1,
				//},
				//model.ScheduleSqlTaskType_SqlExecG1000: {
				//	Type:    model.ScheduleSqlTaskType_SqlExecG1000,
				//	Version: 1,
				//},
				model.ScheduleSqlTaskType_SqlExecDistribution: {
					Type:    model.ScheduleSqlTaskType_SqlExecDistribution,
					Version: 1,
				},
			},
		},
		model.StatisticDataType_SqlTemplateAggregate: { // 耗时分布统计
			IndexVersion: 1,
			SqlTasks: map[model.ScheduleSqlTaskType]*ScheduleSqlTaskMeta{
				model.ScheduleSqlTaskType_SqlTemplateAggregate: {
					Type:    model.ScheduleSqlTaskType_SqlTemplateAggregate,
					Version: 1,
				},
			},
		},
	},
}

func (b SqlStatisticStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (b SqlStatisticStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) (err error) {
	// 这个方法需要自身幂等
	// 分析用的tls暂时不通过这个方法去清除
	// 如果不存在会去清除sqltask
	tlsClient, err := b.GetTlsClient(ctx, a)
	if err != nil {
		log.Warn(ctx, "err:%s", err)
		return err
	}
	// mgr当前版本信息
	mgrStatisticMap := currentStatisticTlsMeta[a.state.DSType]

	instanceStatisticTls := map[model.StatisticDataType]*StatisticTlsMeta{}
	// 实例目前的统计topic与task信息补全
	err = initStatisticMap(ctx, a, instanceStatisticTls)
	if err != nil {
		log.Warn(ctx, "err:%s", err)
		return err
	}

	// 如果statisticMap的DataType中没有project、topic则新建并回填到数据库中
	for statisticDataType, curStatisticTls := range mgrStatisticMap {
		// 初始化实例统计tls信息
		stls, ok := instanceStatisticTls[statisticDataType]
		if !ok {
			sst := dao.StatisticSqlTls{
				TenantId: a.state.TenantId,
				Region:   a.state.Region,
				DbType:   a.state.DSType.String(),
				DataType: statisticDataType.String(),
			}
			sstId, err := a.statisticSqlTlsDAL.Create(ctx, sst)
			if err != nil {
				log.Warn(ctx, "statisticSqlTls create record err:%s", err)
				return err
			}
			stls = &StatisticTlsMeta{
				Id:       sstId,
				Region:   a.state.Region,
				TenantId: a.state.TenantId,
				DataType: statisticDataType,
				DsType:   a.state.DSType,
			}
			instanceStatisticTls[statisticDataType] = stls
		}

		// 存在的话检查project
		if stls.ProjectId == "" {
			// fetch project
			stls.ProjectId, err = fetchProject(ctx, a, tlsClient)
			if err != nil {
				log.Warn(ctx, "fecth project error:%s", err)
				return err
			}
			log.Info(ctx, "fetch project id:%s", stls.ProjectId)
		}
		if stls.TopicId == "" {
			// create topic
			stls.TopicId, err = createTopic(ctx, a, tlsClient, stls.Id, stls.ProjectId, statisticDataType)
			if err != nil {
				log.Warn(ctx, "create topic error:%s", err)
				return err
			}
			log.Info(ctx, "create topic id:%s", stls.TopicId)
		}
		if stls.IndexVersion < curStatisticTls.IndexVersion {
			// 更新或者创建index
			stls.IndexVersion, err = createOrUpdateIndex(ctx, a, tlsClient, stls.TopicId, statisticDataType, curStatisticTls.IndexVersion)
			if err != nil {
				log.Warn(ctx, "err:%s", err)
				return err
			}
		}
		if instanceStatisticTls[statisticDataType].SqlTasks == nil {
			instanceStatisticTls[statisticDataType].SqlTasks = map[model.ScheduleSqlTaskType]*ScheduleSqlTaskMeta{}
		}

		// 检查是否存在任务，如果任务版本号小于当前值则重建，如果不存在任务则新建
		for sqlTaskName, sqlTaskInfo := range curStatisticTls.SqlTasks {
			task, ok := stls.SqlTasks[sqlTaskName]
			if ok {
				if task.Version < sqlTaskInfo.Version {
					log.Info(ctx, "recreate schedule sql task %s, cuz task-version:[%d] less than MGR-task-verion:[%d], task id:%s", sqlTaskInfo.Type.String(), task.Version, sqlTaskInfo.Version, task.SqlTaskId)
					err = DeleteStatisticSqlTask(ctx, a, tlsClient, task.SqlTaskId)
					if err != nil {
						log.Warn(ctx, "DeleteStatisticSqlTask error:%s", err)
						return err
					}
					err = CreateStatisticSqlTask(ctx, a, tlsClient, instanceStatisticTls[statisticDataType], sqlTaskInfo)
					if err != nil {
						log.Warn(ctx, "CreateStatisticSqlTask error:%s", err)
						return err
					}
				}
			} else {
				log.Info(ctx, "create schedule sql task, %s", sqlTaskInfo.Type.String())
				err := CreateStatisticSqlTask(ctx, a, tlsClient, instanceStatisticTls[statisticDataType], sqlTaskInfo)
				if err != nil {
					log.Warn(ctx, "CreateStatisticSqlTask error:%s", err)
					return err
				}
			}
		}
		//// 检查任务是否存在，如果不存在则删除
		//for taskType, task := range stls.SqlTasks {
		//	if _, ok := curStatisticTls.SqlTasks[taskType]; !ok {
		//		log.Warn(ctx, "DeleteStatisticSqlTask")
		//		err := DeleteStatisticSqlTask(ctx, a, tlsClient, task.SqlTaskId)
		//		if err != nil {
		//			log.Warn(ctx, "DeleteStatisticSqlTask error:%s", err)
		//			return err
		//		}
		//	}
		//}
	}

	return nil
}

func ModifyStatisticSqlTask(ctx types.Context, a *AuditLifecycleActor, tlsClient tls_sdk.Client, id string, taskType string, version int64) error {
	request := &tls_enhance.ModifyScheduleSqlTaskRequest{}
	log.Info(ctx, "ModifyScheduleSqlTaskRequest:%s", libutils.Show(request))
	response, err := tls_enhance.ModifyScheduleSqlTask(tlsClient, request)
	if err != nil {
		log.Info(ctx, "ModifyScheduleSqlTaskResponse:%s, error:%s", libutils.Show(response), err)
		return err
	}
	log.Info(ctx, "ModifyScheduleSqlTaskResponse:%s", libutils.Show(response))

	return nil
}

func CreateStatisticSqlTask(ctx types.Context, a *AuditLifecycleActor, tlsClient tls_sdk.Client, staTls *StatisticTlsMeta, task *ScheduleSqlTaskMeta) error {
	analysisSql := GetStatisticQuery(a.state.FollowInstanceId, a.state.DSType, staTls.DataType, task.Type, task.Version)
	if analysisSql == "" {
		return errors.New("not found statistic query")
	}
	request := &tls_enhance.CreateScheduleSqlTaskRequest{
		TaskName:          GenScheduleSqlTaskName(task.Type, task.Version, a.state.FollowInstanceId),
		Description:       GenScheduleSqlTaskDescribe(a.state.FollowInstanceId, a.state.InstanceId, task.Type),
		TopicID:           a.state.TlsTopic,
		DestTopicID:       staTls.TopicId,
		DestRegion:        a.state.TlsRegion,
		Status:            1,
		ProcessStartTime:  time.Now().Unix(),
		ProcessSqlDelay:   &CurProcessSqlDelay,
		ProcessTimeWindow: CurProcessTimeWindow,
		Query:             analysisSql,
		RequestCycle: tls_enhance.RequestCycle{
			Type: "Period",
			Time: RequestCycleTime,
		},
	}
	log.Info(ctx, "CreateTlsScheduleSqlTaskRequest:%s", libutils.Show(request))
	response, err := tls_enhance.CreateScheduleSqlTask(tlsClient, request)
	if err != nil {
		log.Info(ctx, "CreateTlsScheduleSqlTaskResponse:%s, error:%s", libutils.Show(response), err)
		return err
	}
	log.Info(ctx, "CreateTlsScheduleSqlTaskResponse:%s", libutils.Show(response))

	err = a.statisticSqlTaskDAL.Create(ctx, dao.StatisticSqlTask{
		InstanceId:  a.state.InstanceId,
		StatisticId: int32(staTls.Id),
		TenantId:    a.state.TenantId,
		Type:        task.Type.String(),
		DsType:      a.state.DSType.String(),
		SqlTaskId:   response.TaskId,
		Version:     int32(task.Version),
	})
	if err != nil {
		log.Warn(ctx, "statisticSqlTaskDAL Create error:%s", err)
		return err
	}
	return nil
}

func GenScheduleSqlTaskDescribe(followInstanceId string, instanceId string, taskType model.ScheduleSqlTaskType) string {
	return fmt.Sprintf("由DBW生成定时SQL任务,InstanceID:%s", followInstanceId)
}

func GenScheduleSqlTaskName(taskType model.ScheduleSqlTaskType, version int64, id string) string {
	return fmt.Sprintf("%s-%d-%s", strings.ToLower(taskType.String()), version, id)
}

func DeleteStatisticSqlTask(ctx types.Context, a *AuditLifecycleActor, tlsClient tls_sdk.Client, taskId string) error {
	request := &tls_enhance.DeleteScheduleSqlTaskRequest{
		CommonRequest: tls_sdk.CommonRequest{},
		TaskId:        taskId,
	}
	log.Info(ctx, "DeleteTlsScheduleSqlTaskRequest:%s", libutils.Show(request))
	response, err := tls_enhance.DeleteScheduleSqlTask(tlsClient, request)
	if err != nil && !strings.Contains(err.Error(), "NotExist") {
		log.Warn(ctx, "DeleteTlsScheduleSqlTaskResponse:%s, error:%s", libutils.Show(response), err)
		return err
	}
	log.Info(ctx, "DeleteTlsScheduleSqlTaskResponse:%s", libutils.Show(response))
	err = a.statisticSqlTaskDAL.Delete(ctx, taskId, a.state.TenantId)
	if err != nil {
		log.Warn(ctx, "statisticSqlTaskDAL Delete error:%s", err)
		return err
	}
	return nil
}

// createOrUpdateIndex 创建 或 更新Index
func createOrUpdateIndex(ctx types.Context, a *AuditLifecycleActor, tlsClient tls_sdk.Client, topicId string, dataType model.StatisticDataType, version int64) (int64, error) {
	descIndex, err := tlsClient.DescribeIndex(&tls_sdk.DescribeIndexRequest{
		CommonRequest: tls_sdk.CommonRequest{},
		TopicID:       topicId,
	})
	log.Info(ctx, "DescribeIndexResp:%s , error:%s", libutils.Show(descIndex), err)
	if err != nil {
		if !strings.Contains(err.Error(), "IndexNotExists") {
			return 0, err
		}

		createIndexRequest, err := a.genTlsSvc.GenCreateIndexRequest(ctx, topicId, a.state.DSType, dataType, version)
		if err != nil {
			log.Warn(ctx, "GenCreateIndexRequest error:%s", err)
			return 0, err
		}
		log.Info(ctx, "createIndexRequest : %s", libutils.Show(createIndexRequest))
		createIndexResp, err := tlsClient.CreateIndex(createIndexRequest)
		if err != nil {
			log.Warn(ctx, "create tls index error, topic:%s, error:%s", topicId, err.Error())
			return 0, err
		}
		log.Info(ctx, "createIndexResp : %+v", createIndexResp)
		err = updateTopicIndexMeta(ctx, a, topicId, version)
		if err != nil {
			log.Warn(ctx, "err:%s", err)
			return 0, err
		}
		return version, nil
	}
	createIndexRequest, err := a.genTlsSvc.GenCreateIndexRequest(ctx, topicId, a.state.DSType, dataType, version)
	if err != nil {
		log.Warn(ctx, "GenCreateIndexRequest error:%s", err)
		return 0, err
	}
	modifyIndexRequest := createIndexToModify(createIndexRequest)
	log.Info(ctx, "ModifyIndexRequest : %s", libutils.Show(modifyIndexRequest))
	modifyIndexResp, err := tlsClient.ModifyIndex(modifyIndexRequest)
	if err != nil {
		log.Info(ctx, "ModifyIndex error, topic:%s, error:%s", topicId, err.Error())
		return 0, err
	}
	log.Info(ctx, "ModifyIndex : %+v", modifyIndexResp)
	err = updateTopicIndexMeta(ctx, a, topicId, version)
	if err != nil {
		log.Warn(ctx, "err:%s", err)
		return 0, err
	}
	return version, nil
}

func updateTopicIndexMeta(ctx types.Context, a *AuditLifecycleActor, topicId string, version int64) error {
	return nil
}

func createIndexToModify(request *tls_sdk.CreateIndexRequest) *tls_sdk.ModifyIndexRequest {
	return &tls_sdk.ModifyIndexRequest{
		CommonRequest: tls_sdk.CommonRequest{},
		TopicID:       request.TopicID,
		FullText:      request.FullText,
		KeyValue:      request.KeyValue,
	}
}

func createTopic(ctx types.Context, a *AuditLifecycleActor, tlsClient tls_sdk.Client, stlsId int64, projectId string, statisticDataType model.StatisticDataType) (string, error) {
	createTopicRequest := &tls_sdk.CreateTopicRequest{
		ProjectID:     projectId,
		TopicName:     uuid.New().String(),
		Description:   fmt.Sprintf("%s 数据类别：%s", consts.TLSDescription, statisticDataType.String()),
		Ttl:           StatisticTlsTTL,
		AutoSplit:     true,
		ShardCount:    4,
		MaxSplitShard: libutils.Int32Ref(50),
	}
	log.Info(ctx, "createTopicRequest:%s", createTopicRequest)
	createTopicResponse, err := tlsClient.CreateTopic(createTopicRequest)
	if err != nil {
		log.Warn(ctx, "create topic %s failed, error:%s", createTopicRequest.TopicName, err)
		return "", err
	}
	log.Info(ctx, " create topic %s response:%+v", createTopicRequest.TopicName, createTopicResponse)

	tlsId, err := a.tlsDAL.Create(ctx, &dao.Tls{
		TlsProjectId: projectId,
		TlsTopicId:   createTopicResponse.TopicID,
		TlsEndpoint:  a.state.TlsEndpoint,
		Region:       a.state.TlsRegion,
		TenantID:     a.state.TenantId,
	})
	if err != nil {
		log.Warn(ctx, "err:%s", err)
		return "", err
	}
	sst, err := a.statisticSqlTlsDAL.GetByID(ctx, stlsId)
	if err != nil {
		log.Warn(ctx, "err:%s", err)
		return "", err
	}
	sst.TlsId = tlsId
	err = a.statisticSqlTlsDAL.Update(ctx, *sst)
	if err != nil {
		log.Warn(ctx, "err:%s", err)
		return "", err
	}
	return createTopicResponse.TopicID, nil
}

// fetchProject 仅用于审计日志v1开白功能获取project
func fetchProject(ctx types.Context, a *AuditLifecycleActor, tlsClient tls_sdk.Client) (string, error) {
	// 只从有记录的project获取是否有quota
	pls, err := a.tlsDAL.GetProjects(ctx, a.state.TenantId, a.state.Region)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// need create new
		} else {
			log.Warn(ctx, "failed to get tls project list, err=%v", err)
			return "", err
		}
	}
	// 获取project是否有quota
	for _, pl := range pls {
		project, err := tlsClient.DescribeProject(&tls_sdk.DescribeProjectRequest{
			CommonRequest: tls_sdk.CommonRequest{},
			ProjectID:     pl.TlsProjectId,
		})
		if err != nil {
			if strings.Contains(err.Error(), "ProjectNotExists") {
				continue
			} else {
				log.Warn(ctx, "err:%s", err)
				return "", err
			}
		}
		if project.TopicCount < 50 {
			return project.ProjectID, nil
		}
	}
	// 没有满足的project则创建新project
	projectPrefix := "dbw-sql-audit-"
	createProjectRequest := &tls_sdk.CreateProjectRequest{
		ProjectName: projectPrefix + uuid.New().String(),
		Region:      a.state.TlsRegion,
		Description: consts.TLSDescription,
	}
	createProjectResponse, err := tlsClient.CreateProject(createProjectRequest)
	log.Info(ctx, "createProjectResponse:%+v", createProjectResponse)
	if err != nil {
		log.Warn(ctx, "create project %s failed, error:%s", createProjectRequest.ProjectName, err.Error())
		return "", err
	}
	site := i18n.GetSiteName(ctx, a.state.TenantId)
	_, err = a.tlsDAL.Create(ctx, &dao.Tls{
		TlsProjectId: createProjectResponse.ProjectID,
		Region:       a.state.TlsRegion,
		TenantID:     a.state.TenantId,
		TlsEndpoint:  a.conf.Get(ctx).GetTlsRegionEndpoint(site),
	})
	if err != nil {
		// 元数据存不进去的话就真没啥好办法,人工往库里加数据吧
		log.Error(ctx, "failed to create tls project, err=%v, createProjectResponse.ProjectID:%s", err, createProjectResponse.ProjectID)
	}
	return createProjectResponse.ProjectID, nil
}

func initStatisticMap(ctx types.Context, a *AuditLifecycleActor, statisticMap map[model.StatisticDataType]*StatisticTlsMeta) error {
	log.Info(ctx, "init StatisticMap")
	statisticTls, err := a.statisticSqlTlsDAL.Get(ctx, a.state.TenantId)
	if err != nil {
		log.Warn(ctx, "err:%s", err)
		return err
	}
	log.Info(ctx, "statisticTls : %s", libutils.Show(statisticTls))
	for _, statistic := range statisticTls {
		dbType, err := model.DSTypeFromString(statistic.DbType)
		if err != nil {
			log.Warn(ctx, "err:%s", err)
			return err
		}
		dataType, err := model.StatisticDataTypeFromString(statistic.DataType)
		if err != nil {
			log.Warn(ctx, "err:%s", err)
			return err
		}
		statisticDataType, err := model.StatisticDataTypeFromString(statistic.DataType)
		if err != nil {
			log.Warn(ctx, "err:%s", err)
			return err
		}

		tls, err := a.tlsDAL.GetFromAdmin(ctx, strconv.FormatInt(statistic.TlsId, 10))
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				stm := &StatisticTlsMeta{
					Id:       statistic.Id,
					Region:   statistic.Region,
					TenantId: statistic.TenantId,
					DataType: dataType,
					DsType:   dbType,
					SqlTasks: map[model.ScheduleSqlTaskType]*ScheduleSqlTaskMeta{},
				}
				statisticMap[statisticDataType] = stm
				continue
			}
			log.Warn(ctx, "err:%s", err)
			return err
		}
		statisticTasks, err := a.statisticSqlTaskDAL.GetByInstanceId(ctx, a.state.InstanceId, strconv.FormatInt(statistic.Id, 10), a.state.TenantId)
		if err != nil {
			log.Warn(ctx, "err:%s", err)
			return err
		}
		stm := &StatisticTlsMeta{
			Id:           statistic.Id,
			Region:       statistic.Region,
			TenantId:     statistic.TenantId,
			DataType:     dataType,
			TlsId:        tls.ID,
			ProjectId:    tls.TlsProjectId,
			TopicId:      tls.TlsTopicId,
			IndexVersion: int64(tls.IndexVersion),
			DsType:       dbType,
			SqlTasks:     map[model.ScheduleSqlTaskType]*ScheduleSqlTaskMeta{},
		}
		// 存量task初始化
		for _, task := range statisticTasks {
			taskType, err := model.ScheduleSqlTaskTypeFromString(task.Type)
			if err != nil {
				log.Warn(ctx, "err:%s", err)
				return err
			}
			stm.SqlTasks[taskType] = &ScheduleSqlTaskMeta{
				Type:      taskType,
				Id:        strconv.FormatInt(task.Id, 10),
				Version:   int64(task.Version),
				SqlTaskId: task.SqlTaskId,
			}
		}
		statisticMap[statisticDataType] = stm
	}
	log.Warn(ctx, "statisticMap:%s", libutils.Show(statisticMap))
	return nil
}

func (b SqlStatisticStep) MaxExecRetry() int {
	return 10
}

func (b SqlStatisticStep) ErrorWait() time.Duration {
	return time.Second * 60
}

func (b SqlStatisticStep) GetStepName() string {
	return "SqlStatisticStep"
}

func GetStatisticQuery(instanceID string, dsType model.DSType, dataType model.StatisticDataType, sqlTaskType model.ScheduleSqlTaskType, sqlTaskVersion int64) string {
	switch dsType {
	case model.DSType_VeDBMySQL:
		fallthrough
	case model.DSType_MySQL:
		switch dataType {
		case model.StatisticDataType_SqlExecCount:
			switch sqlTaskType {
			case model.ScheduleSqlTaskType_SqlExecTotolCount:
				switch sqlTaskVersion {
				case 1:
					return fmt.Sprintf("* | select '%s' as instance_id, 'ALL' as type, COUNT(*) as count where mysql_sql_template_md5 != ''", instanceID)
				}
			case model.ScheduleSqlTaskType_SqlExecDistribution:
				switch sqlTaskVersion {
				case 1:
					return fmt.Sprintf("* | SELECT '%s' as instance_id, "+
						" CASE   WHEN event_duration <= 1000000 THEN 'L1'  "+
						"  WHEN event_duration <= 2000000 and event_duration > 1000000 THEN 'L2'  "+
						"  WHEN event_duration <= 3000000 and event_duration > 2000000 THEN 'L3'  "+
						"  WHEN event_duration <= 10000000 and event_duration > 3000000 THEN 'L10'  "+
						"  WHEN event_duration <= 100000000 and event_duration > 10000000 THEN 'L100'  "+
						"  WHEN event_duration <= 1000000000 and event_duration > 100000000 THEN 'L1000'  "+
						"  WHEN event_duration > 1000000000 THEN 'G1000' END AS type,"+
						"  count(*) as count "+
						"WHERE mysql_sql_template_md5 != '' group by type", instanceID)
				}
			case model.ScheduleSqlTaskType_SqlExecL1:
				switch sqlTaskVersion {
				case 1:
					return fmt.Sprintf("* | select '%s' as instance_id, 'L1' as type, count(*) as count where event_duration <= 1000000 and mysql_sql_template_md5 != ''", instanceID)
				}
			case model.ScheduleSqlTaskType_SqlExecL2:
				switch sqlTaskVersion {
				case 1:
					return fmt.Sprintf("* | select '%s' as instance_id, 'L2' as type, count(*) as count where event_duration <= 2000000 and event_duration > 1000000 and mysql_sql_template_md5 != ''", instanceID)
				}
			case model.ScheduleSqlTaskType_SqlExecL3:
				switch sqlTaskVersion {
				case 1:
					return fmt.Sprintf("* | select '%s' as instance_id, 'L3' as type, count(*) as count where event_duration <= 3000000 and event_duration > 2000000 and mysql_sql_template_md5 != ''", instanceID)

				}
			case model.ScheduleSqlTaskType_SqlExecL10:
				switch sqlTaskVersion {
				case 1:
					return fmt.Sprintf("* | select '%s' as instance_id, 'L10' as type, count(*) as count where event_duration <= 10000000 and event_duration > 3000000 and mysql_sql_template_md5 != ''", instanceID)

				}
			case model.ScheduleSqlTaskType_SqlExecL100:
				switch sqlTaskVersion {
				case 1:
					return fmt.Sprintf("* | select '%s' as instance_id, 'L100' as type, count(*) as count where event_duration <= 100000000 and event_duration > 10000000 and mysql_sql_template_md5 != ''", instanceID)

				}
			case model.ScheduleSqlTaskType_SqlExecL1000:
				switch sqlTaskVersion {
				case 1:
					return fmt.Sprintf("* | select '%s' as instance_id, 'L1000' as type, count(*) as count where event_duration <= 1000000000 and event_duration > 100000000 and mysql_sql_template_md5 != ''", instanceID)

				}
			case model.ScheduleSqlTaskType_SqlExecG1000:
				switch sqlTaskVersion {
				case 1:
					return fmt.Sprintf("* | select '%s' as instance_id, 'G1000' as type, count(*) as count where event_duration > 1000000000 and mysql_sql_template_md5 != ''", instanceID)
				}
			}
		case model.StatisticDataType_SqlTemplateAggregate:
			switch sqlTaskType {
			case model.ScheduleSqlTaskType_SqlTemplateAggregate:
				switch sqlTaskVersion {
				case 1:
					return fmt.Sprintf("* | select '%s' as instance_id, "+
						"  mysql_sql_template_md5 as sql_fingerprint, "+
						"  mysql_sql_template as sql_template, "+
						"  mysql_context_db as context_db, "+
						"  method, "+
						"  COUNT(*) as count,   "+
						"  SUM(event_duration) as event_duration_sum, "+
						"  SUM(mysql_affected_rows) as affected_rows_sum,"+
						"  SUM(mysql_num_rows) as num_rows_sum, "+
						"  SUM(server_bytes) as server_bytes_sum "+
						" WHERE mysql_sql_template_md5 != '' "+
						" group by mysql_sql_template_md5, mysql_sql_template, mysql_context_db, method", instanceID)
				}
			}
		}
	}
	return ""
}

type CleanScheduleSqlTaskStep struct {
	BaseStep
}

func (b CleanScheduleSqlTaskStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (b CleanScheduleSqlTaskStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	scheduleSqlTasks, err := a.statisticSqlTaskDAL.Get(ctx, a.state.InstanceId, a.state.TenantId)
	if err != nil {
		log.Warn(ctx, "query scheduleSqlTasks err: %s", err)
		return err
	}
	tlsClient, err := b.GetTlsClient(ctx, a)
	if err != nil {
		log.Warn(ctx, "GetTenantTlsClient err: %s", err)
		return err
	}
	for _, task := range scheduleSqlTasks {
		log.Info(ctx, "clean sql task %s", task.SqlTaskId)
		err := DeleteStatisticSqlTask(ctx, a, tlsClient, task.SqlTaskId)
		if err != nil {
			log.Warn(ctx, "query DeleteStatisticSqlTask err: %s", err)
			return err
		}
	}
	return nil
}

func (b CleanScheduleSqlTaskStep) MaxExecRetry() int {
	return 10
}

func (b CleanScheduleSqlTaskStep) ErrorWait() time.Duration {
	return time.Second * 10
}

func (b CleanScheduleSqlTaskStep) GetStepName() string {
	return "CleanScheduleSqlTaskStep"
}
