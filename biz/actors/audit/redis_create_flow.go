package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

var redisCreateSteps = map[string]IdempotentStep{
	// 创建实例
	// PreCheck 实例状态
	"PrepareInstanceStateStep": &PrepareInstanceStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				switch a.state.InstanceStatus {
				case model.AuditStatus_Running:
					// 补单情况
					return a.idempotentSteps["NotifyCreateOrderStep"]
				case model.AuditStatus_OrderNew:
					// 更新实例状态
					return a.idempotentSteps["UpdateInstanceStatusCreatingStep"]
				default:
					// 其他状态直接返回
				}
				return nil
			},
		},
	},
	// UpdateCreatingStatue
	"UpdateInstanceStatusCreatingStep": &UpdateInstanceStatusCreatingStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareIAMProjectStep"]
			},
		},
	},
	// AddProject
	"PrepareIAMProjectStep": &PrepareIAMProjectStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareIAMProjectTagStep"]
			},
		},
	},
	// AddSystemTag
	"PrepareIAMProjectTagStep": &PrepareIAMProjectTagStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["TagResourceStep"]
		}},
	},
	// Add custom tag 添加用户标签
	"TagResourceStep": &TagResourceStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["FetchInnerProjectStep"]
		}},
	},
	// 获取或者创建TLS Project
	"FetchInnerProjectStep": &FetchInnerProjectStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicStep": &CreateInnerTopicStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveTLSInfoStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveTLSInfoStep": &SaveTLSInfoStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareInnerTLSIndexStep"]
			},
		},
	},
	// 创建索引
	"PrepareInnerTLSIndexStep": &PrepareInnerTLSIndexStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTLSHostGroupStep"]
			},
		},
	},

	// 创建机器组
	"CreateInnerTLSHostGroupStep": &CreateInnerTLSHostGroupStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["UpdateTLSHostGroupStep"]
		}},
	},
	"UpdateTLSHostGroupStep": &UpdateTLSHostGroupStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["CreateInnerTLSRuleStep"]
		}},
	},
	// 创建采集规则
	"CreateInnerTLSRuleStep": &CreateInnerTLSRuleStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["UpdateTLSRuleStep"]
		}},
	},
	"UpdateTLSRuleStep": &UpdateTLSRuleStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["ApplyRuleToInnerHostGroupsStep"]
		}},
	},
	// 绑定采集规则到机器组
	"ApplyRuleToInnerHostGroupsStep": &ApplyRuleToInnerHostGroupsStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["CreateInnerLogCollectorResourceStep"]
		}},
	},
	// 创建资源 Daemonset & CM 部署log-collector
	"CreateInnerLogCollectorResourceStep": &CreateInnerLogCollectorResourceStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["OpenDBInstanceAuditLogStep"]
			},
		},
	},
	"OpenDBInstanceAuditLogStep": &OpenDBInstanceAuditLogStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				a.state.InstanceStatus = model.AuditStatus_Running
				return a.idempotentSteps["UpdateInstanceStatusStep"]
			},
		},
	},
	// 等待采集资源部署完成
	//"WaitCollectorReadyStep": &WaitCollectorReadyStep{},
	// 修改实例状态
	"UpdateInstanceStatusStep": &UpdateInstanceStatusStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["NotifyCreateOrderStep"]
			},
		},
	},
	// 通知订单完成
	"NotifyCreateOrderStep": &NotifyCreateOrderStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}

func NewRedisCreateLifecycleActor(in NewAuditCreateLifecycleActorIn) types.VirtualPersistenceProducer {
	// 组装垃圾回收子流
	GlobalStepLock.Lock()
	for s, step := range auditCreateGCSteps {
		redisCreateSteps[s] = step
	}
	GlobalStepLock.Unlock()

	return types.VirtualPersistenceProducer{
		Kind: consts.RedisAuditCreateActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &AuditLifecycleActor{
				state:           newTaskState(state),
				idempotentSteps: redisCreateSteps,

				source:          in.DataSource,
				crossAuthSvc:    in.CrossAuthSvc,
				conf:            in.Conf,
				c3Conf:          in.C3Conf,
				loc:             in.Loc,
				auditTlsDAL:     in.AuditTlsDAL,
				tagSvc:          in.TagSvc,
				projectSvc:      in.ProjectSvc,
				auditService:    in.AuditService,
				genTlsSvc:       in.GenTlsSvc,
				tlsDAL:          in.TlsDAL,
				lcSvc:           in.LCSvc,
				billSvc:         in.BillSvc,
				publishEventSvc: in.PublishEventSvc,
			}
			return actor
		}),
	}
}
