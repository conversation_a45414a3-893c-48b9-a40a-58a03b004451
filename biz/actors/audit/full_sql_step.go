package audit

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql/zkconfig"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	tls_sdk "github.com/volcengine/volc-sdk-golang/service/tls"
	"gorm.io/gorm"
)

var FullSqlCurrentTlsVersion = int64(4)

type PrepareFullSqlInstanceStateStep struct {
	BaseStep
}

func (i PrepareFullSqlInstanceStateStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (i PrepareFullSqlInstanceStateStep) ProtectExec(ctx types.Context, actor *AuditLifecycleActor) error {
	log.Info(ctx, "ProtectExec InstanceId: %s, OrderID: %s", actor.state.InstanceId, actor.state.OrderId)
	orderExtra := &billing.OrderExtra{}
	_ = libutils.Unmarshal([]byte(actor.state.Message.Attribute), orderExtra)
	actor.state.TlsTopicTTL = int64(orderExtra.TlsTtl)
	actor.state.InitTags = orderExtra.CustomTags
	audit, err := actor.fullSqlInstDAL.GetByID(ctx, actor.state.InstanceId, actor.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return err
	}
	actor.state.InstanceStatus = model.AuditStatus(audit.Status)
	actor.state.FollowInstanceId = audit.FollowInstanceID
	actor.state.DSType, err = model.DSTypeFromString(audit.DbType)
	if err != nil {
		log.Error(ctx, "DSType fail, error:%s", err)
		return err
	}
	actor.state.DeployType, err = model.LabelTypeFromString(audit.DeployType)
	if err != nil {
		log.Error(ctx, "DeployType fail, error:%s", err)
		return err
	}
	actor.state.LogProductType, err = model.LogProductTypeFromString(audit.ProductType)
	if err != nil {
		log.Error(ctx, "LogProductType fail, error:%s", err)
		return err
	}
	actor.state.StorageSqlTypes = strings.Split(audit.StorageSqlTypes, ",")
	actor.state.Region = audit.Region

	emap, err := ConvEnableFuncNames(ctx, audit.EnableFunctions)
	if err != nil {
		log.Error(ctx, "EnableFunctions fail, error:%s", err)
		return err
	}
	actor.state.EnableFunctions = emap
	actor.state.SqlDesensitizationType, err = model.SqlDesensitizationTypeFromString(audit.SqlDesensitizationType)
	if err != nil {
		log.Error(ctx, "SqlDesensitizationType fail, error:%s", err)
		return err
	}
	actor.state.RateSimpling = audit.RateSimpling

	tlsRegion, err := actor.conf.Get(ctx).GetTLSRegion(i18n.GetSiteName(ctx, actor.conf.Get(ctx).FullSqlInnerAccountId))
	if err != nil {
		log.Error(ctx, "请检查运维面参数:TlsServiceEndpoint, tls region err:%s", err)
		return err
	}
	actor.state.TlsRegion = tlsRegion
	tlsEndpoint := actor.conf.Get(ctx).GetTlsRegionEndpoint(i18n.GetSiteName(ctx, actor.conf.Get(ctx).FullSqlInnerAccountId))
	actor.state.TlsEndpoint = tlsEndpoint
	actor.state.TlsDataType = model.StatisticDataType_FullSqlDetail
	actor.state.TlsIndexVersion = FullSqlCurrentTlsVersion

	actor.state.SqlTemplateTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(orderExtra.TlsTtl),
		TlsDataType:     model.StatisticDataType_FullSqlTemplateAggr,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	actor.state.SqlCountTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(orderExtra.TlsTtl),
		TlsDataType:     model.StatisticDataType_FullSqlExecCount,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	actor.state.SqlTableAggrTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(orderExtra.TlsTtl),
		TlsDataType:     model.StatisticDataType_FullSqlTableAggr,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	actor.state.SqlTopTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(orderExtra.TlsTtl),
		TlsDataType:     model.StatisticDataType_FullSqlTopSql,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}

	detail, err := actor.source.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{InstanceId: actor.state.FollowInstanceId, Type: shared.DataSourceType(actor.state.DSType)})
	if err != nil {
		log.Warn(ctx, "Action_DescribeDBInstanceDetail call error: %s", err)
		return err
	}
	if detail.InstanceStatus != model.InstanceStatus_Running.String() {
		log.Warn(ctx, "Please try later. RDS instance is not running, %s", detail.InstanceStatus)
		return err
	}

	log.Info(ctx, fmt.Sprintf("Init params:[%s]", libutils.Show(actor.state)))
	return nil
}

// ConvEnableFuncNames 功能名称转换
func ConvEnableFuncNames(ctx types.Context, enableFunctionsStr string) (map[model.FullSqlFuncName]bool, error) {
	enableFunctions := map[model.FullSqlFuncName]bool{
		model.FullSqlFuncName_SQLAuditDetail: false,
		model.FullSqlFuncName_SQLInsight:     false,
	}
	for _, v := range strings.Split(enableFunctionsStr, ",") {
		if v == "" {
			continue
		}
		efuncName, err := model.FullSqlFuncNameFromString(v)
		if err != nil {
			log.Error(ctx, "EnableFunctions fail, error:%s", err)
			return enableFunctions, err
		}
		enableFunctions[efuncName] = true
	}

	return enableFunctions, nil
}

func (i PrepareFullSqlInstanceStateStep) MaxExecRetry() int {
	return 10
}

func (i PrepareFullSqlInstanceStateStep) GetStepName() string {
	return "PrepareFullSqlInstanceStateStep"
}

type PrepareFullSqlInstanceUpgradeStateStep struct {
	BaseStep
}

func (i PrepareFullSqlInstanceUpgradeStateStep) ProtectExec(ctx types.Context, actor *AuditLifecycleActor) error {
	log.Info(ctx, "ProtectExec InstanceId: %s", actor.state.InstanceId)

	audit, err := actor.fullSqlInstDAL.GetByID(ctx, actor.state.InstanceId, actor.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return err
	}
	actor.state.InstanceStatus = model.AuditStatus(audit.Status)
	actor.state.FollowInstanceId = audit.FollowInstanceID
	actor.state.DSType, err = model.DSTypeFromString(audit.DbType)
	if err != nil {
		log.Error(ctx, "DSType fail, error:%s", err)
		return err
	}
	actor.state.DeployType, err = model.LabelTypeFromString(audit.DeployType)
	if err != nil {
		log.Error(ctx, "DeployType fail, error:%s", err)
		return err
	}
	actor.state.LogProductType, err = model.LogProductTypeFromString(audit.ProductType)
	if err != nil {
		log.Error(ctx, "LogProductType fail, error:%s", err)
		return err
	}
	actor.state.StorageSqlTypes = strings.Split(audit.StorageSqlTypes, ",")
	actor.state.Region = audit.Region
	tlsRegion, err := actor.conf.Get(ctx).GetTLSRegion(i18n.GetSiteName(ctx, actor.conf.Get(ctx).FullSqlInnerAccountId))
	if err != nil {
		log.Error(ctx, "请检查运维面参数:TlsServiceEndpoint, tls region err:%s", err)
		return err
	}
	actor.state.TlsRegion = tlsRegion
	tlsEndpoint := actor.conf.Get(ctx).GetTlsRegionEndpoint(i18n.GetSiteName(ctx, actor.conf.Get(ctx).FullSqlInnerAccountId))
	actor.state.TlsEndpoint = tlsEndpoint
	actor.state.TlsDataType = model.StatisticDataType_FullSqlDetail
	actor.state.TlsIndexVersion = FullSqlCurrentTlsVersion

	tlsInfo, err := actor.tlsDAL.GetFromAdmin(ctx, strconv.FormatInt(audit.TlsId, 10))

	if err != nil {
		log.Warn(ctx, "get fullsql detail tls info error:%s", err)
		return err
	}

	actor.state.TlsProject = tlsInfo.TlsProjectId
	actor.state.TlsTopic = tlsInfo.TlsTopicId
	actor.state.TlsId = audit.TlsId

	actor.state.SqlTemplateTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(tlsInfo.Ttl),
		TlsDataType:     model.StatisticDataType_FullSqlTemplateAggr,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	err = prepareTlsCnf(ctx, actor, actor.state.SqlTemplateTls)
	if err != nil {
		log.Warn(ctx, "prepare tls config error:%s", err)
	}
	actor.state.SqlCountTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(tlsInfo.Ttl),
		TlsDataType:     model.StatisticDataType_FullSqlExecCount,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	err = prepareTlsCnf(ctx, actor, actor.state.SqlCountTls)
	if err != nil {
		log.Warn(ctx, "prepare tls config error:%s", err)
	}
	actor.state.SqlTableAggrTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(tlsInfo.Ttl),
		TlsDataType:     model.StatisticDataType_FullSqlTableAggr,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	err = prepareTlsCnf(ctx, actor, actor.state.SqlTableAggrTls)
	if err != nil {
		log.Warn(ctx, "prepare tls config error:%s", err)
	}
	actor.state.SqlTopTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(tlsInfo.Ttl),
		TlsDataType:     model.StatisticDataType_FullSqlTopSql,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	err = prepareTlsCnf(ctx, actor, actor.state.SqlTableAggrTls)
	if err != nil {
		log.Warn(ctx, "prepare tls config error:%s", err)
	}

	actor.state.SqlTopTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(tlsInfo.Ttl),
		TlsDataType:     model.StatisticDataType_FullSqlTopSql,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	err = prepareTlsCnf(ctx, actor, actor.state.SqlTopTls)
	if err != nil {
		log.Warn(ctx, "prepare tls config error:%s", err)
	}

	actor.state.SqlTopTls = &TlsConfig{
		TlsEndpoint:     tlsEndpoint,
		TlsRegion:       tlsRegion,
		TlsProject:      "",
		TlsTopic:        "",
		TlsTopicTTL:     int64(tlsInfo.Ttl),
		TlsDataType:     model.StatisticDataType_FullSqlTopSql,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	err = prepareTlsCnf(ctx, actor, actor.state.SqlTopTls)
	if err != nil {
		log.Warn(ctx, "prepare tls config error:%s", err)
	}

	instanceId := actor.state.FollowInstanceId

	if actor.state.DSType == model.DSType_ByteRDS {
		lastDotIndex := LastDotIndex(instanceId)
		if lastDotIndex != -1 {
			instanceId = instanceId[:lastDotIndex]
		}
	}
	detail, err := actor.source.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{InstanceId: instanceId, Type: shared.DataSourceType(actor.state.DSType), RegionId: actor.state.Region})
	if err != nil {
		log.Warn(ctx, "Action_DescribeDBInstanceDetail call error: %s", err)
		return err
	}
	if detail.InstanceStatus != model.InstanceStatus_Running.String() {
		log.Warn(ctx, "Please try later. RDS instance is not running, %s", detail.InstanceStatus)
		return err
	}

	log.Info(ctx, fmt.Sprintf("Init params:[%s]", libutils.Show(actor.state)))
	return nil
}

func LastDotIndex(s string) int {
	return lastIndex(s, '.')
}

func lastIndex(s string, c byte) int {
	for i := len(s) - 1; i >= 0; i-- {
		if s[i] == c {
			return i
		}
	}
	return -1
}

func (i PrepareFullSqlInstanceUpgradeStateStep) MaxExecRetry() int {
	return 10
}

func (i PrepareFullSqlInstanceUpgradeStateStep) GetStepName() string {
	return "PrepareFullSqlInstanceUpgradeStateStep"
}

type ModifyFullSqlTLSIndexSqlTemplateStep struct {
	BaseStep
}

func (p ModifyFullSqlTLSIndexSqlTemplateStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTemplateTlsConf := a.state.SqlTemplateTls
	return modifyIndexProtectExec(ctx, a, SqlTemplateTlsConf, a.state.DSType)
}

func (p ModifyFullSqlTLSIndexSqlTemplateStep) MaxExecRetry() int {
	return 10
}

func (p ModifyFullSqlTLSIndexSqlTemplateStep) GetStepName() string {
	return "ModifyFullSqlTLSIndexSqlTemplateStep"
}

type ModifyFullSqlTLSIndexDetailStep struct {
	BaseStep
}

func (p ModifyFullSqlTLSIndexDetailStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	detailTlsCnf := &TlsConfig{
		TlsId:           a.state.TlsId,
		TlsEndpoint:     a.state.TlsEndpoint,
		TlsRegion:       a.state.TlsRegion,
		TlsProject:      a.state.TlsProject,
		TlsTopic:        a.state.TlsTopic,
		TlsTopicTTL:     a.state.TlsTopicTTL,
		TlsDataType:     model.StatisticDataType_FullSqlDetail,
		TlsIndexVersion: FullSqlCurrentTlsVersion,
	}
	return modifyIndexProtectExec(ctx, a, detailTlsCnf, a.state.DSType)
}

func (p ModifyFullSqlTLSIndexDetailStep) MaxExecRetry() int {
	return 10
}

func (p ModifyFullSqlTLSIndexDetailStep) GetStepName() string {
	return "ModifyFullSqlTLSIndexDetailStep"
}

type ModifyFullSqlTLSIndexSqlCountStep struct {
	BaseStep
}

func (p ModifyFullSqlTLSIndexSqlCountStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlCountTls := a.state.SqlCountTls
	return modifyIndexProtectExec(ctx, a, SqlCountTls, a.state.DSType)
}

func (p ModifyFullSqlTLSIndexSqlCountStep) MaxExecRetry() int {
	return 10
}

func (p ModifyFullSqlTLSIndexSqlCountStep) GetStepName() string {
	return "ModifyFullSqlTLSIndexSqlCountStep"
}

type FetchInnerProjectStep struct {
	BaseStep
}

func (p FetchInnerProjectStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.TlsProject != "" {
		return true, nil
	}
	// 如果实例与tls不在一个region的情况下会找不到project
	tlsRegion, err := a.conf.Get(ctx).GetTLSRegion(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId))
	if err != nil {
		log.Error(ctx, "请检查运维面参数:TlsServiceEndpoint, tls region err:%s", err)
		return false, err
	}
	pls, err := a.tlsDAL.GetProjects(context.TODO(), a.conf.Get(ctx).FullSqlInnerAccountId, tlsRegion)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Info(ctx, "project not exist in metadb, TenantId:%s, Region:%s", a.conf.Get(ctx).FullSqlInnerAccountId, a.state.Region)
			return false, nil
		} else {
			log.Warn(ctx, "failed to get tls project list, err=%v", err)
			return false, err
		}
	}
	client, err := p.GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return false, err
	}
	for _, pl := range pls {
		project, err := client.DescribeProject(&tls_sdk.DescribeProjectRequest{
			CommonRequest: tls_sdk.CommonRequest{},
			ProjectID:     pl.TlsProjectId,
		})
		if err != nil {
			if strings.Contains(err.Error(), "ProjectNotExists") {
				log.Warn(ctx, "Project not exists:%s", err)
				continue
			} else {
				return false, err
			}
		}
		if project.TopicCount < a.conf.Get(ctx).InnerAccountTlsProjectTopicQuota {
			log.Info(ctx, "project topic count less than %s", a.conf.Get(ctx).InnerAccountTlsProjectTopicQuota)
			a.state.TlsProject = pl.TlsProjectId
			return true, nil
		}
	}
	log.Info(ctx, "not find valid project")
	return false, nil
}

func (p FetchInnerProjectStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return err
	}
	// 创建新project
	projectPrefix := "dbw-sql-"
	createProjectRequest := &tls_sdk.CreateProjectRequest{
		ProjectName: projectPrefix + uuid.New().String(),
		Region:      a.state.TlsRegion,
		Description: consts.TLSDescription,
	}
	createProjectResponse, err := tlsClient.CreateProject(createProjectRequest)
	log.Info(ctx, "createProjectResponse:%+v", createProjectResponse)
	if err != nil {
		log.Info(ctx, "create project %s failed, error:%s", createProjectRequest.ProjectName, err.Error())
		return err
	}
	_, err = a.tlsDAL.Create(context.TODO(), &dao.Tls{
		TlsProjectId: createProjectResponse.ProjectID,
		Region:       a.state.TlsRegion,
		TenantID:     a.conf.Get(ctx).FullSqlInnerAccountId,
		TlsEndpoint:  a.conf.Get(ctx).GetTlsRegionEndpoint(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId)),
	})
	if err != nil {
		log.Warn(ctx, "failed to create tls topic, err=%v", err)
	}
	a.state.TlsProject = createProjectResponse.ProjectID
	return nil
}

func (p FetchInnerProjectStep) MaxExecRetry() int {
	return 10
}

func (p FetchInnerProjectStep) GetStepName() string {
	return "FetchInnerProjectStep"
}

type CreateInnerTopicStep struct {
	BaseStep
}

func (p CreateInnerTopicStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.TlsTopic == "" {
		return false, nil
	} else {
		return true, nil
	}

}

func formatTopicName(name string) string {
	return KeepLast64Chars(strings.ReplaceAll(strings.ToLower(name), ".", "_"))
}

// KeepLast64Chars 保留字符串的最后 64 个字符
func KeepLast64Chars(s string) string {
	if len(s) > 64 {
		return s[len(s)-64:] // 保留最后 64 个字符
	}
	return s // 如果长度不超过 64，返回原字符串
}

func (p CreateInnerTopicStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return err
	}

	createTopicRequest := &tls_sdk.CreateTopicRequest{
		ProjectID:   a.state.TlsProject,
		TopicName:   formatTopicName(a.state.FollowInstanceId + "_" + a.state.TlsDataType.String()),
		Description: consts.TLSDescription,
		Ttl:         uint16(a.state.TlsTopicTTL),
		AutoSplit:   true,
		ShardCount:  32, // 成本占比低，每个分区提供的写入能力为 5MiB/s、500 次/s，读取能力为 5 MiB/s、100 次/s
	}
	log.Info(ctx, "createTopicRequest:%s", libutils.Show(createTopicRequest))
	createTopicResponse, err := tlsClient.CreateTopic(createTopicRequest)
	if err != nil {
		log.Info(ctx, "create topic %s failed, error:%s", createTopicRequest.TopicName, err)
		return err
	}
	log.Info(ctx, " create topic %s response:%s", createTopicRequest.TopicName, libutils.Show(createTopicResponse))
	a.state.TlsTopic = createTopicResponse.TopicID
	return nil
}

func (p CreateInnerTopicStep) MaxExecRetry() int {
	return 10
}

func (p CreateInnerTopicStep) GetStepName() string {
	return "CreateInnerTopicStep"
}

type SaveInnerTLSInfoStep struct {
	BaseStep
}

func (s SaveInnerTLSInfoStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (s SaveInnerTLSInfoStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	if a.state.TlsId == 0 {
		tlsId, err := a.tlsDAL.Create(context.TODO(), &dao.Tls{
			TlsProjectId: a.state.TlsProject,
			TlsTopicId:   a.state.TlsTopic,
			TlsEndpoint:  a.state.TlsEndpoint,
			Region:       a.state.TlsRegion,
			TenantID:     a.conf.Get(ctx).FullSqlInnerAccountId,
			Ttl:          int32(a.state.TlsTopicTTL),
			IndexVersion: int32(a.state.TlsIndexVersion),
		})
		if err != nil {
			log.Info(ctx, "create tls meta error:%s", err.Error())
			return err
		}
		a.state.TlsId = tlsId
	}
	log.Info(ctx, "update TlsId:%d, FollowInstanceId:%s, InnerTenantID:%s, TenantId:%s", a.state.TlsId, a.state.FollowInstanceId, a.conf.Get(ctx).FullSqlInnerAccountId, a.state.TenantId)
	switch a.state.LogProductType {
	case model.LogProductType_AuditLog:
		err := a.auditTlsDAL.UpdateByFollowInstanceID(ctx, &dao.AuditTls{
			TlsId:            a.state.TlsId,
			FollowInstanceID: a.state.FollowInstanceId,
			TenantID:         a.state.TenantId,
		})
		if err != nil {
			log.Info(ctx, "update audit meta error:%s", err.Error())
			return err
		}
	case model.LogProductType_FullSqlLog:
		err := a.fullSqlInstDAL.UpdateByFollowInstanceID(ctx, &dao.AuditTls{
			TlsId:            a.state.TlsId,
			FollowInstanceID: a.state.FollowInstanceId,
			TenantID:         a.state.TenantId,
		})
		if err != nil {
			log.Info(ctx, "update audit meta error:%s", err.Error())
			return err
		}
	default:
		panic(fmt.Sprintf("unknown log product type: %s", a.state.LogProductType))
	}
	return nil
}

func (s SaveInnerTLSInfoStep) MaxExecRetry() int {
	return 10
}

func (s SaveInnerTLSInfoStep) GetStepName() string {
	return "SaveInnerTLSInfoStep"
}

type OpenFullSqlLogSwitchStep struct {
	BaseStep
}

func (o OpenFullSqlLogSwitchStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	config, err := a.source.DescribeFullSQLLogConfig(ctx, &datasource.DescribeFullSQLLogConfigReq{
		InstanceID:   a.state.FollowInstanceId,
		InstanceType: shared.DataSourceType(a.state.DSType),
	})
	if err != nil {
		log.Warn(ctx, "DescribeFullSQLLogConfig error:%s", err)
		return false, err
	}
	if config.SQLCollectorStatus == datasource.SQLCollectorStatus_Enable {
		if config.TLSDomain == "" || config.TLSProjectId == "" || config.TLSTopic == "" {
			err = errors.New(fmt.Sprintf("DescribeFullSQLLogConfig param error, %s", libutils.Show(config)))
			log.Warn(ctx, "%s", err)
			return false, err
		}
		a.state.TlsConsumerEndpoint = config.TLSDomain
		a.state.TlsConsumerProject = config.TLSProjectId
		a.state.TlsConsumerTopic = config.TLSTopic
		return true, nil
	} else {
		_, err := a.source.ModifyFullSQLLogConfig(ctx, &datasource.ModifyFullSQLLogConfigReq{
			InstanceID:         a.state.FollowInstanceId,
			DryRun:             true,
			SQLCollectorStatus: datasource.SQLCollectorStatus_Enable,
			InstanceType:       shared.DataSourceType(a.state.DSType),
		})
		if err != nil {
			log.Warn(ctx, "ModifyFullSQLLogConfig DryRun error:%s", err)
			return false, err
		}
		return false, nil
	}

}

func (o OpenFullSqlLogSwitchStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	_, err := a.source.ModifyFullSQLLogConfig(ctx, &datasource.ModifyFullSQLLogConfigReq{
		InstanceID:         a.state.FollowInstanceId,
		DryRun:             false,
		SQLCollectorStatus: datasource.SQLCollectorStatus_Enable,
		InstanceType:       shared.DataSourceType(a.state.DSType),
	})
	if err != nil {
		log.Warn(ctx, "ModifyFullSQLLogConfig error:%s", err)
		return err
	}

	config, err := a.source.DescribeFullSQLLogConfig(ctx, &datasource.DescribeFullSQLLogConfigReq{
		InstanceID:   a.state.FollowInstanceId,
		InstanceType: shared.DataSourceType(a.state.DSType),
	})
	if err != nil {
		log.Warn(ctx, "DescribeFullSQLLogConfig error:%s", err)
		return err
	}
	if config.SQLCollectorStatus == datasource.SQLCollectorStatus_Enable {
		if config.TLSDomain == "" || config.TLSProjectId == "" || config.TLSTopic == "" {
			err = errors.New(fmt.Sprintf("DescribeFullSQLLogConfig param error, %s", libutils.Show(config)))
			log.Warn(ctx, "%s", err)
			return err
		}
		a.state.TlsConsumerEndpoint = config.TLSDomain
		a.state.TlsConsumerProject = config.TLSProjectId
		a.state.TlsConsumerTopic = config.TLSTopic
		return nil
	}

	return errors.New("enable not take effect")
}

func (o OpenFullSqlLogSwitchStep) MaxExecRetry() int {
	return 10
}

func (o OpenFullSqlLogSwitchStep) ErrorWait() time.Duration {
	return time.Second * 30
}

func (o OpenFullSqlLogSwitchStep) GetStepName() string {
	return "OpenFullSqlLogSwitchStep"
}

type ConfigConsumerPipelineStep struct {
	BaseStep
}

func (c ConfigConsumerPipelineStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (c ConfigConsumerPipelineStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	var dbBlackList map[string]bool
	var userBlackList map[string]bool
	var consumerAK string
	var consumerSK string
	c3Conf := a.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	a.state.TlsConsumerGroup = "dbw-full-sql-consumer"
	tlsConsumerEndpoint := getTlsKafkaEndpoint(a.state.TlsConsumerEndpoint)
	conf := a.conf.Get(ctx)
	tlsRegion, _ := conf.GetTLSRegion(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId))
	blackStorageSqlTypes := getBlackSqlTypes(a.state.StorageSqlTypes)
	switch a.state.DSType {
	case model.DSType_MySQL:
		dbBlackList = full_sql.DefaultDatabaseFilterMap
		userBlackList = full_sql.DefaultUserFilterMap
		consumerAK = c3Conf.RDSInnerAccountAK
		consumerSK = c3Conf.RDSInnerAccountSK
	case model.DSType_VeDBMySQL:
		dbBlackList = full_sql.DefaultVedbDatabaseFilterMap
		userBlackList = full_sql.DefaultVedbUserFilterMap
		consumerAK = c3Conf.VeDBInnerAccountAK
		consumerSK = c3Conf.VeDBInnerAccountSK
	case model.DSType_MetaMySQL:
		dbBlackList = full_sql.EmptyFilterMap
		userBlackList = full_sql.EmptyFilterMap
		consumerAK = c3Conf.MetaRDSInnerAccountAK
		consumerSK = c3Conf.MetaRDSInnerAccountSK
	default:
		return errors.New(fmt.Sprintf("unknown dstype: %s", a.state.DSType))
	}
	log.Info(ctx, "storage sql type black list:", libutils.Show(blackStorageSqlTypes))
	storageSqlDesensitization := tenant.IsSqlDesensitizationAccount(a.conf, a.state.TenantId) ||
		a.state.SqlDesensitizationType == model.SqlDesensitizationType_Storage
	tlsOutputSwitch := a.state.EnableFunctions[model.FullSqlFuncName_SQLAuditDetail]
	kafkaOutputSwitch := a.state.EnableFunctions[model.FullSqlFuncName_SQLInsight]
	pipeMeta := &zkconfig.PipeMeta{
		InstanceId:             a.state.FollowInstanceId,
		TenantId:               a.state.TenantId,
		DataType:               model.LogProductType_FullSqlLog.String(),
		Filter:                 true,
		ReportError:            conf.FullSqlReportErrorRecord,
		DBBlackListFilter:      dbBlackList,
		UserBlackListFilter:    userBlackList,
		SqlTypeBlackListFilter: blackStorageSqlTypes,
		SimplingRate:           a.state.RateSimpling,
		SecretLevel:            0,
		SqlDesensitization:     storageSqlDesensitization,
	}

	input := &zkconfig.TlsEtlConfig{
		PipeMeta: pipeMeta,
		TlsSource: &zkconfig.TlsConfig{
			Endpoint:   tlsConsumerEndpoint,
			Ak:         consumerAK,
			Sk:         consumerSK,
			Project:    a.state.TlsConsumerProject,
			Topic:      a.state.TlsConsumerTopic,
			GroupId:    a.state.TlsConsumerGroup,
			Region:     tlsRegion,
			Concurrent: int(conf.FullSqlTlsConsumerConcurrent),
		},
		KafkaOutput:    kafkaOutputSwitch,
		TlsOutput:      tlsOutputSwitch,
		TemplateOutput: true,
	}
	output := &zkconfig.TlsEtlConfig{
		PipeMeta: pipeMeta,
		TlsTarget: &zkconfig.TlsConfig{
			Endpoint: conf.GetTlsRegionEndpoint(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId)),
			Ak:       c3Conf.TLSServiceAccessKey,
			Sk:       c3Conf.TLSServiceSecretKey,
			Project:  a.state.TlsProject,
			Topic:    a.state.TlsTopic,
			Region:   tlsRegion,
		},
		KafkaOutput:    kafkaOutputSwitch,
		TlsOutput:      tlsOutputSwitch,
		TemplateOutput: true,
	}
	log.Info(ctx, "Pipe Input Config: %s", libutils.Show(input))
	log.Info(ctx, "Pipe Output Config: %s", libutils.Show(output))
	err := a.fullSqlConfigService.PipeConfigX(ctx, zkconfig.ZKConfig{
		Hosts:      conf.FullSqlZkHosts,
		AuthScheme: conf.FullSqlZkAuthScheme,
		User:       conf.FullSqlZkUser,
		Password:   conf.FullSqlZkPassword,
	}, input, output)
	if err != nil {
		log.Warn(ctx, "zk pipe config error:%s", err)
		return err
	}

	return nil
}

func getBlackSqlTypes(writeSqlTypes []string) map[string]bool {
	allSqlMethod := map[string]bool{
		model.FullSqlMethod_SELECT.String():   true,
		model.FullSqlMethod_INSERT.String():   true,
		model.FullSqlMethod_UPDATE.String():   true,
		model.FullSqlMethod_DELETE.String():   true,
		model.FullSqlMethod_ALTER.String():    true,
		model.FullSqlMethod_CREATE.String():   true,
		model.FullSqlMethod_DROP.String():     true,
		model.FullSqlMethod_RENAME.String():   true,
		model.FullSqlMethod_TRUNCATE.String(): true,
		model.FullSqlMethod_OTHER.String():    true,
	}
	for _, sqlMethod := range writeSqlTypes {
		if _, ok := allSqlMethod[sqlMethod]; ok {
			delete(allSqlMethod, sqlMethod)
		}
	}
	var blackSqlMethods []string
	for k, _ := range allSqlMethod {
		blackSqlMethods = append(blackSqlMethods, k)
	}
	blackStorageSqlTypes := full_sql.ConvSmStrToEx(blackSqlMethods)
	return blackStorageSqlTypes
}

func getTlsKafkaEndpoint(tlsConsumerEndpoint string) string {
	if strings.Contains(tlsConsumerEndpoint, "://") {
		tlsConsumerEndpoint = strings.Split(tlsConsumerEndpoint, "://")[1]
	}
	tlsConsumerEndpoint = strings.Join([]string{tlsConsumerEndpoint, "9093"}, ":")
	return tlsConsumerEndpoint
}

func (c ConfigConsumerPipelineStep) MaxExecRetry() int {
	return 10
}

func (c ConfigConsumerPipelineStep) ErrorWait() time.Duration {
	return time.Second * 5
}

func (c ConfigConsumerPipelineStep) GetStepName() string {
	return "ConfigConsumerPipelineStep"
}

type PrepareInnerTLSIndexStep struct {
	BaseStep
}

func (p PrepareInnerTLSIndexStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	tlsClient, err := p.GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return false, err
	}
	_, err = tlsClient.DescribeIndex(&tls_sdk.DescribeIndexRequest{
		CommonRequest: tls_sdk.CommonRequest{},
		TopicID:       a.state.TlsTopic,
	})
	if err != nil {
		if strings.Contains(err.Error(), "IndexNotExists") {
			return false, nil
		} else {
			return false, err
		}
	} else {
		return true, nil
	}
}

func (p PrepareInnerTLSIndexStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return err
	}

	log.Info(ctx, "create index topic id : %s", a.state.TlsTopic)
	createIndexRequest, err := a.genTlsSvc.GenCreateIndexRequest(ctx, a.state.TlsTopic, a.state.DSType, a.state.TlsDataType, a.state.TlsIndexVersion)
	if err != nil {
		log.Warn(ctx, "GenCreateIndexRequest error:%s", err)
		return err
	}
	if !utils.IsCapabilitiesFlag(a.state.CapabilitiesFlags, model.CapabilitiesFlag_QueryDetailKeyword) {
		createIndexRequest.KeyValue = CleanCloseIndexKey(createIndexRequest.KeyValue, a.state.DSType, model.CapabilitiesFlag_QueryDetailKeyword)
	}

	log.Info(ctx, "createIndexRequest : %s", libutils.Show(createIndexRequest))
	createIndexResp, err := tlsClient.CreateIndex(createIndexRequest)
	if err != nil {
		log.Info(ctx, "create tls index error, topic:%s, error:%s", a.state.TlsTopic, err.Error())
		return err
	}
	log.Info(ctx, "createIndexResp : %+v", libutils.Show(createIndexResp))
	return nil
}

func CleanCloseIndexKey(value *[]tls_sdk.KeyValueInfo, dsType model.DSType, flag model.CapabilitiesFlag) *[]tls_sdk.KeyValueInfo {
	var newKvInfo []tls_sdk.KeyValueInfo
	for _, info := range *value {
		switch flag {
		case model.CapabilitiesFlag_QueryDetailKeyword:
			switch dsType {
			case model.DSType_Redis:
				if info.Key != "cmd" {
					newKvInfo = append(newKvInfo, info)
				}
			case model.DSType_Mongo:
				if info.Key != "cmd" {
					newKvInfo = append(newKvInfo, info)
				}
			// MySQL库暂不支持关闭检索
			case model.DSType_MySQL:
				newKvInfo = append(newKvInfo, info)
			// VeDBMySQL库暂不支持关闭检索
			case model.DSType_VeDBMySQL:
				newKvInfo = append(newKvInfo, info)
			// Postgres库暂不支持关闭检索
			case model.DSType_Postgres:
				newKvInfo = append(newKvInfo, info)
			case model.DSType_ByteRDS:
				newKvInfo = append(newKvInfo, info)
			case model.DSType_MetaMySQL:
				newKvInfo = append(newKvInfo, info)
			default:
				panic("unknown datasource type.")
			}
		}
	}
	return &newKvInfo
}

func (p PrepareInnerTLSIndexStep) MaxExecRetry() int {
	return 10
}

func (p PrepareInnerTLSIndexStep) GetStepName() string {
	return "PrepareInnerTLSIndexStep"
}

type UpdateFullSqlInstanceStatusCreatingStep struct {
	BaseStep
}

func (u UpdateFullSqlInstanceStatusCreatingStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	audit, err := a.fullSqlInstDAL.GetByID(ctx, a.state.InstanceId, a.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return false, err
	}
	a.state.InstanceStatus = model.AuditStatus(audit.Status)
	if a.state.InstanceStatus == model.AuditStatus_OrderNew {
		return false, nil
	} else if a.state.InstanceStatus == model.AuditStatus_Creating {
		return true, nil
	}
	return false, errors.New(fmt.Sprintf("audit status is not expected, want: OrderNew, got:%s", model.AuditStatus(audit.Status).String()))
}

func (u UpdateFullSqlInstanceStatusCreatingStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.fullSqlInstDAL.UpdateByFollowInstanceID(ctx, &dao.AuditTls{
		FollowInstanceID: a.state.FollowInstanceId,
		TenantID:         a.state.TenantId,
		Status:           int32(model.AuditStatus_Creating),
	})
	if err == nil {
		a.state.InstanceStatus = model.AuditStatus_Creating
		return nil
	}
	return err
}

func (u UpdateFullSqlInstanceStatusCreatingStep) MaxExecRetry() int {
	return 10
}

func (u UpdateFullSqlInstanceStatusCreatingStep) GetStepName() string {
	return "UpdateFullSqlInstanceStatusCreatingStep"
}

type UpdateFullSqlInstanceStatusDeletingStep struct {
	BaseStep
}

func (u UpdateFullSqlInstanceStatusDeletingStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	inst, err := a.fullSqlInstDAL.GetByID(ctx, a.state.InstanceId, a.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return err
	}
	if inst.Status <= int32(model.AuditStatus_Deleting) {
		a.state.InstanceStatus = model.AuditStatus_Deleting
		inst.Status = int32(a.state.InstanceStatus)
		err := a.fullSqlInstDAL.UpdateByFollowInstanceID(ctx, inst)
		return err
	}
	return nil
}

func (u UpdateFullSqlInstanceStatusDeletingStep) MaxExecRetry() int {
	return 10
}

func (u UpdateFullSqlInstanceStatusDeletingStep) GetStepName() string {
	return "UpdateFullSqlInstanceStatusDeletingStep"
}

type FullSqlInstanceGCStep struct {
	BaseStep
}

func (G FullSqlInstanceGCStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	log.Info(ctx, "audit instance resource gc, instanceId:%s", a.state.InstanceId)
	var CloseType model.CloseType
	if a.state.CloseType == nil {
		CloseType = model.CloseType_CloseAuditCollectAndTls
	} else {
		// 删除实例的消息有这个值
		CloseType = *a.state.CloseType
	}
	err := a.fullSqlSvc.DeleteFullSqlResource(ctx, a.state.FollowInstanceId, a.state.TenantId, CloseType, a.state.DSType)
	if err == nil {
		ctx.Send(ctx.Self(), &shared.RemoveAuditInstance{InstanceId: a.state.FollowInstanceId})
	}
	return err
}

func (G FullSqlInstanceGCStep) MaxExecRetry() int {
	return 20
}

func (G FullSqlInstanceGCStep) ErrorWait() time.Duration {
	return time.Minute * 3
}

func (G FullSqlInstanceGCStep) GetStepName() string {
	return "FullSqlInstanceGCStep"
}

type PrepareDeleteFullSqlInstanceStateStep struct {
	BaseStep
}

func (p PrepareDeleteFullSqlInstanceStateStep) ProtectExec(ctx types.Context, actor *AuditLifecycleActor) error {
	log.Info(ctx, "InstanceId:%s, TenantId:%s", actor.state.InstanceId, actor.state.TenantId)
	audit, err := actor.fullSqlInstDAL.GetByID(ctx, actor.state.InstanceId, actor.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return err
	}
	actor.state.InstanceStatus = model.AuditStatus(audit.Status)
	actor.state.FollowInstanceId = audit.FollowInstanceID
	actor.state.DSType, err = model.DSTypeFromString(audit.DbType)
	if err != nil {
		log.Error(ctx, "DSType fail, error:%s", err)
		return err
	}
	actor.state.Region = audit.Region
	tlsRegion, err := actor.conf.Get(ctx).GetTLSRegion(i18n.GetSiteName(ctx, actor.conf.Get(ctx).FullSqlInnerAccountId))
	if err != nil {
		log.Error(ctx, "请检查运维面参数:TlsServiceEndpoint, tls region err:%s", err)
		return err
	}
	actor.state.TlsRegion = tlsRegion
	tlsEndpoint := actor.conf.Get(ctx).GetTlsRegionEndpoint(i18n.GetSiteName(ctx, actor.conf.Get(ctx).FullSqlInnerAccountId))
	actor.state.TlsEndpoint = tlsEndpoint
	actor.state.TlsDataType = model.StatisticDataType_FullSqlDetail
	log.Info(ctx, fmt.Sprintf("Init params:[%s]", libutils.Show(actor.state)))
	return nil
}

func (p PrepareDeleteFullSqlInstanceStateStep) MaxExecRetry() int {
	return 10
}

func (p PrepareDeleteFullSqlInstanceStateStep) GetStepName() string {
	return "PrepareDeleteFullSqlInstanceStateStep"
}

type UpdateInstanceStatusRunningStep struct {
	BaseStep
}

func (u UpdateInstanceStatusRunningStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	a.state.InstanceStatus = model.AuditStatus_Running
	err := a.fullSqlInstDAL.UpdateByFollowInstanceID(ctx, &dao.AuditTls{
		FollowInstanceID: a.state.FollowInstanceId,
		TenantID:         a.state.TenantId,
		Status:           int32(a.state.InstanceStatus),
	})
	return err
}

func (u UpdateInstanceStatusRunningStep) MaxExecRetry() int {
	return 10
}

func (u UpdateInstanceStatusRunningStep) GetStepName() string {
	return "UpdateInstanceStatusRunningStep"
}

func projectPreCheck(ctx types.Context, a *AuditLifecycleActor, tlsConfig *TlsConfig) (bool, error) {
	if tlsConfig.TlsProject != "" {
		return true, nil
	}
	// 如果实例与tls不在一个region的情况下会找不到project
	tlsRegion, err := a.conf.Get(ctx).GetTLSRegion(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId))
	if err != nil {
		log.Error(ctx, "请检查运维面参数:TlsServiceEndpoint, tls region err:%s", err)
		return false, err
	}
	pls, err := a.tlsDAL.GetProjects(context.TODO(), a.conf.Get(ctx).FullSqlInnerAccountId, tlsRegion)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Info(ctx, "project not exist in metadb, TenantId:%s, Region:%s", a.conf.Get(ctx).FullSqlInnerAccountId, tlsConfig.TlsRegion)
			return false, nil
		} else {
			log.Warn(ctx, "failed to get tls project list, err=%v", err)
			return false, err
		}
	}
	client, err := GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return false, err
	}
	for _, pl := range pls {
		project, err := client.DescribeProject(&tls_sdk.DescribeProjectRequest{
			CommonRequest: tls_sdk.CommonRequest{},
			ProjectID:     pl.TlsProjectId,
		})
		if err != nil {
			if strings.Contains(err.Error(), "ProjectNotExists") {
				log.Warn(ctx, "Project not exists:%s", err)
				continue
			} else {
				return false, err
			}
		}
		if project.TopicCount < a.conf.Get(ctx).InnerAccountTlsProjectTopicQuota {
			log.Info(ctx, "project topic count less than %s", a.conf.Get(ctx).InnerAccountTlsProjectTopicQuota)
			tlsConfig.TlsProject = pl.TlsProjectId
			return true, nil
		}
	}
	log.Info(ctx, "not find valid project")
	return false, nil
}

func projectProtectExec(ctx types.Context, a *AuditLifecycleActor, tlsConfig *TlsConfig) error {
	tlsClient, err := GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return err
	}
	// 创建新project
	projectPrefix := "dbw-sql-fullsql-"
	createProjectRequest := &tls_sdk.CreateProjectRequest{
		ProjectName: projectPrefix + uuid.New().String(),
		Region:      tlsConfig.TlsRegion,
		Description: consts.TLSDescription,
	}
	log.Info(ctx, "createProjectRequest:%+v", libutils.Show(createProjectRequest))
	createProjectResponse, err := tlsClient.CreateProject(createProjectRequest)
	log.Info(ctx, "createProjectResponse:%+v", libutils.Show(createProjectResponse))
	if err != nil {
		log.Info(ctx, "create project %s failed, error:%s", createProjectRequest.ProjectName, err.Error())
		return err
	}
	_, err = a.tlsDAL.Create(context.TODO(), &dao.Tls{
		TlsProjectId: createProjectResponse.ProjectID,
		Region:       tlsConfig.TlsRegion,
		TenantID:     a.conf.Get(ctx).FullSqlInnerAccountId,
		TlsEndpoint:  a.conf.Get(ctx).GetTlsRegionEndpoint(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId)),
	})
	if err != nil {
		log.Warn(ctx, "failed to create tls topic, err=%v", err)
	}
	tlsConfig.TlsProject = createProjectResponse.ProjectID
	return nil
}

func topicProectExec(ctx types.Context, a *AuditLifecycleActor, tlsConfig *TlsConfig) error {
	tlsClient, err := GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return err
	}

	createTopicRequest := &tls_sdk.CreateTopicRequest{
		ProjectID:   tlsConfig.TlsProject,
		TopicName:   formatTopicName(a.state.FollowInstanceId + "_" + tlsConfig.TlsDataType.String()),
		Description: consts.TLSDescription,
		Ttl:         uint16(tlsConfig.TlsTopicTTL),
		AutoSplit:   true,
		ShardCount:  4, // 成本占比低，每个分区提供的写入能力为 5MiB/s、500 次/s，读取能力为 5 MiB/s、100 次/s
	}
	log.Info(ctx, "createTopicRequest:%s", createTopicRequest)
	createTopicResponse, err := tlsClient.CreateTopic(createTopicRequest)
	if err != nil {
		log.Info(ctx, "create topic %s failed, error:%s", createTopicRequest.TopicName, err)
		return err
	}
	log.Info(ctx, " create topic %s response:%+v", createTopicRequest.TopicName, createTopicResponse)
	tlsConfig.TlsTopic = createTopicResponse.TopicID
	return nil
}

func tlsInfoSaveProtectExec(ctx types.Context, a *AuditLifecycleActor, tlsConfig *TlsConfig, instanceId string, tenantId string) error {
	if tlsConfig.TlsId == 0 {
		tlsId, err := a.tlsDAL.Create(context.TODO(), &dao.Tls{
			TlsProjectId: tlsConfig.TlsProject,
			TlsTopicId:   tlsConfig.TlsTopic,
			TlsEndpoint:  tlsConfig.TlsEndpoint,
			Region:       tlsConfig.TlsRegion,
			TenantID:     a.conf.Get(ctx).FullSqlInnerAccountId,
			Ttl:          int32(tlsConfig.TlsTopicTTL),
			IndexVersion: int32(tlsConfig.TlsIndexVersion),
		})
		if err != nil {
			log.Warn(ctx, "create tls meta error:%s", err.Error())
			return err
		}
		tlsConfig.TlsId = tlsId
		log.Info(ctx, "update TlsId:%d", tlsConfig.TlsId)
	}
	extra, err := a.instanceExtraTlsDAL.GetByInstanceAndType(ctx, instanceId, tlsConfig.TlsDataType.String())
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Warn(ctx, "create tls meta error:%s", err)
		return err
	}
	if err == nil {
		log.Warn(ctx, "tls meta always exist, extra tls:%s", libutils.Show(extra))
		return nil
	}
	extraTlsId, err := a.instanceExtraTlsDAL.Create(ctx, dao.InstanceExtraTls{
		InstanceId: instanceId,
		TenantId:   tenantId,
		DataType:   tlsConfig.TlsDataType.String(),
		TlsId:      tlsConfig.TlsId,
	})
	if err != nil {
		log.Warn(ctx, "create tls meta error:%s", err.Error())
		return err
	}
	log.Info(ctx, "%s tls id:%d", tlsConfig.TlsDataType.String(), extraTlsId)
	return nil
}

func indexPreCheck(ctx types.Context, a *AuditLifecycleActor, config *TlsConfig) (bool, error) {
	tlsClient, err := GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return false, err
	}
	_, err = tlsClient.DescribeIndex(&tls_sdk.DescribeIndexRequest{
		CommonRequest: tls_sdk.CommonRequest{},
		TopicID:       config.TlsTopic,
	})
	if err != nil {
		if strings.Contains(err.Error(), "IndexNotExists") {
			return false, nil
		} else {
			return false, err
		}
	} else {
		return true, nil
	}
}

func indexProtectExec(ctx types.Context, a *AuditLifecycleActor, config *TlsConfig, dsType model.DSType) error {
	tlsClient, err := GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return err
	}

	log.Info(ctx, "create new topic id : %s", config.TlsTopic)
	createIndexRequest, err := a.genTlsSvc.GenCreateIndexRequest(ctx, config.TlsTopic, dsType, config.TlsDataType, config.TlsIndexVersion)
	if err != nil {
		log.Warn(ctx, "GenCreateIndexRequest error:%s", err)
		return err
	}
	log.Info(ctx, "createIndexRequest : %+v", createIndexRequest)
	createIndexResp, err := tlsClient.CreateIndex(createIndexRequest)
	if err != nil {
		log.Info(ctx, "create tls index error, topic:%s, error:%s", config.TlsTopic, err.Error())
		return err
	}
	log.Info(ctx, "createIndexResp : %+v", createIndexResp)
	return nil
}

func modifyIndexProtectExec(ctx types.Context, a *AuditLifecycleActor, config *TlsConfig, dsType model.DSType) error {
	tlsClient, err := GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return err
	}
	if config.TlsId == 0 {
		return errors.New("tls config id should not be nil")
	}
	log.Info(ctx, "create new topic id : %s", config.TlsTopic)
	createIndexRequest, err := a.genTlsSvc.GenCreateIndexRequest(ctx, config.TlsTopic, dsType, config.TlsDataType, config.TlsIndexVersion)
	if err != nil {
		log.Warn(ctx, "GenCreateIndexRequest error:%s", err)
		return err
	}
	modifyIndexRequest := createIndexToModify(createIndexRequest)
	log.Info(ctx, "modifyIndexRequest : %+v", modifyIndexRequest)
	modifyIndexResp, err := tlsClient.ModifyIndex(modifyIndexRequest)
	if err != nil {
		log.Warn(ctx, "modify tls index error, topic:%s, error:%s", config.TlsTopic, err.Error())
		return err
	}
	log.Info(ctx, "modifyIndexResp : %+v", modifyIndexResp)
	tlsInfo, err := a.tlsDAL.GetFromAdmin(ctx, strconv.FormatInt(config.TlsId, 10))
	if err != nil {
		log.Warn(ctx, "get tls error, tls_id:%s, error:%s", config.TlsId, err.Error())
		return err
	}
	tlsInfo.IndexVersion = int32(config.TlsIndexVersion)
	err = a.tlsDAL.UpdateByID(ctx, tlsInfo.TenantID, tlsInfo)
	if err != nil {
		log.Warn(ctx, "update tls error, tls_id:%s, error:%s", config.TlsId, err.Error())
		return err
	}
	return nil
}

type FetchInnerProjectSqlTemplateStep struct {
	BaseStep
}

func (p FetchInnerProjectSqlTemplateStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	SqlTemplateTlsConf := a.state.SqlTemplateTls
	return projectPreCheck(ctx, a, SqlTemplateTlsConf)
}

func (p FetchInnerProjectSqlTemplateStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTemplateTlsConf := a.state.SqlTemplateTls
	return projectProtectExec(ctx, a, SqlTemplateTlsConf)
}

func (p FetchInnerProjectSqlTemplateStep) MaxExecRetry() int {
	return 10
}

func (p FetchInnerProjectSqlTemplateStep) GetStepName() string {
	return "FetchInnerProjectSqlTemplateStep"
}

type CreateInnerTopicSqlTemplateStep struct {
	BaseStep
}

func (p CreateInnerTopicSqlTemplateStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.SqlTemplateTls.TlsTopic == "" {
		return false, nil
	} else {
		return true, nil
	}
}

func (p CreateInnerTopicSqlTemplateStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTemplateTlsConf := a.state.SqlTemplateTls
	return topicProectExec(ctx, a, SqlTemplateTlsConf)
}

func (p CreateInnerTopicSqlTemplateStep) MaxExecRetry() int {
	return 10
}

func (p CreateInnerTopicSqlTemplateStep) GetStepName() string {
	return "CreateInnerTopicSqlTemplateStep"
}

type SaveInnerTLSInfoSqlTemplateStep struct {
	BaseStep
}

func (s SaveInnerTLSInfoSqlTemplateStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (s SaveInnerTLSInfoSqlTemplateStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTemplateTlsConf := a.state.SqlTemplateTls
	return tlsInfoSaveProtectExec(ctx, a, SqlTemplateTlsConf, a.state.InstanceId, a.state.TenantId)
}

func (s SaveInnerTLSInfoSqlTemplateStep) MaxExecRetry() int {
	return 10
}

func (s SaveInnerTLSInfoSqlTemplateStep) GetStepName() string {
	return "SaveInnerTLSInfoSqlTemplateStep"
}

type PrepareFullSqlTLSIndexSqlTemplateStep struct {
	BaseStep
}

func (p PrepareFullSqlTLSIndexSqlTemplateStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	SqlTemplateTlsConf := a.state.SqlTemplateTls
	return indexPreCheck(ctx, a, SqlTemplateTlsConf)
}

func (p PrepareFullSqlTLSIndexSqlTemplateStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTemplateTlsConf := a.state.SqlTemplateTls
	return indexProtectExec(ctx, a, SqlTemplateTlsConf, a.state.DSType)
}

func (p PrepareFullSqlTLSIndexSqlTemplateStep) MaxExecRetry() int {
	return 10
}

func (p PrepareFullSqlTLSIndexSqlTemplateStep) GetStepName() string {
	return "PrepareFullSqlTLSIndexSqlTemplateStep"
}

// -----------------------

type FetchInnerProjectSqlCountStep struct {
	BaseStep
}

func (p FetchInnerProjectSqlCountStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	SqlCountTls := a.state.SqlCountTls
	return projectPreCheck(ctx, a, SqlCountTls)
}

func (p FetchInnerProjectSqlCountStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlCountTls := a.state.SqlCountTls
	return projectProtectExec(ctx, a, SqlCountTls)
}

func (p FetchInnerProjectSqlCountStep) MaxExecRetry() int {
	return 10
}

func (p FetchInnerProjectSqlCountStep) GetStepName() string {
	return "FetchInnerProjectSqlCountStep"
}

type CreateInnerTopicSqlCountStep struct {
	BaseStep
}

func (p CreateInnerTopicSqlCountStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.SqlCountTls.TlsTopic == "" {
		return false, nil
	} else {
		return true, nil
	}
}

func (p CreateInnerTopicSqlCountStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlCountTls := a.state.SqlCountTls
	return topicProectExec(ctx, a, SqlCountTls)
}

func (p CreateInnerTopicSqlCountStep) MaxExecRetry() int {
	return 10
}

func (p CreateInnerTopicSqlCountStep) GetStepName() string {
	return "CreateInnerTopicSqlCountStep"
}

type SaveInnerTLSInfoSqlCountStep struct {
	BaseStep
}

func (s SaveInnerTLSInfoSqlCountStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (s SaveInnerTLSInfoSqlCountStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlCountTls := a.state.SqlCountTls
	return tlsInfoSaveProtectExec(ctx, a, SqlCountTls, a.state.InstanceId, a.state.TenantId)
}

func (s SaveInnerTLSInfoSqlCountStep) MaxExecRetry() int {
	return 10
}

func (s SaveInnerTLSInfoSqlCountStep) GetStepName() string {
	return "SaveInnerTLSInfoSqlCountStep"
}

type PrepareFullSqlTLSIndexSqlCountStep struct {
	BaseStep
}

func (p PrepareFullSqlTLSIndexSqlCountStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	SqlCountTls := a.state.SqlCountTls
	return indexPreCheck(ctx, a, SqlCountTls)
}

func (p PrepareFullSqlTLSIndexSqlCountStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlCountTls := a.state.SqlCountTls
	return indexProtectExec(ctx, a, SqlCountTls, a.state.DSType)
}

func (p PrepareFullSqlTLSIndexSqlCountStep) MaxExecRetry() int {
	return 10
}

func (p PrepareFullSqlTLSIndexSqlCountStep) GetStepName() string {
	return "PrepareFullSqlTLSIndexSqlCountStep"
}

// -----------SqlTableStep Start------------

type FetchInnerProjectSqlTableStep struct {
	BaseStep
}

func (p FetchInnerProjectSqlTableStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	SqlTableAggrTls := a.state.SqlTableAggrTls
	return projectPreCheck(ctx, a, SqlTableAggrTls)
}

func (p FetchInnerProjectSqlTableStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTableAggrTls := a.state.SqlTableAggrTls
	return projectProtectExec(ctx, a, SqlTableAggrTls)
}

func (p FetchInnerProjectSqlTableStep) MaxExecRetry() int {
	return 10
}

func (p FetchInnerProjectSqlTableStep) GetStepName() string {
	return "FetchInnerProjectSqlTableStep"
}

type CreateInnerTopicSqlTableStep struct {
	BaseStep
}

func (p CreateInnerTopicSqlTableStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.SqlTableAggrTls.TlsTopic == "" {
		return false, nil
	} else {
		return true, nil
	}
}

func (p CreateInnerTopicSqlTableStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTableAggrTls := a.state.SqlTableAggrTls
	return topicProectExec(ctx, a, SqlTableAggrTls)
}

func (p CreateInnerTopicSqlTableStep) MaxExecRetry() int {
	return 10
}

func (p CreateInnerTopicSqlTableStep) GetStepName() string {
	return "CreateInnerTopicSqlTableStep"
}

type SaveInnerTLSInfoSqlTableStep struct {
	BaseStep
}

func (s SaveInnerTLSInfoSqlTableStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (s SaveInnerTLSInfoSqlTableStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTableAggrTls := a.state.SqlTableAggrTls
	return tlsInfoSaveProtectExec(ctx, a, SqlTableAggrTls, a.state.InstanceId, a.state.TenantId)
}

func (s SaveInnerTLSInfoSqlTableStep) MaxExecRetry() int {
	return 10
}

func (s SaveInnerTLSInfoSqlTableStep) GetStepName() string {
	return "SaveInnerTLSInfoSqlTableStep"
}

type PrepareFullSqlTLSIndexSqlTableStep struct {
	BaseStep
}

func (p PrepareFullSqlTLSIndexSqlTableStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	SqlTableAggrTls := a.state.SqlTableAggrTls
	return indexPreCheck(ctx, a, SqlTableAggrTls)
}

func (p PrepareFullSqlTLSIndexSqlTableStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	SqlTableAggrTls := a.state.SqlTableAggrTls
	return indexProtectExec(ctx, a, SqlTableAggrTls, a.state.DSType)
}

func (p PrepareFullSqlTLSIndexSqlTableStep) MaxExecRetry() int {
	return 10
}

func (p PrepareFullSqlTLSIndexSqlTableStep) GetStepName() string {
	return "PrepareFullSqlTLSIndexSqlTableStep"
}

// -----------SqlTableStep End------------

type ConfigStatisticPipelineStep struct {
	BaseStep
}

func (c ConfigStatisticPipelineStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {

	err := prepareTlsCnf(ctx, a, a.state.SqlTemplateTls)
	if err != nil {
		return false, err
	}
	err = prepareTlsCnf(ctx, a, a.state.SqlCountTls)
	if err != nil {
		return false, err
	}
	err = prepareTlsCnf(ctx, a, a.state.SqlTableAggrTls)
	if err != nil {
		return false, err
	}
	err = prepareTlsCnf(ctx, a, a.state.SqlTopTls)
	if err != nil {
		return false, err
	}
	return false, nil
}

func prepareTlsCnf(ctx types.Context, a *AuditLifecycleActor, tlsCnf *TlsConfig) error {
	if tlsCnf == nil {
		log.Warn(ctx, "prepare tls config can not be nil, need data type to load")
		return errors.New("tlsCnf can not be nil, need init TlsConfig data type.")
	}
	if tlsCnf.TlsProject == "" || tlsCnf.TlsTopic == "" {
		log.Info(ctx, "load tls config from metadata")
		extraTls, err := a.instanceExtraTlsDAL.GetByInstanceAndType(ctx, a.state.InstanceId, tlsCnf.TlsDataType.String())
		if err != nil {
			log.Warn(ctx, "GetByInstanceAndType error:%s", err)
			return err
		}
		tlsInfo, err := a.tlsDAL.GetFromAdmin(ctx, strconv.FormatInt(extraTls.TlsId, 10))
		if err != nil {
			log.Warn(ctx, "tlsDAL Get error:%s", err)
			return err
		}
		tlsCnf.TlsProject = tlsInfo.TlsProjectId
		tlsCnf.TlsTopic = tlsInfo.TlsTopicId
		tlsCnf.TlsId = extraTls.TlsId
		log.Info(ctx, "load tls config:%s", libutils.Show(tlsCnf))
	}
	return nil
}

func (c ConfigStatisticPipelineStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	c3Conf := a.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	conf := a.conf.Get(ctx)
	tlsRegion, _ := conf.GetTLSRegion(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId))

	output := map[string]*zkconfig.TlsConfig{
		a.state.SqlTemplateTls.TlsDataType.String(): {
			Endpoint: conf.GetTlsRegionEndpoint(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId)),
			Ak:       c3Conf.TLSServiceAccessKey,
			Sk:       c3Conf.TLSServiceSecretKey,
			Project:  a.state.SqlTemplateTls.TlsProject,
			Topic:    a.state.SqlTemplateTls.TlsTopic,
			Region:   tlsRegion,
		},
		a.state.SqlCountTls.TlsDataType.String(): {
			Endpoint: conf.GetTlsRegionEndpoint(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId)),
			Ak:       c3Conf.TLSServiceAccessKey,
			Sk:       c3Conf.TLSServiceSecretKey,
			Project:  a.state.SqlCountTls.TlsProject,
			Topic:    a.state.SqlCountTls.TlsTopic,
			Region:   tlsRegion,
		},
		a.state.SqlTableAggrTls.TlsDataType.String(): {
			Endpoint: conf.GetTlsRegionEndpoint(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId)),
			Ak:       c3Conf.TLSServiceAccessKey,
			Sk:       c3Conf.TLSServiceSecretKey,
			Project:  a.state.SqlTableAggrTls.TlsProject,
			Topic:    a.state.SqlTableAggrTls.TlsTopic,
			Region:   tlsRegion,
		},
		a.state.SqlTopTls.TlsDataType.String(): {
			Endpoint: conf.GetTlsRegionEndpoint(i18n.GetSiteName(ctx, a.conf.Get(ctx).FullSqlInnerAccountId)),
			Ak:       c3Conf.TLSServiceAccessKey,
			Sk:       c3Conf.TLSServiceSecretKey,
			Project:  a.state.SqlTopTls.TlsProject,
			Topic:    a.state.SqlTopTls.TlsTopic,
			Region:   tlsRegion,
		},
	}
	log.Info(ctx, "statistic Output Config: %s", libutils.Show(output))
	err := a.fullSqlConfigService.ConfigStatisticTlsX(ctx, zkconfig.ZKConfig{
		Hosts:      conf.FullSqlZkHosts,
		AuthScheme: conf.FullSqlZkAuthScheme,
		User:       conf.FullSqlZkUser,
		Password:   conf.FullSqlZkPassword,
	}, a.state.FollowInstanceId, output)
	if err != nil {
		log.Warn(ctx, "zk pipe config error:%s", err)
		return err
	}

	return nil
}

func (c ConfigStatisticPipelineStep) MaxExecRetry() int {
	return 10
}

func (c ConfigStatisticPipelineStep) ErrorWait() time.Duration {
	return time.Second * 5
}

func (c ConfigStatisticPipelineStep) GetStepName() string {
	return "ConfigStatisticPipelineStep"
}

type CancelConfigStatisticPipelineStep struct {
	BaseStep
}

func (c CancelConfigStatisticPipelineStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (c CancelConfigStatisticPipelineStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	conf := a.conf.Get(ctx)
	err := a.fullSqlConfigService.ConfigDeleteStatisticTlsX(ctx, zkconfig.ZKConfig{
		Hosts:      conf.FullSqlZkHosts,
		AuthScheme: conf.FullSqlZkAuthScheme,
		User:       conf.FullSqlZkUser,
		Password:   conf.FullSqlZkPassword,
	}, a.state.FollowInstanceId)
	if err != nil {
		log.Warn(ctx, "zk pipe config error:%s", err)
		return err
	}
	return nil
}

func (c CancelConfigStatisticPipelineStep) MaxExecRetry() int {
	return 10
}

func (c CancelConfigStatisticPipelineStep) ErrorWait() time.Duration {
	return time.Second * 5
}

func (c CancelConfigStatisticPipelineStep) GetStepName() string {
	return "CancelConfigStatisticPipelineStep"
}

type ClearSqlTemplateCacheStep struct {
	BaseStep
}

func (c ClearSqlTemplateCacheStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	conf := a.conf.Get(ctx)
	err := a.fullSqlConfigService.ClearInstanceCache(ctx, conf.FullSqlRedisAddr, conf.FullSqlRedisPwd, 0, a.state.FollowInstanceId)
	if err != nil {
		log.Warn(ctx, "clear sql template in redis cache error:%s", err)
		return err
	}
	return nil
}

func (c ClearSqlTemplateCacheStep) MaxExecRetry() int {
	return 10
}

func (c ClearSqlTemplateCacheStep) ErrorWait() time.Duration {
	return time.Second * 10
}

func (c ClearSqlTemplateCacheStep) GetStepName() string {
	return "ClearSqlTemplateCacheStep"
}

type CancelConfigPipelineStep struct {
	BaseStep
}

func (c CancelConfigPipelineStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (c CancelConfigPipelineStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	conf := a.conf.Get(ctx)
	err := a.fullSqlConfigService.PipeDeleteConfigX(ctx, zkconfig.ZKConfig{
		Hosts:      conf.FullSqlZkHosts,
		AuthScheme: conf.FullSqlZkAuthScheme,
		User:       conf.FullSqlZkUser,
		Password:   conf.FullSqlZkPassword,
	}, a.state.FollowInstanceId)
	if err != nil {
		log.Warn(ctx, " ModifyFullSQLLogConfig close fullsql err:%s", err)
		return err
	}
	return nil
}

func (c CancelConfigPipelineStep) MaxExecRetry() int {
	return 10
}

func (c CancelConfigPipelineStep) ErrorWait() time.Duration {
	return time.Second * 5
}

func (c CancelConfigPipelineStep) GetStepName() string {
	return "CancelConfigPipelineStep"
}

type FullSqlRemoveTopicsStep struct {
	BaseStep
}

func (c FullSqlRemoveTopicsStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	// 尝试清理明细topic日志
	err := a.fullSqlSvc.DeleteTopic(ctx, a.state.TlsId)
	if err != nil {
		log.Warn(ctx, "clear sql detail tls topic error:%s", err)
		return err
	}
	// 尝试清理统计topic日志
	if a.state.SqlTemplateTls != nil {
		err = a.fullSqlSvc.DeleteTopic(ctx, a.state.SqlTemplateTls.TlsId)
		if err != nil {
			log.Warn(ctx, "clear sql template tls topic error:%s", err)
			return err
		}
	}
	if a.state.SqlCountTls != nil {
		err = a.fullSqlSvc.DeleteTopic(ctx, a.state.SqlCountTls.TlsId)
		if err != nil {
			log.Warn(ctx, "clear sql count tls topic error:%s", err)
			return err
		}
	}
	if a.state.SqlTableAggrTls != nil {
		err = a.fullSqlSvc.DeleteTopic(ctx, a.state.SqlTableAggrTls.TlsId)
		if err != nil {
			log.Warn(ctx, "clear sql table aggr tls topic error:%s", err)
			return err
		}
	}
	if a.state.SqlTopTls != nil {
		err = a.fullSqlSvc.DeleteTopic(ctx, a.state.SqlTopTls.TlsId)
		if err != nil {
			log.Warn(ctx, "clear sql top n tls topic error:%s", err)
			return err
		}
	}
	return nil
}

func (c FullSqlRemoveTopicsStep) MaxExecRetry() int {
	return 10
}

func (c FullSqlRemoveTopicsStep) ErrorWait() time.Duration {
	return time.Second * 10
}

func (c FullSqlRemoveTopicsStep) GetStepName() string {
	return "FullSqlRemoveTopicsStep"
}

type RemoveCollectionFingerprintStep struct {
	BaseStep
}

func (c RemoveCollectionFingerprintStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.fullSqlSvc.DeleteAllFullSqlCollectionFingerprint(ctx, a.state.InstanceId)
	if err != nil {
		return err
	}
	return nil
}

func (c RemoveCollectionFingerprintStep) MaxExecRetry() int {
	return 10
}

func (c RemoveCollectionFingerprintStep) ErrorWait() time.Duration {
	return time.Second * 10
}

func (c RemoveCollectionFingerprintStep) GetStepName() string {
	return "RemoveCollectionFingerprintStep"
}

type PrepareDefaultIAMProjectStep struct {
	BaseStep
}

func (p PrepareDefaultIAMProjectStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (p PrepareDefaultIAMProjectStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.projectSvc.AddResourceToProject(ctx, "default", a.state.Region, a.state.TenantId, a.state.InstanceId)
	if err != nil {
		log.Warn(ctx, "AddResourceToProject error %s", err)
		return err
	}
	return nil
}

func (p PrepareDefaultIAMProjectStep) MaxExecRetry() int {
	return 10
}

func (p PrepareDefaultIAMProjectStep) GetStepName() string {
	return "PrepareDefaultIAMProjectStep"
}
