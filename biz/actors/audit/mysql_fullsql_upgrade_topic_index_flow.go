package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

// 升级全量SQL
var upgradeFullSqlTopicIndexSteps = map[string]IdempotentStep{
	// 创建实例
	// PreCheck 实例状态
	"PrepareFullSqlInstanceUpgradeStateStep": &PrepareFullSqlInstanceUpgradeStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ModifyFullSqlTLSIndexDetailStep"]
			},
		},
	},

	// 修改详情的索引
	"ModifyFullSqlTLSIndexDetailStep": &ModifyFullSqlTLSIndexDetailStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ModifyFullSqlTLSIndexTableStatisticStep"]
			},
		},
	},
	// 修改表分析的索引
	"ModifyFullSqlTLSIndexTableStatisticStep": &ModifyFullSqlTLSIndexTableStatisticStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ModifyFullSqlTLSIndexSqlTemplateStep"]
			},
		},
	},

	// 修改模版索引
	"ModifyFullSqlTLSIndexSqlTemplateStep": &ModifyFullSqlTLSIndexSqlTemplateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ModifyFullSqlTLSIndexSqlCountStep"]
			},
		},
	},
	// 修改数量索引
	"ModifyFullSqlTLSIndexSqlCountStep": &ModifyFullSqlTLSIndexSqlCountStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ModifyFullSqlTLSIndexTopSqlStep"]
			},
		},
	},

	// 修改top n 索引
	"ModifyFullSqlTLSIndexTopSqlStep": &ModifyFullSqlTLSIndexTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}

// NewMysqlFullSqlUpgradeIndexLifecycleActor
func NewMysqlFullSqlUpgradeIndexLifecycleActor(in NewAuditCreateLifecycleActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.MysqlFullSqlUpgradeIndexActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &AuditLifecycleActor{
				state:           newTaskState(state),
				idempotentSteps: upgradeFullSqlTopicIndexSteps,

				source:               in.DataSource,
				crossAuthSvc:         in.CrossAuthSvc,
				conf:                 in.Conf,
				c3Conf:               in.C3Conf,
				loc:                  in.Loc,
				tagSvc:               in.TagSvc,
				projectSvc:           in.ProjectSvc,
				tlsDAL:               in.TlsDAL,
				lcSvc:                in.LCSvc,
				billSvc:              in.BillSvc,
				publishEventSvc:      in.PublishEventSvc,
				auditService:         in.AuditService,
				fullSqlInstDAL:       in.FullSqlInstDAL,
				fullSqlSvc:           in.FullSqlSvc,
				fullSqlConfigService: in.FullSqlConfigService,
				instanceExtraTlsDAL:  in.InstanceExtraTlsDAL,
			}
			return actor
		}),
	}
}
