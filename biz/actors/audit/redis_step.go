package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit/k8s"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls/tls_enhance"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"errors"
	tls_sdk "github.com/volcengine/volc-sdk-golang/service/tls"
	"k8s.io/utils/pointer"
)

type CreateInnerTLSHostGroupStep struct {
	BaseStep
}

func (p CreateInnerTLSHostGroupStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.TlsHostGroupId != "" {
		return true, nil
	} else {
		tlsClient, err := p.GetInnerAccountTlsClient(ctx, a)
		if err != nil {
			return false, err
		}
		hostGroupName := k8s.GetHostIdentifier(a.conf.Get(ctx).FullSqlInnerAccountId, a.state.Region)
		req := &tls_sdk.DescribeHostGroupsRequest{
			HostGroupName: &hostGroupName,
		}
		log.Info(ctx, "req:%+v", req.HostGroupName)
		groups, err := tlsClient.DescribeHostGroups(req)
		log.Info(ctx, "resp:%+v, err:%s", groups, err)
		if err != nil {
			return false, err
		}
		if groups.HostGroupHostsRulesInfos != nil && len(groups.HostGroupHostsRulesInfos) > 0 {
			a.state.TlsHostGroupId = groups.HostGroupHostsRulesInfos[0].HostGroupInfo.HostGroupID
			return true, nil
		}
		return false, nil
	}
}

func (p CreateInnerTLSHostGroupStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return err
	}
	// 机器组名与标识一样
	hostGroupName := k8s.GetHostIdentifier(a.conf.Get(ctx).FullSqlInnerAccountId, a.state.Region)
	hostIdentifier := k8s.GetHostIdentifier(a.conf.Get(ctx).FullSqlInnerAccountId, a.state.Region)
	createHostGroupReq := tls_sdk.CreateHostGroupRequest{
		CommonRequest:  tls_sdk.CommonRequest{Headers: IgnoreHeaders},
		HostGroupName:  hostGroupName,
		HostGroupType:  "Label",
		HostIdentifier: &hostIdentifier,
		AutoUpdate:     pointer.Bool(false),
	}
	hostGroup, err := tlsClient.CreateHostGroup(&createHostGroupReq)
	if err != nil {
		return err
	}
	a.state.TlsHostGroupId = hostGroup.HostGroupID
	return nil
}

func (p CreateInnerTLSHostGroupStep) MaxExecRetry() int {
	return 10
}

func (p CreateInnerTLSHostGroupStep) GetStepName() string {
	return "CreateInnerTLSHostGroupStep"
}

type CreateInnerTLSRuleStep struct {
	BaseStep
}

func (p CreateInnerTLSRuleStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.TlsRuleId != "" {
		return true, nil
	} else {
		tlsClient, err := p.GetInnerAccountTlsClient(ctx, a)
		if err != nil {
			return false, err
		}
		ruleName := k8s.GetRuleName(a.conf.Get(ctx).FullSqlInnerAccountId, a.state.Region, a.state.FollowInstanceId)
		rules, err := tlsClient.DescribeRules(&tls_sdk.DescribeRulesRequest{
			ProjectID: a.state.TlsProject,
			RuleName:  &ruleName,
		})
		if err != nil {
			return false, err
		}
		if rules.RuleInfos != nil && len(rules.RuleInfos) > 0 {
			a.state.TlsRuleId = rules.RuleInfos[0].RuleID
			return true, nil
		}
		return false, nil
	}
}

func (p CreateInnerTLSRuleStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return err
	}
	switch a.state.DSType {
	case model.DSType_Redis:
		logType := JsonLog
		inputType := 2
		logSample := "{\"account\":\"default\",\"cmd\":\"COMMAND DOCS\",\"db\":0,\"ip\":\"127.0.0.1\",\"time\":*************,\"latency\":283711,\"type\":\"audit\"}"
		createRuleReq := tls_enhance.CreateRuleEnhanceRequest{
			CommonRequest: tls_sdk.CommonRequest{Headers: IgnoreHeaders},
			TopicID:       a.state.TlsTopic,
			RuleName:      k8s.GetRuleName(a.state.TenantId, a.state.Region, a.state.FollowInstanceId),
			Paths:         &[]string{"/var/log/{__pod_name__}/*_audit*log"},
			LogType:       &logType,
			ExtractRule: &tls_sdk.ExtractRule{
				//BeginRegex:          PGLogRegex,
				UnMatchUpLoadSwitch: true,
				UnMatchLogKey:       "LogParseFailed",
				TimeKey:             "time",
				TimeFormat:          "%F",
			},
			LogSample: &logSample,
			InputType: &inputType,
			ContainerRule: &tls_sdk.ContainerRule{
				//ContainerNameRegex: fmt.Sprintf("%s*", a.state.FollowInstanceId),
				KubernetesRule: tls_sdk.KubernetesRule{
					NamespaceNameRegex: RedisMultiNameSpace,
					IncludePodLabelRegex: map[string]string{
						RedisInstLabel: a.state.FollowInstanceId,
					},
					ExcludePodLabelRegex: map[string]string{
						RedisFilterLabel: "redis",
					},
					LabelTag: map[string]string{
						RedisInstLabel:   RedisInstLabel,
						RedisFilterLabel: RedisFilterLabel,
					},
				},
			},
		}
		log.Info(ctx, "createRuleReq:%+v", libutils.Show(createRuleReq))
		createRuleResp, err := tls_enhance.CreateRule(tlsClient, &createRuleReq)
		log.Info(ctx, "createRuleResp:%+v ,error:%s", libutils.Show(createRuleResp), err)
		if err != nil {
			log.Info(ctx, "createRule fail:%s", err)
			return err
		}
		a.state.TlsRuleId = createRuleResp.RuleID
	case model.DSType_Mongo:
		logType := JsonLog
		inputType := 2
		logSample := "{ \"atype\" : \"insert\", \"otype\" : \"Insert\", \"level\" : \"Normal\", \"component\" : \"mongod\", \"ts\" : \"2025-05-19T13:54:18.100+00:00\", \"local\" : { \"ip\" : \"***********\", \"port\" : 27017 }, \"remote\" : { \"ip\" : \"\", \"port\" : 27017 }, \"user\" : { \"user\" : \"spencer\", \"db\" : \"test\" }, \"cost\" : 0, \"db\" : \"testDB\", \"coll\" : \"testCol\", \"param\" : { \"command\" : \"insert\", \"ns\" : \"testDB.testCol\", \"db\" : \"testDB\", \"coll\" : \"testCol\", \"args\" : { \"obj\" : { \"documents\" : [ { \"_id\" : 1 }, { \"_id\" : 2 } ] } } }, \"result\" : 0 }"
		createRuleReq := tls_enhance.CreateRuleEnhanceRequest{
			CommonRequest: tls_sdk.CommonRequest{Headers: IgnoreHeaders},
			TopicID:       a.state.TlsTopic,
			RuleName:      k8s.GetRuleName(a.state.TenantId, a.state.Region, a.state.FollowInstanceId),
			Paths:         &[]string{"/var/log/mongodb/audit*"},
			LogType:       &logType,
			ExtractRule: &tls_sdk.ExtractRule{
				UnMatchUpLoadSwitch: true,
				UnMatchLogKey:       "LogParseFailed",
				TimeKey:             "time",
				TimeFormat:          "%F",
			},
			LogSample: &logSample,
			InputType: &inputType,
			ContainerRule: &tls_sdk.ContainerRule{
				ContainerNameRegex: MongoContainerName,
				KubernetesRule: tls_sdk.KubernetesRule{
					NamespaceNameRegex: MongoNameSpace,
					IncludePodLabelRegex: map[string]string{
						MongoInstLabel: a.state.FollowInstanceId,
					},
					ExcludePodLabelRegex: map[string]string{},
					LabelTag: map[string]string{
						MongoInstLabel: MongoInstLabel,
					},
				},
			},
		}
		log.Info(ctx, "createRuleReq:%+v", libutils.Show(createRuleReq))
		createRuleResp, err := tls_enhance.CreateRule(tlsClient, &createRuleReq)
		log.Info(ctx, "createRuleResp:%+v ,error:%s", libutils.Show(createRuleResp), err)
		if err != nil {
			log.Info(ctx, "createRule fail:%s", err)
			return err
		}
		a.state.TlsRuleId = createRuleResp.RuleID
	default:
		log.Error(ctx, "unknown type")
		return consts.ErrorOf(model.ErrorCode_InstanceTypeNotSupport)
	}

	return nil
}

func (p CreateInnerTLSRuleStep) MaxExecRetry() int {
	return 10
}

func (p CreateInnerTLSRuleStep) GetStepName() string {
	return "CreateInnerTLSRuleStep"
}

type ApplyRuleToInnerHostGroupsStep struct {
	BaseStep
}

func (p ApplyRuleToInnerHostGroupsStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	tlsClient, err := p.GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return false, err
	}
	rule, err := tlsClient.DescribeRule(&tls_sdk.DescribeRuleRequest{
		RuleID: a.state.TlsRuleId,
	})
	if err != nil {
		return false, err
	}
	if len(rule.HostGroupInfos) > 0 {
		if rule.HostGroupInfos[0].HostGroupID == a.state.TlsHostGroupId {
			return true, nil
		} else {
			return false, errors.New("wtf rule not bind with the host group")
		}
	}
	return false, nil
}

func (p ApplyRuleToInnerHostGroupsStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetInnerAccountTlsClient(ctx, a)
	if err != nil {
		return err
	}
	applyRuleToHostGroupsRequest := &tls_sdk.ApplyRuleToHostGroupsRequest{
		RuleID:       a.state.TlsRuleId,
		HostGroupIDs: []string{a.state.TlsHostGroupId},
	}
	log.Info(ctx, "applyRuleToHostGroupsRequest:%+v", applyRuleToHostGroupsRequest)
	applyRuleToHostGroupsResp, err := tlsClient.ApplyRuleToHostGroups(applyRuleToHostGroupsRequest)
	log.Info(ctx, "applyRuleToHostGroupsResp:%+v", applyRuleToHostGroupsResp)
	if err != nil {
		log.Info(ctx, "applyRuleToHostGroups failed:%s", err)
		return err
	}
	return nil
}

func (p ApplyRuleToInnerHostGroupsStep) MaxExecRetry() int {
	return 10
}

func (p ApplyRuleToInnerHostGroupsStep) GetStepName() string {
	return "ApplyRuleToInnerHostGroupsStep"
}

type CreateInnerLogCollectorResourceStep struct {
	BaseStep
}

func (c CreateInnerLogCollectorResourceStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (c CreateInnerLogCollectorResourceStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	applicationC3Config := a.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	innerAK := applicationC3Config.Application.TLSServiceAccessKey
	innerSK := applicationC3Config.Application.TLSServiceSecretKey
	for _, clusterName := range a.state.AzClusterMap {
		var nodePools []string
		for k, v := range a.state.NodePoolsToCluster {
			if v == clusterName {
				nodePools = append(nodePools, k)
			}
		}
		err := a.lcSvc.DeployInnerLogCollector(ctx, a.state.DSType, &entity.ClusterInfo{ClusterName: clusterName}, nodePools, k8s.TenantConfig{
			Endpoint:     a.state.TlsEndpoint,
			SecretID:     innerAK,
			SecretKey:    innerSK,
			Region:       a.state.TlsRegion,
			VolcEndpoint: a.conf.Get(ctx).VPCServiceEndpoint,
			AccountID:    a.conf.Get(ctx).FullSqlInnerAccountId,
			RoleProvider: k8s.RoleProviderStaticProvider,
			RoleName:     "",
			// 这里机器组标识
			ServiceName: k8s.GetHostIdentifier(a.conf.Get(ctx).FullSqlInnerAccountId, a.state.Region),
		})
		if err != nil {
			return err
		}
	}
	ctx.Send(ctx.Self(), &shared.NewAuditInstance{InstanceId: a.state.FollowInstanceId})
	return nil
}

func (c CreateInnerLogCollectorResourceStep) MaxExecRetry() int {
	return 10
}

func (c CreateInnerLogCollectorResourceStep) GetStepName() string {
	return "CreateInnerLogCollectorResourceStep"
}
