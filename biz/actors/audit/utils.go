package audit

import (
	"bytes"
	"github.com/olekukonko/tablewriter"
)

type LogType = string

const (
	MinimalistLog LogType = "minimalist_log"
	MultilineLog  LogType = "multiline_log"
	DelimiterLog  LogType = "delimiter_log"
	JsonLog       LogType = "json_log"
	FullregexLog  LogType = "fullregex_log"
)

var IgnoreHeaders = map[string]string{
	"CsTag": "dbw-hide",
}

func DrawStepTrace(data [][]string) string {
	return DrawTable([]string{"Step", "Comment", "Timestamp"}, data)
}

func DrawTable(column []string, data [][]string) string {
	buffer := &bytes.Buffer{}
	table := tablewriter.NewWriter(buffer)
	table.SetColWidth(200)
	table.SetHeader(column)
	table.AppendBulk(data[0:])

	table.Render()
	return buffer.String()
}
