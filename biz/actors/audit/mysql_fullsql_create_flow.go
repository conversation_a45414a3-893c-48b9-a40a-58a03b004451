package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

var createFullSqlSteps = map[string]IdempotentStep{
	// 创建实例
	// PreCheck 实例状态
	"PrepareFullSqlInstanceStateStep": &PrepareFullSqlInstanceStateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				switch a.state.InstanceStatus {
				case model.AuditStatus_Running:
					// 补单情况
					return a.idempotentSteps["NotifyCreateOrderStep"]
				case model.AuditStatus_OrderNew:
					// 更新实例状态
					return a.idempotentSteps["UpdateFullSqlInstanceStatusCreatingStep"]
				default:
					// 其他状态直接返回
					log.Error(ctx, "create full sql, get unexpected statue.")
				}
				return nil
			},
		},
	},
	// UpdateCreatingStatue
	"UpdateFullSqlInstanceStatusCreatingStep": &UpdateFullSqlInstanceStatusCreatingStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				switch a.state.DSType {
				case model.DSType_MySQL:
					return a.idempotentSteps["PrepareIAMProjectStep"]
				case model.DSType_VeDBMySQL:
					return a.idempotentSteps["PrepareIAMProjectStep"]
				case model.DSType_MetaMySQL:
					return a.idempotentSteps["PrepareDefaultIAMProjectStep"]
				default:
					return a.idempotentSteps["PrepareIAMProjectStep"]
				}
			},
		},
	},
	// AddDefaultProject
	"PrepareDefaultIAMProjectStep": &PrepareDefaultIAMProjectStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectStep"]
			},
		},
	},
	// AddProject
	"PrepareIAMProjectStep": &PrepareIAMProjectStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareIAMProjectTagStep"]
			},
		},
	},
	// AddSystemTag
	"PrepareIAMProjectTagStep": &PrepareIAMProjectTagStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["TagResourceStep"]
		}},
	},
	// Add custom tag 添加用户标签
	"TagResourceStep": &TagResourceStep{
		BaseStep: BaseStep{NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
			return a.idempotentSteps["FetchInnerProjectStep"]
		}},
	},
	// 获取或者创建TLS Project
	"FetchInnerProjectStep": &FetchInnerProjectStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicStep": &CreateInnerTopicStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoStep": &SaveInnerTLSInfoStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareInnerTLSIndexStep"]
			},
		},
	},
	// 创建索引
	"PrepareInnerTLSIndexStep": &PrepareInnerTLSIndexStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectSqlTemplateStep"]
			},
		},
	},

	// 创建sql模版TLS资源
	// 获取或者创建TLS Project
	"FetchInnerProjectSqlTemplateStep": &FetchInnerProjectSqlTemplateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicSqlTemplateStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicSqlTemplateStep": &CreateInnerTopicSqlTemplateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoSqlTemplateStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoSqlTemplateStep": &SaveInnerTLSInfoSqlTemplateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareFullSqlTLSIndexSqlTemplateStep"]
			},
		},
	},
	// 创建索引
	"PrepareFullSqlTLSIndexSqlTemplateStep": &PrepareFullSqlTLSIndexSqlTemplateStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectSqlCountStep"]
			},
		},
	},

	// 创建sql指标TLS资源
	// 获取或者创建TLS Project
	"FetchInnerProjectSqlCountStep": &FetchInnerProjectSqlCountStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicSqlCountStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicSqlCountStep": &CreateInnerTopicSqlCountStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoSqlCountStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoSqlCountStep": &SaveInnerTLSInfoSqlCountStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareFullSqlTLSIndexSqlCountStep"]
			},
		},
	},
	// 创建索引
	"PrepareFullSqlTLSIndexSqlCountStep": &PrepareFullSqlTLSIndexSqlCountStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectSqlTableStep"]
			},
		},
	},

	// 创建sql指标TLS资源
	// 获取或者创建TLS Project
	"FetchInnerProjectSqlTableStep": &FetchInnerProjectSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicSqlTableStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicSqlTableStep": &CreateInnerTopicSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoSqlTableStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoSqlTableStep": &SaveInnerTLSInfoSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareFullSqlTLSIndexSqlTableStep"]
			},
		},
	},
	// 创建索引
	"PrepareFullSqlTLSIndexSqlTableStep": &PrepareFullSqlTLSIndexSqlTableStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["FetchInnerProjectTopSqlStep"]
			},
		},
	},

	// 创建TopN tls的信息
	// 获取或者创建TLS Project
	"FetchInnerProjectTopSqlStep": &FetchInnerProjectTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["CreateInnerTopicTopSqlStep"]
			},
		},
	},

	// 创建TLS topic
	"CreateInnerTopicTopSqlStep": &CreateInnerTopicTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["SaveInnerTLSInfoTopSqlStep"]
			},
		},
	},

	// 保存TLS入库
	"SaveInnerTLSInfoTopSqlStep": &SaveInnerTLSInfoTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["PrepareFullSqlTLSIndexTopSqlStep"]
			},
		},
	},
	// 创建索引
	"PrepareFullSqlTLSIndexTopSqlStep": &PrepareFullSqlTLSIndexTopSqlStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["OpenFullSqlLogSwitchStep"]
			},
		},
	},

	// 开启内核-RdsAgent采集并获取RdsTLS资源
	"OpenFullSqlLogSwitchStep": &OpenFullSqlLogSwitchStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ConfigConsumerPipelineStep"]
			},
		},
	},
	// 配置消费链路信息（配置zookeeper）
	"ConfigConsumerPipelineStep": &ConfigConsumerPipelineStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["ConfigStatisticPipelineStep"]
			},
		},
	},
	// 配置统计TLS信息（配置zookeeper）
	"ConfigStatisticPipelineStep": &ConfigStatisticPipelineStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["UpdateInstanceStatusRunningStep"]
			},
		},
	},

	"UpdateInstanceStatusRunningStep": &UpdateInstanceStatusRunningStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return a.idempotentSteps["NotifyCreateOrderStep"]
			},
		},
	},
	// 通知订单完成
	"NotifyCreateOrderStep": &NotifyCreateOrderStep{
		BaseStep: BaseStep{
			NextStepFunc: func(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
				return nil
			},
		},
	},
}

// NewFullSqlCreateLifecycleActor MySQL 创建
func NewFullSqlCreateLifecycleActor(in NewAuditCreateLifecycleActorIn) types.VirtualPersistenceProducer {
	// 组装垃圾回收子流
	GlobalStepLock.Lock()
	for s, step := range CreateFullSqlGCSteps {
		createFullSqlSteps[s] = step
	}
	GlobalStepLock.Unlock()

	return types.VirtualPersistenceProducer{
		Kind: consts.MysqlFullSqlCreateActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &AuditLifecycleActor{
				state:           newTaskState(state),
				idempotentSteps: createFullSqlSteps,

				source:               in.DataSource,
				crossAuthSvc:         in.CrossAuthSvc,
				conf:                 in.Conf,
				c3Conf:               in.C3Conf,
				loc:                  in.Loc,
				tagSvc:               in.TagSvc,
				projectSvc:           in.ProjectSvc,
				tlsDAL:               in.TlsDAL,
				lcSvc:                in.LCSvc,
				billSvc:              in.BillSvc,
				publishEventSvc:      in.PublishEventSvc,
				auditService:         in.AuditService,
				genTlsSvc:            in.GenTlsSvc,
				fullSqlInstDAL:       in.FullSqlInstDAL,
				fullSqlSvc:           in.FullSqlSvc,
				fullSqlConfigService: in.FullSqlConfigService,
				instanceExtraTlsDAL:  in.InstanceExtraTlsDAL,
			}
			return actor
		}),
	}
}
