package scale

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"github.com/bytedance/mockey"
	"github.com/volcengine/volc-sdk-golang/base"
	"net/http"
	"testing"
)

func TestGetSpecFromInstanceInfo(t *testing.T) {
	GetSpecFromInstanceInfo(1, 1)
}

func TestGenerateQueries(t *testing.T) {
	GenerateQueries("x", "x", "x")
}

func TestBuildRequest(t *testing.T) {
	mock1 := mockey.Mock((*mocks.MockCredentials).GetAK).Return("123").Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockCredentials).GetSK).Return("123").Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockCredentials).GetToken).Return("123").Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((base.Credentials).Sign).Return(nil).Build()
	defer mock4.UnPatch()
	BuildRequest(&http.Request{}, &mocks.MockCredentials{}, "xx")
}
