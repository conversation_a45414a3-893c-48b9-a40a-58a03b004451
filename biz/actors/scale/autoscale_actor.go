package scale

import (
	"bytes"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/autoscale"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	vdbModel "code.byted.org/infcs/dbw-mgr/gen/vedb-mgr/2022-01-01/kitex_gen/infcs/bytendb/model"
	vdbModelV2 "code.byted.org/infcs/dbw-mgr/gen/vedb-mgr/2024-01-01/kitex_gen/infcs/bytendb/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/dig"
	"net/http"
	"net/http/httputil"
	"runtime/debug"
	"time"
)

const MaxTry = 10

const LimitScaleInterval = 5 * 60 // 单位是秒
const VeDBSpecAPIVersion = "2024-01-01"

var MetricMap = map[string]map[string]string{
	"VeDBMySQL": {
		"Spec": "MaxCpuUtil",
	},
}

type ScaleTarget struct {
	NodeNumber int32  // 节点数量
	NodeSpec   string // 规格
	ZoneId     string // 可用区
	Vcpu       int32  // cpu
	Mem        int32  // 内存
	SpecFamily string
}

type AutoScaleActorIn struct {
	dig.In
	Conf           config.ConfigProvider
	Repo           repository.AutoScaleRepo
	IdgenSvc       idgen.Service
	ActorClient    cli.ActorClient
	C3ConfProvider c3.ConfigProvider
	Location       location.Location
	DS             datasource.DataSourceService
	CrossAuthSvc   crossauth.CrossServiceAuthorizationService
}

func NewAutoScaleActor(p AutoScaleActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.AutoScaleActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &AutoScaleActor{
				state:          recoverState(state),
				cnf:            p.Conf,
				repo:           p.Repo,
				idgenSvc:       p.IdgenSvc,
				actorClient:    p.ActorClient,
				c3ConfProvider: p.C3ConfProvider,
				loca:           p.Location,
				ds:             p.DS,
				crossAuthSvc:   p.CrossAuthSvc,
			}
		}),
	}
}

func recoverState(state []byte) *AutoScaleActorState {
	var actorState AutoScaleActorState
	if len(state) == 0 {
		return &actorState
	}
	err := utils.Unmarshal(state, &actorState)
	if err != nil {
		return &actorState
	}
	//if actorState.BandwidthInfo == nil {
	//	actorState.BandwidthInfo = &datasource.InstanceBandwidthInfo{}
	//}
	return &actorState
}

type AutoScaleActor struct {
	state          *AutoScaleActorState
	cnf            config.ConfigProvider
	repo           repository.AutoScaleRepo
	idgenSvc       idgen.Service
	actorClient    cli.ActorClient
	c3ConfProvider c3.ConfigProvider
	loca           location.Location
	ds             datasource.DataSourceService
	crossAuthSvc   crossauth.CrossServiceAuthorizationService
}

type Metric struct {
	MetricName string
}
type AutoScaleActorState struct {
	RuleId       int64
	EventId      int64
	InstanceType string
	MetricName   []*shared.AutoScaleMetric
	Before       string // 扩容前的值
	After        string // 扩容后的值
	RetryCount   int
	Memo         string
	EventState   shared.ScaleEventState
}

func (t *AutoScaleActor) GetState() []byte {
	state, _ := json.Marshal(t.state)
	return state
}

func (t *AutoScaleActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call AutoScaleActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (t *AutoScaleActor) Process(ctx types.Context) {
	if t.state == nil {
		t.state = new(AutoScaleActorState)
	}
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		// 启动时候做的事情
		t.protectUserCall(ctx, func() {
			t.OnStart(ctx)
		})
	case *shared.CreateAutoScaleEvent:
		t.protectUserCall(ctx, func() {
			t.StartScale(ctx, msg)
		})
	case *actor.ReceiveTimeout:
		t.protectUserCall(ctx, func() {
			t.CheckScaleEvent(ctx)
		})
	case *shared.WatchResult:
		t.protectUserCall(ctx, func() {
			t.WatchScaleResult(ctx)
		})
	}
}

func (t *AutoScaleActor) OnStart(ctx types.Context) {
	log.Info(ctx, "AutoScaleActor %s start", ctx.GetName())
	switch t.state.EventState {
	case shared.EventUndo:
		t.undoStart(ctx)
	case shared.EventScaling:
		t.scalingStart(ctx)
	case shared.EventFinished:
		t.finishEvent(ctx, shared.EventFinished)
	case shared.EventError:
		t.finishEvent(ctx, shared.EventError)
	}
}

func (t *AutoScaleActor) undoStart(ctx types.Context) {
	// actor start时state为undo有两种情况
	// 第一种是第一次启动，目前仅考虑这种情况
	if t.state.RuleId == 0 {
		// 第一次启动
		t.state.EventState = shared.EventUndo
		t.state.RetryCount = 0
		t.state.EventId = 0
	}

	//第二种是没有开始进入扩容就挂了，我们通过记录在state的ruleId来区分
	//NOTE 第二种情况下,有可能发起一个不再需要扩容的任务,所以这里给他去掉
	//} else {
	//	log.Info(ctx, "ruleId:%d recover scale", t.state.RuleId)
	//	// actor重启恢复
	//	// scale在进入scaling之前做了幂等的容错，直接重新发起即可
	//	t.retryScale(ctx)
	//}
}

func (t *AutoScaleActor) scalingStart(ctx types.Context) {
	log.Info(ctx, "ruleId %v eventId %v is scaling when actor restart", t.state.RuleId, t.state.EventId)
	// 启动时，actor在scaling状态，说明正在进行扩缩容，我们只需要看它有没有扩缩容完就行了
	t.WatchScaleResult(ctx)
}

func (t *AutoScaleActor) clearState() {
	t.state.EventId = 0
	t.state.RetryCount = 0
	t.state.Memo = ""
	t.state.EventState = shared.EventUndo
}

func (t *AutoScaleActor) StartScale(ctx types.Context, msg *shared.CreateAutoScaleEvent) {
	log.Info(ctx, "rule:%d start scale", msg.RuleId)

	// 1、检查actor状态
	// 如果actor在扩容中，则不进行扩容
	if t.state.EventState == shared.EventScaling {
		log.Info(ctx, "actor:%v is scaling, refuse rule:%v scale req", t.state.RuleId, msg.RuleId)
		return
	}
	// 如果上次扩容和这次扩容时间在5分钟之内,则禁止此次扩容
	if t.isScaleLimited(ctx, msg.RuleId) {
		log.Warn(ctx, "ruleId: %d is scaled in 5 minutes, refuse scale req", msg.RuleId)
		return
	}
	// 从规则表里拿出这个规则
	rule, err := t.getRuleByRuleId(ctx, msg.RuleId)
	if err != nil {
		log.Warn(ctx, "get rule from db error %v", err)
		return
	}
	log.Info(ctx, "before auto scale check success,rule is %v", utils.Show(rule))

	// 2、写记录表
	if err = t.createScaleEvent(ctx, rule, msg); err != nil {
		log.Warn(ctx, "ruleId: %d create scale event error:%s", msg.RuleId, err.Error())
		return
	}
	// 3、获取当前实例的规格
	instanceInfo, err := t.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		InstanceId: rule.InstanceId,
		Type:       shared.DataSourceType(shared.DataSourceType_value[rule.InstanceType]),
	})
	if err != nil {
		errMsg := fmt.Sprintf("ruleId %v get instance %v detail from rds error:%v", msg.RuleId, rule.InstanceId, err)
		log.Warn(ctx, errMsg)
		t.repo.UpdateAutoScaleErrorEvent(ctx, errMsg, t.state.EventId)
		return
	}
	log.Info(ctx, "get instance info is %v", utils.Show(instanceInfo))

	// 3、扩容前检查一下实例是否在运行中
	// 这里取任何一个数据源的Running状态都可以
	if instanceInfo.InstanceStatus != vdbModel.InspectStatus_Running.String() {
		errMsg := fmt.Sprintf("ruleId:%d, instance:%s is not running", rule.RuleId, rule.InstanceId)
		log.Warn(ctx, errMsg)
		t.repo.UpdateAutoScaleErrorEvent(ctx, errMsg, t.state.EventId)
		return
	}
	// 一般来说，actor的Name就是ruleId，我们在这里更新ruleId，这样就可以在恢复的时候知道这个actor之前有没有执行过scale
	t.state.RuleId = msg.RuleId
	t.state.InstanceType = rule.InstanceType

	// 4、计算扩缩容后的指标值
	var scaleTarget = &ScaleTarget{}
	switch rule.ScaleTarget {
	case shared.Spec.String():
		log.Info(ctx, "this is a spec scale")
		var metrics []*datasource.AutoScaleMetricName
		for _, val := range msg.Metrics {
			metrics = append(metrics, &datasource.AutoScaleMetricName{
				MetricName:  val.MetricName,
				MetricValue: val.MetricValue,
			})
		}
		req := &datasource.CalculateSpecAfterScaleReq{
			Type:         shared.DataSourceType(shared.DataSourceType_value[rule.InstanceType]), // 实例类型
			InstanceId:   instanceInfo.InstanceId,
			ScalingLimit: rule.ScalingLimit, // 扩容规格上限
			Metrics:      metrics,           // 扩容时候的指标值
			CurrentCpu:   instanceInfo.VCPU,
			CurrentMem:   instanceInfo.Memory,
			SpecFamily:   instanceInfo.SpecFamily,
			ScalingType:  model.AutoScaleAction(rule.ScalingType),
		}
		nextSpec, err := t.ds.CalculateSpecAfterScale(ctx, req)
		if err != nil {
			return
		}
		log.Info(ctx, "nextSpec is %v", utils.Show(nextSpec))
		t.state.Before = fmt.Sprintf("%vC%vG", instanceInfo.VCPU, instanceInfo.Memory)
		t.state.After = fmt.Sprintf("%vC%vG", nextSpec.NewCpu, nextSpec.NewMem)
		t.repo.UpdateAutoScaleMetricValueEvent(ctx, int(shared.EventScaling), t.state.Before, t.state.After, "", t.state.EventId)

		if instanceInfo.VCPU == nextSpec.NewCpu && instanceInfo.Memory == nextSpec.NewMem {
			errMsg := fmt.Sprintf("ruleId: %d, currentSpec:%v, currentBandwidth is same as nextSpec %v, need not scale", msg.RuleId, utils.Show(req), utils.Show(nextSpec))
			log.Warn(ctx, errMsg)
			Msg := fmt.Sprintf("ruleId: %d, currentSpec:%v, it is same as nextSpec %v,",
				msg.RuleId, fmt.Sprintf("%vC%vG", req.CurrentCpu, req.CurrentMem), fmt.Sprintf("%vC%vG", nextSpec.NewCpu, nextSpec.NewMem))
			t.state.EventState = shared.EventFinished
			t.repo.UpdateAutoScaleMetricValueEvent(ctx, int(shared.EventError), t.state.Before, t.state.After, Msg, t.state.EventId)
			t.clearState()
			return
		}

		scaleTarget.NodeSpec = nextSpec.NewSpec
		scaleTarget.Mem = nextSpec.NewMem
		scaleTarget.Vcpu = nextSpec.NewCpu
		scaleTarget.SpecFamily = nextSpec.NewSpecFamily
		scaleTarget.NodeNumber = instanceInfo.NodeNumber
		scaleTarget.ZoneId = instanceInfo.ZoneId
	}
	// 执行扩缩容
	log.Info(ctx, "scale target is %v", utils.Show(scaleTarget))
	t.DoScale(ctx, rule, scaleTarget)
}

func (t *AutoScaleActor) getRuleByRuleId(ctx types.Context, ruleId int64) (*entity.AutoScaleRule, error) {
	rule, err := t.repo.GetScaleRuleById(ctx, ruleId)
	if err != nil {
		log.Warn(ctx, "ruleId: %d get rule info error:%s", ruleId, err)
		return rule, err
	}
	if rule.RuleId != ruleId {
		log.Warn(ctx, "input ruleId:%d is not same as db,db:%d", ruleId, rule.RuleId)
		return rule, err
	}
	return rule, nil
}

// isScaleLimited 如果上次扩容和这次扩容时间在5分钟之内,则禁止此次扩容
func (t *AutoScaleActor) isScaleLimited(ctx types.Context, ruleId int64) bool {
	// 拿最后一次完成扩容的信息
	event, err := t.repo.GetLatestFinishedEventByRuleId(ctx, ruleId)
	if err != nil || event == nil {
		log.Warn(ctx, "get latest finished auto scale event err:%v", err)
		return false
	}
	return time.Now().Unix()-event.EndTime < LimitScaleInterval
}

func (t *AutoScaleActor) DoScale(ctx types.Context, rule *entity.AutoScaleRule, scaleTarget *ScaleTarget) {
	switch rule.ScaleTarget {
	case shared.Spec.String():
		// veDB这个地方,需要assumeRole扮演用户来调用接口
		err := t.ModifyInstanceSpec(ctx, rule, scaleTarget)
		if err != nil {
			log.Warn(ctx, "ModifyDBInstanceSpec error is %v", err)
			t.repo.UpdateAutoScaleErrorEvent(ctx, err.Error(), t.state.EventId)
			return
		}
		// 这里是直接调用接口的代码，暂时不删
		//res, err := t.ds.ModifyDBInstanceSpec(ctx, &datasource.ModifyDBInstanceSpecReq{
		//	Type:       shared.DataSourceType(shared.DataSourceType_value[rule.InstanceType]),
		//	InstanceId: rule.InstanceId,
		//	NodeNumber: scaleTarget.NodeNumber,
		//	NodeSpec:   scaleTarget.NodeSpec,
		//	//https://www.volcengine.com/docs/6357/96182#nodeinfoobject
		//	// DBEngine：主节点/只读节点 APWorkNode：分析节点。本期规格扩容只考虑主节点和只读节点
		//	NodeType: "DBEngine",
		//	ZoneId:   scaleTarget.ZoneId,
		//})
		//if err != nil {
		//	log.Warn(ctx, "ModifyDBInstanceSpec res is %v", utils.Show(res))
		//	ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
		//	return
		//}
		log.Info(ctx, "call modify instance spec success")
		t.state.EventState = shared.EventScaling
		if err = t.repo.UpdateScalingEvent(ctx, t.state.EventId); err != nil {
			// 打印日志，因为上面有create，这里出错概率比较低，即使真的极小概率卡在这里了，那么我们也继续往后走即可，只影响startTime字段
			log.Warn(ctx, "scale UpdateScalingEvent error: %s", utils.Show(err))
		}
		// 这里开始进入20min的等待,不接收其他消息
		t.WatchScaleResult(ctx)
	}
}

func (t *AutoScaleActor) finishScale(ctx types.Context) {
	// 最后一步失败的情况，转送到timeout进行更新
	err := t.repo.UpdateAutoScaleEndEvent(ctx, t.state.After, t.state.EventId)
	if err != nil {
		log.Warn(ctx, "ruleId: %d UpdateScaleEndEvent error :%s", t.state.RuleId, utils.Show(err))
	}
	t.clearState()
}

func (t *AutoScaleActor) WatchScaleResult(ctx types.Context) {
	if t.state.EventState != shared.EventScaling || t.state.RuleId == 0 {
		return
	}
	rule, err := t.getRuleByRuleId(ctx, t.state.RuleId)
	if err != nil {
		log.Warn(ctx, "get rule by ruleId %v from db error %v", t.state.RuleId, err)
		return
	}
	t.watchResult(ctx, rule, t.state.After)
	t.finishScale(ctx)
}

func (t *AutoScaleActor) watchResult(ctx types.Context, rule *entity.AutoScaleRule, calculateMetric string) {
	// 这里我们轮询3600s，看实例的规格是否已经变成了我们计算出来的规格
	for i := 0; i <= 180; i++ {
		time.Sleep(20 * time.Second)
		// 不关心这个接口是不是报错了，如果报错了，返回0，与next不符，待会重试就行了
		detail, _ := t.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
			InstanceId: rule.InstanceId,
			Type:       shared.DataSourceType(shared.DataSourceType_value[rule.InstanceType]),
		})
		var newSpec string
		if detail != nil {
			newSpec = GetSpecFromInstanceInfo(detail.VCPU, detail.Memory)
		}
		if newSpec == calculateMetric {
			log.Info(ctx, "ruleId %v,eventId %v,auto scale success，%v", t.state.RuleId, t.state.EventId, utils.Show(rule))
			t.state.EventState = shared.EventFinished
			return
		}
	}
	// 一般不会走到这里,到这里，说明调整规格还没有结束，打印个error日志告警
	msg := fmt.Sprintf("ruleId:%d eventId:%d redis-scale is not finished in 3600s", t.state.RuleId, t.state.EventId)
	log.Error(ctx, msg)
	t.repo.UpdateScaleErrorEvent(ctx, msg, t.state.EventId)
	t.state.EventState = shared.EventError
}

func (t *AutoScaleActor) CheckScaleEvent(ctx types.Context) {
	// NOTE 这里什么多不做
}

func (t *AutoScaleActor) finishEvent(ctx types.Context, state shared.ScaleEventState) {
	// 不论是finished或者error，走到这里已经说明实际上'扩缩容'已经'完成'了，我们这里直接更新数据库就行了
	var err error
	switch state {
	case shared.EventFinished:
		err = t.repo.UpdateAutoScaleEndEvent(ctx, t.state.After, t.state.EventId)
	case shared.EventError:
		err = t.repo.UpdateScaleErrorEvent(ctx, t.state.Memo, t.state.EventId)
	}
	if err != nil {
		return
	}
	t.clearState()
}

//func (t *AutoScaleActor) retryScale(ctx types.Context) {
//	// 如果超过限制的10次,直接更新任务失败
//	if t.isLimited() {
//		log.Warn(ctx, "AutoScaleActor retry: %d > 10, stop actor", t.state.RetryCount)
//		t.state.Memo = "event retry > 10"
//		t.state.EventState = shared.EventError
//		t.finishEvent(ctx, shared.EventError)
//		return
//	}
//	// 如果没有超过上限,发送一个扩缩容消息，继续执行
//	log.Info(ctx, "AutoScaleActor retry: %d", t.state.RetryCount)
//	ctx.Send(ctx.Self(), &shared.CreateAutoScaleEvent{
//		RuleId:  t.state.RuleId,
//		Metrics: t.state.MetricName,
//	})
//}

//func (t *AutoScaleActor) isLimited() bool {
//	t.state.RetryCount++
//	return t.state.RetryCount > MaxTry
//}

func (t *AutoScaleActor) getEventId(ctx types.Context) (int64, error) {
	// 如果eventId已经生成出来了，那么就用已经生成出来的Id,正常来讲,这里都是没有ID的
	if t.state.EventId != 0 {
		return t.state.EventId, nil
	}
	eventId, err := t.idgenSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "rule:%d, get next Id error", t.state.RuleId)
		return 0, err
	}
	return eventId, nil
}

func (t *AutoScaleActor) createScaleEvent(ctx types.Context, rule *entity.AutoScaleRule, msg *shared.CreateAutoScaleEvent) error {
	eventId, err := t.getEventId(ctx)
	if err != nil {
		return err
	}
	targetUtil := t.GetTargetUtilizationFromRuleAndMSG(rule, msg)
	log.Info(ctx, "ruleId %v,eventId %v,target util is %v", t.state.RuleId, eventId, targetUtil)
	event := &entity.AutoScaleEvent{
		RuleId:            rule.RuleId,
		EventId:           eventId,
		CreateTime:        time.Now().Unix(),
		TargetUtilization: targetUtil,
		Status:            int8(shared.EventUndo),
		Deleted:           int8(shared.EventUnDeleted),
		//BeforeMetric:      t.state.Before,
		//AfterMetric:       t.state.After, // 这个计算后再写
		TenantId:    rule.TenantId,
		InstanceId:  rule.InstanceId,
		ScalingType: rule.ScalingType,
		StartTime:   time.Now().Unix(),
	}
	if err = t.repo.CreateScaleEvent(ctx, event); err != nil {
		log.Warn(ctx, "ruleId: %d insert scale event to db error:%v", rule.RuleId, err)
		return err
	}
	// 存在actor的state中
	t.state.EventId = eventId
	return nil
}

// GetTargetUtilizationFromRuleAndMSG 通过规则和回调消息，获取触发扩容时候的阈值
func (t *AutoScaleActor) GetTargetUtilizationFromRuleAndMSG(rule *entity.AutoScaleRule, msg *shared.CreateAutoScaleEvent) string {
	switch rule.InstanceType {
	case shared.VeDBMySQL.String():
		if metric, ok := MetricMap[rule.InstanceType]; ok {
			for _, val := range msg.Metrics {
				if val.MetricName == metric[rule.ScaleTarget] {
					return fmt.Sprintf("%.2f", val.MetricValue)
				}
			}
		}
	}
	return ""
}

func (t *AutoScaleActor) ModifyInstanceSpec(ctx context.Context, rule *entity.AutoScaleRule, scaleTarget *ScaleTarget) error {
	switch rule.InstanceType {
	default:
		uri := GenerateQueries(vdbModelV2.Action_ModifyDBInstanceSpec.String(), VeDBSpecAPIVersion, t.cnf.Get(ctx).AutoScaleVeDBEndpoint)
		req := &vdbModelV2.ModifyDBInstanceSpecReq{
			InstanceId: utils.StringRef(rule.InstanceId),
			NodeInfo: []*vdbModelV2.NodeInfoObject{
				{
					NodeType:   utils.StringRef("DBEngine"),
					NodeSpec:   utils.StringRef(scaleTarget.NodeSpec),
					NodeNumber: utils.Int8Ref(int8(scaleTarget.NodeNumber)),
					ZoneId:     utils.StringRef(scaleTarget.ZoneId),
				},
			},
			ScheduleType: vdbModelV2.ScheduleType_Immediate, // 立刻执行
			OneStepOrder: utils.BoolRef(true),               // 直接执行,不跳转计费
		}
		resp := &vdbModelV2.ModifyDBInstanceSpecResp{}
		reqByte, err := json.Marshal(req)
		if err != nil {
			log.Warn(ctx, "AutoScale Marshal rule config error %v", err)
			return err
		}
		log.Info(ctx, "create rule config is %v", string(reqByte))
		// veDB 这个接口调用的时候,需要assumeRole
		request, err := http.NewRequest(http.MethodPost, uri, bytes.NewBuffer(reqByte))
		if err != nil {
			return fmt.Errorf("AutoScale: bad request: %w", err)
		}
		credential, err := t.crossAuthSvc.AssumeRole(ctx, rule.TenantId)
		if err != nil {
			log.Warn(ctx, "AutoScale: failed to get user ak/sk, err=%v", err)
			return consts.ErrorOf(model.ErrorCode_GetTempCredentialFailed)
		}
		log.Info(ctx, "AutoScale: ak is %s , sk is %s, tenant id is %v", credential.GetAK(), credential.GetSK(), rule.TenantId)
		request = BuildRequest(request, credential, "vedbm")

		// 2、打印请求
		requestRaw, err := httputil.DumpRequest(request, true)
		if err != nil {
			return fmt.Errorf("dump request err: %w", err)
		}
		log.Info(ctx, "AutoScale: request:\n%s\n", string(requestRaw))

		// 3、执行请求
		response, err := http.DefaultClient.Do(request)
		if err != nil {
			log.Warn(ctx, "AutoScale: do request error:%s", err.Error())
			return fmt.Errorf("do request err: %w", err)
		}

		// 4、 打印响应
		responseRaw, err := httputil.DumpResponse(response, true)
		if err != nil {
			log.Warn(ctx, "AutoScale: DumpResponse error:%s", err.Error())
			return fmt.Errorf("dump response err: %w", err)
		}
		log.Info(ctx, "AutoScale: response:\n%s\n", string(responseRaw))

		// 5、解析返回结构体
		respBody, err := autoscale.GetBody(response)
		if err != nil {
			return err
		}
		log.Info(ctx, "respBody is %v", respBody)
		if err = json.Unmarshal(respBody, resp); err != nil {
			log.Warn(ctx, "AutoScale: uri: %s unmarshal auto scale metric result fail %v, result: %s", uri, err, string(respBody))
			return err
		}
		// 6、判断请求状态
		if response.StatusCode == 200 {
			log.Info(ctx, "AutoScale: call vedb modify instance spec success")
		} else {
			log.Warn(ctx, "AutoScale: call vedb modify instance spec failed")
			return fmt.Errorf("call %s err", uri)
		}
		return nil
	}
}
