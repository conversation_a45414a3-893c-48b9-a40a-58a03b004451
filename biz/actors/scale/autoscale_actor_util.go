package scale

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"fmt"
	"github.com/volcengine/volc-sdk-golang/base"
	"net/http"
	"net/url"
	"os"
)

const Path = "/"

func GetSpecFromInstanceInfo(vCpu int32, mem int32) string {
	return fmt.Sprintf("%vC%vG", vCpu, mem)
}

func GenerateQueries(action string, version string, addr string) string {
	var queries = make(url.Values)
	queries.Set("Action", action)
	queries.Set("Version", version)
	requestAddr := fmt.Sprintf("https://%s%s?%s", addr, Path, queries.Encode())
	return requestAddr
}

func BuildRequest(request *http.Request, creds cross_service_authorization.Credentials, service string) *http.Request {
	credentials := base.Credentials{
		AccessKeyID:     creds.GetAK(),
		SecretAccessKey: creds.GetSK(),
		Service:         service,
		Region:          os.Getenv(`BDC_REGION_ID`),
		SessionToken:    creds.GetToken(),
	}
	return credentials.Sign(request)
}
