package bandwidth

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	redisModel "code.byted.org/infcs/dbw-mgr/gen/redis-mgr/kitex_gen/base"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"go.uber.org/dig"
	"math"
	"runtime/debug"
	"time"
)

const MaxTry = 10

const LimitScaleInterval = 5 * 60

type AutoBandwidthScaleActorIn struct {
	dig.In
	Conf           config.ConfigProvider
	Repo           repository.AutoScaleRepo
	IdgenSvc       idgen.Service
	ActorClient    cli.ActorClient
	C3ConfProvider c3.ConfigProvider
	Loca           location.Location
	RedisMgr       mgr.Provider `name:"redis"`
	DS             datasource.DataSourceService
}

func NewBandWidthScaleActor(p AutoBandwidthScaleActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.BandwidthScaleActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &AutoBandwidthScaleActor{
				state:          recoverState(state),
				cnf:            p.Conf,
				repo:           p.Repo,
				idgenSvc:       p.IdgenSvc,
				actorClient:    p.ActorClient,
				c3ConfProvider: p.C3ConfProvider,
				loca:           p.Loca,
				nosql:          p.RedisMgr,
				ds:             p.DS,
			}
		}),
	}
}

func recoverState(state []byte) *AutoBandwidthScaleActorState {
	var actorState AutoBandwidthScaleActorState
	if state == nil || len(state) == 0 {
		return &actorState
	}
	err := utils.Unmarshal(state, &actorState)
	if err != nil {
		return &actorState
	}
	if actorState.BandwidthInfo == nil {
		actorState.BandwidthInfo = &datasource.InstanceBandwidthInfo{}
	}
	return &actorState
}

type AutoBandwidthScaleActor struct {
	state          *AutoBandwidthScaleActorState
	cnf            config.ConfigProvider
	repo           repository.AutoScaleRepo
	idgenSvc       idgen.Service
	actorClient    cli.ActorClient
	c3ConfProvider c3.ConfigProvider
	loca           location.Location
	nosql          mgr.Provider
	ds             datasource.DataSourceService
}

type AutoBandwidthScaleActorState struct {
	RuleId                        int64
	EventId                       int64
	AfterBandwidth                int
	RetryCount                    int
	NetworkReceiveThroughputUtil  float32
	NetworkTransmitThroughputUtil float32
	Memo                          string
	EventState                    shared.ScaleEventState
	BandwidthInfo                 *datasource.InstanceBandwidthInfo
	InstanceType                  shared.DataSourceType
}

func (t *AutoBandwidthScaleActor) GetState() []byte {
	state, _ := json.Marshal(t.state)
	return state
}

func (t *AutoBandwidthScaleActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call AutoBandwidthScaleActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (t *AutoBandwidthScaleActor) Process(ctx types.Context) {
	if t.state == nil {
		t.state = new(AutoBandwidthScaleActorState)
	}
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		// 启动时候做的事情
		t.protectUserCall(ctx, func() {
			t.OnStart(ctx)
		})
	case *shared.CreateScaleEvent:
		t.protectUserCall(ctx, func() {
			t.StartScale(ctx, msg)
		})
	case *actor.ReceiveTimeout:
		t.protectUserCall(ctx, func() {
			t.CheckScaleEvent(ctx)
		})
	case *shared.WatchResult:
		t.protectUserCall(ctx, func() {
			t.WatchScaleResult(ctx)
		})
	}
}

func (t *AutoBandwidthScaleActor) OnStart(ctx types.Context) {
	log.Info(ctx, "AutoBandwidthScaleActor %s start", ctx.GetName())
	switch t.state.EventState {
	case shared.EventUndo:
		t.undoStart(ctx)
	case shared.EventScaling:
		t.scalingStart(ctx)
	case shared.EventFinished:
		t.finishEvent(ctx, shared.EventFinished)
	case shared.EventError:
		t.finishEvent(ctx, shared.EventError)
	}
}

func (t *AutoBandwidthScaleActor) undoStart(ctx types.Context) {
	// actor start时为state为undo有两种情况
	// 第一种是第一次启动，第二种是没有开始进入扩容就挂了，我们通过记录在state的ruleId来区分
	if t.state.RuleId == 0 {
		// 第一次启动
		t.state.EventState = shared.EventUndo
		t.state.RetryCount = 0
		t.state.BandwidthInfo = &datasource.InstanceBandwidthInfo{}
		t.state.EventId = 0
	} else if t.state.NetworkTransmitThroughputUtil == 0 && t.state.NetworkReceiveThroughputUtil == 0 {
		// 如果拉起来这两个值同时为0了，说明当前是空转状态，没有任务，不需要管它
		// 假设极小概率就是出现了两个都为0的缩容，那也不是着急事，等下次监控触发缩容就可以了
		return
	} else {
		log.Info(ctx, "ruleId:%d recover scale", t.state.RuleId)
		// actor重启恢复
		// scale在进入scaling之前做了幂等的容错，直接重新发起即可
		t.retryScale(ctx)
	}
}

func (t *AutoBandwidthScaleActor) scalingStart(ctx types.Context) {
	// 启动时，actor在scaling状态，说明正在进行扩缩容，我们只需要看它有没有扩缩容完就行了
	ctx.Send(ctx.Self(), &shared.WatchResult{})
}

func (t *AutoBandwidthScaleActor) clearState() {
	t.state.EventId = 0
	t.state.RetryCount = 0
	t.state.AfterBandwidth = 0
	t.state.NetworkTransmitThroughputUtil = 0
	t.state.NetworkReceiveThroughputUtil = 0
	t.state.Memo = ""
	t.state.BandwidthInfo = &datasource.InstanceBandwidthInfo{}
	t.state.EventState = shared.EventUndo
}

func (t *AutoBandwidthScaleActor) StartScale(ctx types.Context, msg *shared.CreateScaleEvent) {
	log.Info(ctx, "rule:%d start scale", msg.RuleId)
	if t.state.EventState == shared.EventScaling {
		log.Info(ctx, "actor:% is scaling, refuse rule:%d scale req", t.state.RuleId, msg.RuleId)
		return
	}
	t.state.NetworkTransmitThroughputUtil = msg.NetworkTransmitThroughputUtil
	t.state.NetworkReceiveThroughputUtil = msg.NetworkReceiveThroughputUtil
	// 一般来说，actor的Name就是ruleId，我们在这里更新ruleId，这样就可以在恢复的时候知道这个actor之前有没有执行过scale
	t.state.RuleId = msg.RuleId
	if t.isScaleLimited(ctx, msg.RuleId) {
		log.Warn(ctx, "ruleId: %d is scaled in 5 minutes, refuse scale req", msg.RuleId)
		return
	}
	// 从规则表里拿出这个规则
	rule, err := t.getRuleByRuleId(ctx, msg.RuleId)
	if err != nil {
		return
	}
	t.state.InstanceType = shared.DataSourceType(shared.DataSourceType_value[rule.InstanceType])
	// 拿当前实例规格和带宽
	instanceBandwidthInfo, err := t.ds.GetCurrentBandwidth(ctx, &datasource.GetCurrentBandwidthReq{DSType: t.state.InstanceType, Rule: rule})
	if err != nil {
		log.Warn(ctx, "ruleId: %d GetCurrentBandwidth error:%s", msg.RuleId, utils.Show(err))
		ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
		return
	}
	if instanceBandwidthInfo.Status != redisModel.InstanceStatus_Running.String() {
		log.Warn(ctx, "ruleId:%d, redis instance:%s is not running", rule.RuleId, rule.InstanceId)
		ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
		return
	}
	log.Info(ctx, "instanceId:%s, instanceBandwidthInfo:%s", rule.InstanceId, utils.Show(instanceBandwidthInfo))
	t.state.BandwidthInfo = instanceBandwidthInfo
	// 计算扩缩容后的带宽
	nextBandwidth, err := t.CalculateAfterScaleBandwidth(ctx, rule, msg, instanceBandwidthInfo.CurrentBandwidth)
	if err != nil {
		ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
		return
	}
	if instanceBandwidthInfo.CurrentBandwidth == nextBandwidth {
		log.Warn(ctx, "ruleId: %d, currentBandwidth:%d, currentBandwidth is same as nextBandwidth, need not scale", msg.RuleId, instanceBandwidthInfo.CurrentBandwidth)
		t.state.EventState = shared.EventFinished
		t.clearState()
		return
	}
	// 写记录表
	if err := t.createScaleEvent(ctx, rule, msg, instanceBandwidthInfo.CurrentBandwidth); err != nil {
		log.Warn(ctx, "ruleId: %d create scale event error:%s", msg.RuleId, err.Error())
		ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
		return
	}
	// 执行扩缩容
	t.DoScale(ctx, rule, instanceBandwidthInfo, nextBandwidth)
}

func (t *AutoBandwidthScaleActor) getRuleByRuleId(ctx types.Context, ruleId int64) (*entity.AutoScaleRule, error) {
	rule, err := t.repo.GetScaleRuleById(ctx, ruleId)
	if err != nil {
		log.Warn(ctx, "ruleId: %d get rule info error:%s", ruleId, err)
		return rule, err
	}
	if rule.RuleId != ruleId {
		log.Warn(ctx, "input ruleId:%d is not same as db,db:%d", ruleId, rule.RuleId)
		return rule, err
	}
	return rule, nil
}

func (t *AutoBandwidthScaleActor) isScaleLimited(ctx types.Context, ruleId int64) bool {
	// 拿最后一次完成扩容的信息
	event, err := t.repo.GetLatestFinishedEventByRuleId(ctx, ruleId)
	if err != nil || event == nil {
		log.Warn(ctx, "get latest finished event err:%s", err.Error())
		return false
	}
	return time.Now().Unix()-event.EndTime < LimitScaleInterval
}

func (t *AutoBandwidthScaleActor) getBandwidth(rule *entity.AutoScaleRule, msg *shared.CreateScaleEvent) float32 {
	// 扩容的时候，取带宽输入和输出中最大的
	// 缩容的时候，取最大的
	if rule.ScalingType == int8(shared.Expansion) {
		return float32(math.Max(float64(msg.NetworkReceiveThroughputUtil), float64(msg.NetworkTransmitThroughputUtil)))
	} else {
		return float32(math.Max(float64(msg.NetworkReceiveThroughputUtil), float64(msg.NetworkTransmitThroughputUtil)))
	}
}

func (t *AutoBandwidthScaleActor) DoScale(ctx types.Context, rule *entity.AutoScaleRule, instanceBandwidthInfo *datasource.InstanceBandwidthInfo, nextBandwidth int) {
	if nextBandwidth != 0 {
		t.state.AfterBandwidth = nextBandwidth
	} else {
		t.state.AfterBandwidth = instanceBandwidthInfo.CurrentBandwidth
	}
	err := t.ds.BandwidthScale(ctx, &datasource.BandwidthScaleReq{
		DSType:        t.state.InstanceType,
		Rule:          rule,
		BandwidthInfo: instanceBandwidthInfo,
		NextBandwidth: t.state.AfterBandwidth,
	})
	if err != nil {
		ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
		return
	}
	t.state.EventState = shared.EventScaling
	err = t.repo.UpdateScalingEvent(ctx, t.state.EventId)
	if err != nil {
		// 打印日志，因为上面有create，这里出错概率比较低，即使真的极小概率卡在这里了，那么我们也继续往后走即可，只影响startTime字段
		log.Warn(ctx, "scale UpdateScalingEvent error: %s", utils.Show(err))
	}
	ctx.Send(ctx.Self(), &shared.WatchResult{})
}

func (t *AutoBandwidthScaleActor) finishScale(ctx types.Context) {
	t.state.EventState = shared.EventFinished
	// 最后一步失败的情况，转送到timeout进行更新
	err := t.repo.UpdateScaleEndEvent(ctx, t.state.AfterBandwidth, t.state.EventId)
	if err != nil {
		log.Warn(ctx, "ruleId: %d UpdateScaleEndEvent error :%s", t.state.RuleId, utils.Show(err))
		ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
	}
	t.clearState()
}

func (t *AutoBandwidthScaleActor) WatchScaleResult(ctx types.Context) {
	if t.state.EventState != shared.EventScaling || t.state.RuleId == 0 {
		return
	}
	rule, err := t.getRuleByRuleId(ctx, t.state.RuleId)
	if err != nil {
		return
	}
	t.watchResult(ctx, rule, t.state.AfterBandwidth)
	t.finishScale(ctx)
}

func (t *AutoBandwidthScaleActor) watchResult(ctx types.Context, rule *entity.AutoScaleRule, nextBandwidth int) {
	for i := 0; i <= 60; i++ {
		time.Sleep(20 * time.Second)
		// 不关心这个接口是不是报错了，如果报错了，返回0，与next不符，待会重试就行了
		bandwidthInfo, _ := t.ds.GetCurrentBandwidth(ctx, &datasource.GetCurrentBandwidthReq{DSType: t.state.InstanceType, Rule: rule})
		if bandwidthInfo.CurrentBandwidth == nextBandwidth {
			return
		}
	}
	// 到这里，说明调整带宽还没有结束，打印个error日志告警
	log.Error(ctx, "ruleId:%d eventId:%d redis-scale is not finished in 1200s", t.state.RuleId, t.state.EventId)
}

func (t *AutoBandwidthScaleActor) CalculateAfterScaleBandwidth(ctx types.Context, rule *entity.AutoScaleRule, msg *shared.CreateScaleEvent, currentBandwidth int) (int, error) {
	// 粗略估计到调整后的带宽利用率为50即可，不用关心它是缩容还是扩容，只要保障它在范围内就行了
	calculateValue := int(float32(currentBandwidth) * t.getBandwidth(rule, msg) * 2)
	minBandwidth, err := t.ds.GetMinBandwidth(ctx, &datasource.GetMinMaxBandwidthReq{TenantId: rule.TenantId, DSType: t.state.InstanceType, BandwidthInfo: t.state.BandwidthInfo})
	maxBandwidth, err := t.ds.GetMaxBandwidth(ctx, &datasource.GetMinMaxBandwidthReq{TenantId: rule.TenantId, DSType: t.state.InstanceType, BandwidthInfo: t.state.BandwidthInfo})
	log.Info(ctx, "calculateValue is %d,minBandwidth is %d,maxBandwidth is %d,rule scaling_type is %d", calculateValue, minBandwidth, maxBandwidth, rule.ScalingType)
	if err != nil {
		log.Warn(ctx, "获取实例最大最小带宽失败: %s", err.Error())
		return 0, err
	}
	if calculateValue < minBandwidth {
		log.Warn(ctx, "rule:%d 缩容已经触发最小值，将缩为：%d", rule.RuleId, minBandwidth)
		return minBandwidth, nil
	}
	if calculateValue > maxBandwidth {
		log.Warn(ctx, "rule:%d 扩容已经触发最大值，将扩为：%d", rule.RuleId, maxBandwidth)
		return maxBandwidth, nil
	}
	return calculateValue, nil
}

func (t *AutoBandwidthScaleActor) CheckScaleEvent(ctx types.Context) {
	// 到这里，说明中间出问题了，避免短时间内重复调用失败，我们sleep一会重试
	time.Sleep(10 * time.Second)
	switch t.state.EventState {
	case shared.EventScaling:
		if t.isLimited() {
			log.Warn(ctx, "AutoBandwidthScaleActor retry: %d > 10, end event", t.state.RetryCount)
			t.state.Memo = "event retry > 10"
			t.state.EventState = shared.EventError
			t.finishEvent(ctx, shared.EventError)
			return
		}
	case shared.EventFinished:
		t.finishEvent(ctx, shared.EventFinished)
	case shared.EventError:
		t.finishEvent(ctx, shared.EventError)
	case shared.EventUndo:
		t.retryScale(ctx)
	}
}

func (t *AutoBandwidthScaleActor) finishEvent(ctx types.Context, state shared.ScaleEventState) {
	// 不论是finished或者error，走到这里已经说明实际上'扩缩容'已经'完成'了，我们这里直接更新数据库就行了
	var err error
	switch state {
	case shared.EventFinished:
		err = t.repo.UpdateScaleEndEvent(ctx, t.state.AfterBandwidth, t.state.EventId)
	case shared.EventError:
		err = t.repo.UpdateScaleErrorEvent(ctx, t.state.Memo, t.state.EventId)
	}
	if err != nil {
		ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
		return
	}
	t.clearState()
}

func (t *AutoBandwidthScaleActor) retryScale(ctx types.Context) {
	if t.isLimited() {
		log.Warn(ctx, "AutoBandwidthScaleActor retry: %d > 10, stop actor", t.state.RetryCount)
		t.state.Memo = "event retry > 10"
		t.state.EventState = shared.EventError
		t.finishEvent(ctx, shared.EventError)
		return
	}
	ruleId := t.state.RuleId
	ctx.Send(ctx.Self(), &shared.CreateScaleEvent{
		RuleId:                        ruleId,
		NetworkReceiveThroughputUtil:  t.state.NetworkReceiveThroughputUtil,
		NetworkTransmitThroughputUtil: t.state.NetworkTransmitThroughputUtil,
	})
}

func (t *AutoBandwidthScaleActor) isLimited() bool {
	t.state.RetryCount++
	return t.state.RetryCount > MaxTry
}

func (t *AutoBandwidthScaleActor) getEventId(ctx types.Context) (int64, error) {
	// 如果eventId已经生成出来了，那么就用已经生成出来的Id
	if t.state.EventId != 0 {
		return t.state.EventId, nil
	}
	eventId, err := t.idgenSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "rule:%d, get next Id error", t.state.RuleId)
		return 0, err
	}
	return eventId, nil
}

func (t *AutoBandwidthScaleActor) createScaleEvent(ctx types.Context, rule *entity.AutoScaleRule, msg *shared.CreateScaleEvent, currentBandwidth int) error {
	eventId, err := t.getEventId(ctx)
	if err != nil {
		return err
	}
	event := &entity.AutoScaleEvent{
		RuleId:            rule.RuleId,
		EventId:           eventId,
		CreateTime:        time.Now().Unix(),
		TargetUtilization: floatToString(t.getBandwidth(rule, msg)),
		Status:            int8(shared.EventUndo),
		Deleted:           int8(shared.EventUnDeleted),
		BeforeValue:       int32(currentBandwidth),
		TenantId:          rule.TenantId,
		InstanceId:        rule.InstanceId,
		ScalingType:       rule.ScalingType,
	}
	if err := t.repo.CreateScaleEvent(ctx, event); err != nil {
		log.Warn(ctx, "ruleId: %d insert scale event to db error:%s", rule.RuleId, utils.Show(err))
		return err
	}
	t.state.EventId = eventId
	return nil
}
