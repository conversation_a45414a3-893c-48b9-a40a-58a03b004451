package bandwidth

//import (
//	"code.byted.org/infcs/dbw-mgr/biz/entity"
//	"code.byted.org/infcs/dbw-mgr/biz/repository"
//	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
//	"code.byted.org/infcs/dbw-mgr/biz/shared"
//	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
//	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
//	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/dslibmocks"
//	"code.byted.org/infcs/ds-lib/common/log"
//	"context"
//	"fmt"
//	"github.com/bytedance/mockey"
//	"github.com/stretchr/testify/assert"
//	"testing"
//	"time"
//)
//
//func mockScaleActor() *AutoBandwidthScaleActor {
//	return &AutoBandwidthScaleActor{
//		state:          &AutoBandwidthScaleActorState{},
//		cnf:            &config.MockConfigProvider{},
//		repo:           &repository.ScaleRepoImpl{},
//		actorClient:    &dslibmocks.MockActorClient{},
//		c3ConfProvider: &mocks.MockC3ConfigProvider{},
//		loca:           &mocks.MockLocation{},
//		idgenSvc:       &mocks.MockService{},
//		nosql:          &MProvider{},
//		ds:             &mocks.MockDataSourceService{},
//	}
//}
//
//func TestOnStart(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockContext).GetName).Return("123").Build()
//	defer mock1.UnPatch()
//
//	actor.OnStart(&mocks.MockContext{})
//	log.Info(context.Background(), "%d", actor.state.RuleId)
//	mock2 := mockey.Mock((*AutoBandwidthScaleActor).finishEvent).Return().Build()
//	defer mock2.UnPatch()
//	actor.state.EventState = shared.EventError
//	actor.OnStart(&mocks.MockContext{})
//	actor.state.EventState = shared.EventFinished
//	actor.OnStart(&mocks.MockContext{})
//}
//
//func TestStartScale(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	actor.state.EventState = shared.EventScaling
//	msgMock := &shared.CreateScaleEvent{
//		RuleId:                        12345,
//		NetworkReceiveThroughputUtil:  0.2,
//		NetworkTransmitThroughputUtil: 0.2,
//	}
//	actor.StartScale(&mocks.MockContext{}, msgMock)
//	actor.state.EventState = shared.EventUndo
//
//	mock1 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//	defer mock2.UnPatch()
//	mock0 := mockey.Mock((*AutoBandwidthScaleActor).isScaleLimited).Return(false).Build()
//	defer mock0.UnPatch()
//	mockRule := &entity.AutoScaleRule{InstanceType: "Redis"}
//	mock3 := mockey.Mock((*repository.ScaleRepoImpl).GetScaleRuleById).Return(mockRule, fmt.Errorf("test")).Build()
//	actor.StartScale(&mocks.MockContext{}, msgMock)
//	mock3.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock4 := mockey.Mock((*repository.ScaleRepoImpl).GetScaleRuleById).Return(mockRule, nil).Build()
//	defer mock4.UnPatch()
//	mockRule.RuleId = 12345
//	mockBandwidthInfo := &datasource.InstanceBandwidthInfo{InstanceSpec: 1024, IsUnShared: true, CurrentBandwidth: 16}
//	mock5 := mockey.Mock((*mocks.MockDataSourceService).GetCurrentBandwidth).Return(mockBandwidthInfo, nil).Build()
//	defer mock5.UnPatch()
//	mock6 := mockey.Mock((*AutoBandwidthScaleActor).createScaleEvent).Return(fmt.Errorf("test")).Build()
//	defer mock6.UnPatch()
//	mock7 := mockey.Mock((*AutoBandwidthScaleActor).CalculateAfterScaleBandwidth).Return(10, nil).Build()
//	defer mock7.UnPatch()
//
//	actor.StartScale(&mocks.MockContext{}, msgMock)
//	mockBandwidthInfo.CurrentBandwidth = 18
//	actor.StartScale(&mocks.MockContext{}, msgMock)
//}
//
//func TestGetBandwidth(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	mockRule := &entity.AutoScaleRule{ScalingType: int8(shared.Shrinkage)}
//	res := actor.getBandwidth(mockRule, &shared.CreateScaleEvent{NetworkReceiveThroughputUtil: 0.4, NetworkTransmitThroughputUtil: 0.6})
//	assert.Equal(t, res, float32(0.6))
//	mockRule.ScalingType = int8(shared.Expansion)
//	res = actor.getBandwidth(mockRule, &shared.CreateScaleEvent{NetworkReceiveThroughputUtil: 0.4, NetworkTransmitThroughputUtil: 0.6})
//	assert.Equal(t, res, float32(0.6))
//}
//
////func TestDoScale(t *testing.T) {
////	actor := mockScaleActor()
////	logMock := mockey.Mock(log.Log).Return().Build()
////	defer logMock.UnPatch()
////
////	mockRule := &entity.AutoScaleRule{InstanceType: "Redis"}
////	mockBandwidthInfo := &datasource.InstanceBandwidthInfo{InstanceSpec: 1024, IsUnShared: true, CurrentBandwidth: 16}
////
////	mock1 := mockey.Mock((*AutoBandwidthScaleActor).watchResult).Return().Build()
////	defer mock1.UnPatch()
////	mock2 := mockey.Mock((*AutoBandwidthScaleActor).finishScale).Return().Build()
////	defer mock2.UnPatch()
////	mock3 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
////	defer mock3.UnPatch()
////	mock4 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
////	defer mock4.UnPatch()
////	mock5 := mockey.Mock((*mocks.MockDataSourceService).BandwidthScale).Return(fmt.Errorf("test")).Build()
////	actor.DoScale(&mocks.MockContext{}, mockRule, mockBandwidthInfo, 18)
////	mock5.UnPatch()
////	time.Sleep(100 * time.Millisecond)
////
////	mock6 := mockey.Mock((*mocks.MockDataSourceService).BandwidthScale).Return(fmt.Errorf("test")).Build()
////	defer mock6.UnPatch()
////	actor.DoScale(&mocks.MockContext{}, mockRule, mockBandwidthInfo, 18)
////}
//
//func TestCalculateAfterScaleBandwidth(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	mockRule := &entity.AutoScaleRule{}
//	mockMsg := &shared.CreateScaleEvent{NetworkReceiveThroughputUtil: 0.3, NetworkTransmitThroughputUtil: 0.3}
//	mock1 := mockey.Mock((*mocks.MockDataSourceService).GetMinBandwidth).Return(12, nil).Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*mocks.MockDataSourceService).GetMaxBandwidth).Return(20, nil).Build()
//	defer mock2.UnPatch()
//
//	res, _ := actor.CalculateAfterScaleBandwidth(&mocks.MockContext{}, mockRule, mockMsg, 16)
//	assert.Equal(t, res, 12)
//	mockMsg = &shared.CreateScaleEvent{NetworkReceiveThroughputUtil: 0.6, NetworkTransmitThroughputUtil: 0.6}
//	res, _ = actor.CalculateAfterScaleBandwidth(&mocks.MockContext{}, mockRule, mockMsg, 16)
//	assert.Equal(t, res, 19)
//	mockMsg = &shared.CreateScaleEvent{NetworkReceiveThroughputUtil: 0.9, NetworkTransmitThroughputUtil: 0.9}
//	res, _ = actor.CalculateAfterScaleBandwidth(&mocks.MockContext{}, mockRule, mockMsg, 16)
//	assert.Equal(t, res, 20)
//}
//
//func TestCheckScaleEvent(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	mock1 := mockey.Mock((*AutoBandwidthScaleActor).finishEvent).Return().Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*AutoBandwidthScaleActor).retryScale).Return().Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//	defer mock4.UnPatch()
//
//	actor.state.EventState = shared.EventScaling
//	actor.CheckScaleEvent(&mocks.MockContext{})
//	actor.state.RetryCount = 11
//	actor.CheckScaleEvent(&mocks.MockContext{})
//	actor.state.EventState = shared.EventFinished
//	actor.CheckScaleEvent(&mocks.MockContext{})
//	actor.state.EventState = shared.EventError
//	actor.CheckScaleEvent(&mocks.MockContext{})
//	actor.state.EventState = shared.EventUndo
//	actor.CheckScaleEvent(&mocks.MockContext{})
//}
//
//func TestFinishEvent(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockContext).Stop).Return().Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//	defer mock3.UnPatch()
//
//	actor.finishEvent(&mocks.MockContext{}, shared.EventUndo)
//	mock4 := mockey.Mock((*repository.ScaleRepoImpl).UpdateScaleEndEvent).Return(fmt.Errorf("test")).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*repository.ScaleRepoImpl).UpdateScaleErrorEvent).Return(nil).Build()
//	defer mock5.UnPatch()
//	actor.finishEvent(&mocks.MockContext{}, shared.EventFinished)
//	actor.finishEvent(&mocks.MockContext{}, shared.EventError)
//	actor.state.RetryCount = 11
//	actor.finishEvent(&mocks.MockContext{}, shared.EventError)
//}
//
//func TestRetryScale(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockContext).Stop).Return().Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//	defer mock2.UnPatch()
//
//	actor.state.RuleId = 1111
//	mock3 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//	defer mock3.UnPatch()
//	actor.retryScale(&mocks.MockContext{})
//	actor.state.RetryCount = 11
//	mock4 := mockey.Mock((*AutoBandwidthScaleActor).finishEvent).Return().Build()
//	defer mock4.UnPatch()
//	actor.retryScale(&mocks.MockContext{})
//}
//
//func TestGetEventId(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	actor.state.EventId = 12345
//	res, err := actor.getEventId(&mocks.MockContext{})
//	assert.Equal(t, res, int64(12345))
//	assert.Nil(t, err)
//
//	actor.state.EventId = 0
//	mock1 := mockey.Mock((*mocks.MockService).NextID).Return(1, fmt.Errorf("test")).Build()
//	_, err = actor.getEventId(&mocks.MockContext{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*mocks.MockService).NextID).Return(11111, nil).Build()
//	defer mock2.UnPatch()
//
//	res, err = actor.getEventId(&mocks.MockContext{})
//	assert.Equal(t, res, int64(11111))
//	assert.Nil(t, err)
//}
//
//func TestCreateScaleEvent(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	mock1 := mockey.Mock((*AutoBandwidthScaleActor).getEventId).Return(12138, nil).Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*AutoBandwidthScaleActor).getBandwidth).Return(0.6).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*repository.ScaleRepoImpl).CreateScaleEvent).Return(fmt.Errorf("test")).Build()
//	err := actor.createScaleEvent(&mocks.MockContext{}, &entity.AutoScaleRule{}, &shared.CreateScaleEvent{}, 1)
//	assert.NotNil(t, err)
//	assert.Equal(t, int64(0), actor.state.EventId)
//	mock3.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock4 := mockey.Mock((*repository.ScaleRepoImpl).CreateScaleEvent).Return(nil).Build()
//	defer mock4.UnPatch()
//	err = actor.createScaleEvent(&mocks.MockContext{}, &entity.AutoScaleRule{}, &shared.CreateScaleEvent{}, 1)
//	assert.Nil(t, err)
//	assert.Equal(t, int64(12138), actor.state.EventId)
//}
//
//func TestWatchResult(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockDataSourceService).GetCurrentBandwidth).Return(&datasource.InstanceBandwidthInfo{CurrentBandwidth: 10}, nil).Build()
//	defer mock1.UnPatch()
//	actor.watchResult(&mocks.MockContext{}, nil, 10)
//}
//
//func TestScalingStart(t *testing.T) {
//	actor := mockScaleActor()
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	mock1 := mockey.Mock((*AutoBandwidthScaleActor).watchResult).Return().Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*AutoBandwidthScaleActor).finishScale).Return().Build()
//	defer mock2.UnPatch()
//
//	mock3 := mockey.Mock((*repository.ScaleRepoImpl).GetScaleRuleById).Return(nil, fmt.Errorf("test")).Build()
//	mock4 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//	defer mock5.UnPatch()
//
//	actor.state.RuleId = 1111
//	actor.scalingStart(&mocks.MockContext{})
//	mock3.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	rule := &entity.AutoScaleRule{}
//	mock6 := mockey.Mock((*repository.ScaleRepoImpl).GetScaleRuleById).Return(rule, nil).Build()
//	defer mock6.UnPatch()
//	actor.scalingStart(&mocks.MockContext{})
//	rule.RuleId = 1111
//	actor.scalingStart(&mocks.MockContext{})
//}
