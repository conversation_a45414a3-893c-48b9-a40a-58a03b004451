package actors

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/metric"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	"github.com/qjpcpu/fp"
)

func (a *SessionActor) closeSession(ctx types.Context, msg *shared.CloseSession) {
	connIDs := fp.StreamOf(a.state.Conn).Map(func(c *Connection) string { return c.ID }).Strings()
	for _, connID := range connIDs {
		a.closeSessionConn(ctx, connID)
	}
	if a.state.DataSource != nil {
		// UnregisterSelf
		a.closeTunnel(ctx, a.state.DataSource)
		//a.removeWhiteList(ctx, a.state.DataSource)
	}
	log.Info(ctx, "session %s prepare suicide", a.GetSessionID(ctx))
	defer func() {
		a.finalize(ctx)
		sessionStatus := "success"
		if !a.state.SessionCreated || a.state.isIdleTimeout() {
			sessionStatus = "failed"
		}
		if a.state.DataSource != nil {
			metric.GlobalMetricData.MetricsMgrSessionNum.DecrWithLabel(metric.MgrSessionNumLabel{
				RegionId:      a.loc.RegionID(),
				InstanceId:    a.state.DataSource.InstanceId,
				DbType:        a.state.DataSource.Type.String(),
				Username:      a.state.DataSource.User,
				SessionStatus: sessionStatus})
		}
	}()
}

func (a *SessionActor) finalize(ctx types.Context) {
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	a.crRepo.MarkExpired(ctx, a.GetSessionID(ctx))
	// ignore error
	_ = a.actorCli.KindOf(consts.SqlAssistantActorKind).Send(ctx, ctx.GetName(), &shared.CloseSqlAssistant{})
}
