package actors

import (
	"context"
	"errors"
	"strings"
	"time"

	dbwerrors "code.byted.org/infcs/dbw-mgr/biz/errors"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

	"code.byted.org/infcs/dbw-mgr/biz/actors/dialog"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/metric"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	"github.com/qjpcpu/fp"
)

func (a *SessionActor) createSession(ctx types.Context, msg *shared.CreateSession) {
	result, err := a._createSession(ctx, msg)

	defer func() {
		sessionStatus := "success"
		if !a.state.SessionCreated {
			sessionStatus = "failed"
		}
		if a.state.DataSource != nil {
			metric.GlobalMetricData.MetricsMgrSessionNum.IncrWithLabel(
				metric.MgrSessionNumLabel{
					RegionId:      a.loc.RegionID(),
					DbType:        a.state.DataSource.Type.String(),
					InstanceId:    a.state.DataSource.InstanceId,
					Username:      a.state.DataSource.User,
					SessionStatus: sessionStatus})

			metric.GlobalMetricData.MetricsMgrRdsNum.IncrWithLabel(
				metric.MgrRdsNumLabel{
					RegionId:   a.loc.RegionID(),
					DbType:     a.state.DataSource.Type.String(),
					InstanceId: a.state.DataSource.InstanceId,
				})
		}
	}()
	if err != nil {
		log.Warn(ctx, "createSession failed,  unregisterMySelf start. instanceId is:%v", msg.Source.InstanceId)

		ctx.Respond(&shared.SessionCreated{
			Code:          shared.CreateSessionFail,
			ErrorMessage:  err.Error(),
			StandardError: consts.TranslateStandardErrorToShared(err),
		})
		// 获取一下当前createSession的连接信息，监控打点用
		a.state.TenantID = fwctx.GetTenantID(ctx)
		a.state.UserID = fwctx.GetUserID(ctx)
		a.state.DataSource = msg.Source
		a.state.SessionCreated = false
		return
	}
	a.state.DataSource = result.Source
	// mongo 需要，创建Session时的DB作为此Session的鉴权DB
	a.state.DataSource.AuthDb = msg.Source.Db
	a.state.addConn(result.Conn)
	a.state.SessionCreated = true
	a.state.SesstionTimeoutSeconds = msg.SessionTimeoutSeconds
	a.state.TenantID = fwctx.GetTenantID(ctx)
	a.state.UserID = fwctx.GetUserID(ctx)
	ctx.Respond(&shared.SessionCreated{
		SessionId:                  a.GetSessionID(ctx),
		DefaultConnectionId:        result.Conn.ID,
		DefaultConnectionName:      result.Conn.Name,
		DefaultConnectionCurrentDb: result.Conn.CurrentDB,
		Code:                       shared.CreatedOK,
	})
}

type createSessionResult struct {
	Source *shared.DataSource
	Conn   *Connection
}

func (a *SessionActor) _createSession(ctx types.Context, msg *shared.CreateSession) (*createSessionResult, error) {
	connID, err := a.idSvc.NextIDStr(ctx)
	if err != nil {
		log.Warn(ctx, "generate next id error:%s", err.Error())
		return nil, err
	}
	conn := a.state.newConnection(connID, msg.DefaultConnectionName)

	//TODO 要把nodeid加进去，目前走到这里，msg里面是有nodeid的
	// 这里调用了DescribeInstance接口，拿到了VPCID
	realSource, err := a.mapDataSource(ctx, msg.Source)
	if err != nil {
		log.Warn(ctx, "mapDataSource error:%s", err.Error())
		return nil, err
	}
	msg.Source = realSource
	if err = a.createWhiteList(ctx, conn.ID, msg); err != nil {
		log.Warn(ctx, "createWhiteList error:%s", err.Error())
		return nil, err
	}
	if err = a.registerMySelf(ctx, realSource); err != nil {
		log.Warn(ctx, "registerMySelf error:%s", err.Error())
		return nil, err
	}
	err = a.openTunnel(ctx, realSource)
	if err != nil {
		log.Warn(ctx, "openTunnel error:%s", err.Error())
		return nil, err
	}
	connInfo, err := a.connect(ctx, conn.ID, realSource, msg.SessionTimeoutSeconds)
	if err != nil {
		log.Warn(ctx, "connect error:%s", err.Error())
		return nil, a.wrapError(ctx, err)
	}
	conn.OuterID = connInfo.OuterConnectionId
	conn.CurrentDB = connInfo.CurrentDb
	return &createSessionResult{Source: realSource, Conn: conn}, nil
}

func (a *SessionActor) wrapError(ctx context.Context, err error) error {
	if err == nil {
		return nil
	}
	if dbwerrors.IsWhiteIPError(err) {
		return consts.ErrorOf(model.ErrorCode_AllowlistError)
	}
	if dbwerrors.IsEmptyVPCError(err) {
		return consts.ErrorOf(model.ErrorCode_NotSupprotNoVpcInstance)
	}
	if dbwerrors.IsAccessDeniedError(err) || dbwerrors.IsUsernamePasswordError(err) {
		return consts.ErrorOf(model.ErrorCode_CheckRDSConnectionFailed)
	} else if dbwerrors.IsUsernamePasswordError(err) {
		return consts.ErrorOf(model.ErrorCode_CheckConnectionFailed)
	}
	if dbwerrors.IsOperateAllowlistError(err) {
		return consts.ErrorOf(model.ErrorCode_ModifyInstanceAllowListFailed)
	}
	if dbwerrors.IsLoginFailedError(err) {
		return consts.ErrorOf(model.ErrorCode_CheckConnectionFailed)
	}
	if dbwerrors.IsPSMAuthFailed(err) {
		return consts.ErrorOf(model.ErrorCode_PSMAuthFailed)
	}
	return consts.ErrorOf(model.ErrorCode_CreateSessionError)
}

func (a *SessionActor) createInternalSession(ctx types.Context, msg *shared.CreateInternalSession) {
	newCtx, _ := context.WithTimeout(ctx, time.Second*time.Duration(dialog.GetSessionTimeoutSeconds-2))
	ctx = types.WithContext(ctx, newCtx)
	result, err := a._createInternalSession(ctx, msg)
	if err != nil {
		ctx.Respond(&shared.InternalSessionCreated{
			Code:         shared.CreateSessionFail,
			ErrorMessage: err.Error(),
		})
		return
	}
	a.state.DataSource = result.Source
	a.state.SessionCreated = true
	a.state.SesstionTimeoutSeconds = msg.SessionTimeoutSeconds
	a.state.TenantID = fwctx.GetTenantID(ctx)
	a.state.UserID = fwctx.GetUserID(ctx)
	ctx.Respond(&shared.InternalSessionCreated{
		SessionId: a.GetSessionID(ctx),
		Code:      shared.CreatedOK,
	})
}

func (a *SessionActor) checkInternalSession(ctx types.Context, msg *shared.CheckInternalSession) {
	if err := a.sources.CheckDataSource(ctx, a.state.DataSource); err != nil {
		ctx.Respond(&shared.CheckInternalSessionFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.CheckInternalSessionSucceed{})
}

func (a *SessionActor) _createInternalSession(ctx types.Context, msg *shared.CreateInternalSession) (*createSessionResult, error) {
	var realSource *shared.DataSource
	if msg.Source.Address == "" {
		//realSource, err = a.mapDataSource(ctx, msg.Source)
		wlID, err := a.sources.AddWhiteList(ctx, a.GetSessionID(ctx), msg.Source)
		if err != nil {
			return nil, err
		}
		a.state.WlID = wlID
		realSource, err = a.mapInnerDataSource(ctx, msg.Source)
		if err != nil {
			return nil, err
		}
	} else {
		realSource = msg.Source
	}
	return &createSessionResult{Source: realSource}, nil
}

func (a *SessionActor) createWhiteList(ctx types.Context, connID string, msg *shared.CreateSession) error {
	return fp.M(a.getConnectorLocation(ctx, connID)).
		Map(func(ip string) (string, error) {
			wlID, err := a.sources.AddWhiteList(ctx, a.GetSessionID(ctx), msg.Source)
			a.state.WlID = wlID
			return ip, err
		}).
		Map(func(ip string) error {
			a.state.addClientIP(ip)
			return nil
		}).
		Val().
		Err()
}

func (a *SessionActor) removeWhiteList(ctx types.Context, source *shared.DataSource) error {
	if err := a.sources.RemoveWhiteList(ctx, a.GetSessionID(ctx), source, a.state.WlID); err != nil {
		log.Warn(ctx, "remove white list fail %v", err)
		return err
	}
	log.Info(ctx, "remove white list OK")
	return nil
}

func (a *SessionActor) mapDataSource(ctx types.Context, ds *shared.DataSource) (*shared.DataSource, error) {
	newDS := new(shared.DataSource)
	*newDS = *ds
	return newDS, a.sources.FillDataSource(ctx, newDS)
}

func (a *SessionActor) mapInnerDataSource(ctx types.Context, ds *shared.DataSource) (*shared.DataSource, error) {
	newDS := new(shared.DataSource)
	*newDS = *ds
	return newDS, a.sources.FillInnerDataSource(ctx, newDS)
}

func (a *SessionActor) openTunnel(ctx types.Context, ds *shared.DataSource) error {
	return a.sources.OpenTunnel(ctx, ds, a.GetSessionID(ctx))
}

func (a *SessionActor) connect(ctx types.Context, connID string, src *shared.DataSource, timeoutSeconds int64) (resp *shared.ConnectionInfo, err error) {
	whiteListNotReady := func(err error) bool {
		return strings.Contains(err.Error(), `IP NOT IN white List`) || strings.Contains(err.Error(), `client ip is not in whitelist`)
	}
	for i := 0; i < 3; i++ {
		if resp, err = a._connect(ctx, connID, src, timeoutSeconds); err == nil {
			return
		} else if whiteListNotReady(err) {
			log.Info(ctx, "whitelist not ready, waiting...")
			time.Sleep(3 * time.Second)
		} else {
			log.Info(ctx, "connect instance error %s", err)
			return nil, err
		}
	}
	//经过三次循环之后，依然从这里出来，说明白名单还是没加上，那么这个时候就把没加上白名单的错误返回出去
	return nil, consts.ErrorOf(model.ErrorCode_AllowListMaintaining)
}

func (a *SessionActor) _connect(ctx types.Context, connID string, src *shared.DataSource, timeoutSeconds int64) (*shared.ConnectionInfo, error) {
	actorName := a.getConnectionActorName(ctx, connID)
	actorClient := ctx.ClientOf(consts.SessionActorKind)
	callOpts := actorClient.NewCallOpts().WithTimeout(20 * time.Second).WithRetry(1)
	resp, err := ctx.ClientOf(consts.ConnectionActorKind).
		Call(ctx,
			actorName,
			&shared.OpenConnection{
				Source:             src,
				Supervisor:         &shared.ActorRef{Kind: consts.SessionActorKind, Name: a.GetSessionID(ctx)},
				IdleTimeoutSeconds: timeoutSeconds,
			},
			callOpts,
		)
	if err != nil {
		log.Warn(ctx, "open connection fail %v", err)
		return nil, err
	}

	switch rsp := resp.(type) {
	case *shared.OpenConnectionSuccessful:
		log.Info(ctx, "open connection %v ok", actorName)
		return &shared.ConnectionInfo{ConnectionId: connID, OuterConnectionId: rsp.OuterConnectionId, CurrentDb: rsp.CurrentDb}, nil
	case *shared.OpenConnectionFailed:
		log.Warn(ctx, "open connection fail %#v", rsp)
		return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, rsp.ErrorMessage)
	default:
		log.Warn(ctx, "open connection fail, unknown response %#v", rsp)
		return nil, consts.ErrorWithParam(model.ErrorCode_InternalError, "open connection fail,please retry later")
	}
}

// getConnectorLocation 返回Connections Pod的HostIP
func (a *SessionActor) getConnectorLocation(ctx types.Context, connID string) (string, error) {
	resp, err := ctx.ClientOf(consts.ConnectionActorKind).
		Call(ctx, a.getConnectionActorName(ctx, connID), &shared.GetLocation{})
	if err != nil {
		log.Warn(ctx, "get location of conn %s fail %v", connID, err)
		return "", err
	}
	switch msg := resp.(type) {
	case *shared.Location:
		log.Info(ctx, "get connection %s location %s", connID, msg.HostIp)
		return msg.HostIp, nil
	}
	log.Warn(ctx, "unknown response %#v when query location", resp)
	return "", errors.New(`can' find connection location`)
}
