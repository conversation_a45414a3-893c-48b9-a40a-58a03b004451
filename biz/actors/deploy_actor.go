package actors

import (
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"reflect"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy/resource"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/infra"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
)

type service int

const (
	dbw service = iota
	bytebrain
	DetectionByteBrain
	dbwtransit
)

type NewDeployActorIn struct {
	dig.In
	Config                   config.ConfigProvider
	Infra                    infra.Service
	DbwDeploy                *deploy.DbwDeployImpl
	ByteBrainDeploy          *deploy.BrainDeployImpl
	DetectionByteBrainDeploy *deploy.DetectionBrainDeployImpl
	FullSqlDeploy            *deploy.FullSqlDeployImpl
}

func NewDeployActor(p NewDeployActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.DeployActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &DeployActor{
				state:                    newDeployState(state, p.Config),
				cnf:                      p.Config,
				DbwDeploy:                p.DbwDeploy,
				ByteBrainDeploy:          p.ByteBrainDeploy,
				DetectionByteBrainDeploy: p.DetectionByteBrainDeploy,
				FullSqlDeploy:            p.FullSqlDeploy,
				infra:                    p.Infra,
			}
		}),
	}
}

type componentInfo struct {
	Name     string
	Replicas int64
	Image    string
}

type deployState struct {
	DeployInfo                   *shared.DeployInfo
	DeployedOnce                 bool
	ComponentsInfo               map[string][]*componentInfo
	ByteBrainDeployInfo          *shared.DeployInfo
	DetectionByteBrainDeployInfo *shared.DeployInfo
	FullSqlDeployInfo            *shared.DeployInfo
}

func (s *deployState) Bytes() []byte { return utils.MustJSONMarshal(s) }

func (s *deployState) UpsertCluster(c *shared.ClusterInfo, svc service) {
	s.RemoveCluster(c, svc)
	switch svc {
	case dbw:
		s.DeployInfo.Clusters = append(s.DeployInfo.Clusters, c)
	case bytebrain:
		s.ByteBrainDeployInfo.Clusters = append(s.ByteBrainDeployInfo.Clusters, c)
	case DetectionByteBrain:
		s.DetectionByteBrainDeployInfo.Clusters = append(s.DetectionByteBrainDeployInfo.Clusters, c)
	case dbwtransit:
		s.FullSqlDeployInfo.Clusters = append(s.FullSqlDeployInfo.Clusters, c)
	}
}

func (s *deployState) RemoveCluster(c *shared.ClusterInfo, svc service) {
	switch svc {
	case dbw:
		fp.StreamOf(s.DeployInfo.Clusters).
			Reject(func(s *shared.ClusterInfo) bool {
				return s.Name == c.Name
			}).
			ToSlice(&s.DeployInfo.Clusters)
	case bytebrain:
		fp.StreamOf(s.DeployInfo.Clusters).
			Reject(func(s *shared.ClusterInfo) bool {
				return s.Name == c.Name
			}).
			ToSlice(&s.ByteBrainDeployInfo.Clusters)
	case DetectionByteBrain:
		_ = fp.StreamOf(s.DeployInfo.Clusters).
			Reject(func(s *shared.ClusterInfo) bool {
				return s.Name == c.Name
			}).
			ToSlice(&s.DetectionByteBrainDeployInfo.Clusters)
	case dbwtransit:
		_ = fp.StreamOf(s.DeployInfo.Clusters).
			Reject(func(s *shared.ClusterInfo) bool {
				return s.Name == c.Name
			}).
			ToSlice(&s.FullSqlDeployInfo.Clusters)
	}

}

func (s *deployState) UpdateComponents(cluster *shared.ClusterInfo, cnf config.ConfigProvider) {
	s.ComponentsInfo[cluster.Name] = []*componentInfo{
		{
			Name:     resource.ConnectionsResourceNameInVS,
			Replicas: cnf.Get(context.TODO()).ConnectionsReplica,
			Image:    cnf.Get(context.TODO()).ConnectionsImage,
		},
		{
			Name:     resource.MonitorResourceNameInVS,
			Replicas: cnf.Get(context.TODO()).MonitorReplicas,
			Image:    cnf.Get(context.TODO()).MonitorImage,
		},
		{
			Name:     resource.CollectorResourceNameInVS,
			Replicas: cnf.Get(context.TODO()).CollectorReplicas,
			Image:    cnf.Get(context.TODO()).CollectorImage,
		},
		{
			Name:     resource.DbwSyncerResource,
			Replicas: cnf.Get(context.TODO()).SyncerReplicas,
			Image:    cnf.Get(context.TODO()).SyncerImage,
		},
		//{
		//	Name:     resource.FullSqlLogTransitNameInVS,
		//	Replicas: cnf.Get(context.TODO()).FullSqlLogTransitReplicas,
		//	Image:    cnf.Get(context.TODO()).FullSqlLogTransitImage,
		//},
	}
}

func (s *deployState) ClearComponents(c *shared.ClusterInfo) {
	delete(s.ComponentsInfo, c.Name)
}

func newDeployState(state []byte, config config.ConfigProvider) *deployState {
	var st deployState
	if len(state) == 0 {
		st = deployState{}
	} else {
		utils.MustUnmarshal(state, &st)
	}
	if st.DeployInfo == nil {
		st.DeployInfo = &shared.DeployInfo{}
	}
	if st.ComponentsInfo == nil {
		st.ComponentsInfo = make(map[string][]*componentInfo)
		for _, c := range st.DeployInfo.Clusters {
			st.ComponentsInfo[c.Name] = []*componentInfo{
				{
					Name:     resource.ConnectionsResourceNameInVS,
					Replicas: 3,
				},
			}
		}
	}
	if st.ByteBrainDeployInfo == nil {
		st.ByteBrainDeployInfo = &shared.DeployInfo{}
	}

	if st.DetectionByteBrainDeployInfo == nil {
		st.DetectionByteBrainDeployInfo = &shared.DeployInfo{}
	}

	if st.FullSqlDeployInfo == nil {
		st.FullSqlDeployInfo = &shared.DeployInfo{}
	}

	return &st
}

// TODO deploy actor need to refactor
type DeployActor struct {
	state *deployState
	/* dependency */
	cnf                      config.ConfigProvider
	infra                    infra.Service
	DbwDeploy                *deploy.DbwDeployImpl
	ByteBrainDeploy          *deploy.BrainDeployImpl
	DetectionByteBrainDeploy *deploy.DetectionBrainDeployImpl
	FullSqlDeploy            *deploy.FullSqlDeployImpl
}

func (a *DeployActor) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		a.tryDefaultDeploy(ctx)
	case *shared.GetDeployInfo:
		ctx.Respond(a.state.DeployInfo)
	case *shared.StartDeploy:
		a.startDeploy(ctx, msg.Force)
	}
}

func (a *DeployActor) GetState() []byte {
	return a.state.Bytes()
}

func (a *DeployActor) tryDefaultDeploy(ctx types.Context) {
	log.Info(ctx, "DeployActor started")
	if a.state.DeployedOnce {
		return
	}
	defer func() {
		a.state.DeployedOnce = true
	}()
	cnf := a.cnf.Get(ctx)
	if len(cnf.DeployInfo) > 0 {
		return
	}
	if a.state.DeployInfo != nil && len(a.state.DeployInfo.Clusters) > 0 {
		return
	}
	clusters, err := a.getClusters(ctx)
	if err != nil || len(clusters) != 1 {
		return
	}
	/* auto deploy default cluster only when */
	/* 1. no deploy info config in DBW Params */
	/* 2. i haven't deployed to any cluster */
	/* 3. there's only one cluster in this region */
	defaultCluster := clusters[0]
	ci := &shared.ClusterInfo{
		Name:     defaultCluster.Name,
		Domain:   defaultCluster.Domain,
		NodePool: fp.StreamOf(defaultCluster.AvailableNodePools).First().String(),
	}
	if err = a.deployToCluster(ctx, ci, dbw); err == nil {
		a.state.UpsertCluster(ci, dbw)
		a.state.UpdateComponents(defaultCluster, a.cnf)
		cnf = a.cnf.Get(ctx)
		cnf.DeployInfo = []*shared.ClusterInfo{defaultCluster}
		a.cnf.Update(ctx, cnf)
	}
}

func (a *DeployActor) startDeploy(ctx types.Context, force bool) {
	log.Info(ctx, "force deploy is %v", force)
	if force {
		fp.StreamOf(a.getCurrentDeployInfo(ctx, dbw)).
			Map(func(c *shared.ClusterInfo) error {
				return a.deployToCluster(ctx, c, dbw)
			}).
			Error()
		return
	}

	log.Info(ctx, "start deploy dbw")
	a.startDeployDBW(ctx)
	log.Info(ctx, "start deploy bytebrain")
	a.startDeployByteBrain(ctx)
	log.Info(ctx, "start deploy detection bytebrain")
	a.startDetectionDeployByteBrain(ctx)
	log.Info(ctx, " start deploy fullsql transit")
	a.startFullSqlTransitDeploy(ctx)
}

func (a *DeployActor) startDeployDBW(ctx types.Context) {
	plan := a.getDeployPlan(ctx, dbw)
	log.Info(ctx, "start deploy dbw,get deploy plan is %s", utils.Show(plan))
	fp.StreamOf(plan.Add).
		Union(fp.StreamOf(plan.Update)).
		Map(func(c *shared.ClusterInfo) (*shared.ClusterInfo, error) {
			return c, a.deployToCluster(ctx, c, dbw)
		}).
		Map(func(c *shared.ClusterInfo) error {
			a.state.UpsertCluster(c, dbw)
			a.state.UpdateComponents(c, a.cnf)
			return nil
		}).
		Error()

	fp.StreamOf(plan.Del).
		Map(func(c *shared.ClusterInfo) (*shared.ClusterInfo, error) {
			return c, a.removeDeployFromCluster(ctx, c, dbw)
		}).
		Map(func(c *shared.ClusterInfo) error {
			a.state.RemoveCluster(c, dbw)
			return nil
		}).
		Error()
}

func (a *DeployActor) startDeployByteBrain(ctx types.Context) {
	log.Info(ctx, "start deploy ByteBrain")
	plan := a.getDeployPlan(ctx, bytebrain)
	fp.StreamOf(plan.Add).
		Union(fp.StreamOf(plan.Update)).
		Map(func(c *shared.ClusterInfo) (*shared.ClusterInfo, error) {
			return c, a.deployToCluster(ctx, c, bytebrain)
		}).
		Map(func(c *shared.ClusterInfo) error {
			a.state.UpsertCluster(c, bytebrain)
			a.state.UpdateComponents(c, a.cnf)
			return nil
		}).
		Error()
	log.Info(ctx, "end deploy ByteBrain")
	fp.StreamOf(plan.Del).
		Map(func(c *shared.ClusterInfo) (*shared.ClusterInfo, error) {
			return c, a.removeDeployFromCluster(ctx, c, bytebrain)
		}).
		Map(func(c *shared.ClusterInfo) error {
			a.state.RemoveCluster(c, bytebrain)
			return nil
		}).
		Error()
}

func (a *DeployActor) startDetectionDeployByteBrain(ctx types.Context) {
	plan := a.getDeployPlan(ctx, DetectionByteBrain)
	log.Info(ctx, "deploy plan:%s ", utils.Show(plan))
	_ = fp.StreamOf(plan.Add).
		Union(fp.StreamOf(plan.Update)).
		Map(func(c *shared.ClusterInfo) (*shared.ClusterInfo, error) {
			return c, a.deployToCluster(ctx, c, DetectionByteBrain)
		}).
		Map(func(c *shared.ClusterInfo) error {
			a.state.UpsertCluster(c, DetectionByteBrain)
			a.state.UpdateComponents(c, a.cnf)
			return nil
		}).
		Error()

	_ = fp.StreamOf(plan.Del).
		Map(func(c *shared.ClusterInfo) (*shared.ClusterInfo, error) {
			return c, a.removeDeployFromCluster(ctx, c, DetectionByteBrain)
		}).
		Map(func(c *shared.ClusterInfo) error {
			a.state.RemoveCluster(c, DetectionByteBrain)
			return nil
		}).
		Error()
}

func (a *DeployActor) startFullSqlTransitDeploy(ctx types.Context) {
	plan := a.getDeployPlan(ctx, dbwtransit)
	log.Info(ctx, "deploy plan %s: %s ", dbwtransit, utils.Show(plan))
	err := fp.StreamOf(plan.Add).
		Union(fp.StreamOf(plan.Update)).
		Map(func(c *shared.ClusterInfo) (*shared.ClusterInfo, error) {
			return c, a.deployToCluster(ctx, c, dbwtransit)
		}).
		Map(func(c *shared.ClusterInfo) error {
			a.state.UpsertCluster(c, dbwtransit)
			a.state.UpdateComponents(c, a.cnf)
			return nil
		}).
		Error()
	if err != nil {
		log.Warn(ctx, "startFullSqlTransitDeploy add error:%s", err)
	}
	err = fp.StreamOf(plan.Del).
		Map(func(c *shared.ClusterInfo) (*shared.ClusterInfo, error) {
			return c, a.removeDeployFromCluster(ctx, c, dbwtransit)
		}).
		Map(func(c *shared.ClusterInfo) error {
			a.state.RemoveCluster(c, dbwtransit)
			return nil
		}).
		Error()
	if err != nil {
		log.Warn(ctx, "startFullSqlTransitDeploy del error:%s", err)
	}
}

func (a *DeployActor) getClusters(ctx types.Context) ([]*shared.ClusterInfo, error) {
	if res, err := a.infra.ListClusters(ctx); err != nil {
		log.Info(ctx, "get clusters fail %v", err)
		return nil, err
	} else {
		var list []*shared.ClusterInfo
		fp.StreamOf(res.Clusters).
			Map(func(c *infra.KubeCluster) *shared.ClusterInfo {
				return &shared.ClusterInfo{
					Name:               c.Name,
					Domain:             c.Domain,
					AvailableNodePools: c.NodePools,
				}
			}).
			ToSlice(&list)
		return list, nil
	}
}

func (a *DeployActor) getDeployPlan(ctx types.Context, svc service) (ret deployPlan) {
	expected := a.getConfigDeployInfo(ctx, svc)
	current := a.getCurrentDeployInfo(ctx, svc)
	//expected := a.getConfigComponents(ctx)
	//current := a.getCurComponents(ctx)
	keyfn := func(com *shared.ClusterInfo) string {
		return com.Name
	}
	log.Info(ctx, "expected(from ops config): %s, current(from actor state): %s ,svc %s", utils.Show(expected), utils.Show(current), svc)
	fp.StreamOf(expected).
		SubBy(fp.StreamOf(current), keyfn).
		ToSlice(&ret.Add)
	fp.StreamOf(current).
		SubBy(fp.StreamOf(expected), keyfn).
		ToSlice(&ret.Del)

	var currsetSet map[string]*shared.ClusterInfo
	fp.StreamOf(current).
		ToSetBy(func(c *shared.ClusterInfo) string { return c.Name }).
		To(&currsetSet)
	log.Info(ctx, "current deploy set is %s", utils.Show(currsetSet))
	fp.StreamOf(expected).
		Filter(func(c *shared.ClusterInfo) bool {
			v, ok := currsetSet[c.Name]
			//return ok && reflect.DeepEqual(v, c)
			log.Info(ctx, "v is %s,c is %s", utils.Show(v), utils.Show(c))
			return (ok && (v.Domain != c.Domain || v.NodePool != c.NodePool)) || a.componentsNeedUpdate(ctx, c)
		}).
		ToSlice(&ret.Update)
	log.Info(ctx, "deploy info is add: %s, del: %s,update %s ,currentSet:%s , svc %s",
		utils.Show(ret.Add), utils.Show(ret.Del), utils.Show(ret.Update), utils.Show(currsetSet), utils.Show(svc))
	return
}

func (a *DeployActor) needUpdate(comps []*shared.Component, com *shared.Component) bool {
	for _, c := range comps {
		if c.Name == com.Name && !reflect.DeepEqual(c, com) {
			return true
		}
	}
	return false
}

// ugly TODO enhance maintainability
func (a *DeployActor) componentsNeedUpdate(ctx context.Context, cluster *shared.ClusterInfo) bool {
	// TODO Magic Number Need To Be Update
	/*
		dbw-connections、dbw-monitor、dbw-collector、dbw-transit、dbw-syncer 5个组件,所以是5
	*/
	log.Info(ctx, "state components info is %s", utils.Show(a.state.ComponentsInfo[cluster.Name]))
	if len(a.state.ComponentsInfo[cluster.Name]) != 3 {
		return true
	}
	for _, com := range a.state.ComponentsInfo[cluster.Name] {
		log.Info(ctx, "com.Name is %v", com.Name)
		switch com.Name {
		case resource.MonitorResourceNameInVS:
			if com.Replicas != a.cnf.Get(ctx).MonitorReplicas ||
				com.Image != a.cnf.Get(ctx).MonitorImage {
				return true
			}
		case resource.ConnectionsResourceNameInVS:
			if com.Replicas != a.cnf.Get(ctx).ConnectionsReplica ||
				com.Image != a.cnf.Get(ctx).ConnectionsImage {
				return true
			}
		case resource.CollectorResourceNameInVS:
			if com.Replicas != a.cnf.Get(ctx).CollectorReplicas ||
				com.Image != a.cnf.Get(ctx).CollectorImage {
				return true
			}
			//case resource.DbwSyncerResource:
			//	if com.Replicas != a.cnf.Get(ctx).SyncerReplicas ||
			//		com.Image != a.cnf.Get(ctx).SyncerImage {
			//		return true
			//	}
			//case resource.FullSqlLogTransitNameInVS:
			//	if com.Replicas != a.cnf.Get(ctx).FullSqlLogTransitReplicas ||
			//		com.Image != a.cnf.Get(ctx).FullSqlLogTransitImage {
			//		return true
			//	}
		}

	}
	return false
}

type deployPlan struct {
	Add    []*shared.ClusterInfo
	Del    []*shared.ClusterInfo
	Update []*shared.ClusterInfo
}

func (dp deployPlan) isEmpty() bool {
	return len(dp.Add) == 0 && len(dp.Del) == 0 && len(dp.Update) == 0
}

func (a *DeployActor) getCurrentDeployInfo(ctx types.Context, svc service) (ret []*shared.ClusterInfo) {
	if svc == dbw {
		fp.StreamOf(a.state.DeployInfo.Clusters).
			UniqBy(func(c *shared.ClusterInfo) string {
				return c.Name
			}).
			ToSlice(&ret)
	} else if svc == bytebrain {
		fp.StreamOf(a.state.ByteBrainDeployInfo.Clusters).
			UniqBy(func(c *shared.ClusterInfo) string {
				return c.Name
			}).
			ToSlice(&ret)
	} else if svc == DetectionByteBrain {
		_ = fp.StreamOf(a.state.DetectionByteBrainDeployInfo.Clusters).
			UniqBy(func(c *shared.ClusterInfo) string {
				return c.Name
			}).
			ToSlice(&ret)
	} else if svc == dbwtransit {
		_ = fp.StreamOf(a.state.FullSqlDeployInfo.Clusters).
			UniqBy(func(c *shared.ClusterInfo) string {
				return c.Name
			}).
			ToSlice(&ret)
	}
	return
}

func (a *DeployActor) getConfigDeployInfo(ctx types.Context, svc service) []*shared.ClusterInfo {
	var configClusters []*shared.ClusterInfo
	var deployInfo []*shared.ClusterInfo
	if svc == dbw {
		deployInfo = a.cnf.Get(ctx).DeployInfo
	} else if svc == bytebrain {
		deployInfo = a.cnf.Get(ctx).ByteBrainDeployInfo
	} else if svc == DetectionByteBrain {
		deployInfo = a.cnf.Get(ctx).AbnormalDetectionByteBrainDeployInfo
	} else if svc == dbwtransit {
		deployInfo = a.cnf.Get(ctx).FullSqlDeployInfo
	}
	fp.StreamOf(deployInfo).
		UniqBy(func(c *shared.ClusterInfo) string {
			return c.Name
		}).
		ToSlice(&configClusters)
	return configClusters
}

func (a *DeployActor) deployToCluster(ctx types.Context, cluster *shared.ClusterInfo, svc service) error {
	if svc == dbw {
		return a.DbwDeploy.Deploy(ctx, cluster)
	} else if svc == bytebrain {
		return a.ByteBrainDeploy.Deploy(ctx, cluster)
	} else if svc == DetectionByteBrain {
		return a.DetectionByteBrainDeploy.Deploy(ctx, cluster)
	} else if svc == dbwtransit {
		return a.FullSqlDeploy.Deploy(ctx, cluster)
	}
	return nil
}

func (a *DeployActor) removeDeployFromCluster(ctx types.Context, cluster *shared.ClusterInfo, svc service) error {
	if svc == dbw {
		return a.DbwDeploy.Undeploy(ctx, cluster)
	} else if svc == bytebrain {
		return a.ByteBrainDeploy.Undeploy(ctx, cluster)
	} else if svc == DetectionByteBrain {
		return a.DetectionByteBrainDeploy.Undeploy(ctx, cluster)
	} else if svc == dbwtransit {
		return a.FullSqlDeploy.Undeploy(ctx, cluster)
	}
	return nil
}
