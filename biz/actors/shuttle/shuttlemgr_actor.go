package shuttle

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/robfig/cron/v3"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"go.uber.org/dig"
)

type NewShuttleMgrActorIn struct {
	dig.In
	Cnf config.ConfigProvider
}

func NewShuttleMgrActor(p NewShuttleMgrActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.ShuttleMgrActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &shuttleMgrActor{
				cnf:  p.Cnf,
				cron: cron.New(),
			}
			json.Unmarshal(state, &actor.state)
			return actor
		}),
	}
}

type shuttleMgrActor struct {
	cnf   config.ConfigProvider
	state NodePoolInfo
	cron  *cron.Cron
}

type NodePoolInfo struct {
	Pools             map[string]*PoolInfo `json:"pools"`
	Vpcs              []string             `json:"vpcs"`
	ActorVpc          map[string]string    `json:"actor_vpc"`
	ActorStartTime    map[string]int64     `json:"actor_start_time"`
	IdlePeriodSeconds uint64               `json:"idle_period_seconds"`
	TimeoutSeconds    uint64               `json:"timeout_second"`
}

func (state *NodePoolInfo) resetIdlePeriod() {
	state.IdlePeriodSeconds = 0
}

func (state *NodePoolInfo) increaseIdlePeriod(dur uint64) {
	state.IdlePeriodSeconds += dur
}

func (state *NodePoolInfo) isIdleTimeout() bool {
	return state.TimeoutSeconds > 0
}

type PoolInfo struct {
	RegisteredActor map[string]string `json:"registered_actor"`
	RefCount        map[string]int    `json:"ref_count"`
}

func (s *shuttleMgrActor) GetState() []byte {
	state, _ := json.Marshal(s.state)
	return state
}

const (
	timeIntervalSeconds uint64 = 3600
)

func (s *shuttleMgrActor) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		s.init(ctx, msg)
	case *shared.RegisterActor:
		s.registerActor(ctx, msg)
	case *shared.UnregisterActor:
		s.unregisterActor(ctx, msg)
	case *shared.GetVpcNodeInfo:
		s.getVpcNodeInfo(ctx, msg)
	case *actor.ReceiveTimeout:
		s.doWhenIdle(ctx)
		ctx.SetReceiveTimeout(time.Duration(timeIntervalSeconds) * time.Second)
	case *shared.PingActorResp:
		s.handleActorResp(ctx, msg)
	}
}

func (s *shuttleMgrActor) init(ctx types.Context, msg *actor.Started) {
	if s.state.Pools == nil {
		s.state.Pools = make(map[string]*PoolInfo)
	}
	// FIXME:作为非状态成员？
	if s.state.ActorVpc == nil {
		s.state.ActorVpc = make(map[string]string)
		for vpc, pool := range s.state.Pools {
			for actor := range pool.RegisteredActor {
				s.state.ActorVpc[actor] = vpc
			}
		}
	}
	if s.state.ActorStartTime == nil {
		s.state.ActorStartTime = make(map[string]int64)
		for _, pool := range s.state.Pools {
			for actor := range pool.RegisteredActor {
				s.state.ActorStartTime[actor] = time.Now().Unix()
			}
		}
	}
	s.state.Vpcs = make([]string, 0, len(s.state.Pools))
	for vpc := range s.state.Pools {
		s.state.Vpcs = append(s.state.Vpcs, vpc)
	}
	s.resetTimeoutSeconds(ctx)

	//开启自动任务，每小时执行一次
	s.cron = cron.New(cron.WithSeconds())
	s.cron.Start()
	_, err := s.cron.AddFunc("@hourly", func() {
		ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
	})
	log.Info(ctx, "Add ShuttleMgrActor doWhenIdle cron task finished!")
	if err != nil {
		log.Warn(ctx, "doWhenIdle cron task error, err is: %v", err)
		return
	}
}

func (s *shuttleMgrActor) registerActor(ctx types.Context, msg *shared.RegisterActor) {
	//首先先根据vpcID去查询是否已经有注册过的数据，没有就先初始化一个pool
	pool, ok := s.state.Pools[msg.VpcId]
	if !ok || pool == nil {
		log.Info(ctx, "New VpcID: %s message from IP [%s] Kind [%s] Name [%s]", msg.VpcId, msg.Ip, msg.Kind, msg.Name)
		s.state.Vpcs = append(s.state.Vpcs, msg.VpcId)
		pool = &PoolInfo{
			RegisteredActor: make(map[string]string),
			RefCount:        make(map[string]int),
		}
		s.state.Pools[msg.VpcId] = pool
	}

	var nodeChanged = false
	id := s.getActorID(ctx, msg.Kind, msg.Name)
	log.Info(ctx, "Before Register [%s]: RegisteredActor: %v, RefCount: %v", id, pool.RegisteredActor, pool.RefCount)
	s.state.ActorStartTime[id] = time.Now().Unix()

	//通过sessionID获取当前已经注册的registered_actor的信息
	//1、如果获取到且IP相同，说明已经注册过，注册过就不用再注册了。
	//2、如果获取到但IP不同，说明当前session发生了更新，需要重新注册（先删除掉旧的）。
	ip, ok := pool.RegisteredActor[id]
	if ok {
		if ip == msg.Ip {
			log.Info(ctx, "Actor[%s/%s] already registered with identical ip %s", msg.Kind, msg.Name, ip)
			return
		}
		log.Info(ctx, "Actor[%s/%s] already registered with ip %s, update to new ip %s", msg.Kind, msg.Name, ip, msg.Ip)
		nodeChanged = decreaseRefCount(pool.RefCount, ip)
	}

	//重新注册shuttle的信息
	s.onRegisterActor(pool, id, msg)
	//将shuttle的计数器向上累加，如果发现这个IP是第一次注册，那么nodeChanged就是true，需要去做节点创建的动作
	if changed := increaseRefCount(pool.RefCount, msg.Ip); changed {
		nodeChanged = changed
	}

	log.Info(ctx, "After Register: [%s]: RegisteredActor: %v, RefCount: %v", id, pool.RegisteredActor, pool.RefCount)
	log.Info(ctx, "nodeChanged is %v", nodeChanged)
	if nodeChanged {
		err := ctx.ClientOf(consts.ShuttleActorKind).Send(ctx, msg.VpcId, &shared.NodeChanged{})
		if err != nil {
			log.Error(ctx, "nodeChanged failed! err is : %v", err)
			s.onUnregisterActor(pool, id)
			return
		}
	}
}

func (s *shuttleMgrActor) unregisterActor(ctx types.Context, msg *shared.UnregisterActor) {
	pool, ok := s.state.Pools[msg.VpcId]
	if !ok {
		log.Warn(ctx, "Invalid VpcID: %s message from IP [%s] Kind [%s] Name [%s]", msg.VpcId, msg.Ip, msg.Kind, msg.Name)
		return
	}

	var nodeChanged bool = false
	id := s.getActorID(ctx, msg.Kind, msg.Name)

	log.Info(ctx, "Before UnRegister [%s]: RegisteredActor: %v, RefCount: %v", id, pool.RegisteredActor, pool.RefCount)

	ip, ok := pool.RegisteredActor[id]
	if !ok {
		log.Warn(ctx, "Actor[%s/%s] didn't register before", msg.Kind, msg.Name)
		return
	}
	if ip != msg.Ip {
		log.Warn(ctx, "Actor[%s/%s] before ip [%s] != unregistered ip [%s]", msg.Kind, msg.Name, ip, msg.Ip)
	}
	// 这里用ip，而不用msg.Ip
	nodeChanged = decreaseRefCount(pool.RefCount, ip)
	s.onUnregisterActor(pool, id)

	if len(pool.RegisteredActor) == 0 {
		delete(s.state.Pools, msg.VpcId)
		s.deleteVpcInfo(id, msg.VpcId)
	}

	log.Info(ctx, " UnRegister finished! RefCount: %v", pool.RefCount)

	if nodeChanged {
		ctx.ClientOf(consts.ShuttleActorKind).
			Send(ctx, msg.VpcId, &shared.NodeChanged{})
	}
}

// getVpcNodeInfo 获取当前Vpc下注册的所有IP信息
func (s *shuttleMgrActor) getVpcNodeInfo(ctx types.Context, msg *shared.GetVpcNodeInfo) {
	pool, ok := s.state.Pools[msg.VpcId]
	if !ok {
		log.Warn(ctx, "Empty VpcID %s for getVpcNodeInfo message", msg.VpcId)
		ctx.Respond(&shared.VpcNodeInfo{
			Info: []string{},
		})
		return
	}
	// 这里将shuttleMgrActor里面某个VPC下面的所有Node,打包到一个slice中
	nodes := make([]string, 0, len(pool.RefCount))
	for ip, cnt := range pool.RefCount {
		if cnt > 0 && ip != "" {
			nodes = append(nodes, ip)
		}
	}
	ctx.Respond(&shared.VpcNodeInfo{
		Info: nodes,
	})
}

func decreaseRefCount(refCount map[string]int, ip string) bool {
	var nodeChanged bool = false
	cnt := refCount[ip]
	cnt--
	refCount[ip] = cnt
	if cnt == 0 {
		nodeChanged = true
		delete(refCount, ip)
	}
	return nodeChanged
}

func increaseRefCount(refCount map[string]int, ip string) bool {
	var nodeChanged bool = false
	cnt, ok := refCount[ip]
	if !ok || cnt == 0 {
		nodeChanged = true
	}
	refCount[ip]++
	return nodeChanged
}

func (s *shuttleMgrActor) deleteVpcInfo(id string, vpcId string) {
	var index int
	nVpc := len(s.state.Vpcs)
	for index = 0; index < nVpc; index++ {
		if s.state.Vpcs[index] == vpcId {
			break
		}
	}
	if index >= 0 && index < nVpc {
		s.state.Vpcs[index], s.state.Vpcs[nVpc-1] = s.state.Vpcs[nVpc-1], s.state.Vpcs[index]
		s.state.Vpcs = s.state.Vpcs[:nVpc-1]
	}
}

func (s *shuttleMgrActor) doInspection(ctx types.Context) {
	log.Info(ctx, "Do Self Inspection for vpc %v", s.state.Vpcs)
	for i := range s.state.Vpcs {
		// TODO: 每次ping一个vpc
		vpc := s.state.Vpcs[i]
		actors := s.state.Pools[vpc].RegisteredActor
		for id := range actors {
			kind, name := s.splitActorID(id)
			s.PingActor(ctx, kind, name)
		}
	}
}

func (s *shuttleMgrActor) doInspectionExpired(ctx types.Context) {
	log.Info(ctx, "Do Self Inspection for Expired Actor")
	now := time.Now().Unix()
	expiredSecs := s.cnf.Get(ctx).ShuttleExpiredSeconds
	for id, lastTime := range s.state.ActorStartTime {
		if existsTime := (now - lastTime); existsTime > expiredSecs {
			log.Info(ctx, "Actor %s exist %d secs > cnf.ShuttleExpiredSeconds %d", id, existsTime, expiredSecs)
			vpc := s.state.ActorVpc[id]
			kind, name := s.splitActorID(id)
			s.unregisterActor(ctx, &shared.UnregisterActor{
				VpcId: vpc,
				Ip:    "",
				Kind:  kind,
				Name:  name,
			})
		}
	}
}

func (s *shuttleMgrActor) PingActor(ctx types.Context, kind string, name string) {
	// FIXME: 目前仅支持Session kind
	if kind == consts.SessionActorKind {
		log.Info(ctx, "Ping Actor [%s/%s]", kind, name)
		// ignore err
		ctx.ClientOf(kind).Send(ctx, name, &shuttle.PingActor{})
	}
}

func (s *shuttleMgrActor) handleActorResp(ctx types.Context, msg *shared.PingActorResp) {
	id := s.getActorID(ctx, msg.Kind, msg.Name)
	log.Info(ctx, "handle actor %s resp from %#v", id, msg)
	if msg.VpcId == "" {
		vpc, ok := s.state.ActorVpc[id]
		if !ok {
			log.Warn(ctx, "Invalid Actor %s ActorVpc %v", id, s.state.ActorVpc)
			return
		}
		s.unregisterActor(ctx, &shared.UnregisterActor{
			VpcId: vpc,
			Ip:    "",
			Kind:  msg.Kind,
			Name:  msg.Name,
		})
	}
}

func (s *shuttleMgrActor) getActorID(ctx types.Context, kind string, name string) string {
	return fmt.Sprintf("%s/%s", kind, name)
}

func (s *shuttleMgrActor) splitActorID(id string) (string, string) {
	tokens := strings.Split(id, "/")
	return tokens[0], tokens[1]
}

func (s *shuttleMgrActor) resetIdlePeriod(ctx types.Context) {
	/* clear idle period */
	switch ctx.Message().(type) {
	case *actor.ReceiveTimeout:
	default:
		s.state.resetIdlePeriod()
		s.resetTimeoutSeconds(ctx)
	}
}

func (s *shuttleMgrActor) doWhenIdle(ctx types.Context) {
	log.Info(ctx, "doWhenIdle start!")
	s.doInspectionExpired(ctx)
}

func (s *shuttleMgrActor) resetTimeoutSeconds(ctx types.Context) {
	s.state.TimeoutSeconds = s.cnf.Get(ctx).ShuttleMgrTimeoutSeconds
}

func (s *shuttleMgrActor) onRegisterActor(pool *PoolInfo, id string, msg *shared.RegisterActor) {
	pool.RegisteredActor[id] = msg.Ip
	s.state.ActorVpc[id] = msg.VpcId
}

func (s *shuttleMgrActor) onUnregisterActor(pool *PoolInfo, id string) {
	delete(pool.RegisteredActor, id)
	delete(s.state.ActorVpc, id)
	delete(s.state.ActorStartTime, id)
}
