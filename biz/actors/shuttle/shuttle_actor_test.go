package shuttle

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"go.uber.org/dig"
	"testing"

	"code.byted.org/gopkg/mockito"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type ShuttleActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *ShuttleActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *ShuttleActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestCleanActorSuite(t *testing.T) {
	suite.Run(t, new(ShuttleActorSuite))
}

func (suite *ShuttleActorSuite) TestGetState() {
	shuttleActor := shuttleActor{state: TunnelInfo{"xx", "xx", nil}}
	ret := shuttleActor.GetState()
	suite.NotEmpty(ret)
}

func (suite *ShuttleActorSuite) TestNewShuttleActor() {
	ret := NewShuttleActor(NewShuttleActorIn{
		In: dig.In{},
		Db: nil,
	})
	suite.NotEmpty(ret)
}

func (suite *ShuttleActorSuite) TestDescribeShuttleServer() {
	// 模拟actor.context
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()

	mockito.PatchConvey("normal", suite.T(), func() {
		myShuttleV2Provider := mocks.NewMockPGWShuttleV2Provider(suite.ctrl)
		//myShuttleActor := NewShuttleActor(NewShuttleActorIn{
		//		ShuttleV2: myShuttleV2Provider,
		//})
		myShuttleActor := &shuttleActor{
			shuttleV2: myShuttleV2Provider,
		}
		myShuttleV2Provider.EXPECT().DescribeShuttleServers(gomock.Any(), gomock.Any()).Return(&shuttle.DescribeShuttleServersRespV2{
			ShuttleServers: []*entity.ShuttleServer{
				{
					VpcID: "xx",
					IP:    "xxx",
					Port:  3717,
				},
				{
					VpcID: "xx",
					IP:    "xxx",
					Port:  3717,
				},
			}}, nil).AnyTimes()
		_shuttle := &entity.Shuttle{
			ID: "",
			Servers: []*entity.ShuttleServer{
				{
					VpcID: "xx",
					IP:    "xxx",
					Port:  3717,
				},
				{
					VpcID: "xx",
					IP:    "xxx",
					Port:  3717,
				},
			},
		}
		msg := &shared.OpenTunnel{
			ServerIp:   "xxx",
			ServerPort: 3717,
			Vpc:        "xx",
		}
		server, err := myShuttleActor.describeShuttleServer(Ctx, _shuttle, msg)
		suite.NotEmpty(server)
		suite.Empty(err)
	})
}
