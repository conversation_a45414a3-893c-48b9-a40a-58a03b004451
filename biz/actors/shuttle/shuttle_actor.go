package shuttle

import (
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

type NewShuttleActorIn struct {
	dig.In
	Cnf         config.ConfigProvider
	ShuttleV2   shuttle.PGWShuttleV2Provider
	ShuttleRepo repository.ShuttleRepo
	Db          dal.DBProvider
}

func NewShuttleActor(p NewShuttleActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.ShuttleActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &shuttleActor{
				cnf:         p.Cnf,
				shuttleV2:   p.ShuttleV2,
				shuttleRepo: p.ShuttleRepo,
				db:          p.Db,
			}
			json.Unmarshal(state, &actor.state)
			return actor
		}),
	}
}

type TunnelInfo struct {
	TenantID  string  `json:"tenant"`
	VpcID     string  `json:"vpc"`
	ShuttleID *string `json:"shuttle_id"`
}

type shuttleActor struct {
	cnf         config.ConfigProvider
	shuttleV2   shuttle.PGWShuttleV2Provider
	shuttleRepo repository.ShuttleRepo
	state       TunnelInfo
	db          dal.DBProvider
}

func (a *shuttleActor) GetState() []byte {
	state, _ := json.Marshal(a.state)
	return state
}

func (a *shuttleActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(time.Hour)
	switch msg := ctx.Message().(type) {
	// TODO: 定期清除Shuttle实例，非常重要!!!
	case *shared.OpenTunnel:
		a.openTunnel(ctx, msg)
		ctx.SetReceiveTimeout(time.Minute)
	case *shared.CloseTunnel:
		a.closeTunnel(ctx, msg)
		ctx.SetReceiveTimeout(time.Minute)
	case *shared.NodeChanged:
		a.refreshNodeIPListV2(ctx, msg)
		ctx.SetReceiveTimeout(time.Minute)
	case *shared.DeleteShuttleV2Manual:
		a.deleteShuttleV2Manual(ctx, msg)
	case *actor.ReceiveTimeout:
		a.doSelfInspectionV2(ctx)
		defer ctx.SetReceiveTimeout(time.Hour)
	}
}

func (a *shuttleActor) openTunnel(ctx types.Context, msg *shared.OpenTunnel) {
	a.openTunnelV2(ctx, msg)
}

func (a *shuttleActor) openTunnelV2(ctx types.Context, msg *shared.OpenTunnel) {
	var err error
	shuttle_ := &entity.Shuttle{}
	defer func() {
		a.responseToOpenTunnel(ctx, msg, shuttle_, err)
	}()

	if err = a.ensureState(ctx, msg.Tenant, msg.Vpc); err != nil {
		return
	}
	// 这一步生成shuttleID，并入库shuttle信息，其中client和server都是空，后面填充
	shuttleID, err := a.getShuttleID(ctx)
	if err != nil {
		log.Warn(ctx, "openTunnelV2: get shuttle id failed: %s", err.Error())
		return
	}
	log.Info(ctx, "get shuttle ID [%s]", shuttleID)
	// 从元数据库去查shuttle信息
	resp, err := a.shuttleRepo.Get(ctx, a.db.GetMetaDB(ctx), shuttleID)
	if err != nil {
		log.Warn(ctx, "get shuttle(%s) from db failed: %v", shuttleID, err)
		return
	}
	if resp == nil {
		log.Warn(ctx, "shuttle(%s) is not exists in db", shuttleID)
		return
	}
	shuttle_.ID = resp.ID
	shuttle_.Type = resp.Type
	shuttle_.Name = resp.Name
	shuttle_.Description = resp.Description
	shuttle_.AccountID = resp.AccountID
	shuttle_.Clients = resp.Clients
	shuttle_.Servers = resp.Servers

	err = a.refreshShuttleClients(ctx, shuttle_)
	if err != nil {
		log.Warn(ctx, "refresh shuttle(%s) clients failed: %v", shuttleID, err)
		return
	}

	err = a.refreshShuttleServers(ctx, shuttle_, msg)
	if err != nil {
		log.Warn(ctx, "refresh shuttle(%s) servers failed: %v", shuttleID, err)
		return
	}

	err = a.shuttleRepo.Save(ctx, a.db.GetMetaDB(ctx), shuttle_)
}

// refreshShuttleClients 确定是否创建shuttle client
func (a *shuttleActor) refreshShuttleClients(ctx types.Context, _shuttle *entity.Shuttle) error {
	// 1、从数据库dbw_shuttle里面，获取clientIP列表
	// 这里的_shuttle,是根据shuttle_id从dbw元数据库去拿的
	if _shuttle == nil {
		return fmt.Errorf("shuttle is nil")
	}
	var clientIPList = make([]string, 0)
	fp.StreamOf(_shuttle.Clients).Map(func(client *entity.ShuttleClient) string {
		return client.HostIP
	}).Uniq().ToSlice(&clientIPList)

	// 2、从shuttleMgrActor的state里面去拿注册过的clientIp
	resp, err := ctx.ClientOf(consts.ShuttleMgrActorKind).Call(ctx, consts.SingletonActorName, &shared.GetVpcNodeInfo{
		VpcId: a.state.VpcID,
	})
	if err != nil {
		return fmt.Errorf("get node pool info failed: %v", err)
	}
	nodePool, ok := resp.(*shared.VpcNodeInfo)
	if !ok || nodePool == nil {
		return fmt.Errorf("no nodes found")
	}
	var nodeIPList = nodePool.Info

	// 3、根据上面2个结果,这里进行判断
	// 当前Shuttle的Client IP(元数据dbw_shuttle)和当前VPC下所有IP(从shuttleMgrActor的state里面获取)做对比，以后者为准。
	// addedIP是shuttleMgrActor有，元数据dbw_shuttle没有的，需要补充
	// deletedIP是shuttleMgrActor没有，元数据dbw_shuttle有的，需要删除
	addedIP := fp.StreamOf(nodeIPList).Sub(fp.StreamOf(clientIPList)).Strings()
	deletedIP := fp.StreamOf(clientIPList).Sub(fp.StreamOf(nodeIPList)).Strings()
	log.Info(ctx, "nodeIPList = %v", nodeIPList)
	log.Info(ctx, "clientIPList = %v", clientIPList)
	log.Info(ctx, "addIp = %v", addedIP)
	log.Info(ctx, "deletedIp = %v", deletedIP)

	// 4、对于增加的节点，创建client
	for _, ip := range addedIP {
		resp, err := a.shuttleV2.CreateShuttleClient(ctx, &shuttle.CreateShuttleClientReq{
			ShuttleID: *a.state.ShuttleID,
			HostIP:    ip,
		})
		if err != nil {
			if strings.Contains(err.Error(), `ErrShuttleClientRepeated`) {
				log.Info(ctx, "already created client: %s", ip)
			} else {
				log.Info(ctx, "create shuttle client error: %v", err)
				return err
			}
		}
		if resp != nil {
			_shuttle.Clients = append(_shuttle.Clients, &entity.ShuttleClient{
				ID:        resp.ShuttleClientID,
				HostIP:    resp.HostIP,
				VpcID:     resp.VpcID,
				BackendIP: resp.BackendIP,
			})
		}
	}

	// 5、对于删除的节点，删除client，删除失败不作处理，在删除shuttle时一并清理
	for _, ip := range deletedIP {
		_, err := a.shuttleV2.DeleteShuttleClient(ctx, &shuttle.DeleteShuttleClientReq{
			ShuttleID: *a.state.ShuttleID,
			HostIP:    ip,
		})
		if err != nil {
			log.Warn(ctx, "delete shuttle(%s) client(%s) failed: %v", *a.state.ShuttleID, ip, err)
		}
	}
	// 6、更新数据库。把dbw_actor表_shuttle.Clients中被deletedIP包含的client,全部清理掉
	fp.StreamOf(_shuttle.Clients).Reject(func(client *entity.ShuttleClient) bool {
		return fp.StreamOf(deletedIP).Contains(client.HostIP)
	}).ToSlice(&_shuttle.Clients)

	return a.shuttleRepo.Save(ctx, a.db.GetMetaDB(ctx), _shuttle)
}

func (a *shuttleActor) addShuttleClient(ctx types.Context, _shuttle *entity.Shuttle, hostIP string) error {
	resp, err := a.shuttleV2.CreateShuttleClient(ctx, &shuttle.CreateShuttleClientReq{
		ShuttleID: *a.state.ShuttleID,
		HostIP:    hostIP,
	})
	if err != nil {
		if strings.Contains(err.Error(), `ErrShuttleClientRepeated`) {
			log.Info(ctx, "already created client: %s for %s", hostIP, *a.state.ShuttleID)
			clients, descErr := a.shuttleV2.DescribeShuttleClients(ctx, &shuttle.DescribeShuttleClientsReq{
				ShuttleID: *a.state.ShuttleID,
				HostIP:    hostIP,
			})
			if descErr != nil {
				return descErr
			}
			if len(clients.ShuttleClients) == 0 {
				return fmt.Errorf("describe client %s response is empty", hostIP)
			}
			resp = &shuttle.CreateShuttleClientResp{
				ShuttleID:       *a.state.ShuttleID,
				ShuttleClientID: clients.ShuttleClients[0].ID,
				HostIP:          clients.ShuttleClients[0].HostIP,
				BackendIP:       clients.ShuttleClients[0].BackendIP,
			}
		} else {
			return err
		}
	}
	_shuttle.Clients = append(_shuttle.Clients, &entity.ShuttleClient{
		ID:        resp.ShuttleClientID,
		HostIP:    resp.HostIP,
		VpcID:     resp.VpcID,
		BackendIP: resp.BackendIP,
	})
	return nil
}

func (a *shuttleActor) refreshShuttleServers(ctx types.Context, _shuttle *entity.Shuttle, msg *shared.OpenTunnel) error {
	// update task ref and add shuttle server
	var serverMap map[string][]*entity.ShuttleServer
	// 元数据
	fp.StreamOf(_shuttle.Servers).GroupBy(func(server *entity.ShuttleServer) string {
		return a.endpoint(server.IP, server.Port)
	}).To(&serverMap)
	/* 这里考虑2个条件：
	   1、传入的私网server ip(从cacheMongo获取)是否在元数据库中；
	   2、调用shuttle接口，查看对应的shuttle server是否存在；
	   如果1、2都成立，当前shuttle server的引用+1；
	   否则，创建shuttle server */
	server, ok := serverMap[a.endpoint(msg.ServerIp, msg.ServerPort)]
	shuttleServerList, err := a.describeShuttleServer(ctx, _shuttle, msg)
	if err != nil {
		log.Warn(ctx, "describeShuttleServer error: %s ", err.Error())
	}
	if ok && len(shuttleServerList) > 0 {
		log.Info(ctx, "cache server %s,port %s is in meta", msg.ServerIp, msg.ServerPort)
		if len(server) > 1 {
			fp.StreamOf(server).Filter(func(s *entity.ShuttleServer) bool {
				return s.ID == shuttleServerList[0].ID
			}).ToSlice(&server) // 这里拿到dbw数据库里和接口返回的shuttle server相同的那个shuttle server
		}
		server[0].AddTaskRef(msg.TunnelId, msg.Ttl)
	} else {
		err := a.addShuttleServer(ctx, _shuttle, msg, msg.ServerIp, msg.ServerPort)
		if err != nil {
			return err
		}
		log.Info(ctx, "cache server %s,port %s is not in meta,addShuttleServer success", msg.ServerIp, msg.ServerPort)
	}

	return nil
}

func (a *shuttleActor) describeShuttleServer(ctx types.Context, _shuttle *entity.Shuttle, msg *shared.OpenTunnel) ([]*entity.ShuttleServer, error) {
	resp, err := a.shuttleV2.DescribeShuttleServers(ctx, &shuttle.DescribeShuttleServersReqV2{
		ShuttleID: _shuttle.ID,
		VpcID:     msg.Vpc,
		IP:        msg.ServerIp,
		Port:      msg.ServerPort,
	})
	// logs.Info(ctx, "cesar: addShuttleServer: resp: %v", resp)
	if err != nil {
		log.Warn(ctx, "describe Shuttle Servers failed: %s", err.Error())
		return nil, err
	}
	// 循环返回结果，查看是否有对应的shuttle
	var ret []*entity.ShuttleServer
	for _, value := range resp.ShuttleServers {
		if value.IP == msg.ServerIp && value.Port == msg.ServerPort && value.VpcID == msg.Vpc {
			log.Info(ctx, "Exist Shuttle Server:%v ,Vpc:%s,Ip:%s,Port:%s", value, value.VpcID, value.IP, value.Port)
			ret = append(ret, value)
		}
	}
	return ret, nil
}

func (a *shuttleActor) addShuttleServer(ctx types.Context, _shuttle *entity.Shuttle, msg *shared.OpenTunnel, ip string, port uint64) error {
	var shuttleServer *entity.ShuttleServer
	resp, err := a.shuttleV2.CreateShuttleServer(ctx, &shuttle.CreateShuttleServerReq{
		ShuttleID: _shuttle.ID,
		VpcID:     msg.Vpc,
		IP:        ip,
		Port:      port,
	})
	// logs.Info(ctx, "cesar: addShuttleServer: resp: %v", resp)
	if err != nil {
		if strings.Contains(err.Error(), `ErrShuttleServerRepeated`) {
			log.Info(ctx, "already created shuttle server: %s %s:%d", msg.Vpc, ip, port)
			server, err := a.shuttleV2.DescribeShuttleServers(ctx, &shuttle.DescribeShuttleServersReqV2{
				ShuttleID: _shuttle.ID,
				VpcID:     msg.Vpc,
				IP:        ip,
				Port:      port,
			})
			if err != nil {
				return nil
			}
			if server.ShuttleServers == nil {
				return fmt.Errorf("shuttle(%s) server(%s %s:%d)", _shuttle.ID, msg.Vpc, ip, port)
			}
			shuttleServer = server.ShuttleServers[0]
		} else {
			return err
		}
	} else {
		shuttleServer = &entity.ShuttleServer{
			ID:           resp.ShuttleServerID,
			VpcID:        resp.VpcID,
			IP:           resp.IP,
			Port:         resp.Port,
			ShuttleVIP:   resp.ShuttleVIP,
			ShuttleVPort: resp.ShuttleVport,
		}
	}

	shuttleServer.AddTaskRef(msg.TunnelId, msg.Ttl)
	_shuttle.Servers = append(_shuttle.Servers, shuttleServer)
	return nil
}

func (a *shuttleActor) closeTunnel(ctx types.Context, msg *shared.CloseTunnel) {
	var err error
	defer func() {
		if ctx.Sender() != nil {
			if err != nil {
				ctx.Respond(&shared.CloseTunnelResult{Success: false, Reason: err.Error()})
			} else {
				ctx.Respond(&shared.CloseTunnelResult{Success: true})
			}
		}
	}()

	err = a._closeTunnelV2(ctx, msg)
}

func (a *shuttleActor) _closeTunnelV2(ctx types.Context, msg *shared.CloseTunnel) error {
	if err := a.ensureState(ctx, msg.Tenant, msg.Vpc); err != nil {
		return err
	}
	if a.state.ShuttleID == nil {
		return nil
	}
	_shuttle, err := a.shuttleRepo.Get(ctx, a.db.GetMetaDB(ctx), *a.state.ShuttleID)
	if err != nil {
		return err
	}
	if _shuttle == nil {
		return fmt.Errorf("shuttle is nil")
	}

	// 获取当前VPC对应的所有Shuttle Servers列表
	// 对其进行遍历，找到与当前TunnelID，也就是SessionID相关的Server
	// 对当前会话相关的Tunnel更新其TaskRef数组
	// 如果TaskRef数组为0，表示客户端已全部关闭
	// 则对该Shuttle Server执行删除操作
	fp.StreamOf(_shuttle.Servers).Filter(func(server *entity.ShuttleServer) bool {
		return fp.StreamOf(server.TaskRef).Map(func(ref *entity.TaskRef) string {
			return ref.ID
		}).Contains(msg.TunnelId)
	}).Foreach(func(server *entity.ShuttleServer) {
		fp.StreamOf(server.TaskRef).Reject(func(ref *entity.TaskRef) bool {
			return ref.ID == msg.TunnelId
		}).ToSlice(&server.TaskRef)

		if len(server.TaskRef) == 0 {
			_, err := a.shuttleV2.DeleteShuttleServer(ctx, &shuttle.DeleteShuttleServerReq{
				ShuttleID: _shuttle.ID,
				VpcID:     msg.Vpc,
				IP:        server.IP,
				Port:      server.Port,
			})
			if err != nil {
				log.Warn(ctx, "delete shuttle(%s) server(%s:%d) failed: %v", _shuttle.ID, server.IP, server.Port, err)
			}
		}
	}).Run()

	fp.StreamOf(_shuttle.Servers).Reject(func(server *entity.ShuttleServer) bool {
		return len(server.TaskRef) == 0
	}).ToSlice(&_shuttle.Servers)

	err = a.shuttleRepo.Save(ctx, a.db.GetMetaDB(ctx), _shuttle)
	if err != nil {
		return err
	}
	return nil
}

func (a *shuttleActor) deleteShuttleClient(ctx types.Context, _shuttle *entity.Shuttle, msg *shared.CloseTunnel) error {
	// NOTE: delete shuttle server 后 shuttle status 进入不可用状态，此时不能删除shuttle client
	deletedIp := make(map[string]bool)
	log.Info(ctx, "delete shuttle client for host ips: %v", msg.HostsIp)
	for _, hostIp := range msg.HostsIp {
		_, err := a.shuttleV2.DeleteShuttleClient(ctx, &shuttle.DeleteShuttleClientReq{
			HostIP:    hostIp,
			ShuttleID: *a.state.ShuttleID,
		})
		if err != nil {
			log.Warn(ctx, "delete shuttle(%s) client(%s) failed: %v", *a.state.ShuttleID, hostIp, err)
		} else {
			deletedIp[hostIp] = true
		}
	}
	clientsIpList := []*entity.ShuttleClient{}
	for _, cli := range _shuttle.Clients {
		if !deletedIp[cli.HostIP] {
			clientsIpList = append(clientsIpList, cli)
		}
	}
	failedIps := make([]string, 0, len(clientsIpList))
	for i := range clientsIpList {
		failedIps = append(failedIps, clientsIpList[i].HostIP)
	}
	msg.HostsIp = failedIps
	_shuttle.Clients = clientsIpList
	return nil
}

func (a *shuttleActor) responseToOpenTunnel(ctx types.Context, msg *shared.OpenTunnel, shuttle_ *entity.Shuttle, err error) {
	if ctx.Sender() == nil {
		return
	}

	resp := &shared.OpenTunnelResult{Success: true}
	// 这里不通过数据库去取了
	// if shuttle_ != nil {
	//	fp.StreamOf(shuttle_.Servers).Filter(func(server *entity.ShuttleServer) bool {
	//		return fp.StreamOf(server.TaskRef).Map(func(ref *entity.TaskRef) string {
	//			return ref.ID
	//		}).Contains(msg.TunnelId)
	//	}).Filter(func(server *entity.ShuttleServer) bool {
	//		// 需要匹配ip和port，避免把任务的其他shuttle信息给返回了
	//		// for _, v := range msg.Src {
	//		// 	if v.Ip == server.IP && v.Port == server.Port {
	//		// 		return true
	//		// 	}
	//		// }
	//		if msg.ServerIp == server.IP && msg.ServerPort == server.Port {
	//			return true
	//		}
	//		return false
	//	}).Map(func(server *entity.ShuttleServer) *shared.TunnelInfo {
	//		return &shared.TunnelInfo{
	//			Ip:    server.IP,
	//			Port:  server.Port,
	//			Vip:   server.ShuttleVIP,
	//			Vport: server.ShuttleVPort,
	//		}
	//	}).ToSlice(&resp.Tunnels)
	//}
	// 改为通过真正的shuttle server接口结果去取
	server, err := a.describeShuttleServer(ctx, shuttle_, msg)
	if err != nil {
		return
	}
	fp.StreamOf(server).Map(func(server *entity.ShuttleServer) *shared.TunnelInfo {
		return &shared.TunnelInfo{
			Ip:    server.IP,
			Port:  server.Port,
			Vip:   server.ShuttleVIP,
			Vport: server.ShuttleVPort,
		}
	}).ToSlice(&resp.Tunnels)
	if err != nil {
		resp.Success = false
		log.Warn(ctx, "open tunnel error: %s", err.Error())
		resp.Reason = err.Error()
	}

	log.Info(ctx, "open tunnel response: %s", resp)
	ctx.Respond(resp)
}

func (a *shuttleActor) ensureState(ctx types.Context, tenantID string, vpc string) error {
	if a.state.VpcID == "" {
		a.state.TenantID = tenantID
		a.state.VpcID = vpc
		return nil
	}
	if a.state.TenantID != tenantID || a.state.VpcID != vpc {
		log.Warn(ctx, "bad tunnel state want(tenant=%v,vpc=%v) got(tenant=%v,vpc=%v)", a.state.TenantID, a.state.VpcID, tenantID, vpc)

		return fmt.Errorf("bad tunnel state want(tenant=%v,vpc=%v) got(tenant=%v,vpc=%v)", a.state.TenantID, a.state.VpcID, tenantID, vpc)
	}
	return nil
}

// getShuttleID 创建shuttle，返回shuttleID，并将shuttle信息保存在元数据库里面
func (a *shuttleActor) getShuttleID(ctx types.Context) (string, error) {
	if a.state.ShuttleID == nil {
		shuttleID, err := a.createShuttleInstance(ctx)
		if err != nil {
			return "", err
		}
		log.Info(ctx, "create shuttle success, shuttle id:%s", shuttleID)
		a.state.ShuttleID = &shuttleID
	}
	return *a.state.ShuttleID, nil
}

func (a *shuttleActor) createShuttleInstance(ctx types.Context) (string, error) {
	resp, err := a.shuttleV2.CreateShuttle(ctx, &shuttle.CreateShuttleReq{
		Type:        "underlay",
		Name:        "dbw_shuttle",
		Description: "dbw_shuttle",
	})
	if err != nil {
		return "", err
	}
	// 入库
	err = a.shuttleRepo.Create(ctx, a.db.GetMetaDB(ctx), &entity.Shuttle{
		ID:      resp.ShuttleID,
		Name:    resp.Name,
		Type:    resp.Type,
		Clients: nil,
		Servers: nil,
	})
	if err != nil {
		a.shuttleV2.DeleteShuttle(ctx, &shuttle.DeleteShuttleReq{ShuttleID: resp.ShuttleID})
		return "", err
	}

	return resp.ShuttleID, nil
}

func (a *shuttleActor) deleteShuttleV2Manual(ctx types.Context, msg *shared.DeleteShuttleV2Manual) {
	if len(msg.ShuttleId) == 0 {
		if a.state.ShuttleID == nil {
			log.Warn(ctx, "shuttle id is nil")
			return
		}
		msg.ShuttleId = *a.state.ShuttleID
	}

	err := a.deleteShuttleInstance(ctx, msg.ShuttleId)
	if err != nil {
		log.Warn(ctx, "delete shuttle(%s) failed: %v", msg.ShuttleId, err)
	}

	var shuttle_ *entity.Shuttle
	resp, err := a.shuttleRepo.Get(ctx, a.db.GetMetaDB(ctx), msg.ShuttleId)
	if err != nil {
		log.Warn(ctx, "get shuttle(%s) from db failed: %v", msg.ShuttleId, err)
		return
	}
	if resp == nil {
		log.Warn(ctx, "shuttle(%s) is not exists in db", msg.ShuttleId)
		return
	}
	shuttle_.ID = resp.ID
	shuttle_.Type = resp.Type
	shuttle_.Name = resp.Name
	shuttle_.Description = resp.Description
	shuttle_.AccountID = resp.AccountID
	shuttle_.Clients = []*entity.ShuttleClient{}
	shuttle_.Servers = []*entity.ShuttleServer{}

	err = a.shuttleRepo.Save(ctx, a.db.GetMetaDB(ctx), shuttle_)

	if a.state.ShuttleID != nil && msg.ShuttleId == *a.state.ShuttleID {
		a.state.ShuttleID = nil
	}
}

func (a *shuttleActor) deleteShuttleInstance(ctx types.Context, shuttleID string) error {
	// cleanup clients
	clients, err := a.shuttleV2.DescribeShuttleClients(ctx, &shuttle.DescribeShuttleClientsReq{
		ShuttleID: shuttleID,
	})
	if err != nil {
		log.Warn(ctx, "describe shuttle(%s) clients failed:%v", shuttleID, err)
	} else {
		fp.StreamOf(clients.ShuttleClients).Foreach(func(client *entity.ShuttleClient) {
			_, err := a.shuttleV2.DeleteShuttleClient(ctx, &shuttle.DeleteShuttleClientReq{
				ShuttleID: shuttleID,
				HostIP:    client.HostIP,
			})
			if err != nil {
				log.Warn(ctx, "delete shuttle(%s) client(%s) failed: %v", shuttleID, client.HostIP, err)
			}
		}).Run()
	}

	// cleanup servers
	servers, err := a.shuttleV2.DescribeShuttleServers(ctx, &shuttle.DescribeShuttleServersReqV2{
		ShuttleID: shuttleID,
	})
	if err != nil {
		log.Warn(ctx, "describe shuttle(%s) servers failed:%v", shuttleID, err)
	} else {
		fp.StreamOf(servers.ShuttleServers).Foreach(func(server *entity.ShuttleServer) {
			_, err := a.shuttleV2.DeleteShuttleServer(ctx, &shuttle.DeleteShuttleServerReq{
				ShuttleID: shuttleID,
				VpcID:     server.VpcID,
				IP:        server.IP,
				Port:      server.Port,
			})
			if err != nil {
				log.Warn(ctx, "delete shuttle(%s) server(%s, %s:%s) failed: %v",
					shuttleID, server.VpcID, server.IP, server.Port, err)
			}
		}).Run()
	}

	_, err = a.shuttleV2.DeleteShuttle(ctx, &shuttle.DeleteShuttleReq{
		ShuttleID: shuttleID,
	})
	if err != nil {
		log.Warn(ctx, "delete shuttle(%s) failed:%v", shuttleID, err)
		return err
	}
	log.Info(ctx, "delete shuttle(%s) success", shuttleID)
	return nil
}

func (a *shuttleActor) refreshNodeIPListV2(ctx types.Context, msg *shared.NodeChanged) error {
	if a.state.ShuttleID == nil {
		return nil
	}

	_shuttle, err := a.shuttleRepo.Get(ctx, a.db.GetMetaDB(ctx), *a.state.ShuttleID)
	if err != nil {
		log.Warn(ctx, "get shuttle(%s) failed", *a.state.ShuttleID)
		return err
	}

	err = a.refreshShuttleClients(ctx, _shuttle)
	if err != nil {
		log.Warn(ctx, "refresh shuttle(%s) client failed: %v", *a.state.ShuttleID, err)
		return err
	}

	log.Info(ctx, "refresh node ip list: %s", utils.Show(_shuttle.Clients))
	return nil
}

func (a *shuttleActor) endpoint(ip string, port uint64) string {
	return fmt.Sprintf("%s:%d", ip, port)
}
func (a *shuttleActor) doSelfInspectionV2(ctx types.Context) {
	if a.state.ShuttleID == nil {
		return
	}

	_shuttle, err := a.shuttleRepo.Get(ctx, a.db.GetMetaDB(ctx), *a.state.ShuttleID)
	if err != nil {
		log.Warn(ctx, "get shuttle(%s) failed: %v", *a.state.ShuttleID, err)
		return
	}

	if _shuttle == nil || len(_shuttle.Servers) == 0 || len(_shuttle.Clients) == 0 {
		err = a.deleteShuttleInstance(ctx, *a.state.ShuttleID)
		if err == nil {
			a.state.ShuttleID = nil
			_shuttle.Servers = []*entity.ShuttleServer{}
			_shuttle.Clients = []*entity.ShuttleClient{}
		} else {
			log.Info(ctx, "delete shuttle(%s) failed: %v", *a.state.ShuttleID, err)
		}
		return
	}

	a.inspectClients(ctx, _shuttle)

	a.shuttleRepo.Save(ctx, a.db.GetMetaDB(ctx), _shuttle)
}

func (a *shuttleActor) inspectClients(ctx types.Context, _shuttle *entity.Shuttle) {
	resp, err := a.shuttleV2.DescribeShuttleClients(ctx, &shuttle.DescribeShuttleClientsReq{
		ShuttleID: *a.state.ShuttleID,
	})
	if err != nil {
		log.Warn(ctx, "describe shuttle(%s) clients failed:%v", *a.state.ShuttleID, err)
		return
	}
	clients := resp.ShuttleClients

	/* rebuild lost clients */
	fp.StreamOf(_shuttle.Clients).Filter(func(client *entity.ShuttleClient) bool {
		return fp.StreamOf(clients).ContainsBy(func(c *entity.ShuttleClient) bool {
			return c.VpcID == client.VpcID && c.HostIP == client.HostIP
		})
	}).ToSlice(&_shuttle.Clients)

	fp.StreamOf(_shuttle.Clients).SubBy(
		fp.StreamOf(clients).UniqBy(func(client *entity.ShuttleClient) string {
			return client.VpcID + client.HostIP
		}),
		func(client *entity.ShuttleClient) string {
			return client.VpcID + client.HostIP
		},
	).Foreach(func(client *entity.ShuttleClient) {
		log.Info(ctx, "rebuild shuttle client %s", client.HostIP)
		resp, err := a.shuttleV2.CreateShuttleClient(ctx, &shuttle.CreateShuttleClientReq{
			ShuttleID: *a.state.ShuttleID,
			HostIP:    client.HostIP,
		})
		if err != nil {
			log.Warn(ctx, "rebuild shuttle client failed: %v", err)
			return
		}

		_shuttle.Clients = append(_shuttle.Clients, &entity.ShuttleClient{
			ID:        resp.ShuttleClientID,
			HostIP:    resp.HostIP,
			VpcID:     resp.VpcID,
			BackendIP: resp.BackendIP,
		})
	}).Run()
}
