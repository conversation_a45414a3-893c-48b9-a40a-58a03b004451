package dialog

import (
	"encoding/json"
	"fmt"
	"runtime/debug"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/protoactor-go/actor"

	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

var collectionIntervalSeconds int64 = 30

type NewDialogMgrActorIn struct {
	dig.In
	C3Config c3.ConfigProvider
	Source   datasource.DataSourceService
	Location location.Location
	Cnf      config.ConfigProvider
}

func NewDialogMgrActor(p NewDialogMgrActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.DialogMgrActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &DialogMgrActor{
				c3Conf: p.C3Config,
				source: p.Source,
				loc:    p.Location,
				state:  newSessionState(state),
				cnf:    p.Cnf,
			}
		}),
	}
}

type DialogMgrActor struct {
	c3Conf c3.ConfigProvider
	source datasource.DataSourceService
	loc    location.Location
	state  DialogMgrState
	cnf    config.ConfigProvider
}

func (d *DialogMgrActor) GetState() []byte {
	state, _ := json.Marshal(d.state)
	return state
}

type DialogMgrState struct {
	LastCollectTime int64
}

func newSessionState(bytes []byte) DialogMgrState {
	state := DialogMgrState{}
	json.Unmarshal(bytes, &state)
	if state.LastCollectTime == 0 {
		state.LastCollectTime = time.Now().Unix()
	}
	return state
}

func (d *DialogMgrActor) Process(actorCtx types.Context) {
	defer func() {
		// now := time.Now().Unix()
		// realInterval := fp.MaxInt64(collectionIntervalSeconds-(now-d.state.LastCollectTime), 1)
		actorCtx.SetReceiveTimeout(time.Second * time.Duration(collectionIntervalSeconds))
	}()

	switch msg := actorCtx.Message().(type) {
	case *actor.Started:
		log.Info(actorCtx, "DialogMgrActor Started")
		d.Initialization(actorCtx)
	case *actor.Stopping:
		return
	case *actor.Stopped:
		return
	case *actor.Restarting:
		return
	case *shared.AreYouOK:
		d.onPingActor(actorCtx, msg)
	case *actor.ReceiveTimeout:
		d.protectUserCall(actorCtx, func() {
			log.Info(actorCtx, "DialogMgrActor timeout")
			d.state.LastCollectTime = time.Now().Unix()
			d.getInstanceSnapShots(actorCtx)
		})
	}
}

func (d *DialogMgrActor) onPingActor(actorCtx types.Context, msg *shared.AreYouOK) {
	log.Info(actorCtx, "receive AreYouOK message")
	actorCtx.Respond(&shared.OK{})
}

func (d *DialogMgrActor) getDataSourceTypes(ctx types.Context) []model.DSType {
	return []model.DSType{model.DSType_MySQL}
}

func (d *DialogMgrActor) getInstanceSnapShots(ctx types.Context) {
	cfg := d.cnf.Get(ctx)
	if cfg.DiaLogStartSwitch == false {
		log.Info(ctx, "dialog task stop! DiaLogStartSwitch is false")
		return
	}

	dsTypes := d.getDataSourceTypes(ctx)

	// iterate all database types
	for _, dsType := range dsTypes {
		// get tenants
		tenantInfos, err := d.listTenants(ctx, 0, 1)
		if err != nil {
			break
		}
		tenantsEverySeconds := tenantInfos.Total/10 + 1

		var offset, limit, cnt int64 = 0, tenantsEverySeconds, 0
		for {
			tenantInfos, err = d.listTenants(ctx, offset, limit)
			if err != nil {
				break
			}
			for _, tenant := range tenantInfos.Tenants {
				d.collectDialogSnapshot(ctx, tenant.TenantID, dsType)
			}
			cnt += int64(len(tenantInfos.Tenants))
			if cnt >= tenantInfos.Total || int64(len(tenantInfos.Tenants)) < limit {
				break
			}
			offset += limit
			time.Sleep(time.Second)
		}
	}
}

func (d *DialogMgrActor) collectDialogSnapshot(ctx types.Context, tenantID string, dsType model.DSType) {
	actorName := d.getDialogActorName(tenantID, dsType)
	d.setTenantId(ctx, tenantID)
	ctx.ClientOf(consts.DialogActorKind).Send(ctx, actorName, &shared.CollectDialogSnapshot{
		DsType:    conv.ToSharedType(dsType),
		Timestamp: time.Now().Unix(),
	})
	return
}

func (d *DialogMgrActor) getDialogActorName(tenantID string, dsType model.DSType) string {
	return fmt.Sprintf("%s_%s", tenantID, dsType.String())
}
func (d *DialogMgrActor) listTenants(ctx types.Context, offset int64, limit int64) (*shared.TenantInfos, error) {
	resp, err := ctx.ClientOf(consts.TenantMgrKind).Call(ctx, consts.SingletonActorName, &shared.ListTenants{
		Offset:   offset,
		Limit:    limit,
		HasState: true,
		State:    shared.TenantState_Agree,
	})
	if err != nil {
		log.Warn(ctx, "failed to call ListTenants, err=%v", err)
		return nil, err
	}
	switch rsp := resp.(type) {
	case *shared.TenantInfos:
		return rsp, nil
	case *shared.ListTenantsFailed:
		log.Warn(ctx, "failed to ListTenants, err=%v", err)
	default:
		log.Warn(ctx, "unknown response of ListTenants, err=%v", err)
	}
	return nil, consts.ErrorWithParam(model.ErrorCode_InternalError, "failed to get tenant info,please retry later ")
}

func (d *DialogMgrActor) setTenantId(ctx types.Context, tenantId string) {
	ctx.WithValue(fwctx.BIZ_CONTEXT_KEY, fwctx.NewBizContext())
	if c := fwctx.GetBizContext(ctx); c != nil {
		c.TenantID = tenantId
	}
}

func (d *DialogMgrActor) Initialization(ctx types.Context) {
}

func (d *DialogMgrActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, "panic, err=%v, stack=%s", r, string(debug.Stack()))
		}
	}()
	fn()
}
