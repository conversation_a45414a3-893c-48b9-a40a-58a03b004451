package dialog

import (
	"encoding/json"
	"fmt"
	"github.com/qjpcpu/fp"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"
	"github.com/volcengine/volc-sdk-golang/service/tls/producer"
	"go.uber.org/dig"
	"math/rand"
	"os"
	"reflect"
	"regexp"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
)

// GetSessionTimeoutSeconds 获取session的超时时间
var GetSessionTimeoutSeconds int64 = 30

var queryTimeoutSeconds int64 = 65
var getSessionRetryCount = 2

type NewDialogActorIn struct {
	dig.In
	Config       config.ConfigProvider
	C3Config     c3.ConfigProvider
	Source       datasource.DataSourceService
	CrossAuthSvc crossauth.CrossServiceAuthorizationService
	Location     location.Location
}

func init() {
	rand.Seed(time.Now().UnixNano())
}

func NewDialogActor(p NewDialogActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.DialogActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &DialogActor{
				cnf:            p.Config,
				c3Conf:         p.C3Config,
				source:         p.Source,
				loc:            p.Location,
				crossAuthSvc:   p.CrossAuthSvc,
				delayedSuicide: false,
			}
		}),
	}
}

type DialogActor struct {
	cnf                  config.ConfigProvider
	c3Conf               c3.ConfigProvider
	source               datasource.DataSourceService
	loc                  location.Location
	tenantID             string
	crossAuthSvc         crossauth.CrossServiceAuthorizationService
	tlsDialogDetailTopic string
	tlsEngineStatusTopic string
	delayedSuicide       bool // 延迟删除标记
	callTimeoutSeconds   int64
	internalUsers        []string // 内部用户，需要过滤
	internalIPs          []string // 内部ip，需要过滤
}

func (d *DialogActor) Process(actorCtx types.Context) {
	interval := time.Minute * 10
	defer actorCtx.SetReceiveTimeout(interval)

	switch msg := actorCtx.Message().(type) {
	case *actor.Started:
		log.Info(actorCtx, "DialogActor Started")
		d.initialization(actorCtx)
	case *actor.Stopping:
		return
	case *actor.Stopped:
		return
	case *actor.Restarting:
		return
	case *shared.CollectDialogSnapshot:
		d.protectUserCall(actorCtx, func() {
			d.collectDialogSnapshot(actorCtx, msg)
		})
	case *actor.ReceiveTimeout:
		log.Info(actorCtx, "timeout")
		d.delayedSuicide = true
	}
	d.checkStatus(actorCtx)
}

func (d *DialogActor) collectDialogSnapshot(ctx types.Context, msg *shared.CollectDialogSnapshot) {
	// 丢弃积累的过时请求
	if time.Now().Unix()-msg.Timestamp >= collectionIntervalSeconds {
		return
	}
	d.tenantID = fwctx.GetTenantID(ctx)

	instances, err := d.listInstances(ctx, fwctx.GetTenantID(ctx), msg.DsType)
	if err != nil {
		return
	}

	d.queryInstances(ctx, msg.DsType, instances)
	log.Info(ctx, "collect tenant %s's instance dialog snapshot finished", d.tenantID)
}

func (d *DialogActor) listInstances(ctx types.Context, tenantID string, dsType shared.DataSourceType) ([]*model.InstanceInfo, error) {
	var instances []*model.InstanceInfo
	var pageNumber, pageSize, cnt int32 = 1, 300, 0
	query := &datasource.ListInstanceReq{
		Type:           dsType,
		LinkType:       shared.Volc,
		PageNumber:     pageNumber,
		PageSize:       pageSize,
		InstanceStatus: "Running",
		RegionId:       os.Getenv(`BDC_REGION_ID`),
		TenantId:       tenantID,
	}
	for {
		query.PageNumber = pageNumber
		query.PageSize = pageSize
		resp, err := d.source.ListInstance(ctx, query)
		if err != nil {
			log.Warn(ctx, "failed to list instance, err=%v", err)
			break
		}
		instances = append(instances, resp.InstanceList...)
		cnt += int32(len(resp.InstanceList))
		if cnt >= int32(resp.Total) || int32(len(resp.InstanceList)) < pageSize {
			break
		}
		pageNumber += 1
	}
	return instances, nil
}

func (d *DialogActor) queryInstances(ctx types.Context, dsType shared.DataSourceType, instances []*model.InstanceInfo) {
	if instances == nil || len(instances) == 0 {
		return
	}
	var dbInternalUsers map[string]string
	cpuUsageMap, err := handler.GetInstancesLatestCpuUsage(ctx, conv.ToModelType(dsType), instances, os.Getenv(`BDC_REGION_ID`),
		d.tenantID, d.cnf, d.crossAuthSvc, model.Component_DBEngine.String(), "", nil)
	if err != nil {
		log.Warn(ctx, "failed to get instance cpu usage, tenant=%s, err=%v", d.tenantID, err)
		// if tenant does not exist or dbw not authorized，mark tenant disagree
		reg := regexp.MustCompile("role/ServiceRoleForDbw.*does not exist")
		if reg.MatchString(err.Error()) {
			//ctx.ClientOf(consts.TenantMgrKind).Send(ctx, consts.SingletonActorName, &shared.RemoveTenant{
			//	TenantId: d.tenantID,
			//})
			//d.delayedSuicide = true
			return
		}
	}
	cfg := d.cnf.Get(ctx)
	internalUsers := make([]string, 0)
	err = json.Unmarshal([]byte(cfg.DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
	} else {
		internalUsers = strings.Split(dbInternalUsers[dsType.String()], ",")
	}
	d.internalUsers = internalUsers
	d.internalIPs = strings.Split(cfg.InternalIPs, ",")

	var wg sync.WaitGroup
	concurrency := d.getConcurrency(len(instances))
	ch := make(chan struct{}, concurrency)
	finish := make(chan struct{})
	for i := 0; i < len(instances); i++ {
		wg.Add(1)
		ch <- struct{}{} // occupy a slot
		go func(i int) {
			defer func() {
				if r := recover(); r != nil {
					log.Warn(ctx, "collect instance snapshot panic %v %s", r, string(debug.Stack()))
				}
				wg.Done()
				<-ch
			}()
			time.Sleep(time.Duration(rand.Intn(500)) * time.Millisecond)
			d.collectInstanceSnapshot(ctx, instances[i], dsType, cpuUsageMap[instances[i].GetInstanceId()])
		}(i)
	}
	go func() {
		wg.Wait()
		finish <- struct{}{}
	}()
	select {
	case <-time.After(time.Second * time.Duration(queryTimeoutSeconds+GetSessionTimeoutSeconds*int64(getSessionRetryCount)+10)):
		log.Warn(ctx, "query instances timeout, tenantId=%s", d.tenantID)
	case <-finish:
	}
	return
}

func (d *DialogActor) getConcurrency(instNum int) int64 {
	return int64(instNum/4 + 1)
}

func (d *DialogActor) collectInstanceSnapshot(ctx types.Context, inst *model.InstanceInfo, dsType shared.DataSourceType, cpuUsage float64) {
	start := time.Now()
	snapshot, err := d.queryDataSource(ctx, dsType, inst)
	if err != nil {
		log.Warn(ctx, " failed to query instance %s, err=%v", inst.GetInstanceId(), err)
		return
	}
	if err = d.putToTLS(ctx, snapshot, inst.GetInstanceId(), fmt.Sprintf("%.6f", cpuUsage)); err != nil {
		log.Warn(ctx, " failed to put snapshot to tls", "instanceID",
			inst.GetInstanceId(), "err", err)
	}
	log.Info(ctx, "query instance %s finish, cost %v ms", inst.GetInstanceId(), time.Since(start).Milliseconds())
}

type DialogLog struct {
	TenantId    string
	InstanceId  string
	CollectTime string
	ProcessId   string
	User        string
	Host        string
	DB          string
	Command     string
	Time        string
	State       string
	Info        string
	BlockingPid string
}

type EngineStatusLog struct {
	TenantId    string
	InstanceId  string
	CollectTime string
	Type        string
	Name        string
	Status      *string // 指针，减少拷贝
	CpuUsage    string
	Index       int64 // log的索引，多个log组成一个整体(status过长会分片)
}

type DialogSnapshot struct {
	dialogDetails []*shared.DialogDetail
	engineStatus  *shared.EngineStatus
}

func (d *DialogActor) queryDataSource(ctx types.Context, dsType shared.DataSourceType, inst *model.InstanceInfo,
) (*DialogSnapshot, error) {
	sessionId, err := d.getInstanceSession(ctx, inst.GetInstanceId(), conv.ToModelType(dsType))
	if err != nil {
		log.Warn(ctx, " failed to GetInstanceSession, instanceId=%s, err=%v", inst.GetInstanceId(), err)
		return nil, err
	}
	defer d.giveBackInstanceSession(ctx, inst.GetInstanceId(), sessionId)

	// call session actor
	callOpts := cli.GrainCallOptions{
		RetryCount:  1,
		Timeout:     time.Second * time.Duration(queryTimeoutSeconds),
		RetryAction: func(i int) {},
	}
	resp, err := ctx.ClientOf(consts.SessionActorKind).
		Call(ctx, sessionId, &shared.DescribeDialogDetails{
			PageSize:      40000,
			PageNumber:    1,
			InternalUsers: d.internalUsers,
			InternalIPs:   d.internalIPs,
		}, &callOpts)
	if err != nil {
		log.Warn(ctx, " failed to describe dialog details, sessionId=%s, err=%v", sessionId, err)
		return nil, err
	}

	var details []*shared.DialogDetail
	switch rsp := resp.(type) {
	case *shared.DialogDetails:
		details = rsp.Details
	default:
		err = consts.ErrorOf(model.ErrorCode_DataSourceOpFail)
		log.Warn(ctx, " describe dialog details fail %#v, sessionId=%s", resp, sessionId)
		return nil, err
	}

	// call session actor
	resp, err = ctx.ClientOf(consts.SessionActorKind).
		Call(ctx, sessionId, &shared.DescribeEngineStatus{}, &callOpts)
	if err != nil {
		log.Warn(ctx, " failed to call describe engine status, sessionId=%s, err=%v", sessionId, err)
		return nil, err
	}

	var status *shared.EngineStatus
	switch rsp := resp.(type) {
	case *shared.EngineStatus:
		status = rsp
	default:
		err = consts.ErrorOf(model.ErrorCode_DataSourceOpFail)
		log.Warn(ctx, " describe engine status fail %#v, sessionId=%s", resp, sessionId)
		return nil, err
	}

	ret := &DialogSnapshot{
		dialogDetails: details,
		engineStatus:  status,
	}
	return ret, nil
}

func (d *DialogActor) getInstanceSession(ctx types.Context, instanceId string, dsType model.DSType) (string, error) {
	opts := cli.GrainCallOptions{
		RetryCount: getSessionRetryCount,
		Timeout:    time.Second * time.Duration(GetSessionTimeoutSeconds),
		RetryAction: func(i int) {
			log.Warn(ctx, "call GetSession timeout, retry")
		},
	}
	resp, err := ctx.ClientOf(consts.SessionMgrActorKind).Call(ctx, instanceId, &shared.GetSession{
		DSType:     conv.ToSharedType(dsType),
		LinkType:   shared.Volc,
		InstanceId: instanceId,
	}, &opts)
	if err != nil {
		log.Warn(ctx, "failed to get session, err=%v", err)
		return "", err
	}
	switch rsp := resp.(type) {
	case *shared.SessionInfo:
		log.Debug(ctx, "succeed to get session, sessionId=%s", rsp.GetSessionId())
		return rsp.GetSessionId(), nil
	case *shared.GetSessionFailed:
		err = consts.ErrorWithParam(model.ErrorCode_DataSourceOpFail, resp)
		log.Warn(ctx, "failed to get session, resp=%#v", resp)
	default:
		err = consts.ErrorOf(model.ErrorCode_DataSourceOpFail)
		log.Warn(ctx, "unknown response of GetSession, resp=%#v", resp)
	}
	return "", err
}

func (d *DialogActor) giveBackInstanceSession(ctx types.Context, instanceId string, sessionId string) {
	if err := ctx.ClientOf(consts.SessionMgrActorKind).
		Send(ctx, instanceId, &shared.GiveBackSession{
			InstanceId: instanceId,
			SessionId:  sessionId,
		}); err != nil {
		log.Warn(ctx, " failed to give back session, instanceId=%s, sessionId=%s, err=%v",
			instanceId, sessionId, err)
	}
}

func (d *DialogActor) putToTLS(ctx types.Context, snapshot *DialogSnapshot, instanceID string, cpuUsage string) error {
	now := time.Now().Unix()
	tenantId := fwctx.GetTenantID(ctx)
	// add Tenant、 InstanceId and CollectTime
	var dialogLogs []*DialogLog
	fp.StreamOf(snapshot.dialogDetails).Map(func(detail *shared.DialogDetail) *DialogLog {
		return &DialogLog{
			TenantId:    tenantId,
			InstanceId:  instanceID,
			CollectTime: fmt.Sprint(now),
			ProcessId:   detail.ProcessID,
			User:        detail.User,
			Host:        detail.Host,
			DB:          detail.DB,
			Command:     detail.Command,
			Time:        detail.Time,
			State:       detail.State,
			Info:        detail.Info[:fp.MinInt(consts.TLSSingleFieldMaxSize, len(detail.Info))],
			BlockingPid: detail.BlockingPid,
		}
	}).ToSlice(&dialogLogs)

	// put to tls
	var detailLogs []*pb.Log
	fp.StreamOf(dialogLogs).Map(func(info *DialogLog) *pb.Log {
		return d.DialogLogToLog(ctx, info, now)
	}).ToSlice(&detailLogs)
	if err := sendLogs(ctx, d.tlsDialogDetailTopic, detailLogs); err != nil {
		log.Warn(ctx, "failed to put dialog detail to TLS, err=%v", err)
	}

	// slice status if too large
	var statusLogs []*pb.Log
	var statuses []*EngineStatusLog
	for i := 0; i*consts.TLSSingleFieldMaxSize < len(snapshot.engineStatus.Status); i++ {
		beg := i * consts.TLSSingleFieldMaxSize
		end := utils.MinInt((i+1)*consts.TLSSingleFieldMaxSize, len(snapshot.engineStatus.Status))
		partStatus := snapshot.engineStatus.Status[beg:end]
		statuses = append(statuses, &EngineStatusLog{
			TenantId:    tenantId,
			InstanceId:  instanceID,
			CollectTime: fmt.Sprint(now),
			Type:        snapshot.engineStatus.Type,
			Name:        snapshot.engineStatus.Name,
			Status:      &partStatus,
			CpuUsage:    cpuUsage,
			Index:       int64(i),
		})
	}
	fp.StreamOf(statuses).Map(func(statusLog *EngineStatusLog) *pb.Log {
		return d.EngineStatusLogToLog(ctx, statusLog, now)
	}).ToSlice(&statusLogs)
	if err := sendLogs(ctx, d.tlsEngineStatusTopic, statusLogs); err != nil {
		log.Warn(ctx, "failed to put engine status to TLS, err=%v", err)
	}
	return nil
}

func (d *DialogActor) EngineStatusLogToLog(ctx types.Context, statusLog *EngineStatusLog, collectTime int64) *pb.Log {
	if statusLog == nil {
		log.Warn(ctx, "failed to convert engine status log to log contents, detail is nil")
		return &pb.Log{}
	}
	res := &pb.Log{
		Time: collectTime,
	}

	typ := reflect.TypeOf(statusLog).Elem()
	val := reflect.ValueOf(statusLog).Elem()
	for i := 0; i < typ.NumField(); i++ {
		ft, fv := typ.Field(i), val.Field(i)
		res.Contents = append(res.Contents, &pb.LogContent{
			Key:   ft.Name,
			Value: d.interfaceToString(fv.Interface()),
		})
	}
	return res
}

func (d *DialogActor) DialogLogToLog(ctx types.Context, dialogInfo *DialogLog, collectTime int64) *pb.Log {
	if dialogInfo == nil {
		log.Warn(ctx, "failed to convert dialog detail log to log contents, detail is nil")
		return &pb.Log{}
	}
	res := &pb.Log{
		Time: collectTime,
	}

	typ := reflect.TypeOf(dialogInfo).Elem()
	val := reflect.ValueOf(dialogInfo).Elem()
	for i := 0; i < typ.NumField(); i++ {
		ft, fv := typ.Field(i), val.Field(i)
		res.Contents = append(res.Contents, &pb.LogContent{
			Key:   ft.Name,
			Value: d.interfaceToString(fv.Interface()),
		})
	}
	return res
}

func (d *DialogActor) interfaceToString(cell interface{}) string {
	if cell == nil {
		return "NULL"
	}
	val := reflect.ValueOf(cell)
	for val.Kind() == reflect.Ptr && !val.IsNil() {
		val = val.Elem()
	}
	if val.Kind() == reflect.Ptr && val.IsNil() {
		return "NULL"
	}
	return fmt.Sprint(val.Interface())
}

func (d *DialogActor) checkStatus(ctx types.Context) {
	if d.delayedSuicide {
		ctx.Stop(ctx.Self())
	}
}

// 已弃用
func (d *DialogActor) initialization(ctx types.Context) {
	once.Do(func() {
		// configure tls producer
		c3Cfg := d.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
		regionId := d.loc.RegionID()
		tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, regionId)
		producerCfg := producer.GetDefaultProducerConfig()
		producerCfg.Endpoint = tlsEndpoint
		producerCfg.Region = os.Getenv("BDC_REGION_ID")
		producerCfg.AccessKeyID = c3Cfg.TLSServiceAccessKey
		producerCfg.AccessKeySecret = c3Cfg.TLSServiceSecretKey
		tlsProducer = producer.NewProducer(producerCfg)
		tlsProducer.Start()
	})
	cfg := d.cnf.Get(ctx)
	d.tlsDialogDetailTopic = cfg.TLSDialogDetailTopic
	d.tlsEngineStatusTopic = cfg.TLSEngineStatusTopic
}

func (d *DialogActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " panic, err=%v, stack=%s", r, string(debug.Stack()))
		}
	}()
	fn()
}
