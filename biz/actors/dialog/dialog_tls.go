package dialog

import (
	"context"
	"fmt"
	"sync"

	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"
	"github.com/volcengine/volc-sdk-golang/service/tls/producer"
)

var (
	tlsProducer producer.Producer
	once        sync.Once
	callback    SendLogsCallback
)

func sendLogs(ctx context.Context, topicID string, logs []*pb.Log) error {
	if logs == nil || len(logs) == 0 {
		return nil
	}
	logGroup := &pb.LogGroup{
		Logs: logs,
	}
	if err := tlsProducer.SendLogs("", topicID, "", "", logGroup, callback); err != nil {
		log.Warn(ctx, "send log error, err=%v", err)
		return err
	}
	log.Debug(ctx, "send log succeed")
	return nil
}

type SendLogsCallback struct {
}

func (s SendLogsCallback) Success(result *producer.Result) {
}

func (s SendLogsCallback) Fail(result *producer.Result) {
	for _, att := range result.Attempts {
		log.WarnS(context.Background(), "send dialog snapshot to TLS error", "detail",
			fmt.Sprintf("%+v", att))
	}
}
