package sla

import (
	commonlog "code.byted.org/infcs/ds-lib/common/log"
	"context"
	log "github.com/sirupsen/logrus"
)

type JsonLoggerConfig struct {
	LogFilePath string
	KeepFiles   int
}

var logger *log.Logger

func BuildJsonLogger(conf JsonLoggerConfig) {
	logger = log.New()
	logger.SetFormatter(&log.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05.000",
	})
	if conf.LogFilePath != "" {
		if output, err := commonlog.NewFileRotateLog(&commonlog.FileRotateLogOptions{
			LogFilePath:    conf.LogFilePath,
			RotateDuration: commonlog.RotateHourly,
			KeepCount:      conf.KeepFiles,
		}); err != nil {
			panic(err)
		} else {
			logger.SetOutput(output)
		}
	}
}

func JsonLog(ctx context.Context, fields log.Fields, msg string) {
	logger.WithFields(fields).Log(log.InfoLevel, msg)
}
