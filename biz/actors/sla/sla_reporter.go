package sla

import (
	"code.byted.org/gopkg/lang/maths"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	sla_svc "code.byted.org/infcs/dbw-mgr/biz/service/sla"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"encoding/json"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
	"time"
)

type SLAAuditMetricReporterActorIn struct {
	dig.In

	InstDal dal.ObInstDAL
	SlaSvc  sla_svc.SlaSvc
	Conf    config.ConfigProvider
	Source  datasource.DataSourceService
}

type SLAAuditMetricReporterActor struct {
	state *TaskState

	InstDal dal.ObInstDAL
	SlaSvc  sla_svc.SlaSvc
	Conf    config.ConfigProvider
	Source  datasource.DataSourceService

	// 各实例 sla列表
	LastSlaMap map[string][]float32
	// 上次推送sla
	LastSlaPush map[string]float32
	// 当前 sla
	CurSlaMap map[string][]float32
	CurPeriod *int64
}

type TaskState struct{}

func NewSLAAuditMetricReporterActor(t SLAAuditMetricReporterActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.SLAAuditMetricReporterActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &SLAAuditMetricReporterActor{
				state:   newTaskState(state),
				InstDal: t.InstDal,
				SlaSvc:  t.SlaSvc,
				Conf:    t.Conf,
				Source:  t.Source,

				LastSlaPush: map[string]float32{},
				LastSlaMap:  map[string][]float32{},
				CurSlaMap:   map[string][]float32{},
				CurPeriod:   libutils.Int64Ref(time.Now().Truncate(5 * time.Minute).Unix()),
			}
			return actor
		}),
	}
}

func newTaskState(bytes []byte) *TaskState {
	ts := &TaskState{}
	json.Unmarshal(bytes, ts)
	return ts
}

func (s *SLAAuditMetricReporterActor) GetState() []byte {
	state, _ := json.Marshal(s.state)
	return state
}

func (s *SLAAuditMetricReporterActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(time.Duration(time.Second.Nanoseconds() * s.Conf.Get(ctx).SLACheckTimeInterval))
	switch ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "started SLAAuditMetricReporterActor")
		BuildJsonLogger(JsonLoggerConfig{
			LOG_FILE,
			DEFAULT_KEEP_COUNT,
		})
	case *actor.ReceiveTimeout:
		_ = s.Start(ctx)
	case *actor.Stopped:
		log.Error(ctx, "sla reporter stopped")
	}
}

const SLA_METRIC_KEY = "DBW_Availability"

func (s *SLAAuditMetricReporterActor) Start(ctx context.Context) error {
	var auditInstances []*dao.AuditTls
	a1, _ := s.AuditInstanceReporter(ctx)
	for _, a := range a1 {
		auditInstances = append(auditInstances, a)
	}
	a2, _ := s.FullSqlInstanceReporter(ctx)
	for _, a := range a2 {
		auditInstances = append(auditInstances, a)
	}
	curTime := time.Now().Truncate(5 * time.Minute).Unix()
	if *(s.CurPeriod) != curTime {
		log.Info(ctx, "output sla log, current period:%s current time:%s", *(s.CurPeriod), curTime)
		s.CurPeriod = libutils.Int64Ref(curTime)
		s.OutputSlaLog(ctx, auditInstances)
		s.LastSlaMap = s.CurSlaMap
		s.CurSlaMap = map[string][]float32{}
	}
	return nil
}

func (s *SLAAuditMetricReporterActor) FullSqlInstanceReporter(ctx context.Context) ([]*dao.AuditTls, error) {
	log.Info(ctx, "FullSqlInstanceReporter start SLA Metric Reporter")
	allAuditInstances, err := s.InstDal.GetAll(ctx, model.LogProductType_FullSqlLog.String())
	if err != nil {
		log.Warn(ctx, "fullsql GetAll err:%s", err)
		return nil, err
	}
	var auditInstances []*dao.AuditTls
	err = fp.StreamOf(allAuditInstances).Filter(func(auditInst *dao.AuditTls) bool {
		for _, bTenantId := range s.Conf.Get(ctx).SLATenantBlackList {
			if auditInst.TenantID == bTenantId {
				return false
			}
		}
		return true
	}).ToSlice(&auditInstances)
	if err != nil {
		log.Warn(ctx, "black list filter failed, err:%s", err)
		return auditInstances, err
	}
	for _, instance := range auditInstances {
		if instance.Status == int32(model.AuditStatus_Running) {
			dsType, _ := model.DSTypeFromString(instance.DbType)
			sla, err := s.SlaSvc.GetSqlInsightInstanceSLA(ctx, &entity.SlaEntity{
				AuditID:       instance.InstanceID,
				TenantID:      instance.TenantID,
				RDSInstanceID: instance.FollowInstanceID,
				Region:        instance.Region,
				DSType:        dsType,
			})
			if err != nil {
				log.Warn(ctx, "AuditDal GetAll err:%s", err)
				s.CurSlaMap[instance.InstanceID] = append(s.CurSlaMap[instance.InstanceID], 1)
			} else {
				s.CurSlaMap[instance.InstanceID] = append(s.CurSlaMap[instance.InstanceID], sla)
			}
		}
	}
	return auditInstances, nil
}

func (s *SLAAuditMetricReporterActor) AuditInstanceReporter(ctx context.Context) ([]*dao.AuditTls, error) {
	log.Info(ctx, "AuditInstanceReporter start SLA Metric Reporter")
	allAuditInstances, err := s.InstDal.GetAll(ctx, model.LogProductType_AuditLog.String())
	if err != nil {
		log.Warn(ctx, "AuditDal GetAll err:%s", err)
		return allAuditInstances, err
	}
	var auditInstances []*dao.AuditTls
	err = fp.StreamOf(allAuditInstances).Filter(func(auditInst *dao.AuditTls) bool {
		for _, bTenantId := range s.Conf.Get(ctx).SLATenantBlackList {
			if auditInst.TenantID == bTenantId {
				return false
			}
		}
		return true
	}).ToSlice(&auditInstances)
	if err != nil {
		log.Warn(ctx, "black list filter failed, err:%s", err)
		return auditInstances, err
	}
	for _, instance := range auditInstances {
		if instance.Status == int32(model.AuditStatus_Running) {
			dsType, _ := model.DSTypeFromString(instance.DbType)
			sla, err := s.SlaSvc.GetAuditInstanceSLA(ctx, &entity.SlaEntity{
				AuditID:       instance.InstanceID,
				TenantID:      instance.TenantID,
				RDSInstanceID: instance.FollowInstanceID,
				Region:        instance.Region,
				DSType:        dsType,
			})
			if err != nil {
				log.Warn(ctx, "AuditDal GetAll err:%s", err)
				s.CurSlaMap[instance.InstanceID] = append(s.CurSlaMap[instance.InstanceID], 1)
			} else {
				s.CurSlaMap[instance.InstanceID] = append(s.CurSlaMap[instance.InstanceID], sla)
			}
		}
	}
	return auditInstances, nil
}

func (s *SLAAuditMetricReporterActor) OutputSlaLog(ctx context.Context, instances []*dao.AuditTls) {
	for _, instance := range instances {
		o5 := s.Over5Unavailable(s.CurSlaMap[instance.InstanceID], s.LastSlaMap[instance.InstanceID], instance.InstanceID)
		if o5 {
			s.logSla(ctx, instance, float32(maths.AvgFloat32s(s.CurSlaMap[instance.InstanceID]...)))
		} else {
			s.logSla(ctx, instance, 1)
		}
	}
}

func (s *SLAAuditMetricReporterActor) logSla(ctx context.Context, instance *dao.AuditTls, sla float32) {
	if sla < 1 {
		dsType, _ := model.DSTypeFromString(instance.DbType)
		detail, err := s.Source.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{InstanceId: instance.FollowInstanceID,
			Type: shared.DataSourceType(dsType)})
		if err != nil {
			log.Info(ctx, "sla < 1, but DescribeDBInstanceDetail err:%s, InstanceID:%s", err, instance.InstanceID)
			s.reportSLA(ctx, instance.TenantID, instance.InstanceID, 1)
			return
		}
		if detail.InstanceStatus != model.InstanceStatus_Running.String() {
			log.Info(ctx, "sla < 1, but DescribeDBInstanceDetail InstanceID:%s, InstanceStatus:%s", instance.InstanceID, detail.InstanceStatus)
			s.reportSLA(ctx, instance.TenantID, instance.InstanceID, 1)
			return
		}
	}
	s.reportSLA(ctx, instance.TenantID, instance.InstanceID, sla)
}

func (s *SLAAuditMetricReporterActor) reportSLA(ctx context.Context, TenantID string, InstanceID string, sla float32) {
	fields := s.buildFields(ctx, *s.CurPeriod, TenantID, InstanceID, sla)
	JsonLog(ctx, fields, "SLA metric report")
}

// Over5Unavailable 超过五个连续不可用点返回true
func (s *SLAAuditMetricReporterActor) Over5Unavailable(curArr []float32, lastArr []float32, instanceId string) bool {
	if len(curArr) == 0 {
		return false
	}
	ctn := 0
	for i := len(curArr) - 1; i >= 0; i-- {
		if curArr[i] < 1 {
			ctn++
			if ctn >= 5 {
				return true
			}
		} else {
			ctn = 0
		}
	}
	if f, ok := s.LastSlaPush[instanceId]; ok && f == 1 {
		return false
	}
	if ctn == 0 || len(lastArr) == 0 {
		return false
	}
	for i := len(lastArr) - 1; i >= 0; i-- {
		if lastArr[i] >= 1 {
			return false
		}
		ctn++
		if ctn >= 5 {
			return true
		}
	}
	return false
}

func (s *SLAAuditMetricReporterActor) buildFields(ctx context.Context, sliTime int64, tenantId string, instanceId string, sli float32) map[string]interface{} {
	if sli > 1 || sli < 0 {
		log.Error(ctx, "sli time:%d, sli value : %f, tenantId: %s, instanceId:%s", sliTime, sli, tenantId, instanceId)
		sli = 1
	}
	s.LastSlaPush[instanceId] = sli

	fields := map[string]interface{}{}
	fields["account_id"] = tenantId
	fields["event_time"] = time.Now().Unix()
	fields["sli_time"] = sliTime
	fields["sli_key"] = SLA_METRIC_KEY
	fields["val"] = sli
	extra := map[string]interface{}{}
	extra["instance_id"] = instanceId
	fields["extra"] = extra
	if sli < 1 {
		log.Error(ctx, "low sla: %.2f ,fields:%+v, LastSlaMap:%s, CurSlaMap:%s", sli, fields, s.LastSlaMap[instanceId], s.CurSlaMap[instanceId])
	}
	return fields
}

const (
	LOG_CONSOLE = "console"
	LOG_FILE    = "/opt/tiger/dbwmgr/log/sla_audit"

	DEFAULT_KEEP_COUNT = 48
)

type LogConfig struct {
	Type        string            `json:"type"`
	Level       string            `json:"level,omitempty"`
	Config      json.RawMessage   `json:"config,omitempty"`
	SentryDSN   string            `json:"sentry_dsn,omitempty"`
	SentryProxy string            `json:"sentry_proxy,omitempty"`
	SentryTags  map[string]string `json:"sentry_tags,omitempty"`
}

type FileLogOptions struct {
	File      string   `json:"file"`
	KeepCount int      `json:"keep_count,omitempty"`
	Hooks     []string `json:"hooks,omitempty"`
	PSM       string   `json:"psm"`
}

func (self *FileLogOptions) GetKeepCount() int {
	if self.KeepCount < 1 {
		return DEFAULT_KEEP_COUNT
	}
	return self.KeepCount
}
