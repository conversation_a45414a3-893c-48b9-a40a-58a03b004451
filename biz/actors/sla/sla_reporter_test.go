package sla

import (
	ycnf "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/sla"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	mock_dal "code.byted.org/infcs/dbw-mgr/biz/test/mocks/dal"
	sla2 "code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/sla"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"errors"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"testing"
	"time"

	. "github.com/bytedance/mockey"
)

func TestSLAAuditMetricReporterActor_buildFields(t *testing.T) {
	type fields struct {
		state    *TaskState
		AuditDal dal.ObInstDAL
		SlaSvc   sla.SlaSvc
	}
	type args struct {
		ctx        context.Context
		sliTime    int64
		tenantId   string
		instanceId string
		sli        float32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   map[string]interface{}
	}{
		{
			name: "sla",
			fields: fields{
				state:    nil,
				AuditDal: nil,
				SlaSvc:   nil,
			},
			args: args{
				ctx:        context.Background(),
				sliTime:    time.Now().Truncate(5 * time.Minute).Unix(),
				tenantId:   "123",
				instanceId: "456",
				sli:        0.5,
			},
			want: nil,
		},
		{
			name: "sla",
			fields: fields{
				state:    nil,
				AuditDal: nil,
				SlaSvc:   nil,
			},
			args: args{
				ctx:        context.Background(),
				sliTime:    time.Now().Truncate(5 * time.Minute).Unix(),
				tenantId:   "123",
				instanceId: "456",
				sli:        0.5,
			},
			want: nil,
		},
	}

	BuildJsonLogger(JsonLoggerConfig{
		"./sla.log",
		DEFAULT_KEEP_COUNT,
	})
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := SLAAuditMetricReporterActor{
				state:   tt.fields.state,
				InstDal: tt.fields.AuditDal,
				SlaSvc:  tt.fields.SlaSvc,

				LastSlaPush: map[string]float32{},
			}
			got := s.buildFields(tt.args.ctx, tt.args.sliTime, tt.args.tenantId, tt.args.instanceId, tt.args.sli)
			if got["account_id"] == "123" &&
				got["sli_key"] == "DBW_Availability" &&
				got["val"] == float32(0.5) &&
				got["extra"].(map[string]interface{})["instance_id"] == "456" {

			} else {
				t.Errorf("buildFields() value not expect")
			}

			JsonLog(tt.args.ctx, got, "SLA metric report")
		})
	}
}

func TestOver5Unavailable(t *testing.T) {
	type args struct {
		curArr     []float32
		lastArr    []float32
		instanceId string
	}
	s := SLAAuditMetricReporterActor{
		CurPeriod: libutils.Int64Ref(time.Now().Truncate(5 * time.Minute).Unix()),
		LastSlaPush: map[string]float32{
			"a": 0,
			"b": 1,
		},
	}

	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "",
			args: args{
				curArr:     []float32{0.1, 0.1, 0.1, 1, 1},
				lastArr:    []float32{0.1, 1, 1, 0.1, 0.1},
				instanceId: "a",
			},
			want: true,
		},
		{
			name: "",
			args: args{
				curArr:     []float32{0.1, 0.1, 0.1, 1, 1},
				lastArr:    []float32{0.1, 1, 1, 0.1, 1},
				instanceId: "a",
			},
			want: false,
		},
		{
			name: "",
			args: args{
				curArr:     []float32{0.1, 0.1, 0.1, 0.1, 0.1},
				lastArr:    []float32{0.1, 1, 1, 0.1, 0.1},
				instanceId: "a",
			},
			want: true,
		},
		{
			name: "",
			args: args{
				curArr:     []float32{1, 1, 1, 1, 1},
				lastArr:    []float32{0.1, 0.1, 0.1, 0.1, 0.1},
				instanceId: "a",
			},
			want: false,
		},
		{
			name: "",
			args: args{
				curArr:     []float32{0.1, 0.1, 0.1, 1, 1},
				lastArr:    []float32{0.1, 0.1, 0.1, 1, 0.1},
				instanceId: "a",
			},
			want: false,
		},
		{
			name: "",
			args: args{
				curArr:     []float32{0.1, 0.1, 0.1, 1},
				lastArr:    []float32{0.1, 0.1, 0.1, 0.1},
				instanceId: "a",
			},
			want: true,
		},
		{
			name: "",
			args: args{
				curArr:     []float32{0.1, 0.1, 0.1, 1},
				lastArr:    []float32{0.1, 0.1, 0.1, 0.1},
				instanceId: "b",
			},
			want: false,
		},
		{
			name: "",
			args: args{
				curArr:  []float32{0.1, 0.1, 0.1, 1},
				lastArr: []float32{},
			},
			want: false,
		},
		{
			name: "",
			args: args{
				curArr:  []float32{0.1, 0.1, 0.1, 1},
				lastArr: nil,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := s.Over5Unavailable(tt.args.curArr, tt.args.lastArr, tt.args.instanceId); got != tt.want {
				t.Errorf("Over5Unavailable() = %v, want %v", got, tt.want)
			}
		})
	}
}

type SLATestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *SLATestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}
func (suite *SLATestSuite) TearDownTest() {
	suite.ctrl.Finish()
}
func TestSLASuite(t *testing.T) {
	suite.Run(t, new(SLATestSuite))
}

func (suite *SLATestSuite) TestNewSLA() {

	PatchConvey("", suite.T(), func() {
		ctx := context.TODO()
		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		instanceInfo := &datasource.DescribeDBInstanceDetailResp{
			InstanceStatus: "Running",
		}
		ds.EXPECT().DescribeDBInstanceDetail(gomock.Any(), gomock.Any()).Return(instanceInfo, nil).AnyTimes()
		s := SLAAuditMetricReporterActor{
			Source:      ds,
			CurPeriod:   libutils.Int64Ref(time.Now().Truncate(5 * time.Minute).Unix()),
			LastSlaPush: map[string]float32{},
		}
		s.logSla(ctx, &dao.AuditTls{
			InstanceID:       "xxx",
			TlsId:            123,
			FollowInstanceID: "mysql-48be8f7bfa3c",
			Region:           "",
			DbType:           model.DSType_MySQL.String(),
			DeployType:       "proxy-front",
			TenantID:         "1",
			Status:           int32(model.SqlAuditFuncStatus_RUN),
			DefaultCloseType: "",
			Deleted:          0,
		}, 1)
	})

	PatchConvey("", suite.T(), func() {
		ctx := context.TODO()
		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		instanceInfo := &datasource.DescribeDBInstanceDetailResp{
			InstanceStatus: "Running",
		}
		ds.EXPECT().DescribeDBInstanceDetail(gomock.Any(), gomock.Any()).Return(instanceInfo, nil).AnyTimes()
		s := SLAAuditMetricReporterActor{
			Source:      ds,
			CurPeriod:   libutils.Int64Ref(time.Now().Truncate(5 * time.Minute).Unix()),
			LastSlaPush: map[string]float32{},
		}
		s.logSla(ctx, &dao.AuditTls{
			InstanceID:       "xxx",
			TlsId:            123,
			FollowInstanceID: "mysql-48be8f7bfa3c",
			Region:           "",
			DbType:           model.DSType_MySQL.String(),
			DeployType:       "proxy-front",
			TenantID:         "1",
			Status:           int32(model.SqlAuditFuncStatus_RUN),
			DefaultCloseType: "",
			Deleted:          0,
		}, 0.5)
	})

	PatchConvey("", suite.T(), func() {
		ctx := context.TODO()
		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		instanceInfo := &datasource.DescribeDBInstanceDetailResp{
			InstanceStatus: "Migrate",
		}
		ds.EXPECT().DescribeDBInstanceDetail(gomock.Any(), gomock.Any()).Return(instanceInfo, nil).AnyTimes()
		s := SLAAuditMetricReporterActor{
			Source:      ds,
			CurPeriod:   libutils.Int64Ref(time.Now().Truncate(5 * time.Minute).Unix()),
			LastSlaPush: map[string]float32{},
		}
		s.logSla(ctx, &dao.AuditTls{
			InstanceID:       "xxx",
			TlsId:            123,
			FollowInstanceID: "mysql-48be8f7bfa3c",
			Region:           "",
			DbType:           model.DSType_MySQL.String(),
			DeployType:       "proxy-front",
			TenantID:         "1",
			Status:           int32(model.SqlAuditFuncStatus_RUN),
			DefaultCloseType: "",
			Deleted:          0,
		}, 0.5)
	})

	PatchConvey("", suite.T(), func() {
		ctx := context.TODO()
		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		ds.EXPECT().DescribeDBInstanceDetail(gomock.Any(), gomock.Any()).Return(nil, errors.New("are you ok")).AnyTimes()
		s := SLAAuditMetricReporterActor{
			Source:      ds,
			CurPeriod:   libutils.Int64Ref(time.Now().Truncate(5 * time.Minute).Unix()),
			LastSlaPush: map[string]float32{},
		}
		s.logSla(ctx, &dao.AuditTls{
			InstanceID:       "xxx",
			TlsId:            123,
			FollowInstanceID: "mysql-48be8f7bfa3c",
			Region:           "",
			DbType:           model.DSType_MySQL.String(),
			DeployType:       "proxy-front",
			TenantID:         "1",
			Status:           int32(model.SqlAuditFuncStatus_RUN),
			DefaultCloseType: "",
			Deleted:          0,
		}, 0.5)
	})
}

func (suite *SLATestSuite) TestSLAStart() {
	BuildJsonLogger(JsonLoggerConfig{
		LOG_FILE,
		DEFAULT_KEEP_COUNT,
	})
	PatchConvey("", suite.T(), func() {
		ctx := context.TODO()
		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		instanceInfo := &datasource.DescribeDBInstanceDetailResp{
			InstanceStatus: "Running",
		}
		ds.EXPECT().DescribeDBInstanceDetail(gomock.Any(), gomock.Any()).Return(instanceInfo, nil).AnyTimes()

		atDal := mock_dal.NewMockObInstDAL(suite.ctrl)
		atDal.EXPECT().GetAll(gomock.Any(), gomock.Any()).Return([]*dao.AuditTls{
			{
				InstanceID:       "1",
				FollowInstanceID: "1",
				TenantID:         "1",
				DbType:           "MySQL",
				Status:           2,
			},
			{
				InstanceID:       "2",
				FollowInstanceID: "2",
				TenantID:         "2",
				DbType:           "MySQL",
				Status:           2,
			},
			{
				InstanceID:       "3",
				FollowInstanceID: "3",
				TenantID:         "3",
				DbType:           "MySQL",
				Status:           2,
			},
		}, nil).AnyTimes()
		cfg := config.NewMockConfigProvider(suite.ctrl)
		cfg.EXPECT().Get(gomock.Any()).Return(&ycnf.Config{
			SLACheckTimeInterval: 300,
			SLATenantBlackList:   []string{"3"},
		}).AnyTimes()
		slasvc := sla2.NewMockSlaSvc(suite.ctrl)
		slasvc.EXPECT().GetAuditInstanceSLA(gomock.Any(), gomock.Any()).Return(float32(0.5), nil).AnyTimes()
		slasvc.EXPECT().GetSqlInsightInstanceSLA(gomock.Any(), gomock.Any()).Return(float32(0.5), nil).AnyTimes()
		s := SLAAuditMetricReporterActor{
			InstDal: atDal,
			Source:  ds,
			Conf:    cfg,
			SlaSvc:  slasvc,

			CurPeriod:   libutils.Int64Ref(time.Now().Truncate(5 * time.Minute).Unix()),
			LastSlaPush: map[string]float32{},
			LastSlaMap:  map[string][]float32{},
			CurSlaMap:   map[string][]float32{},
		}
		err := s.Start(ctx)
		if err != nil {
			suite.Error(err)
		}
	})
	PatchConvey("", suite.T(), func() {
		ctx := context.TODO()
		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		instanceInfo := &datasource.DescribeDBInstanceDetailResp{
			InstanceStatus: "Running",
		}
		ds.EXPECT().DescribeDBInstanceDetail(gomock.Any(), gomock.Any()).Return(instanceInfo, nil).AnyTimes()

		atDal := mock_dal.NewMockObInstDAL(suite.ctrl)
		atDal.EXPECT().GetAll(gomock.Any(), gomock.Any()).Return([]*dao.AuditTls{
			{
				InstanceID:       "1",
				FollowInstanceID: "1",
				TenantID:         "1",
				DbType:           "MySQL",
				Status:           2,
			},
			{
				InstanceID:       "2",
				FollowInstanceID: "2",
				TenantID:         "2",
				DbType:           "MySQL",
				Status:           2,
			},
			{
				InstanceID:       "3",
				FollowInstanceID: "3",
				TenantID:         "3",
				DbType:           "MySQL",
				Status:           2,
			},
		}, nil).AnyTimes()
		cfg := config.NewMockConfigProvider(suite.ctrl)
		cfg.EXPECT().Get(gomock.Any()).Return(&ycnf.Config{
			SLACheckTimeInterval: 300,
			SLATenantBlackList:   []string{"3"},
		}).AnyTimes()
		slasvc := sla2.NewMockSlaSvc(suite.ctrl)
		slasvc.EXPECT().GetAuditInstanceSLA(gomock.Any(), gomock.Any()).Return(float32(0.5), nil).AnyTimes()
		slasvc.EXPECT().GetSqlInsightInstanceSLA(gomock.Any(), gomock.Any()).Return(float32(0.5), nil).AnyTimes()
		s := SLAAuditMetricReporterActor{
			InstDal: atDal,
			Source:  ds,
			Conf:    cfg,
			SlaSvc:  slasvc,

			CurPeriod:   libutils.Int64Ref(time.Time{}.Unix()),
			LastSlaPush: map[string]float32{},
			LastSlaMap:  map[string][]float32{},
			CurSlaMap:   map[string][]float32{},
		}
		err := s.Start(ctx)
		if err != nil {
			suite.Error(err)
		}
	})
	PatchConvey("", suite.T(), func() {
		ctx := context.TODO()
		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
		ds := mocks.NewMockDataSourceService(suite.ctrl)
		instanceInfo := &datasource.DescribeDBInstanceDetailResp{
			InstanceStatus: "Running",
		}
		ds.EXPECT().DescribeDBInstanceDetail(gomock.Any(), gomock.Any()).Return(instanceInfo, nil).AnyTimes()

		atDal := mock_dal.NewMockObInstDAL(suite.ctrl)
		atDal.EXPECT().GetAll(gomock.Any(), gomock.Any()).Return([]*dao.AuditTls{
			{
				InstanceID:       "1",
				FollowInstanceID: "1",
				TenantID:         "1",
				DbType:           "MySQL",
				Status:           2,
			},
			{
				InstanceID:       "2",
				FollowInstanceID: "2",
				TenantID:         "2",
				DbType:           "MySQL",
				Status:           2,
			},
			{
				InstanceID:       "3",
				FollowInstanceID: "3",
				TenantID:         "3",
				DbType:           "MySQL",
				Status:           2,
			},
		}, nil).AnyTimes()
		cfg := config.NewMockConfigProvider(suite.ctrl)
		cfg.EXPECT().Get(gomock.Any()).Return(&ycnf.Config{
			SLACheckTimeInterval: 300,
			SLATenantBlackList:   []string{"3"},
		}).AnyTimes()
		slasvc := sla2.NewMockSlaSvc(suite.ctrl)
		slasvc.EXPECT().GetAuditInstanceSLA(gomock.Any(), gomock.Any()).Return(float32(0.5), nil).AnyTimes()
		slasvc.EXPECT().GetSqlInsightInstanceSLA(gomock.Any(), gomock.Any()).Return(float32(0.5), nil).AnyTimes()
		s := SLAAuditMetricReporterActor{
			InstDal: atDal,
			Source:  ds,
			Conf:    cfg,
			SlaSvc:  slasvc,

			CurPeriod:   libutils.Int64Ref(time.Now().Add(-5 * time.Minute).Truncate(5 * time.Minute).Unix()),
			LastSlaPush: map[string]float32{},
			LastSlaMap:  map[string][]float32{},
			CurSlaMap:   map[string][]float32{},
		}
		err := s.Start(ctx)
		if err != nil {
			suite.Error(err)
		}
	})
}
