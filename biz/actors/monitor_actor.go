// Dts Monitor

package actors

import (
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

func NewMonitorVirtualActor() types.SingletonProducer {
	return types.SingletonProducer{
		Name: "Monitor",
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &MonitorVirtualActor{}
		}),
	}
}

type MonitorVirtualActor struct {
}

func (a *MonitorVirtualActor) Process(actorCtx types.Context) {
	interval := time.Minute * 1
	defer actorCtx.SetReceiveTimeout(interval)
	ctx := context.Background()
	switch actorCtx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "Daemon Monitor Actor Started")
		a.activeDaemonActors(actorCtx)
	case *actor.ReceiveTimeout:
		a.activeDaemonActors(actorCtx)
	}
}

func (a *MonitorVirtualActor) activeDaemonActors(ctx types.Context) {
	ctx.ClientOf(consts.ApolloActorKind).GetPID(consts.ApolloActorName)
	ctx.ClientOf(consts.ShuttleMgrActorKind).GetPID(consts.SingletonActorName)
}
