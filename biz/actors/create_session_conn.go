package actors

import (
	"github.com/qjpcpu/fp"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

func (a *SessionActor) createSessionConn(ctx types.Context, msg *shared.NewSessionConnection) {
	if a.state.DataSource.Type == shared.Mongo && msg.DbName == "" {
		// mongo，如果没有传dbName，就改为 AuthDb 这个是第一次登录的时候，设置的内容
		msg.DbName = a.state.DataSource.AuthDb
	}
	result, err := a._createSessionConn(ctx, msg)
	if err != nil {
		ctx.Respond(&shared.SessionConnectionCreated{
			Code:         shared.CreateSessionFail,
			ErrorMessage: err.Error(),
		})
		return
	}
	a.state.addConn(result.Conn)
	a.state.TenantID = fwctx.GetTenantID(ctx)
	a.state.UserID = fwctx.GetUserID(ctx)

	if a.state.DataSource.Type == shared.Mongo && a.state.DataSource.AuthDb != msg.DbName {
		// 如果是mongo，且要登录的DB和鉴权DB不一样，那么就做一次切库的动作，因为我们上面是按照鉴权DB去连的
		err = a._changeDb(ctx, &shared.ChangeDB{ConnectionId: result.Conn.ID, DB: msg.DbName})
		if err != nil {
			ctx.Respond(&shared.SessionConnectionCreated{
				Code:         shared.CreateSessionFail,
				ErrorMessage: err.Error(),
			})
			return
		}
		result.Conn.CurrentDB = msg.DbName
	}

	ctx.Respond(&shared.SessionConnectionCreated{
		SessionId:         a.GetSessionID(ctx),
		ConnectionId:      result.Conn.ID,
		ConnectionName:    result.Conn.Name,
		Code:              shared.CreatedOK,
		CurrentDb:         result.Conn.CurrentDB,
		OuterConnectionId: result.Conn.OuterID,
	})
}

func (a *SessionActor) _changeDb(ctx types.Context, msg *shared.ChangeDB) error {
	_, err := ctx.ClientOf(consts.ConnectionActorKind).
		Call(ctx, msg.ConnectionId, &shared.ChangeDB{
			DB: msg.DB,
		})
	if err != nil {
		log.Warn(ctx, "change database fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.DatabaseNotExist,
			ErrorMessage: err.Error(),
		})
		return err
	}
	return nil
}

type createSessionConnResult struct {
	Conn *Connection
}

func (a *SessionActor) _createSessionConn(ctx types.Context, msg *shared.NewSessionConnection) (*createSessionConnResult, error) {
	connID, err := a.idSvc.NextIDStr(ctx)
	if err != nil {
		return nil, err
	}
	conn := a.state.newConnection(connID, msg.Name)

	// err = a.openTunnelForConnector(ctx, a.state.DataSource, conn.ID)
	// if err != nil {
	// 	return nil, err
	// }
	if err = a.checkSSL(ctx, a.state.DataSource); err != nil {
		return nil, err
	}

	//fixme：这一行代码实际没什么作用，已经可以删除了 cc陈小雨
	//if err := a.addWhiteList(ctx, conn.ID, a.state.DataSource); err != nil {
	//	return nil, err
	//}

	connDatasource := a.state.DataSource
	// create connection优先用传进来的dbName
	connDatasource.Db = msg.DbName
	// 如果是mongo，需要用authDB去连，不然会报错，之后我们在做切库的动作
	if a.state.DataSource.Type == shared.Mongo {
		connDatasource.Db = a.state.DataSource.AuthDb
	}
	if info, err := a.connect(ctx, conn.ID, connDatasource, a.state.SesstionTimeoutSeconds); err != nil {
		return nil, err
	} else {
		conn.CurrentDB = info.CurrentDb
		conn.OuterID = info.OuterConnectionId
	}
	return &createSessionConnResult{Conn: conn}, nil
}

func (a *SessionActor) checkSSL(ctx types.Context, source *shared.DataSource) error {
	sslInfo, err := a.sources.DescribeDBInstanceSSL(ctx, &datasource.DescribeDBInstanceSSLReq{
		InstanceId: source.InstanceId,
		Type:       source.Type,
	})
	if err != nil {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
	}
	if sslInfo.SSLEnable {
		return consts.ErrorOf(model.ErrorCode_NotSupportSsl)
	}
	return nil
}

func (a *SessionActor) addWhiteList(ctx types.Context, connID string, source *shared.DataSource) error {
	return fp.M(a.getConnectorLocation(ctx, connID)).
		ExpectPass(func(ip string) bool {
			return !fp.StreamOf(a.state.clientIPList()).Contains(ip)
		}).
		Map(func(ip string) (string, error) {
			innerK8s := fp.StreamOf([]string{"********/8", "*********/8", "**********/10"})
			ipList := fp.StreamOf(a.state.clientIPList()).Union(innerK8s).Append(ip).Uniq().Strings()
			return ip, a.sources.UpdateWhiteList(ctx, a.GetSessionID(ctx), source, ipList)
		}).
		Map(func(ip string) error {
			a.state.addClientIP(ip)
			return nil
		}).
		Val().
		Err()
}
