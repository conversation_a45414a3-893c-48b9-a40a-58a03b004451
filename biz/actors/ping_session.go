package actors

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/qjpcpu/fp"
)

func (a *SessionActor) pingSession(ctx types.Context) {
	fp.StreamOf(a.state.Conn).
		Foreach(func(c *Connection) {
			ctx.ClientOf(consts.ConnectionActorKind).
				Send(ctx, a.getConnectionActorName(ctx, c.ID), &shared.Ping{})
		}).
		Run()
	// TODO: ping c3 actor?
}
