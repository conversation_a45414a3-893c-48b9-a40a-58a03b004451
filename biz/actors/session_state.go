package actors

import (
	"encoding/json"
	"fmt"
	"time"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"github.com/qjpcpu/fp"
)

func newSessionState(bytes []byte) *sessionState {
	ss := &sessionState{}
	if len(bytes) == 0 {
		if ss.ConnLock == nil {
			ss.ConnLock = make(map[string]*CommandCursor)
		}
		ss.QueryContext = &QueryContext{}
		return ss
	}
	json.Unmarshal(bytes, ss)
	if ss.ConnLock == nil {
		ss.ConnLock = make(map[string]*CommandCursor)
	}
	if ss.QueryContext == nil {
		ss.QueryContext = &QueryContext{}
	}
	return ss
}

type SqlTaskInfo struct {
	SqlTaskId     string
	CommandId     string
	ConnectionId  string
	SqlTaskStatus model.SqlTaskStatus
}

type QueryContext struct {
	DB            int
	Query         string
	PrePageSize   int64
	PrePageNumber int64
	CurCursor     uint64
	CursorMap     map[uint64]uint64
	TotalKeys     int64
}

func (q *QueryContext) SetCursor(page uint64, cursor uint64) {
	if q.CursorMap == nil {
		q.CursorMap = make(map[uint64]uint64)
	}
	q.CursorMap[page] = cursor
}

func (q *QueryContext) Clear() {
	q = &QueryContext{}
}

type sessionState struct {
	/* all field must use upper case cause we choose json marshal */
	IdlePeriodSeconds      int64
	MyIP                   string
	ConnectionIPList       []string
	SessionCreated         bool
	SesstionTimeoutSeconds int64
	DataSource             *shared.DataSource `json:"DataSource,omitempty"`
	Conn                   []*Connection
	DeadConn               []string
	TenantID               string
	UserID                 string
	WlID                   string
	/* Redis Scan */
	QueryContext *QueryContext
	/* connection id to command cursor */
	ConnLock            map[string]*CommandCursor
	IgnoreSecurityCheck bool
	/* sql task status */
	SqlTaskRunning bool
	SqlTaskInfo    SqlTaskInfo
}

func (state *sessionState) Bytes() []byte {
	data, _ := json.Marshal(state)
	return data
}

func (state *sessionState) clear() {
	state.IdlePeriodSeconds = 0
	state.SesstionTimeoutSeconds = 0
	state.QueryContext = nil
}

func (state *sessionState) clearQueryContext() {
	state.QueryContext = &QueryContext{}
}

func (state *sessionState) resetIdlePeriod() {
	state.IdlePeriodSeconds = 0
}

func (state *sessionState) increaseIdlePeriod(dur int64) {
	state.IdlePeriodSeconds += dur
}

func (state *sessionState) isIdleTimeout() bool {
	return state.SesstionTimeoutSeconds > 0 && state.IdlePeriodSeconds > state.SesstionTimeoutSeconds
}

func (state *sessionState) addConn(conn *Connection) {
	state.Conn = append(state.Conn, conn)
}

func (state *sessionState) resetQueryContext(qctx *QueryContext) {
	state.QueryContext = qctx
}

func (state *sessionState) containsConn(connID string) bool {
	return fp.StreamOf(state.Conn).ContainsBy(func(c *Connection) bool {
		return c.ID == connID
	})
}

func (state *sessionState) getConn(connID string) *Connection {
	for _, c := range state.Conn {
		if c.ID == connID {
			return c
		}
	}
	return nil
}

func (state *sessionState) updateConnInfo(info *shared.ConnectionInfo) bool {
	for _, conn := range state.Conn {
		if conn.ID == info.ConnectionId && (info.CurrentDb != conn.CurrentDB || info.OuterConnectionId != conn.OuterID) {
			conn.CurrentDB = info.CurrentDb
			conn.OuterID = info.OuterConnectionId
			return true
		}
	}
	return false
}

func (state *sessionState) containsDeadConn(connID string) bool {
	return fp.StreamOf(state.DeadConn).Contains(connID)
}

func (state *sessionState) connectionBusy(connID string) bool {
	_, ok := state.ConnLock[connID]
	return ok
}

func (state *sessionState) removeConn(id string) {
	fp.StreamOf(state.Conn).
		Reject(func(c *Connection) bool {
			return c.ID == id
		}).
		ToSlice(&state.Conn)
	delete(state.ConnLock, id)
}

func (state *sessionState) addClientIP(ip string) {
	state.ConnectionIPList = append(state.ConnectionIPList, ip)
}

func (state *sessionState) clientIPList() []string {
	return fp.StreamOf(state.ConnectionIPList).
		Append(state.MyIP).
		Reject(fp.EmptyString()).
		Sort().
		Strings()
}

func (state *sessionState) newConnection(id, name string) *Connection {
	if name == "" {
		name = fmt.Sprintf("tab-%v", len(state.Conn)+1)
	}
	return &Connection{
		ID:         id,
		Name:       name,
		CreateTime: time.Now(),
	}
}

type Connection struct {
	ID         string
	Name       string
	CreateTime time.Time
	CurrentDB  string
	OuterID    string
}
