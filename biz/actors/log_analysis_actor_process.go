package actors

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"encoding/json"
	"fmt"
)

func (a *LogAnalysisActor) processDescribeSlowLogTimeSeriesStatsReq(ctx types.Context, req *shared.DescribeSlowLogTimeSeriesStatsReq) {
	resp, err := a.tlsClient.DescribeSlogLogTimeSeriesStats(ctx, req)
	if err != nil {
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrCallTls,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp)
}

func (a *LogAnalysisActor) processDescribeAggregateSlowLogsReq(ctx types.Context, req *shared.DescribeAggregateSlowLogsReq) {
	var (
		err            error
		resp           *shared.DescribeAggregateSlowLogsResp
		cacheValuePair *aggregateSlowLogsCachePair
	)

	limit := req.GetLimit()
	offset := req.GetOffset()

	key := computeID(ctx, req)
	if key == nil {
		err = fmt.Errorf("unknown input message %#v", req)
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrComputeIdFailed,
			ErrorMessage: err.Error(),
		})
		return
	}

	// read value from cache
	cacheValue, err := a.cache.Get(key)
	if err != nil {
		// if value not in cache,
		// read from tls and put result in cache
		resp, err = a.tlsClient.DescribeAggregateSlowLogs(ctx, req)
		if err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCallTls,
				ErrorMessage: err.Error(),
			})
			return
		}

		cacheValuePair = &aggregateSlowLogsCachePair{
			Values: resp.GetAggregateSlowLogs(),
			Total:  resp.GetTotal(),
		}
		cacheValue, err = json.Marshal(cacheValuePair)
		if err != nil {
			e := fmt.Errorf("DescribeAggregateSlowLogs: marshal cacheValue error: %s", err)
			log.Warn(ctx, "%s", e.Error())
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrMarshalFail,
				ErrorMessage: err.Error(),
			})
			return
		}

		a.cache.Set(key, cacheValue, keyTimeout)
	}

	resp = &shared.DescribeAggregateSlowLogsResp{}
	cacheValuePair = &aggregateSlowLogsCachePair{}
	err = json.Unmarshal(cacheValue, cacheValuePair)
	if err != nil {
		e := fmt.Errorf("DescribeAggregateSlowLogs: unmarshal cacheValue error: %s", err)
		log.Warn(ctx, "%s", e.Error())
		log.Warn(ctx, "%s", e.Error())
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrUnmarshalFail,
			ErrorMessage: err.Error(),
		})
		return
	}

	left := offset
	right := min(offset+limit, int32(len(cacheValuePair.Values)))
	if left >= int32(len(cacheValuePair.Values)) {
		ctx.Respond(&shared.DescribeAggregateSlowLogsResp{AggregateSlowLogs: make([]*shared.AggregateSlowLog, 0), Total: cacheValuePair.Total})
		return
	}

	resp.AggregateSlowLogs = cacheValuePair.Values[left:right]
	resp.Total = cacheValuePair.Total
	ctx.Respond(resp)
}

func (a *LogAnalysisActor) processDescribeSlowLogsReq(ctx types.Context, req *shared.DescribeSlowLogsReq) {
	var (
		err            error
		resp           *shared.DescribeSlowLogsResp
		cacheValuePair *slowLogsCachePair
	)

	limit := req.GetLimit()
	offset := req.GetOffset()

	key := computeID(ctx, req)
	if key == nil {
		err = fmt.Errorf("unknown input message %#v", req)
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrComputeIdFailed,
			ErrorMessage: err.Error(),
		})
		return
	}

	// read value from cache
	cacheValue, err := a.cache.Get(key)
	if err != nil {
		// if value not in cache,
		// read from tls and put result in cache
		resp, err = a.tlsClient.DescribeSlowLogs(ctx, req)
		if err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCallTls,
				ErrorMessage: err.Error(),
			})
			return
		}

		cacheValuePair = &slowLogsCachePair{
			Values: resp.GetSlowLogs(),
			Total:  resp.GetTotal(),
		}
		cacheValue, err = json.Marshal(cacheValuePair)
		if err != nil {
			e := fmt.Errorf("DescribeSlowLogs: marshal cacheValue error: %s", err)
			log.Warn(ctx, "%s", e.Error())
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrMarshalFail,
				ErrorMessage: err.Error(),
			})
			return
		}

		a.cache.Set(key, cacheValue, keyTimeout)
	}

	resp = &shared.DescribeSlowLogsResp{}
	cacheValuePair = &slowLogsCachePair{}
	err = json.Unmarshal(cacheValue, cacheValuePair)
	if err != nil {
		e := fmt.Errorf("DescribeSlowLogs: unmarshal cacheValue error: %s", err)
		log.Warn(ctx, "%s", e)
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrUnmarshalFail,
			ErrorMessage: err.Error(),
		})
		return
	}

	left := offset
	right := min(offset+limit, int32(len(cacheValuePair.Values)))
	log.Info(ctx, "left is %d,cacheValuePair lens is %d", left, len(cacheValuePair.Values))
	if left >= int32(len(cacheValuePair.Values)) {
		ctx.Respond(&shared.DescribeSlowLogsResp{SlowLogs: make([]*shared.SlowLog, 0), Total: cacheValuePair.Total})
		return
	}

	resp.SlowLogs = cacheValuePair.Values[left:right]
	resp.Total = cacheValuePair.Total
	ctx.Respond(resp)
}

func (a *LogAnalysisActor) processDescribeUsers(ctx types.Context, req *shared.DescribeUsersReq) {
	var (
		err            error
		resp           *shared.DescribeUsersResp
		cacheValuePair *GroupCachePair
	)

	limit := req.GetLimit()
	offset := req.GetOffset()

	key := computeID(ctx, req)
	if key == nil {
		err = fmt.Errorf("unknown input message %#v", req)
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrComputeIdFailed,
			ErrorMessage: err.Error(),
		})
		return
	}

	// read value from cache
	cacheValue, err := a.cache.Get(key)
	if err != nil {
		// if value not in cache,
		// read from tls and put result in cache
		resp, err = a.tlsClient.DescribeUsers(ctx, req)
		if err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCallTls,
				ErrorMessage: err.Error(),
			})
			return
		}

		cacheValuePair = &GroupCachePair{
			Values: resp.GetUsers(),
			Total:  resp.GetTotal(),
		}
		cacheValue, err = json.Marshal(cacheValuePair)
		if err != nil {
			e := fmt.Errorf("DescribeUsers: marshal cacheValue error: %s", err)
			log.Warn(ctx, "%s", e.Error())
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrMarshalFail,
				ErrorMessage: err.Error(),
			})
			return
		}

		a.cache.Set(key, cacheValue, keyTimeout)
	}

	resp = &shared.DescribeUsersResp{}
	cacheValuePair = &GroupCachePair{}
	err = json.Unmarshal(cacheValue, cacheValuePair)
	if err != nil {
		e := fmt.Errorf("DescribeUsers: unmarshal cacheValue error: %s", err)
		log.Warn(ctx, "%s", e.Error())
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrUnmarshalFail,
			ErrorMessage: err.Error(),
		})
		return
	}

	left := offset
	right := min(offset+limit, int32(len(cacheValuePair.Values)))
	if left >= int32(len(cacheValuePair.Values)) {
		ctx.Respond(&shared.DescribeUsersResp{Users: make([]string, 0), Total: cacheValuePair.Total})
		return
	}

	resp.Users = cacheValuePair.Values[left:right]
	resp.Total = cacheValuePair.Total
	ctx.Respond(resp)
}

func (a *LogAnalysisActor) processDescribeSourceIPs(ctx types.Context, req *shared.DescribeSourceIPsReq) {
	var (
		err            error
		resp           *shared.DescribeSourceIPsResp
		cacheValuePair *GroupCachePair
	)

	limit := req.GetLimit()
	offset := req.GetOffset()

	key := computeID(ctx, req)
	if key == nil {
		err = fmt.Errorf("unknown input message %#v", req)
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrComputeIdFailed,
			ErrorMessage: err.Error(),
		})
		return
	}

	// read value from cache
	cacheValue, err := a.cache.Get(key)
	if err != nil {
		// if value not in cache,
		// read from tls and put result in cache
		resp, err = a.tlsClient.DescribeSourceIPs(ctx, req)
		if err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCallTls,
				ErrorMessage: err.Error(),
			})
			return
		}

		cacheValuePair = &GroupCachePair{
			Values: resp.GetSourceIPs(),
			Total:  resp.GetTotal(),
		}
		cacheValue, err = json.Marshal(cacheValuePair)
		if err != nil {
			e := fmt.Errorf("DescribeSourceIPs: marshal cacheValue error: %s", err)
			log.Warn(ctx, "%s", e.Error())
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrMarshalFail,
				ErrorMessage: err.Error(),
			})
			return
		}

		a.cache.Set(key, cacheValue, keyTimeout)
	}

	resp = &shared.DescribeSourceIPsResp{}
	cacheValuePair = &GroupCachePair{}
	err = json.Unmarshal(cacheValue, cacheValuePair)
	if err != nil {
		e := fmt.Errorf("DescribeSourceIPs: unmarshal cacheValue error: %s", err)
		log.Warn(ctx, "%s", e.Error())
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrUnmarshalFail,
			ErrorMessage: err.Error(),
		})
		return
	}

	left := offset
	right := min(offset+limit, int32(len(cacheValuePair.Values)))
	if left >= int32(len(cacheValuePair.Values)) {
		ctx.Respond(&shared.DescribeSourceIPsResp{SourceIPs: make([]string, 0), Total: cacheValuePair.Total})
		return
	}

	resp.SourceIPs = cacheValuePair.Values[left:right]
	resp.Total = cacheValuePair.Total
	ctx.Respond(resp)
}

func (a *LogAnalysisActor) processDescribeDBs(ctx types.Context, req *shared.DescribeDBsReq) {
	var (
		err            error
		resp           *shared.DescribeDBsResp
		cacheValuePair *GroupCachePair
	)

	limit := req.GetLimit()
	offset := req.GetOffset()

	key := computeID(ctx, req)
	if key == nil {
		err = fmt.Errorf("unknown input message %#v", req)
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrComputeIdFailed,
			ErrorMessage: err.Error(),
		})
		return
	}

	// read value from cache
	cacheValue, err := a.cache.Get(key)
	if err != nil {
		// if value not in cache,
		// read from tls and put result in cache
		resp, err = a.tlsClient.DescribeDBs(ctx, req)
		if err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCallTls,
				ErrorMessage: err.Error(),
			})
			return
		}

		cacheValuePair = &GroupCachePair{
			Values: resp.GetDBs(),
			Total:  resp.GetTotal(),
		}
		cacheValue, err = json.Marshal(cacheValuePair)
		if err != nil {
			e := fmt.Errorf("DescribeDBs: marshal cacheValue error: %s", err)
			log.Warn(ctx, "%s", e.Error())
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrMarshalFail,
				ErrorMessage: err.Error(),
			})
			return
		}

		a.cache.Set(key, cacheValue, keyTimeout)
	}

	resp = &shared.DescribeDBsResp{}
	cacheValuePair = &GroupCachePair{}
	err = json.Unmarshal(cacheValue, cacheValuePair)
	if err != nil {
		e := fmt.Errorf("DescribeDBs: unmarshal cacheValue error: %s", err)
		log.Warn(ctx, "%s", e.Error())
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrUnmarshalFail,
			ErrorMessage: err.Error(),
		})
		return
	}

	left := offset
	right := min(offset+limit, int32(len(cacheValuePair.Values)))
	if left >= int32(len(cacheValuePair.Values)) {
		ctx.Respond(&shared.DescribeDBsResp{DBs: make([]string, 0), Total: cacheValuePair.Total})
		return
	}

	resp.DBs = cacheValuePair.Values[left:right]
	resp.Total = cacheValuePair.Total
	ctx.Respond(resp)
}

func (a *LogAnalysisActor) processDescribeExampleSQLReqReq(ctx types.Context, req *shared.DescribeExampleSQLReq) {
	resp, err := a.tlsClient.DescribeExampleSQL(ctx, req)
	if err != nil {
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrCallTls,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp)
}

func (a *LogAnalysisActor) processCreateSlowLogsExportTask(ctx types.Context, req *shared.CreateSlowLogsExportTaskReq) {
	resp, err := a.tlsClient.CreateSlowLogsExportTask(ctx, req)
	if err != nil {
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrCallTls,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp)
}

func (a *LogAnalysisActor) processDescribeSlowLogsExportTasks(ctx types.Context, req *shared.DescribeSlowLogsExportTasksReq) {
	resp, err := a.tlsClient.DescribeSlowLogsExportTasks(ctx, req)
	if err != nil {
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrCallTls,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp)
}

func (a *LogAnalysisActor) processDescribeLogsDownloadUrl(ctx types.Context, req *shared.DescribeLogsDownloadUrlReq) {
	resp, err := a.tlsClient.DescribeLogsDownloadUrl(ctx, req)
	if err != nil {
		ctx.Respond(&shared.SearchLogsFailed{
			Code:         shared.ErrCallTls,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp)
}
func min(a, b int32) int32 {
	if a > b {
		return b
	}
	return a
}
