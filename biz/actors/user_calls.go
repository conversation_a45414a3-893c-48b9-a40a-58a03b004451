package actors

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	biz_conv "code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/tos"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larksheets "github.com/larksuite/oapi-sdk-go/v3/service/sheets/v3"
)

func (a *SessionActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, "user call panic %v %s", r, string(debug.Stack()))
			ctx.Respond(&shared.DataSourceOpFailed{
				Error:        shared.UnknownOpError,
				ErrorMessage: fmt.Sprint(r),
			})
		}
	}()
	fn()
}

func (a *SessionActor) listDatabase(ctx types.Context, msg *shared.ListDatabase) {
	req := &datasource.ListDatabasesReq{
		Source:         a.state.DataSource,
		Offset:         msg.Offset,
		Limit:          msg.Limit,
		Keyword:        msg.Keyword,
		EnableSystemDB: msg.EnableSystemDb,
	}
	resp, err := a.sources.ListDatabases(ctx, req)
	if err != nil {
		log.Warn(ctx, "list datasource fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:         shared.DatabaseNotExist,
			ErrorMessage:  err.Error(),
			StandardError: consts.TranslateStandardErrorToShared(err),
		})
		return
	}

	ctx.Respond(&shared.DatabasesInfo{
		Total: resp.Total,
		Items: resp.Items,
	})
}

func (a *SessionActor) listTables(ctx types.Context, msg *shared.ListTable) {
	req := &datasource.ListTablesReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Schema:  msg.Schema,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
		Filters: msg.Filters,
	}
	log.Info(ctx, "list table req %v", req)
	resp, err := a.sources.ListTables(ctx, req)
	if err != nil {
		log.Warn(ctx, "list table fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.TableNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.TablesInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) listAllTables(ctx types.Context, msg *shared.ListAllTable) {
	req := &datasource.ListTablesReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Schema:  msg.Schema,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
		Filters: msg.Filters,
	}
	log.Info(ctx, "list table req %v", req)
	resp, err := a.sources.ListAllTables(ctx, req)
	if err != nil {
		log.Warn(ctx, "list table fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.TableNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.TablesInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) listSchemaTables(ctx types.Context, msg *shared.ListSchemaTables) {
	req := &datasource.ListSchemaTablesReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Schema:  msg.Schema,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
		Filters: msg.Filters,
	}
	log.Info(ctx, "list table req %v", req)
	resp, err := a.sources.ListSchemaTables(ctx, req)
	if err != nil {
		log.Warn(ctx, "list table fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.TableNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.TablesInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) listTablesInfo(ctx types.Context, msg *shared.ListTablesInfo) {
	req := &datasource.ListTablesInfoReq{
		Source:   a.state.DataSource,
		Database: msg.Db,
	}
	resp, err := a.sources.ListTablesInfo(ctx, req)
	if err != nil {
		log.Warn(ctx, "list tablesInfo error %s", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	log.Info(ctx, "session actor listed tables %s", utils.Show(resp.Tables))
	var tables []*shared.TableInfo
	for _, table := range resp.Tables {
		t := &shared.TableInfo{
			Name:        table.Name,
			Columns:     table.Columns,
			Indexs:      table.Indexes,
			ForeignKeys: table.ForeignKeys,
			TableOption: table.Option,
			Definition:  table.Definition,
		}
		tables = append(tables, t)
	}
	ctx.Respond(&shared.Tables{
		Tables: tables,
	})
}

func (a *SessionActor) describeTable(ctx types.Context, msg *shared.DescribeTable) {
	req := &datasource.DescribeTableReq{
		Source: a.state.DataSource,
		DB:     msg.Db,
		Table:  msg.Table,
	}
	resp, err := a.sources.DescribeTable(ctx, req)
	if err != nil {
		log.Warn(ctx, "describe table fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.TableInfo{
		Columns:     resp.Columns,
		Indexs:      resp.Indexs,
		ForeignKeys: resp.ForeignKeys,
		TableOption: &resp.Option,
		Definition:  resp.Definition,
		HtapOption:  resp.HTAPOption,
	})
}

func (a *SessionActor) describePgTable(ctx types.Context, msg *shared.DescribePgTable) {
	req := &datasource.DescribePgTableReq{
		Source: a.state.DataSource,
		DB:     msg.Db,
		Table:  msg.Table,
		Schema: msg.Schema,
	}
	resp, err := a.sources.DescribePgTable(ctx, req)
	if err != nil {
		log.Warn(ctx, "describe table fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.TableNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.PgTableInfo{
		TableOption:       &resp.Option,
		Columns:           resp.Columns,
		Indexs:            resp.Indexs,
		ForeignReference:  resp.ForeignReferenceInfo,
		ExcludeConstraint: resp.ExcludeConstraintInfo,
		UniqueConstraint:  resp.UniqueConstraintInfo,
		CheckConstraint:   resp.CheckConstraintInfo,
	})
}

func (a *SessionActor) listPgCollations(ctx types.Context, msg *shared.ListPgCollations) {
	req := &datasource.ListPgCollationsReq{
		Source:  a.state.DataSource,
		Keyword: msg.Keyword,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
	}
	resp, err := a.sources.ListPgCollations(ctx, req)
	if err != nil {
		log.Warn(ctx, "describe table fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.TableNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.PgCollationsInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) listPgUsers(ctx types.Context, msg *shared.ListPgUsers) {
	req := &datasource.ListPgUsersReq{
		Source:  a.state.DataSource,
		Keyword: msg.Keyword,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
	}
	resp, err := a.sources.ListPgUsers(ctx, req)
	if err != nil {
		log.Warn(ctx, "ListPgUsers fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.TableNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.PgUsersInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) listTableSpaces(ctx types.Context, msg *shared.ListTableSpaces) {
	req := &datasource.ListTableSpacesReq{
		Source:  a.state.DataSource,
		Keyword: msg.Keyword,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
	}
	resp, err := a.sources.ListTableSpaces(ctx, req)
	if err != nil {
		log.Warn(ctx, "ListTableSpaces fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.TableNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	log.Info(ctx, "DescribeTableSpaces Items SessionActor: %s", resp.Items)
	ctx.Respond(&shared.TableSpacesInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) listViews(ctx types.Context, msg *shared.ListView) {
	req := &datasource.ListViewsReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
		Schema:  msg.Schema,
	}
	resp, err := a.sources.ListViews(ctx, req)
	if err != nil {
		log.Warn(ctx, "list view fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.TableNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.ViewsInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) describeView(ctx types.Context, msg *shared.DescribeView) {
	req := &datasource.DescribeViewReq{
		Source: a.state.DataSource,
		DB:     msg.Db,
		View:   msg.View,
	}
	resp, err := a.sources.DescribeView(ctx, req)
	if err != nil {
		log.Warn(ctx, "describe view fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.ViewNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.ViewInfo)
}

func (a *SessionActor) describeFunction(ctx types.Context, msg *shared.DescribeFunction) {
	req := &datasource.DescribeFunctionReq{
		Source:   a.state.DataSource,
		DB:       msg.GetDb(),
		Function: msg.GetFunction(),
	}
	resp, err := a.sources.DescribeFunction(ctx, req)
	if err != nil {
		log.Warn(ctx, "describe fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.FunctionNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.FunctionInfo)
}

func (a *SessionActor) describeProcedure(ctx types.Context, msg *shared.DescribeProcedure) {
	req := &datasource.DescribeProcedureReq{
		Source:    a.state.DataSource,
		DB:        msg.GetDb(),
		Procedure: msg.GetProcedure(),
	}
	resp, err := a.sources.DescribeProcedure(ctx, req)
	if err != nil {
		log.Warn(ctx, "describe fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.ProcedureNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.ProcedureInfo)
}

func (a *SessionActor) listFunctions(ctx types.Context, msg *shared.ListFunction) {
	req := &datasource.ListFunctionsReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
		Schema:  msg.Schema,
	}
	resp, err := a.sources.ListFunctions(ctx, req)
	if err != nil {
		log.Warn(ctx, "list function fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.FunctionsInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) listProcedures(ctx types.Context, msg *shared.ListProcedure) {
	req := &datasource.ListProceduresReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
	}
	resp, err := a.sources.ListProcedures(ctx, req)
	if err != nil {
		log.Warn(ctx, "list procedure fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.ProceduresInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) listTriggers(ctx types.Context, msg *shared.ListTrigger) {
	req := &datasource.ListTriggersReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
		Schema:  msg.Schema,
	}
	resp, err := a.sources.ListTriggers(ctx, req)
	if err != nil {
		log.Warn(ctx, "list trigger fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.TriggersInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) describeEvent(ctx types.Context, msg *shared.DescribeEvent) {
	req := &datasource.DescribeEventReq{
		Source: a.state.DataSource,
		DB:     msg.GetDb(),
		Name:   msg.GetName(),
	}
	resp, err := a.sources.DescribeEvent(ctx, req)
	if err != nil {
		log.Warn(ctx, "describe fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.EventNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.EventInfo)
}

func (a *SessionActor) describeTrigger(ctx types.Context, msg *shared.DescribeTrigger) {
	req := &datasource.DescribeTriggerReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Trigger: msg.Trigger,
	}
	resp, err := a.sources.DescribeTrigger(ctx, req)
	if err != nil {
		log.Warn(ctx, "show create trigger fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.TriggerNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.TriggerInfo)
}

func (a *SessionActor) listEvents(ctx types.Context, msg *shared.ListEvents) {
	req := &datasource.ListEventsReq{
		Source:  a.state.DataSource,
		DB:      msg.GetDb(),
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
	}
	resp, err := a.sources.ListEvents(ctx, req)
	if err != nil {
		log.Warn(ctx, "list events fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.EventsInfo{
		Total: resp.Total,
		Items: resp.Items,
	})
}

func (a *SessionActor) changeDB(ctx types.Context, msg *shared.ChangeDB) {
	// 目前对外不支持changeDB，如果需要，提醒新建一个Connection
	// 目前整个代码已经支持在单个connection（特指，我们的connection并非真实物理连接）切换DB，如果后续需要支持，将下面判断内容移除即可
	//if a.state.DataSource.Type == shared.Postgres {
	//	ctx.Respond(&shared.DataSourceOpFailed{
	//		Error:        shared.PgNotSupportChangeDB,
	//		ErrorMessage: "pg not support",
	//	})
	//	return
	//}
	if !a.checkConnection(ctx, msg.ConnectionId) {
		return
	}
	resp, err := ctx.ClientOf(consts.ConnectionActorKind).
		Call(ctx, msg.ConnectionId, &shared.ChangeDB{
			DB: msg.DB,
		})
	if err != nil {
		log.Warn(ctx, "change database fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.DatabaseNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	switch result := resp.(type) {
	case *shared.DBChanged:
		a.resetOuterId(msg, result.OuterConnectionId)
	}
	ctx.Respond(resp)
}

func (a *SessionActor) resetOuterId(msg *shared.ChangeDB, outerId string) {
	// 如果传过来的outerId不为""，说明outerId可能因为changeDB被改了，需要重新设置
	if outerId == "" {
		return
	}
	for _, c := range a.state.Conn {
		if c.ID == msg.ConnectionId {
			c.OuterID = outerId
		}
	}
}

func (a *SessionActor) getAdvice(ctx types.Context, msg *shared.GetAdvice) {
	req := &datasource.GetAdviceReq{
		Source: a.state.DataSource,
		DB:     msg.GetDb(),
		Sql:    msg.GetSql(),
	}
	resp, err := a.sources.GetAdvice(ctx, req)
	if err != nil {
		log.Warn(ctx, "get sql Advice fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.AdviceInfo{
		ExplainInfos: resp.Explains,
	})
}

func (a *SessionActor) listCharsets(ctx types.Context, msg *shared.ListCharsets) {
	req := &datasource.ListCharsetsReq{
		Source:  a.state.DataSource,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
	}
	resp, err := a.sources.ListCharsets(ctx, req)
	if err != nil {
		log.Warn(ctx, "list events fail %v", err)
		return
	}
	ctx.Respond(&shared.CharsetsInfo{
		Total: resp.Total,
		Items: resp.Items,
	})
}

func (a *SessionActor) listCollations(ctx types.Context, msg *shared.ListCollations) {
	req := &datasource.ListCollationsReq{
		Source:  a.state.DataSource,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
	}
	resp, err := a.sources.ListCollations(ctx, req)
	if err != nil {
		log.Warn(ctx, "list events fail %v", err)
		return
	}
	ctx.Respond(&shared.CollationsInfo{
		Total: resp.Total,
		Items: resp.Items,
	})
}

func (a *SessionActor) describeDialogDetails(ctx types.Context, msg *shared.DescribeDialogDetails) {
	req := &datasource.DescribeDialogDetailsReq{
		Offset:        int64((msg.PageNumber - 1) * msg.PageSize),
		Limit:         int64(msg.PageSize),
		Source:        a.state.DataSource,
		QueryFilter:   msg.QueryFilter,
		InternalUsers: msg.InternalUsers,
		InternalIPs:   msg.InternalIPs,
	}
	resp, err := a.sources.DescribeDialogDetails(ctx, req)
	if err != nil {
		log.Warn(ctx, "failed to describe dialog details, instanceId=%s, sessionId=%s, err=%v",
			a.state.DataSource.GetInstanceId(), ctx.GetName(), err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.DialogDetails{
		Total:   resp.Total,
		Details: resp.DialogDetails,
	})
}

func (a *SessionActor) describeDialogStatistics(ctx types.Context, msg *shared.DescribeDialogStatistics) {
	req := &datasource.DescribeDialogStatisticsReq{
		Source:        a.state.DataSource,
		InternalUsers: msg.InternalUsers,
		InternalIPs:   msg.InternalIPs,
		Component:     msg.Component,
		QueryFilter:   msg.QueryFilter,
		TopN:          msg.TopN,
	}
	resp, err := a.sources.DescribeDialogStatistics(ctx, req)
	if err != nil {
		log.Warn(ctx, "failed to describe dialog statics, err=%v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.DialogStatistics)
}

func (a *SessionActor) killProcess(ctx types.Context, msg *shared.KillProcess) {
	req := &datasource.KillProcessReq{
		Source:     a.state.DataSource,
		ProcessIDs: msg.ProcessIDs,
		NodeId:     msg.NodeId,
	}
	resp, err := a.sources.KillProcess(ctx, req)
	if err != nil {
		log.Warn(ctx, "failed to kill dialog, err=%v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.KillProcessResult{
		KillFailInfoList: resp.FailInfoList,
	})
}

func (a *SessionActor) describeEngineStatus(ctx types.Context, msg *shared.DescribeEngineStatus) {
	req := &datasource.DescribeEngineStatusReq{
		Source: a.state.DataSource,
	}
	resp, err := a.sources.DescribeEngineStatus(ctx, req)
	if err != nil {
		log.Warn(ctx, "failed to describe engine status, instanceId=%s, sessionId=%s, err=%v",
			a.state.DataSource.GetInstanceId(), ctx.GetName(), err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.EngineStatus)
}

/* trx and lock*/
func (a *SessionActor) describeTrxAndLocks(ctx types.Context, msg *shared.DescribeTrxAndLocks) {
	req := &datasource.DescribeTrxAndLocksReq{
		Source:      a.state.DataSource,
		TrxStatus:   msg.TrxStatus,
		LockStatus:  msg.LockStatus,
		TrxExecTime: msg.TrxExecTime,
		SortParam:   msg.SortParam,
		Order:       msg.Order,
	}
	resp, err := a.sources.DescribeTrxAndLocks(ctx, req)
	if err != nil {
		log.Warn(ctx, "sort trx fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.Result)
}

func (a *SessionActor) describeDeadlock(ctx types.Context, msg *shared.DescribeDeadlock) {
	req := &datasource.DescribeDeadlockReq{
		Source:     a.state.DataSource,
		InstanceId: msg.InstanceId,
		TenantId:   msg.TenantId,
	}
	resp, err := a.sources.DescribeDeadlock(ctx, req)
	if err != nil {
		log.Warn(ctx, "describe Deadlock fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.DescribeDeadlockInfo)
}

func (a *SessionActor) describeDeadlockDetect(ctx types.Context, msg *shared.DescribeDeadlockDetect) {
	req := &datasource.DescribeDeadlockDetectReq{
		Source: a.state.DataSource,
	}
	resp, err := a.sources.DescribeDeadlockDetect(ctx, req)
	if err != nil {
		log.Warn(ctx, "describe Deadlock Detect fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.DescribeDeadlockDetectInfo)
}

func (a *SessionActor) describeDialogInfos(ctx types.Context, msg *shared.DescribeDialogInfos) {
	req := &datasource.DescribeDialogInfosReq{
		Offset:        int64((msg.PageNumber - 1) * msg.PageSize),
		Limit:         int64(msg.PageSize),
		Source:        a.state.DataSource,
		QueryFilter:   msg.QueryFilter,
		InternalUsers: msg.InternalUsers,
		InternalIPs:   msg.InternalIPs,
		Component:     msg.Component,
	}
	resp, err := a.sources.DescribeDialogInfos(ctx, req)
	if err != nil {
		log.Warn(ctx, "failed to describe dialog infos, instanceId=%s, sessionId=%s, err=%v",
			a.state.DataSource.GetInstanceId(), ctx.GetName(), err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.DialogInfos{
		Statistics: resp.DialogStatistics,
		Details:    resp.DialogDetails,
	})
}

func (a *SessionActor) listSchema(ctx types.Context, msg *shared.ListSchema) {
	req := &datasource.ListSchemaReq{
		Source: a.state.DataSource,
		DB:     msg.Db,
		//Offset:  msg.Offset,
		//Limit:   msg.Limit,
		Keyword: msg.Keyword,
	}
	resp, err := a.sources.ListSchema(ctx, req)
	if err != nil {
		log.Warn(ctx, "list Schema fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.SchemaNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.SchemasInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) listSequence(ctx types.Context, msg *shared.ListSequence) {
	req := &datasource.ListSequenceReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
		Schema:  msg.Schema,
	}
	resp, err := a.sources.ListSequence(ctx, req)
	if err != nil {
		log.Warn(ctx, "list Sequence fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.SequenceNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.SequencesInfo{
		Total: resp.Total,
		Name:  resp.Items,
	})
}

func (a *SessionActor) CheckExportRunningTask(ctx types.Context, msg *shared.CreateDbExportTaskReq) error {
	//校验当前实例有无在执行的导出任务
	exportTaskInfos, err := a.MigRepo.ListActiveTasks(ctx, a.state.DataSource.InstanceId, "Export", msg.TenantId)
	if err != nil {
		log.Warn(ctx, "instance %s create export task record failed %v", a.state.DataSource.InstanceId, err)
		ctx.Respond(&shared.DataMigrationFailed{
			ErrorMessage: err.Error(),
		})
		return err
	}
	sqlResultTasks, err := a.MigRepo.ListActiveTasks(ctx, a.state.DataSource.InstanceId, "SqlResultExport", msg.TenantId)
	if err != nil {
		log.Warn(ctx, "instance %s create sqlResultExport task record failed %v", a.state.DataSource.InstanceId, err)
		ctx.Respond(&shared.DataMigrationFailed{
			ErrorMessage: err.Error(),
		})
		return err
	}
	if exportTaskInfos.Total >= 1 || sqlResultTasks.Total >= 1 {
		errMsg := fmt.Sprintf("Not supporting more than 1 data export task for instance %s", a.state.DataSource.InstanceId)
		log.Warn(ctx, errMsg)
		ctx.Respond(&shared.DataMigrationFailed{
			ErrorMessage: errMsg,
		})
		return fmt.Errorf("not supporting more than 1 data export task")
	}
	return nil
}

func (a *SessionActor) CreateDbExportTask(ctx types.Context, msg *shared.CreateDbExportTaskReq) {
	ctx.WithValue("biz-context", &fwctx.BizContext{
		LogID: msg.GetLogID(),
	})
	//校验当前实例有无在执行的导出任务
	if err := a.CheckExportRunningTask(ctx, msg); err != nil {
		return
	}
	msg.DataSource = a.state.DataSource
	exportConfig := &entity.ExportConfig{}
	exportConfig.FileType = msg.FileType.String()
	exportConfig.Charset = msg.FileEncoding.String()
	exportConfig.ContentFormat = msg.ContentFormat.String()
	exportConfig.Dbname = msg.DbName
	exportConfig.TableList = msg.TableList
	exportConfig.ExportType = msg.ExportType
	exportConfig.AdvancedOptions = msg.AdvancedOptions
	exportConfig.User = a.state.DataSource.User
	exportConfig.Password = a.state.DataSource.Password
	exportConfig.Structures = msg.Structures
	exportConfig.SqlText = msg.SqlText
	describeInstanceAddressReq := &datasource.DescribeInstanceAddressReq{
		Type:       a.state.DataSource.Type,
		InstanceId: a.state.DataSource.InstanceId,
		LinkType:   shared.Volc,
		NodeType:   model.NodeType_Secondary.String(),
	}
	resp, err := a.sources.DescribeInstanceAddress(ctx, describeInstanceAddressReq)
	if err != nil {
		ctx.Respond(&shared.DataMigrationFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	exportConfig.IP = resp.IP
	exportConfig.Port = int(resp.Port)
	exportConfig.TempPath = "/app/dm/"
	exportConfig.TosAK = msg.AK
	exportConfig.TosSK = msg.SK
	exportConfig.TosToken = msg.Token
	exportConfig.TosBucket = msg.TosBucket

	if msg.FileType == shared.CLOUD_DOC {
		if err = a.initCloudDoc(ctx, exportConfig, msg); err != nil {
			return
		}
	}

	if msg.FileType.String() == model.FileType_CSV.String() {
		exportConfig.CsvFirstRowIsColumnDef = msg.CsvFirstRowIsColumnDef
	}
	taskId, err := a.idSvc.NextID(ctx)
	if err != nil {
		ctx.Respond(&shared.DataMigrationFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	var objectName string
	exportedFileName := fmt.Sprintf("%s-%s-%d.%s", msg.DataSource.InstanceId, msg.DbName, taskId, strings.ToLower(msg.FileType.String()))
	if msg.UserID == "" {
		objectName = fmt.Sprintf("%s/%s/export/%s", msg.TenantId, msg.DataSource.InstanceId, exportedFileName)
	} else {
		objectName = fmt.Sprintf("%s/%s/%s/export/%s", msg.TenantId, msg.DataSource.InstanceId, msg.UserID, exportedFileName)
	}
	if strings.TrimSpace(msg.TosObject) != "" {
		objectName = msg.TosObject
	}
	exportConfig.TargetName = objectName
	Config, err := json.Marshal(exportConfig)
	if err != nil {
		log.Warn(ctx, "task %s json marshal failed:%+v", taskId, err)
		ctx.Respond(&shared.DataMigrationFailed{
			ErrorMessage: err.Error(),
		})
		return
	}

	convertedTask := &entity.MigrationTask{
		ID:       taskId,
		UserID:   msg.UserID,
		UserName: msg.UserName,
		// 因为Task表中Type中只设计了导入和导出，不方便细分类型
		// 所以我们直接在这里加细分类型，将原来的类型看做细分类型，`Export`不再表示导出，而是表示按库表导出
		// 即对原有的内容不进行修改，只新增
		Type:        a.getExportTaskType(msg.ExportType),
		TenantID:    msg.GetTenantId(),
		InstanceID:  msg.DataSource.InstanceId,
		ObjectName:  objectName,
		DBType:      msg.DataSource.Type.String(),
		DbName:      msg.DbName,
		CreatedAt:   msg.CreateTime,
		ExecutedAt:  msg.ExecuteTime,
		Tables:      biz_conv.StringSliceToString(msg.TableList),
		Description: msg.Comment,
		Config:      string(Config),
		ClusterName: msg.KubeCluster,
		SqlText:     msg.SqlText,
	}
	if msg.FromType == shared.FROM_INNER {
		convertedTask.FromType = int8(shared.FROM_INNER)
	}
	if err := a.MigRepo.CreateTask(ctx, convertedTask); err != nil {
		errMsg := fmt.Sprintf("instance %s create export task record failed %v", convertedTask.InstanceID, err)
		log.Warn(ctx, errMsg)
		ctx.Respond(&shared.DataMigrationFailed{
			ErrorMessage: errMsg,
		})
		return
	}
	actorName := strings.Join([]string{convertedTask.DBType, convertedTask.InstanceID, msg.KubeCluster, utils.Int64ToStr(taskId)}, ">")
	actorResp, err := ctx.ClientOf(consts.TaskActorKind).Call(ctx, actorName, msg)
	if err != nil {
		log.Info(ctx, "actor %s executed failed:%v", actorName, err)
		ctx.Respond(&shared.DataMigrationFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	if _, ok := actorResp.(error); ok {
		//如果创建任务失败，直接保存失败的操作记录
		a.OperateRecordService.CreateConsoleRecordByImportAndExportTask(ctx, model.ConsoleOperationStatus_FAILED, model.ConsoleOperationType_DATA_EXPORT, *convertedTask, (*convertedTask).ID)
		ctx.Respond(&shared.DataMigrationFailed{})
		return
	}

	//如果创建任务成功，则保存一条处于执行中的操作记录
	a.OperateRecordService.CreateConsoleRecordByImportAndExportTask(ctx, model.ConsoleOperationStatus_ECECUTION, model.ConsoleOperationType_DATA_EXPORT, *convertedTask, (*convertedTask).ID)
	ctx.Respond(&shared.CreateDbExportTaskResp{
		TaskId: taskId,
	})
}

func (a *SessionActor) initCloudDoc(ctx types.Context, exportConfig *entity.ExportConfig, msg *shared.CreateDbExportTaskReq) error {
	c3Cfg := a.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	exportConfig.AppId = c3Cfg.AppId
	exportConfig.AppSecret = c3Cfg.AppSecret

	var client = lark.NewClient(c3Cfg.AppId, c3Cfg.AppSecret) // 默认配置为自建应用

	exportConfig.UserName = msg.UserName
	folderToken, err := a.getFolderToken(ctx, client, msg)
	if err != nil {
		ctx.Respond(consts.ErrorOf(model.ErrorCode_InternalError))
		return err
	}
	exportConfig.ChildFolderToken = folderToken
	// 创建子文件，后面需要用子文件来生成url
	fileToken, err := a.createFile(ctx, client, exportConfig.ChildFolderToken, msg.ExportType)
	if err != nil {
		ctx.Respond(consts.ErrorOf(model.ErrorCode_InternalError))
		return err
	}
	// 给子文件授用户权限
	if err = a.addTablePermission(ctx, client, fileToken, exportConfig.UserName); err != nil {
		ctx.Respond(consts.ErrorOf(model.ErrorCode_InternalError))
		return err
	}
	exportConfig.CloudFileToken = fileToken
	return nil
}

func (a *SessionActor) getFolderToken(ctx types.Context, client *lark.Client, msg *shared.CreateDbExportTaskReq) (string, error) {
	cnf := a.cnf.Get(ctx)
	folderName := "RDS_" + msg.UserName
	// 1.找文件夹下面是否有用户子文件夹
	childFolderToken, err := a.findChildFolder(ctx, msg.UserName)
	if err != nil {
		return "", err
	}
	if childFolderToken != "" {
		return childFolderToken, nil
	}
	// 2.如果没有，就新建一个
	token, err := a.createFolder(ctx, client, cnf.CloudFolderToken, folderName)
	if err != nil {
		return "", err
	}
	err = a.addFolderPermission(ctx, client, token, msg.UserName)
	if err != nil {
		return "", err
	}
	userToken := &entity.UserFolderToken{
		UserName:    msg.UserName,
		TenantId:    msg.TenantId,
		UserId:      msg.UserID,
		FolderToken: token,
	}
	err = a.MigRepo.CreateUserToken(ctx, userToken)
	if err != nil {
		log.Warn(ctx, "CreateUserToken error: %s", err.Error())
	}
	return token, err
}

func (a *SessionActor) findChildFolder(ctx types.Context, userName string) (string, error) {
	res, err := a.MigRepo.GetUserToken(ctx, userName)
	if err != nil {
		log.Warn(ctx, "findChildFolder error: %s", err.Error())
		return "", err
	}
	return res.FolderToken, nil
}

func (a *SessionActor) createFolder(ctx types.Context, client *lark.Client, folderToken string, folderName string) (string, error) {
	body := &larkdrive.CreateFolderFileReqBody{
		Name:        &folderName,
		FolderToken: &folderToken,
	}
	req := larkdrive.NewCreateFolderFileReqBuilder().Body(body).Build()
	resp, err := client.Drive.File.CreateFolder(ctx, req)
	if err != nil {
		log.Warn(ctx, "create folder error: %s", err.Error())
		return "", err
	}
	if !resp.Success() {
		log.Warn(ctx, "create folder not success: %s", utils.Show(resp))
		return "", fmt.Errorf(utils.Show(resp))
	}
	return *resp.Data.Token, nil
}

func (a *SessionActor) createFile(ctx types.Context, client *lark.Client, folderToken string, exportType string) (string, error) {
	title := "表结构导出"
	if exportType == model.ExportType_SqlResultSet.String() {
		title = "sql结果集导出"
	}
	sheet := larksheets.NewSpreadsheetBuilder().FolderToken(folderToken).Title(title).Build()

	createSheetReq := larksheets.NewCreateSpreadsheetReqBuilder().Spreadsheet(sheet).Build()

	createSheetResp, err := client.Sheets.Spreadsheet.Create(ctx, createSheetReq)

	if err != nil {
		log.Warn(ctx, "request cloud create sheet fail: %s", err.Error())
		return "", err
	}
	if !createSheetResp.Success() {
		log.Warn(ctx, "client.Bitable.App.Create failed, code: %d, msg: %s, log_id: %s",
			createSheetResp.Code, createSheetResp.Msg, createSheetResp.RequestId())
		return "", err
	}

	return *createSheetResp.Data.Spreadsheet.SpreadsheetToken, nil
}

func (a *SessionActor) addFolderPermission(ctx types.Context, client *lark.Client, folderToken string, userName string) error {
	userEmail := []string{"@bytedance.com", "@dcarlife.com"}

	for _, email := range userEmail {
		memberId := userName + email

		log.Info(ctx, "start addFolderPermission memberId: %s", memberId)
		baseMember := larkdrive.NewBaseMemberBuilder().MemberId(memberId).Type("user").MemberType("email").Perm("edit").Build()

		req := larkdrive.NewCreatePermissionMemberReqBuilder().
			Token(folderToken).
			Type("folder").
			NeedNotification(false).BaseMember(baseMember).Build()

		resp, err := client.Drive.PermissionMember.Create(ctx, req)
		if err != nil {
			log.Warn(ctx, "cloud file update folder user permission error: %s", err.Error())
			continue
		}
		if resp.StatusCode != 200 {
			log.Warn(ctx, "cloud file update folder user permission error, status code is not 200 resp: %s", utils.Show(resp))
			continue
		}
		log.Info(ctx, "addFolderPermission success memberId: %s", memberId)
	}
	return nil
}

func (a *SessionActor) addTablePermission(ctx types.Context, client *lark.Client, sheetToken string, userName string) error {
	userEmail := []string{"@bytedance.com", "@dcarlife.com"}

	for _, email := range userEmail {
		memberId := userName + email
		log.Info(ctx, "start addTablePermission memberId: %s", memberId)
		baseMember := larkdrive.NewBaseMemberBuilder().MemberId(memberId).Type("user").MemberType("email").Perm("full_access").Build()

		req := larkdrive.NewUpdatePermissionMemberReqBuilder().
			Token(sheetToken).
			Type("sheet").MemberId(memberId).
			NeedNotification(false).BaseMember(baseMember).Build()

		resp, err := client.Drive.PermissionMember.Update(ctx, req)
		if err != nil {
			log.Warn(ctx, "cloud file update table user permission error: %s", err.Error())
			continue
		}
		if resp.StatusCode != 200 {
			log.Warn(ctx, "cloud file update table user permission error, status code is not 200 resp: %s", utils.Show(resp))
			continue
		}
		log.Info(ctx, "addTablePermission success memberId: %s", memberId)

	}
	return nil
}

func (a *SessionActor) getExportTaskType(exportType string) string {
	switch exportType {
	case model.ExportType_DataBase.String():
		return entity.ExportType
	case model.ExportType_SqlResultSet.String():
		return entity.SqlResultExportType
	}
	return entity.ExportType
}

func (a *SessionActor) CreateDbImportTask(ctx types.Context, msg *shared.CreateDbImportTaskReq) {
	ctx.WithValue("biz-context", &fwctx.BizContext{
		LogID: msg.GetLogID(),
	})
	taskInfos, err := a.MigRepo.ListActiveTasks(ctx, a.state.DataSource.InstanceId, "Import", msg.TenantId)
	if err != nil {
		log.Warn(ctx, "instance %s create import task record failed %v", a.state.DataSource.InstanceId, err)
		ctx.Respond(&shared.DataMigrationFailed{
			ErrorMessage: fmt.Sprintf("instance %s create import task record failed %v", a.state.DataSource.InstanceId, err),
		})
		return
	}
	if taskInfos.Total >= 1 {
		errMsg := fmt.Sprintf("Not supporting more than 1 data import task for instance %s", a.state.DataSource.InstanceId)
		log.Warn(ctx, errMsg)
		ctx.Respond(&shared.DataMigrationFailed{
			ErrorMessage: errMsg,
		})
		return
	}
	cnf := a.cnf.Get(ctx)
	msg.DataSource = a.state.DataSource
	importConfig := &entity.ImportConfig{}
	importConfig.FileType = msg.FileType.String()
	importConfig.Charset = msg.FileEncoding.String()
	importConfig.DbName = msg.DbName
	importConfig.BatchNum = cnf.DataMigToolBatchNum
	var objectName string
	if msg.UserID == "" {
		objectName = fmt.Sprintf("%s/%s/import/%s", msg.TenantId, msg.DataSource.InstanceId, msg.SourceName)
	} else {
		objectName = fmt.Sprintf("%s/%s/%s/import/%s", msg.TenantId, msg.DataSource.InstanceId, msg.UserID, msg.SourceName)
	}
	importConfig.SourceName = objectName
	describeInstanceAddressReq := &datasource.DescribeInstanceAddressReq{
		Type:       a.state.DataSource.Type,
		InstanceId: a.state.DataSource.InstanceId,
		LinkType:   shared.Volc,
		NodeType:   model.NodeType_Primary.String(),
	}

	var resp *datasource.DescribeInstanceAddressResp
	if a.state.DataSource.Type == shared.VeDBMySQL {
		resp, err = a.sources.DescribeInstanceProxyAddress(ctx, describeInstanceAddressReq)
		if err != nil {
			ctx.Respond(&shared.DataMigrationFailed{
				ErrorMessage: err.Error(),
			})
			return
		}
		log.Info(ctx, "import VeDB instance, use proxy, instanceId:%s, ip:%s port:%d", a.state.DataSource.InstanceId, resp.IP, resp.Port)
	} else {
		resp, err = a.sources.DescribeInstanceAddress(ctx, describeInstanceAddressReq)
		if err != nil {
			ctx.Respond(&shared.DataMigrationFailed{
				ErrorMessage: err.Error(),
			})
			return
		}
	}
	importConfig.IP = resp.IP
	importConfig.Port = int(resp.Port)
	importConfig.User = a.state.DataSource.User
	importConfig.Password = a.state.DataSource.Password
	importConfig.IgnoreError = msg.IgnoreError
	//禁用的sql语法
	importConfig.BlackList = strings.Split(cnf.DisableImportSqlStatementList, ",")
	importConfig.TempPath = "/app/dm/" //临时文件保存目录
	importConfig.TosAK = msg.AK
	importConfig.TosSK = msg.SK
	importConfig.TosToken = msg.Token
	if msg.FileType.String() == model.FileType_CSV.String() {
		importConfig.CsvFirstRowIsColumnDef = msg.CsvFirstRowIsColumnDef
		importConfig.CsvInputType = msg.InsertType.String()
		importConfig.TableName = msg.TableName
	}
	Config, err := json.Marshal(importConfig)
	if err != nil {
		ctx.Respond(&shared.DataMigrationFailed{})
		return
	}
	taskId, err := a.idSvc.NextID(ctx)
	if err != nil {
		ctx.Respond(&shared.DataMigrationFailed{})
		return
	}
	// insert task info into metaDB
	filePath := a.GetImportTosUrl(ctx, objectName)
	convertedTask := &entity.MigrationTask{
		ID:          taskId,
		UserID:      msg.UserID,
		UserName:    msg.UserName,
		Type:        "Import",
		InstanceID:  msg.DataSource.InstanceId,
		TenantID:    msg.GetTenantId(),
		DBType:      msg.DataSource.Type.String(),
		DbName:      msg.DbName,
		ObjectName:  objectName,
		CreatedAt:   msg.CreateTime,
		ExecutedAt:  msg.ExecuteTime,
		Tables:      msg.TableName,
		Description: msg.Comment,
		Config:      string(Config),
		ClusterName: msg.KubeCluster,
		SqlText:     filePath,
	}
	if msg.IgnoreError {
		convertedTask.IgnoreError = 1
	} else {
		convertedTask.IgnoreError = 0
	}
	if err := a.MigRepo.CreateTask(ctx, convertedTask); err != nil {
		log.Warn(ctx, "create import task record failed %v", err)
		ctx.Respond(&shared.DataMigrationFailed{})
		return
	}
	log.Info(ctx, "create import task record succeed %d", taskId)
	// TODO taskActor name equal taskID
	actorName := strings.Join([]string{convertedTask.DBType, convertedTask.InstanceID, msg.KubeCluster, utils.Int64ToStr(taskId)}, ">")
	actorResp, err := ctx.ClientOf(consts.TaskActorKind).Call(ctx, actorName, msg)
	log.Info(ctx, "received response from task actor")
	if err != nil {
		log.Warn(ctx, "actor %s executed failed:%v", actorName, err)
		ctx.Respond(&shared.DataMigrationFailed{})
		return
	}
	if _, ok := actorResp.(error); ok {
		//如果创建任务失败，直接保存失败的操作记录
		a.OperateRecordService.CreateConsoleRecordByImportAndExportTask(ctx, model.ConsoleOperationStatus_FAILED, model.ConsoleOperationType_DATA_IMPORT, *convertedTask, (*convertedTask).ID)
		ctx.Respond(&shared.DataMigrationFailed{})
		return
	}

	//如果创建任务成功，则保存一条处于执行中的操作记录
	a.OperateRecordService.CreateConsoleRecordByImportAndExportTask(ctx, model.ConsoleOperationStatus_ECECUTION, model.ConsoleOperationType_DATA_IMPORT, *convertedTask, (*convertedTask).ID)
	ctx.Respond(&shared.CreateDbImportTaskResp{
		TaskId: taskId,
	})
}

func (a *SessionActor) GetImportTosUrl(ctx types.Context, objectName string) string {

	tosClient, err := a.initTOSConnection(ctx)
	if err != nil {
		log.Warn(ctx, "inti tos connection error:%s", err.Error())
		return ""
	}
	bucketName := a.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace).TOSBucketName
	url, err := tosClient.DescribeDownloadUrl(ctx, bucketName, objectName)
	if err != nil {
		log.Warn(ctx, "DescribeDownloadUrl error:%s", err.Error())
		return ""
	}
	return url
}

func (a *SessionActor) initTOSConnection(ctx context.Context) (tos.Client, error) {
	var connectionInfo *tos.ConnectionInfo
	c3Cfg := a.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	tosAK := c3Cfg.TOSServiceAccessKey
	tosSK := c3Cfg.TOSServiceSecretKey
	tosRegion := c3Cfg.TOSServiceRegion
	tosEndpoint := c3Cfg.TOSServiceEndpoint
	tosEndpoint = strings.Replace(tosEndpoint, "-s3", "", 1)
	connectionInfo = &tos.ConnectionInfo{
		Endpoint:        tosEndpoint,
		AccessKeyID:     tosAK,
		AccessKeySecret: tosSK,
		Region:          tosRegion,
	}

	client := tos.NewTOSClient(ctx, connectionInfo)
	return client, nil
}

func (a *SessionActor) listCollections(ctx types.Context, msg *shared.ListCollections) {
	req := &datasource.ListCollectionsReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
	}
	resp, err := a.sources.ListCollections(ctx, req)
	if err != nil {
		log.Warn(ctx, "list collection fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.CollectionNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.CollectionsInfo{
		Total: resp.Total,
		Items: resp.Items,
	})
}

func (a *SessionActor) listIndexs(ctx types.Context, msg *shared.ListIndexs) {
	req := &datasource.ListIndexesReq{
		Source:  a.state.DataSource,
		DB:      msg.Db,
		Col:     msg.Col,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
		Idx:     msg.Idx,
	}
	resp, err := a.sources.ListIndexs(ctx, req)
	if err != nil {
		log.Warn(ctx, "list table fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.IndexNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	if resp.IdxInfo == nil {
		ctx.Respond(&shared.IndexsInfo{
			Total:     resp.Total,
			Items:     resp.Items,
			IndexInfo: nil,
		})
		return
	}
	// 这里需要做一层转换，把返回的map[name]interface{}转换成一个struct
	mongoIndexInfo := &shared.MongoIndexInfo{
		Name:               resp.IdxInfo.Name,
		Ns:                 resp.IdxInfo.Ns,
		Background:         resp.IdxInfo.Background,
		Unique:             resp.IdxInfo.Unique,
		Sparse:             resp.IdxInfo.Sparse,
		ExpireAfterSeconds: int32(resp.IdxInfo.ExpireAfterSeconds),
		Key:                make([]*shared.IndexKey, 0),
	}
	for k, v := range resp.IdxInfo.Key {
		switch v.(type) {
		case float64:
			mongoIndexInfo.Key = append(mongoIndexInfo.Key, &shared.IndexKey{
				Name:      k,
				ValueType: "int",
				Value:     fmt.Sprintf("%.0f", v.(float64)),
			})
		case int32:
			mongoIndexInfo.Key = append(mongoIndexInfo.Key, &shared.IndexKey{
				Name:      k,
				ValueType: "int",
				Value:     fmt.Sprintf("%d", v.(int32)),
			})
		case string:
			mongoIndexInfo.Key = append(mongoIndexInfo.Key, &shared.IndexKey{
				Name:      k,
				ValueType: "string",
				Value:     v.(string),
			})
		default:
			log.Warn(ctx, "list idx fail %v", err)
			ctx.Respond(&shared.DataSourceOpFailed{
				Error:        shared.UnknownOpError,
				ErrorMessage: err.Error(),
			})
			return
		}
	}
	ctx.Respond(&shared.IndexsInfo{
		Total:     resp.Total,
		Items:     resp.Items,
		IndexInfo: mongoIndexInfo,
	})
}

func (a *SessionActor) listMongoDBs(ctx types.Context, msg *shared.ListMongoDBs) {
	req := &datasource.ListMongoDBsReq{
		Source:  a.state.DataSource,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
		Keyword: msg.Keyword,
	}
	resp, err := a.sources.ListMongoDBs(ctx, req)
	if err != nil {
		log.Warn(ctx, "list MongoDB fail %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.MongoDBNotExist,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp.MongoDBsInfo)
}

func (a *SessionActor) describeTableSpace(ctx types.Context, msg *shared.DescribeTableSpace) {
	log.Info(ctx, "DescribeTableSpace-enter1-msg %s", utils.Show(msg))
	req := a.getDescribeTableSpaceReq(msg)
	log.Info(ctx, "DescribeTableSpace-enter1-describeTableSpace %s", utils.Show(req))
	resp, err := a.sources.DescribeTableSpace(ctx, req)
	if err != nil {
		log.Warn(ctx, "获取 表空间信息失败 %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp)
}

func (a *SessionActor) describeTableColumn(ctx types.Context, msg *shared.DescribeTableColumn) {
	pageSize := msg.PageSize
	if pageSize == 0 {
		pageSize = 10
	}
	pageNum := msg.PageNumber
	if pageNum == 0 {
		pageNum = 1
	}
	req := &datasource.DescribeTableInfoReq{
		Source:     a.state.DataSource,
		InstanceId: msg.InstanceId,
		Database:   msg.Database,
		Schema:     msg.Schema,
		Table:      msg.Table,
		Offset:     (pageNum - 1) * pageSize,
		Limit:      a.getTableSpaceReqLimit(pageNum, pageSize),
	}
	resp, err := a.sources.DescribeTableColumn(ctx, req)
	if err != nil {
		log.Warn(ctx, "获取 表字段失败 %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp)
}

func (a *SessionActor) describeTableIndex(ctx types.Context, msg *shared.DescribeTableIndex) {
	pageSize := msg.PageSize
	if pageSize == 0 {
		pageSize = 10
	}
	pageNum := msg.PageNumber
	if pageNum == 0 {
		pageNum = 1
	}
	req := &datasource.DescribeTableInfoReq{
		Source:     a.state.DataSource,
		InstanceId: msg.InstanceId,
		Database:   msg.Database,
		Schema:     msg.Schema,
		Table:      msg.Table,
		Offset:     (pageNum - 1) * pageSize,
		Limit:      a.getTableSpaceReqLimit(pageNum, pageSize),
	}
	resp, err := a.sources.DescribeTableIndex(ctx, req)
	if err != nil {
		log.Warn(ctx, "获取 表索引失败 %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(resp)
}

func (a *SessionActor) getDescribeTableSpaceReq(msg *shared.DescribeTableSpace) *datasource.DescribeTableSpaceReq {
	pageSize := msg.PageSize
	if pageSize == 0 {
		pageSize = 10
	}
	pageNum := msg.PageNumber
	if pageNum == 0 {
		pageNum = 1
	}
	orderItem := msg.OrderItem
	if orderItem == "" {
		orderItem = "TableSpace"
	}
	orderRule := msg.OrderRule
	if orderRule == "" {
		orderRule = "ASC"
	}
	return &datasource.DescribeTableSpaceReq{
		Source:     a.state.DataSource,
		InstanceId: msg.InstanceId,
		Product:    msg.Product,
		RegionId:   msg.RegionId,
		Offset:     (pageNum - 1) * pageSize,
		Limit:      a.getTableSpaceReqLimit(pageNum, pageSize),
		OrderItem:  orderItem,
		OrderRule:  orderRule,
		Database:   msg.Database,
		TableName:  msg.TableName,
	}
}

func (a *SessionActor) getTableSpaceReqLimit(pageNum int32, pageSize int32) int32 {
	if pageNum*pageSize <= 2000 {
		return pageSize
	} else if pageSize*(pageNum-1) >= 2000 {
		return 0
	}
	return 2000 - pageSize*(pageNum-1)
}

func (a *SessionActor) ExecuteCCL(ctx types.Context, msg *shared.ExecuteCCLReq) {
	req := &datasource.ExecuteCCLReq{
		Commend:  msg.Command,
		Type:     msg.DSType,
		LinkType: msg.LinkType,
		Source:   a.state.DataSource,
	}
	resp, err := a.sources.ExecuteCCL(ctx, req)
	if err != nil {
		log.Warn(ctx, "ExecuteCCL fail %v", err)
		ctx.Respond(consts.ErrorOf(model.ErrorCode_InternalError))
		return
	}
	ctx.Respond(&shared.ExecuteCCLResp{
		Result: resp.Result,
	})
}

func (a *SessionActor) CCLShow(ctx types.Context, msg *shared.CCLShow) {
	req := &datasource.CCLShowReq{
		Commend:  msg.Command,
		Type:     msg.DSType,
		LinkType: msg.LinkType,
		Source:   a.state.DataSource,
	}
	resp, err := a.sources.CCLShow(ctx, req)
	if err != nil {
		log.Warn(ctx, "ExecuteCCL fail %v", err)
		ctx.Respond(consts.ErrorOf(model.ErrorCode_InternalError))
		return
	}
	log.Info(ctx, "ExecuteCCL S %v", resp.Result)
	ctx.Respond(&shared.CCLRuleInfoResp{
		CCLRule: resp.Result,
	})
}
