package actors

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/conv"
	k8sSvc "code.byted.org/infcs/dbw-mgr/biz/service/audit/k8s"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/protoactor-go/actor"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/coocood/freecache"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls"
	"code.byted.org/infcs/dbw-mgr/biz/shared"

	"go.uber.org/dig"
)

const (
	// 每个instance actor分配10M的空间做缓存
	freeCacheSize = 10 * 1024 * 1024
	// 单位: second
	keyTimeout = 300
	// 单位: min
	actorTimeout = 30
)

type NewLogAnalysisActorIn struct {
	dig.In
	Conf   config.ConfigProvider
	C3Conf c3.ConfigProvider
	Loc    location.Location
	K8sPro k8sSvc.AuditResClientProvider
}

func NewLogAnalysisActor(in NewLogAnalysisActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.LogAnalysisActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &LogAnalysisActor{
				conf:   in.Conf,
				c3Conf: in.C3Conf,
				loc:    in.Loc,
				k8sPro: in.K8sPro,
			}
		}),
	}
}

type LogAnalysisActor struct {
	tlsClient tls.Client
	cache     *freecache.Cache
	conf      config.ConfigProvider
	c3Conf    c3.ConfigProvider
	loc       location.Location
	k8sPro    k8sSvc.AuditResClientProvider
	sourceDS  shared.DataSourceType
}

func (a *LogAnalysisActor) checkTLSClient(ctx types.Context) error {
	if a.tlsClient == nil {
		// actorName为regionId-dsType-instanceId的格式,接收到actor.Started消息后
		// 通过actorName提取dsType instanceId,调用对应的mgr获取TLS连接方式
		log.Info(ctx, "init tls client, name: %s  kind: %s", ctx.GetName(), ctx.GetKind())
		values := strings.Split(ctx.GetName(), ">")
		if len(values) != 3 {
			e := fmt.Errorf("the length of split of actor name %s expect %d, got %d", ctx.GetName(), 3, len(values))
			log.Warn(ctx, "checkTLSClient error: %s", e.Error())
			return e
		}

		regionId := values[0]
		dsType := values[1]
		instanceId := values[2]

		err := a.initTLSConnection(ctx, regionId, dsType, instanceId)
		if err != nil {
			return err
		}
		a.cache = freecache.NewCache(freeCacheSize)
		ctx.SetReceiveTimeout(time.Minute * actorTimeout)
	}

	return nil
}

func (a *LogAnalysisActor) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *shared.DescribeSlowLogTimeSeriesStatsReq:
		if err := a.checkTLSClient(ctx); err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCheckTLSClientFailed,
				ErrorMessage: err.Error(),
			})
			return
		}
		if msg.DataSourceType == shared.MySQLSharding {
			msg.DataSourceType = conv.ToSharedType(conv.GetDsTypeByShardInstanceId(msg.GetInstanceId()))
			a.sourceDS = shared.MySQLSharding
		}
		a.processDescribeSlowLogTimeSeriesStatsReq(ctx, msg)
	case *shared.DescribeAggregateSlowLogsReq:
		if err := a.checkTLSClient(ctx); err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCheckTLSClientFailed,
				ErrorMessage: err.Error(),
			})
			return
		}
		if msg.DataSourceType == shared.MySQLSharding {
			msg.DataSourceType = conv.ToSharedType(conv.GetDsTypeByShardInstanceId(msg.GetInstanceId()))
			a.sourceDS = shared.MySQLSharding
		}
		a.processDescribeAggregateSlowLogsReq(ctx, msg)
	case *shared.CreateSlowLogsExportTaskReq:
		if err := a.checkTLSClient(ctx); err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCheckTLSClientFailed,
				ErrorMessage: err.Error(),
			})
			return
		}
		if msg.DataSourceType == shared.MySQLSharding {
			msg.DataSourceType = conv.ToSharedType(conv.GetDsTypeByShardInstanceId(msg.GetInstanceId()))
			a.sourceDS = shared.MySQLSharding
		}
		a.processCreateSlowLogsExportTask(ctx, msg)
	case *shared.DescribeSlowLogsExportTasksReq:
		if err := a.checkTLSClient(ctx); err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCheckTLSClientFailed,
				ErrorMessage: err.Error(),
			})
			return
		}
		if msg.DataSourceType == shared.MySQLSharding {
			msg.DataSourceType = conv.ToSharedType(conv.GetDsTypeByShardInstanceId(msg.GetInstanceId()))
			a.sourceDS = shared.MySQLSharding
		}
		a.processDescribeSlowLogsExportTasks(ctx, msg)
	case *shared.DescribeLogsDownloadUrlReq:
		if err := a.checkTLSClient(ctx); err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCheckTLSClientFailed,
				ErrorMessage: err.Error(),
			})
			return
		}
		if msg.DataSourceType == shared.MySQLSharding {
			msg.DataSourceType = conv.ToSharedType(conv.GetDsTypeByShardInstanceId(msg.GetInstanceId()))
			a.sourceDS = shared.MySQLSharding
		}
		a.processDescribeLogsDownloadUrl(ctx, msg)
	case *shared.DescribeSlowLogsReq:
		if err := a.checkTLSClient(ctx); err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCheckTLSClientFailed,
				ErrorMessage: err.Error(),
			})
			return
		}
		if msg.DataSourceType == shared.MySQLSharding {
			msg.DataSourceType = conv.ToSharedType(conv.GetDsTypeByShardInstanceId(msg.GetInstanceId()))
			a.sourceDS = shared.MySQLSharding
		}
		a.processDescribeSlowLogsReq(ctx, msg)
	case *shared.DescribeSourceIPsReq:
		if err := a.checkTLSClient(ctx); err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCheckTLSClientFailed,
				ErrorMessage: err.Error(),
			})
			return
		}
		if msg.DataSourceType == shared.MySQLSharding {
			msg.DataSourceType = conv.ToSharedType(conv.GetDsTypeByShardInstanceId(msg.GetInstanceId()))
			a.sourceDS = shared.MySQLSharding
		}
		a.processDescribeSourceIPs(ctx, msg)
	case *shared.DescribeUsersReq:
		if err := a.checkTLSClient(ctx); err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCheckTLSClientFailed,
				ErrorMessage: err.Error(),
			})
			return
		}
		if msg.DataSourceType == shared.MySQLSharding {
			msg.DataSourceType = conv.ToSharedType(conv.GetDsTypeByShardInstanceId(msg.GetInstanceId()))
			a.sourceDS = shared.MySQLSharding
		}
		a.processDescribeUsers(ctx, msg)
	case *shared.DescribeDBsReq:
		if err := a.checkTLSClient(ctx); err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:         shared.ErrCheckTLSClientFailed,
				ErrorMessage: err.Error(),
			})
			return
		}
		a.processDescribeDBs(ctx, msg)
	case *shared.DescribeExampleSQLReq:
		if err := a.checkTLSClient(ctx); err != nil {
			ctx.Respond(&shared.SearchLogsFailed{
				Code:          shared.ErrCheckTLSClientFailed,
				ErrorMessage:  err.Error(),
				StandardError: consts.TranslateStandardErrorToShared(err),
			})
			return
		}
		if msg.DataSourceType == shared.MySQLSharding {
			msg.DataSourceType = conv.ToSharedType(conv.GetDsTypeByShardInstanceId(msg.GetInstanceId()))
			a.sourceDS = shared.MySQLSharding
		}
		a.processDescribeExampleSQLReqReq(ctx, msg)
	case *actor.Stopped:
		if a.cache != nil {
			a.cache.Clear()
		}
	case *actor.ReceiveTimeout:
		log.Info(ctx, "actor %s receive time out, kill self", ctx.Self().String())
		ctx.CancelReceiveTimeout()
		ctx.Stop(ctx.Self())
	}
}

func (a *LogAnalysisActor) initTLSConnection(ctx context.Context, regionId string, dsType string, instanceId string) error {
	var slowLogTopic string
	connectionInfo := &tls.ConnectionInfo{}

	cfg := a.conf.Get(ctx)
	c3Cfg := a.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	tlsEndpoint := cfg.GetVolcTlsEndpoint()
	tlsRegion, _ := cfg.GetVolcTlsRegion()

	tlsAK := c3Cfg.TLSServiceAccessKey
	tlsSK := c3Cfg.TLSServiceSecretKey
	if cfg.EnableNewVersionSlowLogTopic {
		lastChar := instanceId[len(instanceId)-1:] + "$"
		slowLogTopic = a.getTopicId(ctx, dsType, lastChar)
	} else {
		tlsSlowLogTopic := c3Cfg.TLSSlowLogTopic
		topicSet := &datasource.TLSSlowLogTopic{}
		if err := json.Unmarshal([]byte(tlsSlowLogTopic), topicSet); err != nil {
			log.Warn(ctx, " tlsSlowLogTopic unmarshal failed %v", err)
		}
		for _, topic := range topicSet.Topics {
			if topic.InstanceType == dsType {
				slowLogTopic = topic.TopicID
			}
		}
	}
	log.Info(ctx, "current topic is %s", slowLogTopic)

	var (
		dbInternalUsers map[string]string
	)
	internalUsers := make([]string, 0)
	err := json.Unmarshal([]byte(a.conf.Get(ctx).DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
	} else {
		internalUsers = strings.Split(dbInternalUsers[dsType], ",")
	}
	connectionInfo = &tls.ConnectionInfo{
		Endpoint:        tlsEndpoint,
		AccessKeyID:     tlsAK,
		AccessKeySecret: tlsSK,
		Region:          tlsRegion,
		TopicId:         slowLogTopic,
		InternalUsers:   internalUsers,
	}
	log.Info(ctx, "init tls client, connection info: %s", libutils.Show(connectionInfo))
	client := tls.NewTLSClient(connectionInfo, a.conf)
	a.tlsClient = client
	return nil
}

func computeID(ctx context.Context, req interface{}) []byte {
	switch rreq := req.(type) {
	case *shared.DescribeAggregateSlowLogsReq:
		return computeIDFromDescribeAggregateSlowLogsReq(rreq)
	case *shared.DescribeSlowLogsReq:
		slowLogSearchParamHash := computeIDFromSlowLogSearchParam(rreq.GetSearchParam())
		hash := sha256.New()
		hash.Write([]byte{0x01})
		hash.Write([]byte(rreq.GetRegionId()))
		hash.Write([]byte(rreq.GetInstanceId()))
		hash.Write([]byte(rreq.GetDataSourceType().String()))
		hash.Write([]byte(strconv.FormatInt(rreq.GetStartTime(), 10)))
		hash.Write([]byte(strconv.FormatInt(rreq.GetEndTime(), 10)))
		hash.Write([]byte(rreq.OrderBy.String()))
		hash.Write([]byte(rreq.SortBy.String()))
		hash.Write([]byte(rreq.GetNodeId()))
		hash.Write(slowLogSearchParamHash)
		return hash.Sum(nil)
	case *shared.DescribeUsersReq:
		slowLogSearchParamHash := computeIDFromSlowLogSearchParam(rreq.GetSearchParam())
		hash := sha256.New()
		hash.Write([]byte{0x02})
		hash.Write([]byte(rreq.GetRegionId()))
		hash.Write([]byte(rreq.GetInstanceId()))
		hash.Write([]byte(rreq.GetDataSourceType().String()))
		hash.Write([]byte(strconv.FormatInt(rreq.GetStartTime(), 10)))
		hash.Write([]byte(strconv.FormatInt(rreq.GetEndTime(), 10)))
		hash.Write([]byte(rreq.GetNodeId()))
		hash.Write([]byte(rreq.GetNodeId()))
		hash.Write(slowLogSearchParamHash)
		return hash.Sum(nil)
	case *shared.DescribeSourceIPsReq:
		slowLogSearchParamHash := computeIDFromSlowLogSearchParam(rreq.GetSearchParam())
		hash := sha256.New()
		hash.Write([]byte{0x03})
		hash.Write([]byte(rreq.GetRegionId()))
		hash.Write([]byte(rreq.GetInstanceId()))
		hash.Write([]byte(rreq.GetDataSourceType().String()))
		hash.Write([]byte(strconv.FormatInt(rreq.GetStartTime(), 10)))
		hash.Write([]byte(strconv.FormatInt(rreq.GetEndTime(), 10)))
		hash.Write([]byte(rreq.GetNodeId()))
		hash.Write(slowLogSearchParamHash)
		return hash.Sum(nil)
	case *shared.DescribeDBsReq:
		slowLogSearchParamHash := computeIDFromSlowLogSearchParam(rreq.GetSearchParam())
		hash := sha256.New()
		hash.Write([]byte{0x04})
		hash.Write([]byte(rreq.GetRegionId()))
		hash.Write([]byte(rreq.GetInstanceId()))
		hash.Write([]byte(rreq.GetDataSourceType().String()))
		hash.Write([]byte(strconv.FormatInt(rreq.GetStartTime(), 10)))
		hash.Write([]byte(strconv.FormatInt(rreq.GetEndTime(), 10)))
		hash.Write([]byte(rreq.GetNodeId()))
		hash.Write(slowLogSearchParamHash)
		return hash.Sum(nil)
	default:
		log.Warn(ctx, "computeID in logAnalysis actor got a wrong input: %v", req)
		return nil
	}
}

type slowLogsCachePair struct {
	Values []*shared.SlowLog
	Total  int32
}

type aggregateSlowLogsCachePair struct {
	Values []*shared.AggregateSlowLog
	Total  int32
}

type GroupCachePair struct {
	Values []string
	Total  int32
}

func computeIDFromSlowLogSearchParam(searchParam *shared.SlowLogSearchParam) []byte {
	if searchParam == nil {
		return nil
	}
	hash := sha256.New()
	if searchParam.GetSQLTemplate() == "" {
		hash.Write([]byte("1*"))
	} else {
		hash.Write([]byte(searchParam.GetSQLTemplate()))
	}
	if searchParam.GetSQLTemplateID() == "" {
		hash.Write([]byte("2&"))
	} else {
		hash.Write([]byte(searchParam.GetSQLTemplateID()))
	}
	for _, db := range searchParam.GetDBs() {
		hash.Write([]byte(db))
	}
	for _, user := range searchParam.GetUsers() {
		hash.Write([]byte(user))
	}
	for _, sourceIP := range searchParam.GetSourceIPs() {
		hash.Write([]byte(sourceIP))
	}
	if searchParam.GetMinQueryTime() != 0.0 {
		hash.Write([]byte(strconv.FormatFloat(searchParam.GetMinQueryTime(), 'f', 2, 64)))
	}
	if searchParam.GetMaxQueryTime() != 0.0 {
		hash.Write([]byte(strconv.FormatFloat(searchParam.GetMaxQueryTime(), 'f', 2, 64)))
	}

	return hash.Sum(nil)
}

func computeIDFromDescribeAggregateSlowLogsReq(req *shared.DescribeAggregateSlowLogsReq) []byte {
	hash := sha256.New()
	hash.Write([]byte{0x00})
	hash.Write([]byte(req.GetRegionId()))
	hash.Write([]byte(req.GetInstanceId()))
	hash.Write([]byte(req.GetDataSourceType().String()))
	hash.Write([]byte(strconv.FormatInt(req.GetStartTime(), 10)))
	hash.Write([]byte(strconv.FormatInt(req.GetEndTime(), 10)))
	hash.Write([]byte(req.GetSortBy().String()))
	hash.Write([]byte(req.GetOrderBy().String()))

	if req.GetSearchParam() != nil {
		for _, group := range req.GetSearchParam().GetGroupIgnored() {
			hash.Write([]byte(group.String()))
		}
		if len(req.GetSearchParam().GetDBs()) > 0 {
			hash.Write([]byte(strings.Join(req.GetSearchParam().GetDBs(), "*")))
			hash.Write([]byte("*"))
		} else {
			hash.Write([]byte("8*"))
		}
		if len(req.GetSearchParam().GetUsers()) > 0 {
			hash.Write([]byte(strings.Join(req.GetSearchParam().GetUsers(), "&")))
			hash.Write([]byte("&"))
		} else {
			hash.Write([]byte("7&"))
		}
		if len(req.GetSearchParam().GetKeywords()) > 0 {
			hash.Write([]byte(strings.Join(req.GetSearchParam().GetKeywords(), "$")))
			hash.Write([]byte("$"))
		} else {
			hash.Write([]byte("4$"))
		}

		if len(req.GetSearchParam().GetSourceIPs()) > 0 {
			hash.Write([]byte(strings.Join(req.GetSearchParam().GetSourceIPs(), "#")))
			hash.Write([]byte("#"))
		} else {
			hash.Write([]byte("3#"))
		}

		if len(req.GetSearchParam().GetSqlMethod()) > 0 {
			hash.Write([]byte(strings.Join(req.GetSearchParam().GetSqlMethod(), "#")))
			hash.Write([]byte("#"))
		} else {
			hash.Write([]byte("2#"))
		}

		if req.GetSearchParam().GetMinQueryTime() != 0.0 {
			hash.Write([]byte(strconv.FormatFloat(req.GetSearchParam().GetMinQueryTime(), 'f', 2, 64)))
		}
		if req.GetSearchParam().GetMaxQueryTime() != 0.0 {
			hash.Write([]byte(strconv.FormatFloat(req.GetSearchParam().GetMaxQueryTime(), 'f', 2, 64)))
		}
		if req.NodeId != "" {
			hash.Write([]byte(req.NodeId))
		}
		if len(req.GetSearchParam().GetPSMs()) > 0 {
			hash.Write([]byte("psms="))
			hash.Write([]byte(strings.Join(req.GetSearchParam().GetPSMs(), ",")))
		}

		if len(req.GetSearchParam().GetTables()) > 0 {
			hash.Write([]byte("tables="))
			hash.Write([]byte(strings.Join(req.GetSearchParam().GetTables(), ",")))
		}
		if len(req.GetSearchParam().GetSqlFingerprint()) > 0 {
			hash.Write([]byte("sqlfingerprint="))
			hash.Write([]byte(req.GetSearchParam().GetSqlFingerprint()))
		}
	}
	return hash.Sum(nil)
}

func (a *LogAnalysisActor) getTopicId(ctx context.Context, DSType string, lastChar string) string {
	var (
		tlsSlowLogTopicV2 string
		topicId           string
	)
	topicSet := &datasource.TLSSlowLogTopicV2{}
	c3Cfg := a.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	switch DSType {
	case model.DSType_MySQL.String():
		tlsSlowLogTopicV2 = c3Cfg.TLSRdsSlowLogTopicV2
	case model.DSType_MySQLSharding.String():
		tlsSlowLogTopicV2 = c3Cfg.TLSRdsSlowLogTopicV2
	case model.DSType_MySQLSharding.String():
		tlsSlowLogTopicV2 = c3Cfg.TLSRdsSlowLogTopicV2
	case model.DSType_VeDBMySQL.String():
		tlsSlowLogTopicV2 = c3Cfg.TLSNdbSlowLogTopicV2
	case model.DSType_Postgres.String():
		tlsSlowLogTopicV2 = c3Cfg.TLSPgSlowLogTopicV2
	case model.DSType_MetaMySQL.String():
		tlsSlowLogTopicV2 = c3Cfg.TLSMetaSlowLogTopicV2
	case model.DSType_MetaRDS.String():
		tlsSlowLogTopicV2 = c3Cfg.TLSMetaSlowLogTopicV2
	default:
		log.Warn(ctx, "Not support instance type %s", DSType)
		return topicId
	}
	if err := json.Unmarshal([]byte(tlsSlowLogTopicV2), topicSet); err != nil {
		log.Warn(ctx, " tlsSlowLogTopic unmarshal failed %v", err)
		return topicId
	}
	for _, topic := range topicSet.Topics {
		for _, item := range topic.TopicList {
			if item.MatchedRegex == lastChar {
				topicId = item.TopicID
				return topicId
			}
		}
	}
	return topicId
}
