package inspection

import "testing"

func Test_getCronTime(t *testing.T) {
	type args struct {
		inspectionExecutableStartTime int64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test1",
			args: args{inspectionExecutableStartTime: 86400000},
			want: "0 24 * * *",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getCronTime(tt.args.inspectionExecutableStartTime); got != tt.want {
				t.Errorf("getCronTime() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isTimeInTimeWindow(t *testing.T) {
	type args struct {
		now             int64
		timeWindowStart int64
		timeWindowEnd   int64
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "test1",
			args: args{
				0,
				1,
				1,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isTimeInTimeWindow(tt.args.now, tt.args.timeWindowStart, tt.args.timeWindowEnd); got != tt.want {
				t.Errorf("isTimeInTimeWindow() = %v, want %v", got, tt.want)
			}
		})
	}
}
