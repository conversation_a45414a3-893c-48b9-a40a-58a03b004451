package inspection

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/dslibmocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/robfig/cron/v3"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
	"testing"
	"time"
)

func mockCronActor() *CronActor {
	return &CronActor{
		state:       &cronActorState{},
		cronMap:     map[string]int{},
		cron:        cron.New(),
		repo:        &repository.InspectionRepoImpl{},
		actorClient: &dslibmocks.MockActorClient{},
	}
}
func TestGetState(t *testing.T) {
	actor := mockCronActor()
	flag := actor.GetState()
	assert.NotNil(t, flag)
}

func TestNewCronActor(t *testing.T) {
	assert.NotNil(t, NewCronActor(NewCronActorIn{}))
}

func TestIsInstanceConfigInMeta(t *testing.T) {
	actor := mockCronActor()
	mock := mockey.Mock(log.Log).Return().Build()
	defer mock.UnPatch()

	mock1 := mockey.Mock((*repository.InspectionRepoImpl).GetConfig).Return(nil, gorm.ErrRecordNotFound).Build()
	flag1 := actor.isInstanceConfigInMeta(&mocks.MockContext{}, model.InstanceType_MySQL.String(), "123", "123")
	assert.False(t, flag1)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*repository.InspectionRepoImpl).GetConfig).Return(nil, fmt.Errorf("test")).Build()
	flag2 := actor.isInstanceConfigInMeta(&mocks.MockContext{}, model.InstanceType_MySQL.String(), "123", "123")
	assert.False(t, flag2)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*repository.InspectionRepoImpl).GetConfig).Return(&entity.InspectionConfig{}, nil).Build()
	flag3 := actor.isInstanceConfigInMeta(&mocks.MockContext{}, model.InstanceType_MySQL.String(), "123", "123")
	assert.False(t, flag3)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*repository.InspectionRepoImpl).GetConfig).Return(&entity.InspectionConfig{InstanceId: "123"}, nil).Build()
	flag4 := actor.isInstanceConfigInMeta(&mocks.MockContext{}, model.InstanceType_MySQL.String(), "123", "123")
	assert.True(t, flag4)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)
}

func TestModifyCronTask(t *testing.T) {
	actor := mockCronActor()
	mock := mockey.Mock(log.Log).Return().Build()
	defer mock.UnPatch()

	mock1 := mockey.Mock((*repository.InspectionRepoImpl).UpdateConfig).Return(gorm.ErrRecordNotFound).Build()
	err1 := actor.modifyCronTask(&mocks.MockContext{}, &shared.ModifyCronTask{InstanceIds: []string{"1", "2"}})
	assert.NotNil(t, err1)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*repository.InspectionRepoImpl).UpdateConfig).Return(nil).Build()
	mock3 := mockey.Mock(actor.deleteCronTask).Return(nil).Build()
	err2 := actor.modifyCronTask(&mocks.MockContext{}, &shared.ModifyCronTask{InstanceIds: []string{"1", "2"}, IsOpen: 1})
	assert.Nil(t, err2)
	mock2.UnPatch()
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*repository.InspectionRepoImpl).UpdateConfig).Return(nil).Build()
	mock5 := mockey.Mock(actor.createCronTask).Return(nil).Build()
	mock6 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	err4 := actor.modifyCronTask(&mocks.MockContext{}, &shared.ModifyCronTask{InstanceIds: []string{"1", "2"}, IsOpen: 0})
	assert.Nil(t, err4)
	mock4.UnPatch()
	mock5.UnPatch()
	mock6.UnPatch()
	time.Sleep(100 * time.Millisecond)

}
