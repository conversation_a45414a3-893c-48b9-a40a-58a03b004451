package pdf

import (
	util "code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
	"github.com/vicanso/go-charts/v2"
)

func CreateSlowSqlTable(ctx context.Context, resultInfo *util.InspectionResultInfo) (int, error) {
	f, err := getCNFont() // 用自己的字体
	if err != nil {
		log.Warn(ctx, "getCNFont error:%s", err.Error())
		return 0, err
	}
	charts.SetDefaultWidth(1200)
	charts.SetDefaultHeight(300)
	charts.SetDefaultFont(f)
	header := []string{"SQL模板", "数据库", "执行User", "执行次数", "总耗时", "平均执行时间(s)"}
	data := formatSlowLogData(resultInfo)

	spans := map[int]int{0: 2, 1: 1}
	p, err := charts.TableRender(header, data, spans)
	if err != nil {
		log.Warn(ctx, "tableRender error:%s", err.Error())
		return 0, err
	}
	buf, err := p.Bytes()
	if err != nil {
		log.Warn(ctx, "bytes error:%s", err.Error())
		return 0, err
	}

	filePath := GetInspectionFilePath(resultInfo)

	err = writeFile(ctx, buf, filePath, SlowLogFileName)
	if err != nil {
		log.Warn(ctx, "writeFile error:%s", err.Error())
		return 0, err
	}
	return len(data) + 1, nil
}

func formatSlowLogData(resultInfo *util.InspectionResultInfo) [][]string {
	var data [][]string
	if resultInfo.SlowLogs == nil || len(resultInfo.SlowLogs) == 0 {
		return data
	}
	for _, slowLog := range resultInfo.SlowLogs {
		var slowLogData []string
		slowLogData = append(slowLogData, interceptStr(slowLog.SqlTemplate, 50))
		slowLogData = append(slowLogData, interceptStr(slowLog.DBName, 28))
		slowLogData = append(slowLogData, interceptStr(slowLog.ExecuteUser, 28))

		slowLogData = append(slowLogData, fmt.Sprintf("%d", slowLog.ExecuteCount))
		slowLogData = append(slowLogData, fmt.Sprintf("%.2f", slowLog.TotalQueryTime))
		slowLogData = append(slowLogData, fmt.Sprintf("%.2f", slowLog.AvgQueryTime))

		data = append(data, slowLogData)
	}
	return data
}

func interceptStr(str string, length int) string {
	if len(str) < 10 || length < 5 {
		return str
	}
	if len(str) > length {
		return str[0:length-3] + "..."
	} else {
		return str
	}
}
