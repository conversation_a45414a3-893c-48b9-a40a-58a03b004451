package pdf

import (
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/vicanso/go-charts/v2"
	"testing"
	"time"
)

func TestCreateAllItemMetric(t *testing.T) {
	ctx := context.Background()
	resultInfo := mockInspectionResultInfo()

	mock1 := mockey.Mock(writeFile).Return(nil).Build()
	mock2 := mockey.Mock(CreateItemMetricPngBytes).Return([]byte{}, nil).Build()
	defer mock2.UnPatch()
	_ = createAllItemMetric(ctx, resultInfo)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock(writeFile).Return(fmt.Errorf("test")).Build()
	_ = createAllItemMetric(ctx, resultInfo)
	mock3.UnPatch()
}

func TestCreateItemMetricPngBytes(t *testing.T) {
	ctx := context.Background()
	resultInfo := mockInspectionResultInfo()

	mock1 := mockey.Mock(getCNFont).Return(nil, fmt.Errorf("tetl")).Build()
	_, _ = CreateItemMetricPngBytes(ctx, resultInfo.Report.DataMetrics[0])
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock(getCNFont).Return(nil, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock(charts.SetDefaultFont).Return().Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock(writeFile).Return(fmt.Errorf("test")).Build()
	_, _ = CreateItemMetricPngBytes(ctx, resultInfo.Report.DataMetrics[0])
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)
}
