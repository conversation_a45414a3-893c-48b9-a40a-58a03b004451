package pdf

import (
	util "code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
	"github.com/vicanso/go-charts/v2"
)

func HealthTable(ctx context.Context, resultInfo *util.InspectionResultInfo) error {
	f, err := getCNFont() // 用自己的字体
	if err != nil {
		log.Warn(ctx, "get cn Font error:%s", err.Error())
		return err
	}
	charts.SetDefaultWidth(900)
	charts.SetDefaultHeight(300)
	charts.SetDefaultFont(f)
	header := []string{"命中规则", "扣分"}
	var data [][]string
	for _, scoreDetail := range resultInfo.Report.ScoreDetail {
		data = append(data, []string{scoreDetail.Description, fmt.Sprintf("%d", scoreDetail.Score)})
	}
	spans := map[int]int{0: 2, 1: 1}
	p, err := charts.TableRender(header, data, spans)
	if err != nil {
		log.Warn(ctx, "TableRender error:%s", err.Error())
		return err
	}
	buf, err := p.Bytes()
	if err != nil {
		log.Warn(ctx, "Bytes error:%s", err.Error())
		return err
	}

	filePath := GetInspectionFilePath(resultInfo)
	err = writeFile(ctx, buf, filePath, HealthTableFileName)
	if err != nil {
		log.Warn(ctx, "writeFile error:%s", err.Error())
		return err
	}
	return nil
}
