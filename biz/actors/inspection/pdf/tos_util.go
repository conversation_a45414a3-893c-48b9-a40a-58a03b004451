package pdf

import (
	util "code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"os"
	"os/exec"
	"strconv"
)

const (
	AwsCmd    = "aws configure set "
	S3tarTool = "aws cp "
)

type TosConfig struct {
	InstanceId string `json:"instanceId"`
	Region     string `json:"region"`
	Endpoint   string `json:"endpoint"`
	Id         string `json:"id"`
	Secret     string `json:"secret"`
	Token      string `json:"token"`
	Bucket     string `json:"bucket"`
}

func UploadPdfToTos(ctx context.Context, resultInfo *util.InspectionResultInfo, tos *TosConfig) error {
	// 初始化aws
	if err := initAwsTools(ctx, tos); err != nil {
		log.Warn(ctx, "initAwsTools failed err:%s", err.Error())
		return err
	}
	sourceFile := GetInspectionFilePath(resultInfo) + fmt.Sprintf("/%d.pdf", resultInfo.TaskId)
	if err := awsUpload(ctx, tos, sourceFile, fmt.Sprintf("%d.pdf", resultInfo.TaskId)); err != nil {
		log.Warn(ctx, "awsUpload error:%s", err.Error())
		return err
	}
	return nil
}

func initAwsTools(ctx context.Context, tos *TosConfig) error {
	err := os.Setenv("S3_ENDPOINT_URL", tos.Endpoint)
	if err != nil {
		log.Warn(ctx, "os.Setenv error:%s", err.Error())
	}
	region := AwsCmd + "default.region " + tos.Region
	output := AwsCmd + "default.output " + "json"
	awsAccessKey := AwsCmd + "default.aws_access_key_id " + tos.Id
	awsSecretKey := AwsCmd + "default.aws_secret_access_key " + tos.Secret
	token := AwsCmd + "default.aws_session_token " + tos.Token
	s3MaxBandWidthMb := AwsCmd + "default.s3.max_bandwidth " + strconv.Itoa(1) + "MB/s"
	addressStyle := AwsCmd + "default.s3.addressing_style virtual "

	cmdStr := region + " && " + output + " && " + awsAccessKey + " && " + awsSecretKey + " && " + token + " && " + s3MaxBandWidthMb + " && " + addressStyle
	cmd := exec.Command("bash", "-c", cmdStr) // ignore_security_alert
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err = cmd.Run(); err != nil {
		log.Warn(ctx, "initAwsTools cmd: %s", cmd)
		log.Warn(ctx, "initAwsTools error: %s", err.Error())
		return err
	}
	return nil
}

func CreateS3Instance(ctx context.Context, tosInfo *TosConfig) (*s3.S3, error) {
	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(tosInfo.Region),
		Endpoint:    aws.String(tosInfo.Endpoint),
		Credentials: credentials.NewStaticCredentials(tosInfo.Id, tosInfo.Secret, tosInfo.Token),
	})
	if err != nil {
		log.Warn(ctx, "c3 NewSession error:%s", err.Error())
		return nil, err
	}
	s3Instance := s3.New(sess)
	return s3Instance, nil
}

func awsUpload(ctx context.Context, tosInfo *TosConfig, sourceName string, fileName string) error {
	upPath := InspectionPdfFilePath + tosInfo.InstanceId
	cmdStr := "aws s3 cp  " + sourceName + " " + "s3://" + tosInfo.Bucket + upPath + "/" + fileName +
		" --endpoint-url=" + tosInfo.Endpoint

	cmd := exec.Command("bash", "-c", cmdStr) // ignore_security_alert
	if err := cmd.Run(); err != nil {
		log.Warn(ctx, "awsUpload cmd: %s", cmd)
		log.Warn(ctx, "awsUpload error: %s", err.Error())
		return err
	}
	return nil
}
