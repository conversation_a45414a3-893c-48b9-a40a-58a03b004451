package pdf

import (
	util "code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
	"github.com/golang/freetype/truetype"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"time"
)

const (
	InspectionPdfFilePath = "/dbw-report/"
	CnFontBoldPath        = "/dbw-report/Alibaba-PuHuiTi-Bold.ttf"

	HealthTableFileName = "table.png"
	HealthGaugeFileName = "gauge.png"
	SlowLogFileName     = "slow.png"
)

func createFileFolder(ctx context.Context, resultInfo *util.InspectionResultInfo) error {
	folderName := InspectionPdfFilePath + resultInfo.Report.InstanceId
	// 创建实例文件夹，没有创建，存在忽略
	err := os.MkdirAll(folderName, 0766)
	if err != nil {
		errMsg := fmt.Sprintf("taskId:%d, create inspection folder error: %s", resultInfo.TaskId, err.Error())
		log.Warn(ctx, errMsg)
		return err
	}
	folderName = GetInspectionFilePath(resultInfo)
	// 创建任务文件夹，没有创建，存在忽略
	err = os.MkdirAll(folderName, 0766)
	if err != nil {
		errMsg := fmt.Sprintf("taskId:%d, create inspection task folder error: %s", resultInfo.TaskId, err.Error())
		log.Warn(ctx, errMsg)
		return err
	}
	return nil
}

func getCNFont() (*truetype.Font, error) {
	fontFile := CnFontBoldPath
	// 读字体数据
	fontBytes, err := ioutil.ReadFile(fontFile)
	if err != nil {
		errMsg := "ReadCnFontFile error " + err.Error()
		return nil, fmt.Errorf(errMsg)
	}
	font, err := truetype.Parse(fontBytes)
	if err != nil {
		errMsg := "ParseCnFontFile error " + err.Error()
		return nil, fmt.Errorf(errMsg)
	}
	return font, nil
}

func writeFile(ctx context.Context, buf []byte, filePath string, filename string) error {
	file := filepath.Join(filePath, filename)
	err := os.WriteFile(file, buf, 0600)
	if err != nil {
		log.Warn(ctx, "WriteFile error:%s", err.Error())
		return err
	}
	return nil
}

func timestampToDateTimeString(timestamp int64) string {
	timeTemplate := "2006-01-02 15:04:05"
	nowTime := time.Unix(timestamp, 0)
	timeStr := nowTime.Format(timeTemplate)
	return timeStr
}

func fotMatAxisMinuteTime(timestamp int64) time.Time {
	// timeTemplate := "15:04"
	minuteTime := time.Unix(timestamp, 0)
	// timeStr := nowTime.Format(timeTemplate)
	return minuteTime
}

func GetInspectionFilePath(resultInfo *util.InspectionResultInfo) string {
	return InspectionPdfFilePath + resultInfo.Report.InstanceId + "/" + fmt.Sprintf("%d", resultInfo.TaskId)
}

func execCommand(ctx context.Context, cmdStr string) {
	// 要执行的命令
	cmd := exec.Command("bash", "-c", cmdStr) // 示例命令，可以替换为其他命令
	// 执行命令并获取输出
	_, err := cmd.CombinedOutput() // CombinedOutput 会捕获标准输出和错误输出
	// 检查执行状态
	if err != nil {
		log.Warn(ctx, "execute Gauge Command error, cmd:%s  detail:%s", cmd, err.Error())
		return
	}
}
