package pdf

import (
	util "code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
	"github.com/gobuffalo/packr/v2"
	"github.com/jung-kurt/gofpdf"
)

func CreatePdfFile(ctx context.Context, resultInfo *util.InspectionResultInfo) error {
	err := createFileFolder(ctx, resultInfo)
	if err != nil {
		return err
	}
	pdf := createHeader(timestampToDateTimeString(resultInfo.Basic.InspectionStartTime/1000), timestampToDateTimeString(resultInfo.Basic.InspectionEndTime/1000))
	createBasicInfo(pdf, resultInfo.Report)
	createHealthInfo(pdf)
	err = HealthTable(ctx, resultInfo)
	if err != nil {
		return err
	}
	createHealthTable(pdf, resultInfo)
	createGaugeMetric(ctx, pdf, resultInfo)
	createSourceUsage(pdf)
	err = createAllItemMetric(ctx, resultInfo)
	if err != nil {
		return err
	}
	insertMetricToPdf(pdf, resultInfo)
	logNum, err := CreateSlowSqlTable(ctx, resultInfo)
	if err != nil {
		return err
	}
	insertSlowToPdf(pdf, logNum, resultInfo)
	outPath := GetInspectionFilePath(resultInfo) + "/" + fmt.Sprintf("%d.pdf", resultInfo.TaskId)
	err = pdf.OutputFileAndClose(outPath)
	if err != nil {
		log.Warn(ctx, "OutputPdfFile error:%s", err.Error())
		return err
	}
	return nil
}

func createHeader(startTime string, endTime string) *gofpdf.Fpdf {
	// 初始化一个pdf
	pdf := gofpdf.New("P", "mm", "A4", "")
	// 添加中文字体
	box := packr.New("pdf", InspectionPdfFilePath)
	aliB, _ := box.Find("Alibaba-PuHuiTi-Bold.ttf")
	aliR, _ := box.Find("Alibaba-PuHuiTi-Regular.ttf")
	pdf.AddUTF8FontFromBytes("aliCn", "", aliR)
	pdf.AddUTF8FontFromBytes("aliCn", "B", aliB)
	// 生成页和头部内容
	pdf.AddPage()
	pdf.SetFont("aliCn", "B", 12)
	pdf.CellFormat(0, 0, "数 据 库 巡 检 报 告", "0", 1, "CM", false, 0, "")
	pdf.Ln(6)
	pdf.SetFont("Arial", "", 8)
	pdf.CellFormat(0, 0, fmt.Sprintf("(%s ~ %s)", startTime, endTime), "0", 1, "CM", false, 0, "")
	pdf.Ln(6)
	return pdf
}

func createBasicInfo(pdf *gofpdf.Fpdf, report *util.InspectionReportInfo) {
	pdf.SetFont("aliCn", "B", 10)
	pdf.CellFormat(0, 0, "基本信息", "0", 1, "L", false, 0, "")
	pdf.Ln(6)
	pdf.SetFont("aliCn", "", 8)
	pdf.CellFormat(0, 0, fmt.Sprintf("实例ID： %s                                                    实例规格： %s", report.InstanceId, report.InstanceSpecification), "0", 1, "L", false, 0, "")
	pdf.CellFormat(0, 0, fmt.Sprintf("数据库版本 %s %s", report.InstanceType, report.InstanceVersion), "0", 1, "R", false, 0, "")
	pdf.Ln(6)
}

func createHealthInfo(pdf *gofpdf.Fpdf) {
	pdf.SetFont("aliCn", "B", 10)
	pdf.CellFormat(0, 0, "健康得分                                                             健康得分", "0", 1, "L", false, 0, "")
}

func createHealthTable(pdf *gofpdf.Fpdf, resultInfo *util.InspectionResultInfo) {
	filePath := GetInspectionFilePath(resultInfo) + "/" + HealthTableFileName
	pdf.Image(filePath, 80, 39, 121, 30, false, "", 0, "")
}

func createGaugeMetric(ctx context.Context, pdf *gofpdf.Fpdf, resultInfo *util.InspectionResultInfo) {
	filePath := GetInspectionFilePath(resultInfo)
	command := fmt.Sprintf("python3 /opt/tiger/dbwmgr/gauge.py --score %d --file_path %s ", resultInfo.Basic.InspectAgg.HealthScore, filePath)
	execCommand(ctx, command)
	filePath += "/" + HealthGaugeFileName
	pdf.Image(filePath, 8, 28, 70, 50, false, "", 0, "")
}

func createSourceUsage(pdf *gofpdf.Fpdf) {
	pdf.SetFont("aliCn", "B", 10)
	pdf.CellFormat(0, 85, "资源使用", "0", 1, "L", false, 0, "")
}

func insertMetricToPdf(pdf *gofpdf.Fpdf, resultInfo *util.InspectionResultInfo) {
	filePath := GetInspectionFilePath(resultInfo)
	fy := float64(85) // 图片的纵向位置
	for i := 0; i+1 < len(resultInfo.Report.DataMetrics); i += 2 {
		firstFileName := filePath + "/" + resultInfo.Report.DataMetrics[i].Description + ".png"
		secondFileName := filePath + "/" + resultInfo.Report.DataMetrics[i+1].Description + ".png"
		// 由前到后依次是 图的横向位置，图的纵向位置，图片宽占位，图片高占位
		pdf.Image(firstFileName, 10, fy, 90, 45, false, "", 0, "")
		pdf.Image(secondFileName, 110, fy, 90, 45, false, "", 0, "")
		// 因为图片横向位置是固定的，pdf是往下走的，所以只需要偏移y轴位置即可
		fy += 45
	}
}

func insertSlowToPdf(pdf *gofpdf.Fpdf, num int, resultInfo *util.InspectionResultInfo) {
	pdf.AddPage()
	pdf.SetFont("aliCn", "B", 10)
	pdf.CellFormat(0, 0, "TOP10 慢SQL", "0", 1, "L", false, 0, "")

	w := 190
	h := (num * 59) / 1200 * w

	filePath := GetInspectionFilePath(resultInfo) + "/" + SlowLogFileName
	pdf.Image(filePath, 10, 20, float64(w), float64(h), false, "", 0, "")
}
