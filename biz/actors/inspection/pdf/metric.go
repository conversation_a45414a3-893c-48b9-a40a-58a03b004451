package pdf

import (
	util "code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
	"github.com/vicanso/go-charts/v2"
	"strings"
)

func createAllItemMetric(ctx context.Context, resultInfo *util.InspectionResultInfo) error {
	for _, itemMetric := range resultInfo.Report.DataMetrics {
		buf, err := CreateItemMetricPngBytes(ctx, itemMetric)
		if err != nil {
			log.Warn(ctx, "CreateItemMetricPngBytes error:%s", err.Error())
			return err
		}
		fileName := itemMetric.Description + ".png"
		if err = saveMetricPng(ctx, buf, resultInfo, fileName); err != nil {
			log.Warn(ctx, "saveMetricPng error:%s", err.Error())
			return err
		}
	}
	return nil
}

func CreateItemMetricPngBytes(ctx context.Context, itemMetric *model.InspectionDataMetric) ([]byte, error) {
	xAxisValue, values, firstAxis := formatAxisValue(itemMetric.DataPoints)
	cnFont, err := getCNFont()
	if err != nil {
		log.Warn(ctx, "get cn Font error:%s", err.Error())
		return []byte{}, err
	}
	charts.SetDefaultFont(cnFont)
	charts.SetDefaultWidth(600)
	charts.SetDefaultHeight(300)
	p, err := charts.LineRender(
		[][]float64{values},
		//charts.TitleTextOptionFunc("Cpu使用率(%)      Max:20  Min:13  Avg:16"),
		charts.TitleTextOptionFunc(fmt.Sprintf("%s      Max:%.2f  Min:%.2f  Avg:%.2f", getCnDescription(itemMetric), itemMetric.Max, itemMetric.Min, itemMetric.Avg)),
		charts.XAxisDataOptionFunc(xAxisValue, charts.FalseFlag()),
		charts.ChildOptionFunc(),
		func(opt *charts.ChartOption) {
			opt.XAxis.FirstAxis = firstAxis
			// 必须要比计算得来的最小值更大(每60分钟)
			opt.XAxis.SplitNumber = 15
			opt.Legend.Padding = charts.Box{Top: 5, Bottom: 10}
			opt.SymbolShow = charts.FalseFlag()
			opt.LineStrokeWidth = 1
			opt.ValueFormatter = func(f float64) string {
				return fmt.Sprintf("%.0f", f)
			}
		},
	)
	if err != nil {
		log.Warn(ctx, "charts.LineRender error:%s", err.Error())
		return []byte{}, err
	}
	buf, err := p.Bytes()
	if err != nil {
		log.Warn(ctx, "LineRender Bytes error:%s", err.Error())
		return []byte{}, err
	}
	return buf, nil
}

func getCnDescription(itemMetric *model.InspectionDataMetric) string {
	unitDescription := ""
	if strings.TrimSpace(itemMetric.Unit) != "" {
		unitDescription = "(" + strings.TrimSpace(itemMetric.Unit) + ")"
	}
	// TODO CN
	return itemMetric.Description + unitDescription
}

func saveMetricPng(ctx context.Context, buf []byte, resultInfo *util.InspectionResultInfo, fileName string) error {
	filePath := GetInspectionFilePath(resultInfo)
	err := writeFile(ctx, buf, filePath, fileName)
	if err != nil {
		log.Warn(ctx, "writeFile error:%s", err.Error())
		return err
	}
	return nil
}

func formatAxisValue(DataPoints []*model.DataPoint) ([]string, []float64, int) {
	var xAxisValue []string
	var values []float64
	for _, dataPoint := range DataPoints {
		dataTime := fotMatAxisMinuteTime(int64(dataPoint.TimeStamp))
		xAxisValue = append(xAxisValue, dataTime.Format("15:04"))
		values = append(values, dataPoint.Value)
	}

	firstAxis := len(DataPoints) / 8

	return xAxisValue, values, firstAxis
}
