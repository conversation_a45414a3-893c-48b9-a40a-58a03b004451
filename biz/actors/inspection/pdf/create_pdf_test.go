package pdf

import (
	util "code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/jung-kurt/gofpdf"
	"testing"
	"time"
)

func mockInspectionResultInfo() *util.InspectionResultInfo {
	return &util.InspectionResultInfo{
		Basic: &util.InspectionBasicInfo{},
		Report: &util.InspectionReportInfo{
			ScoreDetail: []*model.MetricScore{{}},
			DataMetrics: []*model.InspectionDataMetric{{DataPoints: []*model.DataPoint{{}, {}}}, {}},
		},
		SlowLogs: []*model.InspectionSlowLog{{}},
	}
}

func TestCreatePdfFile(t *testing.T) {
	ctx := context.Background()
	resultInfo := mockInspectionResultInfo()

	mock1 := mockey.Mock(createFileFolder).Return(fmt.Errorf("test")).Build()
	_ = CreatePdfFile(ctx, resultInfo)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock(createFileFolder).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock(createHeader).Return(&gofpdf.Fpdf{}).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock(createBasicInfo).Return().Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock(createHealthInfo).Return().Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock(createHealthTable).Return().Build()
	defer mock6.UnPatch()
	mock7 := mockey.Mock(createGaugeMetric).Return().Build()
	defer mock7.UnPatch()
	mock8 := mockey.Mock(createSourceUsage).Return().Build()
	defer mock8.UnPatch()
	mock9 := mockey.Mock(insertMetricToPdf).Return().Build()
	defer mock9.UnPatch()
	mock10 := mockey.Mock(insertSlowToPdf).Return().Build()
	defer mock10.UnPatch()
	mock11 := mockey.Mock(HealthTable).Return(nil).Build()
	defer mock11.UnPatch()
	mock12 := mockey.Mock(CreateSlowSqlTable).Return(1, nil).Build()
	defer mock12.UnPatch()
	mock13 := mockey.Mock((*gofpdf.Fpdf).OutputFileAndClose).Return(fmt.Errorf("test")).Build()
	_ = CreatePdfFile(ctx, resultInfo)
	mock13.UnPatch()
	time.Sleep(100 * time.Millisecond)
}

func TestCreateHeader(t *testing.T) {
	mock1 := mockey.Mock((*gofpdf.Fpdf).AddUTF8FontFromBytes).Return().Build()
	defer mock1.UnPatch()
	_ = createHeader("1", "2")
}

func TestCreateBasicInfo(t *testing.T) {
	createBasicInfo(&gofpdf.Fpdf{}, mockInspectionResultInfo().Report)
}

func TestCreateHealthInfo(t *testing.T) {
	createHealthInfo(&gofpdf.Fpdf{})
}

func TestCreateHealthTable(t *testing.T) {
	mock1 := mockey.Mock((*gofpdf.Fpdf).Image).Return().Build()
	defer mock1.UnPatch()
	createHealthTable(&gofpdf.Fpdf{}, mockInspectionResultInfo())
}

//// FIXME:
//func TestCreateGaugeMetric(t *testing.T) {
//	mock1 := mockey.Mock((*gofpdf.Fpdf).Image).Return().Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(execCommand).Return().Build()
//	defer mock2.UnPatch()
//	createGaugeMetric(context.Background(), &gofpdf.Fpdf{}, mockInspectionResultInfo())
//}

func TestCreateSourceUsage(t *testing.T) {
	createSourceUsage(&gofpdf.Fpdf{})
}

func TestInsertMetricToPdf(t *testing.T) {
	mock1 := mockey.Mock((*gofpdf.Fpdf).Image).Return().Build()
	defer mock1.UnPatch()
	insertMetricToPdf(&gofpdf.Fpdf{}, mockInspectionResultInfo())
}

func TestInsertSlowToPdf(t *testing.T) {
	mock1 := mockey.Mock((*gofpdf.Fpdf).Image).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gofpdf.Fpdf).AddPage).Return().Build()
	defer mock2.UnPatch()
	insertSlowToPdf(&gofpdf.Fpdf{}, 1, mockInspectionResultInfo())
}
