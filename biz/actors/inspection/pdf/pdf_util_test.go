package pdf

import (
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/golang/freetype/truetype"
	"io/ioutil"
	"os"
	"testing"
	"time"
)

func TestCreateFileFolder(t *testing.T) {
	ctx := context.Background()
	resultInfo := mockInspectionResultInfo()

	mock1 := mockey.Mock(os.MkdirAll).Return(fmt.Errorf("test")).Build()
	_ = createFileFolder(ctx, resultInfo)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock(os.MkdirAll).Return(nil).Build()
	defer mock2.UnPatch()
	_ = createFileFolder(ctx, resultInfo)
}

func TestGetCNFont(t *testing.T) {
	mock1 := mockey.Mock(ioutil.ReadFile).Return([]byte{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock(truetype.Parse).Return(nil, fmt.Errorf("test")).Build()
	_, _ = getCNFont()
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock(truetype.Parse).Return(nil, nil).Build()
	defer mock3.UnPatch()
	_, _ = getCNFont()
}

func TestWriteFile(t *testing.T) {

	mock1 := mockey.Mock(os.WriteFile).Return(fmt.Errorf("test")).Build()
	_ = writeFile(context.Background(), []byte{}, "", "")
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock(os.WriteFile).Return(nil).Build()
	_ = writeFile(context.Background(), []byte{}, "", "")
	mock2.UnPatch()
}

func TestFotMatAxisMinuteTime(t *testing.T) {
	fotMatAxisMinuteTime(1)
}
