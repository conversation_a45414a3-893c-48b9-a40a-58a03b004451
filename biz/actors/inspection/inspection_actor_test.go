package inspection

import (
	config2 "code.byted.org/infcs/dbw-mgr/biz/config"
	util "code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/diagnosis"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/dslibmocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/config"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"testing"
	"time"
)

func mockTaskActor() *TaskActor {
	return &TaskActor{
		state:          &TaskActorState{},
		cnf:            &config.MockConfigProvider{},
		repo:           &repository.InspectionRepoImpl{},
		rootDiagItem:   mockDiagItem(),
		actorClient:    &dslibmocks.MockActorClient{},
		ds:             &mocks.MockDataSourceService{},
		c3ConfProvider: &mocks.MockC3ConfigProvider{},
		loca:           &mocks.MockLocation{},
		inspectionResult: &util.InspectionResultInfo{
			Basic: &util.InspectionBasicInfo{},
			Report: &util.InspectionReportInfo{
				ScoreDetail: []*model.MetricScore{},
				DataMetrics: []*model.InspectionDataMetric{},
			},
			SlowLogs: []*model.InspectionSlowLog{},
		},
	}
}

func mockDiagItem() diagnosis.RootDiagItem {
	items := map[string]diagnosis.DiagItem{}
	items[model.DiagItem_CpuUsage.String()] = &diagnosis.CpuUsage{}
	items[model.DiagItem_MemUsage.String()] = &diagnosis.MemUsage{}
	return diagnosis.RootDiagItem{Items: items}
}

func mockItemDataResult() *model.ItemDataResult_ {
	return &model.ItemDataResult_{
		DataPoints: []*model.DataPoint{{TimeStamp: 12345, Value: 1}, {TimeStamp: 12346, Value: 2}},
	}
}

//func TestCreateInspectionTask(t *testing.T) {
//	actor := mockTaskActor()
//	baseMock1 := mockey.Mock(log.Log).Return().Build()
//	defer baseMock1.UnPatch()
//	baseMock2 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
//	defer baseMock2.UnPatch()
//	baseMock3 := mockey.Mock((*TaskActor).isInstanceRunning).Return(true).Build()
//	defer baseMock3.UnPatch()
//	baseMock4 := mockey.Mock((*mocks.MockContext).Stop).Return().Build()
//	defer baseMock4.UnPatch()
//	baseMock5 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//	defer baseMock5.UnPatch()
//	baseMock6 := mockey.Mock((*TaskActor).sendMessage).Return().Build()
//	defer baseMock6.UnPatch()
//
//	mock36 := mockey.Mock((*TaskActor).updateInstanceNameByInstanceID).Return(nil).Build()
//	defer mock36.UnPatch()
//	mock1 := mockey.Mock((*TaskActor).insertOneTask).Return(fmt.Errorf("test")).Build()
//	actor.createInspectionTask(&mocks.MockContext{}, &shared.CreateInspectionTask{
//		InspectionType: int32(model.InspectionType_Manual),
//	})
//	mock1.UnPatch()
//
//	mock4 := mockey.Mock((*TaskActor).doInspection).Return(fmt.Errorf("test")).Build()
//	time.Sleep(100 * time.Millisecond)
//	mock2 := mockey.Mock((*TaskActor).insertOneTask).Return(nil).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*TaskActor).updateTaskStatus).Return(nil).Build()
//	defer mock3.UnPatch()
//	actor.createInspectionTask(&mocks.MockContext{}, &shared.CreateInspectionTask{
//		InspectionType: int32(model.InspectionType_Manual),
//	})
//	mock4.UnPatch()
//
//	time.Sleep(100 * time.Millisecond)
//	mock5 := mockey.Mock((*TaskActor).doInspection).Return(nil).Build()
//	defer mock5.UnPatch()
//	mock6 := mockey.Mock((*TaskActor).updateResult).Return(fmt.Errorf("test")).Build()
//	defer mock6.UnPatch()
//	actor.createInspectionTask(&mocks.MockContext{}, &shared.CreateInspectionTask{
//		InspectionType: int32(model.InspectionType_Manual),
//	})
//}

func TestRetryCreateTask(t *testing.T) {
	actor := mockTaskActor()
	mock := mockey.Mock(log.Log).Return().Build()
	defer mock.UnPatch()
	mock1 := mockey.Mock((*TaskActor).createInspectionTask).Return().Build()
	defer mock1.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*mocks.MockContext).Stop).Return().Build()
	defer baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*TaskActor).sendMessage).Return().Build()
	defer baseMock4.UnPatch()

	actor.retryCreateTask(&mocks.MockContext{}, &shared.CreateInspectionTask{})
}

func TestCheckTimeout(t *testing.T) {
	actor := mockTaskActor()
	mock := mockey.Mock(log.Log).Return().Build()
	defer mock.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockContext).Stop).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock2.UnPatch()
	actor.checkTimeout(&mocks.MockContext{})
}

func TestIsOutTime(t *testing.T) {
	// 凌晨1点-1点1分，此单测过不了
	actor := mockTaskActor()
	isOut := actor.isOutTime(&shared.CreateInspectionTask{ExecuteStartTime: 3600000, ExecuteEndTime: 3660000})
	assert.True(t, isOut)
	isOut = actor.isOutTime(&shared.CreateInspectionTask{ExecuteStartTime: 1000, ExecuteEndTime: 86400000})
	assert.False(t, isOut)
}

//func TestInsertOneTask(t *testing.T) {
//	actor := mockTaskActor()
//	mock := mockey.Mock(log.Log).Return().Build()
//	defer mock.UnPatch()
//
//	mock1 := mockey.Mock((*repository.InspectionRepoImpl).CreateTask).Return(fmt.Errorf("test")).Build()
//	err := actor.insertOneTask(&mocks.MockContext{}, &shared.CreateInspectionTask{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*repository.InspectionRepoImpl).CreateTask).Return(nil).Build()
//	defer mock2.UnPatch()
//	err = actor.insertOneTask(&mocks.MockContext{}, &shared.CreateInspectionTask{})
//	assert.Nil(t, err)
//}

// FIXME:
//func TestDoInspection(t *testing.T) {
//	actor := mockTaskActor()
//	mock := mockey.Mock(log.Log).Return().Build()
//	defer mock.UnPatch()
//
//	mock1 := mockey.Mock((*TaskActor).updateTaskStatus).Return(fmt.Errorf("test")).Build()
//	err := actor.doInspection(&mocks.MockContext{}, &shared.CreateInspectionTask{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*TaskActor).updateTaskStatus).Return(nil).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*TaskActor).inspectionBasicInfo).Return(fmt.Errorf("test")).Build()
//	err = actor.doInspection(&mocks.MockContext{}, &shared.CreateInspectionTask{})
//	assert.NotNil(t, err)
//	mock3.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock4 := mockey.Mock((*TaskActor).inspectionBasicInfo).Return(nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*TaskActor).inspectionDetailInfo).Return(fmt.Errorf("test")).Build()
//	err = actor.doInspection(&mocks.MockContext{}, &shared.CreateInspectionTask{})
//	assert.NotNil(t, err)
//	mock5.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock6 := mockey.Mock((*TaskActor).inspectionDetailInfo).Return(nil).Build()
//	defer mock6.UnPatch()
//	mock7 := mockey.Mock((*TaskActor).inspectionSlowLogInfo).Return(fmt.Errorf("test")).Build()
//	err = actor.doInspection(&mocks.MockContext{}, &shared.CreateInspectionTask{})
//	assert.NotNil(t, err)
//	mock7.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock8 := mockey.Mock((*TaskActor).inspectionSlowLogInfo).Return(nil).Build()
//	defer mock8.UnPatch()
//	err = actor.doInspection(&mocks.MockContext{}, &shared.CreateInspectionTask{})
//	assert.Nil(t, err)
//}

func TestUpdateResult(t *testing.T) {
	actor := mockTaskActor()
	mock := mockey.Mock(log.Log).Return().Build()
	defer mock.UnPatch()

	mock1 := mockey.Mock((*mocks.MockC3ConfigProvider).GetNamespace).Return(&config2.C3Config{
		Application: config2.Application{},
	}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockLocation).RegionID).Return("test").Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*TaskActor).formatTlsLogs).Return(&tls.PutLogsRequest{}).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*tls.LsClient).PutLogs).Return(nil, fmt.Errorf("test")).Build()
	err := actor.updateResult(&mocks.MockContext{}, &shared.CreateInspectionTask{})
	assert.NotNil(t, err)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*tls.LsClient).PutLogs).Return(nil, nil).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*TaskActor).updateInspectionResult).Return(fmt.Errorf("test")).Build()
	err = actor.updateResult(&mocks.MockContext{}, &shared.CreateInspectionTask{})
	assert.NotNil(t, err)
	time.Sleep(100 * time.Millisecond)
	mock6.UnPatch()

	mock7 := mockey.Mock((*TaskActor).updateInspectionResult).Return(nil).Build()
	defer mock7.UnPatch()
	err = actor.updateResult(&mocks.MockContext{}, &shared.CreateInspectionTask{})
	assert.Nil(t, err)
}

//func TestUpdateInspectionResult(t *testing.T) {
//	actor := mockTaskActor()
//	mock := mockey.Mock(log.Log).Return().Build()
//	defer mock.UnPatch()
//
//	mock1 := mockey.Mock((*repository.InspectionRepoImpl).UpdateTaskInspectionValue).Return(fmt.Errorf("test")).Build()
//	err := actor.updateInspectionResult(&mocks.MockContext{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*repository.InspectionRepoImpl).UpdateTaskInspectionValue).Return(nil).Build()
//	defer mock2.UnPatch()
//	err = actor.updateInspectionResult(&mocks.MockContext{})
//	assert.Nil(t, err)
//}

//func TestInspectionBasicInfo(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock1 := mockey.Mock((*TaskActor).getHealthScore).Return(1, fmt.Errorf("test")).Build()
//	res, err := actor.getHealthScore(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL})
//	assert.Equal(t, res, 1)
//	assert.NotNil(t, err)
//	err = actor.inspectionBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*TaskActor).getHealthScore).Return(1, nil).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*mocks.MockDataSourceService).GetAvgDiskUsage).Return(&datasource.GetMetricUsageResp{}, fmt.Errorf("test")).Build()
//	err = actor.inspectionBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock3.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock4 := mockey.Mock((*mocks.MockDataSourceService).GetAvgDiskUsage).Return(&datasource.GetMetricUsageResp{}, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockDataSourceService).GetAvgMemUsage).Return(&datasource.GetMetricUsageResp{}, fmt.Errorf("test")).Build()
//	err = actor.inspectionBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock5.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock6 := mockey.Mock((*mocks.MockDataSourceService).GetAvgMemUsage).Return(&datasource.GetMetricUsageResp{}, nil).Build()
//	defer mock6.UnPatch()
//	mock7 := mockey.Mock((*mocks.MockDataSourceService).GetAvgConnectionUsage).Return(&datasource.GetMetricUsageResp{}, fmt.Errorf("test")).Build()
//	err = actor.inspectionBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock7.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock8 := mockey.Mock((*mocks.MockDataSourceService).GetAvgConnectionUsage).Return(&datasource.GetMetricUsageResp{}, nil).Build()
//	defer mock8.UnPatch()
//	mock9 := mockey.Mock((*mocks.MockDataSourceService).GetQpsUsage).Return(&datasource.GetMetricUsageResp{}, fmt.Errorf("test")).Build()
//	err = actor.inspectionBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock9.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock10 := mockey.Mock((*mocks.MockDataSourceService).GetQpsUsage).Return(&datasource.GetMetricUsageResp{}, nil).Build()
//	defer mock10.UnPatch()
//	mock11 := mockey.Mock((*mocks.MockDataSourceService).GetTpsUsage).Return(&datasource.GetMetricUsageResp{}, fmt.Errorf("test")).Build()
//	err = actor.inspectionBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock11.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock12 := mockey.Mock((*mocks.MockDataSourceService).GetTpsUsage).Return(&datasource.GetMetricUsageResp{}, nil).Build()
//	defer mock12.UnPatch()
//	mock13 := mockey.Mock((*mocks.MockDataSourceService).GetAvgCpuUsage).Return(&datasource.GetMetricUsageResp{}, fmt.Errorf("test")).Build()
//	err = actor.inspectionBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock13.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock14 := mockey.Mock((*mocks.MockDataSourceService).GetAvgCpuUsage).Return(&datasource.GetMetricUsageResp{}, nil).Build()
//	defer mock14.UnPatch()
//	err = actor.inspectionBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.Nil(t, err)
//}

//func TestGetHealthScore(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock2 := mockey.Mock((*diagnosis.MemUsage).Score).Return(2, fmt.Errorf("test")).Build()
//	mock1 := mockey.Mock((*diagnosis.CpuUsage).Score).Return(1, nil).Build()
//	defer mock1.UnPatch()
//
//	_, err := actor.getHealthScore(&mocks.MockContext{}, &shared.CreateInspectionTask{})
//	assert.NotNil(t, err)
//	mock2.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock3 := mockey.Mock((*diagnosis.MemUsage).Score).Return(2, nil).Build()
//	defer mock3.UnPatch()
//	score, err := actor.getHealthScore(&mocks.MockContext{}, &shared.CreateInspectionTask{})
//	assert.Nil(t, err)
//	assert.Equal(t, score, 97)
//}

//func TestInspectionDetailInfo(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock1 := mockey.Mock((*TaskActor).formatInstanceBasicInfo).Return(fmt.Errorf("test")).Build()
//	err := actor.inspectionDetailInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*TaskActor).formatInstanceBasicInfo).Return(nil).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*TaskActor).inspectionCpuReportInfo).Return(fmt.Errorf("test")).Build()
//	err = actor.inspectionDetailInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock3.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock4 := mockey.Mock((*TaskActor).inspectionCpuReportInfo).Return(nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*TaskActor).inspectionMemReportInfo).Return(fmt.Errorf("test")).Build()
//	err = actor.inspectionDetailInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock5.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock6 := mockey.Mock((*TaskActor).inspectionMemReportInfo).Return(nil).Build()
//	defer mock6.UnPatch()
//	mock7 := mockey.Mock((*TaskActor).inspectionDiskReportInfo).Return(fmt.Errorf("test")).Build()
//	err = actor.inspectionDetailInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock7.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//}

//func TestFormatInstanceBasicInfo(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	actor.inspectionResult.Report.InstanceSpecification = "test"
//	err := actor.formatInstanceBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.Nil(t, err)
//
//	actor.inspectionResult.Report.InstanceSpecification = ""
//	mock1 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{}, fmt.Errorf("test")).Build()
//	err = actor.formatInstanceBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{
//		NodeSpec: "mysql.rds.1c2g",
//	}, nil).Build()
//	defer mock2.UnPatch()
//	err = actor.formatInstanceBasicInfo(&mocks.MockContext{}, &shared.CreateInspectionTask{InstanceType: shared.MySQL}, &util.InspectionResultInfo{})
//	assert.Nil(t, err)
//	assert.Equal(t, actor.inspectionResult.Report.InstanceSpecification, "mysql.rds.1c2g")
//}

//func TestInspectionCpuReportInfo(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionCpuMetric).Return(&model.ItemDataResult_{}, fmt.Errorf("test")).Build()
//	err := actor.inspectionCpuReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionCpuMetric).Return(&model.ItemDataResult_{}, nil).Build()
//	defer mock2.UnPatch()
//	err = actor.inspectionCpuReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.Nil(t, err)
//}

//func TestInspectionMemReportInfo(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionMemMetric).Return(&model.ItemDataResult_{}, fmt.Errorf("test")).Build()
//	err := actor.inspectionMemReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionMemMetric).Return(&model.ItemDataResult_{}, nil).Build()
//	defer mock2.UnPatch()
//	err = actor.inspectionMemReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.Nil(t, err)
//}

//func TestInspectionDiskReportInfo(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionDiskMetric).Return(&model.ItemDataResult_{}, fmt.Errorf("test")).Build()
//	err := actor.inspectionDiskReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionDiskMetric).Return(&model.ItemDataResult_{}, nil).Build()
//	defer mock2.UnPatch()
//	err = actor.inspectionDiskReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.Nil(t, err)
//}

//func TestInspectionConnReportInfo(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionConnectedMetric).Return(&model.ItemDataResult_{}, fmt.Errorf("test")).Build()
//	err := actor.inspectionConnectedReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionConnectedMetric).Return(&model.ItemDataResult_{}, nil).Build()
//	defer mock2.UnPatch()
//	err = actor.inspectionConnectedReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.Nil(t, err)
//}

//func TestFormatSlowLogsResp(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mockSlowLog := &shared.AggregateSlowLog{
//		DB:                "db_test",
//		QueryTimeStats:    &shared.StatisticResult{},
//		LockTimeStats:     &shared.StatisticResult{},
//		RowsSentStats:     &shared.StatisticResult{},
//		RowsExaminedStats: &shared.StatisticResult{},
//	}
//	mockSlowLogResp := &shared.DescribeAggregateSlowLogsResp{
//		AggregateSlowLogs: []*shared.AggregateSlowLog{mockSlowLog},
//	}
//
//	actor.formatSlowLogsResp(&mocks.MockContext{}, &shared.DescribeAggregateSlowLogsReq{}, mockSlowLogResp, &util.InspectionResultInfo{}, nil)
//	assert.Equal(t, len(actor.inspectionResult.SlowLogs), 1)
//	assert.Equal(t, actor.inspectionResult.SlowLogs[0].DBName, "db_test")
//}

//func TestFormatSlowLogsReq(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	resp := actor.formatSlowLogsReq(&shared.CreateInspectionTask{
//		InstanceId:          "instanceId",
//		RegionId:            "bj",
//		InstanceType:        shared.MySQL,
//		InspectionStartTime: 1000,
//		InspectionEndTime:   2000,
//	})
//	assert.Equal(t, resp.InstanceId, "instanceId")
//	assert.Equal(t, resp.RegionId, "bj")
//	assert.Equal(t, resp.DataSourceType, shared.MySQL)
//	assert.Equal(t, resp.StartTime, int64(1))
//	assert.Equal(t, resp.EndTime, int64(2))
//}

//func TestFormatTlsLogs(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock1 := mockey.Mock((*config.MockConfigProvider).Get).Return(&config2.Config{InspectionTopicId: "test"}).Build()
//	defer mock1.UnPatch()
//	_ = actor.formatTlsLogs(&mocks.MockContext{})
//}

//func TestFormatMetricData(t *testing.T) {
//	actor := mockTaskActor()
//
//	inspectionItemMetric := actor.formatMetricData(mockItemDataResult(), &shared.CreateInspectionTask{InspectionStartTime: 1234000, InspectionEndTime: 123456000}, nil)
//	assert.Equal(t, inspectionItemMetric.Avg, 1.5)
//	assert.Equal(t, inspectionItemMetric.Min, float64(1))
//	assert.Equal(t, inspectionItemMetric.Max, float64(2))
//}

//func TestInspectionConnRatioReportInfo(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionConnRatioMetric).Return(&model.ItemDataResult_{}, fmt.Errorf("test")).Build()
//	err := actor.inspectionConnRatioReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionConnRatioMetric).Return(&model.ItemDataResult_{}, nil).Build()
//	defer mock2.UnPatch()
//	err = actor.inspectionConnRatioReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.Nil(t, err)
//}

//func TestInspectionTpsReportInfo(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionTpsMetric).Return(&model.ItemDataResult_{}, fmt.Errorf("test")).Build()
//	err := actor.inspectionTpsReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionTpsMetric).Return(&model.ItemDataResult_{}, nil).Build()
//	defer mock2.UnPatch()
//	err = actor.inspectionTpsReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.Nil(t, err)
//}

//func TestInspectionQpsReportInfo(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionQpsMetric).Return(&model.ItemDataResult_{}, fmt.Errorf("test")).Build()
//	err := actor.inspectionQpsReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.NotNil(t, err)
//	mock1.UnPatch()
//	time.Sleep(100 * time.Millisecond)
//
//	mock2 := mockey.Mock((*mocks.MockDataSourceService).GetInspectionQpsMetric).Return(&model.ItemDataResult_{}, nil).Build()
//	defer mock2.UnPatch()
//	err = actor.inspectionQpsReportInfo(&mocks.MockContext{}, &datasource.GetMetricUsageReq{}, &shared.CreateInspectionTask{}, &util.InspectionResultInfo{})
//	assert.Nil(t, err)
//}

//func TestFormatSlowLogResult(t *testing.T) {
//	actor := mockTaskActor()
//	logMock1 := mockey.Mock(log.Log).Return().Build()
//	defer logMock1.UnPatch()
//
//	actor.formatSlowLogResult(mockDescribeSlowLogTimeSeriesStatsResp(), &model.InspectionDataMetric{})
//	assert.Equal(t, len(actor.inspectionResult.Report.DataMetrics), 1)
//	assert.Equal(t, len(actor.inspectionResult.Report.DataMetrics[0].DataPoints), 241)
//	assert.Equal(t, actor.inspectionResult.Report.DataMetrics[0].Min, float64(0))
//	assert.Equal(t, actor.inspectionResult.Report.DataMetrics[0].Max, float64(4))
//	assert.Equal(t, actor.inspectionResult.Report.DataMetrics[0].DataPoints[0].Value, float64(1))
//	assert.Equal(t, actor.inspectionResult.Report.DataMetrics[0].DataPoints[4].Value, float64(2))
//	assert.Equal(t, actor.inspectionResult.Report.DataMetrics[0].DataPoints[8].Value, float64(3))
//	assert.Equal(t, actor.inspectionResult.Report.DataMetrics[0].DataPoints[233].Value, float64(4))
//}

func mockDescribeSlowLogTimeSeriesStatsResp() *shared.DescribeSlowLogTimeSeriesStatsResp {
	slowCount1 := &shared.SlowLogCount{
		Timestamp: 1691560800,
		Count:     1,
	}
	slowCount2 := &shared.SlowLogCount{
		Timestamp: 1691560871,
		Count:     2,
	}
	slowCount3 := &shared.SlowLogCount{
		Timestamp: 1691560931,
		Count:     3,
	}
	slowCount4 := &shared.SlowLogCount{
		Timestamp: 1691564300,
		Count:     4,
	}
	return &shared.DescribeSlowLogTimeSeriesStatsResp{
		SlowLogCountStats: []*shared.SlowLogCount{slowCount1, slowCount2, slowCount3, slowCount4},
	}
}

func TestIsInstanceRunning(t *testing.T) {
	actor := mockTaskActor()
	logMock1 := mockey.Mock(log.Log).Return().Build()
	defer logMock1.UnPatch()
	baseMock1 := mockey.Mock(fwctx.GetBizContext).Return(&fwctx.BizContext{}).Build()
	defer baseMock1.UnPatch()

	mock1 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(nil, fmt.Errorf("test")).Build()
	res := actor.isInstanceRunning(&mocks.MockContext{}, shared.MySQL, "1", "2")
	assert.False(t, res)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mockInstance := &datasource.DescribeDBInstanceDetailResp{
		InstanceId:     "1",
		InstanceStatus: model.InstanceStatus_Running.String(),
	}
	mock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(mockInstance, nil).Build()
	defer mock2.UnPatch()
	res = actor.isInstanceRunning(&mocks.MockContext{}, shared.MySQL, "1", "2")
	assert.True(t, res)
}

func TestUpdateInstanceNameByInstanceID(t *testing.T) {
	actor := mockTaskActor()
	logMock1 := mockey.Mock(log.Log).Return().Build()
	defer logMock1.UnPatch()
	baseMock1 := mockey.Mock(fwctx.GetBizContext).Return(&fwctx.BizContext{}).Build()
	defer baseMock1.UnPatch()

	mock1 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(nil, fmt.Errorf("test")).Build()
	actor.updateInstanceNameByInstanceID(&mocks.MockContext{}, shared.MySQL, "1", "2")
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mockInstance := &datasource.DescribeDBInstanceDetailResp{
		InstanceId:     "1",
		InstanceStatus: v2.InstanceStatus_Running.String(),
	}
	mock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(mockInstance, nil).Build()
	mock3 := mockey.Mock((*repository.InspectionRepoImpl).UpdateInstanceNameByInstanceID).Return(fmt.Errorf("error")).Build()
	actor.updateInstanceNameByInstanceID(&mocks.MockContext{}, shared.MySQL, "1", "2")
	mock2.UnPatch()
	mock3.UnPatch()

	mockInstance1 := &datasource.DescribeDBInstanceDetailResp{
		InstanceId:     "1",
		InstanceStatus: v2.InstanceStatus_Running.String(),
	}
	mock21 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(mockInstance1, nil).Build()
	mock31 := mockey.Mock((*repository.InspectionRepoImpl).UpdateInstanceNameByInstanceID).Return(fmt.Errorf("error")).Build()
	actor.updateInstanceNameByInstanceID(&mocks.MockContext{}, shared.MySQL, "1", "2")
	defer mock21.UnPatch()
	mock31.UnPatch()

	mock4 := mockey.Mock((*repository.InspectionRepoImpl).UpdateInstanceNameByInstanceID).Return(nil).Build()
	defer mock4.UnPatch()
	actor.updateInstanceNameByInstanceID(&mocks.MockContext{}, shared.MySQL, "1", "2")
}
