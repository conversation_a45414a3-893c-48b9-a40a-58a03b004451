package inspection

import (
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/dslibmocks"
	"github.com/stretchr/testify/assert"
	"testing"
)

func mockCheckInspectionActor() *CheckInspectionActor {
	return &CheckInspectionActor{
		state:       &CheckInspectionState{},
		repo:        &repository.InspectionRepoImpl{},
		actorClient: &dslibmocks.MockActorClient{},
	}
}
func TestCheckInspectionActorGetState(t *testing.T) {
	actor := mockCheckInspectionActor()
	flag := actor.GetState()
	assert.NotNil(t, flag)
}

func TestNewCheckInspectionState(t *testing.T) {
	flag := NewCheckInspectionState([]byte{})
	assert.NotNil(t, flag)
}

func TestNewCheckInspectionActor(t *testing.T) {
	assert.NotNil(t, NewCheckInspectionActor(CheckInspectionActorIn{}))
}
