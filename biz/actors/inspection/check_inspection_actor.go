package inspection

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/inspection"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"encoding/json"
	"github.com/google/uuid"
	"go.uber.org/dig"
	"runtime/debug"
	"time"
)

const timeout = 30 * time.Minute

type CheckInspectionActorIn struct {
	dig.In
	InspectionService inspection.InspectionService
	Repo              repository.InspectionRepo
	ActorClient       cli.ActorClient
}

type CheckInspectionState struct {
	TaskId string
}

func NewCheckInspectionState(bytes []byte) *CheckInspectionState {
	ts := &CheckInspectionState{}
	_ = json.Unmarshal(bytes, ts)
	return ts
}

type CheckInspectionActor struct {
	state             *CheckInspectionState
	inspectionService inspection.InspectionService
	repo              repository.InspectionRepo
	actorClient       cli.ActorClient
}

// NewCheckInspectionActor 执行巡检的actor
func NewCheckInspectionActor(p CheckInspectionActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.CheckInspectionActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &CheckInspectionActor{
				state:             NewCheckInspectionState([]byte{}),
				inspectionService: p.InspectionService,
				repo:              p.Repo,
				actorClient:       p.ActorClient,
			}

		}),
	}
}

func (c *CheckInspectionActor) GetState() []byte {
	state, _ := json.Marshal(c.state)
	return state
}

func (c *CheckInspectionActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(timeout)
	sysCtx := context.WithValue(context.Background(), "biz-context", &fwctx.BizContext{
		LogID: "LogCheckInspectionActor-" + uuid.New().String(),
	})
	ctx = types.BuildMyContext(sysCtx, ctx, nil)
	switch ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "Check Inspection Actor Started")
		ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
	case *shared.AreYouOK:
		ctx.Respond(&shared.OK{})
	case *actor.ReceiveTimeout:
		c.protectUserCall(ctx, func() {
			c.checkJobStatus(ctx)
		})
		ctx.SetReceiveTimeout(timeout)
	case *actor.Stopping:
		// return
	}
	// return
}

func (c *CheckInspectionActor) checkJobStatus(ctx types.Context) {
	//1、获取所有的"执行中"状态的任务
	tasks, err := c.repo.GetTasksByStatus(ctx, int64(model.InspectionStatus_InspectionRunning))
	if err != nil {
		return
	}

	// 2、查看它对应的actor是否还存活
	for _, val := range tasks {
		resp, err := c.actorClient.KindOf(consts.InspectionActorKind).
			Call(ctx, conv.Int64ToStr(val.ID), &shared.GetInspectionActorStatusReq{})
		if err != nil { // 此时报错,表示不存在这个任务,需要重新生成一个任务
			c.dealTask(ctx, val)
		}
		switch rsp := resp.(type) {
		case *shared.GetInspectionActorStatusResp:
			if rsp.Status == shared.Undo {
				c.dealTask(ctx, val)
			}
		}
	}

}

func (c *CheckInspectionActor) dealTask(ctx types.Context, task *entity.InspectionTask) {
	config, err := c.repo.GetConfig(ctx, task.InstanceType, task.InstanceId, task.TenantId)
	if err != nil {
		return
	}
	if isTimeInTimeWindow(time.Now().UnixMilli(), config.InspectionExecutableStartTime, config.InspectionExecutableEndTime) {
		err = c.actorClient.KindOf(consts.InspectionActorKind).Send(ctx, conv.Int64ToStr(task.ID), &shared.CreateInspectionTask{
			InstanceId:          config.InstanceId,
			InstanceName:        config.InstanceName,
			InspectionStartTime: config.InspectionStartTime,
			InspectionEndTime:   config.InspectionEndTime,
			InspectionType:      int32(model.InspectionType_Auto),
			ExecuteStartTime:    config.InspectionExecutableStartTime,
			ExecuteEndTime:      config.InspectionExecutableEndTime,
			InstanceType:        shared.MySQL,
			TaskId:              task.ID,
			TenantId:            config.TenantId,
			RegionId:            config.RegionId,
		})
		if err != nil {
			log.Warn(ctx, "inspection: check actor call inspection actor err:%s", err.Error())
		}
	}
}

func (c *CheckInspectionActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call checkInspectionActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}
