package inspection

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2_new "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	dslibutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"fmt"
	"github.com/robfig/cron/v3"
	"go.uber.org/dig"
	"gorm.io/gorm"
	"runtime/debug"
	"sync"
	"time"
)

type NewCronActorIn struct {
	dig.In
	Repo        repository.InspectionRepo
	IdSvc       idgen.Service
	ActorClient cli.ActorClient
	Ds          datasource.DataSourceService
}

func NewCronActor(p NewCronActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.CronActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			newCronActor := &CronActor{
				repo:        p.Repo,
				state:       &cronActorState{},
				cronMap:     make(map[string]int),
				cron:        cron.New(),
				idSvc:       p.IdSvc,
				actorClient: p.ActorClient,
				ds:          p.Ds,
			}
			return newCronActor
		}),
	}
}

type CronActor struct {
	repo        repository.InspectionRepo
	state       *cronActorState
	cronMap     map[string]int
	cron        *cron.Cron
	idSvc       idgen.Service
	actorClient cli.ActorClient
	ds          datasource.DataSourceService
}

type cronActorState struct{}

const (
	timeIntervalSeconds uint64 = 60 * 10 // 10分钟
)

func (c *CronActor) GetState() []byte {
	state, _ := json.Marshal(c.state)
	return state
}

func (c *CronActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(time.Duration(timeIntervalSeconds) * time.Second)
	log.Debug(ctx, "msg is %#v", ctx.Message())
	log.Debug(ctx, "cronActor cronMap is %v", c.cronMap)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		c.OnStart(ctx)
	case *shared.CreateCronTask:
		c.protectUserCall(ctx, func() {
			c.createCronTask(ctx, msg)
		})
	case *shared.DeleteCronTask:
		c.protectUserCall(ctx, func() {
			c.deleteCronTask(ctx, msg)
		})
	case *shared.ModifyCronTask:
		c.protectUserCall(ctx, func() {
			c.modifyCronTask(ctx, msg)
		})
	case *actor.ReceiveTimeout:
		c.protectUserCall(ctx, func() {
			c.doWhenIdle(ctx)
		})
	}
}

func (c *CronActor) OnStart(ctx types.Context) {
	log.Info(ctx, "inspection: cronActor start")
	if c.cron != nil {
		c.cron.Start()
	}
	if c.cronMap == nil {
		c.cronMap = make(map[string]int)
	}
	configs, err := c.repo.GetAllConfig(ctx)
	if err != nil {
		log.Warn(ctx, "inspection: get all config error:%s", err.Error())
		return
	}
	log.Info(ctx, "inspection: there are %d configs", configs.Total)
	c.cron = cron.New() // 感觉这个可以不要,在依赖注入时候,已经注入了
	c.cron.Start()
	for _, val := range configs.InspectionConfigs {
		// 这里仅处理那些状态是running的实例
		if !c.isInstanceRunning(ctx, val) {
			// 重启时候,发现实例没有运行,不生成cron任务
			continue
		}
		_, err := c.generateCronTaskFromConfig(ctx, val)
		if err != nil {
			return
		}
	}
}

func (c *CronActor) isInstanceRunning(ctx types.Context, config *entity.InspectionConfig) bool {
	dType, err := model.DSTypeFromString(config.InstanceType)
	if err != nil {
		log.Warn(ctx, "unknow instance type %s", config.InstanceType)
		return false
	}
	inst, err := c.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		InstanceId: config.InstanceId,
		Type:       shared.DataSourceType(dType),
	})
	if err != nil {
		log.Warn(ctx, "inspection: rds api err %s", err.Error())
		return false
	}
	if inst != nil && inst.InstanceStatus == rdsModel_v2_new.InstanceStatus_Running.String() {
		return true
	}
	log.Warn(ctx, "inspection: instance not found %s", config.InstanceId)
	return false
}

func (c *CronActor) generateCronTaskFromConfig(ctx types.Context, config *entity.InspectionConfig) (int64, error) {
	// 这里加一步判断,如果出现启动时间在[起始时间，终止时间)之间的情况，需要立即发起一次cron任务
	log.Info(ctx, "inspection: begin to generate cron task from config,config is %s", dslibutils.Show(config))

	var (
		cronTime string
		//cronTimeInt int64
		mutex sync.Mutex
	)

	if c.needInspectionNow(ctx, config) {
		log.Info(ctx, "inspection: %s need inspection now", config.InstanceId)
		//cronTimeInt = time.Now().UnixMilli()
		cronTime = fmt.Sprintf("%d %d * * *", time.Now().Minute(), time.Now().Hour())

		// 发起一次巡检
		taskId, err := c.idSvc.NextID(ctx)
		if err != nil {
			return -1, err
		}
		//这里先插入发送一条自动巡检的消息，然后再插入一条自动巡检的巡检记录,否则用户看不到巡检记录
		startTime, endTime := GenerateStartTimeAndEndTime()
		msg := &shared.CreateInspectionTask{
			InstanceId:          config.InstanceId,
			InstanceName:        config.InstanceName,
			InspectionStartTime: startTime,
			InspectionEndTime:   endTime,
			InspectionType:      int32(model.InspectionType_Auto),
			ExecuteStartTime:    config.InspectionExecutableStartTime,
			ExecuteEndTime:      config.InspectionExecutableEndTime,
			//ExecuteTime:         cronTimeInt,
			InstanceType: shared.DataSourceType(shared.DataSourceType_value[config.InstanceType]),
			TaskId:       taskId,
			TenantId:     config.TenantId,
			RegionId:     config.RegionId,
		}
		err = c.actorClient.KindOf(consts.InspectionActorKind).Send(ctx, conv.Int64ToStr(taskId), msg)
		if err != nil {
			log.Warn(ctx, "inspection: send msg to inspection actor err:%s", err.Error())
			return -1, err
		}
	} else {
		log.Info(ctx, "inspection: %s is not need inspection now", config.InstanceId, config.InspectionExecutableStartTime)
		//cronTimeInt = GenerateExecuteTimeFromStartTime(config.InspectionExecutableStartTime)
		cronTime = getCronTime(config.InspectionExecutableStartTime)
	}
	log.Info(ctx, "inspection: generate cronTime is %s", cronTime)
	//cron.Parser.Parse()
	cronTaskId, err := c.cron.AddFunc(cronTime, func() {
		taskId, err := c.idSvc.NextID(ctx)
		if err != nil {
			return
		}
		SendMessageToInspectionActor(ctx, c, config, taskId)
	})
	if err != nil {
		log.Warn(ctx, "inspection: AddFunc error:%s", err.Error())
		return -1, err
	}
	log.Info(ctx, "inspection: generateCronTaskFromConfig success,instanceId is %s,cron task id is:%d",
		config.InstanceId, int(cronTaskId))
	// 这里加入到map里面去
	mutex.Lock()
	c.cronMap[config.InstanceId] = int(cronTaskId)
	mutex.Unlock()
	return int64(cronTaskId), nil
}

func (c *CronActor) needInspectionNow(ctx types.Context, config *entity.InspectionConfig) bool {
	/*  以下情况，需要立刻发起执行
	1、当前时间在可执行时间窗口,任务表没有查到任务
	2、当前时间在可执行时间窗口,任务表里面显示任务执行失败
	3、当前时间在可执行时间窗口,任务表里面显示任务执行中，但是对应的actor已经不工作了
	*/

	// 判断当前时间是否在时间窗口之间
	flag := isTimeInTimeWindow(time.Now().UnixMilli(),
		config.InspectionExecutableStartTime, config.InspectionExecutableEndTime)
	// 不在窗口时间，不能立刻创建任务
	log.Info(ctx, "inspection: needInspectionNow flag is %v", flag)
	if !flag {
		return false
	}

	// 这里还需要判断一下任务列表,判断一下是否有执行成功的巡检任务
	task, err := c.repo.GetTaskTodayByInstanceId(ctx, config.InstanceId, config.TenantId)
	if err != nil {
		log.Warn(ctx, "inspection: get task today by instance id error:%s", err.Error())
		// 没有查到今天的任务,需要立即发起任务
		return err == gorm.ErrRecordNotFound
	}
	// 查询到任务失败，而且在执行窗口期间
	log.Info(ctx, "inspection: task is %s", dslibutils.Show(task))
	return false
}

func (c *CronActor) createCronTask(ctx types.Context, msg *shared.CreateCronTask) error {
	log.Info(ctx, "inspection:create cron task,msg is %s", dslibutils.Show(msg))
	var res = make([]int64, 0, len(msg.InstanceInfo))
	var err error
	var mutex sync.Mutex
	for _, val := range msg.InstanceInfo {
		// 1、先查一下这个配置有没有,有的话,更新配置时间; 没有的话,创建配置记录
		log.Info(ctx, "inspection: instance info is %v", val)
		config := &entity.InspectionConfig{
			InstanceType:                  val.InstanceType.String(),
			InstanceId:                    val.InstanceId,
			InstanceName:                  val.InstanceName,
			RegionId:                      msg.RegionId,
			InspectionExecutableStartTime: msg.InspectionExecutableStartTime,
			InspectionExecutableEndTime:   msg.InspectionExecutableEndTime,
			InspectionStartTime:           msg.InspectionStartTime,
			InspectionEndTime:             msg.InspectionEndTime,
			IsOpen:                        int8(msg.IsOpen),
			TenantId:                      msg.TenantId,
		}
		if c.isInstanceConfigInMeta(ctx, config.InstanceType, val.InstanceId, msg.TenantId) {
			log.Info(ctx, "instance config is %v, val is %v ,msg is %v ,instance config is not in meta",
				utils.Show(config), utils.Show(val), utils.Show(msg))
			err = c.repo.UpdateConfig(ctx, config)
			if err != nil {
				ctx.Respond(&shared.InspectionCronCreated{
					Code:       shared.CreateFailed,
					ErrMessage: "定时任务创建失败",
					TaskInfo:   res,
				})
				return err
			}
		} else {
			err = c.repo.CreateConfig(ctx, config)
			if err != nil {
				log.Warn(ctx, "inspection: create inspection config error:%s,config is %s", err.Error(), dslibutils.Show(config))
				ctx.Respond(&shared.InspectionCronCreated{
					Code:       shared.CreateFailed,
					ErrMessage: "定时任务创建失败",
					TaskInfo:   res,
				})
				return err
			}
		}
		// 删除内存中现有的cron配置以及actor中的任务id
		mutex.Lock()
		entryId, ok := c.cronMap[val.InstanceId]
		if ok {
			delete(c.cronMap, val.InstanceId)
			c.cron.Remove(cron.EntryID(entryId))
		}
		mutex.Unlock()

		// 这里判断一下是否开启巡检,如果未开启,则操作完成;如果开启了,才开始生成cron任务
		if msg.IsOpen == 0 {
			log.Info(ctx, "inspection: config isOpen is 0,%s config inserted", val.InstanceId)
			continue
		}
		taskId, err := c.generateCronTaskFromConfig(ctx, config)
		if err != nil {
			ctx.Respond(&shared.InspectionCronCreated{
				Code:       shared.CreateFailed,
				ErrMessage: "定时任务创建失败",
				TaskInfo:   res,
			})
			return err
		}
		res = append(res, taskId)
	}
	ctx.Respond(&shared.InspectionCronCreated{
		Code:       shared.CreateSuccess,
		ErrMessage: "定时任务创建成功",
		TaskInfo:   res,
	})
	return nil
}
func (c *CronActor) deleteCronTask(ctx types.Context, msg *shared.DeleteCronTask) (err error) {
	// 删除定时任务
	var mutex sync.Mutex
	log.Info(ctx, "inspection: begin to delete cron task,msg is %s", dslibutils.Show(msg))

	for _, val := range msg.InstanceIds {
		// 1、更新元信息
		err = c.repo.UpdateConfig(ctx, &entity.InspectionConfig{
			InstanceId: val,
			IsOpen:     int8(0),
		})
		if err != nil {
			return
		}
		// 2、更新内存 && 删除cron任务
		mutex.Lock()
		if entryId, ok := c.cronMap[val]; ok {
			c.cron.Remove(cron.EntryID(entryId))
			delete(c.cronMap, val)
			log.Info(ctx, "inspection: delete %s cron and actor", val)
		}
		mutex.Unlock()
		log.Info(ctx, "inspection: delete %s cronTask", val)
	}
	return
}
func (c *CronActor) modifyCronTask(ctx types.Context, msg *shared.ModifyCronTask) (err error) {
	// 修改定时任务
	log.Info(ctx, "inspection: begin to modify cron task,msg is %s", dslibutils.Show(msg))

	for _, val := range msg.InstanceIds {
		// 1、更新元信息
		err = c.repo.UpdateConfig(ctx, &entity.InspectionConfig{
			InstanceId:                    val,
			IsOpen:                        int8(msg.IsOpen),
			InspectionExecutableStartTime: msg.InspectionExecutableStartTime,
			InspectionExecutableEndTime:   msg.InspectionExecutableEndTime,
		})
		if err != nil {
			return
		}
		// 这里根据IsOpen的值做一下区分
		if msg.IsOpen == 0 {
			// 这里的本质是更新原有的cron
			c.createCronTask(ctx, &shared.CreateCronTask{})
		} else {
			// 这里要删除原有的cron
			c.deleteCronTask(ctx, &shared.DeleteCronTask{})
		}

	}
	return
}

func (c *CronActor) isInstanceConfigInMeta(ctx types.Context, instanceType string, instanceId string, tenantId string) bool {
	config, err := c.repo.GetConfig(ctx, instanceType, instanceId, tenantId)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Warn(ctx, "inspection: inspection config for %s not found,%s", instanceId, err.Error())
			return false
		}
		log.Warn(ctx, "inspection: find inspection config err,%s", err.Error())
		return false
	}
	if config == nil || config.InstanceId == "" {
		log.Warn(ctx, "inspection: no config found")
		return false
	}
	return true
}

func (c *CronActor) doWhenIdle(ctx types.Context) {
	// 这里定时处理空闲时间的一些事情
	// 1、检查实例是否还在运行中,如果不在,则删除对应的任务
	var mutex sync.Mutex
	configs, err := c.repo.GetAllConfig(ctx)
	if err != nil {
		log.Warn(ctx, "inspection: get all config error:%s", err.Error())
		return
	}
	log.Info(ctx, "inspection: there are %d configs", configs.Total)
	for _, val := range configs.InspectionConfigs {
		// 这里仅处理那些状态是running的实例
		if !c.isInstanceRunning(ctx, val) {
			log.Warn(ctx, "inspection: instance is not running,delete cron task,config is %s", dslibutils.Show(val))
			// 发现实例没有运行, 删除内存中现有的cron配置以及actor中的任务id
			mutex.Lock()
			entryId, ok := c.cronMap[val.InstanceId]
			if ok {
				delete(c.cronMap, val.InstanceId)
				c.cron.Remove(cron.EntryID(entryId))
			}
			mutex.Unlock()
			// 将config里面的is_open状态置为关闭
			val.IsOpen = 0
			c.repo.UpdateConfig(ctx, val)
		}
	}

}

func (c *CronActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call cronActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func SendMessageToInspectionActor(ctx types.Context, c *CronActor, config *entity.InspectionConfig, taskId int64) {
	startTime, endTime := GenerateStartTimeAndEndTime()
	err := c.actorClient.KindOf(consts.InspectionActorKind).Send(ctx, conv.Int64ToStr(taskId), &shared.CreateInspectionTask{
		InstanceId:   config.InstanceId,
		InstanceName: config.InstanceName,
		// 这里改成昨天的时间
		InspectionStartTime: startTime,
		InspectionEndTime:   endTime,
		InspectionType:      int32(model.InspectionType_Auto),
		ExecuteStartTime:    config.InspectionExecutableStartTime,
		ExecuteEndTime:      config.InspectionExecutableEndTime,
		InstanceType:        shared.DataSourceType(shared.DataSourceType_value[config.InstanceType]),
		TaskId:              taskId,
		TenantId:            config.TenantId,
		RegionId:            config.RegionId,
	})
	if err != nil {
		log.Warn(ctx, "inspection: send msg to inspection actor err:%s", err.Error())
		return
	}
}
