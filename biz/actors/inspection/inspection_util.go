package inspection

import (
	"time"
)

const (
	percent  = "%"
	noUnit   = ""
	Byte     = "B"
	MegaByte = "MB"

	cpuUsage  = "CPU使用率"
	memUsage  = "内存使用率"
	diskUsage = "磁盘使用率"
)

type MessageReceiver struct {
	TenantId string
	UserIds  []string
}

func GenerateStartTimeAndEndTime() (starttime int64, endtime int64) {
	yesterday := time.Now().AddDate(0, 0, -1)
	// 设置昨天的开始时间为00:00:00
	startTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())
	// 设置昨天的结束时间为23:59:59
	endTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 23, 59, 59, 0, yesterday.Location())
	// 获取开始时间和结束时间的时间戳
	return startTime.UnixMilli(), endTime.UnixMilli()
}

func GenerateExecuteTimeFromStartTime(startTime int64) (executeTime int64) {
	todayStart := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.UTC).Add(time.Duration(startTime/1000) * time.Second)
	return todayStart.Add(24 * time.Hour).UnixMilli()
}
