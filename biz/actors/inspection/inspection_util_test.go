package inspection

import "testing"

func TestGenerateStartTimeAndEndTime(t *testing.T) {
	gotStarttime, gotEndtime := GenerateStartTimeAndEndTime()
	if gotStarttime == 0 {
		t.<PERSON><PERSON><PERSON>("GenerateStartTimeAndEndTime() gotStarttime = 0, want %v", gotStarttime)
	}
	if gotEndtime == 0 {
		t.<PERSON><PERSON><PERSON>("GenerateStartTimeAndEndTime() gotEndtime = 0, want %v", gotEndtime)
	}
}

func TestGenerateExecuteTimeFromStartTime(t *testing.T) {
	gotExecTime := GenerateExecuteTimeFromStartTime(0)
	if gotExecTime == 0 {
		t.Errorf("GenerateExecuteTimeFromStartTime() gotStarttime = %v, want 0", gotExecTime)
	}
}
