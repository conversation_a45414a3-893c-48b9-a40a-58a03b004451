package inspection

import (
	"code.byted.org/gopkg/lang/conv"
	"time"
)

const (
	slowLog = "慢SQL数量"
	qps     = "QPS"
	tps     = "TPS"
)

func getCronTime(inspectionExecutableStartTime int64) string {
	hour := conv.Int64ToStr(inspectionExecutableStartTime / 1000 / 3600)
	minute := conv.Int64ToStr(inspectionExecutableStartTime / 1000 % 3600 / 60)
	return minute + " " + hour + " * * *"
}

func isTimeInTimeWindow(now int64, timeWindowStart int64, timeWindowEnd int64) bool {
	// 转换为时间
	createTime := time.UnixMilli(now)
	startTime := time.UnixMilli(timeWindowStart).UTC()
	endTime := time.UnixMilli(timeWindowEnd).UTC()
	// 忽略日期后的时间
	ti := time.Date(1, 1, 1, createTime.Hour(), createTime.Minute(), createTime.Second(), 0, time.UTC)

	// 给定的时间窗口
	st := time.Date(1, 1, 1, startTime.Hour(), startTime.Minute(), startTime.Second(), 0, time.UTC)
	et := time.Date(1, 1, 1, endTime.Hour(), endTime.Minute(), endTime.Second(), 0, time.UTC)

	// 判断时间是否在给定的时间窗口内
	if ti.After(st) && ti.Before(et) {
		return true
	}
	return false
}
