package actors

import (
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

func (a *SessionActor) cancelCommnad(ctx types.Context, msg *shared.CancelCommand) {
	a.killQuery(ctx, msg.ConnectionId)
	a.releaseConnLock(ctx, msg.ConnectionId)
	cs, err := a.cmdRepo.GetCommandSet(ctx, msg.CommandSetId)
	if err != nil {
		ctx.Respond(&shared.CommandRejected{Reason: shared.Rejected_BadCommand})
		log.Info(ctx, "get command fail %v", err)
		return
	}
	if cs == nil || len(cs.Commands) == 0 {
		ctx.Respond(&shared.CommandRejected{Reason: shared.Rejected_BadCommand})
		log.Info(ctx, "command set %v is empty", msg.CommandSetId)
		return
	}
	// 停止onlineddl task
	err = a.cancelTask(ctx, cs)
	if err != nil {
		ctx.Respond(&shared.CommandRejected{Reason: shared.Rejected_BadCommand})
		log.Info(ctx, "cancel onlineddl task failed", msg.CommandSetId)
		return
	}
	now := time.Now().UnixMilli()
	cs.CancelNonTerminated(now, `user stop task`).UpdateProgress(now)
	a.cmdRepo.SaveCommandSet(ctx, cs)
	log.Info(ctx, "command set %s canceled by user", msg.CommandSetId)
	ctx.Respond(&shared.CommandAccepted{})
}

func (a *SessionActor) cancelTask(ctx types.Context, cs *entity.CommandSet) error {
	var taskList []string
	for _, cmd := range cs.Commands {
		if taskId, ok := cmd.Extra[SQLTaskID].(string); ok && taskId != "" && cmd.State == entity.CommandExecuting {
			taskList = append(taskList, taskId)
		}
	}
	// 通过task id调用sqltask actor停止任务
	for _, taskId := range taskList {
		_, err := a.actorCli.KindOf(consts.SqlTaskActorKind).Call(ctx, strings.Join([]string{fwctx.GetTenantID(ctx), a.state.DataSource.InstanceId, taskId}, consts.ActorSplitSymbol), &shared.StopDDLTaskReq{
			SqlTaskId: taskId,
			TenantId:  fwctx.GetTenantID(ctx),
		})
		if err != nil {
			log.Error(ctx, "stop ddl task fail, %s", err)
			return consts.ErrorWithParam(model.ErrorCode_InternalError, "stop ddl task fail", err)
		}
	}
	return nil
}
