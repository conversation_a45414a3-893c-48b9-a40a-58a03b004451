package actors

import (
	bizConfig "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"errors"
	"github.com/bytedance/mockey"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	dsCtx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/ti/disco_sdk/byte_dns"
	dns "code.byted.org/ti/disco_sdk/private_dns"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type DiscoSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *DiscoSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *DiscoSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestDiscoSuite(t *testing.T) {
	//	suite.Run(t, new(DiscoSuite))
}

func (suite *DiscoSuite) TestCacheDomainHelper_ListDomainRecords_Err() {
	// types.context的mock方法。
	actorCtx := mocks.NewMockContext(suite.ctrl)
	actorCtx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
	actorCtx.EXPECT().Value(gomock.Any()).Return(&dsCtx.BizContext{
		TenantID: "111",
	}).AnyTimes()

	mockey.PatchConvey("normal", suite.T(), func() {
		domain := "mongoreplicaf1c036ce8c480.mongodb-boe.ivolces.com"
		//host, rootDomain := utils.SplitDomain(domain)
		mockey.Mock((*dns.Client).ListDomainRecords).Return(nil, errors.New("Error")).Build()

		disc := &discoActor{}
		helper, err := disc.cacheDomainHelper(actorCtx, domain)
		suite.NotEmpty(err)
		suite.Empty(helper)
	})

}

func (suite *DiscoSuite) TestCacheDomainHelper() {
	// types.context的mock方法。
	actorCtx := mocks.NewMockContext(suite.ctrl)
	actorCtx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
	actorCtx.EXPECT().Value(gomock.Any()).Return(&dsCtx.BizContext{
		TenantID: "111",
	}).AnyTimes()

	mockey.PatchConvey("normal", suite.T(), func() {
		domain := "mongoreplicaf1c036ce8c480.mongodb-boe.ivolces.com"
		//host, rootDomain := utils.SplitDomain(domain)
		mockey.Mock((*dns.Client).ListDomainRecords).Return(&byte_dns.Records{
			TotalCount: 1,
			PageNumber: 1,
			PageSize:   20,
			TotalPages: 1,
			DomainRecords: []byte_dns.Record{
				{
					DomainName: "ivolces.com",
					RR:         "mongoreplicaf1c036ce8c480.mongodb-boe",
					Value:      "***********",
				},
				{},
			},
		}, nil).Build()

		disc := &discoActor{}
		helper, err := disc.cacheDomainHelper(actorCtx, domain)
		suite.Empty(err)
		suite.NotEmpty(helper)
	})
}

func (suite *DiscoSuite) TestCacheMongoAddress_cacheDomainHelper_Err() {
	// types.context的mock方法。
	actorCtx := mocks.NewMockContext(suite.ctrl)
	actorCtx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
	actorCtx.EXPECT().Value(gomock.Any()).Return(&dsCtx.BizContext{
		TenantID: "111",
	}).AnyTimes()
	actorCtx.EXPECT().Respond(gomock.Any()).Return().AnyTimes()
	mockey.PatchConvey("normal", suite.T(), func() {
		msg := &shared.CacheMongoAddress{
			Info: &shared.UserInfo{
				InstanceId:  "mongo-replica-f1c036ce8c48",
				TenantId:    "xxx",
				MongoNodeId: "mongo-replica-f1c036ce8c48",
			},
		}
		mockey.Mock((*discoActor).makeSureClient).Return().Build()
		mockey.Mock((*discoActor).cacheDomainHelper).Return("***********", errors.New("Error")).Build()
		mockey.Mock((*discoActor).getInstancePrivateAddress).Return("***********", "4306", nil).Build()
		// mockey.Mock((*discoActor).getInstancePrivateAddress).Return("***********", "4306", nil).Build()
		disc := &discoActor{
			cache: map[string]Address{
				"mongo-replicc": {
					Ip:   "***********",
					Port: "3717",
				},
			},
		}
		disc.cacheMongoAddress(actorCtx, msg)
	})

}
func (suite *DiscoSuite) TestCacheMongoAddress() {
	// types.context的mock方法。
	actorCtx := mocks.NewMockContext(suite.ctrl)
	actorCtx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
	actorCtx.EXPECT().Value(gomock.Any()).Return(&dsCtx.BizContext{
		TenantID: "111",
	}).AnyTimes()
	actorCtx.EXPECT().Respond(gomock.Any()).Return().AnyTimes()
	mockey.PatchConvey("normal", suite.T(), func() {
		msg := &shared.CacheMongoAddress{
			Info: &shared.UserInfo{
				InstanceId:  "mongo-replica-f1c036ce8c48",
				TenantId:    "xxx",
				MongoNodeId: "mongo-replica-f1c036ce8c48",
			},
		}
		mockey.Mock((*discoActor).makeSureClient).Return().Build()
		mockey.Mock((*discoActor).cacheDomainHelper).Return("***********", nil).Build()
		mockey.Mock((*discoActor).getInstancePrivateAddress).Return("***********", "4306", nil).Build()
		disc := &discoActor{
			cache: map[string]Address{
				"mongo-replicc": {
					Ip:   "***********",
					Port: "3717",
				},
			},
		}
		disc.cacheMongoAddress(actorCtx, msg)
	})
}

func (suite *DiscoSuite) TestgetInstancePrivateAddress() {
	// types.context的mock方法。
	actorCtx := mocks.NewMockContext(suite.ctrl)
	actorCtx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
	actorCtx.EXPECT().Value(gomock.Any()).Return(&dsCtx.BizContext{
		TenantID: "111",
	}).AnyTimes()
	actorCtx.EXPECT().Respond(gomock.Any()).Return().AnyTimes()
	mockey.PatchConvey("normal", suite.T(), func() {

		cfg := config.NewMockConfigProvider(suite.ctrl)
		cfg.EXPECT().Get(gomock.Any()).Return(&bizConfig.Config{}).AnyTimes()

		mgrProvider := mocks.NewMockMgrProvider(suite.ctrl)
		mgrClient := mocks.NewMockMgrClient(suite.ctrl)

		mgrProvider.EXPECT().Get().Return(mgrClient).AnyTimes()
		mgrClient.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(errors.New("error")).AnyTimes()

		msg := &shared.CacheMongoAddress{
			Info: &shared.UserInfo{
				InstanceId:  "mongo-replica-f1c036ce8c48",
				TenantId:    "xxx",
				MongoNodeId: "mongo-replica-f1c036ce8c48",
			},
		}
		disc := &discoActor{
			mongomgr: mgrProvider,
		}
		disc.getInstancePrivateAddress(actorCtx, msg.Info.InstanceId, msg.Info.TenantId, msg.Info.MongoNodeId)
	})
}
