package clean_data

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
)

type CleanActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *CleanActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *CleanActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestCleanActorSuite(t *testing.T) {
	suite.Run(t, new(CleanActorSuite))
}

func (suite *CleanActorSuite) TestGetState() {
	cleanactor := CleanActor{}
	ret := cleanactor.GetState()
	suite.Empty(ret)
}

func (suite *CleanActorSuite) TestNewCleanActor() {
	ret := NewCleanActor(NewCleanActorIn{
		In: dig.In{},
		Db: nil,
	})
	suite.NotEmpty(ret)
}

func (suite *CleanActorSuite) TestProcessStarted() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Started{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()

	dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	cleanactor := NewCleanActor(NewCleanActorIn{
		In: dig.In{},
		Db: dbProvider,
	}).Producer.Spawn(consts.CleanActorKind, consts.SingletonActorName, []byte{})
	cleanactor.Process(Ctx)
}

func (suite *CleanActorSuite) TestProcessStopping() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Stopping{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()

	cleanactor := CleanActor{}
	cleanactor.Process(Ctx)
}

func (suite *CleanActorSuite) TestProcessStopped() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Stopped{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()

	dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	cleanactor := NewCleanActor(NewCleanActorIn{
		In: dig.In{},
		Db: dbProvider,
	}).Producer.Spawn(consts.CleanActorKind, consts.SingletonActorName, []byte{})
	cleanactor.Process(Ctx)
}

func (suite *CleanActorSuite) TestProcessRestarting() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Restarting{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()

	dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	cleanactor := NewCleanActor(NewCleanActorIn{
		In: dig.In{},
		Db: dbProvider,
	}).Producer.Spawn(consts.CleanActorKind, consts.SingletonActorName, []byte{})
	cleanactor.Process(Ctx)
}

func (suite *CleanActorSuite) TestProcessAreYouOK() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.AreYouOK{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()

	dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	cleanactor := NewCleanActor(NewCleanActorIn{
		In: dig.In{},
		Db: dbProvider,
	}).Producer.Spawn(consts.CleanActorKind, consts.SingletonActorName, []byte{})
	cleanactor.Process(Ctx)
}

//func (suite *CleanActorSuite) TestProcessReceiveTimeout() {
//	mockito.PatchConvey("test selectZkBackupMember3", suite.T(), func() {
//		{
//			Ctx := mocks.NewMockContext(suite.ctrl)
//
//			Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//			Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
//			Ctx.EXPECT().Message().Return(&actor.ReceiveTimeout{}).AnyTimes()
//			Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
//
//			dbProvider := mocks.NewMockDBProvider(suite.ctrl)
//			conn := &db.DB{}
//			dbProvider.EXPECT().GetMetaDB(gomock.Any()).Return(conn)
//
//			cleanactor := NewCleanActor(NewCleanActorIn{
//				In: dig.In{},
//				Db: dbProvider,
//			}).Producer.Spawn(consts.CleanActorKind, consts.SingletonActorName, []byte{})
//
//			Mock(conn.Exec).Return(&gorm.DB{Error: nil}).Build()
//			cleanactor.Process(Ctx)
//		}
//	})
//}
