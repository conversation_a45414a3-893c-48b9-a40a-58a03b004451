package clean_data

import (
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/shared"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"go.uber.org/dig"
)

type NewCleanActorIn struct {
	dig.In
	Db dal.DBProvider
}

type CleanActor struct {
	Db dal.DBProvider
}

func (a *CleanActor) GetState() []byte {
	return nil
}

func NewCleanActor(p NewCleanActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.CleanActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &CleanActor{
				Db: p.Db,
			}
		}),
	}
}

func (a *CleanActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(time.Second * 30)
	switch ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "Clean Actor Started")
		ctx.SetReceiveTimeout(time.Second * 30)
	case *actor.Stopping:
		return
	case *actor.Stopped:
		return
	case *actor.Restarting:
		return
	case *shared.AreYouOK:
		ctx.Respond(&shared.OK{})
	case *actor.ReceiveTimeout:
		conn := a.Db.GetMetaDB(ctx)
		deleteTimeFlag := time.Now().AddDate(0, 0, -5).Unix() * 1000
		if err := conn.Exec("delete from dbw_cmd_set where create_time<? limit 2000", deleteTimeFlag).Error; err != nil {
			log.Warn(ctx, "clean expired data fail:%v", err)
		}
		if err := conn.Exec("delete from dbw_cmd where end_time>0 and end_time<? limit 2000", deleteTimeFlag).Error; err != nil {
			log.Warn(ctx, "clean expired data fail:%v", err)
		}
		var maxCreateTime int64
		if err := conn.Raw("select max(create_time) from (select create_time from dbw_cmd_result limit 2000) as t").Scan(&maxCreateTime).Error; err != nil {
			log.Warn(ctx, "get max create time fail:%v", err)
			return
		}
		if maxCreateTime <= deleteTimeFlag {
			if err := conn.Exec("delete from dbw_cmd_result limit 2000").Error; err != nil {
				log.Warn(ctx, "clean expired data fail:%v", err)
			}
			return
		}
		log.Info(ctx, "clean expired data success")
	}
}
