package cclrule

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"encoding/json"
	"github.com/google/uuid"
	"go.uber.org/dig"
	"sync"
	"time"
)

const timeout = 30 * time.Minute

type RuleInspectionActorIn struct {
	dig.In
	ActorClient cli.ActorClient
	CCLRuleDal  dal.SqlCCLRulesDAL
}
type RuleInspectionActor struct {
	actorClient cli.ActorClient
	ccLRuleDal  dal.SqlCCLRulesDAL
	state       *RuleInspectionState
}

type RuleInspectionState struct {
	LastCheckActiveTimeStamp  int64
	LastCheckStoppedTimeStamp int64
	LastCheckDoneTimeStamp    int64
	LastCheckDeletedTimeStamp int64
	LastCheckNoneTimeStamp    int64
}

func NewRuleInspectionState(bytes []byte) *RuleInspectionState {
	ts := &RuleInspectionState{}
	json.Unmarshal(bytes, ts)
	return ts
}

func NewCCLRuleInspectionActor(p RuleInspectionActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.CCLRuleInspectionActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &RuleInspectionActor{
				state:       NewRuleInspectionState(state),
				ccLRuleDal:  p.CCLRuleDal,
				actorClient: p.ActorClient,
			}
		}),
	}
}

func (a *RuleInspectionActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(timeout)
	sysCtx := context.WithValue(context.Background(), "biz-context", &fwctx.BizContext{
		LogID: "LogRuleInspectionActor-" + uuid.New().String(),
	})
	ctx = types.BuildMyContext(sysCtx, ctx, nil)
	switch ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "CCLRule Inspection Actor Started")
		ctx.SetReceiveTimeout(timeout)
	case *shared.AreYouOK:
		ctx.Respond(&shared.OK{})
	case *actor.ReceiveTimeout:
		log.Info(ctx, "Start to Check CCL rules state!")
		a.checkCCLRuleConsistence(ctx)
		ctx.SetReceiveTimeout(timeout)
		return
	case *shared.CreateManualCCLInspectionTaskReq:
		log.Info(ctx, "Start to Check CCL rules state manually")
		a.checkCCLRuleConsistence(ctx)
		log.Info(ctx, "Check CCL rules state manually finished")
		return
	case *actor.Stopping:
		return
	}
}

func (a *RuleInspectionActor) checkCCLRuleConsistence(ctx types.Context) {
	ruleStateSet := []model.RuleState{model.RuleState_ACTIVE, model.RuleState_STOPPED, model.RuleState_DONE, model.RuleState_NONE}
	// get instance lists
	instIds := a.ccLRuleDal.ListInstancesByState(ctx, ruleStateSet)
	var wg sync.WaitGroup
	wg.Add(4)
	go func() {
		defer wg.Done()
		// check Active CCL Rules
		a.checkActiveRule(ctx, instIds)
	}()

	go func() {
		defer wg.Done() // Decrement the counter when the function finishes
		// check Done CCL Rules
		a.checkDoneRule(ctx, instIds)
	}()

	go func() {
		defer wg.Done()
		// check Stopped CCL Rules
		a.checkStoppedRule(ctx, instIds)
	}()

	go func() {
		defer wg.Done()
		// check None CCL Rules
		a.checkNoneRule(ctx, instIds)
	}()
	wg.Wait()
	log.Info(ctx, "Check SQL CCL Rules State completed!")
}

func (a *RuleInspectionActor) checkActiveRule(ctx types.Context, instIds []string) {
	log.Info(ctx, "checkActiveRule Start!")
	var latestTS int64
	actorName := "cclTaskInspectionActive"
	for _, instId := range instIds {
		Rules, err := a.ccLRuleDal.ListCCLRuleByState(ctx, instId, []model.RuleState{model.RuleState_ACTIVE}, 0, a.state.LastCheckActiveTimeStamp)
		if err != nil {
			log.Warn(ctx, "Get Active CCL rules failed %+v", err)
			//跳过，继续执行下个实例
			continue
		}
		if Rules.Total > 0 {
			for _, rule := range Rules.SqlCCLRules {
				log.Info(ctx, "Current inspection Active rule is %d", rule.ID)
				now := time.Now().UnixMilli()
				//rand.Seed(now)
				remainTime := int32((rule.CreatedAt + rule.Duration*60000 - now) / 60000) //minutes
				// 剩余时间不超过10minutes,忽略处理
				if remainTime < 10 {
					log.Info(ctx, "current rule %d remainTime less than 10 minutes, skip", rule.ID)
					continue
				}
				// 判断该规则在ccl show中是是否可以查询到
				cclShowMsg := &shared.DescribeRealTimeCCLRulesReq{
					TaskId:     rule.ID,
					ActionType: shared.CclShow,
				}
				actorResp, err := a.actorClient.KindOf(consts.CCLRuleActorKind).Call(ctx, actorName, cclShowMsg)
				if err != nil {
					log.Warn(ctx, "CCLRule inspection Actor show rule %d error:%s", rule.ID, err)
					continue
				}
				switch msg0 := actorResp.(type) {
				case *shared.CCLRuleActorFailResp:
					log.Warn(ctx, "CCLRule inspection Actor show rule %d failed %+v", rule.ID, msg0)
				case *shared.DescribeRealTimeCCLRulesResp:
					log.Info(ctx, "CCLRule inspection Actor show rule %d success", actorResp)
					if actorResp.(*shared.DescribeRealTimeCCLRulesResp).Consistent {
						// 若一致，更新时间戳
						if latestTS < rule.UpdatedAt {
							latestTS = rule.UpdatedAt
						}
						log.Info(ctx, "CCL Rule %d on metaDb is consistent with proxy", rule.ID)
					} else {
						// 不一致告警
						log.Error(ctx, "CCL Rule %d on metaDb is inconsistent with proxy", rule.ID)
					}
				default:
					log.Warn(ctx, "DescribeRealTimeCCLRulesShow actor response Unknown resp type %s", actorResp)
				}
			}
		}
	}
	if latestTS > a.state.LastCheckActiveTimeStamp {
		a.state.LastCheckActiveTimeStamp = latestTS
	}
	ctx.SaveState()
	log.Info(ctx, "checkActiveRule Finished!")
}

func (a *RuleInspectionActor) checkDeletedRule(ctx types.Context, instIds []string) {
	log.Info(ctx, "checkDeletedRule Start!")
	var latestTS int64
	actorName := "cclTaskInspectionDeleted"
	for _, instId := range instIds {
		Rules, err := a.ccLRuleDal.ListCCLRuleByState(ctx, instId, []model.RuleState{model.RuleState_DELETED, model.RuleState_ACTIVE}, 1, a.state.LastCheckDeletedTimeStamp)
		if err != nil {
			log.Warn(ctx, "Get Deleted CCL rules failed %+v", err)
			//跳过，继续执行下个实例
			continue
		}
		if Rules.Total > 0 {
			var taskIds []int64
			for _, rule := range Rules.SqlCCLRules {
				taskIds = append(taskIds, rule.ID)
			}
			log.Info(ctx, "Current inspection Deleted rule is %s", taskIds)
			msg := &shared.DescribeRealTimeCCLRulesReq{
				TaskIds:    taskIds,
				ActionType: shared.CclDelete,
			}
			if len(taskIds) > 0 {
				msg.TaskId = taskIds[0]
			}
			actorResp, err := a.actorClient.KindOf(consts.CCLRuleActorKind).Call(ctx, actorName, msg)
			if err != nil {
				log.Info(ctx, "CCLRule inspection Actor delete rules error:%s", err)
				//跳过，继续执行下个实例
				continue
			}
			switch msg := actorResp.(type) {
			case *shared.CCLRuleActorFailResp:
				log.Info(ctx, "CCLRule inspection Actor delete rules %s failed %+v", taskIds, msg)
			case *shared.DescribeRealTimeCCLRulesResp:
				if actorResp.(*shared.DescribeRealTimeCCLRulesResp).Success {
					// 若执行成功，更新时间戳
					if latestTS < Rules.SqlCCLRules[Rules.Total-1].UpdatedAt {
						latestTS = Rules.SqlCCLRules[Rules.Total-1].UpdatedAt
					}
					log.Info(ctx, "CCLRule inspection Actor delete rules %d Success", taskIds)
				} else {
					log.Warn(ctx, "CCLRule inspection Actor delete rules %d failed", taskIds)
				}
			default:
				log.Warn(ctx, "DescribeRealTimeCCLRulesDelete actor response  Unknown resp type %s", actorResp)
			}
		}
	}
	if latestTS > a.state.LastCheckDeletedTimeStamp {
		a.state.LastCheckDeletedTimeStamp = latestTS
	}
	ctx.SaveState()
	log.Info(ctx, "checkDeletedRule Finished!")
}

func (a *RuleInspectionActor) checkStoppedRule(ctx types.Context, instIds []string) {
	log.Info(ctx, "checkStoppedRule Start!")
	var latestTS int64
	actorName := "cclTaskInspectionStopped"
	for _, instId := range instIds {
		Rules, err := a.ccLRuleDal.ListCCLRuleByState(ctx, instId, []model.RuleState{model.RuleState_STOPPED}, 0, a.state.LastCheckStoppedTimeStamp)
		if err != nil {
			log.Warn(ctx, "Get Stopped CCL rules failed %+v", err)
			//跳过，继续执行下个实例
			continue
		}
		log.Info(ctx, " %s List Stopped CCLRules is %s", instId, Rules)
		if Rules.Total > 0 {
			var taskIds []int64
			for _, rule := range Rules.SqlCCLRules {
				taskIds = append(taskIds, rule.ID)
			}
			log.Info(ctx, "Current inspection Stopped rule is %s", taskIds)
			msg := &shared.DescribeRealTimeCCLRulesReq{
				TaskIds:    taskIds,
				ActionType: shared.CclStop,
			}
			if len(taskIds) > 0 {
				msg.TaskId = taskIds[0]
			}
			actorResp, err := a.actorClient.KindOf(consts.CCLRuleActorKind).Call(ctx, actorName, msg)
			if err != nil {
				log.Info(ctx, "CCLRule inspection Actor stop rules error:%s", err)
				//跳过，继续执行下个实例
				continue
			}
			switch msg := actorResp.(type) {
			case *shared.CCLRuleActorFailResp:
				log.Info(ctx, "CCLRule inspection Actor stop rules %s failed %+v", taskIds, msg)
			case *shared.DescribeRealTimeCCLRulesResp:
				if actorResp.(*shared.DescribeRealTimeCCLRulesResp).Success {
					// 若执行成功，更新时间戳
					if latestTS < Rules.SqlCCLRules[Rules.Total-1].UpdatedAt {
						latestTS = Rules.SqlCCLRules[Rules.Total-1].UpdatedAt
					}
					log.Info(ctx, "CCLRule inspection Actor stop rules %d Success", taskIds)
				} else {
					log.Warn(ctx, "CCLRule inspection Actor stop rules %d failed", taskIds)
				}
			default:
				log.Warn(ctx, "DescribeRealTimeCCLRulesStop actor response Unknown resp type %s", actorResp)
			}
		}
	}
	if latestTS > a.state.LastCheckStoppedTimeStamp {
		a.state.LastCheckStoppedTimeStamp = latestTS
	}
	ctx.SaveState()
	log.Info(ctx, "checkStoppedRule Finished!")
}

func (a *RuleInspectionActor) checkDoneRule(ctx types.Context, instIds []string) {
	log.Info(ctx, "checkDoneRule Start!")
	var latestTS int64
	actorName := "cclTaskInspectionDone"
	for _, instId := range instIds {
		Rules, err := a.ccLRuleDal.ListCCLRuleByState(ctx, instId, []model.RuleState{model.RuleState_DONE}, 0, a.state.LastCheckDoneTimeStamp)
		if err != nil {
			log.Warn(ctx, "Get Done CCL rules failed %+v", err)
			//跳过，继续执行下个实例
			continue
		}
		log.Info(ctx, "%s List Done CCLRules is %s", instId, Rules)
		if Rules.Total > 0 {
			var taskIds []int64
			for _, rule := range Rules.SqlCCLRules {
				taskIds = append(taskIds, rule.ID)
			}
			log.Info(ctx, "Current inspection Done rule is %s", taskIds)
			msg := &shared.DescribeRealTimeCCLRulesReq{
				TaskIds:    taskIds,
				ActionType: shared.CclStop,
			}
			if len(taskIds) > 0 {
				msg.TaskId = taskIds[0]
			}
			actorResp, err := a.actorClient.KindOf(consts.CCLRuleActorKind).Call(ctx, actorName, msg)
			if err != nil {
				log.Info(ctx, "CCLRule inspection Actor Done rules error:%s", err)
				//跳过，继续执行下个实例
				continue
			}
			switch msg := actorResp.(type) {
			case *shared.CCLRuleActorFailResp:
				log.Info(ctx, "CCLRule inspection Actor Done rules %s failed %+v", taskIds, msg)
			case *shared.DescribeRealTimeCCLRulesResp:
				if actorResp.(*shared.DescribeRealTimeCCLRulesResp).Success {
					// 若执行成功，更新时间戳
					if latestTS < Rules.SqlCCLRules[Rules.Total-1].UpdatedAt {
						latestTS = Rules.SqlCCLRules[Rules.Total-1].UpdatedAt
					}
					log.Info(ctx, "CCLRule inspection Actor Done rules %d Success", taskIds)
				} else {
					log.Warn(ctx, "CCLRule inspection Actor Done rules %d failed", taskIds)
				}
			default:
				log.Warn(ctx, "DescribeRealTimeCCLRulesDone actor response Unknown resp type %s", actorResp)
			}
		}

	}
	if latestTS > a.state.LastCheckDoneTimeStamp {
		a.state.LastCheckDoneTimeStamp = latestTS
	}
	ctx.SaveState()
	log.Info(ctx, "checkDoneRule Finished!")
}

// 检查rule状态为None
func (a *RuleInspectionActor) checkNoneRule(ctx types.Context, instIds []string) {
	log.Info(ctx, "checkNoneRule Start %s!", instIds)
	var latestTS int64
	actorName := "cclTaskInspectionNone"
	for _, instId := range instIds {
		Rules, err := a.ccLRuleDal.ListCCLRuleByState(ctx, instId, []model.RuleState{model.RuleState_NONE, model.RuleState_ACTIVE}, 0, a.state.LastCheckNoneTimeStamp)
		if err != nil {
			log.Warn(ctx, "Get None status CCL rules failed %+v", err)
			//跳过，继续执行下个实例
			continue
		}
		log.Info(ctx, "%s List None status CCLRules is %s", instId, Rules)
		if Rules.Total > 0 {
			var taskIds []int64
			for _, rule := range Rules.SqlCCLRules {
				taskIds = append(taskIds, rule.ID)
			}
			log.Debug(ctx, "Current inspection None rule is %s", taskIds)
			msg := &shared.DescribeRealTimeCCLRulesReq{
				TaskIds:    taskIds,
				ActionType: shared.CclStop,
			}
			if len(taskIds) > 0 {
				msg.TaskId = taskIds[0]
			}
			actorResp, err := a.actorClient.KindOf(consts.CCLRuleActorKind).Call(ctx, actorName, msg)
			if err != nil {
				log.Info(ctx, "CCLRule inspection Actor None status rules error:%s", err)
				//跳过，继续执行下个实例
				continue
			}
			switch msg := actorResp.(type) {
			case *shared.CCLRuleActorFailResp:
				log.Info(ctx, "CCLRule inspection Actor None status rules %s failed %+v", taskIds, msg)
			case *shared.DescribeRealTimeCCLRulesResp:
				if actorResp.(*shared.DescribeRealTimeCCLRulesResp).Success {
					// 若执行成功，更新时间戳
					if latestTS < Rules.SqlCCLRules[Rules.Total-1].UpdatedAt {
						latestTS = Rules.SqlCCLRules[Rules.Total-1].UpdatedAt
					}
					log.Info(ctx, "CCLRule inspection Actor None status rules %d Success", taskIds)
				} else {
					log.Warn(ctx, "CCLRule inspection Actor None status rules %d failed", taskIds)
				}
			default:
				log.Warn(ctx, "DescribeRealTimeCCLRulesNone actor response Unknown resp type %s", actorResp)
			}
		}

	}
	if latestTS > a.state.LastCheckNoneTimeStamp {
		a.state.LastCheckNoneTimeStamp = latestTS
	}
	ctx.SaveState()
	log.Info(ctx, "checkNoneRule Finished!")
}

func (a *RuleInspectionActor) GetState() []byte {
	state, _ := json.Marshal(a.state)
	return state
}
