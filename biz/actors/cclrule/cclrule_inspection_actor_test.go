package cclrule

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
	"testing"
)

type RuleInspectionActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *RuleInspectionActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}
func (suite *RuleInspectionActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}
func TestRuleInspectionActorSuite(t *testing.T) {
	suite.Run(t, new(RuleInspectionActorSuite))
}

func (suite *RuleInspectionActorSuite) TestNewRuleInspectionActorSuite() {
	ret := NewCCLRuleInspectionActor(RuleInspectionActorIn{
		In: dig.In{},
	})
	suite.NotEmpty(ret)
}

func (suite *RuleInspectionActorSuite) TestNewRuleInspectionActorStatusSuite() {
	inspectionActor := RuleInspectionActor{}
	ts := &RuleInspectionState{}
	bytes := inspectionActor.GetState()
	json.Unmarshal(bytes, ts)
}

func (suite *RuleInspectionActorSuite) TestProcessStarted() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Started{}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()

	RuleInspectionActor := NewCCLRuleInspectionActor(RuleInspectionActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.CCLRuleInspectionActorKind, "xxx", []byte{})
	RuleInspectionActor.Process(Ctx)
}

func (suite *RuleInspectionActorSuite) TestProcessStopping() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Stopping{}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	RuleInspectionActor := RuleInspectionActor{}
	RuleInspectionActor.Process(Ctx)
}

func (suite *RuleInspectionActorSuite) TestProcessAreYouOK() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Sender().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.AreYouOK{}).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	RuleInspectionActor := RuleInspectionActor{}
	RuleInspectionActor.Process(Ctx)
}

func (suite *RuleInspectionActorSuite) TestProcessReceiveTimeOut() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.ReceiveTimeout{}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	ruleStateSet := []model.RuleState{model.RuleState_ACTIVE, model.RuleState_STOPPED, model.RuleState_DONE, model.RuleState_NONE}
	instList := []string{"111"}
	cclRules := &dao.SqlCclRulesInfo{
		Total: 1,
		SqlCCLRules: []*dao.SqlCCLRule{
			{
				ID:               1,
				RuleID:           2,
				UserID:           "",
				TenantID:         "",
				InstanceID:       "",
				InstanceType:     "",
				Duration:         0,
				Keywords:         "",
				SqlType:          0,
				State:            0,
				ConcurrencyCount: 0,
				CreatedAt:        0,
				StoppedAt:        0,
				UpdatedAt:        0,
				DeletedAt:        0,
				Deleted:          0,
			},
		},
	}
	cclRuleDal.EXPECT().ListInstancesByState(gomock.Any(), ruleStateSet).Return(instList)
	cclRuleDal.EXPECT().ListCCLRuleByState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cclRules, nil).AnyTimes()
	kc := mocks.NewMockKindClient(suite.ctrl)
	actorCli.EXPECT().KindOf(consts.CCLRuleActorKind).Return(kc).AnyTimes()
	resp := &shared.DescribeRealTimeCCLRulesResp{
		Success:    true,
		Consistent: true,
	}
	kc.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes()
	RuleInspectionActor := RuleInspectionActor{
		actorClient: actorCli,
		ccLRuleDal:  cclRuleDal,
	}
	RuleInspectionActor.state = &RuleInspectionState{
		LastCheckDoneTimeStamp:    0,
		LastCheckDeletedTimeStamp: 0,
		LastCheckStoppedTimeStamp: 0,
		LastCheckActiveTimeStamp:  0,
	}
	RuleInspectionActor.Process(Ctx)
}

func (suite *RuleInspectionActorSuite) TestProcessReceiveTimeOut1() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.ReceiveTimeout{}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	ruleStateSet := []model.RuleState{model.RuleState_ACTIVE, model.RuleState_STOPPED, model.RuleState_DONE, model.RuleState_NONE}
	instList := []string{"111"}
	cclRules := &dao.SqlCclRulesInfo{
		Total: 1,
		SqlCCLRules: []*dao.SqlCCLRule{
			{
				ID:               1,
				RuleID:           2,
				UserID:           "",
				TenantID:         "",
				InstanceID:       "",
				InstanceType:     "",
				Duration:         700,
				Keywords:         "",
				SqlType:          0,
				State:            0,
				ConcurrencyCount: 0,
				CreatedAt:        1688389380000,
				StoppedAt:        0,
				UpdatedAt:        0,
				DeletedAt:        0,
				Deleted:          0,
			},
		},
	}
	cclRuleDal.EXPECT().ListInstancesByState(gomock.Any(), ruleStateSet).Return(instList)
	cclRuleDal.EXPECT().ListCCLRuleByState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cclRules, nil).AnyTimes()
	kc := mocks.NewMockKindClient(suite.ctrl)
	actorCli.EXPECT().KindOf(consts.CCLRuleActorKind).Return(kc).AnyTimes()
	resp := &shared.DescribeRealTimeCCLRulesResp{
		Success:    true,
		Consistent: false,
	}
	kc.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes()
	RuleInspectionActor := RuleInspectionActor{
		actorClient: actorCli,
		ccLRuleDal:  cclRuleDal,
	}
	RuleInspectionActor.state = &RuleInspectionState{
		LastCheckDoneTimeStamp:    0,
		LastCheckDeletedTimeStamp: 0,
		LastCheckStoppedTimeStamp: 0,
		LastCheckActiveTimeStamp:  0,
	}
	RuleInspectionActor.Process(Ctx)
}
