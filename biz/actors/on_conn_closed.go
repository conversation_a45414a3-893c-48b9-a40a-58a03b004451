package actors

import (
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

func (a *SessionActor) onConnClosed(ctx types.Context, connectionID string) {
	ctx.Respond(&shared.OK{})
	if !a.state.containsConn(connectionID) {
		return
	}
	a.markCommandSetCancel(ctx, a.state.ConnLock[connectionID])
	a.state.removeConn(connectionID)
	a.state.DeadConn = append(a.state.DeadConn, connectionID)
	log.Info(ctx, "connection %s closed", connectionID)
}

func (a *SessionActor) onConnInfoUpdated(ctx types.Context, info *shared.ConnectionInfo) {
	ctx.Respond(&shared.OK{})
	if a.state.updateConnInfo(info) {
		log.Info(ctx, "connection info updated %v", *info)
	}
}

func (a *SessionActor) markCommandSetCancel(ctx types.Context, cc *CommandCursor) {
	if cc == nil {
		return
	}
	cs, err := a.cmdRepo.GetCommandSet(ctx, cc.CommandSetID)
	if err != nil {
		log.Warn(ctx, "get command set fail %v", err)
		return
	}
	cs.CancelNonTerminated(time.Now().Unix()*1000, `connect closed`)
	a.cmdRepo.SaveCommandSet(ctx, cs)
}
