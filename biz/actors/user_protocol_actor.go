package actors

import (
	"code.byted.org/infcs/ds-lib/common/log"
	"encoding/json"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/qjpcpu/fp"
)

func NewUserProtocolActor() types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.UserProtocolKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &UserProtocolActor{
				state: newTenantProtocolState(state),
			}
		}),
	}
}

type UserProtocolState struct {
	TenantID  string `json:"u"`
	AgreeTime int64  `json:"t"`
}

func newTenantProtocolState(state []byte) (ret []UserProtocolState) {
	json.Unmarshal(state, &ret)
	return
}

type UserProtocolActor struct {
	state []UserProtocolState
}

func (a *UserProtocolActor) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *shared.AgreeUserProtocol:
		a.agreeUserProtocol(ctx, msg.TenantId)
	case *shared.GetUserProtocolState:
		log.Info(ctx, "received GetUserProtocolState message")
		ctx.Respond(a.getUserProtocolState(ctx, msg.TenantId))
		log.Info(ctx, "received GetUserProtocolState message")
	}
}

func (a *UserProtocolActor) GetState() []byte {
	return utils.MustJSONMarshal(a.state)
}

func (a *UserProtocolActor) agreeUserProtocol(ctx types.Context, tenantID string) {
	if tenantID == "" || tenantID == `0` || tenantID == `1` {
		return
	}
	if fp.StreamOf(a.state).ContainsBy(func(s UserProtocolState) bool { return s.TenantID == tenantID }) {
		return
	}
	a.state = append(a.state, UserProtocolState{TenantID: tenantID, AgreeTime: time.Now().Unix()})
}

func (a *UserProtocolActor) getUserProtocolState(ctx types.Context, tenantID string) *shared.UserProtocolState {
	if tenantID == `0` || tenantID == `1` {
		return &shared.UserProtocolState{TenantId: tenantID, Agreed: true, AgreedTimestamp: time.Now().Unix()}
	}
	for _, s := range a.state {
		if s.TenantID == tenantID {
			return &shared.UserProtocolState{TenantId: tenantID, Agreed: true, AgreedTimestamp: s.AgreeTime}
		}
	}
	return &shared.UserProtocolState{TenantId: tenantID, Agreed: false, AgreedTimestamp: 0}
}
