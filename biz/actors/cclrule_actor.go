package actors

import (
	"encoding/json"
	"errors"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
	"regexp"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	"code.byted.org/infcs/protoactor-go/actor"
)

const (
	Create                  = "create"
	Stop                    = "stop"
	Delete                  = "delete"
	Init                    = "init"
	Successed               = "successed"
	MaxCCLWaitTimeout int64 = 100
)

var SqlTypeName = map[int32]string{
	0: "SELECT",
	1: "UPDATE",
	2: "DELETE",
	3: "INSERT",
	4: "REPLACE",
}

// var PortsList = []int{3679, 3680, 3681, 3682, 3683, 3684, 3685, 3686, 3687, 3688, 3689, 3690, 3691, 3692, 3693, 3694, 3695}

type CCLRuleActorIn struct {
	dig.In
	ActorClient cli.ActorClient
	CCLRuleDal  dal.SqlCCLRulesDAL
	KillRuleDal dal.SqlKillRulesDAL
	DsSvc       datasource.DataSourceService
}

func NewCCLRuleActor(a CCLRuleActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.CCLRuleActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &CCLRuleActor{
				state:       newTaskState(state),
				ActorClient: a.ActorClient,
				CCLRuleDal:  a.CCLRuleDal,
				KillRuleDal: a.KillRuleDal,
				DsSvc:       a.DsSvc,
			}
			return actor
		}),
	}
}

type State struct {
	RuleState string  `json:"rule_state"`
	RuleType  string  `json:"rule_type"`
	Action    string  `json:"action"`
	TaskIds   []int64 `json:"task_ids"`
	TenantId  string  `json:"tenant_id"`
	ErrorInfo string  `json:"error_info"`
}

func newTaskState(bytes []byte) *State {
	state := &State{}
	json.Unmarshal(bytes, &state)
	return state
}

type TaskIds struct {
	ids      []int64
	tenantId string
}

type CCLRuleActor struct {
	state       *State
	ActorClient cli.ActorClient
	CCLRuleDal  dal.SqlCCLRulesDAL
	KillRuleDal dal.SqlKillRulesDAL
	DsSvc       datasource.DataSourceService
}

func (a *CCLRuleActor) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		switch a.state.RuleState {
		// if state is nil，do nothing
		case "":
			log.Info(ctx, "CCLRuleActor start msg: %s", msg)
		case Init:
			a.ActorInit(ctx)
		case Successed:
			a.CheckRuleStopTime(ctx)
		}
	case *shared.CreateSqlConcurrencyControlRuleReq:
		var err error
		a.state = &State{
			TaskIds:   []int64{msg.GetTaskId()},
			TenantId:  msg.GetTenantId(),
			RuleState: Init,
			Action:    Create,
			RuleType:  "CCL",
		}
		ctx.SaveState()
		log.Info(ctx, "Action CreateSqlConcurrencyControlRule:%+v", a.state)
		ruleInfo, err := a.CCLRuleDal.Get(ctx, msg.GetTaskId())
		if err != nil {
			ctx.Respond(&shared.CCLRuleActorFailResp{
				Error:         err.Error(),
				StandardError: consts.TranslateStandardErrorToShared(err),
			})
			a.state.ErrorInfo = err.Error()
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			log.Warn(ctx, "Get CCLRule from metadata failed:%+v", err)
			return
		}
		switch ruleInfo.ThrottleMode {
		case model.ThrottleMode_DBThrottle.String():
			err = a.CreateCclRule(ctx, ruleInfo)
		case model.ThrottleMode_ProxyThrottle.String():
			err = a.CreateProxyRule(ctx, ruleInfo)
		default:
			err = a.CreateCclRule(ctx, ruleInfo)
		}
		if err != nil {
			ctx.Respond(&shared.CCLRuleActorFailResp{
				Error:         err.Error(),
				StandardError: consts.TranslateStandardErrorToShared(err),
			})
			// 更新下任务状态
			if err = a.CCLRuleDal.UpdateStatusByID(ctx, ruleInfo.ID, int8(model.RuleState_ADD_FAILED)); err != nil {
				log.Warn(ctx, "Update SQLCCLRule state on metaDB failed%+v", err)
			}
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
		log.Info(ctx, "Set ReceiveTimeout: %d Minute", ruleInfo.Duration)
		ctx.SetReceiveTimeout(time.Duration(ruleInfo.Duration) * time.Minute)
		a.state.RuleState = Successed
		ctx.Respond(&shared.ActionSuccess{})
	case *shared.DescribeRealTimeCCLRulesReq:
		log.Info(ctx, "Action %s DescribeRealTimeCCLRules :%s", msg.ActionType, utils.Show(msg))
		a.state = &State{
			TaskIds:  msg.GetTaskIds(),
			Action:   msg.ActionType.String(),
			RuleType: "CCL",
		}
		ctx.SaveState()
		var errTemp error
		var ruleInfo *dao.SqlCCLRule
		if msg.ActionType == shared.CclDelete {
			ruleInfo, errTemp = a.CCLRuleDal.GetDeletedRule(ctx, msg.GetTaskId())
			if errTemp != nil {
				log.Warn(ctx, "Get CCLRule from metadata failed:%+v", errTemp)
				ctx.Respond(&shared.CCLRuleActorFailResp{
					Error:         errTemp.Error(),
					StandardError: consts.TranslateStandardErrorToShared(errTemp),
				})
				a.state.ErrorInfo = errTemp.Error()
				//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				return
			}
		} else {
			ruleInfo, errTemp = a.CCLRuleDal.Get(ctx, msg.GetTaskId())
			if errTemp != nil {
				ctx.Respond(&shared.CCLRuleActorFailResp{Error: errTemp.Error()})
				a.state.ErrorInfo = errTemp.Error()
				//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				log.Warn(ctx, "Get CCLRule from metadata failed:%+v", errTemp)
				return
			}
		}
		if ruleInfo.ThrottleMode == model.ThrottleMode_DBThrottle.String() {
			if err := a.FlushCCL(ctx, ruleInfo); err != nil {
				ctx.Respond(&shared.CCLRuleActorFailResp{
					Error:         err.Error(),
					StandardError: consts.TranslateStandardErrorToShared(err),
				})
				a.state.ErrorInfo = err.Error()
				return
			}
		}
		switch msg.ActionType {
		case shared.CclShow:
			if ruleInfo.ThrottleMode == model.ThrottleMode_ProxyThrottle.String() {
				//Todo 支持proxy限流查询限流规则

			} else {
				id, err := a.CCLShow(ctx, ruleInfo)
				if err != nil {
					log.Warn(ctx, "CCL show failed %+v", err)
					if err.Error() == "keywords not found" {
						addMsg := &shared.DescribeRealTimeCCLRulesReq{
							TaskId:     msg.TaskId,
							ActionType: shared.CclAdd,
						}
						ctx.Send(ctx.Self(), addMsg)
						return
					} else {
						ctx.Respond(&shared.CCLRuleActorFailResp{
							Error:         err.Error(),
							StandardError: consts.TranslateStandardErrorToShared(err),
						})
						//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
						return
					}
				}
				if ruleInfo.RuleID == id {
					ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
						Consistent: true,
					})
					//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
					return
				} else {
					addMsg := &shared.DescribeRealTimeCCLRulesReq{
						TaskId:     msg.TaskId,
						ActionType: shared.CclAdd,
					}
					ctx.Send(ctx.Self(), addMsg)
				}
			}
		case shared.CclAdd:
			switch ruleInfo.ThrottleMode {
			case model.ThrottleMode_DBThrottle.String():
				if err := a.AddCclRule(ctx, ruleInfo); err != nil {
					// 告警Warn改为Error
					log.Error(ctx, "task %s AddRule failed", a.state.TaskIds)
					ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
						Consistent: false,
						Success:    false,
					})
					a.state.ErrorInfo = err.Error()
					//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
					return
				}
				if err0 := a.FlushCCL(ctx, ruleInfo); err0 != nil {
					log.Warn(ctx, "flush ccl rules failed %+v", err0)
					ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
						Consistent: false,
						Success:    false,
					})
					a.state.ErrorInfo = err0.Error()
					//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
					return
				}
				id, err := a.CCLShow(ctx, ruleInfo)
				if err != nil {
					log.Warn(ctx, "Get CCLRuleId failed %+v", err)
				}
				if err = a.CCLRuleDal.UpdateCCLRuleByID(ctx, ruleInfo.ID, id); err != nil {
					log.Error(ctx, "Update SQLCCLRule on metaDB failed%+v", err)
					ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
						Consistent: false,
						Success:    false,
					})
					a.state.ErrorInfo = err.Error()
					//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
					return
				}
			case model.ThrottleMode_ProxyThrottle.String():
				if err := a.ModifyProxyRule(ctx, ruleInfo, model.CCLEventType_Add.String()); err != nil {
					ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
						Consistent: false,
						Success:    false,
					})
					return
				}
			}
			ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
				Success:    true,
				Consistent: true,
			})
		case shared.CclStop:
			tasks := TaskIds{ids: msg.TaskIds}
			if len(tasks.ids) == 0 {
				log.Warn(ctx, "Tasks List is null, break")
				ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
					Success: true,
				})
				//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				return
			}
			errCount := a.DeleteRuleByTaskIds(ctx, tasks, model.RuleState_STOPPED)
			if errCount > 0 {
				ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
					Success: false,
				})
			} else {
				ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
					Success: true,
				})
			}
			//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		case shared.CclDelete:
			tasks := TaskIds{ids: msg.TaskIds}
			if len(tasks.ids) == 0 {
				log.Warn(ctx, "Tasks List is null, break")
				ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
					Success: true,
				})
				//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				return
			}
			errCount := a.DeleteRuleByTaskIds(ctx, tasks, model.RuleState_DELETED)
			if errCount > 0 {
				ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
					Success: false,
				})
			} else {
				ctx.Respond(&shared.DescribeRealTimeCCLRulesResp{
					Success: true,
				})
			}
			//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		}
	case *shared.StopSqlConcurrencyControlRuleReq:
		a.state = &State{
			TaskIds:   msg.GetTaskIds(),
			TenantId:  msg.GetTenantId(),
			RuleState: Init,
			Action:    Stop,
			RuleType:  "CCL",
		}
		ctx.SaveState()
		log.Info(ctx, "Action StopSqlConcurrencyControlRuleIn:%s", utils.Show(a.state))
		// 持久化拒绝数
		for _, taskID := range msg.TaskIds {
			if err := a.updateRejectedCountById(ctx, taskID); err != nil {
				log.Warn(ctx, "update task %d reject count failed %+v", taskID, err)
			}
		}
		errCount := a.StopRule(ctx, msg)
		if errCount > 0 {
			a.state.RuleState = Successed
			ctx.Respond(&shared.CCLRuleActorFailResp{})
			return
		}

		a.state.RuleState = Successed
		ctx.Respond(&shared.ActionSuccess{})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	case *shared.DeleteSqlConcurrencyControlRuleReq:
		a.state = &State{
			TaskIds:   msg.GetTaskIds(),
			TenantId:  msg.GetTenantId(),
			RuleState: Init,
			Action:    Delete,
			RuleType:  "CCL",
		}
		ctx.SaveState()
		log.Info(ctx, "Action DeleteSqlConcurrencyControlRuleReq:%s", utils.Show(a.state))
		errCount := a.DeleteRules(ctx, msg)
		if errCount > 0 {
			a.state.RuleState = Successed
			//a.state.ErrorInfo = err.Error()
			ctx.Respond(&shared.CCLRuleActorFailResp{})
			//ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
		a.state.RuleState = Successed
		ctx.Respond(&shared.ActionSuccess{})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	case *shared.CreateSqlKillRuleReq:
		a.state = &State{
			TaskIds:   []int64{msg.GetTaskId()},
			TenantId:  msg.GetTenantId(),
			RuleState: Init,
			Action:    Create,
			RuleType:  "SQLKill",
		}
		ctx.SaveState()
		log.Info(ctx, "Action CreateSqlKillRule:%+v", a.state)
		ruleInfo, err := a.KillRuleDal.Get(ctx, msg.GetTaskId())
		if err != nil {
			ctx.Respond(&shared.SQLKillRuleActorFailResp{Error: err.Error()})
			a.state.ErrorInfo = err.Error()
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			log.Warn(ctx, "Get sql kill rule from metadata failed:%+v", err)
			return
		}
		if err = a.ModifySqlKillRule(ctx, a.state.TaskIds, ruleInfo.InstanceID, ruleInfo.InstanceType, model.KillSqlEventType_Add, ruleInfo.ProtectedUsers, 0); err != nil {
			ctx.Respond(&shared.SQLKillRuleActorFailResp{Error: err.Error()})
			a.state.ErrorInfo = err.Error()
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
		log.Info(ctx, "Set ReceiveTimeout: %d Seconds", ruleInfo.Duration)
		ctx.SetReceiveTimeout(time.Duration(ruleInfo.Duration) * time.Second)
		a.state.RuleState = Successed
		ctx.Respond(&shared.ActionSuccess{})
	case *shared.DeleteSqlKillRuleReq:
		a.state = &State{
			TaskIds:   msg.GetTaskIds(),
			TenantId:  msg.GetTenantId(),
			RuleState: Init,
			Action:    Delete,
			RuleType:  "SQLKill",
		}
		ctx.SaveState()
		log.Info(ctx, "Action DeleteSqlKillRule:%+v", a.state)
		if len(msg.GetTaskIds()) < 1 {
			ctx.Respond(&shared.SQLKillRuleActorFailResp{Error: "taskIds is Null"})
			a.state.ErrorInfo = "taskIds is Null"
			return
		}
		ruleInfo, err := a.KillRuleDal.Get(ctx, msg.GetTaskIds()[0])
		if err != nil {
			ctx.Respond(&shared.SQLKillRuleActorFailResp{Error: err.Error()})
			a.state.ErrorInfo = err.Error()
			log.Warn(ctx, "Get sql kill rule from metadata failed:%+v", err)
			return
		}
		if err = a.ModifySqlKillRule(ctx, a.state.TaskIds, ruleInfo.InstanceID, ruleInfo.InstanceType, model.KillSqlEventType_Delete, ruleInfo.ProtectedUsers, 0); err != nil {
			ctx.Respond(&shared.SQLKillRuleActorFailResp{Error: err.Error()})
			a.state.ErrorInfo = err.Error()
			return
		}
		a.state.RuleState = Successed
		ctx.Respond(&shared.ActionSuccess{})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	case *shared.StopSqlKillRuleReq:
		a.state = &State{
			TaskIds:   msg.GetTaskIds(),
			TenantId:  msg.GetTenantId(),
			RuleState: Init,
			Action:    Stop,
			RuleType:  "SQLKill",
		}
		ctx.SaveState()
		log.Info(ctx, "Action StopSqlKillRule:%+v", a.state)
		if len(msg.GetTaskIds()) < 1 {
			ctx.Respond(&shared.SQLKillRuleActorFailResp{Error: "taskIds is Null"})
			a.state.ErrorInfo = "taskIds is Null"
			return
		}
		ruleInfo, err := a.KillRuleDal.Get(ctx, msg.GetTaskIds()[0])
		if err != nil {
			ctx.Respond(&shared.SQLKillRuleActorFailResp{Error: err.Error()})
			a.state.ErrorInfo = err.Error()
			log.Warn(ctx, "Get sql kill rule from metadata failed:%+v", err)
			return
		}
		if err = a.ModifySqlKillRule(ctx, a.state.TaskIds, ruleInfo.InstanceID, ruleInfo.InstanceType, model.KillSqlEventType_Stop, ruleInfo.ProtectedUsers, msg.State); err != nil {
			ctx.Respond(&shared.SQLKillRuleActorFailResp{Error: err.Error()})
			a.state.ErrorInfo = err.Error()
			return
		}
		a.state.RuleState = Successed
		ctx.Respond(&shared.ActionSuccess{})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	case *actor.ReceiveTimeout:
		a.CheckRuleStopTime(ctx)
	}
}

// CreateCclRule 添加内核限流规则
func (a *CCLRuleActor) CreateCclRule(ctx types.Context, ruleInfo *dao.SqlCCLRule) error {
	if err := a.AddCclRule(ctx, ruleInfo); err != nil {
		// 告警Warn改为Error
		log.Error(ctx, "task %v AddRule failed %v", a.state.TaskIds, err)
		a.state.ErrorInfo = err.Error()
		// vedb限流关键字长度报错特判
		if strings.Contains(strings.ToLower(err.Error()), "keywords string is too long") ||
			strings.Contains(strings.ToLower(err.Error()), "too many keywords") {
			return consts.ErrorWithParam(model.ErrorCode_InputParamError, err.Error())
		}
		return err
	}

	if err := a.FlushCCL(ctx, ruleInfo); err != nil {
		log.Error(ctx, "taskId %s FlushRule failed %v", a.state.TaskIds, err)
		a.state.ErrorInfo = err.Error()
		return err
	}
	if err := a.RuleUpdate(ctx, ruleInfo, int8(model.RuleState_ACTIVE)); err != nil {
		a.state.ErrorInfo = err.Error()
		return err
	}
	return nil
}

// CreateProxyRule 添加proxy限流规则
func (a *CCLRuleActor) CreateProxyRule(ctx types.Context, ruleInfo *dao.SqlCCLRule) error {
	if err := a.ModifyProxyRule(ctx, ruleInfo, model.CCLEventType_Add.String()); err != nil {
		// 告警Warn改为Error
		log.Error(ctx, "task %s crate proxy throttle rule failed", a.state.TaskIds)
		a.state.ErrorInfo = err.Error()
		return err
	}
	if err := a.CCLRuleDal.UpdateStatusByID(ctx, ruleInfo.ID, int8(model.RuleState_ACTIVE)); err != nil {
		log.Warn(ctx, "Update SQLCCLRule on metaDB failed%+v", err)
		a.state.ErrorInfo = err.Error()
		return err
	}
	return nil
}

func (a *CCLRuleActor) ActorInit(ctx types.Context) {
	switch a.state.Action {
	case Create:
		// 说明之前正在进行规则添加，调用CreateRule继续执行
		if a.state.RuleType == "SQLKill" {
			log.Info(ctx, "Continue CreateSqlKillRuleReq:%+v", a.state.Action)
			ctx.Send(ctx.Self(), &shared.CreateSqlKillRuleReq{
				TaskId:   a.state.TaskIds[0],
				TenantId: a.state.TenantId,
			})
		} else {
			log.Info(ctx, "Continue CreateSqlConcurrencyControlRule:%+v", a.state.Action)
			ctx.Send(ctx.Self(), &shared.CreateSqlConcurrencyControlRuleReq{
				TaskId:   a.state.TaskIds[0],
				TenantId: a.state.TenantId,
			})
		}
	case Stop:
		// 说明之前stop任务状态可能中断了，调用StopRule继续执行
		if a.state.RuleType == "SQLKill" {
			log.Info(ctx, "Continue StopSqlKillRuleReq:%+v", a.state.Action)
			ctx.Send(ctx.Self(), &shared.StopSqlKillRuleReq{
				TaskIds:  a.state.TaskIds,
				TenantId: a.state.TenantId,
			})
		} else {
			log.Info(ctx, "Continue StopSqlConcurrencyControlRuleReq:%+v", a.state.Action)
			ctx.Send(ctx.Self(), &shared.StopSqlConcurrencyControlRuleReq{
				TaskIds:  a.state.TaskIds,
				TenantId: a.state.TenantId,
			})
		}

	case Delete:
		// 说明之前delete任务状态可能中断了，调用DeleteRule继续执行
		if a.state.RuleType == "SQLKill" {
			log.Info(ctx, "Continue DeleteSqlKillRuleReq:%+v", a.state.Action)
			ctx.Send(ctx.Self(), &shared.DeleteSqlKillRuleReq{
				TaskIds:  a.state.TaskIds,
				TenantId: a.state.TenantId,
			})
		} else {
			log.Info(ctx, "Continue DeleteSqlConcurrencyControlRuleReq:%+v", a.state.Action)
			ctx.Send(ctx.Self(), &shared.DeleteSqlConcurrencyControlRuleReq{
				TaskIds:  a.state.TaskIds,
				TenantId: a.state.TenantId,
			})
		}

	}
}

func (a *CCLRuleActor) CheckRuleStopTime(ctx types.Context) {
	log.Info(ctx, " task %s Continue calculate ReceiveTimeout: %+v", ctx.GetName(), a.state.Action)
	// 说明之前的规则添加成功了，去通过duration+create_time跟now对比
	now := time.Now().UnixNano() / 1e6
	if a.state.RuleType == "" || a.state.RuleType == "CCL" {
		ruleInfo, err := a.CCLRuleDal.Get(ctx, a.state.TaskIds[0])
		if err != nil {
			// 若规则不存在，则停掉actor
			if strings.Contains(err.Error(), "record not found") {
				log.Warn(ctx, "task %d is not exist, Stop CCLRuleActor", a.state.TaskIds[0])
				ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				return
			} else {
				// 查库失败，10秒后再查
				log.Warn(ctx, "Get CCLRule from metadata failed:%+v, Set ReceiveTimeout 10 Second", err)
				ctx.SetReceiveTimeout(time.Duration(10) * time.Second)
				return
			}
		}
		stopAt := ruleInfo.CreatedAt + ruleInfo.Duration*60000
		if stopAt <= now {
			// 如果已经过期直接StopRule
			log.Info(ctx, "time to StopSqlConcurrencyControlRule:%+v", a.state)
			ctx.Send(ctx.Self(), &shared.StopSqlConcurrencyControlRuleReq{
				TaskIds:  a.state.TaskIds,
				TenantId: a.state.TenantId,
				State:    int64(model.RuleState_DONE),
			})
		} else {
			// 计算ReceiveTimeout过期时间
			log.Info(ctx, "Set ReceiveTimeout: %d Millisecond", stopAt-now)
			ctx.SetReceiveTimeout(time.Duration(stopAt-now) * time.Millisecond)
		}
	} else if a.state.RuleType == "SQLKill" {
		ruleInfo, err := a.KillRuleDal.Get(ctx, a.state.TaskIds[0])
		if err != nil {
			// 若规则不存在，则停掉actor
			if strings.Contains(err.Error(), "record not found") {
				log.Warn(ctx, "task %d is not exist, Stop KillRuleActor", a.state.TaskIds[0])
				ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				return
			} else {
				// 查库失败，10秒后再查
				log.Warn(ctx, "Get CCLRule from metadata failed:%+v, Set ReceiveTimeout 10 Second", err)
				ctx.SetReceiveTimeout(time.Duration(10) * time.Second)
				return
			}
		}
		stopAt := ruleInfo.CreatedAt + ruleInfo.Duration*1000
		if stopAt <= now {
			// 如果已经过期直接StopRule
			log.Info(ctx, "time to StopSqlKillRule:%+v", a.state)
			ctx.Send(ctx.Self(), &shared.StopSqlKillRuleReq{
				TaskIds:  a.state.TaskIds,
				TenantId: a.state.TenantId,
				State:    int64(model.KillRuleState_DONE),
			})
		} else {
			// 计算ReceiveTimeout过期时间
			log.Info(ctx, "Set ReceiveTimeout: %d Millisecond", stopAt-now)
			ctx.SetReceiveTimeout(time.Duration(stopAt-now) * time.Millisecond)
		}
	} else {
	}
}

func (a *CCLRuleActor) AddCclRule(ctx types.Context, msg *dao.SqlCCLRule) error {
	Type := SqlTypeName[int32(msg.SqlType)]
	KeyWords := strings.ReplaceAll(msg.Keywords, "\\", "\\\\\\\\")
	KeyWords = strings.ReplaceAll(KeyWords, "\"", "\\\"")
	KeyWords = strings.ReplaceAll(KeyWords, "~", ",")
	ConcurrencyCountMsg := msg.ConcurrencyCount
	instanceType, err := model.DSTypeFromString(msg.InstanceType)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	// FIXME 待vedb内核支持后，删除
	if msg.ThrottleTarget == model.ThrottleTarget_SqlCCL.String() &&
		msg.InstanceType == model.DSType_VeDBMySQL.String() && msg.ThrottleSqlText != "" {
		KeyWords = generateSqlKeywordsForVeDB(msg.ThrottleSqlText)

	}
	if msg.ThrottleTarget == model.ThrottleTarget_FingerCCL.String() &&
		msg.InstanceType == model.DSType_VeDBMySQL.String() && msg.ThrottleFingerPrint != "" {
		// sql样本转为sql指纹，sql指纹再转为keywords
		sqlTemplate, _ := datasource.GetSqlTemplate(ctx, msg.ThrottleFingerPrint)
		KeyWords = generateSqlKeywordsForVeDB(sqlTemplate)
	}
	// 兜底避免keywords设置为空
	if KeyWords == "" {
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "keywords is null")
	}
	ruleInfo := &datasource.CCLRuleInfo{
		SqlType:          Type,
		UserID:           "",
		HostName:         "",
		SchemaName:       "",
		TableName:        "",
		PartitionName:    "",
		ConcurrencyCount: ConcurrencyCountMsg,
		Keywords:         KeyWords,
		State:            "Y",
		Ordered:          "Y",
		MaxQueueSize:     int64(ConcurrencyCountMsg),
		WaitTimeout:      MaxCCLWaitTimeout,
	}
	addCCLRuleReq := &datasource.AddSQLCCLRuleReq{
		InstanceId: msg.InstanceID,
		Type:       conv.ToSharedType(instanceType),
		CCLRule:    ruleInfo,
	}
	//ccl add('<Type>','<User>','<Host>','<Schema_name>','<Table_name>',<Concurrency_count>,'<Keywords>'，'<State>','<Ordered>','<Max_queue_size>','<Wait_timeout>');
	if _, err := a.DsSvc.AddSQLCCLRule(ctx, addCCLRuleReq); err != nil {
		log.Warn(ctx, "Add CCL rule failed %+v", err)
		return err
	}
	return nil
}
func (a *CCLRuleActor) ModifyProxyRule(ctx types.Context, msg *dao.SqlCCLRule, action string) error {
	instanceType, err := model.DSTypeFromString(msg.InstanceType)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	ruleInfo := &datasource.ProxyThrottleRuleInfo{
		ThrottleHost:        msg.ThrottleHost,
		ThrottleDB:          msg.ThrottleDB,
		ThrottleTarget:      msg.ThrottleTarget,
		ThrottleFingerPrint: msg.ThrottleFingerPrint,
		ThrottleSqlText:     msg.ThrottleSqlText,
		EndpointType:        msg.EndpointType,
		EndpointID:          msg.EndpointID,
		Keywords:            msg.Keywords,
		ThrottleThreshold:   msg.ThrottleThreshold,
		GroupIds:            msg.ThrottleGroupIds,
		RuleID:              msg.RuleID,
		Duration:            msg.Duration,
		ThrottleObjId:       msg.ThrottleObjId, // 仅用于内场rds限流规则
	}
	addCCLRuleReq := &datasource.ModifyProxyThrottleRuleReq{
		InstanceId:        msg.InstanceID,
		Type:              conv.ToSharedType(instanceType),
		ProxyThrottleRule: ruleInfo,
		Action:            action,
		RegionId:          msg.RegionId,
	}
	if _, err := a.DsSvc.ModifyProxyThrottleRule(ctx, addCCLRuleReq); err != nil {
		log.Warn(ctx, "%s proxy throttle rule failed%+v", action, err)
		return err
	}
	return nil
}
func (a *CCLRuleActor) RuleUpdate(ctx types.Context, msg *dao.SqlCCLRule, state int8) error {
	keyWords := strings.Replace(msg.Keywords, "~", ",", -1)
	// FIXME 待vedb内核支持后，删除
	if msg.ThrottleTarget == model.ThrottleTarget_SqlCCL.String() &&
		msg.InstanceType == model.DSType_VeDBMySQL.String() && msg.ThrottleSqlText != "" {
		keyWords = generateSqlKeywordsForVeDB(msg.ThrottleSqlText)

	}
	if msg.ThrottleTarget == model.ThrottleTarget_FingerCCL.String() &&
		msg.InstanceType == model.DSType_VeDBMySQL.String() && msg.ThrottleFingerPrint != "" {
		sqlTemplate, _ := datasource.GetSqlTemplate(ctx, msg.ThrottleFingerPrint)
		keyWords = generateSqlKeywordsForVeDB(sqlTemplate)
	}
	maxRetryCount := 0
	var (
		ruleId  int64
		ID      int64
		isExist bool
	)
	instanceType, err := model.DSTypeFromString(msg.InstanceType)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
LOOP:
	maxRetryCount += 1
	if maxRetryCount > 3 {
		log.Warn(ctx, "Get CCLRuleid over 3 times, break")
		return consts.ErrorWithParam(model.ErrorCode_InternalError, "Get CCLRuleid over 3 times")
	}
	time.Sleep(1 * time.Second)
	cclShowReq := &datasource.ListSQLCCLRulesReq{
		InstanceId: msg.InstanceID,
		Type:       conv.ToSharedType(instanceType),
	}
	RuleInfos, err := a.DsSvc.ListSQLCCLRules(ctx, cclShowReq)
	if err != nil {
		log.Warn(ctx, "Get CCLRules failed, RetryCount %s, %+v", maxRetryCount, err)
		goto LOOP
	}
	// 增加trimSpace
	for _, item := range RuleInfos.CCLRules {
		// 忽略大小写比较
		if strings.EqualFold(item.Keywords, trimKeyword(keyWords)) {
			isExist = true
			ID = msg.ID
			ruleId = item.RuleID
			break
		}
	}
	if !isExist {
		log.Warn(ctx, "ccl rule not found from rds")
		return consts.ErrorOf(model.ErrorCode_RecordNotFound)
	}
	if err = a.CCLRuleDal.UpdateStatusByID(ctx, ID, state); err != nil {
		log.Warn(ctx, "Update SQLCCLRule on metaDB failed%+v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	if err = a.CCLRuleDal.UpdateCCLRuleByID(ctx, ID, ruleId); err != nil {
		log.Warn(ctx, "Update SQLCCLRule on metaDB failed%+v", err)
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return nil
}

func (a *CCLRuleActor) StopRule(ctx types.Context, req *shared.StopSqlConcurrencyControlRuleReq) int64 {
	tasks := TaskIds{
		ids: req.GetTaskIds(),
	}
	var errCount int64
	if req.State != 0 {
		errCount = a.DeleteRuleByTaskIds(ctx, tasks, model.RuleState(req.State))
	} else {
		errCount = a.DeleteRuleByTaskIds(ctx, tasks, model.RuleState_STOPPED)
	}
	return errCount
}

func (a *CCLRuleActor) DeleteRules(ctx types.Context, req *shared.DeleteSqlConcurrencyControlRuleReq) int64 {
	tasks := TaskIds{
		ids: req.GetTaskIds(),
	}
	return a.DeleteRuleByTaskIds(ctx, tasks, model.RuleState_DELETED)
}

func (a *CCLRuleActor) DeleteRuleByTaskIds(ctx types.Context, tasks TaskIds, state model.RuleState) int64 {
	//检查taskIds是否为空
	var errCount int64
	if len(tasks.ids) == 0 {
		log.Warn(ctx, "Tasks List is null, break")
		return 0
	}
	for _, i := range tasks.ids {
		if err := a.DeleteRuleByTaskId(ctx, i, state); err != nil {
			log.Warn(ctx, "delete rule %d failed %s", i, err)
			errCount += 1
		}
	}
	return errCount
}

func (a *CCLRuleActor) updateTaskStatus(ctx types.Context, taskId int64, state model.RuleState) error {
	// 更新数据库状态
	switch state {
	case model.RuleState_DELETED:
		if err := a.CCLRuleDal.Delete(ctx, taskId); err != nil {
			log.Warn(ctx, "Delete SQLCCLRule on metaDB failed %+v", err)
			return err
		}
	case model.RuleState_STOPPED:
		if err := a.CCLRuleDal.UpdateStatusTimeByID(ctx, taskId, int8(state)); err != nil {
			log.Warn(ctx, "Stop %s SQLCCLRule on metaDB failed %+v", taskId, err)
			return err
		}
	case model.RuleState_DONE:
		if err := a.CCLRuleDal.UpdateStatusTimeByID(ctx, taskId, int8(state)); err != nil {
			log.Warn(ctx, "Auto stop %s SQLCCLRule on metaDB failed %+v", taskId, err)
			return err
		}
	default:
		if err := a.CCLRuleDal.UpdateStatusByID(ctx, taskId, int8(state)); err != nil {
			log.Warn(ctx, "taskId %s UpdateStatus failed %+v", taskId, err)
			return err
		}
	}
	return nil
}
func (a *CCLRuleActor) DeleteRuleByTaskId(ctx types.Context, taskId int64, state model.RuleState) error {
	var err error
	ruleInfo, err := a.CCLRuleDal.Get(ctx, taskId)
	if err != nil {
		log.Warn(ctx, "Get CCLRule from metadata failed:%+v", err)
		return err
	}
	switch ruleInfo.ThrottleMode {
	case model.ThrottleMode_DBThrottle.String():
		err = a.deleteCCLRule(ctx, ruleInfo)
	case model.ThrottleMode_ProxyThrottle.String():
		err = a.deleteProxyRule(ctx, ruleInfo)
	default:
		err = consts.ErrorOf(model.ErrorCode_ParamError)
	}
	if err != nil {
		switch state {
		case model.RuleState_DELETED:
			a.updateTaskStatus(ctx, taskId, model.RuleState_DELETE_FAILED)
		case model.RuleState_STOPPED:
			a.updateTaskStatus(ctx, taskId, model.RuleState_STOP_FAILED)
		}
		return err
	}
	err = a.updateTaskStatus(ctx, taskId, state)
	return err
}

func (a *CCLRuleActor) deleteProxyRule(ctx types.Context, ruleInfo *dao.SqlCCLRule) error {
	if err := a.ModifyProxyRule(ctx, ruleInfo, model.CCLEventType_Delete.String()); err != nil {
		return err
	}
	return nil
}

func (a *CCLRuleActor) deleteCCLRule(ctx types.Context, ruleInfo *dao.SqlCCLRule) error {
	Type := SqlTypeName[int32(ruleInfo.SqlType)]
	keyWords := strings.ReplaceAll(ruleInfo.Keywords, "~", ",")
	instanceType, err := model.DSTypeFromString(ruleInfo.InstanceType)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	// FIXME 待vedb内核支持后，删除
	if ruleInfo.ThrottleTarget == model.ThrottleTarget_SqlCCL.String() &&
		ruleInfo.InstanceType == model.DSType_VeDBMySQL.String() && ruleInfo.ThrottleSqlText != "" {
		keyWords = generateSqlKeywordsForVeDB(ruleInfo.ThrottleSqlText)
	}
	if ruleInfo.ThrottleTarget == model.ThrottleTarget_FingerCCL.String() &&
		ruleInfo.InstanceType == model.DSType_VeDBMySQL.String() && ruleInfo.ThrottleFingerPrint != "" {
		sqlTemplate, _ := datasource.GetSqlTemplate(ctx, ruleInfo.ThrottleFingerPrint)
		keyWords = generateSqlKeywordsForVeDB(sqlTemplate)
	}
	ruleItem := &datasource.CCLRuleInfo{
		SqlType:          Type,
		UserID:           "",
		HostName:         "",
		SchemaName:       "",
		TableName:        "",
		PartitionName:    "",
		ConcurrencyCount: ruleInfo.ConcurrencyCount,
		Keywords:         trimKeyword(keyWords),
		State:            "Y",
		Ordered:          "Y",
		MaxQueueSize:     int64(ruleInfo.ConcurrencyCount),
		WaitTimeout:      MaxCCLWaitTimeout,
		RuleID:           ruleInfo.RuleID, // 若能获取id，则基于id去删除限流规则
	}
	deleteCCLRuleReq := &datasource.DeleteSQLCCLRuleReq{
		InstanceId: ruleInfo.InstanceID,
		Type:       conv.ToSharedType(instanceType),
		CCLRule:    ruleItem,
	}
	if _, err = a.DsSvc.DeleteSQLCCLRule(ctx, deleteCCLRuleReq); err != nil {
		return err
	}
	// ccl flush
	if err = a.FlushCCL(ctx, ruleInfo); err != nil {
		return err
	}
	return nil
}

func (a *CCLRuleActor) deleteFailedCCL(ctx types.Context, tasks TaskIds) error {
	for _, i := range tasks.ids {
		ruleInfo, err := a.CCLRuleDal.GetDeletedRule(ctx, i)
		if err != nil {
			log.Warn(ctx, "Get CCLRule from metadata failed:%+v", err)
			return err
		}
		Type := SqlTypeName[int32(ruleInfo.SqlType)]
		//KeyWords := strings.ReplaceAll(ruleInfo.Keywords, "\\", "\\\\\\\\")
		//KeyWords = strings.ReplaceAll(KeyWords, "\"", "\\\"")
		KeyWords := strings.ReplaceAll(ruleInfo.Keywords, "~", ",")
		instanceType, err := model.DSTypeFromString(ruleInfo.InstanceType)
		if err != nil {
			return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
		}
		ruleItem := &datasource.CCLRuleInfo{
			SqlType:          Type,
			UserID:           "",
			HostName:         "",
			SchemaName:       "",
			TableName:        "",
			ConcurrencyCount: ruleInfo.ConcurrencyCount,
			Keywords:         KeyWords,
			State:            "Y",
			Ordered:          "Y",
			MaxQueueSize:     int64(ruleInfo.ConcurrencyCount),
			WaitTimeout:      MaxCCLWaitTimeout,
		}
		deleteCCLRuleReq := &datasource.DeleteSQLCCLRuleReq{
			InstanceId: ruleInfo.InstanceID,
			Type:       conv.ToSharedType(instanceType),
			CCLRule:    ruleItem,
		}
		if _, err = a.DsSvc.DeleteSQLCCLRule(ctx, deleteCCLRuleReq); err != nil {
			return err
		}
	}
	return nil
}

func (a *CCLRuleActor) FlushCCL(ctx types.Context, msg *dao.SqlCCLRule) error {
	instanceType, err := model.DSTypeFromString(msg.InstanceType)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	flushReq := &datasource.FlushSQLCCLRuleReq{
		InstanceId: msg.InstanceID,
		Type:       conv.ToSharedType(instanceType),
	}
	if _, err = a.DsSvc.FlushSQLCCLRule(ctx, flushReq); err != nil {
		log.Warn(ctx, "flush ccl rules failed %+v", err)
		return err
	}
	return nil
}

func (a *CCLRuleActor) CCLShow(ctx types.Context, msg *dao.SqlCCLRule) (int64, error) {
	Type := SqlTypeName[int32(msg.SqlType)]
	KeyWords := strings.ReplaceAll(msg.Keywords, "\\", "\\\\\\\\")
	KeyWords = strings.ReplaceAll(KeyWords, "\"", "\\\"")
	KeyWords = strings.ReplaceAll(KeyWords, "~", ",")
	DSType, err := model.DSTypeFromString(msg.InstanceType)
	if err != nil {
		return -1, consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	RuleList, err := a.DsSvc.ListSQLCCLRules(ctx, &datasource.ListSQLCCLRulesReq{
		InstanceId: msg.InstanceID,
		Type:       conv.ToSharedType(DSType),
	})
	if err != nil {
		log.Warn(ctx, "Get CCLRules failed:%+v", err)
		return -1, err
	}
	// FIXME 待vedb内核支持后，删除
	if msg.ThrottleTarget == model.ThrottleTarget_SqlCCL.String() &&
		msg.InstanceType == model.DSType_VeDBMySQL.String() && msg.ThrottleSqlText != "" {
		KeyWords = generateSqlKeywordsForVeDB(msg.ThrottleSqlText)

	}
	if msg.ThrottleTarget == model.ThrottleTarget_FingerCCL.String() &&
		msg.InstanceType == model.DSType_VeDBMySQL.String() && msg.ThrottleFingerPrint != "" {
		sqlTemplate, _ := datasource.GetSqlTemplate(ctx, msg.ThrottleFingerPrint)
		KeyWords = generateSqlKeywordsForVeDB(sqlTemplate)
	}
	//for _, item := range RuleList.CCLRules {
	//	if item.Keywords == KeyWords && item.SqlType == Type {
	//		return item.RuleID, nil
	//	}
	//}

	for _, item := range RuleList.CCLRules {
		if strings.EqualFold(item.Keywords, trimKeyword(KeyWords)) && item.SqlType == Type {
			return item.RuleID, nil
		}
	}
	return -1, errors.New("keywords not found")
}

func (a *CCLRuleActor) GetState() []byte {
	state, _ := json.Marshal(a.state)
	return state
}

func (a *CCLRuleActor) getRejectedCountById(ctx types.Context, taskId int64, instanceId string, instanceType model.DSType) (int64, error) {
	var rejectedCount int64
	ruleInfo, err := a.CCLRuleDal.Get(ctx, taskId)
	if err != nil {
		log.Warn(ctx, "Get CCL RuleInfo  failed:%+v", err)
		return 0, consts.ErrorWithParam(model.ErrorCode_InternalError, "Get CCL RuleInfo failed", instanceId)
	}
	switch ruleInfo.ThrottleMode {
	case model.ThrottleMode_DBThrottle.String():
		// 运行中时调rds接口获取实时拒绝数
		cclShowReq := &datasource.ListSQLCCLRulesReq{
			InstanceId: instanceId,
			Type:       conv.ToSharedType(instanceType),
		}
		cclRules, err := a.DsSvc.ListSQLCCLRules(ctx, cclShowReq)
		if err != nil {
			log.Warn(ctx, "Get instance %s CCLRules failed  %+v", instanceId, err)
			return 0, consts.ErrorWithParam(model.ErrorCode_InternalError, "Get instance CCLRules failed", instanceId)
		}
		for _, item := range cclRules.CCLRules {
			if item.RuleID == ruleInfo.RuleID {
				rejectedCount = item.Rejected
				break
			}
		}
	case model.ThrottleMode_ProxyThrottle.String():
		// TODO proxy拒绝数一期不支持
	}

	return rejectedCount, nil
}
func (a *CCLRuleActor) updateRejectedCountById(ctx types.Context, taskId int64) error {
	ruleInfo, err := a.CCLRuleDal.Get(ctx, taskId)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	instanceType, err := model.DSTypeFromString(ruleInfo.InstanceType)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	// 查询拒绝数
	rejected, err := a.getRejectedCountById(ctx, taskId, ruleInfo.InstanceID, instanceType)
	if err != nil {
		return err
	}
	// 更新到元数据库
	if err := a.CCLRuleDal.UpdateRejectedCountByID(ctx, ruleInfo.ID, rejected); err != nil {
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return nil
}

// 调整SQL kill规则(增加/删除/停止)
func (a *CCLRuleActor) ModifySqlKillRule(ctx types.Context, taskIds []int64, instanceId string, instanceType string, action model.KillSqlEventType, protectedUser string, state int64) error {
	var (
		sqlKillRules []*datasource.KillRuleInfo
		sqlRuleList  []*dao.SqlKillRule
	)
	dsType, err := model.DSTypeFromString(instanceType)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}
	for _, taskId := range taskIds {
		ruleInfo, err := a.KillRuleDal.Get(ctx, taskId)
		if err != nil {
			continue
		}
		sqlRuleList = append(sqlRuleList, ruleInfo)
	}

	// 获取EndpointID--only for sharding
	endpointList, err := a.DsSvc.DescribeDBInstanceEndpoints(ctx, &datasource.DescribeDBInstanceEndpointsReq{
		Type:       conv.ToSharedType(dsType),
		InstanceId: instanceId,
	})
	if err != nil {
		log.Warn(ctx, "Get instance %s endpoint failed %s", instanceId, utils.Show(err))
		return err
	}
	if len(endpointList.Endpoints) < 1 {
		log.Warn(ctx, "get instance %s endpoints null", instanceId)
		return consts.ErrorOf(model.ErrorCode_CheckInstanceError)
	}
	if err := fp.StreamOf(sqlRuleList).Map(func(ruleInfo *dao.SqlKillRule) *datasource.KillRuleInfo {
		//将数据库psm转化对应的节点类型
		nodeType := make([]string, 0)
		for _, item := range strings.Split(ruleInfo.NodeType, ",") {
			if strings.HasSuffix(item, "write") || strings.EqualFold(item, "Primary") {
				// sharding需要传EndpointID
				if ruleInfo.InstanceType == model.InstanceType_MySQLSharding.String() {
					for _, ed := range endpointList.Endpoints {
						if ed.ReadWriteMode == model.EndpointType_ReadWrite.String() && ed.EndpointType != "Cluster" {
							nodeType = append(nodeType, ed.EndpointID)
							break
						}
					}
				} else {
					nodeType = append(nodeType, "Primary")
				}
			}
			if strings.HasSuffix(item, "read") || strings.EqualFold(item, "Secondary") {
				if ruleInfo.InstanceType == model.InstanceType_MySQLSharding.String() {
					// sharding需要传EndpointID
					for _, ed := range endpointList.Endpoints {
						if ed.ReadWriteMode == model.EndpointType_ReadOnly.String() && ed.EndpointType != "Cluster" {
							nodeType = append(nodeType, ed.EndpointID)
							break
						}
					}
				} else {
					nodeType = append(nodeType, "Secondary")
				}
			}
			// 适配火山MySQL
			if strings.EqualFold(item, "ReadOnly") {
				nodeType = append(nodeType, "Readonly")
			}
		}
		return &datasource.KillRuleInfo{
			Keyword:     ruleInfo.Keywords,
			MaxExecTime: ruleInfo.MaxExecTime,
			SqlType:     ruleInfo.SqlType,
			NodeType:    strings.Join(nodeType, ","),
			SqlText:     ruleInfo.SqlText,
			FingerPrint: ruleInfo.FingerPrint,
			Host:        ruleInfo.Host,
			DB:          ruleInfo.DB,
			RuleID:      ruleInfo.ID,
		}
	}).ToSlice(&sqlKillRules); err != nil {
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	modifySqlKillRuleReq := &datasource.ModifySQLKillRuleReq{
		InstanceId:     instanceId,
		Type:           conv.ToSharedType(dsType),
		KillRule:       sqlKillRules,
		Action:         action.String(),
		ProtectedUsers: protectedUser,
	}
	if _, err := a.DsSvc.ModifySQLKillRule(ctx, modifySqlKillRuleReq); err != nil {
		log.Warn(ctx, "%s sql kill rule failed%+v", action.String(), err)
		return err
	}
	// 规则更新成功后更新元数据库
	return a.UpdateSqlKillRuleStateOnMeta(ctx, action.String(), sqlRuleList, state)
}

func (a *CCLRuleActor) UpdateSqlKillRuleStateOnMeta(ctx types.Context, action string, rules []*dao.SqlKillRule, state int64) error {
	var (
		errCount int32
	)
	switch action {
	case model.KillSqlEventType_Delete.String():
		for _, rule := range rules {
			if err := a.KillRuleDal.Delete(ctx, rule.ID); err != nil {
				log.Warn(ctx, "Set %d rule state as deleted failed %s", rule.ID, err.Error())
				errCount += 1
				continue
			}
		}
	case model.KillSqlEventType_Stop.String():
		var stateStr string
		if state != 0 {
			stateStr = model.KillRuleState(state).String()
		} else {
			stateStr = model.KillRuleState_STOPPED.String()
		}
		for _, rule := range rules {
			if err := a.KillRuleDal.UpdateStatusByID(ctx, rule.ID, stateStr); err != nil {
				log.Warn(ctx, "Set %d rule state as stopped failed %s", rule.ID, err.Error())
				errCount += 1
				continue
			}
		}
	case model.KillSqlEventType_Add.String():
		for _, rule := range rules {
			if err := a.KillRuleDal.UpdateStatusByID(ctx, rule.ID, model.KillRuleState_ACTIVE.String()); err != nil {
				log.Warn(ctx, "Set %d rule state as stopped failed %s", rule.ID, err.Error())
				errCount += 1
				continue
			}
		}
	default:
		return nil
	}
	if errCount > 0 {
		return consts.ErrorOf(model.ErrorCode_MetaDataBaseError)
	}
	return nil
}

// 参照内核处理逻辑
func trimKeyword(keywords string) string {
	var keySlice []string
	temp := strings.Split(keywords, ",")
	for _, item := range temp {
		str := strings.TrimSpace(item)
		if str != "" {
			keySlice = append(keySlice, str)
		}
	}
	return strings.Join(keySlice, ",")
}

// 关键字来替代sql精确限流和模版限流
func generateSqlKeywordsForVeDB(sql string) string {
	// general matcher
	var ret string
	reg0 := regexp.MustCompile(`\W+`)
	// Replace all non-valid characters with "~" and remove leading/trailing "~"
	outKeyword := reg0.ReplaceAllString(sql, "~")
	outKeyword = regexp.MustCompile("^~+|~+$").ReplaceAllString(outKeyword, "")
	// special match_1
	reg1 := regexp.MustCompile(`(?i)^(SELECT~)\d+$`)
	if reg1.MatchString(outKeyword) {
		parts := strings.Split(outKeyword, "~")
		ret = strings.ReplaceAll(parts[0], "~", ",")
	}
	ret = strings.ReplaceAll(outKeyword, "~", ",")
	return ret
}
