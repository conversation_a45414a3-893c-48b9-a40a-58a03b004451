package user_mgmt

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	mock_iam "code.byted.org/infcs/dbw-mgr/biz/test/mocks/iam"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/protoactor-go/actor"
	"errors"
	"github.com/aws/aws-sdk-go/aws"
	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	new_iam "github.com/volcengine/volcengine-go-sdk/service/iam"
	"github.com/volcengine/volcengine-go-sdk/service/iam20210801"
	"github.com/volcengine/volcengine-go-sdk/volcengine/response"
	"go.uber.org/dig"
	"gorm.io/gorm"
	"testing"
	"time"
)

type UserMgmtActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *UserMgmtActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *UserMgmtActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestUserMgmtActorSuite(t *testing.T) {
	suite.Run(t, new(UserMgmtActorSuite))
}

func (suite *UserMgmtActorSuite) TestNewTaskActor() {
	ret := NewUserManagementActor(UserManagementActorIn{
		In: dig.In{},
	})
	suite.NotEmpty(ret)
}

func getUserManagementActor() *UserManagementActor {
	return &UserManagementActor{
		iam:                     &mock_iam.MockIAM{},
		actorClient:             &mocks.MockActorClient{},
		idSvc:                   &mocks.MockService{},
		userDal:                 &mocks.MockDbwUserDAL{},
		userGroupDal:            &mocks.MockDbwUserGroupDAL{},
		projectGroupRelationDal: &mocks.MockProjectGroupRelationDAL{},
		userGroupRelationDal:    &mocks.MockUserGroupRelationDAL{},
		instancePermDal:         &mocks.MockInstancePrivilegeDAL{},
		databasePermDal:         &mocks.MockDatabasePrivilegeDAL{},
		tablePermDal:            &mocks.MockTablePrivilegeDAL{},
		columnPermDal:           &mocks.MockColumnPrivilegeDAL{},
		userSvc:                 &mocks.MockUserService{},
		state: &State{
			LastBeginTimeStamp:  time.Now().Add(-24 * time.Hour).Unix(),
			LastFinishTimeStamp: time.Now().Add(-24 * time.Hour).Unix(),
		},
	}
}

func (suite *UserMgmtActorSuite) Test_ProcessStart() {
	baseMock1 := Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()

	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	ctx.EXPECT().Message().Return(&actor.Started{}).AnyTimes()
	ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	taskActor := NewUserManagementActor(UserManagementActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.UserMgmtActorKind, consts.SingletonActorName, []byte{})
	taskActor.Process(ctx)
}

func (suite *UserMgmtActorSuite) Test_ProcessReceiveTimeout() {
	baseMock1 := Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	mock1 := Mock((*UserManagementActor).SyncAllProject).Return(nil).Build()
	defer mock1.UnPatch()

	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	ctx.EXPECT().Message().Return(&actor.ReceiveTimeout{}).AnyTimes()
	ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	taskActor := NewUserManagementActor(UserManagementActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.UserMgmtActorKind, consts.SingletonActorName, []byte{})
	taskActor.Process(ctx)
}

func (suite *UserMgmtActorSuite) Test_ProcessUserGroupSyncReq() {
	baseMock1 := Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	mock1 := Mock((*UserManagementActor).SyncAllProject).Return(nil).Build()
	defer mock1.UnPatch()
	ctx := mocks.NewMockContext(suite.ctrl)
	ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	ctx.EXPECT().Message().Return(&shared.UserGroupSyncReq{}).AnyTimes()
	ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	ctx.EXPECT().Sender().Return(&actor.PID{}).AnyTimes()
	taskActor := NewUserManagementActor(UserManagementActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.UserMgmtActorKind, consts.SingletonActorName, []byte{})
	taskActor.Process(ctx)
}

func Test_SyncAllProject(t *testing.T) {
	ctx := &mocks.MockContext{}
	baseMock1 := Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := Mock(log.Log).Return().Build()
	defer baseMock2.UnPatch()
	self := getUserManagementActor()
	tenantId := "testTenantId"
	PatchConvey("Test ListAll failed", t, func() {
		Mock((*mocks.MockProjectGroupRelationDAL).ListAll).Return(nil, errors.New("list all failed")).Build()
		err := self.SyncAllProject(ctx, tenantId, false)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test ListAll success", t, func() {
		Mock((*mocks.MockProjectGroupRelationDAL).ListAll).Return(&dao.ProjectGroupRelations{
			ProjectGroupRelations: []*dao.ProjectGroupRelation{
				{Project: "project1"},
				{Project: "project2"},
			},
		}, nil).Build()
		Mock((*mocks.MockDbwUserGroupDAL).Get).Return(nil, nil).Build()

		PatchConvey("Test ListProjectIdentitiesUser failed", func() {
			Mock((*mock_iam.MockIAM).ListProjectIdentitiesUser).Return(nil, errors.New("list identities user failed")).Build()
			err := self.SyncAllProject(ctx, tenantId, false)
			So(err, ShouldBeNil)
		})

		PatchConvey("Test ListProjectIdentitiesUser success", func() {
			Mock((*mock_iam.MockIAM).ListProjectIdentitiesUser).Return(&iam20210801.ListProjectIdentitiesOutput{}, nil).Build()

			PatchConvey("Test updateUsersByProject failed", func() {
				Mock((*UserManagementActor).updateUsersByProject).Return(errors.New("update users by project failed")).Build()
				err := self.SyncAllProject(ctx, tenantId, false)
				So(err, ShouldBeNil)
			})

			PatchConvey("Test updateUsersByProject success", func() {
				Mock((*UserManagementActor).updateUsersByProject).Return(nil).Build()
				err := self.SyncAllProject(ctx, tenantId, false)
				So(err, ShouldBeNil)
			})
		})

		PatchConvey("Test retry ListProjectIdentitiesUser failed", func() {
			Mock((*mock_iam.MockIAM).ListProjectIdentitiesUser).Return(nil, errors.New("list identities user failed")).Build()
			Mock((*UserManagementActor).updateUsersByProject).Return(errors.New("update users by project failed")).Build()
			err := self.SyncAllProject(ctx, tenantId, false)
			So(err, ShouldBeNil)
		})

		PatchConvey("Test retry ListProjectIdentitiesUser success", func() {
			Mock((*mock_iam.MockIAM).ListProjectIdentitiesUser).Return(&iam20210801.ListProjectIdentitiesOutput{}, nil).Build()
			Mock((*UserManagementActor).updateUsersByProject).Return(nil).Build()
			err := self.SyncAllProject(ctx, tenantId, false)
			So(err, ShouldBeNil)
		})
	})
}

func Test_updateUsersByProject(t *testing.T) {
	ctx := &mocks.MockContext{}
	baseMock1 := Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := Mock(log.Log).Return().Build()
	defer baseMock2.UnPatch()
	self := getUserManagementActor()
	projectGroupRelation := &dao.ProjectGroupRelation{
		ID:        1,
		TenantID:  "tenant1",
		GroupID:   1,
		Project:   "project1",
		IAMGroups: "group1,group2",
		IAMUser:   true,
		CreatedAt: 1634567890,
		UpdatedAt: 1634567890,
		DeletedAt: 0,
		Deleted:   false,
	}
	relation := &iam20210801.ListProjectIdentitiesOutput{
		Metadata:          &response.ResponseMetadata{},
		Limit:             aws.Int32(10),
		Offset:            aws.Int32(0),
		Total:             aws.Int32(2),
		ProjectRoles:      []*iam20210801.ProjectRoleForListProjectIdentitiesOutput{},
		ProjectUserGroups: []*iam20210801.ProjectUserGroupForListProjectIdentitiesOutput{},
		ProjectUsers: []*iam20210801.ProjectUserForListProjectIdentitiesOutput{
			{
				UserName: aws.String("user1"),
			},
			{
				UserName: aws.String("user2"),
			},
		},
	}

	PatchConvey("Test get user group info failed", t, func() {
		Mock((*mocks.MockDbwUserGroupDAL).Get).Return(nil, errors.New("get user group info failed")).Build()
		err := self.updateUsersByProject(ctx, projectGroupRelation, relation, false)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test group not sync", t, func() {
		groupInfo := &dao.DbwUserGroup{
			ID:                    1,
			TenantID:              "tenant1",
			GroupName:             "group1",
			Sync:                  false,
			MaxExecuteCount:       100,
			MaxExecuteExpiredTime: 100,
			MaxExecuteDuration:    "1d",
			MaxResultCount:        100,
			MaxResultExpiredTime:  100,
			MaxResultDuration:     "1d",
			ByProject:             true,
			CreatedAt:             1634567890,
			UpdatedAt:             1634567890,
			DeletedAt:             0,
			Deleted:               false,
		}
		Mock((*mocks.MockDbwUserGroupDAL).Get).Return(groupInfo, nil).Build()
		err := self.updateUsersByProject(ctx, projectGroupRelation, relation, false)
		So(err, ShouldBeNil)
	})

	PatchConvey("Test get iam group info failed", t, func() {
		groupInfo := &dao.DbwUserGroup{
			ID:                    1,
			TenantID:              "tenant1",
			GroupName:             "group1",
			Sync:                  true,
			MaxExecuteCount:       100,
			MaxExecuteExpiredTime: 100,
			MaxExecuteDuration:    "1d",
			MaxResultCount:        100,
			MaxResultExpiredTime:  100,
			MaxResultDuration:     "1d",
			ByProject:             true,
			CreatedAt:             1634567890,
			UpdatedAt:             1634567890,
			DeletedAt:             0,
			Deleted:               false,
		}
		Mock((*mocks.MockDbwUserGroupDAL).Get).Return(groupInfo, nil).Build()
		Mock((*mock_iam.MockIAM).ListUsersForGroup).Return(nil, errors.New("get iam group info failed")).Build()
		err := self.updateUsersByProject(ctx, projectGroupRelation, relation, false)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test get user id failed", t, func() {
		groupInfo := &dao.DbwUserGroup{
			ID:                    1,
			TenantID:              "tenant1",
			GroupName:             "group1",
			Sync:                  true,
			MaxExecuteCount:       100,
			MaxExecuteExpiredTime: 100,
			MaxExecuteDuration:    "1d",
			MaxResultCount:        100,
			MaxResultExpiredTime:  100,
			MaxResultDuration:     "1d",
			ByProject:             true,
			CreatedAt:             1634567890,
			UpdatedAt:             1634567890,
			DeletedAt:             0,
			Deleted:               false,
		}
		Mock((*mocks.MockDbwUserGroupDAL).Get).Return(groupInfo, nil).Build()
		Mock((*mock_iam.MockIAM).ListUsersForGroup).Return(&new_iam.ListUsersForGroupOutput{
			Metadata: &response.ResponseMetadata{},
			Limit:    aws.Int32(10),
			Offset:   aws.Int32(0),
			Total:    aws.Int32(2),
			Users: []*new_iam.UserForListUsersForGroupOutput{
				{
					Id: aws.Int32(1),
				},
				{
					Id: aws.Int32(2),
				},
			},
		}, nil).Build()
		Mock((*mock_iam.MockIAM).GetUserIdByName).Return("", errors.New("get user id failed")).Build()
		err := self.updateUsersByProject(ctx, projectGroupRelation, relation, false)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test get user group relations failed", t, func() {
		groupInfo := &dao.DbwUserGroup{
			ID:                    1,
			TenantID:              "tenant1",
			GroupName:             "group1",
			Sync:                  true,
			MaxExecuteCount:       100,
			MaxExecuteExpiredTime: 100,
			MaxExecuteDuration:    "1d",
			MaxResultCount:        100,
			MaxResultExpiredTime:  100,
			MaxResultDuration:     "1d",
			ByProject:             true,
			CreatedAt:             1634567890,
			UpdatedAt:             1634567890,
			DeletedAt:             0,
			Deleted:               false,
		}
		Mock((*mocks.MockDbwUserGroupDAL).Get).Return(groupInfo, nil).Build()
		Mock((*mock_iam.MockIAM).ListUsersForGroup).Return(&new_iam.ListUsersForGroupOutput{
			Metadata: &response.ResponseMetadata{},
			Limit:    aws.Int32(10),
			Offset:   aws.Int32(0),
			Total:    aws.Int32(2),
			Users: []*new_iam.UserForListUsersForGroupOutput{
				{
					Id: aws.Int32(1),
				},
				{
					Id: aws.Int32(2),
				},
			},
		}, nil).Build()
		Mock((*mock_iam.MockIAM).GetUserIdByName).Return("1", nil).Build()
		Mock((*mocks.MockUserGroupRelationDAL).ListAllByGroup).Return(nil, errors.New("get user group relations failed")).Build()
		err := self.updateUsersByProject(ctx, projectGroupRelation, relation, false)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test delete user failed", t, func() {
		groupInfo := &dao.DbwUserGroup{
			ID:                    1,
			TenantID:              "tenant1",
			GroupName:             "group1",
			Sync:                  true,
			MaxExecuteCount:       100,
			MaxExecuteExpiredTime: 100,
			MaxExecuteDuration:    "1d",
			MaxResultCount:        100,
			MaxResultExpiredTime:  100,
			MaxResultDuration:     "1d",
			ByProject:             true,
			CreatedAt:             1634567890,
			UpdatedAt:             1634567890,
			DeletedAt:             0,
			Deleted:               false,
		}
		Mock((*mocks.MockDbwUserGroupDAL).Get).Return(groupInfo, nil).Build()
		Mock((*mock_iam.MockIAM).ListUsersForGroup).Return(&new_iam.ListUsersForGroupOutput{
			Metadata: &response.ResponseMetadata{},
			Limit:    aws.Int32(10),
			Offset:   aws.Int32(0),
			Total:    aws.Int32(2),
			Users: []*new_iam.UserForListUsersForGroupOutput{
				{
					Id: aws.Int32(1),
				},
				{
					Id: aws.Int32(2),
				},
			},
		}, nil).Build()
		Mock((*mock_iam.MockIAM).GetUserIdByName).Return("1", nil).Build()
		Mock((*mocks.MockUserGroupRelationDAL).ListAllByGroup).Return(&dao.UserGroupRelations{
			GroupRelations: []*dao.UserGroupRelation{
				{
					ID:        1,
					TenantID:  "tenant1",
					GroupID:   1,
					UserID:    "1",
					CreatedAt: 1634567890,
					UpdatedAt: 1634567890,
					DeletedAt: 0,
					Deleted:   false,
				},
				{
					ID:        2,
					TenantID:  "tenant1",
					GroupID:   1,
					UserID:    "2",
					CreatedAt: 1634567890,
					UpdatedAt: 1634567890,
					DeletedAt: 0,
					Deleted:   false,
				},
			},
			Total: 2,
		}, nil).Build()
		Mock((*UserManagementActor).deleteUser).Return(errors.New("delete user failed")).Build()
		err := self.updateUsersByProject(ctx, projectGroupRelation, relation, false)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test add user failed", t, func() {
		groupInfo := &dao.DbwUserGroup{
			ID:                    1,
			TenantID:              "tenant1",
			GroupName:             "group1",
			Sync:                  true,
			MaxExecuteCount:       100,
			MaxExecuteExpiredTime: 100,
			MaxExecuteDuration:    "1d",
			MaxResultCount:        100,
			MaxResultExpiredTime:  100,
			MaxResultDuration:     "1d",
			ByProject:             true,
			CreatedAt:             1634567890,
			UpdatedAt:             1634567890,
			DeletedAt:             0,
			Deleted:               false,
		}
		Mock((*mocks.MockDbwUserGroupDAL).Get).Return(groupInfo, nil).Build()
		Mock((*mock_iam.MockIAM).ListUsersForGroup).Return(&new_iam.ListUsersForGroupOutput{
			Metadata: &response.ResponseMetadata{},
			Limit:    aws.Int32(10),
			Offset:   aws.Int32(0),
			Total:    aws.Int32(2),
			Users: []*new_iam.UserForListUsersForGroupOutput{
				{
					Id: aws.Int32(1),
				},
				{
					Id: aws.Int32(2),
				},
			},
		}, nil).Build()
		Mock((*mock_iam.MockIAM).GetUserIdByName).Return("1", nil).Build()
		Mock((*mocks.MockUserGroupRelationDAL).ListAllByGroup).Return(&dao.UserGroupRelations{
			GroupRelations: []*dao.UserGroupRelation{
				{
					ID:        1,
					TenantID:  "tenant1",
					GroupID:   1,
					UserID:    "1",
					CreatedAt: 1634567890,
					UpdatedAt: 1634567890,
					DeletedAt: 0,
					Deleted:   false,
				},
			},
			Total: 1,
		}, nil).Build()
		Mock((*UserManagementActor).deleteUser).Return(nil).Build()
		Mock((*UserManagementActor).addUser).Return(errors.New("add user failed")).Build()
		err := self.updateUsersByProject(ctx, projectGroupRelation, relation, false)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test success", t, func() {
		groupInfo := &dao.DbwUserGroup{
			ID:                    1,
			TenantID:              "tenant1",
			GroupName:             "group1",
			Sync:                  true,
			MaxExecuteCount:       100,
			MaxExecuteExpiredTime: 100,
			MaxExecuteDuration:    "1d",
			MaxResultCount:        100,
			MaxResultExpiredTime:  100,
			MaxResultDuration:     "1d",
			ByProject:             true,
			CreatedAt:             1634567890,
			UpdatedAt:             1634567890,
			DeletedAt:             0,
			Deleted:               false,
		}
		Mock((*mocks.MockDbwUserGroupDAL).Get).Return(groupInfo, nil).Build()
		Mock((*mock_iam.MockIAM).ListUsersForGroup).Return(&new_iam.ListUsersForGroupOutput{
			Metadata: &response.ResponseMetadata{},
			Limit:    aws.Int32(10),
			Offset:   aws.Int32(0),
			Total:    aws.Int32(2),
			Users: []*new_iam.UserForListUsersForGroupOutput{
				{
					Id: aws.Int32(1),
				},
				{
					Id: aws.Int32(2),
				},
			},
		}, nil).Build()
		Mock((*mock_iam.MockIAM).GetUserIdByName).Return("1", nil).Build()
		Mock((*mocks.MockUserGroupRelationDAL).ListAllByGroup).Return(&dao.UserGroupRelations{
			GroupRelations: []*dao.UserGroupRelation{
				{
					ID:        1,
					TenantID:  "tenant1",
					GroupID:   1,
					UserID:    "1",
					CreatedAt: 1634567890,
					UpdatedAt: 1634567890,
					DeletedAt: 0,
					Deleted:   false,
				},
			},
			Total: 1,
		}, nil).Build()
		Mock((*UserManagementActor).deleteUser).Return(nil).Build()
		Mock((*UserManagementActor).addUser).Return(nil).Build()
		err := self.updateUsersByProject(ctx, projectGroupRelation, relation, false)
		So(err, ShouldBeNil)
	})
}

func Test_addUser(t *testing.T) {
	ctx := &mocks.MockContext{}
	baseMock1 := Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := Mock(log.Log).Return().Build()
	defer baseMock2.UnPatch()
	userList := []string{"user1", "user2"}
	groupInfo := &dao.DbwUserGroup{
		ID:                    1,
		TenantID:              "tenant1",
		GroupName:             "group1",
		Role:                  "admin",
		MaxExecuteCount:       100,
		MaxExecuteExpiredTime: 3600,
		MaxExecuteDuration:    "daily",
		MaxResultCount:        500,
		MaxResultExpiredTime:  3600,
		MaxResultDuration:     "daily",
	}
	self := getUserManagementActor()

	PatchConvey("Test GetUserNameById failed", t, func() {
		Mock((*mock_iam.MockIAM).GetUserNameById).Return("", errors.New("get user name failed")).Build()
		err := self.addUser(ctx, userList, groupInfo)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test Get user from DB failed with other error", t, func() {
		Mock((*mock_iam.MockIAM).GetUserNameById).Return("user1", nil).Build()
		Mock((*mocks.MockDbwUserDAL).Get).Return(nil, errors.New("other error")).Build()
		err := self.addUser(ctx, userList, groupInfo)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test Get user from DB not found", t, func() {
		Mock((*mock_iam.MockIAM).GetUserNameById).Return("user1", nil).Build()
		Mock((*mocks.MockDbwUserDAL).Get).Return(nil, gorm.ErrRecordNotFound).Build()
		Mock((*mocks.MockService).NextID).Return(1, nil).Build()
		Mock((*mocks.MockUserService).GrantGroupToUser).Return(nil).Build()
		Mock((*mocks.MockDbwUserDAL).SaveBatch).Return(nil).Build()
		Mock((*mocks.MockUserGroupRelationDAL).CreateBatch).Return(nil).Build()
		err := self.addUser(ctx, userList, groupInfo)
		So(err, ShouldBeNil)
	})

	PatchConvey("Test GrantGroupToUser failed", t, func() {
		Mock((*mock_iam.MockIAM).GetUserNameById).Return("user1", nil).Build()
		Mock((*mocks.MockDbwUserDAL).Get).Return(&dao.DbwUser{UserID: "user1"}, nil).Build()
		Mock((*mocks.MockService).NextID).Return(1, nil).Build()
		Mock((*mocks.MockUserService).GrantGroupToUser).Return(gorm.ErrRecordNotFound).Build()
		err := self.addUser(ctx, userList, groupInfo)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test SaveBatch failed", t, func() {
		Mock((*mock_iam.MockIAM).GetUserNameById).Return("user1", nil).Build()
		Mock((*mocks.MockDbwUserDAL).Get).Return(&dao.DbwUser{UserID: "user1"}, nil).Build()
		Mock((*mocks.MockService).NextID).Return(1, nil).Build()
		Mock((*mocks.MockUserService).GrantGroupToUser).Return(nil).Build()
		Mock((*mocks.MockDbwUserDAL).SaveBatch).Return(gorm.ErrRecordNotFound).Build()
		err := self.addUser(ctx, userList, groupInfo)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test CreateBatch failed", t, func() {
		Mock((*mock_iam.MockIAM).GetUserNameById).Return("user1", nil).Build()
		Mock((*mocks.MockDbwUserDAL).Get).Return(&dao.DbwUser{UserID: "user1"}, nil).Build()
		Mock((*mocks.MockService).NextID).Return(1, nil).Build()
		Mock((*mocks.MockUserService).GrantGroupToUser).Return(nil).Build()
		Mock((*mocks.MockDbwUserDAL).SaveBatch).Return(nil).Build()
		Mock((*mocks.MockUserGroupRelationDAL).CreateBatch).Return(gorm.ErrRecordNotFound).Build()
		err := self.addUser(ctx, userList, groupInfo)
		So(err, ShouldNotBeNil)
	})

	PatchConvey("Test success", t, func() {
		Mock((*mock_iam.MockIAM).GetUserNameById).Return("user1", nil).Build()
		Mock((*mocks.MockDbwUserDAL).Get).Return(&dao.DbwUser{UserID: "user1"}, nil).Build()
		Mock((*mocks.MockService).NextID).Return(1, nil).Build()
		Mock((*mocks.MockUserService).GrantGroupToUser).Return(nil).Build()
		Mock((*mocks.MockDbwUserDAL).SaveBatch).Return(nil).Build()
		Mock((*mocks.MockUserGroupRelationDAL).CreateBatch).Return(nil).Build()
		err := self.addUser(ctx, userList, groupInfo)
		So(err, ShouldBeNil)
	})
}

func Test_deleteUser(t *testing.T) {
	ctx := &mocks.MockContext{}
	baseMock1 := Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := Mock(log.Log).Return().Build()
	defer baseMock2.UnPatch()
	userList := []string{"user1", "user2"}
	groupInfo := &dao.DbwUserGroup{
		ID:        1,
		TenantID:  "tenant1",
		GroupName: "group1",
	}
	self := getUserManagementActor()

	PatchConvey("Test success", t, func() {
		Mock((*mocks.MockInstancePrivilegeDAL).DeleteByCondition).Return(nil).Build()
		Mock((*mocks.MockDatabasePrivilegeDAL).DeleteByCondition).Return(nil).Build()
		Mock((*mocks.MockTablePrivilegeDAL).DeleteByCondition).Return(nil).Build()
		Mock((*mocks.MockColumnPrivilegeDAL).DeleteByCondition).Return(nil).Build()
		Mock((*mocks.MockUserGroupRelationDAL).DeleteByCondition).Return(nil).Build()
		Mock((*mocks.MockUserService).DeleteUserByGroup).Return(nil).Build()
		err := self.deleteUser(ctx, userList, groupInfo)
		So(err, ShouldBeNil)
	})
}

//func Test_getNextStartTime(t *testing.T) {
//	PatchConvey("Test getNextStartTime", t, func() {
//		// Mock time.Now() to return a fixed time for consistent testing
//		fixedTime := time.Date(2023, 10, 1, 12, 0, 0, 0, time.UTC)
//		Mock(time.Now).Return(fixedTime).Build()
//
//		expectedMidnight := time.Date(2023, 10, 2, 0, 0, 0, 0, time.UTC)
//		expectedDuration := time.Duration(int(expectedMidnight.Sub(fixedTime).Seconds())) * time.Second
//
//		actualDuration := getNextStartTime()
//		So(actualDuration, ShouldEqual, expectedDuration)
//	})
//
//	PatchConvey("Test getNextStartTime with different time zone", t, func() {
//		// Mock time.Now() to return a fixed time in a different time zone
//		fixedTime := time.Date(2023, 10, 1, 12, 0, 0, 0, time.FixedZone("CST", 8*3600))
//		Mock(time.Now).Return(fixedTime).Build()
//
//		expectedMidnight := time.Date(2023, 10, 2, 0, 0, 0, 0, time.FixedZone("CST", 8*3600))
//		expectedDuration := time.Duration(int(expectedMidnight.Sub(fixedTime).Seconds())) * time.Second
//
//		actualDuration := getNextStartTime()
//		So(actualDuration, ShouldEqual, expectedDuration)
//	})
//
//	PatchConvey("Test getNextStartTime with time just before midnight", t, func() {
//		// Mock time.Now() to return a time just before midnight
//		fixedTime := time.Date(2023, 10, 1, 23, 59, 59, 0, time.UTC)
//		Mock(time.Now).Return(fixedTime).Build()
//
//		expectedMidnight := time.Date(2023, 10, 2, 0, 0, 0, 0, time.UTC)
//		expectedDuration := time.Duration(int(expectedMidnight.Sub(fixedTime).Seconds())) * time.Second
//
//		actualDuration := getNextStartTime()
//		So(actualDuration, ShouldEqual, expectedDuration)
//	})
//
//	PatchConvey("Test getNextStartTime with time just after midnight", t, func() {
//		// Mock time.Now() to return a time just after midnight
//		fixedTime := time.Date(2023, 10, 2, 0, 0, 1, 0, time.UTC)
//		Mock(time.Now).Return(fixedTime).Build()
//
//		expectedMidnight := time.Date(2023, 10, 3, 0, 0, 0, 0, time.UTC)
//		expectedDuration := time.Duration(int(expectedMidnight.Sub(fixedTime).Seconds())) * time.Second
//
//		actualDuration := getNextStartTime()
//		So(actualDuration, ShouldEqual, expectedDuration)
//	})
//}

func Test_isSameDay(t *testing.T) {
	PatchConvey("Test isSameDay with same day", t, func() {
		timestampA := time.Date(2023, 10, 1, 12, 0, 0, 0, time.UTC).Unix()
		timestampB := time.Date(2023, 10, 1, 15, 30, 0, 0, time.UTC).Unix()
		result := isSameDay(timestampA, timestampB)
		So(result, ShouldBeTrue)
	})

	PatchConvey("Test isSameDay with different days", t, func() {
		timestampA := time.Date(2023, 10, 1, 12, 0, 0, 0, time.UTC).Unix()
		timestampB := time.Date(2023, 10, 2, 15, 30, 0, 0, time.UTC).Unix()
		result := isSameDay(timestampA, timestampB)
		So(result, ShouldBeFalse)
	})

	PatchConvey("Test isSameDay with different years", t, func() {
		timestampA := time.Date(2022, 10, 1, 12, 0, 0, 0, time.UTC).Unix()
		timestampB := time.Date(2023, 10, 1, 15, 30, 0, 0, time.UTC).Unix()
		result := isSameDay(timestampA, timestampB)
		So(result, ShouldBeFalse)
	})

	PatchConvey("Test isSameDay with different months", t, func() {
		timestampA := time.Date(2023, 9, 1, 12, 0, 0, 0, time.UTC).Unix()
		timestampB := time.Date(2023, 10, 1, 15, 30, 0, 0, time.UTC).Unix()
		result := isSameDay(timestampA, timestampB)
		So(result, ShouldBeFalse)
	})
}
