package user_mgmt

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/iam"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	bizUtils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	"github.com/volcengine/volcengine-go-sdk/service/iam20210801"
	"gorm.io/gorm"
	"strconv"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"encoding/json"
	"github.com/google/uuid"
	"go.uber.org/dig"
	"time"
)

const Interval = 30 * time.Millisecond

type UserManagementActorIn struct {
	dig.In
	ActorClient             cli.ActorClient
	Iam                     iam.IAM
	IDSvc                   idgen.Service
	UserDal                 dal.DbwUserDAL
	UserGroupDal            dal.DbwUserGroupDAL
	ProjectGroupRelationDal dal.ProjectGroupRelationDAL
	UserGroupRelationDal    dal.UserGroupRelationDAL
	InstancePermDal         dal.InstancePrivilegeDAL
	DatabasePermDal         dal.DatabasePrivilegeDAL
	TablePermDal            dal.TablePrivilegeDAL
	ColumnPermDal           dal.ColumnPrivilegeDAL
	UserSvc                 usermgmt.UserService
}
type UserManagementActor struct {
	actorClient             cli.ActorClient
	state                   *State
	iam                     iam.IAM
	idSvc                   idgen.Service
	userDal                 dal.DbwUserDAL
	userGroupDal            dal.DbwUserGroupDAL
	projectGroupRelationDal dal.ProjectGroupRelationDAL
	userGroupRelationDal    dal.UserGroupRelationDAL
	instancePermDal         dal.InstancePrivilegeDAL
	databasePermDal         dal.DatabasePrivilegeDAL
	tablePermDal            dal.TablePrivilegeDAL
	columnPermDal           dal.ColumnPrivilegeDAL
	userSvc                 usermgmt.UserService
}

type State struct {
	LastBeginTimeStamp  int64
	LastFinishTimeStamp int64
}

func NewUserManagementState(bytes []byte) *State {
	state := &State{}
	json.Unmarshal(bytes, &state)
	return state
}

func NewUserManagementActor(p UserManagementActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.UserMgmtActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &UserManagementActor{
				state:                   NewUserManagementState(state),
				actorClient:             p.ActorClient,
				iam:                     p.Iam,
				idSvc:                   p.IDSvc,
				userDal:                 p.UserDal,
				userGroupDal:            p.UserGroupDal,
				userGroupRelationDal:    p.UserGroupRelationDal,
				projectGroupRelationDal: p.ProjectGroupRelationDal,
				instancePermDal:         p.InstancePermDal,
				databasePermDal:         p.DatabasePermDal,
				tablePermDal:            p.TablePermDal,
				columnPermDal:           p.ColumnPermDal,
				userSvc:                 p.UserSvc,
			}
		}),
	}
}

func (self *UserManagementActor) Process(ctx types.Context) {
	sysCtx := context.WithValue(context.Background(), "biz-context", &fwctx.BizContext{
		LogID: "LogUserManagementActor-" + uuid.New().String(),
	})
	ctx = types.BuildMyContext(sysCtx, ctx, nil)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "User Management Actor Started, the last sync time is %v, now is %v", time.Unix(self.state.LastFinishTimeStamp, 0), time.Now())
		// 手动触发的不处理start消息
		if ctx.GetName() != consts.SingletonActorName {
			return
		}
		//if isSameDay(self.state.LastBeginTimeStamp, time.Now().Unix()) {
		//	// 如果今天做过同步，则更新下次同步时间
		//	ctx.SetReceiveTimeout(getNextStartTime())
		//} else {
		//	// 如果今天没做过同步，则一分钟后更新下
		//	ctx.SetReceiveTimeout(1 * time.Minute)
		//}
		ctx.SetReceiveTimeout(1 * time.Minute)
		return
	case *actor.ReceiveTimeout:
		beginTime := time.Now().Unix()
		log.Info(ctx, "Start to sync user project info, the last sync time is %v, now is %v", time.Unix(self.state.LastFinishTimeStamp, 0), time.Now())
		//if !isSameDay(time.Now().Unix(), self.state.LastFinishTimeStamp) {
		//	err := self.SyncAllProject(ctx, "")
		//	if err != nil {
		//		log.Warn(ctx, "Auto UserGroupSync Failed, err:%v", err)
		//	}
		//}
		err := self.SyncAllProject(ctx, "", false)
		if err != nil {
			log.Warn(ctx, "Auto UserGroupSync Failed, err:%v", err)
		}
		nextSyncTime := getNextStartTime()
		log.Info(ctx, "The next sync time after: %v", nextSyncTime)
		ctx.SetReceiveTimeout(nextSyncTime)
		endTime := time.Now().Unix()
		self.state.LastBeginTimeStamp = beginTime
		self.state.LastFinishTimeStamp = endTime
		return
	case *shared.UserGroupSyncReq:
		defer ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		log.Info(ctx, "Start to sync user project info manually")
		err := self.SyncAllProject(ctx, msg.TenantId, true)
		if err != nil {
			ctx.Respond(&shared.UserGroupSyncResp{
				Success:    false,
				ErrCode:    strconv.Itoa(500),
				ErrMessage: model.ErrorCode_InternalError.String(),
			})
			return
		}
		ctx.Respond(&shared.UserGroupSyncResp{
			Success: true,
		})
		return
	case *actor.Stopping:
		return
	}
}

func (self *UserManagementActor) GetState() []byte {
	state, _ := json.Marshal(self.state)
	return state
}

func (self *UserManagementActor) SyncAllProject(ctx types.Context, tenantId string, syncAll bool) error {
	var err error
	var failList []*dao.ProjectGroupRelation
	// 获取所有需要同步的用户组
	projectGroupRelations, err := self.projectGroupRelationDal.ListAll(ctx, tenantId)
	if err != nil {
		log.Error(ctx, "UserManagementActor get all user groups that need to be synchronized failed, err: %+v", err)
		return err
	}
	// 根据project获取最新详情
	for _, relation := range projectGroupRelations.ProjectGroupRelations {
		log.Info(ctx, "need to sync relation: %+v", relation.ID)
		_, err := self.userGroupDal.Get(ctx, relation.TenantID, strconv.FormatInt(relation.GroupID, 10))
		if err != nil && err == gorm.ErrRecordNotFound {
			self.projectGroupRelationDal.Delete(ctx, relation.TenantID, relation.GroupID)
			log.Info(ctx, "need to delete relation: %+v", relation.ID)
		}
		time.Sleep(Interval)
		identitiesUser, err := self.iam.ListProjectIdentitiesUser(ctx, relation.TenantID, relation.Project)
		if err != nil {
			failList = append(failList, relation)
			log.Warn(ctx, "UserManagementActor ListProjectIdentitiesUser failed, err: %+v", err)
			continue
		}
		// 更新用户
		err = self.updateUsersByProject(ctx, relation, identitiesUser, syncAll)
		if err != nil {
			failList = append(failList, relation)
			log.Warn(ctx, "UserManagementActor update users by project failed, err: %+v", err)
			continue
		}
	}
	// 失败的重试
	if len(failList) != 0 {
		for _, failRelation := range failList {
			time.Sleep(Interval)
			identitiesUser, err := self.iam.ListProjectIdentitiesUser(ctx, failRelation.TenantID, failRelation.Project)
			if err != nil {
				log.Warn(ctx, "UserManagementActor ListProjectIdentitiesUser failed again, err: %+v", err)
				continue
			}
			err = self.updateUsersByProject(ctx, failRelation, identitiesUser, syncAll)
			if err != nil {
				log.Warn(ctx, "UserManagementActor update users by project failed again, err: %+v", err)
				continue
			}
		}
	}
	return nil
}

// 更新对应用户组
func (self *UserManagementActor) updateUsersByProject(ctx types.Context, projectGroupRelation *dao.ProjectGroupRelation, relation *iam20210801.ListProjectIdentitiesOutput, syncAll bool) error {
	var iamUserList []string
	var localUserList []string
	groupInfo, err := self.userGroupDal.Get(ctx, projectGroupRelation.TenantID, strconv.FormatInt(projectGroupRelation.GroupID, 10))
	if err != nil {
		log.Warn(ctx, "UserManagementActor get user group info failed %+v", err)
		return err
	}
	if !groupInfo.Sync && !syncAll {
		return nil
	}
	// 如果绑定的iam group, 获取所有用户集合iamUserList
	if projectGroupRelation.IAMGroups != "" {
		for _, iamGroup := range strings.Split(projectGroupRelation.IAMGroups, ",") {
			time.Sleep(Interval)
			iamGroupInfo, err := self.iam.ListUsersForGroup(ctx, projectGroupRelation.TenantID, iamGroup)
			if err != nil {
				log.Warn(ctx, "UserManagementActor get iam group info failed %+v", err)
				return err
			}
			for _, iamGroupUser := range iamGroupInfo.Users {
				iamUserList = append(iamUserList, strconv.Itoa(int(*iamGroupUser.Id)))
			}
		}
	}
	// 如果绑定了非组用户，获取所有用户加入集合iamUserList
	if projectGroupRelation.IAMUser {
		for _, noneGroupUser := range relation.ProjectUsers {
			time.Sleep(Interval)
			userId, err := self.iam.GetUserIdByName(ctx, projectGroupRelation.TenantID, *noneGroupUser.UserName, "DbwUserMgmtForIAMRole")
			if err != nil {
				log.Warn(ctx, "UserManagementActor get user id failed %+v", err)
				return err
			}
			iamUserList = append(iamUserList, userId)
		}
	}
	// 获取dbw用户组已关联用户集合localUserList
	userGroupRelations, err := self.userGroupRelationDal.ListAllByGroup(ctx, projectGroupRelation.TenantID, strconv.FormatInt(projectGroupRelation.GroupID, 10), []string{})
	if err != nil {
		log.Warn(ctx, "UserManagementActor get user group relations failed %+v", err)
		return err
	}
	for _, localUser := range userGroupRelations.GroupRelations {
		localUserList = append(localUserList, localUser.UserID)
	}

	// 找出在 localUserList 中但不在 iamUserList 中的用户，添加用户
	localNotInIAM := bizUtils.FindDifference(localUserList, iamUserList)
	err = self.deleteUser(ctx, localNotInIAM, groupInfo)
	if err != nil {
		log.Warn(ctx, "UserManagementActor add user failed %+v", err)
		return err
	}

	// 找出在 iamUserList 中但不在 localUserList 中的用户，删除用户（类似删除用户组）
	iamNotInLocal := bizUtils.FindDifference(iamUserList, localUserList)
	err = self.addUser(ctx, iamNotInLocal, groupInfo)
	if err != nil {
		log.Warn(ctx, "UserManagementActor delete user failed %+v", err)
		return err
	}
	return nil
}

func (self *UserManagementActor) addUser(ctx types.Context, userList []string, groupInfo *dao.DbwUserGroup) error {
	now := time.Now().Unix()
	var err error
	var userInfo *dao.DbwUser
	var newUsers []*dao.DbwUser
	var userGroupRelations []*dao.UserGroupRelation
	for _, userId := range userList {
		time.Sleep(Interval)
		userName, err := self.iam.GetUserNameById(ctx, groupInfo.TenantID, userId, "DbwUserMgmtForIAMRole")
		if err != nil {
			return err
		}
		// 是否已经加入用户管理，没加入的创建
		_, err = self.userDal.Get(ctx, userId, groupInfo.TenantID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// 创建
				id, err := self.idSvc.NextID(ctx)
				if err != nil {
					log.Warn(ctx, "Generate ID failed %+v", err)
					return err
				}
				userInfo = &dao.DbwUser{
					ID:                    id,
					TenantID:              groupInfo.TenantID,
					UserID:                userId,
					UserName:              userName,
					Role:                  groupInfo.Role,
					State:                 model.UserState_NORMAL.String(),
					GroupType:             model.UserGroupType_CUSTOM.String(),
					MaxResultDuration:     groupInfo.MaxResultDuration,
					MaxExecuteDuration:    groupInfo.MaxExecuteDuration,
					MaxResultCount:        groupInfo.MaxResultCount,
					MaxExecuteCount:       groupInfo.MaxExecuteCount,
					MaxExecuteExpiredTime: groupInfo.MaxExecuteExpiredTime,
					MaxResultExpiredTime:  groupInfo.MaxResultExpiredTime,
					AccountType:           model.AccountType_CHILD.String(),
					CreatedAt:             now,
					UpdatedAt:             now,
				}
				newUsers = append(newUsers, userInfo)
			} else {
				return err
			}
		}

		// 绑定用户组
		userGroupRelationId, err := self.idSvc.NextID(ctx)
		if err != nil {
			log.Warn(ctx, "Generate ID failed %+v", err)
			return err
		}
		userGroupRelations = append(userGroupRelations, &dao.UserGroupRelation{
			ID:        userGroupRelationId,
			TenantID:  groupInfo.TenantID,
			GroupID:   groupInfo.ID,
			UserID:    userId,
			CreatedAt: now,
			UpdatedAt: now,
		})
		//同步组权限给新用户
		err = self.userSvc.GrantGroupToUser(ctx, groupInfo.TenantID, userId, userName, strconv.FormatInt(groupInfo.ID, 10))
		if err != nil {
			log.Warn(ctx, "GrantGroupToUser failed %+v", err)
			return err
		}
	}
	//创建用户
	err = self.userDal.SaveBatch(ctx, newUsers)
	if err != nil {
		return err
	}
	// 创建用户和用户组关联关系
	err = self.userGroupRelationDal.CreateBatch(ctx, userGroupRelations)
	if err != nil {
		return err
	}
	return nil
}

func (self *UserManagementActor) deleteUser(ctx types.Context, userList []string, groupInfo *dao.DbwUserGroup) error {
	tenantId := groupInfo.TenantID
	groupId := strconv.FormatInt(groupInfo.ID, 10)
	for _, relationUser := range userList {
		var err error
		// 根据group和userid清除用户权限
		err = self.instancePermDal.DeleteByCondition(ctx, tenantId, groupId, relationUser)
		if err != nil {
			log.Warn(ctx, "Delete user instance privilege failed %+v", err)
		}
		err = self.databasePermDal.DeleteByCondition(ctx, tenantId, groupId, relationUser)
		if err != nil {
			log.Warn(ctx, "Delete user database privilege failed %+v", err)
		}
		err = self.tablePermDal.DeleteByCondition(ctx, tenantId, groupId, relationUser)
		if err != nil {
			log.Warn(ctx, "Delete user table privilege failed %+v", err)
		}
		err = self.columnPermDal.DeleteByCondition(ctx, tenantId, groupId, relationUser)
		if err != nil {
			log.Warn(ctx, "Delete user column privilege failed %+v", err)
		}
		// 删除用户组与用户的关联关系
		err = self.userGroupRelationDal.DeleteByCondition(ctx, tenantId, groupId, relationUser)
		if err != nil {
			log.Warn(ctx, "Delete user group relation failed %+v", err)
		}
		// 删除非个人用户
		err = self.userSvc.DeleteUserByGroup(ctx, tenantId, relationUser)
		if err != nil {
			log.Warn(ctx, "Delete user failed %+v", err)
		}
	}
	return nil
}

func getNextStartTime() time.Duration {
	now := time.Now()
	midnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
	return time.Duration(int(midnight.Sub(now).Seconds())) * time.Second
}

func isSameDay(timestampA, timestampB int64) bool {
	// 将时间戳转换为 time.Time 类型
	timeA := time.Unix(timestampA, 0)
	timeB := time.Unix(timestampB, 0)
	// 提取日期部分，忽略时、分、秒
	dateA := time.Date(timeA.Year(), timeA.Month(), timeA.Day(), 0, 0, 0, 0, timeA.Location())
	dateB := time.Date(timeB.Year(), timeB.Month(), timeB.Day(), 0, 0, 0, 0, timeB.Location())
	// 比较日期是否相同
	return dateA.Equal(dateB)
}
