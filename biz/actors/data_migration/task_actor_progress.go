package data_migration

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	biz_conv "code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_migration_job"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	batchv1 "k8s.io/api/batch/v1"
	"strings"
)

const (
	PreCheckJob      = "pre-check"
	DataMigrationJob = "data-migration"
)

var (
	invalidJobTypeError = errors.New("the job type is invalid")
)

func (t *TaskActor) processCreateDbExportTaskReq(ctx types.Context, req *shared.CreateDbExportTaskReq) {
	log.Info(ctx, " instance %s export data start!", t.state.InstanceId)
	t.updateTaskActorState(ctx, model.TaskState_Init,
		model.TaskAction_INIT_CONFIGMAP,
		model.TaskAction_RENDER_PRE_CHECK_TEMPLATE)
	resp := &shared.CreateDbExportTaskResp{}
	ctx.Respond(resp)
}

func (t *TaskActor) processCreateDbImportTaskReq(ctx types.Context, req *shared.CreateDbImportTaskReq) {
	log.Info(ctx, " instance %s import data start!", t.state.InstanceId)
	t.updateTaskActorState(ctx, model.TaskState_Init,
		model.TaskAction_INIT_CONFIGMAP,
		model.TaskAction_RENDER_PRE_CHECK_TEMPLATE)
	resp := &shared.CreateDbImportTaskResp{}
	ctx.Respond(resp)
}
func (t *TaskActor) processStopDataMigrationTaskReq(ctx types.Context, req *shared.CreateDbImportTaskReq) {
	log.Info(ctx, " instance %s import data start!", t.state.InstanceId)
	t.updateTaskActorState(ctx, model.TaskState_Init,
		model.TaskAction_INIT_CONFIGMAP,
		model.TaskAction_RENDER_PRE_CHECK_TEMPLATE)
	resp := &shared.CreateDbImportTaskResp{}
	ctx.Respond(resp)
}
func (t *TaskActor) deleteConfigMap(ctx types.Context, configMapName string) error {
	if t.k8sClient == nil {
		if err := t.checkK8sClient(ctx); err != nil {
			ctx.Respond(shared.DataMigrationFailed{
				Error:        shared.K8sClientInitFailed,
				ErrorMessage: err.Error(),
			})
			return err
		}
	}
	if err := t.k8sClient.DeleteConfigMap(ctx, configMapName); err != nil {
		return err
	}
	t.state.ConfigMapName = ""
	return nil
}
func (t *TaskActor) initExportConfigMap(ctx types.Context, req *entity.MigrationTask) error {
	if t.k8sClient == nil {
		if err := t.checkK8sClient(ctx); err != nil {
			ctx.Respond(shared.DataMigrationFailed{
				Error:        shared.K8sClientInitFailed,
				ErrorMessage: err.Error(),
			})
			return err
		}
	}
	exportConfig := &entity.ExportConfig{}
	err := json.Unmarshal([]byte(req.Config), exportConfig)
	if err != nil {
		log.Warn(ctx, "export_config unmarshal failed:%+v", err)
		return err
	}
	cnf := t.conf.Get(ctx)
	c3Cfg := t.c3Cnf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	tosAK := exportConfig.TosAK
	tosSK := exportConfig.TosSK
	tosToken := exportConfig.TosToken

	// TODO 重新传name
	// bucketName = "dbw-test"
	bucketName := exportConfig.TosBucket
	if strings.TrimSpace(bucketName) == "" {
		bucketName = c3Cfg.TOSBucketName
	}

	tosRegion := c3Cfg.TOSServiceRegion
	tosEndpoint := c3Cfg.TOSServiceEndpoint
	var (
		structures       []string
		bigDataOptions   []string
		sqlScriptOptions []string
	)
	for _, structs := range exportConfig.Structures {
		structures = append(structures, structs.String())
	}
	for _, bigData := range exportConfig.AdvancedOptions.GetBigDataOptions() {
		bigDataOptions = append(bigDataOptions, bigData.String())
	}
	for _, sqlScript := range exportConfig.AdvancedOptions.GetSqlScriptOptions() {
		sqlScriptOptions = append(sqlScriptOptions, sqlScript.String())
	}
	exportStruct := strings.Join(structures, ",")
	tableList := biz_conv.StringSliceToString(exportConfig.TableList)
	jobConfig := &data_migration_job.JobConfig{
		DmToolsImage: cnf.DataMigToolContainerImage,
		PodConf: data_migration_job.PodSpec{
			InstanceId:  req.InstanceID,
			KubeCluster: req.ClusterName,
		},
		TOSConnection: data_migration_job.TOSConnection{
			ServiceAK:     tosAK,
			ServiceSK:     tosSK,
			SecurityToken: tosToken,
			Endpoint:      tosEndpoint,
			BucketName:    bucketName,
			Region:        tosRegion,
		},
		TaskConf: data_migration_job.TaskConf{
			CommonConf: &data_migration_job.CommonConf{
				DbName:                 req.DbName,
				IP:                     exportConfig.IP,
				User:                   exportConfig.User,
				Password:               exportConfig.Password,
				Port:                   exportConfig.Port,
				TableList:              tableList,
				FileType:               exportConfig.FileType,
				FileEncoding:           exportConfig.Charset,
				ObjectName:             exportConfig.TargetName,
				TempPath:               exportConfig.TempPath,
				CsvFirstRowIsColumnDef: exportConfig.CsvFirstRowIsColumnDef,
			},
			ExportConf: &data_migration_job.DataExportConf{
				Structures:                        exportStruct,
				ContentFormat:                     exportConfig.ContentFormat,
				ExportType:                        exportConfig.ExportType,
				BigDataOptions:                    strings.Join(bigDataOptions, ","),
				SqlScriptOptions:                  strings.Join(sqlScriptOptions, ","),
				S3MaxBandWidthMb:                  cnf.S3MaxBandWidthMb,
				SqlText:                           exportConfig.SqlText,
				AppId:                             exportConfig.AppId,
				AppSecret:                         exportConfig.AppSecret,
				ChildFolderToken:                  exportConfig.ChildFolderToken,
				CloudFileToken:                    exportConfig.CloudFileToken,
				InfieldSqlResultExportResultLimit: cnf.InfieldSqlResultExportResultLimit,
			},
		},
	}
	t.state.JobConfig = jobConfig
	cmName, err := t.k8sClient.CreateConfigMap(ctx, jobConfig, "Export", t.state.TaskID)
	if err != nil {
		log.Warn(ctx, "create export configMap failed %+v", err)
		return err
	}
	t.updateTaskActorState(ctx, model.TaskState_Finished,
		model.TaskAction_INIT_CONFIGMAP,
		model.TaskAction_RENDER_PRE_CHECK_TEMPLATE)
	t.state.ConfigMapName = cmName
	return nil
}
func (t *TaskActor) initImportConfigMap(ctx types.Context, req *entity.MigrationTask) error {
	if t.k8sClient == nil {
		if err := t.checkK8sClient(ctx); err != nil {
			ctx.Respond(shared.DataMigrationFailed{
				Error:        shared.K8sClientInitFailed,
				ErrorMessage: err.Error(),
			})
			return err
		}
	}
	cnf := t.conf.Get(ctx)
	ImportConfig := &entity.ImportConfig{}
	err := json.Unmarshal([]byte(req.Config), ImportConfig)
	if err != nil {
		log.Warn(ctx, "import_config unmarshal failed:%+v", err)
		return err
	}
	c3Cfg := t.c3Cnf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	tosAK := ImportConfig.TosAK
	tosSK := ImportConfig.TosSK
	tosToken := ImportConfig.TosToken
	bucketName := c3Cfg.TOSBucketName
	tosRegion := c3Cfg.TOSServiceRegion
	tosEndpoint := c3Cfg.TOSServiceEndpoint
	jobConfig := &data_migration_job.JobConfig{
		DmToolsImage: cnf.DataMigToolContainerImage,
		PodConf: data_migration_job.PodSpec{
			InstanceId:  t.state.InstanceId,
			KubeCluster: req.ClusterName,
		},
		TOSConnection: data_migration_job.TOSConnection{
			ServiceAK:     tosAK,
			ServiceSK:     tosSK,
			SecurityToken: tosToken,
			Endpoint:      tosEndpoint,
			BucketName:    bucketName,
			Region:        tosRegion,
		},
		TaskConf: data_migration_job.TaskConf{
			CommonConf: &data_migration_job.CommonConf{
				DbName:                 req.DbName,
				IP:                     ImportConfig.IP,
				User:                   ImportConfig.User,
				Password:               ImportConfig.Password,
				Port:                   ImportConfig.Port,
				TableList:              ImportConfig.TableName,
				FileType:               ImportConfig.FileType,
				FileEncoding:           ImportConfig.Charset,
				ObjectName:             req.ObjectName,
				TempPath:               ImportConfig.TempPath,
				CsvFirstRowIsColumnDef: ImportConfig.CsvFirstRowIsColumnDef,
			},
			ImportConf: &data_migration_job.DataImportConf{
				IgnoreError: ImportConfig.IgnoreError,
				InsertType:  ImportConfig.CsvInputType,
				BlackList:   ImportConfig.BlackList,
				BatchNum:    ImportConfig.BatchNum,
			},
		},
	}
	t.state.JobConfig = jobConfig
	cmName, err := t.k8sClient.CreateConfigMap(ctx, jobConfig, "Import", t.state.TaskID)
	if err != nil {
		log.Warn(ctx, "create import configMap failed:%+v", err)
		return err
	}
	t.updateTaskActorState(ctx, model.TaskState_Finished,
		model.TaskAction_INIT_CONFIGMAP,
		model.TaskAction_RENDER_PRE_CHECK_TEMPLATE)
	t.state.ConfigMapName = cmName
	return nil
}
func (t *TaskActor) initExportJobConfig(ctx types.Context, jobType string) *batchv1.Job {
	var jobTemplate *batchv1.Job
	jobConfig := t.state.JobConfig
	if jobConfig == nil {
		log.Warn(ctx, "Init export JobConfig failed,because jobConfig is nil")
		return nil
	}
	log.Info(ctx, "Start init %s export job config", jobType)
	switch jobType {
	case PreCheckJob:
		t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_RENDER_PRE_CHECK_TEMPLATE, model.TaskAction_DEPLOY_PRE_CHECK_JOB)
		jobTemplate, _ = data_migration_job.GenerateJob(jobConfig, jobType, t.state.ConfigMapName, "Export", conv.Int64ToStr(t.state.TaskID))
		t.updateTaskActorState(ctx, model.TaskState_Finished, model.TaskAction_RENDER_PRE_CHECK_TEMPLATE, model.TaskAction_DEPLOY_PRE_CHECK_JOB)
	case DataMigrationJob:
		t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_RENDER_DM_TEMPLATE, model.TaskAction_DEPLOY_DM_JOB)
		jobTemplate, _ = data_migration_job.GenerateJob(jobConfig, jobType, t.state.ConfigMapName, "Export", conv.Int64ToStr(t.state.TaskID))
		t.updateTaskActorState(ctx, model.TaskState_Finished, model.TaskAction_RENDER_DM_TEMPLATE, model.TaskAction_DEPLOY_DM_JOB)
	}
	return jobTemplate
}

func (t *TaskActor) initImportJobConfig(ctx types.Context, jobType string) *batchv1.Job {
	var jobTemplate *batchv1.Job
	jobConfig := t.state.JobConfig
	if jobConfig == nil {
		log.Warn(ctx, "Init import JobConfig failed,because jobConfig is nil")
		return nil
	}
	log.Info(ctx, "Start init %s import job config", jobType)
	switch jobType {
	case PreCheckJob:
		t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_RENDER_PRE_CHECK_TEMPLATE,
			model.TaskAction_DEPLOY_PRE_CHECK_JOB)
		jobTemplate, _ = data_migration_job.GenerateJob(jobConfig, jobType, t.state.ConfigMapName, "Import", conv.Int64ToStr(t.state.TaskID))
		t.updateTaskActorState(ctx, model.TaskState_Finished, model.TaskAction_RENDER_PRE_CHECK_TEMPLATE,
			model.TaskAction_DEPLOY_PRE_CHECK_JOB)
	case DataMigrationJob:
		t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_RENDER_DM_TEMPLATE,
			model.TaskAction_DEPLOY_DM_JOB)
		jobTemplate, _ = data_migration_job.GenerateJob(jobConfig, jobType, t.state.ConfigMapName, "Import", conv.Int64ToStr(t.state.TaskID))
		t.updateTaskActorState(ctx, model.TaskState_Finished, model.TaskAction_RENDER_DM_TEMPLATE,
			model.TaskAction_DEPLOY_DM_JOB)
	}
	t.state.JobTemplate = jobTemplate
	return jobTemplate
}

func (t *TaskActor) createTaskJob(ctx types.Context, template *batchv1.Job, jobType string, tenantId string) (string, error) {
	var (
		jobName string
		err     error
	)
	if t.k8sClient == nil {
		if err := t.checkK8sClient(ctx); err != nil {
			ctx.Respond(shared.DataMigrationFailed{
				Error:        shared.K8sClientInitFailed,
				ErrorMessage: err.Error(),
			})
			return "", err
		}
	}
	taskId := t.state.TaskID
	switch jobType {
	case PreCheckJob:
		log.Info(ctx, "Create pre_check job start!")
		t.updateTaskActorState(ctx, model.TaskState_Init,
			model.TaskAction_DEPLOY_PRE_CHECK_JOB,
			model.TaskAction_MONITOR_PRE_CHECK_JOB)
		jobName, err = t.k8sClient.CreateTaskJob(ctx, template)
		if err != nil {
			log.Warn(ctx, "create pre_check job failed:%v", err)
			t.updateTaskActorState(ctx, model.TaskState_Failed,
				model.TaskAction_DEPLOY_PRE_CHECK_JOB,
				model.TaskAction_MONITOR_PRE_CHECK_JOB)
			return "", err
		} else {
			err := t.migRepo.UpdateJobByTaskID(ctx, taskId, jobName, t.state.ClusterName, tenantId)
			if err != nil {
				log.Warn(ctx, "update taskId %d jobName %s failed in metaDB %v", taskId, jobName, err)
			}
			t.state.JobName = jobName
			t.updateTaskActorState(ctx, model.TaskState_Finished,
				model.TaskAction_DEPLOY_PRE_CHECK_JOB,
				model.TaskAction_MONITOR_PRE_CHECK_JOB)
			return jobName, nil
		}
	case DataMigrationJob:
		log.Info(ctx, "Create data_migration job start!")
		t.updateTaskActorState(ctx, model.TaskState_Init,
			model.TaskAction_DEPLOY_DM_JOB,
			model.TaskAction_MONITOR_DM_JOB)
		jobName, err = t.k8sClient.CreateTaskJob(ctx, template)
		if err != nil {
			log.Warn(ctx, "create data_migration job failed:%v", err)
			t.updateTaskActorState(ctx, model.TaskState_Failed,
				model.TaskAction_DEPLOY_DM_JOB,
				model.TaskAction_MONITOR_DM_JOB)
			return "", err
		} else {
			err := t.migRepo.UpdateJobByTaskID(ctx, taskId, jobName, t.state.ClusterName, tenantId)
			if err != nil {
				log.Warn(ctx, "update taskId %d jobName %s failed in metaDB %v", taskId, jobName, err)
				return jobName, err
			}
			t.state.JobName = jobName
			t.updateTaskActorState(ctx, model.TaskState_Finished,
				model.TaskAction_DEPLOY_DM_JOB,
				model.TaskAction_MONITOR_DM_JOB)
			return jobName, nil
		}
	default:
		log.Warn(ctx, "createTaskJob with invalid job type:%s", jobType)
		return "", consts.ErrorWithParam(model.ErrorCode_InputParamError, invalidJobTypeError.Error())
	}
}
func (t *TaskActor) deleteTaskJob(ctx types.Context, instanceId string, jobName string, JobType string, tenantId string) {
	taskId := t.state.TaskID
	if t.k8sClient == nil {
		if err := t.checkK8sClient(ctx); err != nil {
			ctx.Respond(shared.DataMigrationFailed{
				Error:        shared.K8sClientInitFailed,
				ErrorMessage: err.Error(),
			})
			return
		}
	}
	switch JobType {
	case PreCheckJob:
		log.Info(ctx, "Delete pre_check job start!")
		t.updateTaskActorState(ctx, model.TaskState_Init,
			model.TaskAction_RECYCLE_PRE_CHECK_JOB,
			model.TaskAction_RENDER_DM_TEMPLATE)
		if err := t.k8sClient.DeleteTaskJob(ctx, jobName); err != nil {
			//告警触发：Warn改为Error
			log.Error(ctx, "Delete instance %s pre_check job %s failed,please contact sre to clean it Manually",
				instanceId, jobName)
			t.updateTaskActorState(ctx, model.TaskState_Init,
				model.TaskAction_RENDER_DM_TEMPLATE,
				model.TaskAction_DEPLOY_DM_JOB)
			return
		} else {
			err := t.migRepo.UpdateJobByTaskID(ctx, taskId, "", t.state.ClusterName, tenantId)
			if err != nil {
				log.Warn(ctx, "update taskId %d jobName %s failed in metaDB %v", taskId, jobName, err)
				return
			}
			t.state.JobName = ""
			t.updateTaskActorState(ctx, model.TaskState_Finished,
				model.TaskAction_RECYCLE_PRE_CHECK_JOB,
				model.TaskAction_RENDER_DM_TEMPLATE)
		}
	case DataMigrationJob:
		log.Info(ctx, "Delete data_migration job start!")
		t.updateTaskActorState(ctx, model.TaskState_Init,
			model.TaskAction_RECYCLE_DM_JOB,
			model.TaskAction_TASK_COMPLETED)
		if err := t.k8sClient.DeleteTaskJob(ctx, jobName); err != nil {
			//告警触发：Warn改为Error
			log.Error(ctx, "Delete instance %s data_migration job %s failed,please contact sre to clean it Manually",
				instanceId, jobName)
			return
		} else {
			err := t.migRepo.UpdateJobByTaskID(ctx, taskId, "", t.state.ClusterName, tenantId)
			if err != nil {
				log.Warn(ctx, "update taskId %d jobName %s failed in metaDB %v", taskId, jobName, err)
				return
			}
			t.state.JobName = ""
		}
		t.updateTaskActorState(ctx, model.TaskState_Finished,
			model.TaskAction_TASK_COMPLETED,
			model.TaskAction_TASK_COMPLETED)
	default:
		log.Warn(ctx, "deleteTaskJob with invalid job type:%s", JobType)
	}
}
func (t *TaskActor) fetchPodLogs(ctx context.Context, jobName string, jobType string, tenantId string) {
	taskId := t.state.TaskID
	switch jobType {
	case PreCheckJob:
		log.Info(ctx, "Fetch pre_check logs start!")
		t.updateTaskActorState(ctx, model.TaskState_Init,
			model.TaskAction_MONITOR_PRE_CHECK_JOB,
			model.TaskAction_RENDER_DM_TEMPLATE)
		state, totalSqlNum := t.k8sClient.FetchPodLogs(ctx, t.CanCtx, jobName, jobType, taskId, 0, tenantId)
		log.Info(ctx, "FetchPodLogs state is %s", state)
		t.state.TotalSqlNum = totalSqlNum
		if state != model.TaskState_Finished.String() {
			log.Warn(ctx, "fetch pre_check logs failed")
			//更新task状态为预检查失败
			if err := t.migRepo.UpdateTaskStatusByTaskID(ctx, t.state.TaskID, uint8(model.DBMigrationStatus_CHECK_FAILED), tenantId); err != nil {
				log.Warn(ctx, "task % set status as %s failed:%v", t.state.TaskID, model.DBMigrationStatus_CHECK_FAILED.String(), err)
				return
			}
			// 获取工单执行失败的原因
			var detail = &entity.MigrationTaskDetail{}
			detail, err := t.migRepo.GetTaskDetail(ctx, taskId, tenantId)
			if err != nil {
				log.Warn(ctx, "get task %v detail failed %v", taskId, err)
			}
			var reason string
			if detail != nil && len(detail.Progress) > 0 {
				reason = detail.Progress[len(detail.Progress)-1].ProgressDetail
			}
			if updateErr := t.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{
				TicketId:     t.state.TicketId,
				TicketStatus: int8(model.TicketStatus_TicketError),
				Description:  fmt.Sprintf("ticket %v task %v error %v", t.state.TicketId, t.state.TaskID, reason),
			}); updateErr != nil {
				log.Warn(ctx, "ticket %v task %v update work status error %v", t.state.TicketId, t.state.TaskID, updateErr)
			}
			//更新操作记录为执行失败
			t.OperateRecordService.UpdateStatusByOperateOrderID(ctx, t.state.TaskID, model.ConsoleOperationStatus_FAILED)
			t.updateTaskActorState(ctx, model.TaskState_Failed,
				model.TaskAction_MONITOR_PRE_CHECK_JOB,
				model.TaskAction_RENDER_DM_TEMPLATE)
		} else {
			if err := t.migRepo.UpdateTaskStatusByTaskID(ctx, t.state.TaskID, uint8(model.DBMigrationStatus_CHECK_PASS), tenantId); err != nil {
				log.Warn(ctx, "task % set status as %s failed:%v", t.state.TaskID, model.DBMigrationStatus_CHECK_PASS.String(), err)
				return
			}
			t.updateTaskActorState(ctx, model.TaskState_Finished,
				model.TaskAction_MONITOR_PRE_CHECK_JOB,
				model.TaskAction_RENDER_DM_TEMPLATE)
			log.Info(ctx, "taskId %d fetch preCheck logs completed!", t.state.TaskID)
		}
	case DataMigrationJob:
		log.Info(ctx, "Fetch data_migration logs start!")
		t.updateTaskActorState(ctx, model.TaskState_Init,
			model.TaskAction_MONITOR_DM_JOB,
			model.TaskAction_RECYCLE_DM_JOB)
		var totalSqlNum int64
		if t.state.TaskType == "Import" {
			totalSqlNum = t.state.TotalSqlNum
		} else if t.state.TaskType == "Export" {
			if t.state.JobConfig.TaskConf.CommonConf.TableList == "" {
				totalSqlNum = -1
			} else {
				totalSqlNum = int64(len(biz_conv.StringToStringSlice(t.state.JobConfig.TaskConf.CommonConf.TableList)))
			}
		}
		state, _ := t.k8sClient.FetchPodLogs(ctx, t.CanCtx, jobName, jobType, taskId, totalSqlNum, tenantId)
		if state != model.TaskState_Finished.String() {
			log.Warn(ctx, "fetch data_migration logs failed")
			t.updateTaskActorState(ctx, model.TaskState_Failed,
				model.TaskAction_MONITOR_DM_JOB,
				model.TaskAction_RECYCLE_DM_JOB)
			//更新task状态为运行失败
			if err := t.migRepo.UpdateTaskStatusByTaskID(ctx, t.state.TaskID, uint8(model.DBMigrationStatus_RUNNING_FAILED), tenantId); err != nil {
				log.Warn(ctx, "task % set status as %s failed:%v", t.state.TaskID, model.DBMigrationStatus_RUNNING_FAILED.String(), err)
				return
			}
			// 获取工单执行失败的原因
			var detail = &entity.MigrationTaskDetail{}
			detail, err := t.migRepo.GetTaskDetail(ctx, taskId, tenantId)
			if err != nil {
				log.Warn(ctx, "get task %v detail failed %v", taskId, err)
			}
			var reason string
			if detail != nil && len(detail.Progress) > 0 {
				reason = detail.Progress[len(detail.Progress)-1].ProgressDetail
			}
			if updateErr := t.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{
				TicketId:     t.state.TicketId,
				TicketStatus: int8(model.TicketStatus_TicketError),
				Description:  fmt.Sprintf("ticket %v task %v error %v", t.state.TicketId, t.state.TaskID, reason),
			}); updateErr != nil {
				log.Warn(ctx, "ticket %v task %v update work status error %v", t.state.TicketId, t.state.TaskID, updateErr)
			}
			//更新操作记录为执行失败
			t.OperateRecordService.UpdateStatusByOperateOrderID(ctx, t.state.TaskID, model.ConsoleOperationStatus_FAILED)
		} else {
			if err := t.migRepo.UpdateTaskStatusByTaskID(ctx, t.state.TaskID, uint8(model.DBMigrationStatus_SUCCESS), tenantId); err != nil {
				log.Warn(ctx, "task % set status as %s failed:%v", t.state.TaskID, model.DBMigrationStatus_SUCCESS.String(), err)
				return
			}
			//更新操作记录为执行成功
			t.OperateRecordService.UpdateStatusByOperateOrderID(ctx, t.state.TaskID, model.ConsoleOperationStatus_SUCCESS)
			//更新任务进度为100%(二次确认)
			if err := t.migRepo.UpdateTaskProgressPtByTaskID(ctx, t.state.TaskID, 100, tenantId); err != nil {
				log.Warn(ctx, "task %d update progressPt 100% failed:%v", t.state.TaskID, err)
				return
			}
			t.updateTaskActorState(ctx, model.TaskState_Finished,
				model.TaskAction_MONITOR_DM_JOB,
				model.TaskAction_RECYCLE_DM_JOB)
			log.Info(ctx, "taskId %d fetch data_migration logs completed!", t.state.TaskID)
		}
	default:
		t.updateTaskActorState(ctx, model.TaskState_Failed,
			model.TaskAction_MONITOR_PRE_CHECK_JOB,
			model.TaskAction_RENDER_DM_TEMPLATE)
	}
}
func (t *TaskActor) updateTaskActorState(ctx context.Context, currentState model.TaskState, currentAction model.TaskAction,
	nextAction model.TaskAction) {
	t.state.CurrentAction = currentAction
	t.state.CurrentState = currentState
	t.state.NextAction = nextAction
	log.Info(ctx, "update TaskActor state as currentAction %s, currentState %s nextAction %s", currentAction.String(), currentState.String(), nextAction.String())
}
