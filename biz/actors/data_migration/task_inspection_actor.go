package data_migration

import (
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	migration_job "code.byted.org/infcs/dbw-mgr/biz/service/data_migration_job"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"go.uber.org/dig"
)

const timeout = 12 * time.Hour

type TaskInspectionActorIn struct {
	dig.In
	Conf    config.ConfigProvider
	C3Cnf   c3.ConfigProvider
	MigRepo repository.MigrationRepo
	K8sPro  migration_job.K8sAPIServerClientProvider
}
type TaskInspectionActor struct {
	state   *TaskInspectionState
	conf    config.ConfigProvider
	c3Cnf   c3.ConfigProvider
	migRepo repository.MigrationRepo
	k8sPro  migration_job.K8sAPIServerClientProvider
}

type TaskInspectionState struct {
	InstanceId string
	JobName    string
	clientMap  map[string]migration_job.K8sAPIServerClient
}

func NewTaskInspectionState(bytes []byte) *TaskInspectionState {
	ts := &TaskInspectionState{}
	ts.clientMap = make(map[string]migration_job.K8sAPIServerClient)
	json.Unmarshal(bytes, ts)
	return ts
}

func NewTaskInspectionActor(p TaskInspectionActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.TaskInspectionActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &TaskInspectionActor{
				state:   NewTaskInspectionState(state),
				conf:    p.Conf,
				c3Cnf:   p.C3Cnf,
				migRepo: p.MigRepo,
				k8sPro:  p.K8sPro,
			}
		}),
	}
}

func (a *TaskInspectionActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(timeout)
	sysCtx := context.WithValue(context.Background(), "biz-context", &fwctx.BizContext{
		LogID: "LogTaskInspectionActor-" + uuid.New().String(),
	})
	ctx = types.BuildMyContext(sysCtx, ctx, nil)
	switch ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "Task Inspection Actor Started")
		ctx.SetReceiveTimeout(timeout)
	case *shared.AreYouOK:
		ctx.Respond(&shared.OK{})
	case *actor.ReceiveTimeout:
		log.Info(ctx, "clean unused job resources")
		a.checkJobStatus(ctx)
		ctx.SetReceiveTimeout(timeout)
	case *actor.Stopping:
		return
	}
}

func (a *TaskInspectionActor) checkJobStatus(ctx types.Context) {
	log.Info(ctx, "Start to check k8s job resources")
	//0. 查询元数据库中已结束的task
	CompletedTaskStatus := []model.DBMigrationStatus{model.DBMigrationStatus_CHECK_FAILED, model.DBMigrationStatus_RUNNING_FAILED, model.DBMigrationStatus_SUCCESS}
	TaskInfos, err := a.migRepo.ListFinishedJobs(ctx, CompletedTaskStatus)
	if err != nil {
		return
	}
	var configMapName string
	//1. 初始化k8sClient
	for _, taskInfo := range TaskInfos.MigrationTasks {
		k8sCli, err := a.checkK8sClient(ctx, taskInfo.ClusterName)
		if err != nil {
			log.Warn(ctx, "InspectionActor init %s k8sClient cluster failed%+v", taskInfo.ClusterName, err)
			continue
		}
		// 查询集群中已结束的job
		jobLists, err := k8sCli.ListTaskJobs(ctx)
		if err != nil {
			log.Warn(ctx, "InspectionActor call k8s ListTaskJobs api failed%+v", err)
			continue
		}
		for _, job := range jobLists.Items {
			if job.Name == taskInfo.JobName {
				//2.删除job
				log.Info(ctx, "TaskInspection start to clean job %s", job.Name)
				if err := k8sCli.DeleteTaskJob(ctx, job.Name); err != nil {
					//告警触发：Warn改为Error
					log.Error(ctx, "TaskInspection clean instance %s job %s failed,p lease contact sre to clean it Manually",
						taskInfo.InstanceID, taskInfo.JobName)
				} else {
					//3.job释放成功，更新元数据库状态
					if err := a.migRepo.UpdateJobByTaskID(ctx, taskInfo.ID, "", taskInfo.ClusterName, taskInfo.TenantID); err != nil {
						log.Warn(ctx, "TaskInspection update taskId %d jobName %s failed in metaDB %v", taskInfo.ID, taskInfo.JobName, err)
					}
				}
			}
		}
		//清除configMap
		configMapName = fmt.Sprintf("dm-cm-%s-%d", taskInfo.InstanceID, taskInfo.ID)
		if err := k8sCli.DeleteConfigMap(ctx, configMapName); err != nil {
			continue
		}
	}
}

func (a *TaskInspectionActor) checkK8sClient(ctx types.Context, clusterName string) (migration_job.K8sAPIServerClient, error) {
	if a.state.clientMap[clusterName] == nil {
		c, err := a.InitK8sClient(ctx, clusterName)
		if err != nil {
			log.Warn(ctx, "init %s k8s client Failed %v", clusterName, err)
			return nil, err
		} else {
			log.Info(ctx, "k8s cluster %s init client succeed", clusterName)
			return c, err
		}
	}
	log.Info(ctx, "cluster %s client is existed", clusterName)
	return a.state.clientMap[clusterName], nil
}

func (a *TaskInspectionActor) InitK8sClient(ctx context.Context, clusterName string) (migration_job.K8sAPIServerClient, error) {
	client, err := a.k8sPro.GetClient(ctx, clusterName)
	if err != nil {
		return nil, err
	}
	a.state.clientMap[clusterName] = client
	return client, nil
}
func (a *TaskInspectionActor) GetState() []byte {
	state, _ := json.Marshal(a.state)
	return state
}
