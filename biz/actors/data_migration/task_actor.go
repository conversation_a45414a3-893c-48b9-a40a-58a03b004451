package data_migration

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"

	"go.uber.org/dig"
	batchv1 "k8s.io/api/batch/v1"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	migration_job "code.byted.org/infcs/dbw-mgr/biz/service/data_migration_job"
	"code.byted.org/infcs/dbw-mgr/biz/service/operate_record"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
)

type TaskActorIn struct {
	dig.In
	Conf                 config.ConfigProvider
	C3Cnf                c3.ConfigProvider
	MigRepo              repository.MigrationRepo
	WorkflowDal          dal.WorkflowDAL
	K8sPro               migration_job.K8sAPIServerClientProvider
	OperateRecordService operate_record.OperateRecordService
}

// NewTaskActor  TODO: taskActor split out importActor and exportActor
func NewTaskActor(p TaskActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.TaskActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &TaskActor{
				state:                newTaskState(state),
				conf:                 p.Conf,
				c3Cnf:                p.C3Cnf,
				migRepo:              p.MigRepo,
				k8sPro:               p.K8sPro,
				workflowDal:          p.WorkflowDal,
				OperateRecordService: p.OperateRecordService,
			}
			return actor
		}),
	}
}

type TaskState struct {
	CurrentState  model.TaskState          `json:"current_state"`
	CurrentAction model.TaskAction         `json:"current_action"`
	NextAction    model.TaskAction         `json:"next_action"`
	InstanceId    string                   `json:"instance_id"`
	TenantID      string                   `json:"tenant_id"`
	JobName       string                   `json:"job_name"`
	TaskID        int64                    `json:"task_id"`
	LogID         string                   `json:"log_id"`
	TaskType      string                   `json:"task_type"`
	JobConfig     *migration_job.JobConfig `json:"job_config"`
	JobTemplate   *batchv1.Job             `json:"job_template"`
	ConfigMapName string                   `json:"config_map_name"`
	ClusterName   string                   `json:"cluster_name"`
	TotalSqlNum   int64                    `json:"total_sql_num"`
	TicketId      int64                    `json:"ticket_id"`
}

type TaskActor struct {
	state                *TaskState
	conf                 config.ConfigProvider
	c3Cnf                c3.ConfigProvider
	migRepo              repository.MigrationRepo
	workflowDal          dal.WorkflowDAL
	k8sClient            migration_job.K8sAPIServerClient
	k8sPro               migration_job.K8sAPIServerClientProvider
	Cancel               context.CancelFunc
	CanCtx               context.Context
	OperateRecordService operate_record.OperateRecordService
}

func newTaskState(bytes []byte) *TaskState {
	ts := &TaskState{}
	json.Unmarshal(bytes, ts)
	return ts
}
func (t *TaskActor) checkK8sClient(ctx types.Context) error {
	if t.k8sClient == nil {
		if err := t.InitK8sClient(ctx, t.state.ClusterName); err != nil {
			log.Warn(ctx, "init k8s client Failed %v", err)
			return consts.ErrorWithParam(model.ErrorCode_InternalError, "init Failed,please retry later")
		}
	}
	log.Info(ctx, "task %d init k8s client succeed", t.state.TaskID)
	return nil
}

func (t *TaskActor) Process(ctx types.Context) {
	if t.state.LogID != "" {
		ctx.WithValue("biz-context", &fwctx.BizContext{
			LogID: t.state.LogID,
		})
	}
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "start actor: %s CurrentAction %s", ctx.GetName(), t.state.CurrentAction.String())
		values := strings.Split(ctx.GetName(), ">")
		if len(values) != 4 {
			e := fmt.Errorf("the length of split of actor name %s expect %d, got %d", ctx.GetName(), 4, len(values))
			log.Warn(ctx, "actorName lengths error: %s", e.Error())
			return
		}
		t.state.InstanceId = values[1]
		t.state.ClusterName = values[2]
		t.state.TaskID = utils.MustStrToInt64(values[3])
		if t.state.NextAction != model.TaskAction_INIT_CONFIGMAP {
			log.Info(ctx, "Start with CurrentAction %s", t.state.CurrentAction.String())
			t.protectUserCall(ctx, func() {
				t.processTaskAction(ctx)
			})
		}
	case *shared.CreateDbExportTaskReq:
		t.protectUserCall(ctx, func() {
			t.state.TaskType = "Export"
			t.state.TenantID = msg.TenantId
			t.state.TicketId = conv.StrToInt64(msg.TicketId, 0)
			t.state.LogID = msg.LogID
			if err := t.checkK8sClient(ctx); err != nil {
				ctx.Respond(shared.DataMigrationFailed{
					Error:        shared.K8sClientInitFailed,
					ErrorMessage: err.Error(),
				})
				ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				return
			}
			t.processCreateDbExportTaskReq(ctx, msg)
			ctx.Send(ctx.Self(), &InitConfigMap{})
		})

	case *shared.CreateDbImportTaskReq:
		t.protectUserCall(ctx, func() {
			t.state.TaskType = "Import"
			t.state.TenantID = msg.TenantId
			t.state.TicketId = conv.StrToInt64(msg.TicketId, 0)
			t.state.LogID = msg.LogID
			if err := t.checkK8sClient(ctx); err != nil {
				ctx.Respond(shared.DataMigrationFailed{
					Error:        shared.K8sClientInitFailed,
					ErrorMessage: err.Error(),
				})
				ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				return
			}
			t.processCreateDbImportTaskReq(ctx, msg)
			ctx.Send(ctx.Self(), &InitConfigMap{})
		})
	case *shared.StopDataMigrationTaskReq:
		t.protectUserCall(ctx, func() {
			log.Info(ctx, "Receive StopDataMigrationTask msg %s", utils.Show(msg))
			if t.state.CurrentAction == model.TaskAction_MONITOR_DM_JOB || t.state.CurrentAction == model.TaskAction_MONITOR_PRE_CHECK_JOB {
				log.Info(ctx, "Send Stop signal to goroutine")
				t.Cancel()
			}
			ctx.Send(ctx.Self(), &actor.Stopped{})
			resp := &shared.StopDataMigrationTaskResp{Success: true}
			//更新task状态为已终止
			if err := t.migRepo.UpdateTaskStatusByTaskID(ctx, msg.TaskId, uint8(model.DBMigrationStatus_RUNNING_FAILED), msg.TenantID); err != nil {
				log.Warn(ctx, "task % set status as %s failed:%v", t.state.TaskID, model.DBMigrationStatus_RUNNING_FAILED.String(), err)
				resp = &shared.StopDataMigrationTaskResp{Success: false}
				ctx.Respond(resp)
				return
			}
			if updateErr := t.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{
				TicketId:     t.state.TicketId,
				TicketStatus: int8(model.TicketStatus_TicketError),
				Description:  "Stop Migration Ticket by User",
			}); updateErr != nil {
				log.Warn(ctx, "ticket %v task %v update work status error %v", t.state.TicketId, t.state.TaskID, updateErr)
			}
			//更新操作记录为执行失败
			t.OperateRecordService.UpdateStatusByOperateOrderID(ctx, t.state.TaskID, model.ConsoleOperationStatus_FAILED)
			ctx.Respond(resp)
		})
	case *InitConfigMap:
		t.protectUserCall(ctx, func() {
			log.Info(ctx, "InitConfigMap Start!")
			tenantId := t.state.TenantID
			if t.state.CurrentState != model.TaskState_Finished {
				req, err := t.migRepo.GetTask(ctx, t.state.TaskID, tenantId)
				if err != nil {
					log.Warn(ctx, "InitConfigMap migRepo GetTask failed:%+v", err)
					ctx.Respond(shared.DataMigrationFailed{
						Error:        shared.TaskNotFound,
						ErrorMessage: err.Error(),
					})
					return
				}
				if t.state.TaskType == "Import" {
					if err := t.initImportConfigMap(ctx, req); err != nil {
						return
					}
				} else if t.state.TaskType == "Export" {
					if err := t.initExportConfigMap(ctx, req); err != nil {
						return
					}
				}
			}
			t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_RENDER_PRE_CHECK_TEMPLATE, model.TaskAction_DEPLOY_PRE_CHECK_JOB)
			//更新任务进度为10%
			if err := t.migRepo.UpdateTaskProgressPtByTaskID(ctx, t.state.TaskID, 10, tenantId); err != nil {
				log.Warn(ctx, "task %d update progressPt 10% failed:%v", t.state.TaskID, err)
			}
			if t.state.TaskType == "Import" {
				ctx.Send(ctx.Self(), &RenderPreCheckTemplate{})
			} else if t.state.TaskType == "Export" {
				ctx.Send(ctx.Self(), &RenderDmTemplate{})
			}
		})

	case *RenderPreCheckTemplate:
		t.protectUserCall(ctx, func() {
			log.Info(ctx, "RenderPreCheckTemplate Start!")
			tenantId := t.state.TenantID
			if t.state.CurrentState != model.TaskState_Finished {
				if t.state.TaskType == "Import" {
					if jobConfig := t.initImportJobConfig(ctx, PreCheckJob); jobConfig == nil {
						log.Warn(ctx, "PreCheckJob initImportJobConfig failed")
						ctx.Send(ctx.Self(), &actor.Stopped{})
					}
				} else if t.state.TaskType == "Export" {
					if jobConfig := t.initExportJobConfig(ctx, PreCheckJob); jobConfig == nil {
						log.Warn(ctx, "PreCheckJob initExportJobConfig failed")
						ctx.Send(ctx.Self(), &actor.Stopped{})
					}
				}
			}
			t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_DEPLOY_PRE_CHECK_JOB, model.TaskAction_MONITOR_PRE_CHECK_JOB)
			//更新task状态为预检查中
			if err := t.migRepo.UpdateTaskStatusByTaskID(ctx, t.state.TaskID, uint8(model.DBMigrationStatus_CHECKING), tenantId); err != nil {
				log.Warn(ctx, "task % set status as %s failed:%v", t.state.TaskID, model.DBMigrationStatus_CHECKING.String(), err)
			}
			ctx.Send(ctx.Self(), &DeployPreCheckJob{})
		})
	case *DeployPreCheckJob:
		t.protectUserCall(ctx, func() {
			log.Info(ctx, "DeployPreCheckJob Start!")
			tenantId := t.state.TenantID
			var jobTemplate *batchv1.Job
			if t.state.CurrentState != model.TaskState_Finished {
				if t.state.JobName != "" {
					t.deleteTaskJob(ctx, t.state.InstanceId, t.state.JobName, PreCheckJob, tenantId)
				}
				if t.state.JobTemplate == nil {
					jobTemplate, _ = migration_job.GenerateJob(t.state.JobConfig, PreCheckJob, t.state.ConfigMapName, t.state.TaskType, conv.Int64ToStr(t.state.TaskID))
				} else {
					jobTemplate = t.state.JobTemplate
				}
				jobName, err := t.createTaskJob(ctx, jobTemplate, PreCheckJob, tenantId)
				if err != nil {
					//更新task状态为预检查失败
					if err := t.migRepo.UpdateTaskStatusByTaskID(ctx, t.state.TaskID, uint8(model.DBMigrationStatus_CHECK_FAILED), tenantId); err != nil {
						log.Warn(ctx, "task % set status as %s failed:%v", t.state.TaskID, model.DBMigrationStatus_CHECK_FAILED.String(), err)
					}
					//更新操作记录为执行失败
					t.OperateRecordService.UpdateStatusByOperateOrderID(ctx, t.state.TaskID, model.ConsoleOperationStatus_FAILED)
					ctx.Send(ctx.Self(), actor.Stopped{})
				} else {
					t.state.JobName = jobName
				}
			}
			t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_MONITOR_PRE_CHECK_JOB, model.TaskAction_RECYCLE_PRE_CHECK_JOB)
			ctx.Send(ctx.Self(), &MonitorPreCheckJob{})
		})
	case *MonitorPreCheckJob:
		t.protectUserCall(ctx, func() {
			log.Info(ctx, "MonitorPreCheckJob Start!")
			tenantId := t.state.TenantID
			if t.state.CurrentState != model.TaskState_Finished {
				cancelCtx, cancel := context.WithCancel(context.Background())
				cancelCtx = context.WithValue(cancelCtx, "biz-context", &fwctx.BizContext{
					LogID: t.state.LogID,
				})
				t.Cancel = cancel
				t.CanCtx = cancelCtx
				err := t.executeMonitorJobLogAction(ctx, PreCheckJob, tenantId)
				if err != nil {
					ctx.Send(ctx.Self(), &actor.Stopped{})
					return
				}
			}
		})
	case *WaitFetchingPreCheckLogs:
		// check task whether completed
		taskInfo, err := t.migRepo.GetTask(ctx, t.state.TaskID, t.state.TenantID)
		if err != nil {
			log.Warn(ctx, " WaitFetchingPreCheckLogs call migRepo GetTask failed:%+v", err)
			ctx.Respond(shared.DataMigrationFailed{
				Error:        shared.TaskNotFound,
				ErrorMessage: err.Error(),
			})
			return
		}
		if model.DBMigrationStatus(taskInfo.Status) == model.DBMigrationStatus_CHECK_FAILED || model.DBMigrationStatus(taskInfo.Status) == model.DBMigrationStatus_CHECK_PASS {
			log.Info(ctx, "fetch preCheck logs completed")
			t.Cancel()
			t.Cancel = nil
			if model.DBMigrationStatus(taskInfo.Status) == model.DBMigrationStatus_CHECK_PASS {
				t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_RECYCLE_PRE_CHECK_JOB, model.TaskAction_RENDER_DM_TEMPLATE)
				ctx.Send(ctx.Self(), &RecyclePreCheckJob{})
			}
			// 预检查失败,任务退出,保留pod
			if model.DBMigrationStatus(taskInfo.Status) == model.DBMigrationStatus_CHECK_FAILED {
				ctx.Send(ctx.Self(), &actor.Stopped{})
			}
		} else {
			ctx.SetReceiveTimeout(10 * time.Second)
		}
	case *RecyclePreCheckJob:
		t.protectUserCall(ctx, func() {
			log.Info(ctx, "RecyclePreCheckJob Start!")
			tenantId := t.state.TenantID
			if t.state.CurrentState != model.TaskState_Finished {
				t.deleteTaskJob(ctx, t.state.InstanceId, t.state.JobName, PreCheckJob, tenantId)
			}
			t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_RENDER_DM_TEMPLATE, model.TaskAction_DEPLOY_DM_JOB)
			//更新任务进度为30%
			if err := t.migRepo.UpdateTaskProgressPtByTaskID(ctx, t.state.TaskID, 30, tenantId); err != nil {
				log.Warn(ctx, "task %d update progressPt 30% failed:%v", t.state.TaskID, err)
			}
			ctx.Send(ctx.Self(), &RenderDmTemplate{})
		})
	case *RenderDmTemplate:
		t.protectUserCall(ctx, func() {
			log.Info(ctx, "RenderDmTemplate Start!")
			tenantId := t.state.TenantID
			if t.state.CurrentState != model.TaskState_Finished {
				if t.state.TaskType == "Import" {
					if jobConfig := t.initImportJobConfig(ctx, DataMigrationJob); jobConfig == nil {
						log.Warn(ctx, "DmJob initImportJobConfig failed")
						ctx.Send(ctx.Self(), &actor.Stopped{})
					}
				} else if t.state.TaskType == "Export" {
					if jobConfig := t.initExportJobConfig(ctx, DataMigrationJob); jobConfig == nil {
						log.Warn(ctx, "DmJob initExportJobConfig failed")
						ctx.Send(ctx.Self(), &actor.Stopped{})
					}
				}
			}
			t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_DEPLOY_DM_JOB, model.TaskAction_MONITOR_DM_JOB)
			//更新task状态为执行中
			if err := t.migRepo.UpdateTaskStatusByTaskID(ctx, t.state.TaskID, uint8(model.DBMigrationStatus_RUNNING), tenantId); err != nil {
				log.Warn(ctx, "task % set status as %s failed:%v", t.state.TaskID, model.DBMigrationStatus_RUNNING.String(), err)
			}
			ctx.Send(ctx.Self(), &DeployDmJob{})
		})
	case *DeployDmJob:
		t.protectUserCall(ctx, func() {
			log.Info(ctx, "DeployDmJob Start!")
			tenantId := t.state.TenantID
			var jobTemplate *batchv1.Job
			if t.state.CurrentState != model.TaskState_Finished {
				if t.state.JobName != "" {
					t.deleteTaskJob(ctx, t.state.InstanceId, t.state.JobName, DataMigrationJob, tenantId)
				}
				jobTemplate, _ = migration_job.GenerateJob(t.state.JobConfig, DataMigrationJob, t.state.ConfigMapName, t.state.TaskType, conv.Int64ToStr(t.state.TaskID))
				jobName, err := t.createTaskJob(ctx, jobTemplate, DataMigrationJob, tenantId)
				if err != nil {
					//更新task状态为执行失败
					if err := t.migRepo.UpdateTaskStatusByTaskID(ctx, t.state.TaskID, uint8(model.DBMigrationStatus_RUNNING_FAILED), tenantId); err != nil {
						log.Warn(ctx, "task % set status as %s failed:%v", t.state.TaskID, model.DBMigrationStatus_RUNNING_FAILED.String(), err)
					}
					if updateErr := t.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{
						TicketId:     t.state.TicketId,
						TicketStatus: int8(model.TicketStatus_TicketError),
						Description:  fmt.Sprintf("ticket %v task %v update work status error %v", t.state.TicketId, t.state.TaskID, err),
					}); updateErr != nil {
						log.Warn(ctx, "ticket %v task %v update work status error %v", t.state.TicketId, t.state.TaskID, updateErr)
					}
					//更新操作记录为执行失败
					t.OperateRecordService.UpdateStatusByOperateOrderID(ctx, t.state.TaskID, model.ConsoleOperationStatus_FAILED)
					ctx.Send(ctx.Self(), actor.Stopped{})
				} else {
					t.state.JobName = jobName
				}
			}
			t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_MONITOR_DM_JOB, model.TaskAction_RECYCLE_DM_JOB)
			//更新任务进度为40%
			if err := t.migRepo.UpdateTaskProgressPtByTaskID(ctx, t.state.TaskID, 40, tenantId); err != nil {
				log.Warn(ctx, "task %d update progressPt 40% failed:%v", t.state.TaskID, err)
			}
			ctx.Send(ctx.Self(), &MonitorDmJob{})
		})
	case *MonitorDmJob:
		t.protectUserCall(ctx, func() {
			log.Info(ctx, "MonitorDmJob Start!")
			tenantId := t.state.TenantID
			if t.state.CurrentState != model.TaskState_Finished {
				cancelCtx, cancel := context.WithCancel(context.Background())
				cancelCtx = context.WithValue(cancelCtx, "biz-context", &fwctx.BizContext{
					LogID: t.state.LogID,
				})
				t.Cancel = cancel
				t.CanCtx = cancelCtx
				err := t.executeMonitorJobLogAction(ctx, DataMigrationJob, tenantId)
				if err != nil {
					ctx.Send(ctx.Self(), &actor.Stopped{})
					return
				}
			}
		})
	case *WaitFetchingDMLogs:
		// check task whether completed
		taskInfo, err := t.migRepo.GetTask(ctx, t.state.TaskID, t.state.TenantID)
		if err != nil {
			log.Warn(ctx, " WaitFetchingDMLogs call migRepo GetTask failed:%+v", err)
			ctx.Respond(shared.DataMigrationFailed{
				Error:        shared.TaskNotFound,
				ErrorMessage: err.Error(),
			})
			return
		}
		if model.DBMigrationStatus(taskInfo.Status) == model.DBMigrationStatus_RUNNING_FAILED || model.DBMigrationStatus(taskInfo.Status) == model.DBMigrationStatus_SUCCESS {
			log.Info(ctx, "fetch DataMigration logs completed")
			t.Cancel()
			t.Cancel = nil
			if model.DBMigrationStatus(taskInfo.Status) == model.DBMigrationStatus_SUCCESS {
				t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_RECYCLE_DM_JOB, model.TaskAction_TASK_COMPLETED)
				ctx.Send(ctx.Self(), &RecycleDmJob{})
			}
			// 任务执行失败,任务退出,保留pod
			if model.DBMigrationStatus(taskInfo.Status) == model.DBMigrationStatus_RUNNING_FAILED {
				ctx.Send(ctx.Self(), &actor.Stopped{})
			}
		} else {
			ctx.SetReceiveTimeout(10 * time.Second)
		}
	case *RecycleDmJob:
		t.protectUserCall(ctx, func() {
			log.Info(ctx, "RecycleDmJob Start!")
			tenantId := t.state.TenantID
			if t.state.CurrentState != model.TaskState_Finished {
				t.deleteTaskJob(ctx, t.state.InstanceId, t.state.JobName, DataMigrationJob, tenantId)
				if err := t.deleteConfigMap(ctx, t.state.ConfigMapName); err != nil {
					log.Warn(ctx, "recycleDmJob delete configMap failed:%+v", err)
				}
			}
			t.updateTaskActorState(ctx, model.TaskState_Init, model.TaskAction_TASK_COMPLETED, model.TaskAction_TASK_COMPLETED)
			ctx.Send(ctx.Self(), &TaskCompleted{})
		})
	case *TaskCompleted:
		log.Info(ctx, "TaskCompleted Start!")
		t.updateTaskActorState(ctx, model.TaskState_Finished, model.TaskAction_TASK_COMPLETED, model.TaskAction_TASK_COMPLETED)
		if err := t.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{
			TicketId:     t.state.TicketId,
			TicketStatus: int8(model.TicketStatus_TicketFinished),
			Description:  "Ticket Execute Success",
		}); err != nil {
			log.Warn(ctx, "ticket %v task %v update work status error %v", t.state.TicketId, t.state.TaskID, err)
		}
		ctx.Send(ctx.Self(), &actor.Stopped{})
	case *actor.Stopped:
		log.Info(ctx, "taskActor %s stop", ctx.GetName())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	case *actor.ReceiveTimeout:
		log.Info(ctx, "Receive Timeout signal")
		switch t.state.CurrentAction {
		case model.TaskAction_MONITOR_PRE_CHECK_JOB:
			ctx.Send(ctx.Self(), &WaitFetchingPreCheckLogs{})
		case model.TaskAction_MONITOR_DM_JOB:
			ctx.Send(ctx.Self(), &WaitFetchingDMLogs{})
		default:
			ctx.SetReceiveTimeout(10 * time.Second)
		}
	}
}

func (t *TaskActor) InitK8sClient(ctx context.Context, clusterName string) error {
	client, err := t.k8sPro.GetClient(ctx, clusterName)
	if err != nil {
		return err
	}
	t.k8sClient = client
	return nil
}
func (t *TaskActor) GetState() []byte {
	state, _ := json.Marshal(t.state)
	return state
}

func (t *TaskActor) processTaskAction(ctx types.Context) {
	switch t.state.CurrentAction {
	case model.TaskAction_INIT_CONFIGMAP:
		ctx.Send(ctx.Self(), &InitConfigMap{})
	case model.TaskAction_RENDER_PRE_CHECK_TEMPLATE:
		ctx.Send(ctx.Self(), &RenderPreCheckTemplate{})
	case model.TaskAction_DEPLOY_PRE_CHECK_JOB:
		ctx.Send(ctx.Self(), &DeployPreCheckJob{})
	case model.TaskAction_MONITOR_PRE_CHECK_JOB:
		ctx.Send(ctx.Self(), &MonitorPreCheckJob{})
	case model.TaskAction_RECYCLE_PRE_CHECK_JOB:
		ctx.Send(ctx.Self(), &RecyclePreCheckJob{})
	case model.TaskAction_RENDER_DM_TEMPLATE:
		ctx.Send(ctx.Self(), &RenderDmTemplate{})
	case model.TaskAction_DEPLOY_DM_JOB:
		ctx.Send(ctx.Self(), &DeployDmJob{})
	case model.TaskAction_MONITOR_DM_JOB:
		ctx.Send(ctx.Self(), &MonitorDmJob{})
	case model.TaskAction_RECYCLE_DM_JOB:
		ctx.Send(ctx.Self(), &RecycleDmJob{})
	case model.TaskAction_TASK_COMPLETED:
		ctx.Send(ctx.Self(), &actor.Stopped{})
	}

}
func (t *TaskActor) executeMonitorJobLogAction(ctx types.Context, jobType string, tenantId string) error {
	if t.k8sClient == nil {
		if err := t.checkK8sClient(ctx); err != nil {
			ctx.Respond(shared.DataMigrationFailed{
				Error:        shared.K8sClientInitFailed,
				ErrorMessage: err.Error(),
			})
			return err
		}
	}
	switch jobType {
	case PreCheckJob:
		go t.fetchPodLogs(ctx, t.state.JobName, PreCheckJob, tenantId)
		ctx.Send(ctx.Self(), &WaitFetchingPreCheckLogs{})
	case DataMigrationJob:
		go t.fetchPodLogs(ctx, t.state.JobName, DataMigrationJob, tenantId)
		ctx.Send(ctx.Self(), &WaitFetchingDMLogs{})
	}
	return nil
}

func (t *TaskActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call TaskActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

type InitConfigMap struct{}
type RenderPreCheckTemplate struct{}
type DeployPreCheckJob struct{}
type MonitorPreCheckJob struct{}
type RecyclePreCheckJob struct{}
type RenderDmTemplate struct{}
type DeployDmJob struct{}
type MonitorDmJob struct{}
type RecycleDmJob struct{}
type TaskCompleted struct{}
type WaitFetchingPreCheckLogs struct{}
type WaitFetchingDMLogs struct{}
