package actors

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

func (a *SessionActor) closeSessionConn(ctx types.Context, connectionID string) {
	a.killQuery(ctx, connectionID)
	a.onConnClosed(ctx, connectionID)

	if a.state.containsConn(connectionID) || a.state.containsDeadConn(connectionID) {
		log.Info(ctx, "send close message to connection %v", connectionID)
		ctx.ClientOf(consts.ConnectionActorKind).
			Send(ctx, a.getConnectionActorName(ctx, connectionID), &shared.CloseConnection{})
	}
}
