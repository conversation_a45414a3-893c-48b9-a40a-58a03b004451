package actors

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/db"
	"code.byted.org/infcs/protoactor-go/actor"
	"errors"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
	"testing"
)

type CCLRuleActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *CCLRuleActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *CCLRuleActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestCCLRuleActorSuite(t *testing.T) {
	suite.Run(t, new(CCLRuleActorSuite))
}

func (suite *CCLRuleActorSuite) TestNewTaskActor() {
	ret := NewCCLRuleActor(CCLRuleActorIn{
		In: dig.In{},
	})
	suite.NotEmpty(ret)
}

func (suite *CCLRuleActorSuite) TestProcessStarted() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	actorName := "ccl_rule_1654469127324041216"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Started{}).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	taskActor := NewCCLRuleActor(CCLRuleActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.CCLRuleActorKind, "CCLRuleActor1", []byte{})
	taskActor.Process(Ctx)
}

func (suite *CCLRuleActorSuite) TestProcessStartedInit() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	actorName := "ccl_rule_1654469127324041216"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Started{}).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	taskActor := NewCCLRuleActor(CCLRuleActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.CCLRuleActorKind, "CCLRuleActor1", []byte{})
	taskActor.Process(Ctx)
}

//func (suite *CCLRuleActorSuite) TestProcessStopped() {
//	Ctx := mocks.NewMockContext(suite.ctrl)
//	Ctx.EXPECT().Message().Return(&actor.Stopped{}).AnyTimes()
//	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
//	actorName := "ccl_rule_1654469127324041216"
//	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
//	taskActor := NewCCLRuleActor(CCLRuleActorIn{
//		In: dig.In{},
//	}).Producer.Spawn(consts.TaskActorKind, "taskActor1", []byte{})
//	taskActor.Process(Ctx)
//}

func (suite *CCLRuleActorSuite) TestCreateSqlConcurrencyControlRuleReq() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	dbProvider.EXPECT().GetMetaDB(gomock.Any()).Return(&db.DB{}).AnyTimes()
	k8sPro := mocks.NewMockK8sAPIServerClientProvider(suite.ctrl)
	k8sCli := mocks.NewMockK8sAPIServerClient(suite.ctrl)
	k8sPro.EXPECT().GetClient(gomock.Any(), gomock.Any()).Return(k8sCli, nil).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Sender().AnyTimes()
	actorName := "VeDBMySQL>vedbm-uz3a79hdoru8>xxxx>2333"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.CreateSqlConcurrencyControlRuleReq{
		TaskId: 11,
	}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SaveState().AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	ruleInfo := &dao.SqlCCLRule{
		InstanceType: model.DSType_VeDBMySQL.String(),
		InstanceID:   "vedbm-uz3a79hdoru8",
		ID:           11,
		ThrottleMode: model.ThrottleMode_DBThrottle.String(),
		Keywords:     "select 11111",
	}
	cclRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		CCLRuleDal:  cclRuleDal,
		DsSvc:       dsSvc,
	}

	dsSvc.EXPECT().AddSQLCCLRule(gomock.Any(), gomock.Any()).Return(&datasource.AddSQLCCLRuleResp{}, nil)
	dsSvc.EXPECT().FlushSQLCCLRule(gomock.Any(), gomock.Any()).Return(&datasource.FlushSQLCCLRuleResp{}, nil)
	dsSvc.EXPECT().ListSQLCCLRules(gomock.Any(), gomock.Any()).Return(&datasource.ListSQLCCLRulesResp{}, nil)
	cclRuleDal.EXPECT().UpdateCCLRuleByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	cclRuleDal.EXPECT().UpdateStatusByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	CCLRuleActor.Process(Ctx)
}

func (suite *CCLRuleActorSuite) TestStopSqlConcurrencyControlRuleReq() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	dbProvider.EXPECT().GetMetaDB(gomock.Any()).Return(&db.DB{}).AnyTimes()
	k8sPro := mocks.NewMockK8sAPIServerClientProvider(suite.ctrl)
	k8sCli := mocks.NewMockK8sAPIServerClient(suite.ctrl)
	k8sPro.EXPECT().GetClient(gomock.Any(), gomock.Any()).Return(k8sCli, nil).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Sender().AnyTimes()
	actorName := "VeDBMySQL>vedbm-uz3a79hdoru8>xxxx>2333"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.StopSqlConcurrencyControlRuleReq{}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SaveState().AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	ruleInfo := &dao.SqlCCLRule{
		InstanceType: model.DSType_MySQL.String(),
		InstanceID:   "mysql-9a798d17dffe",
	}
	cclRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()

	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		CCLRuleDal:  cclRuleDal,
		DsSvc:       dsSvc,
	}

	cclRuleDal.EXPECT().UpdateStatusByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	CCLRuleActor.Process(Ctx)
}

func (suite *CCLRuleActorSuite) TestDeleteSqlConcurrencyControlRuleReq() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	dbProvider.EXPECT().GetMetaDB(gomock.Any()).Return(&db.DB{}).AnyTimes()
	k8sPro := mocks.NewMockK8sAPIServerClientProvider(suite.ctrl)
	k8sCli := mocks.NewMockK8sAPIServerClient(suite.ctrl)
	k8sPro.EXPECT().GetClient(gomock.Any(), gomock.Any()).Return(k8sCli, nil).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Sender().AnyTimes()
	actorName := "VeDBMySQL>vedbm-uz3a79hdoru8>xxxx>2333"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.DeleteSqlConcurrencyControlRuleReq{}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SaveState().AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	ruleInfo := &dao.SqlCCLRule{
		InstanceType: model.DSType_MySQL.String(),
		InstanceID:   "mysql-9a798d17dffe",
	}
	cclRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()

	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		CCLRuleDal:  cclRuleDal,
		DsSvc:       dsSvc,
	}

	cclRuleDal.EXPECT().UpdateStatusByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	CCLRuleActor.Process(Ctx)
}

func (suite *CCLRuleActorSuite) TestCCLShowFail1() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	msg := dao.SqlCCLRule{
		SqlType:          0,
		Keywords:         "select",
		ConcurrencyCount: 2,
		InstanceType:     "MySQL",
		InstanceID:       "mysql-77ca07c2f096",
	}
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		CCLRuleDal:  cclRuleDal,
		DsSvc:       dsSvc,
	}

	dsSvc.EXPECT().ListSQLCCLRules(gomock.Any(), gomock.Any()).Return(&datasource.ListSQLCCLRulesResp{}, nil)
	_, err := CCLRuleActor.CCLShow(Ctx, &msg)
	if err == nil {
		return
	}
}

func (suite *CCLRuleActorSuite) TestCCLShowFail2() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	msg := dao.SqlCCLRule{
		SqlType:          0,
		Keywords:         "select",
		ConcurrencyCount: 2,
		InstanceType:     "MySQL",
		InstanceID:       "mysql-77ca07c2f096",
	}
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		CCLRuleDal:  cclRuleDal,
		DsSvc:       dsSvc,
	}
	dsSvc.EXPECT().ListSQLCCLRules(gomock.Any(), gomock.Any()).Return(&datasource.ListSQLCCLRulesResp{}, nil)
	_, err := CCLRuleActor.CCLShow(Ctx, &msg)
	if err == nil {
		return
	}
}

//func (suite *CCLRuleActorSuite) TestGetPodInfo() {
//	Ctx := mocks.NewMockContext(suite.ctrl)
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	actorCli := mocks.NewMockActorClient(suite.ctrl)
//	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
//	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
//	msg := dao.SqlCCLRule{
//		SqlType:          0,
//		Keywords:         "select",
//		ConcurrencyCount: 2,
//		InstanceType:     "MySQL",
//		InstanceID:       "mysql-77ca07c2f096",
//	}
//	CCLRuleActor := CCLRuleActor{
//		state:       newTaskState([]byte{}),
//		ActorClient: actorCli,
//		CCLRuleDal:  cclRuleDal,
//		DsSvc:       dsSvc,
//	}
//	listInstancePodsResp := &datasource.ListInstancePodsResp{Data: []*shared.KubePod{{Component: "Proxy"}}, Total: 1}
//	dsSvc.EXPECT().ListInstancePods(gomock.Any(), gomock.Any()).Return(listInstancePodsResp, nil)
//	_, err := CCLRuleActor.GetPodInfo(Ctx, &msg)
//	if err == nil {
//		return
//	}
//}

//func (suite *CCLRuleActorSuite) TestRollback() {
//	Ctx := mocks.NewMockContext(suite.ctrl)
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	actorCli := mocks.NewMockActorClient(suite.ctrl)
//	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
//	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
//	kindClient := mocks.NewMockKindClient(suite.ctrl)
//	mockey.Mock(net.Dial).Return(nil, nil).Build()
//	PortsList = []int{3679}
//	msg := dao.SqlCCLRule{
//		SqlType:          0,
//		Keywords:         "select",
//		ConcurrencyCount: 2,
//		InstanceType:     "MySQL",
//		InstanceID:       "mysql-77ca07c2f096",
//	}
//	CCLRuleActor := CCLRuleActor{
//		state:       newTaskState([]byte{}),
//		ActorClient: actorCli,
//		CCLRuleDal:  cclRuleDal,
//		DsSvc:       dsSvc,
//	}
//	pods := []*shared.KubePod{{PodIP: "10.0.0.0"}}
//	cclRule := []*shared.CCLRuleInfo{{Id: "1", Keywords: "select", Type: "SELECT"}}
//
//	kindClient.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).
//		Return(&shared.SessionInfo{
//			SessionId: "session-001",
//		}, nil).Times(1)
//	actorCli.EXPECT().KindOf(consts.SessionMgrActorKind).Return(kindClient)
//
//	kindClient.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).
//		Return(&shared.CCLRuleInfoResp{
//			CCLRule: cclRule,
//		}, nil).Times(1)
//	actorCli.EXPECT().KindOf(consts.SessionActorKind).Return(kindClient)
//
//	kindClient.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).
//		Return(&shared.SessionInfo{
//			SessionId: "session-001",
//		}, nil).Times(1)
//	actorCli.EXPECT().KindOf(consts.SessionMgrActorKind).Return(kindClient)
//
//	kindClient.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).
//		Return(&shared.ExecuteCCLResp{
//			Result: "",
//		}, nil).Times(1)
//	actorCli.EXPECT().KindOf(consts.SessionActorKind).Return(kindClient)
//
//	kindClient.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).
//		Return(&shared.SessionInfo{
//			SessionId: "session-001",
//		}, nil).Times(1)
//	actorCli.EXPECT().KindOf(consts.SessionMgrActorKind).Return(kindClient)
//
//	kindClient.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).
//		Return(&shared.ExecuteCCLResp{
//			Result: "",
//		}, nil).Times(1)
//	actorCli.EXPECT().KindOf(consts.SessionActorKind).Return(kindClient)
//
//	mockey.Mock(handler.GiveBackInstanceSession).Return().Build()
//	err := CCLRuleActor.Rollback(Ctx, pods, &msg)
//	if err == nil {
//		return
//	}
//}

func (suite *CCLRuleActorSuite) TestDeleteRuleByTaskIdsFail1() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		CCLRuleDal:  cclRuleDal,
		DsSvc:       dsSvc,
	}
	tasks := TaskIds{
		ids:      []int64{1658742812360839168},
		tenantId: "2100000746",
	}
	//mockey.Mock(CCLRuleActor.getProxyPodsByTaskId).Return([]*shared.KubePod{{PodIP: "10.0.0.0"}}, nil).Build()
	cclRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, errors.New(`error`)).AnyTimes()
	errCount := CCLRuleActor.DeleteRuleByTaskIds(Ctx, tasks, 2)
	if errCount == 0 {
		return
	}
}

func (suite *CCLRuleActorSuite) TestDescribeRealTimeCCLRulesReq0() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Sender().AnyTimes()
	actorName := "VeDBMySQL>vedbm-uz3a79hdoru8>xxxx>2333"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.DescribeRealTimeCCLRulesReq{ActionType: shared.CclShow}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SaveState().AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	ruleInfo := &dao.SqlCCLRule{
		InstanceType: model.DSType_MySQL.String(),
		InstanceID:   "mysql-9a798d17dffe",
		ThrottleMode: model.ThrottleMode_DBThrottle.String(),
	}
	dsSvc.EXPECT().FlushSQLCCLRule(gomock.Any(), gomock.Any()).Return(&datasource.FlushSQLCCLRuleResp{}, nil)
	dsSvc.EXPECT().ListSQLCCLRules(gomock.Any(), gomock.Any()).Return(&datasource.ListSQLCCLRulesResp{}, nil)
	cclRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		CCLRuleDal:  cclRuleDal,
		DsSvc:       dsSvc,
	}
	CCLRuleActor.Process(Ctx)
}

func (suite *CCLRuleActorSuite) TestDescribeRealTimeCCLRulesReq1() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Sender().AnyTimes()
	actorName := "VeDBMySQL>vedbm-uz3a79hdoru8>xxxx>2333"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.DescribeRealTimeCCLRulesReq{ActionType: shared.CclStop, TaskIds: []int64{111}}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SaveState().AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	ruleInfo := &dao.SqlCCLRule{
		InstanceType: model.DSType_MySQL.String(),
		InstanceID:   "mysql-9a798d17dffe",
		ThrottleMode: model.ThrottleMode_DBThrottle.String(),
	}
	cclRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	cclRuleDal.EXPECT().UpdateStatusTimeByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		CCLRuleDal:  cclRuleDal,
		DsSvc:       dsSvc,
	}
	dsSvc.EXPECT().FlushSQLCCLRule(gomock.Any(), gomock.Any()).Return(&datasource.FlushSQLCCLRuleResp{}, nil).AnyTimes()
	dsSvc.EXPECT().DeleteSQLCCLRule(gomock.Any(), gomock.Any()).Return(&datasource.DeleteSQLCCLRuleResp{}, nil)
	CCLRuleActor.Process(Ctx)
}
func (suite *CCLRuleActorSuite) TestDescribeRealTimeCCLRulesReq2() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Sender().AnyTimes()
	actorName := "VeDBMySQL>vedbm-uz3a79hdoru8>xxxx>2333"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.DescribeRealTimeCCLRulesReq{ActionType: shared.CclDelete, TaskIds: []int64{111}}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SaveState().AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	ruleInfo := &dao.SqlCCLRule{
		InstanceType: model.DSType_MySQL.String(),
		InstanceID:   "mysql-9a798d17dffe",
		ThrottleMode: model.ThrottleMode_DBThrottle.String(),
	}
	cclRuleDal.EXPECT().GetDeletedRule(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	cclRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	cclRuleDal.EXPECT().Delete(gomock.Any(), gomock.Any()).Return(nil)
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		CCLRuleDal:  cclRuleDal,
		DsSvc:       dsSvc,
	}
	dsSvc.EXPECT().DeleteSQLCCLRule(gomock.Any(), gomock.Any()).Return(&datasource.DeleteSQLCCLRuleResp{}, nil)
	dsSvc.EXPECT().FlushSQLCCLRule(gomock.Any(), gomock.Any()).Return(&datasource.FlushSQLCCLRuleResp{}, nil).AnyTimes()
	CCLRuleActor.Process(Ctx)
}
func (suite *CCLRuleActorSuite) TestDescribeRealTimeCCLRulesReq3() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Sender().AnyTimes()
	actorName := "VeDBMySQL>vedbm-uz3a79hdoru8>xxxx>2333"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.DescribeRealTimeCCLRulesReq{ActionType: shared.CclAdd, TaskId: 111}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SaveState().AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	cclRuleDal := mocks.NewMockSqlCCLRulesDAL(suite.ctrl)
	ruleInfo := &dao.SqlCCLRule{
		InstanceType:   model.DSType_VeDBMySQL.String(),
		InstanceID:     "vedbm-uz3a79hdoru8",
		ThrottleMode:   model.ThrottleMode_DBThrottle.String(),
		ID:             111,
		ThrottleTarget: model.ThrottleTarget_MaxConcurrencyCount.String(),
		Keywords:       "select 11111",
	}
	cclRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		CCLRuleDal:  cclRuleDal,
		DsSvc:       dsSvc,
	}
	dsSvc.EXPECT().AddSQLCCLRule(gomock.Any(), gomock.Any()).Return(&datasource.AddSQLCCLRuleResp{}, nil)
	dsSvc.EXPECT().ListSQLCCLRules(gomock.Any(), gomock.Any()).Return(&datasource.ListSQLCCLRulesResp{}, nil)
	dsSvc.EXPECT().FlushSQLCCLRule(gomock.Any(), gomock.Any()).Return(&datasource.FlushSQLCCLRuleResp{}, nil).AnyTimes()
	cclRuleDal.EXPECT().UpdateCCLRuleByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	CCLRuleActor.Process(Ctx)
}

func (suite *CCLRuleActorSuite) TestCreateSqlKillRuleReq() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	dbProvider.EXPECT().GetMetaDB(gomock.Any()).Return(&db.DB{}).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Sender().AnyTimes()
	actorName := "VeDBMySQL>vedbm-uz3a79hdoru8>xxxx>2333"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.CreateSqlKillRuleReq{}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SaveState().AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	killRuleDal := mocks.NewMockSqlKillRulesDAL(suite.ctrl)
	ruleInfo := &dao.SqlKillRule{
		InstanceType:   model.DSType_MySQL.String(),
		InstanceID:     "mysql-9a798d17dffe",
		ProtectedUsers: "test1,test2",
		Duration:       20,
	}
	killRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		KillRuleDal: killRuleDal,
		DsSvc:       dsSvc,
	}

	dsSvc.EXPECT().ModifySQLKillRule(gomock.Any(), gomock.Any()).Return(&datasource.ModifySQLKillRuleResp{}, nil)
	killRuleDal.EXPECT().UpdateStatusByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	dsSvc.EXPECT().DescribeDBInstanceEndpoints(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDBInstanceEndpointsResp{
		Endpoints: []*datasource.EndpointInfo{
			{
				EndpointID:   "ed-0",
				EndpointType: model.EndpointType_ReadWrite.String(),
				EndpointName: "main-0",
			},
		},
	}, nil)
	CCLRuleActor.Process(Ctx)
}

func (suite *CCLRuleActorSuite) TestStopSqlKillRuleReq() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	dbProvider.EXPECT().GetMetaDB(gomock.Any()).Return(&db.DB{}).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Sender().AnyTimes()
	actorName := "VeDBMySQL>vedbm-uz3a79hdoru8>xxxx>2333"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.StopSqlKillRuleReq{TaskIds: []int64{111, 222}}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SaveState().AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	killRuleDal := mocks.NewMockSqlKillRulesDAL(suite.ctrl)
	ruleInfo := &dao.SqlKillRule{
		InstanceType:   model.DSType_MySQL.String(),
		InstanceID:     "mysql-9a798d17dffe",
		ProtectedUsers: "test1,test2",
		Duration:       20,
	}
	killRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		KillRuleDal: killRuleDal,
		DsSvc:       dsSvc,
	}

	dsSvc.EXPECT().ModifySQLKillRule(gomock.Any(), gomock.Any()).Return(&datasource.ModifySQLKillRuleResp{}, nil)
	dsSvc.EXPECT().DescribeDBInstanceEndpoints(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDBInstanceEndpointsResp{
		Endpoints: []*datasource.EndpointInfo{
			{
				EndpointID:   "ed-0",
				EndpointType: model.EndpointType_ReadWrite.String(),
				EndpointName: "main-0",
			},
		},
	}, nil)
	killRuleDal.EXPECT().UpdateStatusByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	CCLRuleActor.Process(Ctx)
}

func (suite *CCLRuleActorSuite) TestDeleteSqlKillRuleReq() {
	Ctx := mocks.NewMockContext(suite.ctrl)
	dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	dbProvider.EXPECT().GetMetaDB(gomock.Any()).Return(&db.DB{}).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	Ctx.EXPECT().Sender().AnyTimes()
	actorName := "VeDBMySQL>vedbm-uz3a79hdoru8>xxxx>2333"
	Ctx.EXPECT().GetName().Return(actorName).AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	Ctx.EXPECT().Message().Return(&shared.DeleteSqlKillRuleReq{TaskIds: []int64{111, 222}}).AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SaveState().AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
	actorCli := mocks.NewMockActorClient(suite.ctrl)
	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
	killRuleDal := mocks.NewMockSqlKillRulesDAL(suite.ctrl)
	ruleInfo := &dao.SqlKillRule{
		InstanceType:   model.DSType_MySQL.String(),
		InstanceID:     "mysql-9a798d17dffe",
		ProtectedUsers: "test1,test2",
		Duration:       20,
	}
	killRuleDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ruleInfo, nil).AnyTimes()
	CCLRuleActor := CCLRuleActor{
		state:       newTaskState([]byte{}),
		ActorClient: actorCli,
		KillRuleDal: killRuleDal,
		DsSvc:       dsSvc,
	}

	dsSvc.EXPECT().ModifySQLKillRule(gomock.Any(), gomock.Any()).Return(&datasource.ModifySQLKillRuleResp{}, nil)
	dsSvc.EXPECT().DescribeDBInstanceEndpoints(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDBInstanceEndpointsResp{
		Endpoints: []*datasource.EndpointInfo{
			{
				EndpointID:   "ed-0",
				EndpointType: model.EndpointType_ReadWrite.String(),
				EndpointName: "main-0",
			},
		},
	}, nil)
	killRuleDal.EXPECT().Delete(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	CCLRuleActor.Process(Ctx)
}

func Test_trimKeyword(t *testing.T) {
	mockey.PatchConvey("Test trimKeyword with empty input", t, func() {
		keywords := ""
		result := trimKeyword(keywords)
		So(result, ShouldEqual, "")
	})

	mockey.PatchConvey("Test trimKeyword with single keyword", t, func() {
		keywords := "  keyword1  "
		result := trimKeyword(keywords)
		So(result, ShouldEqual, "keyword1")
	})

	mockey.PatchConvey("Test trimKeyword with multiple keywords", t, func() {
		keywords := "  keyword1  ,  keyword2  ,  keyword3  "
		result := trimKeyword(keywords)
		So(result, ShouldEqual, "keyword1,keyword2,keyword3")
	})

	mockey.PatchConvey("Test trimKeyword with empty keywords in between", t, func() {
		keywords := "  keyword1  ,  ,  keyword3  "
		result := trimKeyword(keywords)
		So(result, ShouldEqual, "keyword1,keyword3")
	})

	mockey.PatchConvey("Test trimKeyword with all empty keywords", t, func() {
		keywords := "  ,  ,  "
		result := trimKeyword(keywords)
		So(result, ShouldEqual, "")
	})

	mockey.PatchConvey("Test trimKeyword with leading and trailing commas", t, func() {
		keywords := ",  keyword1  ,  keyword2  ,"
		result := trimKeyword(keywords)
		So(result, ShouldEqual, "keyword1,keyword2")
	})

	mockey.PatchConvey("Test trimKeyword with mixed spaces and commas", t, func() {
		keywords := "  keyword1  ,,  keyword2  ,  ,  keyword3  "
		result := trimKeyword(keywords)
		So(result, ShouldEqual, "keyword1,keyword2,keyword3")
	})
}
