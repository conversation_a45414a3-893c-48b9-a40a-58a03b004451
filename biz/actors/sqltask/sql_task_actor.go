package sqltask

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"fmt"
	"go.uber.org/dig"
	"gorm.io/gorm"
	"strings"
	"time"
)

const (
	timeout = 5 * time.Second
)

type SqlTaskActorIn struct {
	dig.In
	Source     datasource.DataSourceService
	SqlTaskDal dal.SqlTask
	TicketDal  dal.WorkflowDAL
}

type SqlTaskActor struct {
	state *TaskState

	Source     datasource.DataSourceService
	SqlTaskDAL dal.SqlTask
	ticketDAL  dal.WorkflowDAL
}

type TaskState struct{}

func NewSqlTaskActor(t SqlTaskActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.SqlTaskActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			actor := &SqlTaskActor{
				state:      newTaskState(state),
				Source:     t.Source,
				SqlTaskDAL: t.SqlTaskDal,
				ticketDAL:  t.TicketDal,
			}
			return actor
		}),
	}
}

func newTaskState(bytes []byte) *TaskState {
	ts := &TaskState{}
	json.Unmarshal(bytes, ts)
	return ts
}

func (t SqlTaskActor) GetState() []byte {
	state, _ := json.Marshal(t.state)
	return state
}

type CheckSqlTask struct {
}

func (t SqlTaskActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(timeout)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
	case *shared.AreYouOK:
		ctx.Respond(&shared.OK{})
		t.checkSqlTask(ctx)
	case *shared.GetDDLTaskStatusReq:
		t.getDDLTaskStatus(ctx, msg)
	case *shared.StopDDLTaskReq:
		t.stopDDLTask(ctx, msg)
	case *actor.ReceiveTimeout:
		t.checkSqlTask(ctx)
	case *actor.Stopped:
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}

func (t SqlTaskActor) getDDLTaskStatus(ctx types.Context, msg *shared.GetDDLTaskStatusReq) {
	values := strings.Split(ctx.GetName(), consts.ActorSplitSymbol)
	if len(values) != 3 {
		log.Error(ctx, "actor name error, %s", fmt.Errorf("the length of split of actor name %s expect %d, got %d", ctx.GetName(), 3, len(values)))
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	sqlTask, err := t.SqlTaskDAL.SelectBySqlTaskId(ctx, dao.SqlTask{
		SqlTaskId: msg.SqlTaskId,
		TenantId:  msg.TenantId,
	}, fwctx.GetTenantID(ctx), fwctx.GetUserID(ctx))
	if err != nil {
		log.Warn(ctx, "get sqlTask err:%s", err.Error())
		ctx.Respond(&shared.GetDDLTaskStatusErr{
			Message: err.Error(),
		})
		if err == gorm.ErrRecordNotFound {
			log.Error(ctx, "unknown sql task record: %v", values)
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		}
		return
	}
	ctx.Respond(&shared.GetDDLTaskStatusResp{
		SqlTaskId:     sqlTask.SqlTaskId,
		InstanceId:    sqlTask.InstanceId,
		SqlTaskStatus: sqlTask.SqlTaskStatus,
		Progress:      sqlTask.Progress,
		TenantId:      sqlTask.TenantId,
		Result:        sqlTask.Result,
	})
	return
}

func (t SqlTaskActor) stopDDLTask(ctx types.Context, msg *shared.StopDDLTaskReq) {
	log.Info(ctx, "stopDDLTask: sqlTaskActor receive msg:%s", utils.Show(msg))
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: msg.TenantId,
	})
	sqlTask, err := t.SqlTaskDAL.SelectBySqlTaskId(ctx, dao.SqlTask{
		SqlTaskId: msg.SqlTaskId,
		TenantId:  msg.TenantId,
	}, fwctx.GetTenantID(ctx), fwctx.GetUserID(ctx))
	if err != nil {
		log.Warn(ctx, "stopDDLTask: get sql task error: %s", err)
		if err == gorm.ErrRecordNotFound {
			log.Error(ctx, "stopDDLTask: unknown sql task record: %s", ctx.GetName())
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		}
		ctx.SetReceiveTimeout(timeout)
		return
	}
	switch sqlTask.SqlTaskType {
	case model.SqlTaskType_OnlineDDL.String():
		log.Info(ctx, "stopDDLTask: stop free lock ddl task,%v", sqlTask)
		t.StopFreeLockCorrectOrder(ctx, sqlTask)
	default:
		log.Error(ctx, "stopDDLTask: unknown sql task type: %s, %+v ", sqlTask.SqlTaskType, sqlTask)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}

func (t SqlTaskActor) StopFreeLockCorrectOrder(ctx types.Context, sqlTask *dao.SqlTask) {
	err := t.Source.StopFreeLockCorrectOrders(ctx, &datasource.StopFreeLockCorrectOrdersReq{
		InstanceType: shared.DataSourceType(shared.DataSourceType_value[sqlTask.InstanceType]),
		InstanceId:   sqlTask.InstanceId,
		OrderId:      sqlTask.OrderId,
	})
	if err != nil {
		log.Warn(ctx, "StopFreeLockCorrectOrders error %s", err.Error())
		ctx.Respond(&shared.StopDDLTaskResp{
			SqlTaskId:  sqlTask.SqlTaskId,
			ErrMessage: "stop sql task failed",
			Status:     consts.StatusFailed,
		})
		return
	}
	log.Info(ctx, "stopDDLTask: stop free lock ddl success,task id is %v", sqlTask.SqlTaskId)
	ctx.Respond(&shared.StopDDLTaskResp{
		SqlTaskId:  sqlTask.SqlTaskId,
		ErrMessage: "stop sql task success",
		Status:     consts.StatusSuccess,
	})

	// 调用get方法获取rds接口返回结果并更新dbw_sql_task表元信息
	t.GetFreeLockDDLOrderStatus(ctx, sqlTask.InstanceId, sqlTask.OrderId, sqlTask)
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	return
}

func (t SqlTaskActor) checkSqlTask(ctx types.Context) {
	values := strings.Split(ctx.GetName(), consts.ActorSplitSymbol)
	if len(values) != 3 {
		log.Error(ctx, "actor name error,the length of actor name %s expect %d, got %d", ctx.GetName(), 3, len(values))
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	TenantId := values[0]
	InstanceId := values[1]
	SqlTaskId := values[2]

	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: TenantId,
	})
	sqlTask, err := t.SqlTaskDAL.SelectBySqlTaskId(ctx, dao.SqlTask{
		SqlTaskId: SqlTaskId,
		TenantId:  TenantId,
	}, fwctx.GetTenantID(ctx), fwctx.GetUserID(ctx))
	if err != nil {
		log.Warn(ctx, "SelectBySqlTaskId error: %s", err)
		if err == gorm.ErrRecordNotFound {
			log.Error(ctx, "unknown sql task record: %s", values)
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		}
		return
	}
	orderId := sqlTask.OrderId
	switch sqlTask.SqlTaskType {
	case model.SqlTaskType_OnlineDDL.String():
		log.Info(ctx, "get free lock ddl pull,%s", ctx.GetName())
		t.GetFreeLockDDLOrderStatus(ctx, InstanceId, orderId, sqlTask)
	case model.SqlTaskType_FreeLockDML.String():
		log.Info(ctx, "get free lock dml pull,%s", ctx.GetName())
		t.GetFreeLockDMLOrderStatus(ctx, sqlTask)
	default:
		log.Error(ctx, "unknown sql task type: %s, %+v ", sqlTask.SqlTaskType, sqlTask)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}

func (t SqlTaskActor) GetFreeLockDDLOrderStatus(ctx types.Context, InstanceId string, OrderId string, sqlTask *dao.SqlTask) {
	req := &datasource.DescribeFreeLockCorrectOrdersReq{
		Type:       shared.DataSourceType(shared.DataSourceType_value[sqlTask.InstanceType]),
		InstanceId: InstanceId,
		OrderId:    OrderId,
		PageNumber: 1,
		PageSize:   10,
	}
	orders, err := t.Source.DescribeFreeLockCorrectOrders(ctx, req)
	if err != nil {
		log.Warn(ctx, "sql task %v DescribeFreeLockCorrectOrders error %s", ctx.GetName(), err.Error())
		return
	}
	log.Info(ctx, "sql task %v get online ddl pull result is %v", ctx.GetName(), orders.Total)
	if len(orders.Datas) > 0 {
		odata := orders.Datas[0]
		log.Info(ctx, "sql task %v get sql task result is %#v,%v", ctx.GetName(), orders.Datas[0], odata.GetSqlTaskStatus())
		switch odata.GetSqlTaskStatus() {
		case model.SqlTaskStatus_Init:
			fallthrough
		case model.SqlTaskStatus_Pending:
			fallthrough
		case model.SqlTaskStatus_Precheck:
			fallthrough
		case model.SqlTaskStatus_Pause:
			fallthrough
		case model.SqlTaskStatus_Running:
			t.UpdateTask(ctx, sqlTask, odata)
			return
		case model.SqlTaskStatus_Failed:
			fallthrough
		case model.SqlTaskStatus_Success:
			fallthrough
		case model.SqlTaskStatus_Stop:
			err := t.UpdateTask(ctx, sqlTask, odata)
			if err != nil {
				log.Info(ctx, "sql task %v UpdateTask error", ctx.GetName(), err.Error())
				return
			}
			time.Sleep(4 * timeout) // 这里是保证能ticket_actor访问到结果之后,再清理sql_task_actor
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		default:
			log.Error(ctx, "sql task %v unknown order status %s", ctx.GetName(), orders.Datas[0].GetSqlTaskStatus())
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
	}
}

func (t SqlTaskActor) GetFreeLockDMLOrderStatus(ctx types.Context, sqlTask *dao.SqlTask) {
	ticket, err := t.ticketDAL.GetTicketByTaskID(ctx, sqlTask.TenantId, utils.MustStrToInt64(sqlTask.SqlTaskId))
	if err != nil {
		log.Warn(ctx, "sql task %v get ticket by task id error: %v", ctx.GetName(), err)
		return
	}
	currentBatchRows, _ := GetTicketAffectRows(ticket.AffectedRows)
	log.Info(ctx, "sql task %v get ticket rows is %v", ctx.GetName(), currentBatchRows)
	/* sqlTask的状态机
	//model.SqlTaskStatus_Init:
	//model.SqlTaskStatus_Pending:
	//model.SqlTaskStatus_Precheck:
	//model.SqlTaskStatus_Pause:
	//model.SqlTaskStatus_Running:
	//model.SqlTaskStatus_Failed:
	//model.SqlTaskStatus_Success:
	//model.SqlTaskStatus_Stop:
	*/
	switch ticket.TicketStatus {
	case int8(model.TicketStatus_TicketWaitExecute): // 等待执行,继续等待
		log.Info(ctx, "ticket %v is waiting execute", ticket.TicketId)
		sqlTask.SqlTaskStatus = model.SqlTaskStatus_Pending.String()
		t.SqlTaskDAL.UpdateSqlTask(ctx, *sqlTask)
		return
	case int8(model.TicketStatus_TicketExecute): // 执行中,更新进度
		log.Info(ctx, "ticket %v is  executing", ticket.TicketId)
		sqlTask.SqlTaskStatus = model.SqlTaskStatus_Running.String()
		sqlTask.Progress = ticket.Progress
		sqlTask.FinishTime = int32(time.Now().Unix())
		sqlTask.Result = ticket.Description
		sqlTask.RunningInfo = ticket.Description
		sqlTask.AffectedRows = int32(currentBatchRows)
		t.SqlTaskDAL.UpdateSqlTask(ctx, *sqlTask)
		return
	case int8(model.TicketStatus_TicketFinished): // 执行完成,更新进度和影响行数
		log.Info(ctx, "ticket %v is finished", ticket.TicketId)
		sqlTask.SqlTaskStatus = model.SqlTaskStatus_Success.String()
		sqlTask.Progress = ticket.Progress
		sqlTask.FinishTime = int32(time.Now().Unix())
		// TODO 这里更新影响行数
		sqlTask.Result = ticket.Description
		sqlTask.RunningInfo = ticket.Description
		sqlTask.AffectedRows = int32(currentBatchRows)
		t.SqlTaskDAL.UpdateSqlTask(ctx, *sqlTask)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	case int8(model.TicketStatus_TicketError): // 执行失败,更新进度和失败原因
		log.Warn(ctx, "ticket %v execute error,%v", ticket.TicketId, ticket.Description)
		sqlTask.SqlTaskStatus = model.SqlTaskStatus_Failed.String()
		sqlTask.Progress = ticket.Progress
		sqlTask.FinishTime = int32(time.Now().Unix())
		sqlTask.Result = ticket.Description
		t.SqlTaskDAL.UpdateSqlTask(ctx, *sqlTask)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	default:
		log.Warn(ctx, "ticket %v wrong ticket status: %v", ticket.TicketId, ticket.TicketStatus)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
}

func (t SqlTaskActor) UpdateTask(ctx types.Context, sqlTask *dao.SqlTask, odata *model.SqlTask) error {
	SqlTaskModelToDao(sqlTask, odata)
	log.Info(ctx, "update task is %#v", sqlTask)
	err := t.SqlTaskDAL.UpdateByOrderId(ctx, *sqlTask, fwctx.GetTenantID(ctx), fwctx.GetUserID(ctx))
	if err != nil {
		log.Error(ctx, "update online ddl fail, %s", err)
	}
	return err
}

func SqlTaskModelToDao(sqlTask *dao.SqlTask, odata *model.SqlTask) {
	sqlTask.Comment = odata.GetComment()
	sqlTask.Progress = odata.GetProgress()
	sqlTask.TbName = odata.GetTableName()
	sqlTask.DbName = odata.GetDBName()
	sqlTask.Result = odata.GetResult_()
	sqlTask.ExecuteTime = odata.CreateTime
	sqlTask.FinishTime = odata.FinishTime
	sqlTask.SqlTaskStatus = odata.GetSqlTaskStatus().String()
	sqlTask.RunningInfo = odata.RunningInfo
}
