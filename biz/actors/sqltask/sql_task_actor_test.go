package sqltask

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	local_dal "code.byted.org/infcs/dbw-mgr/biz/test/dal"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	mockdal "code.byted.org/infcs/dbw-mgr/biz/test/mocks/dal"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"fmt"
	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"reflect"
	"testing"
)

type OnlineDdlActorTestSuiteTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *OnlineDdlActorTestSuiteTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}
func (suite *OnlineDdlActorTestSuiteTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}
func TestOnlineDdlActorTestSuiteSuite(t *testing.T) {
	suite.Run(t, new(OnlineDdlActorTestSuiteTestSuite))
}

func TestNewSqlTaskActor(t *testing.T) {
	type args struct {
		t SqlTaskActorIn
	}
	tests := []struct {
		name string
		args args
		want types.VirtualPersistenceProducer
	}{
		{
			name: "1",
			args: args{},
			want: types.VirtualPersistenceProducer{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewSqlTaskActor(tt.args.t); got.Kind != consts.SqlTaskActorKind {
				t.Errorf("NewSqlTaskActor() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTaskActor_GetState(t1 *testing.T) {
	type fields struct {
		state  *TaskState
		Source datasource.DataSourceService
		DdlDAL dal.SqlTask
	}
	tests := []struct {
		name   string
		fields fields
		want   []byte
	}{
		{
			name: "1",
			fields: fields{
				state:  &TaskState{},
				Source: nil,
				DdlDAL: nil,
			},
			want: []byte("{}"),
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := SqlTaskActor{
				state:      tt.fields.state,
				Source:     tt.fields.Source,
				SqlTaskDAL: tt.fields.DdlDAL,
			}
			if got := t.GetState(); !reflect.DeepEqual(got, tt.want) {
				t1.Errorf("GetState() = %v, want %v", got, tt.want)
			}
		})
	}
}

func (suite *OnlineDdlActorTestSuiteTestSuite) TestTaskActor_Process() {
	type fields struct {
		state  *TaskState
		Source datasource.DataSourceService
		DdlDAL dal.SqlTask
	}
	type args struct {
		ctx types.Context
	}
	local := local_dal.LocalAudit{}
	local.Init(context.Background(), suite.ctrl)

	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "",
			fields: fields{
				state:  &TaskState{},
				Source: MockDS(suite.ctrl),
				DdlDAL: local.DdlDAL,
			},
			args: args{
				ctx: actorCtxMock(suite.ctrl),
			},
		},
	}
	PatchConvey("", suite.T(), func() {
		for _, tt := range tests {
			suite.T().Run(tt.name, func(t1 *testing.T) {
				t := SqlTaskActor{
					state:      tt.fields.state,
					Source:     tt.fields.Source,
					SqlTaskDAL: tt.fields.DdlDAL,
				}
				t.Process(tt.args.ctx)
			})
		}
	})
}

func actorCtxMock(ctrl *gomock.Controller) *mocks.MockContext {
	ctxPushMeaReq := mocks.NewMockContext(ctrl)

	ctxPushMeaReq.EXPECT().Value(gomock.Any()).AnyTimes()
	ctxPushMeaReq.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	ctxPushMeaReq.EXPECT().Message().Return(&shared.AreYouOK{}).AnyTimes()
	ctxPushMeaReq.EXPECT().Self().Return(&actor.PID{Address: "1", Id: "1"}).AnyTimes()
	ctxPushMeaReq.EXPECT().Respond(gomock.Any()).Return().AnyTimes()
	ctxPushMeaReq.EXPECT().Send(gomock.Any(), gomock.Any()).Return().AnyTimes()
	ctxPushMeaReq.EXPECT().Sender().Return(&actor.PID{}).AnyTimes()
	ctxPushMeaReq.EXPECT().GetName().Return("1|1|1").AnyTimes()
	ctxPushMeaReq.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	return ctxPushMeaReq
}

func actorCtxMockErr(ctrl *gomock.Controller) *mocks.MockContext {
	ctxPushMeaReq := mocks.NewMockContext(ctrl)

	ctxPushMeaReq.EXPECT().Value(gomock.Any()).AnyTimes()
	ctxPushMeaReq.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	ctxPushMeaReq.EXPECT().Message().Return(&shared.AreYouOK{}).AnyTimes()
	ctxPushMeaReq.EXPECT().Self().Return(&actor.PID{Address: "1", Id: "1"}).AnyTimes()
	ctxPushMeaReq.EXPECT().Respond(gomock.Any()).Return().AnyTimes()
	ctxPushMeaReq.EXPECT().Send(gomock.Any(), gomock.Any()).Return().AnyTimes()
	ctxPushMeaReq.EXPECT().Sender().Return(&actor.PID{}).AnyTimes()
	ctxPushMeaReq.EXPECT().GetName().Return("1|1").AnyTimes()
	ctxPushMeaReq.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	return ctxPushMeaReq
}

func (suite *OnlineDdlActorTestSuiteTestSuite) TestTaskActor_checkDdlTask() {
	type fields struct {
		state  *TaskState
		Source datasource.DataSourceService
		DdlDAL dal.SqlTask
	}

	local := local_dal.LocalAudit{}
	local.Init(context.Background(), suite.ctrl)
	type args struct {
		ctx types.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "1",
			fields: fields{
				state:  &TaskState{},
				Source: MockDS(suite.ctrl),
				DdlDAL: local.DdlDAL,
			},
			args: args{
				ctx: actorCtxMock(suite.ctrl),
			},
		},
	}
	for _, tt := range tests {
		PatchConvey("", suite.T(), func() {
			t := SqlTaskActor{
				state:      tt.fields.state,
				Source:     tt.fields.Source,
				SqlTaskDAL: tt.fields.DdlDAL,
			}
			t.checkSqlTask(tt.args.ctx)
		})
	}
}

func (suite *OnlineDdlActorTestSuiteTestSuite) TestTaskActor_GetDDLTaskStatus() {
	type fields struct {
		state  *TaskState
		Source datasource.DataSourceService
		DdlDAL dal.SqlTask
	}

	local := local_dal.LocalAudit{}
	local.Init(context.Background(), suite.ctrl)
	type args struct {
		ctx types.Context
		msg *shared.GetDDLTaskStatusReq
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "1",
			fields: fields{
				state:  &TaskState{},
				Source: MockDS(suite.ctrl),
				DdlDAL: local.DdlDAL,
			},
			args: args{
				ctx: actorCtxMock(suite.ctrl),
				msg: &shared.GetDDLTaskStatusReq{
					SqlTaskId: "1",
					TenantId:  "1",
				},
			},
		},
	}
	for _, tt := range tests {
		PatchConvey("", suite.T(), func() {
			t := SqlTaskActor{
				state:      tt.fields.state,
				Source:     tt.fields.Source,
				SqlTaskDAL: tt.fields.DdlDAL,
			}
			t.getDDLTaskStatus(tt.args.ctx, tt.args.msg)
		})
	}
}

func (suite *OnlineDdlActorTestSuiteTestSuite) TestTaskActor_GetDDLTaskStatus_Err() {
	type fields struct {
		state  *TaskState
		Source datasource.DataSourceService
		DdlDAL dal.SqlTask
	}

	local := local_dal.LocalAudit{}
	local.Init(context.Background(), suite.ctrl)
	type args struct {
		ctx types.Context
		msg *shared.GetDDLTaskStatusReq
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "1",
			fields: fields{
				state:  &TaskState{},
				Source: MockDS(suite.ctrl),
				DdlDAL: local.DdlDAL,
			},
			args: args{
				ctx: actorCtxMockErr(suite.ctrl),
				msg: &shared.GetDDLTaskStatusReq{
					SqlTaskId: "1",
					TenantId:  "1",
				},
			},
		},
	}
	for _, tt := range tests {
		PatchConvey("", suite.T(), func() {
			t := SqlTaskActor{
				state:      tt.fields.state,
				Source:     tt.fields.Source,
				SqlTaskDAL: tt.fields.DdlDAL,
			}
			t.getDDLTaskStatus(tt.args.ctx, tt.args.msg)
		})
	}
}

func (suite *OnlineDdlActorTestSuiteTestSuite) TestTaskActor_stopDDLTask() {
	type fields struct {
		state  *TaskState
		Source datasource.DataSourceService
		DdlDAL dal.SqlTask
	}

	local := local_dal.LocalAudit{}
	local.Init(context.Background(), suite.ctrl)
	type args struct {
		ctx types.Context
		msg *shared.StopDDLTaskReq
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "1",
			fields: fields{
				state:  &TaskState{},
				Source: MockDS1(suite.ctrl),
				DdlDAL: local.DdlDAL,
			},
			args: args{
				ctx: actorCtxMock(suite.ctrl),
				msg: &shared.StopDDLTaskReq{
					SqlTaskId: "1",
					TenantId:  "1",
				},
			},
		},
	}
	for _, tt := range tests {
		PatchConvey("", suite.T(), func() {
			t := SqlTaskActor{
				state:      tt.fields.state,
				Source:     tt.fields.Source,
				SqlTaskDAL: tt.fields.DdlDAL,
			}
			t.stopDDLTask(tt.args.ctx, tt.args.msg)
		})
	}
}

func (suite *OnlineDdlActorTestSuiteTestSuite) TestUpdateDdlTask() {
	type args struct {
		ctx     types.Context
		ddlTask *dao.SqlTask
		odata   *model.SqlTask
	}
	PatchConvey("", suite.T(), func() {
		tests := []struct {
			name string
			args args
		}{
			{
				name: "",
				args: args{
					ctx: actorCtxMock(suite.ctrl),
					ddlTask: &dao.SqlTask{
						Id:            0,
						OrderId:       "",
						TenantId:      "",
						SqlTaskType:   "",
						SqlTaskStatus: "",
						InstanceId:    "",
						InstanceType:  "",
						DbName:        "",
						TbName:        "",
						ExecSql:       "",
						Comment:       "",
						ExecuteTime:   0,
						FinishTime:    0,
						DeadlineTime:  0,
						Result:        "",
						RunningInfo:   "",
						Progress:      0,
						Deleted:       0,
					},
					odata: &model.SqlTask{
						InstanceId:    "1",
						OrderId:       "1",
						DBName:        "1",
						TableName:     "1",
						ExecSQL:       "1",
						Comment:       "1",
						CreateTime:    1688040000,
						FinishTime:    1688040000,
						SqlTaskStatus: 1,
						Result_:       "1",
						Progress:      1,
					},
				},
			},
		}
		for _, tt := range tests {

			SqlTaskModelToDao(tt.args.ddlTask, tt.args.odata)
			So(tt.args.ddlTask, ShouldResemble, &dao.SqlTask{
				Id:            0,
				OrderId:       "",
				TenantId:      "",
				SqlTaskType:   "",
				SqlTaskStatus: "Pending",
				InstanceId:    "",
				InstanceType:  "",
				DbName:        "1",
				TbName:        "1",
				ExecSql:       "",
				Comment:       "1",
				ExecuteTime:   1688040000,
				FinishTime:    1688040000,
				DeadlineTime:  0,
				Result:        "1",
				RunningInfo:   "",
				Progress:      1,
				Deleted:       0,
			})
		}
	})
}

func Test_newTaskState(t *testing.T) {
	type args struct {
		bytes []byte
	}
	tests := []struct {
		name string
		args args
		want *TaskState
	}{
		{
			name: "1",
			args: args{
				bytes: []byte{},
			},
			want: &TaskState{},
		},
	}
	for _, tt := range tests {
		if got := newTaskState(tt.args.bytes); !reflect.DeepEqual(got, tt.want) {
			t.Errorf("newTaskState() = %v, want %v", got, tt.want)
		}
	}
}

func MockDS(ctrl *gomock.Controller) datasource.DataSourceService {
	source := mocks.NewMockDataSourceService(ctrl)
	source.EXPECT().DescribeDBInstanceDetail(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDBInstanceDetailResp{
		InstanceId:      "1",
		InstanceName:    "1",
		InstanceStatus:  rdsModel_v2.InstanceStatus_Running.String(),
		RegionId:        "cn-nanjing-bbit",
		ZoneId:          "az1",
		DBEngine:        "1",
		DBEngineVersion: "1",
		InstanceType:    "1",
		VCPU:            1,
		Memory:          2,
		ProjectName:     "projectname",
	}, nil).AnyTimes()
	source.EXPECT().DescribeFreeLockCorrectOrders(gomock.Any(), gomock.Any()).Return(&datasource.DescribeFreeLockCorrectOrdersResp{
		Total: 1,
		Datas: []*model.SqlTask{
			{
				InstanceId:    "1",
				OrderId:       "1",
				DBName:        "1",
				TableName:     "1",
				ExecSQL:       "1",
				Comment:       "1",
				CreateTime:    1688040000,
				FinishTime:    1688040000,
				SqlTaskStatus: 1,
				Result_:       "1",
				Progress:      1,
			},
		},
	}, nil).AnyTimes()

	return source
}

func MockDS1(ctrl *gomock.Controller) datasource.DataSourceService {
	source := mocks.NewMockDataSourceService(ctrl)
	source.EXPECT().DescribeDBInstanceDetail(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDBInstanceDetailResp{
		InstanceId:      "1",
		InstanceName:    "1",
		InstanceStatus:  rdsModel_v2.InstanceStatus_Running.String(),
		RegionId:        "cn-nanjing-bbit",
		ZoneId:          "az1",
		DBEngine:        "1",
		DBEngineVersion: "1",
		InstanceType:    "1",
		VCPU:            1,
		Memory:          2,
		ProjectName:     "projectname",
	}, nil).AnyTimes()
	source.EXPECT().DescribeFreeLockCorrectOrders(gomock.Any(), gomock.Any()).Return(&datasource.DescribeFreeLockCorrectOrdersResp{
		Total: 1,
		Datas: []*model.SqlTask{
			{
				InstanceId:    "1",
				OrderId:       "1",
				DBName:        "1",
				TableName:     "1",
				ExecSQL:       "1",
				Comment:       "1",
				CreateTime:    1688040000,
				FinishTime:    1688040000,
				SqlTaskStatus: 1,
				Result_:       "1",
				Progress:      1,
			},
		},
	}, nil).AnyTimes()
	source.EXPECT().StopFreeLockCorrectOrders(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	return source
}

func TestGetFreeLockDMLOrderStatus(t *testing.T) {
	act := SqlTaskActor{
		state:      nil,
		Source:     nil,
		SqlTaskDAL: &mockdal.MockSqlTask{},
		ticketDAL:  &mocks.MockWorkflowDAL{},
	}
	ctx := &mocks.MockContext{}
	baseMock1 := Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	base1 := Mock((*mocks.MockContext).GetName).Return("").Build()
	defer base1.UnPatch()

	baseMock2 := Mock((*mocks.MockWorkflowDAL).GetTicketByTaskID).Return(nil, fmt.Errorf("error")).Build()

	act.GetFreeLockDMLOrderStatus(ctx, &dao.SqlTask{
		SqlTaskId: "1",
	})
	baseMock2.UnPatch()

	baseMock4 := Mock((*mockdal.MockSqlTask).UpdateSqlTask).Return(nil).Build()
	defer baseMock4.UnPatch()

	baseMock18 := Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock18.UnPatch()
	baseMock19 := Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock19.UnPatch()

	baseMock3 := Mock((*mocks.MockWorkflowDAL).GetTicketByTaskID).Return(&dao.Ticket{
		TicketStatus: int8(model.TicketStatus_TicketWaitExecute),
		TicketId:     1,
	}, nil).Build()
	act.GetFreeLockDMLOrderStatus(ctx, &dao.SqlTask{
		SqlTaskId: "1",
	})
	baseMock3.UnPatch()

	baseMock5 := Mock((*mocks.MockWorkflowDAL).GetTicketByTaskID).Return(&dao.Ticket{
		TicketStatus: int8(model.TicketStatus_TicketExecute),
		TicketId:     1,
	}, nil).Build()
	act.GetFreeLockDMLOrderStatus(ctx, &dao.SqlTask{
		SqlTaskId: "1",
	})
	baseMock5.UnPatch()

	baseMock6 := Mock((*mocks.MockWorkflowDAL).GetTicketByTaskID).Return(&dao.Ticket{
		TicketStatus: int8(model.TicketStatus_TicketFinished),
		TicketId:     1,
	}, nil).Build()
	act.GetFreeLockDMLOrderStatus(ctx, &dao.SqlTask{
		SqlTaskId: "1",
	})
	baseMock6.UnPatch()

	baseMock7 := Mock((*mocks.MockWorkflowDAL).GetTicketByTaskID).Return(&dao.Ticket{
		TicketStatus: int8(model.TicketStatus_TicketError),
		TicketId:     1,
	}, nil).Build()
	act.GetFreeLockDMLOrderStatus(ctx, &dao.SqlTask{
		SqlTaskId: "1",
	})
	baseMock7.UnPatch()

	baseMock8 := Mock((*mocks.MockWorkflowDAL).GetTicketByTaskID).Return(&dao.Ticket{
		TicketStatus: 100,
		TicketId:     1,
	}, nil).Build()
	act.GetFreeLockDMLOrderStatus(ctx, &dao.SqlTask{
		SqlTaskId: "1",
	})
	baseMock8.UnPatch()

}
