package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_archive/data_backup"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/sqltask"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"fmt"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"
	"go.uber.org/dig"
	"runtime/debug"
	"time"
)

type DataArchiveActorIn struct {
	dig.In
	Conf           config.ConfigProvider
	IdgenSvc       idgen.Service
	Ds             datasource.DataSourceService
	Loca           location.Location
	C3ConfProvider c3.ConfigProvider
	SqlTaskService sqltask.SqlTaskService
	ArchiveRepo    repository.DataArchiveRepo
	WorkflowDal    dal.WorkflowDAL
	ActorClient    cli.ActorClient
	TicketService  workflow.TicketService
	BackupService  data_backup.BackupService
}

func NewDataArchiveActor(p DataArchiveActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.DataArchiveActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &DataArchiveActor{
				state:          newDataArchiveActorState(state),
				cnf:            p.Conf,
				idgenSvc:       p.IdgenSvc,
				ds:             p.Ds,
				c3ConfProvider: p.C3ConfProvider,
				sqlTaskService: p.SqlTaskService,
				archiveRepo:    p.ArchiveRepo,
				workflowDal:    p.WorkflowDal,
				loca:           p.Loca,
				actorClient:    p.ActorClient,
				ticketService:  p.TicketService,
				backupService:  p.BackupService,
			}
		}),
	}
}

func newDataArchiveActorState(bytes []byte) *DataArchiveActorState {
	ts := &DataArchiveActorState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		return ts
	}
	return ts
}

type DataArchiveActorState struct {
	ArchiveTaskId int64
	TicketId      int64
	IsOnce        bool
	ArchiveTask   *entity.ArchiveTask
	ArchiveConfig *entity.ArchiveConfig
	TryCount      int
}

type DataArchiveActor struct {
	state          *DataArchiveActorState
	cnf            config.ConfigProvider
	idgenSvc       idgen.Service
	ds             datasource.DataSourceService
	loca           location.Location
	c3ConfProvider c3.ConfigProvider
	sqlTaskService sqltask.SqlTaskService
	archiveRepo    repository.DataArchiveRepo
	workflowDal    dal.WorkflowDAL
	tlsClient      tls.Client
	actorClient    cli.ActorClient
	ticketService  workflow.TicketService
	backupService  data_backup.BackupService
}

func (d *DataArchiveActor) GetState() []byte {
	state, _ := json.Marshal(d.state)
	return state
}

// 归档执行顺序为：获取基础信息 -> 安全检查 -> 数据备份 -> 数据校验 -> 删除数据
// 异步检查结果

func (d *DataArchiveActor) Process(ctx types.Context) {
	ctx.SetReceiveTimeout(dataArchiveReceiveTimeout * time.Second)

	switch msg := ctx.Message().(type) {
	case *actor.Started:
		// 启动时候做的事情
		d.protectUserCall(ctx, func() {
			d.OnStart(ctx)
		})
	case *shared.DataArchive:
		d.protectUserCall(ctx, func() {
			d.startDataArchive(ctx, msg)
		})
	case *shared.SafeCheck:
		d.protectUserCall(ctx, func() {
			d.DataArchiveSafeCheck(ctx)
		})
	case *shared.DataBackUp:
		d.protectUserCall(ctx, func() {
			d.DataArchiveBackUpData(ctx)
		})
	case *shared.DataCheck:
		d.protectUserCall(ctx, func() {
			d.DataArchiveDataCheck(ctx)
		})
	case *shared.DataDelete:
		d.protectUserCall(ctx, func() {
			d.DataArchiveDeleteData(ctx)
		})
	case *shared.ArchiveError:
		d.protectUserCall(ctx, func() {
			d.dealArchiveError(ctx, msg)
		})
	case *shared.StopArchive:
		d.protectUserCall(ctx, func() {
			d.stopArchiveTask(ctx)
		})
	case *actor.ReceiveTimeout:
		d.protectUserCall(ctx, func() {
			d.checkArchiveTask(ctx)
		})
	case *actor.Stopped:
		log.Info(ctx, "ExecTicketActor %s stop", ctx.GetName())
	}
}

func (d *DataArchiveActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call FreeLockDMLActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (d *DataArchiveActor) OnStart(ctx types.Context) {
	log.Info(ctx, "DataArchiveActor start")
	// do nothing
	// 因为如果是新发起的，则待会会直接访问DataArchive方法
	// 如果是异常恢复的，则30s后会自动开始检查状态
	if d.state.ArchiveTaskId != 0 {
		_ = d.setActorBasicInfo(ctx, d.state.ArchiveTaskId)
	}
	// 初始化tlsClient
	d.tlsClient = d.createTlsClient(ctx)
}

func (d *DataArchiveActor) sendLogs(ctx types.Context, message string) {
	if d.tlsClient == nil {
		return
	}
	_, err := d.tlsClient.PutLogs(d.formatTlsLogs(ctx, message))
	if err != nil {
		log.Warn(ctx, "DataArchive: write tls error:%s", err.Error())
	}
	time.Sleep(1 * time.Second)
}

func (d *DataArchiveActor) formatTlsLogs(ctx types.Context, message string) *tls.PutLogsRequest {
	inspectorLog := &pb.Log{
		Time: time.Now().Unix(),
		Contents: []*pb.LogContent{
			{Key: "task_id", Value: fmt.Sprintf("%d", d.state.ArchiveTaskId)},
			{Key: "message", Value: message},
			{Key: "create_time", Value: time.Now().Format("2006-01-02 15:04:05")},
		},
	}
	logs := []*pb.Log{inspectorLog}
	logGroup := &pb.LogGroup{
		Logs: logs,
	}
	cnf := d.cnf.Get(ctx)
	topicId := cnf.DataArchiveLogTopicId
	//topicId = "23201d02-0f07-4033-9271-d3a56916bb3a"
	request := &tls.PutLogsRequest{
		TopicID: topicId,
		LogBody: &pb.LogGroupList{LogGroups: []*pb.LogGroup{logGroup}},
	}
	return request
}

func (d *DataArchiveActor) close() {

}

func (d *DataArchiveActor) createTlsClient(ctx types.Context) tls.Client {
	c3Cfg := d.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	Ak := c3Cfg.TLSServiceAccessKey
	Sk := c3Cfg.TLSServiceSecretKey
	regionId := d.loca.RegionID()
	//regionId = "cn-guilin-boe"
	tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, regionId)
	return tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)
}

func (d *DataArchiveActor) startDataArchive(ctx types.Context, dataArchive *shared.DataArchive) {
	// 1.获取任务和配置
	d.state.ArchiveTaskId = dataArchive.ArchiveTaskId
	d.state.TicketId = dataArchive.TicketId
	d.state.IsOnce = dataArchive.IsOnce
	d.sendLogs(ctx, "[pre] start data archiving")
	if err := d.setActorBasicInfo(ctx, dataArchive.ArchiveTaskId); err != nil {
		log.Warn(ctx, "setActorBasicInfo error :%s", err.Error())
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: err.Error()})
		return
	}
	d.sendLogs(ctx, fmt.Sprintf("[pre] get archive config from db, configId: %d", d.state.ArchiveConfig.ConfigId))
	// 2.查询DB的当前时间
	if err := d.setNowTime(); err != nil {
		log.Warn(ctx, "setNowTime error :%s", err.Error())
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: err.Error()})
		return
	}
	// 3.开始执行流程
	ctx.Send(ctx.Self(), &shared.SafeCheck{})
}

func (d *DataArchiveActor) setActorBasicInfo(ctx types.Context, archiveTaskId int64) error {
	// 1.获取归档任务信息
	if d.state.ArchiveTask == nil || d.state.ArchiveTask.TaskId == 0 {
		task, err := d.archiveRepo.GetDataArchiveTask(ctx, archiveTaskId)
		if err != nil {
			log.Warn(ctx, "GetDataArchiveTask error: %s", err.Error())
			return err
		}
		d.state.ArchiveTask = task
	}
	// 2.获取归档配置
	if d.state.ArchiveConfig == nil || d.state.ArchiveConfig.ConfigId == 0 {
		archiveConfig, err := d.archiveRepo.GetDataArchiveConfig(ctx, d.state.ArchiveTask.ArchiveConfigId)
		if err != nil {
			log.Warn(ctx, "GetDataArchiveConfig error: %s", err.Error())
			return err
		}
		d.state.ArchiveConfig = archiveConfig
	}

	return nil
}

func (d *DataArchiveActor) getTicketArchiveConfig(ctx types.Context) (*entity.ArchiveConfig, error) {
	ticket, err := d.workflowDal.DescribeByTicketID(ctx, d.state.TicketId)
	if err != nil {
		log.Warn(ctx, "get ticket:%d info error: %s", d.state.TicketId, err.Error())
		return nil, err
	}
	archiveConfig := &model.DataArchiveConfig{}
	err = json.Unmarshal([]byte(ticket.DataArchiveConfig), &archiveConfig)
	if err != nil {
		log.Warn(ctx, "ticket:%d Unmarshal  DataArchiveConfig error: %s", d.state.TicketId, err.Error())
		return nil, err
	}
	return &entity.ArchiveConfig{
		ConfigId:     -1,
		InstanceId:   ticket.InstanceId,
		InstanceType: ticket.InstanceType,
		TenantId:     ticket.TenantId,
		BizTimes:     archiveConfig.TimeInfos,
		IsBackUp:     archiveConfig.IsBackUp,
		Database:     ticket.DbName,
		TableName:    archiveConfig.TableName,
		OtherCase:    archiveConfig.GetOtherCase(),
	}, nil
}

func (d *DataArchiveActor) setNowTime() error {
	nowDbTime := time.Now().Unix()
	if d.state.ArchiveTask.ExecTime == 0 {
		d.state.ArchiveTask.ExecTime = nowDbTime
	}
	return nil
}

func (d *DataArchiveActor) sendFinshTaskToConfigActor(ctx types.Context, success bool, errMsg string) {
	err := d.actorClient.KindOf(consts.DataArchiveConfigActorKind).Send(ctx, fmt.Sprintf("%d", d.state.ArchiveConfig.ConfigId),
		&shared.FinshArchiveTask{ArchiveConfigId: d.state.ArchiveConfig.ConfigId, Success: success, ErrMsg: errMsg})
	if err != nil {
		log.Warn(ctx, "send DataArchivingConfigActor finished task,taskId:%d error:%s", d.state.TicketId, err.Error())
	}
}

func (d *DataArchiveActor) stopArchiveTask(ctx types.Context) {
	d.killTask(ctx)
	d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Error
	errMsg := "cancel task"
	ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: errMsg})
}
