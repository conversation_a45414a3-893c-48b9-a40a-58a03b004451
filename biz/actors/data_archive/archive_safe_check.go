package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

func (d *DataArchiveActor) DataArchiveSafeCheck(ctx types.Context) {
	if err := d.safeCheck(ctx); err != nil {
		log.Warn(ctx, "safeCheck error :%s", err.Error())
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: err.Error()})
		return
	}
	ctx.Send(ctx.Self(), &shared.DataBackUp{})
}

func (d *DataArchiveActor) safeCheck(ctx types.Context) error {
	if d.state.ArchiveTask.TaskStatus > model.ArchiveTaskStatus_SafeCheck {
		return nil
	}
	d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_SafeCheck
	if err := d.archiveRepo.UpdateTaskInfo(ctx, d.state.ArchiveTask); err != nil {
		log.Warn(ctx, "UpdateTaskInfo error:%s", err)
		return err
	}
	// TODO 是否需要做安全检查
	return nil
}
