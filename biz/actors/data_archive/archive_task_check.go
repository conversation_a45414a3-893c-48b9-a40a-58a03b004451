package data_archive

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_archive/data_backup"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	dslibutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"fmt"
	"strings"
)

func (d *DataArchiveActor) dealArchiveError(ctx types.Context, archiveError *shared.ArchiveError) {
	d.state.TryCount++
	// 如果当前已经是Error状态了，就说明备份/安全检查/删数据由于操作失败了，这个时候，我们就将任务转为失败，结束任务
	if d.state.ArchiveTask.TaskStatus == model.ArchiveTaskStatus_Error {
		d.state.ArchiveTask.ErrorMsg = archiveError.ErrorMsg
		d.dealErrorTask(ctx, archiveError)
		return
	}
	// 如果是其他状态&超过了重试次数，可执行时间
	if d.state.TryCount >= maxTryCount || !d.isExecuteTimeAllow() {
		d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Error
		errMsg := fmt.Sprintf("data-archiving faild, the operation exceeded the execution limit or the executable range, last execute erorrMsg is: %s", archiveError.ErrorMsg)
		d.sendLogs(ctx, errMsg)
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: errMsg})
		return
	}
	// 如果当前是其他状态，我们进入重试逻辑
	d.sendLogs(ctx, "[error] "+archiveError.ErrorMsg)
	d.sendLogs(ctx, "[retry] start retry")
}

func (d *DataArchiveActor) dealErrorTask(ctx types.Context, archiveError *shared.ArchiveError) {
	if err := d.archiveRepo.UpdateTaskInfo(ctx, d.state.ArchiveTask); err != nil {
		log.Warn(ctx, "UpdateTaskInfo error :%s", err.Error())
	}
	d.sendFinshTaskToConfigActor(ctx, false, archiveError.GetErrorMsg())
	d.sendLogs(ctx, "[error] "+d.state.ArchiveTask.ErrorMsg)
	d.sendLogs(ctx, "[error] change task to failed")
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
}

func (d *DataArchiveActor) isExecuteTimeAllow() bool {
	// 没有下限范围，接口预留
	return true
}

func (d *DataArchiveActor) checkArchiveTask(ctx types.Context) {
	if d.state.ArchiveTaskId == 0 {
		log.Warn(ctx, "taskId is 0 kill self")
		// 直接自杀，数据出现问题
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}

	if !d.isExecuteTimeAllow() {
		d.killTask(ctx)
		d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Error
		errMsg := "execute time over range, kill task"
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: errMsg})
		return
	}

	switch d.state.ArchiveTask.TaskStatus {
	case model.ArchiveTaskStatus_Undo:
		ctx.Send(ctx.Self(), &shared.DataArchive{ArchiveTaskId: d.state.ArchiveTaskId})
	case model.ArchiveTaskStatus_SafeCheck:
		ctx.Send(ctx.Self(), &shared.SafeCheck{})
	case model.ArchiveTaskStatus_BackUp:
		d.checkBackUpStatus(ctx)
	case model.ArchiveTaskStatus_DataCheck:
		d.checkDataCheckStatus(ctx)
	case model.ArchiveTaskStatus_Delete:
		d.checkDataDeleteStatus(ctx)
	case model.ArchiveTaskStatus_Finished, model.ArchiveTaskStatus_Error:
		d.finishTask(ctx)
	}
}

func (d *DataArchiveActor) killTask(ctx types.Context) {
	switch d.state.ArchiveTask.TaskStatus {
	case model.ArchiveTaskStatus_BackUp:
		d.killBackUpTask(ctx)
	case model.ArchiveTaskStatus_DataCheck:
		// TODO do nothing 这一期还没有
	case model.ArchiveTaskStatus_Delete:
		d.killDeleteTask(ctx)
	}
}

func (d *DataArchiveActor) killBackUpTask(ctx types.Context) {
	if d.state.ArchiveTask.BackupTaskId == "" {
		return
	}
	stopMsg := data_backup.StopBackUpReq{
		Type:       d.state.ArchiveConfig.BackUpType,
		TaskId:     d.state.ArchiveTask.BackupTaskId,
		TenantId:   d.state.ArchiveTask.TenantId,
		InstanceId: d.state.ArchiveTask.InstanceId,
	}
	err := d.backupService.StopBackUp(ctx, stopMsg)
	if err != nil {
		log.Warn(ctx, "ticket:%d, taskId:%s, stop back up task error:%s", d.state.ArchiveTask.TicketId, d.state.ArchiveTask.BackupTaskId, err.Error())
	}
}

func (d *DataArchiveActor) killDeleteTask(ctx types.Context) {
	if d.state.ArchiveTask.SqlTaskId == "" {
		return
	}
	if d.state.ArchiveConfig.InstanceType == model.InstanceType_MySQLSharding.String() {
		d.KillShardingTask(ctx)
		return
	}
	err := d.actorClient.KindOf(consts.FreeLockDMLActorKind).
		Send(ctx, d.state.ArchiveTask.SqlTaskId, &shared.StopTicket{
			TicketId:   conv.StrToInt64(d.state.ArchiveTask.SqlTaskId, 0),
			TaskId:     d.state.ArchiveTask.SqlTaskId,
			TenantId:   d.state.ArchiveConfig.TenantId,
			InstanceId: d.state.ArchiveConfig.InstanceId,
		})
	if err != nil {
		log.Warn(ctx, "stopTicket: send message to exec ticket actor error:%s", err.Error())
	}
}

func (d *DataArchiveActor) KillShardingTask(ctx types.Context) {
	err := d.actorClient.KindOf(consts.ShardingFreeLockDMLActorKind).
		Send(ctx, d.state.ArchiveTask.SqlTaskId, &shared.StopTicket{
			TicketId:   conv.StrToInt64(d.state.ArchiveTask.SqlTaskId, 0),
			TaskId:     d.state.ArchiveTask.SqlTaskId,
			TenantId:   d.state.ArchiveConfig.TenantId,
			InstanceId: d.state.ArchiveConfig.InstanceId,
		})
	if err != nil {
		log.Warn(ctx, "stopTicket: send message to exec ticket actor error:%s", err.Error())
	}
}

func (d *DataArchiveActor) checkBackUpStatus(ctx types.Context) {
	if !d.state.ArchiveTask.IsBackUp {
		ctx.Send(ctx.Self(), &shared.DataCheck{})
		return
	}

	// 检查备份任务状态
	resp, err := d.backupService.GetBackUpStatus(ctx, data_backup.GetBackUpStatusReq{
		Type:     d.state.ArchiveConfig.BackUpType,
		TaskId:   d.state.ArchiveTask.BackupTaskId,
		TenantId: d.state.ArchiveTask.TenantId,
	})
	if err != nil {
		log.Warn(ctx, "GetBackUpStatus error:%s", err.Error())
		errMsg := err.Error()
		d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Error
		d.state.ArchiveTask.ErrorMsg = "get backup status error: " + err.Error()
		err = d.backupService.StopBackUp(ctx, data_backup.StopBackUpReq{
			Type:   d.state.ArchiveConfig.BackUpType,
			TaskId: d.state.ArchiveTask.BackupTaskId,
		})
		if err != nil {
			log.Warn(ctx, "StopBackUp error:%s", err.Error())
		}
		d.GiveBackInstanceSession(ctx, d.state.ArchiveTask.InstanceId, d.state.ArchiveTask.SubSessionId)
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: "get backup status error: " + errMsg})
		return
	}

	switch resp.Status {
	case data_backup.BackUpStatusRunning:
		// doNothing
		return
	case data_backup.BackUpStatusSuccess:
		log.Info(ctx, "backup success")
		ctx.Send(ctx.Self(), &shared.DataCheck{})
		d.GiveBackInstanceSession(ctx, d.state.ArchiveTask.InstanceId, d.state.ArchiveTask.SubSessionId)
		d.SetBackUpAddress(ctx)
		return
	case data_backup.BackUpStatusFailed:
		log.Warn(ctx, "backup failed: %s", resp.Msg)
		d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Error
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: "backup error: " + resp.Msg})
		d.GiveBackInstanceSession(ctx, d.state.ArchiveTask.InstanceId, d.state.ArchiveTask.SubSessionId)
		return
	}
}

func (d *DataArchiveActor) GiveBackInstanceSession(ctx context.Context, instanceId string, sessionId string) {
	err := d.actorClient.KindOf(consts.SessionMgrActorKind).Send(ctx, instanceId, &shared.GiveBackSession{InstanceId: instanceId, SessionId: sessionId})
	if err != nil {
		log.Warn(ctx, "give back session error:%s", err.Error())
		return
	}
	log.Info(ctx, "give back session %s", sessionId)
}

func (d *DataArchiveActor) SetBackUpAddress(ctx types.Context) {
	message := "[BackUp] BackUpAddress: "

	cnf := d.cnf.Get(ctx)
	resp, err := d.backupService.GetBackUpAddress(ctx, data_backup.GetBackUpAddressReq{
		Type:          d.state.ArchiveConfig.BackUpType,
		TaskId:        d.state.ArchiveTask.BackupTaskId,
		TenantId:      d.state.ArchiveTask.TenantId,
		InstanceId:    d.state.ArchiveTask.InstanceId,
		TosBucketName: cnf.ArchiveBackupTosBucket,
	})
	if err != nil {
		log.Warn(ctx, "GetBackUpAddress error:%s", err.Error())
		message += "backup success, but get backup-address error "
	} else {
		message += resp.Address
	}

	d.sendLogs(ctx, message)
}

func (d *DataArchiveActor) checkDataCheckStatus(ctx types.Context) {
	// 检查核对任务状态
	// TODO  因为现在没有，所以直接发到删除数据
	ctx.Send(ctx.Self(), &shared.DataDelete{})
}

func (d *DataArchiveActor) checkDataDeleteStatus(ctx types.Context) {
	// 检查删除任务状态
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: d.state.ArchiveConfig.TenantId,
		UserID:   "",
		LogID:    "",
	})
	dsResp, err := d.sqlTaskService.DescribeSqlTask(ctx, &model.DescribeSqlTaskReq{SqlTaskId: d.state.ArchiveTask.SqlTaskId})
	if err != nil {
		log.Warn(ctx, "DescribeFreeLockDMLSqlTasks error:%s", err.Error())
		return
	}
	log.Info(ctx, "archive ticket %v dsResp is %v", d.state.TicketId, dslibutils.Show(dsResp))
	if dsResp.SqlTaskStatus == model.SqlTaskStatus_Success {
		d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Finished
		d.state.ArchiveTask.AffectedRows = dsResp.AffectedRows
		d.workflowDal.UpdateProgress(ctx, d.state.TicketId, "ticket execute success", dsResp.Progress)
		if err = d.archiveRepo.UpdateTaskInfo(ctx, d.state.ArchiveTask); err != nil {
			log.Warn(ctx, "UpdateTaskInfo error:%s", err.Error())
			ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: err.Error()})
		}
	} else if dsResp.SqlTaskStatus == model.SqlTaskStatus_Failed {
		d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Error
		d.state.ArchiveTask.ErrorMsg = dsResp.Result_
		if err = d.archiveRepo.UpdateTaskInfo(ctx, d.state.ArchiveTask); err != nil {
			log.Warn(ctx, "UpdateTaskInfo error:%s", err.Error())
			ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: err.Error()})
		}
	} else if dsResp.SqlTaskStatus == model.SqlTaskStatus_Running {
		// 更新一下任务进度
		d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Delete
		d.state.ArchiveTask.AffectedRows = dsResp.AffectedRows
		d.workflowDal.UpdateWorkStatusAndProgress(ctx, d.state.TicketId, int32(model.TicketStatus_TicketExecute), "ticket is executing", dsResp.Progress)
		if err = d.archiveRepo.UpdateTaskInfo(ctx, d.state.ArchiveTask); err != nil {
			log.Warn(ctx, "UpdateTaskInfo error:%s", err.Error())
			ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: err.Error()})
		}
	}
}

func (d *DataArchiveActor) finishTask(ctx types.Context) {
	if err := d.archiveRepo.UpdateTaskInfo(ctx, d.state.ArchiveTask); err != nil {
		log.Warn(ctx, "UpdateTaskInfo error:%s", err.Error())
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: err.Error()})
	}
	success := d.state.ArchiveTask.TaskStatus != model.ArchiveTaskStatus_Error
	errMsg := ""
	if !success {
		d.sendLogs(ctx, d.state.ArchiveTask.ErrorMsg)
		errMsg = d.state.ArchiveTask.ErrorMsg
	}

	// 这里最后做一步操作,把sqltaskid中的影响行数,更新到归档的任务的影响行数中
	// 如果是单次归档,那么把sqltaskid的影响行数更新成本次的影响行数
	// 如果是重复归档,那么把sqltaskid的影响行数加在Total行数上
	// eg:
	// archiveTicket 0/0         sqlTaskTicket 5000/0   最终结果是5000/5000
	// archiveTicket 5000/10000  sqlTaskTicket 5100/0   最终结果 5100/15100
	sqlTaskTicket, err := d.workflowDal.DescribeByTicketID(ctx, conv.StrToInt64(d.state.ArchiveTask.SqlTaskId, 0))
	if err != nil {
		// 如果没有查询到,就不更新行数了,给用户查一下,不阻塞任务
		log.Warn(ctx, "get archive ticket %v from sql task id %v error %v", d.state.TicketId, d.state.ArchiveTask.SqlTaskId)
	}
	var sqlTaskRes = make([]string, 0)
	if sqlTaskTicket != nil {
		sqlTaskRes = strings.Split(sqlTaskTicket.AffectedRows, "/")
		log.Info(ctx, "archive ticket %v sql task ticket %v affect rows is %v ", d.state.TicketId, d.state.ArchiveTask.SqlTaskId, sqlTaskTicket.AffectedRows)
	}

	ticket, err := d.workflowDal.DescribeByTicketID(ctx, d.state.TicketId)
	if err != nil {
		log.Warn(ctx, "get archive ticket %v error %v", d.state.TicketId, err)
		return
	}
	var ticketRes = make([]string, 0)
	if ticket != nil {
		if ticket.AffectedRows == "" {
			ticket.AffectedRows = "0/0"
		}
		ticketRes = strings.Split(ticket.AffectedRows, "/")
		log.Info(ctx, "archive ticket %v  affect rows is %v ", d.state.TicketId, ticket.AffectedRows)
	}

	if len(ticketRes) == 2 && len(sqlTaskRes) == 2 {
		// 如果是单次归档,直接更新即可
		if d.state.ArchiveConfig.ArchiveType == model.ArchiveType_Once {
			ticket.AffectedRows = fmt.Sprintf("%v", ticketRes[1])
		} else {
			ticket.AffectedRows = fmt.Sprintf("%v/%v", sqlTaskRes[0], conv.StrToInt64(ticketRes[1], 0)+conv.StrToInt64(sqlTaskRes[0], 0))
		}
	}

	d.workflowDal.UpdateTicketAffectedRows(ctx, &dao.Ticket{
		TicketId:     d.state.TicketId,
		AffectedRows: ticket.AffectedRows,
	})

	d.sendLogs(ctx, "[Finished] finished task")
	d.sendFinshTaskToConfigActor(ctx, success, errMsg)
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
}
