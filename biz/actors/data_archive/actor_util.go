package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"fmt"
	"github.com/qjpcpu/fp"
	"strings"
	"time"
)

const (
	//dataArchiveReceiveTimeout = 30
	dataArchiveReceiveTimeout = 10
	maxTryCount               = 10

	WaitArchive = 0
	Archiving   = 1
)

func FormatDeleteSqlWhereCase(nowTime int64, config *entity.ArchiveConfig) string {
	whereCase := "(" + config.OtherCase + ")"

	timeCase := fp.StreamOf(config.BizTimes).Map(func(bizTime *model.TimeInfo) string {
		return getTimeCase(nowTime, int64(bizTime.OffsetTime), bizTime.TimeFormat, bizTime.TimeColumn)
	}).JoinStrings(" AND ")
	//if strings.TrimSpace(config.OtherCase) != "" {
	//	whereCase += " AND " + timeCase
	//} else {
	//	whereCase = timeCase
	//}
	//return fmt.Sprintf(" where %s ", whereCase)
	// 过滤条件和变量配置都为空，返回空,不带where
	if strings.TrimSpace(config.OtherCase) == "" && strings.TrimSpace(timeCase) == "" {
		return ""
	}
	// 如果有变量配置,但是过滤条件为空
	if strings.TrimSpace(config.OtherCase) == "" && strings.TrimSpace(timeCase) != "" {
		return fmt.Sprintf(" where %s ", timeCase)
	}
	// 如果有过滤条件,但是变量配置为空
	if strings.TrimSpace(config.OtherCase) != "" && strings.TrimSpace(timeCase) == "" {
		return fmt.Sprintf(" where %s ", whereCase)
	}
	// 如果过滤条件和变量配置都不为空,则where条件中两个都带
	return fmt.Sprintf(" where %s and %s ", whereCase, timeCase)
}

func getTimeCase(nowTime int64, offsetTime int64, timeFormat string, timeColumn string) string {

	if timeFormat == "ss" || timeFormat == "ss.SS" {
		return getTimestampCase(nowTime, offsetTime, timeFormat, timeColumn)
	}

	datetime := time.Unix(nowTime+offsetTime, 0)

	return fmt.Sprintf(" `%s` < '%s' ", timeColumn, datetime.Format(getTimeLayout(timeFormat)))
}

func getTimestampCase(nowTime int64, offsetTime int64, timeFormat string, timeColumn string) string {
	var timeValue int64
	if timeFormat == "ss" {
		timeValue = nowTime + offsetTime
	} else {
		timeValue = nowTime*1000 + offsetTime*1000
	}
	return fmt.Sprintf(" `%s` < %d ", timeColumn, timeValue)
}

func getTimeLayout(timeFormat string) string {
	// 2006:01:02 15:04:05.000
	layout := strings.Replace(timeFormat, "yyyy", "2006", -1)
	layout = strings.Replace(layout, "MM", "04", -1)
	layout = strings.Replace(layout, "dd", "02", -1)
	layout = strings.Replace(layout, "hh", "15", -1)
	layout = strings.Replace(layout, "mm", "01", -1)
	layout = strings.Replace(layout, "ss", "05", -1)
	layout = strings.Replace(layout, "S", "0", -1)
	return layout
}
