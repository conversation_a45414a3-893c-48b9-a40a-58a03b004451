package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/repository"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
	"time"
)

func TestDealArchiveError(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Error
	mock1 := mockey.Mock((*DataArchiveActor).dealErrorTask).Return().Build()
	defer mock1.UnPatch()
	actor.dealArchiveError(&mocks.MockContext{}, &shared.ArchiveError{})

	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Delete
	actor.dealArchiveError(&mocks.MockContext{}, &shared.ArchiveError{})
	actor.state.TryCount = maxTryCount + 1
	actor.dealArchiveError(&mocks.MockContext{}, &shared.ArchiveError{})

}

func TestDealErrorTask(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*repository.MockDataArchiveRepo).UpdateTaskInfo).Return(fmt.Errorf("test")).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*DataArchiveActor).sendFinshTaskToConfigActor).Return().Build()
	defer mock2.UnPatch()

	actor.state.TryCount = maxTryCount + 1
	actor.dealErrorTask(&mocks.MockContext{}, &shared.ArchiveError{})
	actor.state.TryCount = 0
	actor.dealErrorTask(&mocks.MockContext{}, &shared.ArchiveError{})
}

func TestCheckArchiveTask(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	actor.state.ArchiveTaskId = 0
	actor.checkArchiveTask(&mocks.MockContext{})
	mock1 := mockey.Mock((*DataArchiveActor).isExecuteTimeAllow).Return(false).Build()
	mock2 := mockey.Mock((*DataArchiveActor).killTask).Return().Build()
	defer mock2.UnPatch()
	actor.state.ArchiveTaskId = 123
	actor.checkArchiveTask(&mocks.MockContext{})
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*DataArchiveActor).checkBackUpStatus).Return().Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*DataArchiveActor).checkDataCheckStatus).Return().Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*DataArchiveActor).checkDataDeleteStatus).Return().Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*DataArchiveActor).finishTask).Return().Build()
	defer mock6.UnPatch()

	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Undo
	actor.checkArchiveTask(&mocks.MockContext{})
	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_SafeCheck
	actor.checkArchiveTask(&mocks.MockContext{})
	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_BackUp
	actor.checkArchiveTask(&mocks.MockContext{})
	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_DataCheck
	actor.checkArchiveTask(&mocks.MockContext{})
	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Delete
	actor.checkArchiveTask(&mocks.MockContext{})
	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Finished
	actor.checkArchiveTask(&mocks.MockContext{})
}

func TestKillTask(t *testing.T) {
	actor := mockDataArchiveActor()

	mock1 := mockey.Mock((*DataArchiveActor).killDeleteTask).Return().Build()
	defer mock1.UnPatch()
	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_BackUp
	actor.killTask(&mocks.MockContext{})
	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_DataCheck
	actor.killTask(&mocks.MockContext{})
	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Delete
	actor.killTask(&mocks.MockContext{})
}
