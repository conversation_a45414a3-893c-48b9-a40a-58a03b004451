package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/repository"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestNewArchiveConfigActor(t *testing.T) {
	NewArchiveConfigActor(NewArchiveConfigActorIn{})
	_ = newDataArchiveConfigActorState([]byte("{}"))
	_ = newDataArchiveConfigActorState([]byte("{"))
	actor := mockDataArchiveConfigActor()
	_ = actor.GetState()
}

func TestConfigOnStart(t *testing.T) {
	actor := mockDataArchiveConfigActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	actor.OnStart(&mocks.MockContext{})

	actor.state.ConfigId = 123
	actor.state.ArchiveConfig.ConfigId = 123
	actor.OnStart(&mocks.MockContext{})

	actor.state.ArchiveConfig.ConfigId = 0
	mock1 := mockey.Mock((*repository.MockDataArchiveRepo).GetDataArchiveConfig).Return(&entity.ArchiveConfig{}, fmt.Errorf("test")).Build()
	actor.OnStart(&mocks.MockContext{})
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*repository.MockDataArchiveRepo).GetDataArchiveConfig).Return(&entity.ArchiveConfig{}, nil).Build()
	defer mock2.UnPatch()
	actor.OnStart(&mocks.MockContext{})
}

func TestCreateCronConfig(t *testing.T) {
	actor := mockDataArchiveConfigActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*repository.MockDataArchiveRepo).GetDataArchiveConfig).Return(&entity.ArchiveConfig{}, fmt.Errorf("test")).Build()
	actor.createCronConfig(&mocks.MockContext{}, &shared.CreateArchiveConfig{})
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	archiveConfig := &entity.ArchiveConfig{
		CronStr:     "",
		ArchiveType: model.ArchiveType_Cycle,
	}
	mock2 := mockey.Mock((*repository.MockDataArchiveRepo).GetDataArchiveConfig).Return(archiveConfig, nil).Build()
	defer mock2.UnPatch()
	actor.createCronConfig(&mocks.MockContext{}, &shared.CreateArchiveConfig{})
	archiveConfig.CronStr = "* 05 16 * * ? "
	actor.createCronConfig(&mocks.MockContext{}, &shared.CreateArchiveConfig{})
}

func TestCloseArchiveConfig(t *testing.T) {
	actor := mockDataArchiveConfigActor()
	basicMock1 := mockey.Mock((*ArchiveConfigActor).stopSelf).Return().Build()
	defer basicMock1.UnPatch()
	actor.closeArchiveConfig(&mocks.MockContext{})
}

func TestDoWhenIdle(t *testing.T) {
	actor := mockDataArchiveConfigActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	actor.state.IsOpen = false
	actor.doWhenIdle(&mocks.MockContext{})

	actor.state.IsOpen = true
	actor.doWhenIdle(&mocks.MockContext{})
	actor.state.ArchiveStatus = WaitArchive
	actor.state.ArchiveConfig.ArchiveType = model.ArchiveType_Cycle

	basicMock1 := mockey.Mock((*ArchiveConfigActor).archiveData).Return().Build()
	defer basicMock1.UnPatch()
	actor.doWhenIdle(&mocks.MockContext{})
}

func TestArchiveData(t *testing.T) {
	actor := mockDataArchiveConfigActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*ArchiveConfigActor).createArchiveTask).Return(1, fmt.Errorf("test")).Build()
	actor.archiveData(&mocks.MockContext{})
	mock1.UnPatch()
}

func TestCreateArchiveTask(t *testing.T) {
	actor := mockDataArchiveConfigActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*mocks.MockService).NextID).Return(1, fmt.Errorf("test")).Build()
	_, err := actor.createArchiveTask(&mocks.MockContext{})
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)
	mock11 := mockey.Mock((*ArchiveConfigActor).GetUserName).Return("").Build()
	defer mock11.UnPatch()
	mock2 := mockey.Mock((*mocks.MockService).NextID).Return(1, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*repository.MockDataArchiveRepo).CreateArchiveTask).Return(fmt.Errorf("test")).Build()
	_, err = actor.createArchiveTask(&mocks.MockContext{})
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*repository.MockDataArchiveRepo).CreateArchiveTask).Return(nil).Build()
	defer mock4.UnPatch()
	_, err = actor.createArchiveTask(&mocks.MockContext{})
	assert.Nil(t, err)

}

func TestFinshTask(t *testing.T) {
	actor := mockDataArchiveConfigActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*repository.MockDataArchiveRepo).UpdateLastArchiveStatus).Return(nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*ArchiveConfigActor).stopSelf).Return().Build()
	defer mock2.UnPatch()

	actor.finshTask(&mocks.MockContext{}, &shared.FinshArchiveTask{Success: true})

	actor.state.ArchiveConfig.ArchiveType = model.ArchiveType_Cycle
	_ = actor.initCronParser(&mocks.MockContext{}, "* 05 16 * * ? ")
	actor.finshTask(&mocks.MockContext{}, &shared.FinshArchiveTask{Success: false})

}

func TestStopSelf(t *testing.T) {
	actor := mockDataArchiveConfigActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*repository.MockDataArchiveRepo).CloseArchiveConfig).Return(nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*ArchiveConfigActor).UpdateTicketStatus).Return().Build()
	defer mock2.UnPatch()

	actor.stopSelf(&mocks.MockContext{}, &shared.FinshArchiveTask{Success: false})
}

func TestUpdateTicketStatus(t *testing.T) {
	actor := mockDataArchiveConfigActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(fmt.Errorf("test")).Build()
	defer mock1.UnPatch()

	actor.UpdateTicketStatus(&mocks.MockContext{}, &shared.FinshArchiveTask{Success: false, IsTermination: true})
	actor.UpdateTicketStatus(&mocks.MockContext{}, &shared.FinshArchiveTask{Success: false, IsTermination: false})
	actor.UpdateTicketStatus(&mocks.MockContext{}, &shared.FinshArchiveTask{Success: true, IsTermination: false})
}
