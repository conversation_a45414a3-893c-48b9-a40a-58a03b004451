package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/sqltask"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

func (d *DataArchiveActor) DataArchiveDeleteData(ctx types.Context) {
	d.sendLogs(ctx, "[data-delete] use unlock-dml task")
	if err := d.deleteData(ctx); err != nil {
		log.Warn(ctx, "deleteData error :%s", err.Error())
		d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Error
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: err.Error()})
		return
	}
}

func (d *DataArchiveActor) deleteData(ctx types.Context) error {
	if d.state.ArchiveTask.TaskStatus > model.ArchiveTaskStatus_Delete {
		return nil
	}
	d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Delete
	if strings.TrimSpace(d.state.ArchiveTask.SqlTaskId) == "" {
		if err := d.createDeleteSqlTask(ctx); err != nil {
			log.Warn(ctx, "createDeleteSqlTask error: %s", err.Error())
			return err
		}
	}
	return nil
}

func (d *DataArchiveActor) createDeleteSqlTask(ctx types.Context) error {
	archiveConfig := d.state.ArchiveConfig
	deleteSql := fmt.Sprintf("delete from `%s`.`%s`  %s ", archiveConfig.Database, archiveConfig.TableName, FormatDeleteSqlWhereCase(d.state.ArchiveTask.ExecTime, archiveConfig))
	if d.state.ArchiveConfig.InstanceType == model.InstanceType_MySQLSharding.String() {
		deleteSql = fmt.Sprintf("delete from `%s`  %s ", archiveConfig.TableName, FormatDeleteSqlWhereCase(d.state.ArchiveTask.ExecTime, archiveConfig))
	}
	d.sendLogs(ctx, "[data-delete] start data delete, delete sql: "+deleteSql)
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: d.state.ArchiveConfig.TenantId,
		UserID:   "",
		LogID:    "",
	})
	// 下发任务
	req, err := d.formatCreateSqlTaskReq(ctx, archiveConfig, deleteSql)
	if err != nil {
		log.Warn(ctx, "formatCreateSqlTaskReq error: %s", err.Error())
		return err
	}
	taskId, err := d.sqlTaskService.CreateFreeLockDMLSqlTask(ctx, req, sqltask.TicketFromDataArchive)
	if err != nil {
		log.Warn(ctx, "CreateFreeLockDMLSqlTask error: %s", err.Error())
		return err
	}
	log.Info(ctx, "ticket %v get sql task id is %v", d.state.TicketId, *taskId)
	d.state.ArchiveTask.SqlTaskId = *taskId
	d.state.ArchiveTask.DeleteSql = deleteSql
	if err = d.archiveRepo.UpdateTaskInfo(ctx, d.state.ArchiveTask); err != nil {
		log.Warn(ctx, "UpdateTaskInfo error:%s", err)
		return err
	}
	return nil
}

func (d *DataArchiveActor) getDatasource(ctx types.Context) (*shared.DataSource, error) {
	cnf := d.cnf.Get(ctx)
	dsType := shared.DataSourceType_value[d.state.ArchiveConfig.InstanceType]
	ds := &shared.DataSource{
		Type:             shared.DataSourceType(dsType),
		LinkType:         shared.Volc,
		ConnectTimeoutMs: cnf.TicketConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.TicketReadTimeout * 1000,
		WriteTimeoutMs:   cnf.TicketWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.TicketIdleTimeout * 1000,
		InstanceId:       d.state.ArchiveConfig.InstanceId,
		Db:               d.state.ArchiveConfig.Database,
	}
	// 获取安全管控的账号密码
	ds, err := d.ticketService.GetDBAccount(ctx, ds)
	if err != nil {
		log.Warn(ctx, "ticket: get db account err:", err.Error())
		return ds, consts.ErrorOf(model.ErrorCode_ListAccountFail)
	}
	return ds, nil
}

func (d *DataArchiveActor) formatCreateSqlTaskReq(ctx types.Context, archiveConfig *entity.ArchiveConfig, deleteSql string) (*model.CreateSqlTaskReq, error) {
	instanceType, err := model.DSTypeFromString(archiveConfig.InstanceType)
	if err != nil {
		log.Error(ctx, "DSType fail, error:%s", err)
		return nil, err
	}
	comment := "data archiving"
	sqlTaskType := model.SqlTaskType_FreeLockDML

	ds, err := d.getDatasource(ctx)
	if err != nil {
		log.Error(ctx, "getDatasource, error:%s", err)
		return nil, err
	}
	batchSize := int32(2000)
	dbBatchNum := int32(1)
	// 这里需要判断一下,如果加了执行结束时间,则需要在sqlTaskInfo里面加上执行时间,否则不用关心
	var sqlTaskInfo = &model.SqlTaskInfo{BatchSize: &batchSize, DBBatchNum: &dbBatchNum}
	var executeStartTime, executeEndTime = d.getExecuteTime(ctx)
	if executeEndTime != 0 || executeStartTime != 0 {
		log.Info(ctx, "ticket %v config %v user set execute time, need to set sql task info", d.state.TicketId, d.state.ArchiveConfig.ConfigId)
		sqlTaskInfo.ExecutableEndTime = utils.Int64Ref(executeEndTime)
		sqlTaskInfo.ExecutableStartTime = utils.Int64Ref(executeStartTime)
	}
	log.Info(ctx, "ticket %v generate task starttime is %v endtime is %v", d.state.TicketId, executeStartTime, executeEndTime)
	return &model.CreateSqlTaskReq{
		InstanceId:   archiveConfig.InstanceId,
		InstanceType: &instanceType,
		TableName:    &archiveConfig.TableName,
		DBName:       archiveConfig.Database,
		ExecSQL:      deleteSql,
		Comment:      &comment,
		SqlTaskType:  &sqlTaskType,
		SqlTaskInfo:  sqlTaskInfo,
		UserName:     &ds.User,
		Password:     &ds.Password,
	}, nil
}

func (d *DataArchiveActor) getExecuteTime(ctx context.Context) (execStartTime int64, execEndTime int64) {
	ticket, err := d.workflowDal.DescribeByTicketID(ctx, d.state.TicketId)
	if err != nil || ticket == nil {
		log.Warn(ctx, "DescribeByTicketID, error:%s", err)
		return 0, 0
	}
	ticketArchiveConfig := &model.DataArchiveConfig{}
	if strings.TrimSpace(ticket.DataArchiveConfig) != "" {
		err = json.Unmarshal([]byte(ticket.DataArchiveConfig), &ticketArchiveConfig)
		if err != nil {
			log.Warn(ctx, "ticket:%d Unmarshal  DataArchiveConfig error: %s", ticket.TicketId, err.Error())
		}
	}
	log.Info(ctx, "ticket %v is %v archive config is %v", d.state.TicketId, ticket, utils.Show(ticketArchiveConfig))
	if ticketArchiveConfig.ArchiveCycleInfo != nil {
		// 只有一次性归档,才考虑开始时间,然后根据开始时间计算结束时间
		if ticketArchiveConfig.ArchiveType == model.ArchiveType_Once {
			execStartTime = ticket.ExecutableStartTime
			// 如果此时设置了执行时长,开始计算执行时擦会给你
			if ticketArchiveConfig.ArchiveCycleInfo.GetExecuteDuration() != 0 {
				if execStartTime != 0 {
					execEndTime = execStartTime + int64(ticketArchiveConfig.ArchiveCycleInfo.GetExecuteDuration()*3600-120)
				} else {
					execEndTime = time.Now().Unix() + int64(ticketArchiveConfig.ArchiveCycleInfo.GetExecuteDuration()*3600-120)
				}
			}
			return execStartTime, execEndTime
		}
		// 周期归档,发起的时候,就已经是cron的时间了,所以只需要设置截止时间
		if ticketArchiveConfig.ArchiveCycleInfo.GetExecuteDuration() != 0 {
			// 截止时间是当前时间加上执行时长(提前2分钟截止)
			return 0, time.Now().Unix() + int64(ticketArchiveConfig.ArchiveCycleInfo.GetExecuteDuration()*3600-120)
		}
	}
	return 0, 0
}
