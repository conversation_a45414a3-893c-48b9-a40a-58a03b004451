package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"encoding/json"
	"fmt"
	"github.com/robfig/cron/v3"
	"go.uber.org/dig"
	"runtime/debug"
	"time"
)

type NewArchiveConfigActorIn struct {
	dig.In
	Repo          repository.InspectionRepo
	IdSvc         idgen.Service
	ActorClient   cli.ActorClient
	Ds            datasource.DataSourceService
	ArchiveRepo   repository.DataArchiveRepo
	WorkflowDal   dal.WorkflowDAL
	TicketService workflow.TicketService
	Cnf           config.ConfigProvider
	DbwInstance   dal.DbwInstanceDAL
}

func NewArchiveConfigActor(p NewArchiveConfigActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.DataArchiveConfigActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			newArchiveCronActor := &ArchiveConfigActor{
				state:       newDataArchiveConfigActorState(state),
				idSvc:       p.IdSvc,
				actorClient: p.ActorClient,
				ds:          p.Ds,
				archiveRepo: p.ArchiveRepo,
				workflowDal: p.WorkflowDal,
				cnf:         p.Cnf,
				dbwInstance: p.DbwInstance,
			}
			return newArchiveCronActor
		}),
	}
}

func newDataArchiveConfigActorState(bytes []byte) *ArchiveConfigActorState {
	ts := &ArchiveConfigActorState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		return ts
	}
	return ts
}

type ArchiveConfigActor struct {
	state       *ArchiveConfigActorState
	idSvc       idgen.Service
	actorClient cli.ActorClient
	ds          datasource.DataSourceService
	archiveRepo repository.DataArchiveRepo
	schedule    cron.Schedule
	workflowDal dal.WorkflowDAL
	cnf         config.ConfigProvider
	dbwInstance dal.DbwInstanceDAL
}

type ArchiveConfigActorState struct {
	ArchiveConfig          *entity.ArchiveConfig
	ConfigId               int64
	IsOpen                 bool
	NextExecuteTime        int64
	ArchiveStatus          int
	CurrentArchivingTaskId int64
}

const (
	timeIntervalSeconds uint64 = 60 * 1 // 1分钟
)

func (a *ArchiveConfigActor) GetState() []byte {
	state, _ := json.Marshal(a.state)
	return state
}

func (a *ArchiveConfigActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(time.Duration(timeIntervalSeconds) * time.Second)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		a.OnStart(ctx)
	case *shared.CreateArchiveConfig:
		a.protectUserCall(ctx, func() {
			a.createCronConfig(ctx, msg)
		})
	case *shared.FinshArchiveTask:
		a.finshTask(ctx, msg)
	case *shared.DeleteArchiveConfig:
		a.protectUserCall(ctx, func() {
			a.deleteArchiveConfig(ctx, msg)
		})
	case *shared.CloseArchiveConfig:
		a.protectUserCall(ctx, func() {
			a.closeArchiveConfig(ctx)
		})
	case *actor.ReceiveTimeout:
		a.protectUserCall(ctx, func() {
			a.doWhenIdle(ctx)
		})
	}
}

func (a *ArchiveConfigActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call cronActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (a *ArchiveConfigActor) OnStart(ctx types.Context) {
	log.Info(ctx, "ArchiveConfigActor start")
	if a.state.ConfigId == 0 {
		return
	}
	// 如果configId存在了，则说明是异常恢复，则更新一下parser
	// 1.从db获取归档配置
	if a.state.ArchiveConfig == nil || a.state.ArchiveConfig.ConfigId == 0 {
		config, err := a.archiveRepo.GetDataArchiveConfig(ctx, a.state.ConfigId)
		if err != nil {
			log.Warn(ctx, "get data-archive-config id: %d error:%s ", a.state.ConfigId, err)
			return
		}
		a.state.ArchiveConfig = config
	}
	// 2.获取下一次的归档时间
	if err := a.initCronParser(ctx, a.state.ArchiveConfig.CronStr); err != nil {
		log.Warn(ctx, "init cron error:%s", err.Error())
	}
}

func (a *ArchiveConfigActor) createCronConfig(ctx types.Context, msg *shared.CreateArchiveConfig) {
	a.state.ConfigId = msg.ArchiveConfigId
	// 1.从db获取归档配置
	config, err := a.archiveRepo.GetDataArchiveConfig(ctx, msg.ArchiveConfigId)
	if err != nil {
		log.Warn(ctx, "get data-archive-config id: %d error:%s ", msg.ArchiveConfigId, err)
		return
	}
	if a.state.ArchiveConfig == nil || a.state.ArchiveConfig.ConfigId == 0 {
		a.state.ArchiveConfig = config
	}
	// 2.获取下一次的归档时间
	if config.ArchiveType == model.ArchiveType_Once {
		a.archiveData(ctx)
		return
	}
	if err = a.initCronParser(ctx, config.CronStr); err != nil {
		log.Warn(ctx, "init cron error:%s", err.Error())
		return
	}
	a.setNextArchiveTime()
}

func (a *ArchiveConfigActor) initCronParser(ctx types.Context, cronStr string) error {
	cronParser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	schedule, err := cronParser.Parse(cronStr)
	if err != nil {
		log.Warn(ctx, "parser cron str error:%s", err.Error())
		return err
	}
	a.schedule = schedule
	a.state.IsOpen = true
	return nil
}

func (a *ArchiveConfigActor) setNextArchiveTime() {
	nextTime := a.schedule.Next(time.Now())
	a.state.NextExecuteTime = nextTime.Unix()
}

func (a *ArchiveConfigActor) closeArchiveConfig(ctx types.Context) {
	a.stopSelf(ctx, &shared.FinshArchiveTask{
		ArchiveConfigId: a.state.ConfigId,
		Success:         true,
		IsTermination:   true,
	})
}

func (a *ArchiveConfigActor) deleteArchiveConfig(ctx types.Context, msg *shared.DeleteArchiveConfig) {
	// TODO

}

func (a *ArchiveConfigActor) doWhenIdle(ctx types.Context) {
	if !a.state.IsOpen {
		// 如果配置已经close了，就把actor关了
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}

	if a.state.ArchiveStatus == Archiving || a.state.ArchiveConfig.ArchiveType == model.ArchiveType_Once {
		// 如果当前正在归档中 或者是单次调度，那什么也不干
		return
	}

	// 获取当前时间
	now := time.Now().Unix()
	if now >= a.state.NextExecuteTime {
		// 如果当前已经超过了‘下一次’的执行时间 & config再等待归档中 & 当前没有下面在执行的任务，则发起归档
		a.archiveData(ctx)
	}
}

func (a *ArchiveConfigActor) archiveData(ctx types.Context) {
	archiveTaskId, err := a.createArchiveTask(ctx)
	if err != nil {
		log.Warn(ctx, "create archiveTask error %s", err.Error())
		return
	}
	_ = a.archiveRepo.UpdateLastArchiveStatus(ctx, a.state.ConfigId, model.ArchiveConfigStatus_Execute)
	err = a.actorClient.KindOf(consts.DataArchiveActorKind).
		Send(ctx, fmt.Sprintf("%d", archiveTaskId), &shared.DataArchive{ArchiveTaskId: archiveTaskId, TicketId: a.state.ArchiveConfig.TicketId, IsOnce: true})
	if err != nil {
		log.Warn(ctx, "send DataArchiveActor to DataArchive error: %s", err.Error())
		_ = a.archiveRepo.DeleteArchiveTask(ctx, archiveTaskId)
		return
	}
	a.state.CurrentArchivingTaskId = archiveTaskId
	a.state.ArchiveStatus = Archiving
	a.state.IsOpen = true
	log.Info(ctx, "configId: %d, start dataArchive task is :%d", a.state.ConfigId, archiveTaskId)
}

func (a *ArchiveConfigActor) createArchiveTask(ctx context.Context) (int64, error) {
	archiveTaskId, err := a.idSvc.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "createArchiveTask error, can't get id: %s", err.Error())
		return 0, fmt.Errorf("reate ticket failed, generate ticket ID failed")
	}
	archiveConfig := a.state.ArchiveConfig
	archiveTask := &entity.ArchiveTask{
		TaskId:          archiveTaskId,
		ArchiveConfigId: a.state.ConfigId,
		IsBackUp:        archiveConfig.IsBackUp,
		InstanceId:      archiveConfig.InstanceId,
		InstanceType:    archiveConfig.InstanceType,
		Database:        archiveConfig.Database,
		Table:           archiveConfig.TableName,
		TenantId:        archiveConfig.TenantId,
		CreateTime:      time.Now().Unix(),
		UpdateTime:      time.Now().Unix(),
		TicketId:        archiveConfig.TicketId,
		UserName:        a.GetUserName(ctx, archiveConfig),
	}
	err = a.archiveRepo.CreateArchiveTask(ctx, archiveTask)
	if err != nil {
		log.Warn(ctx, "createArchiveTask error: %s", err.Error())
		return 0, fmt.Errorf("create createArchive task failed")
	}
	return archiveTaskId, nil
}

func (a *ArchiveConfigActor) finshTask(ctx types.Context, finishedTask *shared.FinshArchiveTask) {
	if finishedTask.Success {
		_ = a.archiveRepo.UpdateLastArchiveStatus(ctx, a.state.ConfigId, model.ArchiveConfigStatus_Success)
	} else {
		_ = a.archiveRepo.UpdateLastArchiveStatus(ctx, a.state.ConfigId, model.ArchiveConfigStatus_Fail)
	}
	if a.state.ArchiveConfig.ArchiveType == model.ArchiveType_Once {
		// 如果是单次的，就关闭自己
		a.stopSelf(ctx, finishedTask)
		return
	}
	a.state.ArchiveStatus = WaitArchive
	a.state.NextExecuteTime = a.schedule.Next(time.Now()).Unix()
}

func (a *ArchiveConfigActor) stopSelf(ctx types.Context, finishedTask *shared.FinshArchiveTask) {
	a.state.IsOpen = false
	// 1.关闭配置
	_ = a.archiveRepo.CloseArchiveConfig(ctx, a.state.ConfigId)
	// 2.如果有正在执行的任务，把正在执行的任务也关了
	if a.state.ArchiveStatus == Archiving && a.state.CurrentArchivingTaskId != 0 {
		err := a.actorClient.KindOf(consts.DataArchiveActorKind).
			Send(ctx, fmt.Sprintf("%d", a.state.CurrentArchivingTaskId), &shared.StopArchive{})
		if err != nil {
			log.Warn(ctx, "configId %d, taskId:%d send actor error:%s", a.state.ConfigId, a.state.CurrentArchivingTaskId)
		}
	}
	// 3.关工单
	a.UpdateTicketStatus(ctx, finishedTask)
	// 4.等ArchiveActor进行回调，到时候由do when idl关闭actor
}

func (a *ArchiveConfigActor) UpdateTicketStatus(ctx types.Context, finishedTask *shared.FinshArchiveTask) {

	ticket := &dao.Ticket{TicketId: a.state.ArchiveConfig.TicketId}

	if finishedTask.IsTermination {
		ticket.TicketStatus = int8(model.TicketStatus_TicketTermination)
	} else if !finishedTask.Success {
		ticket.TicketStatus = int8(model.TicketStatus_TicketError)
		ticket.Description = finishedTask.ErrMsg
	} else {
		ticket.TicketStatus = int8(model.TicketStatus_TicketFinished)
	}
	err := a.workflowDal.UpdateWorkStatus(ctx, ticket)
	if err != nil {
		log.Warn(ctx, "ticketId: %d UpdateWorkStatus error :%s", a.state.ArchiveConfig.TicketId, err.Error())
	}
}

func (a *ArchiveConfigActor) GetUserName(ctx context.Context, archiveConfig *entity.ArchiveConfig) string {
	// 这块需要实例管理dbw_instance的表记录
	instanceInfo, err := a.dbwInstance.Get(ctx, archiveConfig.InstanceId, archiveConfig.InstanceType, shared.Volc.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		log.Warn(ctx, "ticket %v: get Instance %s info error:%s", archiveConfig.TicketId, archiveConfig.InstanceId, err.Error())
		return ""
	}
	return instanceInfo.DatabaseUser
}
