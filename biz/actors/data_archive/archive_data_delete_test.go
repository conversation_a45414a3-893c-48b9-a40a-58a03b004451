package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/repository"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/sqltask"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestDataArchiveDeleteData(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*DataArchiveActor).deleteData).Return(fmt.Errorf("test")).Build()
	actor.DataArchiveDeleteData(&mocks.MockContext{})
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*DataArchiveActor).deleteData).Return(nil).Build()
	defer mock5.UnPatch()
	actor.DataArchiveDeleteData(&mocks.MockContext{})
}

func TestDeleteData(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer basicMock4.UnPatch()

	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Finished
	err := actor.deleteData(&mocks.MockContext{})
	assert.Nil(t, err)

	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Delete
	actor.state.ArchiveTask.SqlTaskId = "134"
	err = actor.deleteData(&mocks.MockContext{})
	assert.Nil(t, err)

	actor.state.ArchiveTask.SqlTaskId = ""
	mock1 := mockey.Mock((*DataArchiveActor).createDeleteSqlTask).Return(fmt.Errorf("test")).Build()
	defer mock1.UnPatch()
	err = actor.deleteData(&mocks.MockContext{})
	assert.NotNil(t, err)
}

func TestCreateDeleteSqlTask(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer basicMock4.UnPatch()

	actor.state.ArchiveConfig.InstanceType = model.InstanceType_MySQLSharding.String()
	mock1 := mockey.Mock(FormatDeleteSqlWhereCase).Return("").Build()
	defer mock1.UnPatch()
	mock6 := mockey.Mock((*DataArchiveActor).getDatasource).Return(&shared.DataSource{}, nil).Build()
	defer mock6.UnPatch()
	mock61 := mockey.Mock((*DataArchiveActor).getExecuteTime).Return(0, 0).Build()
	defer mock61.UnPatch()

	taskId := "123"
	mock2 := mockey.Mock((*sqltask.MockSqlTaskService).CreateFreeLockDMLSqlTask).Return(&taskId, fmt.Errorf("test")).Build()
	err := actor.createDeleteSqlTask(&mocks.MockContext{})
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*sqltask.MockSqlTaskService).CreateFreeLockDMLSqlTask).Return(&taskId, nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*repository.MockDataArchiveRepo).UpdateTaskInfo).Return(fmt.Errorf("test")).Build()
	err = actor.createDeleteSqlTask(&mocks.MockContext{})
	assert.NotNil(t, err)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*repository.MockDataArchiveRepo).UpdateTaskInfo).Return(nil).Build()
	defer mock5.UnPatch()
	err = actor.createDeleteSqlTask(&mocks.MockContext{})
	assert.Nil(t, err)

}
