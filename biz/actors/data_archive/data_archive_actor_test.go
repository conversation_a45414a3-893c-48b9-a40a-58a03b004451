package data_archive

import (
	config2 "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/repository"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestOnStart(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*DataArchiveActor).createTlsClient).Return(nil).Build()
	defer mock1.UnPatch()

	actor.OnStart(&mocks.MockContext{})
}

func TestFormatTlsLogs(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	mock3 := mockey.Mock((*config.MockConfigProvider).Get).Return(&config2.Config{}).Build()
	defer mock3.UnPatch()
	actor.formatTlsLogs(&mocks.MockContext{}, "aaa")
}

func TestClose(t *testing.T) {
	actor := mockDataArchiveActor()
	actor.close()
}

func TestStartDataArchive(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*DataArchiveActor).setActorBasicInfo).Return(fmt.Errorf("test")).Build()
	actor.startDataArchive(&mocks.MockContext{}, &shared.DataArchive{})
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*DataArchiveActor).setActorBasicInfo).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*DataArchiveActor).setNowTime).Return(fmt.Errorf("test")).Build()
	actor.startDataArchive(&mocks.MockContext{}, &shared.DataArchive{})
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*DataArchiveActor).setNowTime).Return(nil).Build()
	defer mock4.UnPatch()
	actor.startDataArchive(&mocks.MockContext{}, &shared.DataArchive{})
}

func TestSetActorBasicInfo(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*repository.MockDataArchiveRepo).GetDataArchiveTask).Return(&entity.ArchiveTask{}, fmt.Errorf("test")).Build()
	err := actor.setActorBasicInfo(&mocks.MockContext{}, 123)
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*repository.MockDataArchiveRepo).GetDataArchiveTask).Return(&entity.ArchiveTask{}, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*repository.MockDataArchiveRepo).GetDataArchiveConfig).Return(&entity.ArchiveConfig{}, fmt.Errorf("test")).Build()
	err = actor.setActorBasicInfo(&mocks.MockContext{}, 123)
	assert.NotNil(t, err)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*repository.MockDataArchiveRepo).GetDataArchiveConfig).Return(&entity.ArchiveConfig{}, nil).Build()
	defer mock4.UnPatch()
	err = actor.setActorBasicInfo(&mocks.MockContext{}, 123)
	assert.Nil(t, err)
}

func TestGetTicketArchiveConfig(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{}, fmt.Errorf("test")).Build()
	_, err := actor.getTicketArchiveConfig(&mocks.MockContext{})
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{}, nil).Build()
	_, err = actor.getTicketArchiveConfig(&mocks.MockContext{})
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{DataArchiveConfig: "{}"}, nil).Build()
	defer mock3.UnPatch()
	_, err = actor.getTicketArchiveConfig(&mocks.MockContext{})
	assert.Nil(t, err)
}

func TestSetNowTime(t *testing.T) {
	actor := mockDataArchiveActor()
	actor.state.ArchiveTask = &entity.ArchiveTask{}
	_ = actor.setNowTime()
}

func TestStopArchiveTask(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock3 := mockey.Mock((*DataArchiveActor).killTask).Return().Build()
	defer mock3.UnPatch()

	actor.stopArchiveTask(&mocks.MockContext{})
}

func TestNewDataArchiveActor(t *testing.T) {
	NewDataArchiveActor(DataArchiveActorIn{})
	_ = newDataArchiveActorState([]byte("{}"))
	_ = newDataArchiveActorState([]byte("{"))
}
