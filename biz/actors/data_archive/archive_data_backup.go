package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_archive/data_backup"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"fmt"
)

func (d *DataArchiveActor) DataArchiveBackUpData(ctx types.Context) {
	if err := d.backUp(ctx); err != nil {
		log.Warn(ctx, "backUp error :%s", err.Error())
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: err.Error()})
		return
	}
	ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
}

func (d *DataArchiveActor) backUp(ctx types.Context) error {
	if d.state.ArchiveTask.TaskStatus > model.ArchiveTaskStatus_BackUp {
		return nil
	}
	d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_BackUp
	if err := d.archiveRepo.UpdateTaskInfo(ctx, d.state.ArchiveTask); err != nil {
		log.Warn(ctx, "UpdateTaskInfo error:%s", err)
		return err
	}
	if !d.state.ArchiveConfig.IsBackUp {
		// d.sendLogs(ctx, "[back-up] config is no backup, ignore")
		return nil
	}
	d.sendLogs(ctx, "[back-up] start data backup")

	req, err := d.formatTosStartBackUpReq(ctx)
	if err != nil {
		d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Error
		d.state.ArchiveTask.ErrorMsg = fmt.Sprintf("Start BackUpTask error :%s", err.Error())
		log.Warn(ctx, "formatTosStartBackUpReq error: %s", err.Error())
		return err
	}

	backupResponse, err := d.backupService.StartBackUp(ctx, req)
	if err != nil {
		d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_Error
		d.state.ArchiveTask.ErrorMsg = fmt.Sprintf("Start BackUpTask error :%s", err.Error())
		log.Warn(ctx, "StartBackUp error :%s", err.Error())
		return fmt.Errorf("start BackUpTask error :%s", err.Error())
	}
	d.state.ArchiveTask.BackupTaskId = backupResponse.TaskId
	d.state.ArchiveTask.SubSessionId = backupResponse.SessionId
	log.Info(ctx, "start back up task, taskId:%s, sessionId:%s", backupResponse.TaskId, backupResponse.SessionId)
	if err = d.archiveRepo.UpdateTaskInfo(ctx, d.state.ArchiveTask); err != nil {
		log.Warn(ctx, "UpdateTaskInfo error:%s", err)
	}
	return nil
}

func (d *DataArchiveActor) formatTosStartBackUpReq(ctx types.Context) (data_backup.StartBackUpReq, error) {
	switch d.state.ArchiveConfig.BackUpType {
	case model.BackUpType_Tos:
		return d.formatStartBackUpReq(ctx)
	}
	return data_backup.StartBackUpReq{}, fmt.Errorf("not support back up type:%s", d.state.ArchiveConfig.BackUpType)
}

func (d *DataArchiveActor) formatStartBackUpReq(ctx types.Context) (data_backup.StartBackUpReq, error) {
	dsType, err := model.DSTypeFromString(d.state.ArchiveConfig.InstanceType)
	if err != nil {
		return data_backup.StartBackUpReq{}, consts.ErrorOf(model.ErrorCode_NotSupportInstanceType)
	}

	backUpSql := fmt.Sprintf("select * from `%s`.`%s`  %s ", d.state.ArchiveConfig.Database, d.state.ArchiveConfig.TableName, FormatDeleteSqlWhereCase(d.state.ArchiveTask.ExecTime, d.state.ArchiveConfig))

	config := d.cnf.Get(ctx)

	return data_backup.StartBackUpReq{
		Type: model.BackUpType_Tos,
		TosConfig: data_backup.TosBackUpConfig{
			TosBucket: config.ArchiveBackupTosBucket,
			SqlText:   backUpSql,
		},
		Db:            d.state.ArchiveConfig.Database,
		TableName:     d.state.ArchiveConfig.TableName,
		InstanceId:    d.state.ArchiveConfig.InstanceId,
		DsType:        dsType,
		TenantId:      d.state.ArchiveConfig.TenantId,
		TicketId:      fmt.Sprintf("%d", d.state.ArchiveTask.TicketId),
		ArchiveTaskId: fmt.Sprintf("%d", d.state.ArchiveTask.TaskId),
	}, nil
}
