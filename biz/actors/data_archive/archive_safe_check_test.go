package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/repository"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestDataArchiveSafeCheck(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*DataArchiveActor).safeCheck).Return(fmt.Errorf("test")).Build()
	actor.DataArchiveSafeCheck(&mocks.MockContext{})
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*DataArchiveActor).safeCheck).Return(nil).Build()
	defer mock5.UnPatch()
	actor.DataArchiveSafeCheck(&mocks.MockContext{})
}

func TestSafeCheck(t *testing.T) {
	actor := mockDataArchiveActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock1 := mockey.Mock((*DataArchiveActor).sendLogs).Return().Build()
	defer basicMock1.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_DataCheck
	err := actor.safeCheck(&mocks.MockContext{})
	assert.Nil(t, err)

	actor.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_SafeCheck
	mock1 := mockey.Mock((*repository.MockDataArchiveRepo).UpdateTaskInfo).Return(fmt.Errorf("test")).Build()
	err = actor.safeCheck(&mocks.MockContext{})
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*repository.MockDataArchiveRepo).UpdateTaskInfo).Return(nil).Build()
	defer mock2.UnPatch()
	err = actor.safeCheck(&mocks.MockContext{})
	assert.Nil(t, err)
	actor.state.ArchiveConfig.IsBackUp = true
	err = actor.backUp(&mocks.MockContext{})
	//	assert.Nil(t, err)
}
