package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
)

func (d *DataArchiveActor) DataArchiveDataCheck(ctx types.Context) {
	if err := d.DataCheck(ctx); err != nil {
		log.Warn(ctx, "DataCheck error :%s", err.Error())
		ctx.Send(ctx.Self(), &shared.ArchiveError{ErrorMsg: err.Error()})
		return
	}
	ctx.Send(ctx.Self(), &actor.ReceiveTimeout{})
}

func (d *DataArchiveActor) DataCheck(ctx types.Context) error {
	if d.state.ArchiveTask.TaskStatus > model.ArchiveTaskStatus_DataCheck {
		return nil
	}
	d.state.ArchiveTask.TaskStatus = model.ArchiveTaskStatus_DataCheck
	if err := d.archiveRepo.UpdateTaskInfo(ctx, d.state.ArchiveTask); err != nil {
		log.Warn(ctx, "UpdateTaskInfo error:%s", err)
		return err
	}
	if !d.state.ArchiveConfig.IsBackUp {
		// d.sendLogs(ctx, "[data-check] config is no backup, needn't data check, ignore")
		return nil
	}
	d.sendLogs(ctx, "[data-check] start data check")
	// TODO 补充数据核对逻辑
	return nil
}
