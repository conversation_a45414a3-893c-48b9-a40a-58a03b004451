package c3

import (
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"time"

	"code.byted.org/cloud-fe/cloud_config_center/pkg/c3"
	"code.byted.org/infcs/dbw-mgr/biz/com"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	c3Svc "code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"

	"go.uber.org/dig"
)

type NewApolloActorIn struct {
	dig.In
	C3f            c3Svc.ClientFactory
	SecretProvider com.K8sSecretProvider
}

func NewApolloActor(p NewApolloActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.ApolloActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			actor := &apolloActor{
				c3f:      p.C3f,
				sp:       p.SecretProvider,
				clients:  make(map[string]c3.Client),
				watchers: make(map[string]*actor.PID),
				namespaces: []string{
					consts.C3ApplicationNamespace,
					consts.C3PrivateNamespace,
				},
			}
			return actor
		}),
	}
}

type apolloActor struct {
	c3f        c3Svc.ClientFactory
	clients    map[string]c3.Client
	watchers   map[string]*actor.PID
	namespaces []string
	sp         com.K8sSecretProvider
}

func (a *apolloActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(5 * time.Minute)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		a.initC3Client(ctx, msg)
	case *actor.Stopping:
		a.stopC3Client(ctx, msg)
	case *shared.AreYouOK:
		a.respOK(ctx, msg)
	case *actor.Terminated:
		a.onChildTerminated(ctx, msg)
	case *actor.ReceiveTimeout:
		a.onTimeoutEvent(ctx, msg)
	}
}

func (a *apolloActor) initC3Client(ctx types.Context, msg *actor.Started) {
	for _, ns := range a.namespaces {
		a.watchNamespaceHelper(ctx, ns)
	}
}

// 1. 从secret读配置然后Publish
func (a *apolloActor) publishNamespaceFromSecret(ctx types.Context, ns string) {
	secretName := MakeSecretName(ns)
	data, err := a.sp.GetSecretStrData(ctx, consts.C3ConfigSecretNamespace, secretName)
	if err != nil {
		log.Warn(ctx, "Get Secret Data Failed err: %v", err)
		return
	}

	config, err := json.Marshal(data)
	if err != nil {
		log.Warn(ctx, "Marshal Secret Data [%v] err: %v", data, err)
		return
	}

	ctx.ActorSystem().EventStream.Publish(&shared.C3ConfigRefresh{
		NamespaceName: ns,
		NewConfig:     utils.Bytes2String(config),
	})
}

func (a *apolloActor) stopC3Client(ctx types.Context, msg *actor.Stopping) {
	for _, cli := range a.clients {
		if cli != nil {
			cli.Close()
		}
	}
}

func (a *apolloActor) respOK(ctx types.Context, msg *shared.AreYouOK) {
	if ctx.Sender() != nil {
		ctx.Respond(&shared.OK{})
	}
}

func (a *apolloActor) onChildTerminated(ctx types.Context, msg *actor.Terminated) {
	who := msg.Who
	log.Warn(ctx, "Watch Actor [%s] is Terminated", who)
	// TODO:
	// a.watchers[]
}

func (a *apolloActor) watchNamespaceHelper(ctx types.Context, namespace string) {
	var cli c3.Client
	var watcher *actor.PID
	var err error

	if cli = a.clients[namespace]; cli == nil {
		cli, err = a.c3f.New(namespace)
	}
	if err != nil {
		a.clients[namespace] = nil
		log.Warn(ctx, "Create Client for Namespace [%s] Failed err: %v", namespace, err)
		a.publishNamespaceFromSecret(ctx, namespace)
		return
	}
	a.clients[namespace] = cli

	if watcher = a.watchers[namespace]; watcher == nil {
		watcher = a.startWatch(ctx, cli, namespace)
	}
	a.watchers[namespace] = watcher
}

func (a *apolloActor) startWatch(ctx types.Context, cli c3.Client, namespace string) *actor.PID {
	ch := cli.Watch(ctx, namespace)
	props := actor.PropsFromProducer(func() actor.Actor {
		return &watchActor{
			ch:        ch,
			namespace: namespace,
			sp:        a.sp,
		}
	})
	log.Info(ctx, "Start Watch Namespace [%s]", namespace)
	return ctx.Spawn(props)
}

// 2. 超时时间发生时尝试重新建立client同时读取secret并更新
func (a *apolloActor) onTimeoutEvent(ctx types.Context, msg *actor.ReceiveTimeout) {
	// NOTE: 目前可以仅考虑application Namespace
	ns := consts.C3ApplicationNamespace
	if cli, ok := a.clients[ns]; !ok || cli == nil {
		log.Info(ctx, "Timeout: Trying Re-Connect to C3 for Namespace [%s]", ns)
		a.watchNamespaceHelper(ctx, ns)
	} else {
		a.publishNamespaceFromSecret(ctx, ns)
	}
}
