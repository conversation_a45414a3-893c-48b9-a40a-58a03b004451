package c3

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/com"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/lib-mgr-common/pkg/icc"

	"code.byted.org/infcs/protoactor-go/actor"
)

type watchActor struct {
	ch        <-chan icc.Event
	namespace string
	sp        com.K8sSecretProvider
}

const (
	timeout = 3 * time.Second
)

func (a *watchActor) Receive(ctx actor.Context) {
	sysCtx := context.Background()
	a.Process(types.BuildMyContext(sysCtx, ctx, nil))
}

func (a *watchActor) Process(ctx types.Context) {
	switch ctx.Message().(type) {
	case *actor.Started:
		a.doPoll(ctx)
		ctx.SetReceiveTimeout(timeout)
	case *actor.ReceiveTimeout:
		a.doPoll(ctx)
		ctx.SetReceiveTimeout(timeout)
	case *actor.Stopping:
		log.Info(ctx, "Watch Actor [%s] is Stopping", a.namespace)
	}
}

func (a *watchActor) doPoll(ctx types.Context) {
	timer := time.NewTimer(5 * time.Second)
	defer timer.Stop()
	for {
		select {
		case event := <-a.ch:
			log.Info(ctx, "Namespace [%s] receive value %", a.namespace, utils.Show(event))
			a.onUpdate(ctx, event)
		case <-timer.C:
			return
		}
	}
}

func (a *watchActor) onUpdate(ctx types.Context, event icc.Event) {
	configCache := event.Config
	config := make(map[string]string)
	if configCache == nil {
		return
	}
	configCache.Range(func(key string, ival icc.Value) bool {
		config[key] = ival.String()
		return true
	})

	config_, err := json.Marshal(config)
	if err != nil {
		log.Warn(ctx, "Marshal config [%v] err %v", config, err)
		return
	}

	ctx.ActorSystem().EventStream.Publish(&shared.C3ConfigRefresh{
		NamespaceName: event.Namespace,
		NewConfig:     utils.Bytes2String(config_),
	})

	a.writeSecret(ctx, config)
}

func (a *watchActor) writeSecret(ctx types.Context, info map[string]string) {
	if len(info) == 0 {
		return
	}

	f := func(secretNamespace string, c3Namespace string, config map[string]string) {
		secretName := MakeSecretName(c3Namespace)
		a.sp.PutSecretStrData(ctx, secretNamespace, secretName, config)
	}

	f(consts.C3ConfigSecretNamespace, a.namespace, info)
	if a.namespace == consts.C3PrivateNamespace {
		f(consts.MgrNamespace, a.namespace, info)
	}
}
