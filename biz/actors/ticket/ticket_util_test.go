package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	utils2 "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/opcode"
	"github.com/bytedance/mockey"
	"testing"
	"time"
)

func TestClearSensitiveInfoInCommandItems(t *testing.T) {
	var cmd = []*model.CommandItem{
		{ReasonDetail: utils.StringRef("xxx")},
		{ReasonDetail: utils.StringRef("xxx")},
	}
	got := clearSensitiveInfoInCommandItems(cmd)
	if got == nil {
		t.Fatal("error")
	}
}

func TestGenerateSQLTaskName(t *testing.T) {
	ticket := &dao.Ticket{
		TenantId:   "1",
		InstanceId: "1",
		TaskId:     "1",
	}
	got := generateSQLTaskName(ticket)
	if got != "1|1|1" {
		t.Fatal("error")
	}
}

func TestCalcuTimeBeforeExecute(t *testing.T) {
	got := calcuTimeBeforeExecute(time.Now().UnixMilli(), 1, 2)
	if got == 0 {
		t.Fatal("error")
	}
}

func TestGetCronTime(t *testing.T) {
	got := getCronTime(time.Now().Unix())
	if got == "" {
		t.Fatal("error")
	}
}

func TestTicketRecognizeRdsMysqlError(t *testing.T) {
	errMsg := "code=345012 status=PrecheckFailed_DuplicateKeyOrColumn msg=The operation is not permitted due to exist duplicate key or column."
	got := GetTicketErrDesc(errMsg)
	if got == "" {
		t.Fatal("error")
	}
}

func TestTicketRecognizeRdsMysqlError_Err(t *testing.T) {
	errMsg := "code=3450122222 status=PrecheckFailed_KeyOrColumn msg=The operation is not permitted due to exist duplicate key or column."
	got := GetTicketErrDesc(errMsg)
	if got == "" {
		t.Fatal("error")
	}
}

func TestTicketRecognizeRdsMysqlError_Err1(t *testing.T) {
	errMsg := ""
	got := GetTicketErrDesc(errMsg)
	if got == "" {
		t.Fatal("error")
	}
}

func TestExtractOperator(t *testing.T) {
	op := opcode.And
	got := extractOperator(op)
	if got == "" {
		t.Fatal("error")
	}

	op1 := opcode.Op(50)
	got1 := extractOperator(op1)
	if got1 != "" {
		t.Fatal("error")
	}
}

func TestGetTables(t *testing.T) {
	p := parser.New()
	stmts, _, _ := p.Parse("select * from tbl", "utf8", "")
	got, _ := GetTables(stmts[0])
	if len(got) != 0 {
		t.Fatal("select error")
	}
	baseMock1 := mockey.Mock(utils2.CheckFreeLockDMLLimit).Return([]string{"tbl"}, nil).Build()
	defer baseMock1.UnPatch()
	stmts1, _, _ := p.Parse("update tbl set id=1 ", "utf8", "")
	got1, _ := GetTables(stmts1[0])
	if len(got1) == 0 {
		t.Fatal("update error")
	}
}

func TestGenerateGetMaxIndexSQL(t *testing.T) {
	// 0、解析SQL
	p := parser.New()
	stmts, _, _ := p.Parse("select * from tbl", "utf8", "")
	res := GenerateGetMaxIndexSQL("tbl", []*datasource.TableIndexInfo{{
		ColumnName: "test",
	}}, stmts[0])
	if res == "" {
		t.Fatal("error")
	}
}

func TestGenerateGetMinIndexSQL(t *testing.T) {
	p := parser.New()
	stmts, _, _ := p.Parse("select * from tbl", "utf8", "")
	res := GenerateGetMinIndexSQL("tbl", []*datasource.TableIndexInfo{{
		ColumnName: "test",
	}}, stmts[0])
	if res == "" {
		t.Fatal("error")
	}
}

func TestGenerateGetMinIndexSQLForDelete(t *testing.T) {
	p := parser.New()
	stmts, _, _ := p.Parse("select * from tbl", "utf8", "")
	res := GenerateGetMinIndexSQLForDelete("tbl", []*datasource.TableIndexInfo{{
		ColumnName: "test",
	}}, stmts[0])
	if res == "" {
		t.Fatal("error")
	}
}

func TestGenerateColumnList(t *testing.T) {
	GenerateColumnList([]*datasource.TableIndexInfo{
		{TableName: "a"},
	})
}

func TestGenerateFreeLockExecInfo(t *testing.T) {
	res := GenerateFreeLockExecInfo(
		[]*IndexInfo{{
			IndexName:  "id",
			IndexValue: "1",
		}},
		[]*IndexInfo{{
			IndexName:  "age",
			IndexValue: "10"},
		})
	if len(res) == 0 {
		t.Fatal("error")
	}
}

func TestGenerateFreeLockExecInfoForDelete(t *testing.T) {
	res := GenerateFreeLockExecInfoForDelete(
		[]*IndexInfo{{
			IndexName:  "id",
			IndexValue: "1",
		}})
	if len(res) == 0 {
		t.Fatal("error")
	}
}

func TestGetPkOrUniqKeyStr(t *testing.T) {
	res := getPkOrUniqKeyStr([]*datasource.TableIndexInfo{
		{
			IndexName:  "age",
			ColumnName: "age",
		},
	})
	if res == "" {
		t.Fatal("error")
	}
}

func TestGetPkOrUniqKeyOrderByStr(t *testing.T) {
	res := getPkOrUniqKeyOrderByStr([]*datasource.TableIndexInfo{
		{
			IndexName:  "age",
			ColumnName: "age",
		},
	})
	if res == "" {
		t.Fatal("error")
	}
}

func TestGetIndexMaxCondition(t *testing.T) {
	res := getIndexMaxCondition([]*FreeLockExecInfo{
		{IndexName: "age", MaxValue: "1"},
		{IndexName: "id", MaxValue: "1"},
	}, 0)
	if res == "" {
		t.Fatal("error")
	}

	res1 := getIndexMaxCondition([]*FreeLockExecInfo{
		{IndexName: "age", MaxValue: "1"},
		{IndexName: "id", MaxValue: "1"},
	}, 5)
	if res1 != "" {
		t.Fatal("error")
	}
}

func TestGetIndexLeftCondition(t *testing.T) {
	res := getIndexLeftCondition([]*FreeLockExecInfo{
		{IndexName: "age", BatchLeft: "1"},
		{IndexName: "id", BatchLeft: "1"},
	}, 0)
	if res == "" {
		t.Fatal("error")
	}

	res1 := getIndexLeftCondition([]*FreeLockExecInfo{
		{IndexName: "age", BatchLeft: "1"},
		{IndexName: "id", BatchLeft: "1"},
	}, 5)
	if res1 != "" {
		t.Fatal("error")
	}
}

func TestGetIndexRightCondition(t *testing.T) {
	res := getIndexRightCondition([]*FreeLockExecInfo{
		{IndexName: "age", BatchRight: "1"},
		{IndexName: "id", BatchRight: "1"},
	}, 0)
	if res == "" {
		t.Fatal("error")
	}

	res1 := getIndexRightCondition([]*FreeLockExecInfo{
		{IndexName: "age", BatchRight: "1"},
		{IndexName: "id", BatchRight: "1"},
	}, 5)
	if res1 != "" {
		t.Fatal("error")
	}
}

func TestGetIndexResult(t *testing.T) {
	res := getIndexResult("1 or 2 or 3")
	if res == "" {
		t.Fatal("error")
	}
}

func TestIsSliceContainsElement(t *testing.T) {
	res := IsSliceContainsElement([]string{"1", "2"}, "1")
	if !res {
		t.Fatal("error")
	}

	res2 := IsSliceContainsElement([]string{"1", "2"}, "3")
	if res2 {
		t.Fatal("error")
	}
}

func TestGetWhereCondition(t *testing.T) {
	p := parser.New()
	stmts, _, _ := p.Parse("update tbl set id=1 where id=1 and age between 1 and 2 ", "utf8", "")
	res := GetWhereCondition(stmts[0])
	if res == "" {
		t.Fatal("update error")
	}

	stmts1, _, _ := p.Parse("delete from tbl where id=1", "utf8", "")
	res1 := GetWhereCondition(stmts1[0])
	if res1 == "" {
		t.Fatal("delete error")
	}

	stmts3, _, _ := p.Parse("insert into test2 select * from tbl where id=1", "utf8", "")
	res3 := GetWhereCondition(stmts3[0])
	if res3 == "" {
		t.Fatal("insert error")
	}

}
