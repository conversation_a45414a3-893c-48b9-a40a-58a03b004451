package ticket

import (
	"fmt"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/parser"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/sqltask"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"go.uber.org/dig"
)

type FreeLockTicketActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *FreeLockTicketActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *FreeLockTicketActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestFreeLockTicketActorSuite(t *testing.T) {
	suite.Run(t, new(FreeLockTicketActorSuite))
}

func (suite *FreeLockTicketActorSuite) TestGetState() {
	FreeLockDMLActor := FreeLockDMLActor{}
	ret := FreeLockDMLActor.GetState()
	suite.NotEmpty(ret)
}

func (suite *FreeLockTicketActorSuite) TestNewFreeLockTicketActor() {
	ret := NewFreeLockDMLActor(FreeLockDMLActorIn{
		In:   dig.In{},
		Conf: &config.MockConfigProvider{},
	})
	suite.NotEmpty(ret)
}

func (suite *FreeLockTicketActorSuite) TestProcessStarted() {
	//Ctx := mocks.NewMockContext(suite.ctrl)
	//
	//Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	//Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	//Ctx.EXPECT().Message().Return(&actor.Started{}).Times(1)
	//Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	//Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	//Ctx.EXPECT().GetName().AnyTimes()
	//
	//FreeLockDMLActor := NewFreeLockDMLActor(FreeLockDMLActorIn{
	//	In: dig.In{},
	//}).Producer.Spawn(consts.FreeLockDMLActorKind, consts.SingletonActorName, []byte("\"log_id\":1"))
	//FreeLockDMLActor.GetState()
	//FreeLockDMLActor.Process(Ctx)
}

func (suite *FreeLockTicketActorSuite) TestExecFreeLockDMLTicket() {
	//Ctx := mocks.NewMockContext(suite.ctrl)
	//
	//Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	//Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	//Ctx.EXPECT().Message().Return(&shared.ExecFreeLockDMLTicket{}).Times(1)
	//Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	//Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	//Ctx.EXPECT().GetName().AnyTimes()
	//Ctx.EXPECT().Self().AnyTimes()
	//Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()
	//
	//baseMock1 := mockey.Mock(log.Log).Return().Build()
	//defer baseMock1.UnPatch()
	//
	//FreeLockDMLActor := NewFreeLockDMLActor(FreeLockDMLActorIn{
	//	In: dig.In{},
	//}).Producer.Spawn(consts.FreeLockDMLActorKind, consts.SingletonActorName, []byte("\"log_id\":1"))
	//FreeLockDMLActor.GetState()
	//FreeLockDMLActor.Process(Ctx)
	//
	//Ctx.EXPECT().Message().Return(&shared.CreateSession{}).Times(1)
	//FreeLockDMLActor.Process(Ctx)
	//
	//Ctx.EXPECT().Message().Return(&shared.ExecuteCommand{}).Times(1)
	//FreeLockDMLActor.Process(Ctx)
	//
	//Ctx.EXPECT().Message().Return(&actor.ReceiveTimeout{}).Times(1)
	//FreeLockDMLActor.Process(Ctx)
	//
	//Ctx.EXPECT().Message().Return(&actor.Stopped{}).Times(1)
	//FreeLockDMLActor.Process(Ctx)

}

// mock一个actor
func mockFreeLockDMLTicketActor() *FreeLockDMLActor {
	return &FreeLockDMLActor{
		state:          &FreeLockDMLActorState{CurrentAction: model.FreeLockDMLTicketAction_ExecuteFinished, TenantId: "1", UserId: "1"},
		cnf:            &config.MockConfigProvider{},
		workflowDal:    &mocks.MockWorkflowDAL{},
		sp:             &mocks.MockCommandParser{},
		idgenSvc:       &mocks.MockService{},
		cmdRepo:        &mocks.MockCommandRepo{},
		ticketService:  &mocks.MockTicketService{},
		sqlTaskSvc:     &sqltask.MockSqlTaskService{},
		ds:             &mocks.MockDataSourceService{},
		c3ConfProvider: &mocks.MockC3ConfigProvider{},
		crRepo:         &mocks.MockCommandResultRepo{},
	}
}

func TestNewFreeLockDMLActorState(t *testing.T) {
	res := newFreeLockDMLActorState([]byte("\"log_id\":1,\"tenant_id\":1,\"user_id\":1"))
	if res != nil {
		t.Fatal("error")
	}
}

func TestGetTableIndexBorderValueForDelete(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((freelockDmlActor).GetTableIndexInfo).Return(
		&datasource.GetTableInfoIndexResp{
			TableIndexInfo: []*datasource.TableIndexInfo{{ColumnName: "id"}},
		}, nil).Build()
	defer baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockTicketService).GetTableIndexInfo).Return(
		&datasource.GetTableInfoIndexResp{
			TableIndexInfo: []*datasource.TableIndexInfo{{ColumnName: "id"}},
		}, nil).Build()
	defer baseMock4.UnPatch()
	baseMock5 := mockey.Mock((*mocks.MockTicketService).GetIndexValue).Return(
		&datasource.GetIndexValueResp{
			TableIndexValue: []*datasource.TableIndexValue{{IndexValue: "id"}},
		}, nil).Build()
	defer baseMock5.UnPatch()

	value, _ := freelockDmlActor.GetTableIndexBorderValueForDelete(ctx, &shared.ExecFreeLockDMLTicket{
		SqlText: "update tbl set id=1 where id < 1 and id in (2) and id like \"%2\"",
		Source:  &shared.DataSource{Address: ""}})
	if len(value) != 0 {
		t.Fatal("error")
	}
}

func TestGetIndexValue(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}

	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockTicketService).GetIndexValue).Return(
		&datasource.GetIndexValueResp{
			TableIndexValue: []*datasource.TableIndexValue{{IndexName: "id"}},
		}, nil).Build()
	baseMock3 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock3.UnPatch()

	freelockDmlActor.GetIndexValue(ctx, &shared.DataSource{}, "test", "select 1", []string{"aaa"})
	freelockDmlActor.GetIndexValue(ctx, &shared.DataSource{}, "test", "select 1", []string{"aaa", "bbb"})
	baseMock2.UnPatch()

	baseMock4 := mockey.Mock((*mocks.MockTicketService).GetIndexValue).Return(
		nil, fmt.Errorf("error")).Build()
	freelockDmlActor.GetIndexValue(ctx, &shared.DataSource{}, "test", "select 1", []string{"aaa", "bbb"})
	baseMock4.UnPatch()
}

func TestGetTablePKOrUniqKey(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	freelockDmlActor.GetTablePKOrUniqKey(&datasource.GetTableInfoIndexResp{
		TableIndexInfo: []*datasource.TableIndexInfo{
			{
				TableName:    "",
				IndexName:    "",
				Nullable:     "",
				SeqInIndex:   0,
				IndexType:    "Primary",
				ColumnName:   "",
				SubPart:      "",
				IndexComment: "",
			},
			{
				TableName:    "",
				IndexName:    "",
				Nullable:     "",
				SeqInIndex:   0,
				IndexType:    "Unique",
				ColumnName:   "",
				SubPart:      "",
				IndexComment: "",
			},
			{
				TableName:    "",
				IndexName:    "",
				Nullable:     "Yes",
				SeqInIndex:   0,
				IndexType:    "",
				ColumnName:   "",
				SubPart:      "",
				IndexComment: "",
			},
		},
	})

	freelockDmlActor.GetTablePKOrUniqKey(&datasource.GetTableInfoIndexResp{
		TableIndexInfo: []*datasource.TableIndexInfo{
			{
				TableName:    "",
				IndexName:    "",
				Nullable:     "Yes",
				SeqInIndex:   0,
				IndexType:    "",
				ColumnName:   "",
				SubPart:      "",
				IndexComment: "",
			},
		},
	})

	freelockDmlActor.GetTablePKOrUniqKey(&datasource.GetTableInfoIndexResp{
		TableIndexInfo: []*datasource.TableIndexInfo{
			{
				TableName:    "",
				IndexName:    "",
				Nullable:     "Yes",
				SeqInIndex:   0,
				IndexType:    "Unique",
				ColumnName:   "",
				SubPart:      "",
				IndexComment: "",
			},
		},
	})

	freelockDmlActor.GetTablePKOrUniqKey(&datasource.GetTableInfoIndexResp{
		TableIndexInfo: []*datasource.TableIndexInfo{
			{
				TableName:    "",
				IndexName:    "aa",
				Nullable:     "",
				SeqInIndex:   0,
				IndexType:    "Unique",
				ColumnName:   "",
				SubPart:      "",
				IndexComment: "",
			},
			{
				TableName:    "",
				IndexName:    "aa",
				Nullable:     "Yes",
				SeqInIndex:   0,
				IndexType:    "Unique",
				ColumnName:   "",
				SubPart:      "",
				IndexComment: "",
			},
		},
	})
}

func TestProcess(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}

	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock5 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock5.UnPatch()
	baseMock6 := mockey.Mock((*mocks.MockContext).SetReceiveTimeout).Return().Build()
	defer baseMock6.UnPatch()
	baseMock18 := mockey.Mock((*mocks.MockContext).Send).Return().Build()

	defer baseMock18.UnPatch()
	baseMock19 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock19.UnPatch()
	baseMock9 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock9.UnPatch()
	baseMock8 := mockey.Mock((*FreeLockDMLActor).BuildCtx).Return().Build()
	defer baseMock8.UnPatch()

	freelockDmlActor.state = nil

	baseMock4 := mockey.Mock((*mocks.MockContext).Message).Return(&actor.Started{}).Build()
	baseMock41 := mockey.Mock((*FreeLockDMLActor).OnStart).Return().Build()
	freelockDmlActor.Process(ctx)
	baseMock4.UnPatch()
	baseMock41.UnPatch()

	baseMock2 := mockey.Mock((*mocks.MockContext).Message).Return(&shared.ExecFreeLockDMLTicket{}).Build()
	baseMock21 := mockey.Mock((*FreeLockDMLActor).OnStart).Return().Build()
	freelockDmlActor.Process(ctx)
	baseMock2.UnPatch()
	baseMock21.UnPatch()

	baseMock3 := mockey.Mock((*mocks.MockContext).Message).Return(&actor.ReceiveTimeout{}).Build()
	baseMock31 := mockey.Mock((*FreeLockDMLActor).ExecFreeLockDMLTicket).Return().Build()
	freelockDmlActor.Process(ctx)
	baseMock3.UnPatch()
	baseMock31.UnPatch()

	baseMock7 := mockey.Mock((*mocks.MockContext).Message).Return(&actor.Stopped{}).Build()
	baseMock71 := mockey.Mock((*FreeLockDMLActor).GetFreeLockDMLTicketStatus).Return().Build()
	freelockDmlActor.Process(ctx)
	baseMock7.UnPatch()
	baseMock71.UnPatch()
}

func TestOnStart(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock3.UnPatch()
	baseMock5 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock5.UnPatch()
	baseMock21 := mockey.Mock((*FreeLockDMLActor).createTlsClient).Return(tls.NewClient("a", "a", "a", "a", "a")).Build()
	defer baseMock21.UnPatch()
	baseMock4 := mockey.Mock((*FreeLockDMLActor).repairActorState).Return().Build()
	defer baseMock4.UnPatch()
	baseMock18 := mockey.Mock((*mocks.MockContext).Send).Return().Build()

	defer baseMock18.UnPatch()
	baseMock19 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock19.UnPatch()

	freelockDmlActor.state.CurrentAction = model.FreeLockDMLTicketAction_ExecuteFinished
	freelockDmlActor.OnStart(ctx)

	freelockDmlActor.state.CurrentAction = model.FreeLockDMLTicketAction_ExecuteCommand
	freelockDmlActor.OnStart(ctx)
}

func TestRepairActorState(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock3 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock3.UnPatch()
	baseMock5 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock5.UnPatch()
	baseMock18 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock18.UnPatch()
	baseMock19 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock19.UnPatch()
	baseMock6 := mockey.Mock((*mocks.MockContext).Respond).Return(nil).Build()
	defer baseMock6.UnPatch()
	baseMock7 := mockey.Mock((*FreeLockDMLActor).UpdateTicketRepo).Return(nil).Build()
	defer baseMock7.UnPatch()

	freelockDmlActor.state.CurrentAction = model.FreeLockDMLTicketAction_Started
	freelockDmlActor.repairActorState(ctx)

	freelockDmlActor.state.CurrentAction = model.FreeLockDMLTicketAction_ReceiveCommand
	baseMock8 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{TicketType: int8(model.TicketType_NormalSqlChange)}, fmt.Errorf("error")).Build()
	freelockDmlActor.repairActorState(ctx)
	baseMock8.UnPatch()

	baseMock9 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{TicketType: int8(model.TicketType_NormalSqlChange)}, nil).Build()
	defer baseMock9.UnPatch()
	freelockDmlActor.repairActorState(ctx)

	freelockDmlActor.state.CurrentAction = model.FreeLockDMLTicketAction_ExecuteFinished
	freelockDmlActor.repairActorState(ctx)

	freelockDmlActor.state.CurrentAction = model.FreeLockDMLTicketAction_ExecuteFailed
	freelockDmlActor.repairActorState(ctx)
}

func TestExecuteCommand(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*FreeLockDMLActor).updateActorAction).Return().Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock3.UnPatch()
	baseMock5 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock5.UnPatch()
	baseMock51 := mockey.Mock((*mocks.MockContext).ClientOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock51.UnPatch()
	baseMock53 := mockey.Mock((*mocks.MockContext).SetReceiveTimeout).Return().Build()
	defer baseMock53.UnPatch()

	baseMock52 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	baseMock6 := mockey.Mock((*FreeLockDMLActor).UpdateTicketRepo).Return(fmt.Errorf("error")).Build()
	freelockDmlActor.ExecuteCommand(ctx, &shared.ExecuteCommand{})
	baseMock6.UnPatch()
	baseMock52.UnPatch()

	baseMock7 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.CommandAccepted{}, nil).Build()
	freelockDmlActor.ExecuteCommand(ctx, &shared.ExecuteCommand{})
	baseMock7.UnPatch()

	baseMock8 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.CommandRejected{}, nil).Build()
	baseMock61 := mockey.Mock((*FreeLockDMLActor).UpdateTicketRepo).Return(fmt.Errorf("error")).Build()
	freelockDmlActor.ExecuteCommand(ctx, &shared.ExecuteCommand{})
	baseMock61.UnPatch()
	baseMock8.UnPatch()

	baseMock81 := mockey.Mock((*mocks.MockKindClient).Call).Return("aaa", nil).Build()
	baseMock611 := mockey.Mock((*FreeLockDMLActor).UpdateTicketRepo).Return(fmt.Errorf("error")).Build()
	freelockDmlActor.ExecuteCommand(ctx, &shared.ExecuteCommand{})
	baseMock611.UnPatch()
	baseMock81.UnPatch()

	baseMock811 := mockey.Mock((*mocks.MockKindClient).Call).Return("aaa", nil).Build()
	baseMock6111 := mockey.Mock((*FreeLockDMLActor).UpdateTicketRepo).Return(fmt.Errorf("error")).Build()
	freelockDmlActor.ExecuteCommand(ctx, &shared.ExecuteCommand{})
	baseMock6111.UnPatch()
	baseMock811.UnPatch()
}

func TestExplainCommand(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ds := &shared.DataSource{
		Type: shared.MySQL,
	}

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockCommandParser).Explain).Return([]*parser.Command{}, fmt.Errorf("error")).Build()
	freelockDmlActor.explainCommand(ctx, ds, &model.ExecuteCommandSetReq{
		CommandSetContent: utils.StringRef("select 1"),
	})
	baseMock2.UnPatch()

	baseMock3 := mockey.Mock((*mocks.MockCommandParser).Explain).Return([]*parser.Command{}, nil).Build()
	freelockDmlActor.explainCommand(ctx, ds, &model.ExecuteCommandSetReq{
		CommandSetContent: utils.StringRef("select 1"),
	})
	baseMock3.UnPatch()

	baseMock4 := mockey.Mock((*mocks.MockCommandParser).Explain).Return([]*parser.Command{{}}, nil).Build()
	freelockDmlActor.explainCommand(ctx, ds, &model.ExecuteCommandSetReq{
		CommandSetContent: utils.StringRef("select 1"),
	})
	baseMock4.UnPatch()

}

func TestConvertToEntity(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockService).NextStr).Return(nil, fmt.Errorf("error")).Build()
	freelockDmlActor.convertToEntity(ctx, &model.ExecuteCommandSetReq{}, []*parser.Command{{}})
	baseMock2.UnPatch()

	baseMock3 := mockey.Mock((*mocks.MockService).NextStr).Return([]string{"1"}, nil).Build()
	defer baseMock3.UnPatch()
	freelockDmlActor.convertToEntity(ctx, &model.ExecuteCommandSetReq{}, []*parser.Command{{}})
}

func TestUpdateTicketRepo(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock2.UnPatch()

	baseMock3 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(fmt.Errorf("error")).Build()
	freelockDmlActor.UpdateTicketRepo(ctx, &dao.Ticket{})
	baseMock3.UnPatch()

	baseMock4 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(nil).Build()
	freelockDmlActor.UpdateTicketRepo(ctx, &dao.Ticket{})
	defer baseMock4.UnPatch()
}

func TestDoWhenBatchGap(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock2.UnPatch()
	freelockDmlActor.doWhenBatchGap(ctx, &shared.Ticket{IsReplicaDelayEnable: false})

	//baseMock23 := mockey.Mock((*FreeLockDMLActor).GetSecondsBehindMaster).Return(10, fmt.Errorf("error")).Build()
	//freelockDmlActor.doWhenBatchGap(ctx, &shared.Ticket{IsReplicaDelayEnable: true, ReplicaDelaySeconds: 15})
	//defer baseMock23.UnPatch()
}

func TestGetSecondsBehindMaster(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeInstanceReplicaDelay).Return(1, nil).Build()
	defer baseMock2.UnPatch()
	freelockDmlActor.GetSecondsBehindMaster(ctx, &shared.Ticket{InstanceType: shared.MySQL, InstanceId: "xx"})
}

func TestGenerateNextBatchSelectSQL(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeInstanceReplicaDelay).Return(1, nil).Build()
	defer baseMock2.UnPatch()
	freelockDmlActor.state.Tables = []string{"a", "b"}
	freelockDmlActor.state.FreeLockExecInfo = []*FreeLockExecInfo{
		{
			MinValue:   "1",
			MaxValue:   "2",
			BatchLeft:  "1",
			BatchRight: "2",
			IndexName:  "id",
		},
	}
	defer baseMock2.UnPatch()
	freelockDmlActor.GenerateNextBatchSelectSQL(
		ctx, &shared.Ticket{
			SqlText:      "update test1 set id=1 where id=1",
			InstanceType: shared.MySQL,
			InstanceId:   "xx"})
}

func TestGenerateNextBatchSelectSQLForDelete(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock4.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeInstanceReplicaDelay).Return(1, nil).Build()
	defer baseMock2.UnPatch()
	freelockDmlActor.state.Tables = []string{"a", "b"}
	freelockDmlActor.state.FreeLockExecInfo = []*FreeLockExecInfo{
		{
			MinValue:   "1",
			MaxValue:   "2",
			BatchLeft:  "1",
			BatchRight: "2",
			IndexName:  "id",
		},
	}
	defer baseMock2.UnPatch()
	freelockDmlActor.GenerateNextBatchSelectSQLForDelete(
		ctx, &shared.Ticket{
			SqlText:      "update test1 set id=1 where id=1",
			InstanceType: shared.MySQL,
			InstanceId:   "xx"})
}

func TestGenerateNextBatchExecSQL(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeInstanceReplicaDelay).Return(1, nil).Build()
	defer baseMock2.UnPatch()
	freelockDmlActor.state.Tables = []string{"a", "b"}
	freelockDmlActor.state.FreeLockExecInfo = []*FreeLockExecInfo{
		{
			MinValue:   "1",
			MaxValue:   "2",
			BatchLeft:  "1",
			BatchRight: "2",
			IndexName:  "id",
		},
	}
	defer baseMock2.UnPatch()
	freelockDmlActor.GenerateNextBatchExecSQL(
		ctx, &shared.Ticket{
			SqlText:      "update test1 set id=1 where id=1",
			InstanceType: shared.MySQL, InstanceId: "xx"})
}

func TestGenerateNextBatchExecSQLForDelete(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeInstanceReplicaDelay).Return(1, nil).Build()
	defer baseMock2.UnPatch()
	freelockDmlActor.state.Tables = []string{"a", "b"}
	freelockDmlActor.state.FreeLockExecInfo = []*FreeLockExecInfo{
		{
			MinValue:   "1",
			MaxValue:   "2",
			BatchLeft:  "1",
			BatchRight: "2",
			IndexName:  "id",
		},
	}
	defer baseMock2.UnPatch()
	freelockDmlActor.GenerateNextBatchExecSQLForDelete(
		ctx, &shared.Ticket{
			SqlText:      "update test1 set id=1 where id=1",
			InstanceType: shared.MySQL, InstanceId: "xx"})
}

func TestIsNextBatchResultReachMaxValue(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	freelockDmlActor.IsNextBatchResultReachMaxValue(
		[]string{"1", "2"},
		[]*FreeLockExecInfo{{MaxValue: "2"}, {MaxValue: "3"}})
	freelockDmlActor.IsNextBatchResultReachMaxValue(
		[]string{"2", "3"},
		[]*FreeLockExecInfo{{MaxValue: "1"}, {MaxValue: "2"}})
	freelockDmlActor.IsNextBatchResultReachMaxValue(
		[]string{"1", "2"},
		[]*FreeLockExecInfo{{MaxValue: "1"}, {MaxValue: "2"}})
	freelockDmlActor.IsNextBatchResultReachMaxValue(
		[]string{"a", "b"},
		[]*FreeLockExecInfo{{MaxValue: "a"}, {MaxValue: "b"}})
	freelockDmlActor.IsNextBatchResultReachMaxValue(
		[]string{"b", "c"},
		[]*FreeLockExecInfo{{MaxValue: "a"}, {MaxValue: "b"}})
	freelockDmlActor.IsNextBatchResultReachMaxValue(
		[]string{"a", "b"},
		[]*FreeLockExecInfo{{MaxValue: "b"}, {MaxValue: "c"}})
}

func TestNeedDml(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*FreeLockDMLActor).IsNextBatchResultReachMaxValue).Return(false).Build()
	freelockDmlActor.state.FreeLockExecInfo = []*FreeLockExecInfo{{
		BatchRight: "1",
		MaxValue:   "2",
	}, {
		BatchRight: "2",
		MaxValue:   "2",
	}}
	freelockDmlActor.NeedDml(ctx, []string{"3", "4"},
		[]*FreeLockExecInfo{{MaxValue: "1", BatchRight: "1"}, {MaxValue: "2", BatchRight: "2"}})
	baseMock2.UnPatch()

	baseMock3 := mockey.Mock((*FreeLockDMLActor).IsNextBatchResultReachMaxValue).Return(true).Build()
	freelockDmlActor.state.FreeLockExecInfo = []*FreeLockExecInfo{{
		BatchRight: "1",
	}, {
		BatchRight: "2",
	}}
	freelockDmlActor.NeedDml(ctx, []string{"1", "2"},
		[]*FreeLockExecInfo{{MaxValue: "1", BatchRight: "1"}, {MaxValue: "2", BatchRight: "2"}})
	baseMock3.UnPatch()
}

func TestLastBatch(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	ctx := &mocks.MockContext{}

	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	freelockDmlActor.LastBatch(ctx,
		[]*FreeLockExecInfo{{MaxValue: "1", BatchRight: "1"}, {MaxValue: "2", BatchRight: "2"}})
}

func TestInitState(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	ctx := &mocks.MockContext{}

	baseMock11 := mockey.Mock(log.Log).Return().Build()
	defer baseMock11.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock4.UnPatch()
	baseMock5 := mockey.Mock((*FreeLockDMLActor).BuildCtx).Return().Build()
	defer baseMock5.UnPatch()
	baseMock6 := mockey.Mock((*mocks.MockDataSourceService).GetDatasourceAddress).Return(nil).Build()
	defer baseMock6.UnPatch()

	freelockDmlActor.state.TotalBatchNum = 0
	baseMock1 := mockey.Mock((*FreeLockDMLActor).GetSharedTicket).Return(nil, fmt.Errorf("error")).Build()
	freelockDmlActor.initState(ctx, &shared.ExecFreeLockDMLTicket{
		TenantID: "1", UserID: "1", Source: &shared.DataSource{},
	})
	baseMock1.UnPatch()

	freelockDmlActor.state.TotalBatchNum = 0
	baseMock2 := mockey.Mock((*FreeLockDMLActor).GetSharedTicket).Return(&shared.Ticket{}, nil).Build()
	baseMock3 := mockey.Mock((*FreeLockDMLActor).getTotalBatchNum).Return(fmt.Errorf("error")).Build()
	freelockDmlActor.initState(ctx, &shared.ExecFreeLockDMLTicket{
		TenantID: "1", UserID: "1", Source: &shared.DataSource{},
	})
	baseMock2.UnPatch()
	baseMock3.UnPatch()

}

func TestIsLastBatch(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	freelockDmlActor.state.FreeLockExecInfo = []*FreeLockExecInfo{{
		BatchRight: "1",
		MaxValue:   "1",
	}, {
		BatchRight: "2",
		MaxValue:   "3",
	}}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	freelockDmlActor.IsLastBatch()
}

func TestGetMessage(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	freelockDmlActor.state.FreeLockExecInfo = []*FreeLockExecInfo{{
		BatchRight: "1",
		BatchLeft:  "1",
		MaxValue:   "1",
		IndexName:  "age",
	}, {
		BatchRight: "2",
		BatchLeft:  "1",
		MaxValue:   "3",
		IndexName:  "id",
	}}
	freelockDmlActor.getMessage()
}

func TestGetTotalBatchNum(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	ctx := &mocks.MockContext{}

	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock2.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock4.UnPatch()

	//amock := mockey.Mock((*FreeLockDMLActor).SaveTicketAffectedRows).Return().Build()
	//defer amock.UnPatch()
	amock1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateTicketAffectedRows).Return(nil).Build()
	defer amock1.UnPatch()

	baseMock5 := mockey.Mock((*mocks.MockTicketService).ExplainCommand).Return(nil, fmt.Errorf("error")).Build()
	freelockDmlActor.getTotalBatchNum(ctx, &shared.Ticket{TicketId: 1, CreateFrom: TicketFromOpenAPI})
	baseMock5.UnPatch()

	baseMock6 := mockey.Mock((*mocks.MockTicketService).ExplainCommand).Return(&datasource.ExplainCommandResp{
		Command: []*datasource.ExplainCommandResult{
			{Rows: ""},
			{Rows: "1"},
		},
	}, nil).Build()
	defer baseMock6.UnPatch()
	freelockDmlActor.getTotalBatchNum(ctx, &shared.Ticket{TicketId: 1, CreateFrom: TicketFromOpenAPI})

	baseMock15 := mockey.Mock((*mocks.MockWorkflowDAL).GetPreCheckResult).Return(nil, fmt.Errorf("error")).Build()
	freelockDmlActor.getTotalBatchNum(ctx, &shared.Ticket{TicketId: 1})
	baseMock15.UnPatch()

	baseMock3 := mockey.Mock((*mocks.MockWorkflowDAL).GetPreCheckResult).Return(
		[]*dao.TicketPreCheckResult{
			{
				TicketId: 0,
				Item:     workflow.PreCheckExplain,
				Status:   0,
				Memo:     "1000",
			},
		}, nil).Build()
	defer baseMock3.UnPatch()
	freelockDmlActor.getTotalBatchNum(ctx, &shared.Ticket{TicketId: 1})
}

func TestGetSharedTicket(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	ctx := &mocks.MockContext{}

	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock13 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock13.UnPatch()

	baseMock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, fmt.Errorf("error")).Build()
	freelockDmlActor.GetSharedTicket(ctx)
	baseMock2.UnPatch()

	baseMock3 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, nil).Build()
	freelockDmlActor.GetSharedTicket(ctx)
	baseMock3.UnPatch()

	baseMock4 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{TicketType: int8(model.TicketType_NormalSqlChange)}, nil).Build()
	baseMock8 := mockey.Mock(workflow.ChangeTicketType).Return(&shared.Ticket{}).Build()
	freelockDmlActor.GetSharedTicket(ctx)
	defer baseMock8.UnPatch()
	baseMock4.UnPatch()
}

func TestTicketSucceed(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	ctx := &mocks.MockContext{}

	baseMock2 := mockey.Mock(log.Log).Return().Build()
	defer baseMock2.UnPatch()
	baseMock13 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock13.UnPatch()
	baseMock6 := mockey.Mock((*FreeLockDMLActor).ProducerToTls).Return().Build()
	defer baseMock6.UnPatch()
	baseMock1 := mockey.Mock((*FreeLockDMLActor).updateActorAction).Return().Build()
	defer baseMock1.UnPatch()

	baseMock15 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(nil).Build()
	defer baseMock15.UnPatch()

	freelockDmlActor.TicketSucceed(ctx, &shared.Ticket{})
}

func TestUpdateIndex(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	ctx := &mocks.MockContext{}
	freelockDmlActor.state.FreeLockExecInfo = []*FreeLockExecInfo{{
		MinValue:   "",
		MaxValue:   "",
		BatchLeft:  "",
		BatchRight: "",
		IndexName:  "",
	}}
	freelockDmlActor.updateIndex(ctx)
}

func TestNotReachCronStartTime(t *testing.T) {
	NotReachCronStartTime(int32(model.ExecuteType_Cron), 0)
}

func TestGetSQLTextType(t *testing.T) {
	GetSQLTextType("update tbl set id=1")
	GetSQLTextType("delete from tbl where id=1")
}

func TestStopTicket(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	ctx := &mocks.MockContext{}
	freelockDmlActor.state = nil
	freelockDmlActor.StopTicket(ctx, &shared.StopTicket{})

	freelockDmlActor.state = &FreeLockDMLActorState{
		CurrentAction: model.FreeLockDMLTicketAction_ExecuteFinished,
		TenantId:      "1",
		UserId:        "1",
		CsID:          "xx",
		ConnectionId:  "xx",
		SessionId:     "xx",
	}
	baseMock51 := mockey.Mock((*mocks.MockContext).ClientOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock51.UnPatch()
	baseMock53 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock53.UnPatch()
	baseMock54 := mockey.Mock(log.Log).Return().Build()
	defer baseMock54.UnPatch()
	baseMock52 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	freelockDmlActor.StopTicket(ctx, &shared.StopTicket{})
	baseMock52.UnPatch()
	baseMock55 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock55.UnPatch()
	baseMock57 := mockey.Mock((*FreeLockDMLActor).UpdateTicketRepo).Return(nil).Build()
	defer baseMock57.UnPatch()
	baseMock18 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock18.UnPatch()
	baseMock19 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock19.UnPatch()

	baseMock5 := mockey.Mock((*mocks.MockKindClient).Call).Return("", nil).Build()
	freelockDmlActor.StopTicket(ctx, &shared.StopTicket{})
	baseMock5.UnPatch()

	baseMock6 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.CommandAccepted{}, nil).Build()
	freelockDmlActor.StopTicket(ctx, &shared.StopTicket{})
	baseMock6.UnPatch()

	baseMock7 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.CommandRejected{}, nil).Build()
	freelockDmlActor.StopTicket(ctx, &shared.StopTicket{})
	baseMock7.UnPatch()
}

func TestExecCommand(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	ctx := &mocks.MockContext{}

	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock1.UnPatch()

	freelockDmlActor.state.SQLType = DeleteType
	baseMock2 := mockey.Mock((*mocks.MockDataSourceService).ExecuteDQL).Return(&datasource.ExecuteDQLResp{
		ColumnName:  []string{},
		ColumnValue: []string{},
	}, fmt.Errorf("error")).Build()
	baseMock3 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(fmt.Errorf("error")).Build()
	defer baseMock3.UnPatch()
	baseMock31 := mockey.Mock((*mocks.MockDataSourceService).GetDatasourceAddress).Return(nil).Build()
	defer baseMock31.UnPatch()
	freelockDmlActor.execCommand(ctx, &shared.Ticket{}, "select 1", SQLTypeSelect)
	baseMock2.UnPatch()

	baseMock2_1 := mockey.Mock((*mocks.MockDataSourceService).ExecuteDQL).Return(&datasource.ExecuteDQLResp{
		ColumnName:  []string{},
		ColumnValue: []string{},
	}, fmt.Errorf("error")).Build()
	baseMock4 := mockey.Mock((*FreeLockDMLActor).SaveResToLeft).Return().Build()
	defer baseMock4.UnPatch()
	baseMock5 := mockey.Mock((*FreeLockDMLActor).UpdateProgress).Return().Build()
	defer baseMock5.UnPatch()
	baseMock41 := mockey.Mock((*FreeLockDMLActor).NeedDml).Return().Build()
	defer baseMock41.UnPatch()
	baseMock51 := mockey.Mock((*FreeLockDMLActor).LastBatch).Return().Build()
	defer baseMock51.UnPatch()
	freelockDmlActor.execCommand(ctx, &shared.Ticket{}, "select 1", SQLTypeSelect)
	baseMock2_1.UnPatch()

	freelockDmlActor.state.SQLType = NormalType
	baseMock2_2 := mockey.Mock((*mocks.MockDataSourceService).ExecuteDQL).Return(&datasource.ExecuteDQLResp{
		ColumnName:  []string{},
		ColumnValue: []string{},
	}, fmt.Errorf("error")).Build()
	freelockDmlActor.execCommand(ctx, &shared.Ticket{}, "select 1", SQLTypeSelect)
	baseMock2_2.UnPatch()

	baseMock2_3 := mockey.Mock((*mocks.MockDataSourceService).ExecuteDQL).Return(&datasource.ExecuteDQLResp{
		ColumnName:  []string{},
		ColumnValue: []string{},
	}, nil).Build()
	freelockDmlActor.execCommand(ctx, &shared.Ticket{}, "select 1", SQLTypeSelect)
	baseMock2_3.UnPatch()

	baseMock6 := mockey.Mock((*mocks.MockDataSourceService).ExecuteDMLAndGetAffectedRows).Return(0, nil).Build()
	baseMock3_1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateTicketAffectedRows).Return(nil).Build()
	freelockDmlActor.execCommand(ctx, &shared.Ticket{}, "update t set id= 1", SQLTypeDML)
	defer baseMock6.UnPatch()
	defer baseMock3_1.UnPatch()
}

func TestExecSQLDirectly(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	ctx := &mocks.MockContext{}

	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock1.UnPatch()

	baseMock6 := mockey.Mock((*mocks.MockDataSourceService).ExecuteDMLAndGetAffectedRows).Return(0, fmt.Errorf("")).Build()
	freelockDmlActor.ExecSQLDirectly(ctx, &shared.Ticket{})
	baseMock6.UnPatch()

	baseMock3_1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateTicketAffectedRows).Return(nil).Build()
	defer baseMock3_1.UnPatch()

	baseMock61 := mockey.Mock((*mocks.MockDataSourceService).ExecuteDMLAndGetAffectedRows).Return(0, nil).Build()
	freelockDmlActor.ExecSQLDirectly(ctx, &shared.Ticket{})
	baseMock61.UnPatch()
}

func TestIsEmptyResult(t *testing.T) {
	IsEmptyResult([]string{"1"})
	IsEmptyResult([]string{""})
}
