package ticket

import (
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"github.com/bytedance/mockey"
)

func TestFormatTlsLogs(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	ctx := &mocks.MockContext{}

	baseMock1 := mockey.Mock((*mocks.MockC3ConfigProvider).GetNamespace).Return(&config.C3Config{Application: config.Application{
		TicketLogTopic: "1",
	}}).Build()
	defer baseMock1.UnPatch()
	freelockDmlActor.formatTlsLogs(ctx, "a", "100", "1", "1", "1")
}

func TestGetHash(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()
	freelockDmlActor.getHash("1")
}
