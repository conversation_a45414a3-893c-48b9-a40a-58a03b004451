package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/sqltask"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"go.uber.org/dig"
	"math"
	"testing"
)

// mock一个actor
func mockVeDBOnlineDDLTicketActor() *VeDBDDLTicketActor {
	return &VeDBDDLTicketActor{
		state: &VeDBDDLTicketState{
			CurrentAction: model.VeDBDDLTicketAction_Started,
			TenantId:      "1",
			UserId:        "1"},
		cnf:           &config.MockConfigProvider{},
		workflowDal:   &mocks.MockWorkflowDAL{},
		sp:            &mocks.MockCommandParser{},
		idgenSvc:      &mocks.MockService{},
		cmdRepo:       &mocks.MockCommandRepo{},
		ticketService: &mocks.MockTicketService{},
		sqlTaskSvc:    &sqltask.MockSqlTaskService{},
	}
}

func TestNewVeDBDDLTicketActor(t *testing.T) {
	NewVeDBDDLTicketActor(VeDBDDLTicketActorIn{
		In:   dig.In{},
		Conf: &config.MockConfigProvider{},
	})
}

func TestNewVeDBDDLTicketState(t *testing.T) {
	newVeDBDDLTicketState([]byte{})
}

func TestGetState(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	vedbActor.GetState()
}

func TestVeDBProcess(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	baseMock1 := mockey.Mock((*mocks.MockContext).Message).Return(&actor.Started{}).Build()
	baseMock2 := mockey.Mock((*VeDBDDLTicketActor).OnStart).Return().Build()
	vedbActor.Process(ctx)
	baseMock1.UnPatch()
	baseMock2.UnPatch()

	baseMock3 := mockey.Mock((*mocks.MockContext).Message).Return(&shared.ExecVeDBDDLTicket{}).Build()
	baseMock4 := mockey.Mock((*VeDBDDLTicketActor).ExecVeDBDDLTicket).Return().Build()
	vedbActor.Process(ctx)
	baseMock3.UnPatch()
	baseMock4.UnPatch()

	baseMock5 := mockey.Mock((*mocks.MockContext).Message).Return(&shared.StopTicket{}).Build()
	baseMock6 := mockey.Mock((*VeDBDDLTicketActor).StopTicket).Return().Build()
	vedbActor.Process(ctx)
	baseMock5.UnPatch()
	baseMock6.UnPatch()

	baseMock7 := mockey.Mock((*mocks.MockContext).Message).Return(&actor.ReceiveTimeout{}).Build()
	baseMock8 := mockey.Mock((*VeDBDDLTicketActor).GetVeDBTicketStatus).Return().Build()
	vedbActor.Process(ctx)
	baseMock7.UnPatch()
	baseMock8.UnPatch()

	baseMock9 := mockey.Mock((*mocks.MockContext).Message).Return(&actor.Stopped{}).Build()
	vedbActor.Process(ctx)
	baseMock9.UnPatch()
}

func TestVeDBOnStart(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock0000.UnPatch()

	vedbActor.state = nil
	vedbActor.OnStart(ctx)

	vedbActor.state.CurrentAction = model.VeDBDDLTicketAction_ExecuteFailed
	vedbActor.OnStart(ctx)

	baseMock1 := mockey.Mock((*VeDBDDLTicketActor).repairTicketState).Return().Build()
	defer baseMock1.UnPatch()
	vedbActor.OnStart(ctx)
}

func TestVeDBExecVeDBDDLTicket(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock0000.UnPatch()
	baseMock00000 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock00000.UnPatch()

	baseMock1 := mockey.Mock((*VeDBDDLTicketActor).initState).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*VeDBDDLTicketActor).updateActorState).Return().Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*VeDBDDLTicketActor).IsNowExceedExecEndTime).Return(true).Build()
	baseMock4 := mockey.Mock((*VeDBDDLTicketActor).doWhenReachExecEndTime).Return().Build()
	vedbActor.ExecVeDBDDLTicket(ctx, &shared.ExecVeDBDDLTicket{})
	baseMock3.UnPatch()
	baseMock4.UnPatch()

	//baseMock31 := mockey.Mock((*VeDBDDLTicketActor).IsNowExceedExecEndTime).Return(false).Build()
	//baseMock41 := mockey.Mock((*VeDBDDLTicketActor).doWhenReachExecEndTime).Return().Build()
	//defer baseMock41.UnPatch()
	//
	//baseMock5 := mockey.Mock((*VeDBDDLTicketActor).IsNowUnReachCronTime).Return(true).Build()
	//baseMock6 := mockey.Mock((*VeDBDDLTicketActor).doWhenUnReachCronTime).Return().Build()
	//vedbActor.ExecVeDBDDLTicket(ctx, &shared.ExecVeDBDDLTicket{})
	//baseMock31.UnPatch()
	//baseMock5.UnPatch()
	//baseMock6.UnPatch()

}

func TestVeDBIsNowExceedExecEndTime(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}

	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	vedbActor.IsNowExceedExecEndTime(ctx, &shared.ExecVeDBDDLTicket{ExecutableEndTime: 1})
}

func TestVeDBdoWhenReachExecEndTime(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	er := fmt.Errorf("error")

	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock00000 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock00000.UnPatch()
	baseMock1 := mockey.Mock((*VeDBDDLTicketActor).updateActorState).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*VeDBDDLTicketActor).UpdateTicketRepo).Return(er).Build()
	defer baseMock2.UnPatch()

	vedbActor.doWhenReachExecEndTime(ctx)
}

func TestVeDBIsNowUnReachCronTime(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	vedbActor.IsNowUnReachCronTime(ctx, &shared.ExecVeDBDDLTicket{ExecuteType: int32(model.ExecuteType_Cron),
		ExecutableStartTime: math.MaxInt32})
}

func TestVeDBdoWhenUnReachCronTime(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).SetReceiveTimeout).Return().Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock0000.UnPatch()
	baseMock00000 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock00000.UnPatch()

	vedbActor.doWhenUnReachCronTime(ctx, &shared.ExecVeDBDDLTicket{ExecutableStartTime: 0})
}

func TestVeDBInitState(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock0000.UnPatch()

	vedbActor.initState(ctx, &shared.ExecVeDBDDLTicket{})
}

func TestVeDBUpdateTicketRepo(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	er := fmt.Errorf("error")

	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	baseMock1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(er).Build()
	vedbActor.UpdateTicketRepo(ctx, &dao.Ticket{})
	baseMock1.UnPatch()

	baseMock2 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(nil).Build()
	vedbActor.UpdateTicketRepo(ctx, &dao.Ticket{})
	baseMock2.UnPatch()
}

func TestVeDBUpdateActorState(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	vedbActor.updateActorState(ctx, model.VeDBDDLTicketAction_Started)
}

func TestVeDBRepairTicketState(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	er := fmt.Errorf("error")

	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock0000.UnPatch()

	vedbActor.state.CurrentAction = model.VeDBDDLTicketAction_Started
	vedbActor.repairTicketState(ctx)

	vedbActor.state.Ds = &shared.DataSource{}
	baseMock1 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, er).Build()
	vedbActor.state.CurrentAction = model.VeDBDDLTicketAction_ReceiveCommand
	vedbActor.repairTicketState(ctx)
	baseMock1.UnPatch()

	baseMock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{}, nil).Build()
	vedbActor.repairTicketState(ctx)
	baseMock2.UnPatch()

}

func TestVeDBGetVeDBTicketStatus(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	er := fmt.Errorf("error")

	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock0000.UnPatch()
	baseMock00000 := mockey.Mock(context.GetUserID).Return("1").Build()
	defer baseMock00000.UnPatch()
	baseMock000000 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock000000.UnPatch()

	baseMock1 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, er).Build()
	vedbActor.GetVeDBTicketStatus(ctx)
	baseMock1.UnPatch()

	baseMock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{}, nil).Build()
	baseMock3 := mockey.Mock((*VeDBDDLTicketActor).DescribeOnlineDDLTask).Return().Build()
	vedbActor.GetVeDBTicketStatus(ctx)
	baseMock2.UnPatch()
	baseMock3.UnPatch()

}

func TestVeDBCreateOnlineDDLTask(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	er := fmt.Errorf("error")

	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	baseMock1 := mockey.Mock((*VeDBDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, er).Build()
	vedbActor.CreateOnlineDDLTask(ctx)
	baseMock2.UnPatch()

	baseMock21 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{InstanceType: "xxx"}, nil).Build()
	vedbActor.CreateOnlineDDLTask(ctx)
	baseMock21.UnPatch()

	//baseMock22 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{InstanceType: "MySQL",
	//	InstanceId: "xxx", ExecutableStartTime: 1}, nil).Build()
	//defer baseMock22.UnPatch()
	//baseMock3 := mockey.Mock((*mocks.MockTicketService).IsInstanceRunning).Return(false).Build()
	//vedbActor.CreateOnlineDDLTask(ctx)
	//baseMock3.UnPatch()

	//baseMock31 := mockey.Mock((*mocks.MockTicketService).IsInstanceRunning).Return(true).Build()
	//defer baseMock31.UnPatch()
	//baseMock4 := mockey.Mock((*mocks.MockSqlTaskService).CreateOnlineDDLSqlTask).Return(nil, er).Build()
	//vedbActor.CreateOnlineDDLTask(ctx)
	//baseMock4.UnPatch()
	//
	//baseMock41 := mockey.Mock((*mocks.MockSqlTaskService).CreateOnlineDDLSqlTask).Return(nil, nil).Build()
	//vedbActor.CreateOnlineDDLTask(ctx)
	//baseMock41.UnPatch()
	//
	//baseMock42 := mockey.Mock((*mocks.MockSqlTaskService).CreateOnlineDDLSqlTask).Return(utils.StringRef("1"), er).Build()
	//baseMock5 := mockey.Mock((*VeDBDDLTicketActor).generateStopCronTask).Return().Build()
	//defer baseMock5.UnPatch()
	//baseMock6 := mockey.Mock((*mocks.MockWorkflowDAL).Save).Return(nil).Build()
	//defer baseMock6.UnPatch()
	//vedbActor.CreateOnlineDDLTask(ctx)
	//baseMock42.UnPatch()

}

func TestVeDBDescribeOnlineDDLTask(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	er := fmt.Errorf("error")

	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	vedbActor.state.IsDDLTaskCreated = false
	baseMock1 := mockey.Mock((*VeDBDDLTicketActor).sendExecTicketMsg).Return().Build()
	defer baseMock1.UnPatch()

	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
	})

	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   9999999999,
	})

	vedbActor.state.IsDDLTaskCreated = true
	baseMock2 := mockey.Mock((*mocks.MockContext).ClientOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   9999999999,
	})
	baseMock3.UnPatch()

	baseMock31 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.GetDDLTaskStatusResp{}, nil).Build()
	baseMock5 := mockey.Mock(IsSqlTaskInProgress).Return(true).Build()
	baseMock6 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(nil).Build()
	baseMock7 := mockey.Mock((*VeDBDDLTicketActor).doWhenReachEndTime).Return(er).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   1,
	})
	baseMock7.UnPatch()
	baseMock6.UnPatch()

	baseMock71 := mockey.Mock((*VeDBDDLTicketActor).doWhenReachEndTime).Return(nil).Build()
	baseMock71.UnPatch()
	baseMock8 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(er).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   1,
	})
	baseMock8.UnPatch()

	baseMock81 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(nil).Build()
	defer baseMock81.UnPatch()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   1,
	})
	baseMock5.UnPatch()
	baseMock31.UnPatch()

	baseMock32 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.GetDDLTaskStatusErr{}, nil).Build()
	baseMock4 := mockey.Mock((*VeDBDDLTicketActor).UpdateTicketRepo).Return(er).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   9999999999,
	})
	baseMock4.UnPatch()

	baseMock41 := mockey.Mock((*VeDBDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   9999999999,
	})
	baseMock41.UnPatch()
	baseMock32.UnPatch()

	baseMock33 := mockey.Mock((*mocks.MockKindClient).Call).Return("", nil).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   9999999999,
	})
	baseMock33.UnPatch()
}

func TestVeDBSendExecTicketMsg(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	vedbActor.sendExecTicketMsg(ctx, &dao.Ticket{})
}

func TestVeDBdoWhenReachEndTime(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock((*mocks.MockContext).ClientOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock0000.UnPatch()
	baseMock000000 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	defer baseMock000000.UnPatch()

	vedbActor.state.IsDDLTaskCreated = true
	vedbActor.doWhenReachEndTime(ctx, &dao.Ticket{ExecuteType: int8(model.ExecuteType_Cron)})
}

//func TestVeDBGenerateStopCronTask(t *testing.T) {
//	vedbActor := mockVeDBOnlineDDLTicketActor()
//	ctx := &mocks.MockContext{}
//	logbMock := mockey.Mock(log.Log).Return().Build()
//	defer logbMock.UnPatch()
//	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//	defer baseMock0.UnPatch()
//	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//	defer baseMock00.UnPatch()
//	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
//	defer baseMock000.UnPatch()
//
//	baseMock1 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
//
//
//	vedbActor.generateStopCronTask(ctx,&dao.Ticket{})
//}

func TestVeDBSendStopVeDBDDLTaskToSQLTaskActor(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	er := fmt.Errorf("error")
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return(er).Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return().Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	a := mocks.NewMockKindClient(&gomock.Controller{})
	baseMock0000 := mockey.Mock((*mocks.MockContext).ClientOf).Return(a).Build()
	defer baseMock0000.UnPatch()
	baseMock000000 := mockey.Mock((*mocks.MockKindClient).Send).Return(fmt.Errorf("error")).Build()
	defer baseMock000000.UnPatch()

	vedbActor.sendStopVeDBDDLTaskToSQLTaskActor(ctx, &dao.Ticket{TaskId: "1", TicketId: 1})
}

func TestVeDBStopTicket(t *testing.T) {
	vedbActor := mockVeDBOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock((*mocks.MockContext).ClientOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock0000.UnPatch()
	baseMock000000 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock000000.UnPatch()

	baseMock00000 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	vedbActor.StopTicket(ctx, &shared.StopTicket{})
	baseMock00000.UnPatch()

	baseMock00001 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.StopDDLTaskResp{}, nil).Build()
	vedbActor.StopTicket(ctx, &shared.StopTicket{})
	baseMock00001.UnPatch()

	baseMock00002 := mockey.Mock((*mocks.MockKindClient).Call).Return("", nil).Build()
	vedbActor.StopTicket(ctx, &shared.StopTicket{})
	baseMock00002.UnPatch()

}
