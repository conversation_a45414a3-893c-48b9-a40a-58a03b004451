package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/parser"
	sqltask_svc "code.byted.org/infcs/dbw-mgr/biz/service/sqltask"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"fmt"
	"github.com/robfig/cron/v3"
	"go.uber.org/dig"
	"runtime/debug"
	"time"
)

const OnlineDDLDefaultTimeDuration = 10
const OnlineDDLTicketTimeout = time.Duration(OnlineDDLDefaultTimeDuration) * time.Second

type OnlineDDLTicketActorIn struct {
	dig.In
	Conf          config.ConfigProvider
	WorkflowDal   dal.WorkflowDAL
	IdgenSvc      idgen.Service
	Sp            parser.CommandParser
	CmdRepo       repository.CommandRepo
	TicketService workflow.TicketService
	SqlTaskSvc    sqltask_svc.SqlTaskService
}

// NewOnlineDDLTicketActor 执行OnlineDDL工单的actor
func NewOnlineDDLTicketActor(p OnlineDDLTicketActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.OnlineDDLTicketActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			OnlineDDLActor := &OnlineDDLTicketActor{
				state:         newOnlineDDLTicketState(state),
				cnf:           p.Conf,
				workflowDal:   p.WorkflowDal,
				idgenSvc:      p.IdgenSvc,
				sp:            p.Sp,
				cmdRepo:       p.CmdRepo,
				ticketService: p.TicketService,
				sqlTaskSvc:    p.SqlTaskSvc,
				cron:          cron.New(),
			}
			return OnlineDDLActor
		}),
	}
}

func newOnlineDDLTicketState(bytes []byte) *OnlineDDLTicketState {
	ts := &OnlineDDLTicketState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		return nil
	}
	return ts
}

type OnlineDDLTicketState struct {
	SessionId        string                      `json:"session_id"`
	ConnectionId     string                      `json:"connection_id"`
	Cs               *entity.CommandSet          `json:"command_set"`
	Ds               *shared.DataSource          `json:"datasource"`
	CurrentAction    model.OnlineDDLTicketAction `json:"current_action"`
	LogID            string                      `json:"log_id"`
	TenantId         string                      `json:"tenant_id"`
	UserId           string                      `json:"user_id"`
	TicketType       int32                       `json:"ticket_type"`
	IsDDLTaskCreated bool                        `json:"is_ddl_task_created"`
}

type OnlineDDLTicketActor struct {
	state         *OnlineDDLTicketState
	cnf           config.ConfigProvider
	workflowDal   dal.WorkflowDAL
	idgenSvc      idgen.Service
	sp            parser.CommandParser
	cmdRepo       repository.CommandRepo
	ticketService workflow.TicketService
	sqlTaskSvc    sqltask_svc.SqlTaskService
	cron          *cron.Cron
}

func (e *OnlineDDLTicketActor) GetState() []byte {
	state, _ := json.Marshal(e.state)
	return state
}

func (e *OnlineDDLTicketActor) Process(ctx types.Context) {
	log.Info(ctx, "online ddl ticket actor start!!")
	if e.state == nil {
		e.state = new(OnlineDDLTicketState)
	}
	e.BuildCtx(ctx)

	switch msg := ctx.Message().(type) {
	case *actor.Started:
		// 启动时候做的事情
		e.protectUserCall(ctx, func() {
			e.OnStart(ctx)
		})
	case *shared.ExecTicket:
		e.protectUserCall(ctx, func() {
			e.ExecTicket(ctx, msg)
		})
	case *shared.StopTicket:
		e.protectUserCall(ctx, func() {
			e.StopTicket(ctx, msg)
		})
	case *actor.ReceiveTimeout:
		e.protectUserCall(ctx, func() {
			e.GetCommandSetResult(ctx) // 每10s调用一下获取结果的命令
			ctx.SetReceiveTimeout(OnlineDDLTicketTimeout)
		})
	case *actor.Stopped:
		log.Info(ctx, "OnlineDDLTicketActor %s stop", ctx.GetName())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}

func (e *OnlineDDLTicketActor) BuildCtx(ctx types.Context) {
	var logID, tenantID, userID string
	if e.state.LogID != "" {
		logID = e.state.LogID
	} else {
		logID = "ticket-" + ctx.GetName()
	}
	if e.state.TenantId != "" {
		tenantID = e.state.TenantId
	}
	if e.state.UserId != "" {
		userID = e.state.UserId
	}
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: tenantID,
		UserID:   userID,
		LogID:    logID,
	})
}

func (e *OnlineDDLTicketActor) OnStart(ctx types.Context) {
	ctx.SetReceiveTimeout(OnlineDDLTicketTimeout)
	log.Info(ctx, "OnlineDDLTicketActor %s start,CurrentAction is %s,tenant is %s", ctx.GetName(), e.state.CurrentAction.String(), fwctx.GetTenantID(ctx))
	// 如果是挂了重启,这个时候需要做修复的动作,仅修复状态不为"执行完成"或者"执行失败"的
	if e.state.CurrentAction == model.OnlineDDLTicketAction_ExecuteFinished || e.state.CurrentAction == model.OnlineDDLTicketAction_ExecuteFailed {
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	e.processOnlineDDLTicketAction(ctx)
}

func (e *OnlineDDLTicketActor) ExecTicket(ctx types.Context, msg *shared.ExecTicket) {
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: msg.TenantID,
		UserID:   msg.UserID,
	})
	log.Info(ctx, "ticket: actor receive command,datasource is %v,tenant is %s,msg tenant id is %s", msg.Source, fwctx.GetTenantID(ctx), msg.TenantID)
	e.state.TenantId = msg.TenantID
	e.state.Ds = msg.Source
	e.state.SessionId = ctx.GetName() // 如果是SQL变更类型的工单，则以ticketId作为sessionId来发起session

	// 1、更新当前的actor状态
	e.updateExecTicketActorState(ctx, model.OnlineDDLTicketAction_ReceiveCommand)

	// 2、处理不同类型的工单
	switch msg.TicketType {
	case int32(model.TicketType_FreeLockStructChange):
		e.execStructChangeTicket(ctx, msg) // 结构变更
		return
	default:
		log.Warn(ctx, "unknown ticket type : %v", msg.TicketType)
		return
	}
}

func (e *OnlineDDLTicketActor) execStructChangeTicket(ctx types.Context, msg *shared.ExecTicket) {
	// 2.1 所有种类的DDL工单审批过后如果已经到达了截止时间,则直接提示工单超过截止时间，关闭即可
	if msg.ExecutableEndTime != 0 && time.Now().Unix() > int64(msg.ExecutableEndTime) {
		log.Info(ctx, "ticket: %s out of execute time window", ctx.GetName())
		e.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError),
			Description: "cannot execute this ticket,because current time exceed ticket end time"})
		ctx.Respond(&shared.TicketExecuted{
			TicketId: ctx.GetName(),
			Code:     shared.ExecutedFail,
			Message:  "执行时间超过可执行时间窗口,不予执行",
		})
		e.updateExecTicketActorState(ctx, model.OnlineDDLTicketAction_ExecuteFailed)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	// 2.2 定时DDL工单如果未到执行时间,计算一下和起始时间的时间差,并设置为ctx的ReceiveTimeOut
	if msg.ExecuteType == int32(model.ExecuteType_Cron) && time.Now().Unix() < int64(msg.ExecutableStartTime) {
		timeDelta := int64(msg.ExecutableStartTime) - time.Now().Unix()
		if timeDelta < 0 {
			timeDelta = 0
		}
		log.Info(ctx, "ticket: %s timeDelta is %v s", ctx.GetName(), timeDelta)
		ctx.SetReceiveTimeout(time.Duration(timeDelta+1) * time.Second)
		ctx.Respond(&shared.TicketExecuted{
			TicketId: ctx.GetName(),
			Code:     shared.ExecutedStart,
			Message:  "执行DDL工单任务创建成功",
		})
		return
	}
	// 2.3 定时DDL工单如果在执行时间范围 或者 普通DDL工单,则直接执行即可
	err := e.CreateOnlineDDLTask(ctx)
	if err != nil {
		// 如果创建Online DDL失败
		// a、打印日志
		// b、更新失败原因
		// c、返回工单任务发起失败的消息
		// d、停止actor
		log.Warn(ctx, "Online DDL Ticket err:%s", err.Error())
		e.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError), Description: err.Error()})
		ctx.Respond(&shared.TicketExecuted{
			TicketId: ctx.GetName(),
			Code:     shared.ExecutedFail,
			Message:  fmt.Sprintf("执行工单任务发起失败,%s", err.Error()),
		})
		e.updateExecTicketActorState(ctx, model.OnlineDDLTicketAction_ExecuteFailed)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	e.state.IsDDLTaskCreated = true
	ctx.Respond(&shared.TicketExecuted{
		TicketId: ctx.GetName(),
		Code:     shared.ExecutedStart,
		Message:  "执行DDL工单任务发起成功",
	})
}

// UpdateTicketRepo 更新任务状态
func (e *OnlineDDLTicketActor) UpdateTicketRepo(ctx types.Context, ticket *dao.Ticket) error {
	log.Info(ctx, "update ticket status to %v", ticket.TicketStatus)
	ticket.TicketId = utils.MustStrToInt64(ctx.GetName())
	err := e.workflowDal.UpdateWorkStatus(ctx, ticket)
	if err != nil {
		log.Warn(ctx, "update ticket status to %v error", ticket.TicketStatus)
		return err
	}
	return nil
}

func (e *OnlineDDLTicketActor) updateExecTicketActorState(ctx types.Context, currentAction model.OnlineDDLTicketAction) {
	log.Info(ctx, "update OnlineDDLTicketActor action %s as action %s", e.state.CurrentAction.String(), currentAction.String())
	e.state.CurrentAction = currentAction
}

func (e *OnlineDDLTicketActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call ExecTicketActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (e *OnlineDDLTicketActor) processOnlineDDLTicketAction(ctx types.Context) {
	switch e.state.CurrentAction {
	case model.OnlineDDLTicketAction_ReceiveCommand:
		log.Info(ctx, "actor restart, try to exec ticket again")
		if e.state != nil && e.state.Ds != nil { // 说明是之前执行了一半,断掉了
			log.Info(ctx, "actor restart, send exec ticket message to exec ticket actor ")
			ticket, err := e.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
			if err != nil {
				log.Warn(ctx, "actor restart find ticket error:%s", err.Error())
				return
			}
			ctx.Send(ctx.Self(), &shared.ExecTicket{
				Source:              e.state.Ds,
				TenantID:            e.state.TenantId,
				UserID:              e.state.UserId,
				TicketType:          int32(ticket.TicketType),
				ExecuteType:         int32(ticket.ExecuteType),
				ExecutableStartTime: int32(ticket.ExecutableStartTime),
				ExecutableEndTime:   int32(ticket.ExecutableEndTime),
			})
		}
	}

}

func (e *OnlineDDLTicketActor) GetCommandSetResult(ctx types.Context) {
	ticket, err := e.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
	if err != nil {
		log.Warn(ctx, "ticket: get ticket from ticketId err:%s", err.Error())
		return
	}
	log.Info(ctx, "OnlineDDLActor Receive TimeOut, ticket is %s", utils.Show(ticket))
	// 1、如果是DDL工单,需要给sqlTaskActor发消息查询DDL工单的状态
	if ticket.TicketType == int8(model.TicketType_FreeLockStructChange) {
		e.DescribeOnlineDDLTask(ctx, ticket)
		return
	}
}

func (e *OnlineDDLTicketActor) CreateOnlineDDLTask(ctx types.Context) error {
	log.Info(ctx, "begin to deal online ddl,ticketId is %s", ctx.GetName())
	// 真正开始执行的时候,才更新工单状态为执行中
	if err := e.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketExecute)}); err != nil {
		log.Warn(ctx, "ticket: update ticket %s execute err:%s", ctx.GetName(), err.Error())
		return err
	}
	ticket, err := e.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
	if err != nil {
		return err
	}
	dsType, err := model.DSTypeFromString(ticket.InstanceType)
	if err != nil {
		return err
	}
	if !e.ticketService.IsInstanceRunning(ctx, ticket.InstanceId, shared.DataSourceType(dsType)) {
		log.Warn(ctx, "ticket:%s,instance %s is not running or instance not exist", ticket.TicketId, ticket.InstanceId)
		return consts.ErrorOf(model.ErrorCode_CheckInstanceError)
	}
	req := &model.CreateSqlTaskReq{
		InstanceId:   ticket.InstanceId,
		InstanceType: model.DSTypePtr(dsType),
		//TableName:    utils.StringRef("test"), // 表名字非必传
		DBName:      ticket.DbName,
		ExecSQL:     ticket.SqlText,
		Comment:     utils.StringRef(ticket.Description),
		SqlTaskType: model.SqlTaskTypePtr(model.SqlTaskType_OnlineDDL),
	}
	sqlTaskId, err := e.sqlTaskSvc.CreateOnlineDDLSqlTask(ctx, req)
	if err != nil {
		return err
	}
	if sqlTaskId == nil || *sqlTaskId == "" {
		log.Warn(ctx, "Online DDL ticket err %v", ticket.TicketId)
		return fmt.Errorf("ticket %d Online DDL Task id is empty", ticket.TicketId)
	}
	e.updateExecTicketActorState(ctx, model.OnlineDDLTicketAction_ExecuteDDL)
	// 到这里,才生成了Online DDL的任务ID，拿到ID之后，做2件事情
	// 1、如果用户设置了DDL截止时间，对这个DDL工单生成一个定时的关闭任务
	// 2、将DDL工单ID保存到工单数据库中
	if ticket.ExecutableEndTime != 0 {
		ticket.TaskId = *sqlTaskId
		e.generateStopCronTask(ctx, ticket)
	}
	return e.workflowDal.Save(ctx, &dao.Ticket{
		TicketId: ticket.TicketId,
		TaskId:   *sqlTaskId,
	})
}

func (e *OnlineDDLTicketActor) DescribeOnlineDDLTask(ctx types.Context, ticket *dao.Ticket) {
	log.Info(ctx, "ticket %d , begin to describe ddl task", ticket.TicketId)
	// 1、定时DDL任务
	// 如果是定时DDL任务，之前没有执行过,开始执行
	if ticket.ExecuteType == int8(model.ExecuteType_Cron) && !e.state.IsDDLTaskCreated {
		// 如果没有设置结束时间,则只需要判断当前时间是否等于开始时间
		if ticket.ExecutableEndTime == 0 && time.Now().Unix() > ticket.ExecutableStartTime {
			e.sendExecTicketMsg(ctx, ticket)
			return
		}
		// 如果设置了结束时间,则需要保证当前时间在时间窗口中
		if ticket.ExecutableEndTime > 0 && time.Now().Unix() > ticket.ExecutableStartTime && time.Now().Unix() < ticket.ExecutableEndTime {
			e.sendExecTicketMsg(ctx, ticket)
			return
		}
	}
	// 2、普通DDL任务 或者 已经执行过的定时DDL任务,则直接获取当前的状态
	resp, err := ctx.ClientOf(consts.SqlTaskActorKind).
		Call(ctx, generateSQLTaskName(ticket), &shared.GetDDLTaskStatusReq{SqlTaskId: ticket.TaskId, TenantId: e.state.TenantId})
	if err != nil {
		log.Error(ctx, "ticket: get ddl ticket status fail, %s", err.Error())
		return
	}
	switch rsp := resp.(type) {
	case *shared.GetDDLTaskStatusResp:
		log.Info(ctx, "ticket rsp is %#v", rsp)
		// 如果工单未开始、等待中、预检查、执行中,此时需要判断是否超过了执行时间
		if IsSqlTaskInProgress(rsp) {
			e.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId, int32(model.TicketStatus_TicketExecute),
				"ticket is executing,please wait...", rsp.Progress)
			// 如果到达了任务截止时间,则停止任务,停止actor
			if ticket.ExecutableEndTime > 0 && time.Now().Unix() > ticket.ExecutableEndTime {
				if err = e.doWhenReachEndTime(ctx, ticket); err != nil {
					log.Error(ctx, "ticket  %s execute failed,instance %s stop sql task %s error,please check", ticket.TicketId, rsp.InstanceId, rsp.SqlTaskId)
					return
				}
				log.Warn(ctx, "ticket %s reach endtime,stopped", ticket.TicketId)
				e.updateExecTicketActorState(ctx, model.OnlineDDLTicketAction_ExecuteFinished)
				if err = e.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId, int32(model.TicketStatus_TicketError),
					fmt.Sprintf("ticket is stopped,please check if time reach ticket endTime"), rsp.Progress); err != nil {
					log.Warn(ctx, "ticket:%s execute success,update ticket status error:%s ", ctx.GetName(), err.Error())
					return
				}
				ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			}
			return
		} else if rsp.SqlTaskStatus == model.SqlTaskStatus_Stop.String() {
			// 如果工单停止,更新状态和结果
			if err = e.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId, int32(model.TicketStatus_TicketError),
				fmt.Sprintf("ticket is stopped,please check if time reach ticket endTime. %v", rsp.Result), rsp.Progress); err != nil {
				log.Warn(ctx, "ticket:%s execute success,update ticket status error:%s ", ctx.GetName(), err.Error())
				return
			}
			log.Warn(ctx, "ticket:%s execute stopped,begin to suicide actor", ctx.GetName())
			e.updateExecTicketActorState(ctx, model.OnlineDDLTicketAction_ExecuteFinished)
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		} else if rsp.SqlTaskStatus == model.SqlTaskStatus_Success.String() {
			// 如果工单成功,更新数据库,返回工单成功
			if err = e.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId, int32(model.TicketStatus_TicketFinished),
				"ticket success", rsp.Progress); err != nil {
				log.Warn(ctx, "ticket:%s execute success,update ticket status error:%s ", ctx.GetName(), err.Error())
				return
			}
			log.Info(ctx, "ticket:%s execute success,begin to suicide actor", ctx.GetName())
			e.updateExecTicketActorState(ctx, model.OnlineDDLTicketAction_ExecuteFinished)
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		} else {
			// TODO 其他工单状态，更新数据库，返回失败,这里考虑终止之后的任务状态是什么，并更新上去
			e.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError),
				Description: fmt.Sprintf("ticket status abnormal,please contact admin.%v", rsp.Result)})
			log.Warn(ctx, "ticket:%s execute error,result is %s, begin to suicide", ctx.GetName(), rsp.Result)
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
	case *shared.GetDDLTaskStatusErr:
		log.Info(ctx, "ticket rsp is %#v", rsp)
		if err = e.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError), Description: rsp.Message}); err != nil {
			return
		}
		log.Warn(ctx, "ticket:%s execute error,begin to suicide", ctx.GetName())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	default:
		log.Warn(ctx, "unknown resp type,%v", rsp)
		return
	}
}

func (e *OnlineDDLTicketActor) sendExecTicketMsg(ctx types.Context, ticket *dao.Ticket) {
	log.Info(ctx, "ticket %d , begin to execute ddl task", ticket.TicketId)
	ctx.Send(ctx.Self(), &shared.ExecTicket{
		TenantID:            e.state.TenantId,
		UserID:              e.state.UserId,
		Source:              e.state.Ds,
		TicketType:          int32(ticket.TicketType),
		ExecuteType:         int32(ticket.ExecuteType),
		ExecutableStartTime: int32(ticket.ExecutableStartTime),
		ExecutableEndTime:   int32(ticket.ExecutableEndTime),
	})
}

// 如果是定时任务，之前已经执行过了，但是超过了执行末尾时间,需要发送请求终止这个OnlineDDL操作
func (e *OnlineDDLTicketActor) doWhenReachEndTime(ctx types.Context, ticket *dao.Ticket) error {
	if ticket.ExecuteType == int8(model.ExecuteType_Cron) && e.state.IsDDLTaskCreated {
		if _, err := ctx.ClientOf(consts.SqlTaskActorKind).Call(ctx, generateSQLTaskName(ticket),
			&shared.StopDDLTaskReq{SqlTaskId: ticket.TaskId, TenantId: ticket.TenantId}); err != nil {
			log.Error(ctx, "ticket: get ddl ticket status fail, %s", err.Error())
			return err
		}
	}
	return nil
}

func (e *OnlineDDLTicketActor) generateStopCronTask(ctx types.Context, ticket *dao.Ticket) {
	e.cron = cron.New(cron.WithSeconds())
	e.cron.Start()
	spec := getCronTime(ticket.ExecutableEndTime)
	log.Info(ctx, "ticket: generate spec is %s", spec)
	addFunc, err := e.cron.AddFunc(spec, func() {
		SendStopOnlineDDLTaskToSQLTaskActor(ctx, ticket)
	})
	log.Info(ctx, "ticket: addFunc entry is %v", addFunc)
	if err != nil {
		return
	}
}

func SendStopOnlineDDLTaskToSQLTaskActor(ctx types.Context, ticket *dao.Ticket) {
	log.Info(ctx, "ticket: time is %v, begin to stop ticket,actor name is %v", time.Now(), generateSQLTaskName(ticket))
	err := ctx.ClientOf(consts.SqlTaskActorKind).Send(ctx, generateSQLTaskName(ticket), &shared.StopDDLTaskReq{
		SqlTaskId: ticket.TaskId,
		TenantId:  ticket.TenantId,
	})
	if err != nil {
		log.Warn(ctx, "ticket: stop online ddl err:%s", err.Error())
		return
	}
}

func (e *OnlineDDLTicketActor) StopTicket(ctx types.Context, msg *shared.StopTicket) {
	// 给sql_task_actor发消息
	log.Info(ctx, "stopTicket: begin to stop ticket,msg is %s", utils.Show(msg))
	resp, err := ctx.ClientOf(consts.SqlTaskActorKind).
		Call(ctx, generateSQLTaskName(&dao.Ticket{TicketId: msg.TicketId, TaskId: msg.TaskId, TenantId: msg.TenantId, InstanceId: msg.InstanceId}),
			&shared.StopDDLTaskReq{SqlTaskId: msg.TaskId, TenantId: msg.TenantId})
	if err != nil {
		// 发送告警
		log.Error(ctx, "stopTicket: stop ddl ticket fail, %s", err.Error())
		return
	}
	// 处理结果
	switch rsp := resp.(type) {
	case *shared.StopDDLTaskResp:
		log.Info(ctx, "stopTicket: get stop ddl task resp from sql task actor success: %s", utils.Show(rsp))
		ctx.Respond(&shared.StopTicketResp{
			TicketId:   utils.Int64ToStr(msg.TicketId),
			ErrMessage: rsp.ErrMessage,
			Status:     rsp.Status,
		})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	default:
		// 发送告警
		log.Error(ctx, "stopTicket: stop ddl ticket fail,%v", rsp)
		ctx.Respond(&shared.StopTicketResp{})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}
