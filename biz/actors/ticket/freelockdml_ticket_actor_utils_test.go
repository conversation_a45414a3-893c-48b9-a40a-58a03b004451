package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/bytedance/mockey"
	"testing"
)

func TestIsInstanceExistRunningFreeLockDMLTicket(t *testing.T) {
	freelockDmlActor := mockFreeLockDMLTicketActor()

	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock8 := mockey.Mock((*mocks.MockWorkflowDAL).GetTicketByInstanceID).Return([]*dao.Ticket{
		{
			TicketId: 1,
		}, {
			TicketId: 2,
		}}, nil).Build()
	defer baseMock8.UnPatch()
	baseMock5 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock5.UnPatch()
	baseMock7 := mockey.Mock((*FreeLockDMLActor).UpdateTicketRepo).Return(nil).Build()
	defer baseMock7.UnPatch()
	freelockDmlActor.IsInstanceExistRunningFreeLockDMLTicket(ctx, &shared.ExecFreeLockDMLTicket{
		BizContext:          "",
		TenantID:            "",
		UserID:              "",
		Source:              &shared.DataSource{},
		TicketType:          0,
		ExecuteType:         0,
		SqlText:             "",
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
		CreateFrom:          "",
		Hint:                "",
	})
}
