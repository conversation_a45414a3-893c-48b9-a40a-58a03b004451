package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/parser"
	sqltask_svc "code.byted.org/infcs/dbw-mgr/biz/service/sqltask"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"fmt"
	"github.com/robfig/cron/v3"
	"go.uber.org/dig"
	"runtime/debug"
	"time"
)

const VeDBTimeDuration = 5
const VedbDDLTicketTimeout = time.Duration(VeDBTimeDuration) * time.Second

type VeDBDDLTicketActorIn struct {
	dig.In
	Conf          config.ConfigProvider
	WorkflowDal   dal.WorkflowDAL
	IdgenSvc      idgen.Service
	Sp            parser.CommandParser
	CmdRepo       repository.CommandRepo
	TicketService workflow.TicketService
	SqlTaskSvc    sqltask_svc.SqlTaskService
}

// NewVeDBDDLTicketActor 执行工单的actor
func NewVeDBDDLTicketActor(p VeDBDDLTicketActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.VeDBDDLTicketActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			VeDBOnlineDDLActor := &VeDBDDLTicketActor{
				state:         newVeDBDDLTicketState(state),
				cnf:           p.Conf,
				workflowDal:   p.WorkflowDal,
				idgenSvc:      p.IdgenSvc,
				sp:            p.Sp,
				cmdRepo:       p.CmdRepo,
				ticketService: p.TicketService,
				sqlTaskSvc:    p.SqlTaskSvc,
				cron:          cron.New(),
			}
			return VeDBOnlineDDLActor
		}),
	}
}

func newVeDBDDLTicketState(bytes []byte) *VeDBDDLTicketState {
	ts := &VeDBDDLTicketState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		return nil
	}
	return ts
}

type VeDBDDLTicketState struct {
	SessionId        string                    `json:"session_id"`
	ConnectionId     string                    `json:"connection_id"`
	Cs               *entity.CommandSet        `json:"command_set"`
	Ds               *shared.DataSource        `json:"datasource"`
	CurrentAction    model.VeDBDDLTicketAction `json:"current_action"`
	LogID            string                    `json:"log_id"`
	TenantId         string                    `json:"tenant_id"`
	UserId           string                    `json:"user_id"`
	TicketType       int32                     `json:"ticket_type"`
	IsDDLTaskCreated bool                      `json:"is_ddl_task_created"`
}

type VeDBDDLTicketActor struct {
	state         *VeDBDDLTicketState
	cnf           config.ConfigProvider
	workflowDal   dal.WorkflowDAL
	idgenSvc      idgen.Service
	sp            parser.CommandParser
	cmdRepo       repository.CommandRepo
	ticketService workflow.TicketService
	sqlTaskSvc    sqltask_svc.SqlTaskService
	cron          *cron.Cron
}

func (v *VeDBDDLTicketActor) GetState() []byte {
	state, _ := json.Marshal(v.state)
	return state
}

func (v *VeDBDDLTicketActor) Process(ctx types.Context) {

	switch msg := ctx.Message().(type) {
	case *actor.Started:
		// 启动时候做的事情
		v.protectUserCall(ctx, func() {
			v.OnStart(ctx)
		})
	case *shared.ExecVeDBDDLTicket:
		v.protectUserCall(ctx, func() {
			v.ExecVeDBDDLTicket(ctx, msg)
		})
	case *shared.StopTicket:
		v.protectUserCall(ctx, func() {
			v.StopTicket(ctx, msg)
		})
	case *actor.ReceiveTimeout:
		v.protectUserCall(ctx, func() {
			v.GetVeDBTicketStatus(ctx) // 每10s调用一下获取结果的命令
			ctx.SetReceiveTimeout(VedbDDLTicketTimeout)
		})
	case *actor.Stopped:
		log.Info(ctx, "ExecTicketActor %s stop", ctx.GetName())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}

func (v *VeDBDDLTicketActor) OnStart(ctx types.Context) {
	if v.state == nil {
		v.state = new(VeDBDDLTicketState)
	}
	log.Info(ctx, "VedbDDLTicketActor %s start,CurrentAction is %s,tenant is %s",
		ctx.GetName(), v.state.CurrentAction.String(), fwctx.GetTenantID(ctx))
	// 修复工单状态
	v.repairTicketState(ctx)
}

func (v *VeDBDDLTicketActor) repairTicketState(ctx types.Context) {
	switch v.state.CurrentAction {
	case model.VeDBDDLTicketAction_Started:
		return
	case model.VeDBDDLTicketAction_ReceiveCommand,
		model.VeDBDDLTicketAction_CreateSession,
		model.VeDBDDLTicketAction_ExecuteCommand:
		log.Info(ctx, "actor restart, try to exec ticket again")
		if v.state != nil && v.state.Ds != nil { // 说明是之前执行了一半,断掉了
			if v.state.TenantId != "" {
				ctx.WithValue("biz-context", &fwctx.BizContext{
					TenantID: v.state.TenantId,
					UserID:   v.state.UserId,
				})
			}
			log.Info(ctx, "actor restart, send exec ticket message to exec ticket actor ")
			ticket, err := v.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
			if err != nil {
				log.Warn(ctx, "actor restart find ticket error:%s", err.Error())
				return
			}
			ctx.Send(ctx.Self(), &shared.ExecVeDBDDLTicket{
				Source:              v.state.Ds,
				TenantID:            v.state.TenantId,
				UserID:              v.state.UserId,
				TicketType:          int32(ticket.TicketType),
				ExecuteType:         int32(ticket.ExecuteType),
				ExecutableStartTime: int32(ticket.ExecutableStartTime),
				ExecutableEndTime:   int32(ticket.ExecutableEndTime),
			})
		}
	case model.VeDBDDLTicketAction_ExecuteDDL:
		ctx.SetReceiveTimeout(VedbDDLTicketTimeout)
		return
	// 状态为"执行完成"或者"执行失败"的,没有必要修复,直接停止actor
	case model.VeDBDDLTicketAction_ExecuteFailed, model.VeDBDDLTicketAction_ExecuteFinished:
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	default:
		return
	}
}

func (v *VeDBDDLTicketActor) ExecVeDBDDLTicket(ctx types.Context, msg *shared.ExecVeDBDDLTicket) {
	log.Info(ctx, "ticket: actor receive command,datasource is %v,tenant is %s,msg tenant id is %s",
		msg.Source, fwctx.GetTenantID(ctx), msg.TenantID)

	// 1、更新当前的actor状态,并返回工单任务发起成功
	v.initState(ctx, msg)
	v.updateActorState(ctx, model.VeDBDDLTicketAction_ReceiveCommand)
	ctx.Respond(&shared.TicketExecuted{
		TicketId: ctx.GetName(),
		Code:     shared.ExecutedStart,
		Message:  "DDL ticket task initiation successfully",
	})

	// 2、此处需要考虑 定时DDL工单 和 普通DDL工单 2种情况：
	// 2.1 所有种类的DDL工单审批过后如果已经到达了截止时间,则直接提示工单超过截止时间，关闭即可
	if v.IsNowExceedExecEndTime(ctx, msg) {
		v.doWhenReachExecEndTime(ctx)
		return
	}
	// 2.2 定时DDL工单如果未到执行时间,计算一下和起始时间的时间差,并设置为ctx的ReceiveTimeOut
	if v.IsNowUnReachCronTime(ctx, msg) {
		v.doWhenUnReachCronTime(ctx, msg)
		return
	}
	// 2.3 定时DDL工单如果在执行时间范围 或者 普通DDL工单,则直接执行即可
	err := v.CreateOnlineDDLTask(ctx)
	if err != nil {
		log.Warn(ctx, "Online DDL Ticket err:%s", err.Error())
		v.UpdateTicketRepo(ctx, &dao.Ticket{
			TicketStatus: int8(model.TicketStatus_TicketError),
			Description:  err.Error()})
		ctx.Respond(&shared.TicketExecuted{
			TicketId: ctx.GetName(),
			Code:     shared.ExecutedFail,
			Message:  fmt.Sprintf("Ticket task initiation failed, %s", err.Error()),
		})
		v.updateActorState(ctx, model.VeDBDDLTicketAction_ExecuteFailed)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}

	// 3、标记任务已经开始，设置ReceiveTimeOut
	v.state.IsDDLTaskCreated = true
	ctx.SetReceiveTimeout(VedbDDLTicketTimeout)

	log.Info(ctx, "online ddl task start,tenant is %s,user is %s ",
		fwctx.GetTenantID(ctx), fwctx.GetUserID(ctx))
}

func (v *VeDBDDLTicketActor) IsNowExceedExecEndTime(ctx types.Context, msg *shared.ExecVeDBDDLTicket) bool {
	return msg.ExecutableEndTime != 0 && time.Now().Unix() > int64(msg.ExecutableEndTime)
}

func (v *VeDBDDLTicketActor) doWhenReachExecEndTime(ctx types.Context) {
	log.Info(ctx, "ticket: %s out of execute time window", ctx.GetName())
	v.UpdateTicketRepo(ctx, &dao.Ticket{
		TicketStatus: int8(model.TicketStatus_TicketError),
		Description:  "The execution time exceeds the allowable execution time window and will not be executed.",
	})
	ctx.Respond(&shared.TicketExecuted{
		TicketId: ctx.GetName(),
		Code:     shared.ExecutedFail,
		Message:  "The execution time exceeds the allowable execution time window and will not be executed.",
	})
	v.updateActorState(ctx, model.VeDBDDLTicketAction_ExecuteFailed)
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
}

func (v *VeDBDDLTicketActor) IsNowUnReachCronTime(ctx types.Context, msg *shared.ExecVeDBDDLTicket) bool {
	return msg.ExecuteType == int32(model.ExecuteType_Cron) && time.Now().Unix() < int64(msg.ExecutableStartTime)
}

func (v *VeDBDDLTicketActor) doWhenUnReachCronTime(ctx types.Context, msg *shared.ExecVeDBDDLTicket) {
	timeDelta := int64(msg.ExecutableStartTime) - time.Now().Unix()
	if timeDelta < 0 {
		timeDelta = 0
	}
	log.Info(ctx, "ticket: %s timeDelta is %v s", ctx.GetName(), timeDelta)
	ctx.SetReceiveTimeout(time.Duration(timeDelta+1) * time.Second)
	ctx.Respond(&shared.TicketExecuted{
		TicketId: ctx.GetName(),
		Code:     shared.ExecutedStart,
		Message:  "DDL ticket task created successfully",
	})
}

func (v *VeDBDDLTicketActor) initState(ctx types.Context, msg *shared.ExecVeDBDDLTicket) {
	v.state.TenantId = msg.TenantID
	v.state.Ds = msg.Source
	v.state.SessionId = ctx.GetName() // 如果是SQL变更类型的工单，则以ticketId作为sessionId来发起session
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: msg.TenantID,
		UserID:   msg.UserID,
	})
}

// UpdateTicketRepo 更新任务状态
func (v *VeDBDDLTicketActor) UpdateTicketRepo(ctx types.Context, ticket *dao.Ticket) error {
	log.Info(ctx, "update ticket status to %v", ticket.TicketStatus)
	ticket.TicketId = utils.MustStrToInt64(ctx.GetName())
	err := v.workflowDal.UpdateWorkStatus(ctx, ticket)
	if err != nil {
		log.Warn(ctx, "update ticket status to %v error", ticket.TicketStatus)
		return err
	}
	return nil
}

func (v *VeDBDDLTicketActor) updateActorState(ctx types.Context, currentAction model.VeDBDDLTicketAction) {
	log.Info(ctx, "update VeDBDDLTicketActor action %s as action %s", v.state.CurrentAction.String(), currentAction.String())
	v.state.CurrentAction = currentAction
}

func (v *VeDBDDLTicketActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call ExecTicketActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (v *VeDBDDLTicketActor) GetVeDBTicketStatus(ctx types.Context) {
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: v.state.TenantId,
		UserID:   v.state.UserId,
	})
	log.Info(ctx, "GetVeDBTicketStatus tenant is %s,user is %s ", fwctx.GetTenantID(ctx), fwctx.GetUserID(ctx))

	ticket, err := v.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
	if err != nil {
		log.Warn(ctx, "ticket: get ticket from ticketId err:%s", err.Error())
		return
	}
	log.Info(ctx, "VeDBOnlineDDLActor Receive TimeOut, ticket is %s", utils.Show(ticket))

	// 给sqlTaskActor发消息查询DDL工单的状态
	v.DescribeOnlineDDLTask(ctx, ticket)
}

func (v *VeDBDDLTicketActor) CreateOnlineDDLTask(ctx types.Context) error {
	log.Info(ctx, "begin to deal online ddl,ticketId is %s", ctx.GetName())
	// 1、更新工单状态为执行中
	v.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketExecute)})
	ticket, err := v.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
	if err != nil {
		log.Error(ctx, "ticket %s: get ticket error", ctx.GetName(), err.Error())
		return err
	}
	dsType, err := model.DSTypeFromString(ticket.InstanceType)
	if err != nil {
		log.Warn(ctx, "Unknown dsType,err %s", err.Error())
		return err
	}

	if !v.ticketService.IsInstanceRunning(ctx, ticket.InstanceId, shared.DataSourceType(dsType)) {
		log.Warn(ctx, "ticket:%s,instance %s is not running or instance not exist", ticket.TicketId, ticket.InstanceId)
		return consts.ErrorOf(model.ErrorCode_CheckInstanceError)
	}
	//不加白
	//if !bizUtils.IsTenantEnabledFromCtx(ctx, v.cnf.Get(ctx).EnableOnlineDDLTenantIDList) {
	//	return consts.ErrorOf(model.ErrorCode_NotSupportUseTheFunction)
	//}
	req := &model.CreateSqlTaskReq{
		InstanceId:   ticket.InstanceId,
		InstanceType: model.DSTypePtr(dsType),
		//TableName:    utils.StringRef("test"), // 表名字非必传
		DBName:      ticket.DbName,
		ExecSQL:     ticket.SqlText,
		Comment:     utils.StringRef(ticket.Description),
		SqlTaskType: model.SqlTaskTypePtr(model.SqlTaskType_OnlineDDL),
	}
	sqlTaskId, err := v.sqlTaskSvc.CreateOnlineDDLSqlTask(ctx, req)
	if err != nil {
		log.Warn(ctx, "create online ddl sql task error:%s", err.Error())
		return err
	}
	if sqlTaskId == nil || *sqlTaskId == "" {
		log.Warn(ctx, "Online DDL ticket err %v", ticket.TicketId)
		return fmt.Errorf("ticket %d Online DDL Task id is empty", ticket.TicketId)
	}
	v.updateActorState(ctx, model.VeDBDDLTicketAction_ExecuteDDL)

	// 到这里,才生成了Online DDL的任务ID，拿到ID之后，做2件事情
	// 1、如果用户设置了DDL截止时间，对这个DDL工单生成一个定时的关闭任务
	// 2、将DDL工单ID保存到工单数据库中
	if ticket.ExecutableEndTime != 0 {
		ticket.TaskId = *sqlTaskId
		v.generateStopCronTask(ctx, ticket)
	}
	return v.workflowDal.Save(ctx, &dao.Ticket{
		TicketId: ticket.TicketId,
		TaskId:   *sqlTaskId,
	})
}

func (v *VeDBDDLTicketActor) DescribeOnlineDDLTask(ctx types.Context, ticket *dao.Ticket) {
	log.Info(ctx, "ticket %d , begin to describe ddl task", ticket.TicketId)
	// 1、定时DDL任务
	// 如果是定时DDL任务，之前没有执行过,开始执行
	if ticket.ExecuteType == int8(model.ExecuteType_Cron) && !v.state.IsDDLTaskCreated {
		// 如果没有设置结束时间,则只需要判断当前时间是否等于开始时间
		if ticket.ExecutableEndTime == 0 && time.Now().Unix() > ticket.ExecutableStartTime {
			v.sendExecTicketMsg(ctx, ticket)
			return
		}
		// 如果设置了结束时间,则需要保证当前时间在时间窗口中
		if ticket.ExecutableEndTime > 0 && time.Now().Unix() > ticket.ExecutableStartTime && time.Now().Unix() < ticket.ExecutableEndTime {
			v.sendExecTicketMsg(ctx, ticket)
			return
		}
	}
	// 2、普通DDL任务 或者 已经执行过的定时DDL任务,则直接获取当前的状态
	resp, err := ctx.ClientOf(consts.SqlTaskActorKind).
		Call(ctx, generateSQLTaskName(ticket), &shared.GetDDLTaskStatusReq{SqlTaskId: ticket.TaskId, TenantId: v.state.TenantId})
	if err != nil {
		log.Error(ctx, "ticket: get ddl ticket status fail, %s", err.Error())
		return
	}
	switch rsp := resp.(type) {
	case *shared.GetDDLTaskStatusResp:
		log.Info(ctx, "ticket rsp is %#v", rsp)
		// 如果工单未开始、等待中、预检查、执行中,此时需要判断是否超过了执行时间
		if IsSqlTaskInProgress(rsp) {
			v.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId,
				int32(model.TicketStatus_TicketExecute), "The ticket is being executed, be patient", rsp.Progress)
			// 如果到达了任务截止时间,则停止任务,停止actor
			if ticket.ExecutableEndTime > 0 && time.Now().Unix() > ticket.ExecutableEndTime {
				err = v.doWhenReachEndTime(ctx, ticket)
				if err != nil {
					log.Error(ctx, "ticket  %s execute failed,instance %s stop sql task %s error,please check",
						ticket.TicketId, rsp.InstanceId, rsp.SqlTaskId)
					return
				}
				log.Info(ctx, "ticket %s reach endtime,stopped", ticket.TicketId)
				v.updateActorState(ctx, model.VeDBDDLTicketAction_ExecuteFinished)
				if err = v.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId,
					int32(model.TicketStatus_TicketError), "The ticket has been automatically terminated. Please check if the set ticket deadline has been reached", rsp.Progress); err != nil {
					log.Warn(ctx, "ticket:%s execute success,update ticket status error:%s ", ctx.GetName(), err.Error())
					return
				}
				ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			}
			return
		} else if rsp.SqlTaskStatus == model.SqlTaskStatus_Stop.String() {
			// 如果工单停止,更新状态和结果
			if err = v.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId,
				int32(model.TicketStatus_TicketError), "The ticket has been automatically terminated. Please check if the set ticket deadline has been reached", rsp.Progress); err != nil {
				log.Warn(ctx, "ticket:%s execute success,update ticket status error:%s ", ctx.GetName(), err.Error())
				return
			}
			log.Info(ctx, "ticket:%s execute stopped,begin to suicide actor", ctx.GetName())
			v.updateActorState(ctx, model.VeDBDDLTicketAction_ExecuteFinished)
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		} else if rsp.SqlTaskStatus == model.SqlTaskStatus_Success.String() {
			// 如果工单成功,更新数据库,返回工单成功
			if err = v.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId,
				int32(model.TicketStatus_TicketFinished), "Ticket execution successful", rsp.Progress); err != nil {
				log.Warn(ctx, "ticket:%s execute success,update ticket status error:%s ", ctx.GetName(), err.Error())
				return
			}
			log.Info(ctx, "ticket:%s execute success,begin to suicide actor", ctx.GetName())
			v.updateActorState(ctx, model.VeDBDDLTicketAction_ExecuteFinished)
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		} else {
			var desc = "The ticket status is abnormal. Please contact the administrator for assistance."
			if rsp.Result != "" {
				desc = rsp.Result
			}

			if err = v.UpdateTicketRepo(ctx, &dao.Ticket{
				TicketStatus: int8(model.TicketStatus_TicketError),
				Description:  desc}); err != nil {
				return
			}
			log.Warn(ctx, "ticket:%s execute error,result is %s, begin to suicide", ctx.GetName(), rsp.Result)
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
	case *shared.GetDDLTaskStatusErr:
		log.Info(ctx, "ticket rsp is %#v", rsp)
		if err = v.UpdateTicketRepo(ctx, &dao.Ticket{
			TicketStatus: int8(model.TicketStatus_TicketError),
			Description:  rsp.Message}); err != nil {
			return
		}
		log.Warn(ctx, "ticket:%s execute error,begin to suicide", ctx.GetName())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	default:
		log.Warn(ctx, "unknown resp type,%v", rsp)
		return
	}
}

func (v *VeDBDDLTicketActor) sendExecTicketMsg(ctx types.Context, ticket *dao.Ticket) {
	log.Info(ctx, "ticket %d , begin to execute ddl task", ticket.TicketId)
	ctx.Send(ctx.Self(), &shared.ExecVeDBDDLTicket{
		TenantID:            v.state.TenantId,
		UserID:              v.state.UserId,
		Source:              v.state.Ds,
		TicketType:          int32(ticket.TicketType),
		ExecuteType:         int32(ticket.ExecuteType),
		ExecutableStartTime: int32(ticket.ExecutableStartTime),
		ExecutableEndTime:   int32(ticket.ExecutableEndTime),
	})
}

// 如果是定时任务，之前已经执行过了，但是超过了执行末尾时间,需要发送请求终止这个OnlineDDL操作
func (v *VeDBDDLTicketActor) doWhenReachEndTime(ctx types.Context, ticket *dao.Ticket) error {
	if ticket.ExecuteType == int8(model.ExecuteType_Cron) && v.state.IsDDLTaskCreated {
		_, err := ctx.ClientOf(consts.SqlTaskActorKind).
			Call(ctx, generateSQLTaskName(ticket), &shared.StopDDLTaskReq{SqlTaskId: ticket.TaskId, TenantId: ticket.TenantId})
		if err != nil {
			log.Error(ctx, "ticket: get ddl ticket status fail, %s", err.Error())
			return err
		}
	}
	return nil
}

func (v *VeDBDDLTicketActor) generateStopCronTask(ctx types.Context, ticket *dao.Ticket) {
	v.cron = cron.New(cron.WithSeconds())
	v.cron.Start()
	spec := getCronTime(ticket.ExecutableEndTime)
	log.Info(ctx, "ticket: generate spec is %s", spec)
	addFunc, err := v.cron.AddFunc(spec, func() {
		v.sendStopVeDBDDLTaskToSQLTaskActor(ctx, ticket)
	})
	log.Info(ctx, "ticket: addFunc entry is %v", addFunc)
	if err != nil {
		return
	}

}

func (v *VeDBDDLTicketActor) sendStopVeDBDDLTaskToSQLTaskActor(ctx types.Context, ticket *dao.Ticket) {
	log.Info(ctx, "ticket: time is %v, begin to stop ticket,actor name is %v", time.Now(), generateSQLTaskName(ticket))
	err := ctx.ClientOf(consts.SqlTaskActorKind).Send(ctx, generateSQLTaskName(ticket), &shared.StopDDLTaskReq{
		SqlTaskId: ticket.TaskId,
		TenantId:  ticket.TenantId,
	})
	if err != nil {
		log.Warn(ctx, "ticket: stop online ddl err:%s", err.Error())
		return
	}
}

func (v *VeDBDDLTicketActor) StopTicket(ctx types.Context, msg *shared.StopTicket) {
	// 给sql_task_actor发消息
	log.Info(ctx, "stopTicket: begin to stop ticket,msg is %s", utils.Show(msg))
	resp, err := ctx.ClientOf(consts.SqlTaskActorKind).
		Call(ctx, generateSQLTaskName(&dao.Ticket{TicketId: msg.TicketId, TaskId: msg.TaskId, TenantId: msg.TenantId, InstanceId: msg.InstanceId}),
			&shared.StopDDLTaskReq{SqlTaskId: msg.TaskId, TenantId: msg.TenantId})
	if err != nil {
		// 发送告警
		log.Error(ctx, "stopTicket: stop ddl ticket fail, %s", err.Error())
		return
	}
	// 处理结果
	switch rsp := resp.(type) {
	case *shared.StopDDLTaskResp:
		log.Info(ctx, "stopTicket: get stop ddl task resp from sql task actor success: %s", utils.Show(rsp))
		ctx.Respond(&shared.StopTicketResp{
			TicketId:   utils.Int64ToStr(msg.TicketId),
			ErrMessage: rsp.ErrMessage,
			Status:     rsp.Status,
		})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	default:
		// 发送告警
		ctx.Respond(&shared.StopTicketResp{})
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}
