package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
	"code.byted.org/infcs/ds-sql-parser/format"
	. "code.byted.org/infcs/ds-sql-parser/opcode"
	"code.byted.org/infcs/ds-sql-parser/test_driver"
	"fmt"
	"github.com/pingcap/tidb/types"
	driver "github.com/pingcap/tidb/types/parser_driver"
	"regexp"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	bizUtils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"

	"github.com/qjpcpu/fp"
)

const CommonReason = "Ticket execution failed, contact the administrator"

var ipReg = regexp.MustCompile(`@.?\d+\.\d+\.\d+\.\d+.?`)

var Ops = map[Op]struct {
	name      string
	literal   string
	isKeyword bool
}{
	LogicAnd: {
		name:      "and",
		literal:   "AND",
		isKeyword: true,
	},
	LogicOr: {
		name:      "or",
		literal:   "OR",
		isKeyword: true,
	},
	LogicXor: {
		name:      "xor",
		literal:   "XOR",
		isKeyword: true,
	},
	LeftShift: {
		name:      "leftshift",
		literal:   "<<",
		isKeyword: false,
	},
	RightShift: {
		name:      "rightshift",
		literal:   ">>",
		isKeyword: false,
	},
	GE: {
		name:      "ge",
		literal:   ">=",
		isKeyword: false,
	},
	LE: {
		name:      "le",
		literal:   "<=",
		isKeyword: false,
	},
	EQ: {
		name:      "eq",
		literal:   "=",
		isKeyword: false,
	},
	NE: {
		name:      "ne",
		literal:   "!=", // perhaps should use `<>` here
		isKeyword: false,
	},
	LT: {
		name:      "lt",
		literal:   "<",
		isKeyword: false,
	},
	GT: {
		name:      "gt",
		literal:   ">",
		isKeyword: false,
	},
	Plus: {
		name:      "plus",
		literal:   "+",
		isKeyword: false,
	},
	Minus: {
		name:      "minus",
		literal:   "-",
		isKeyword: false,
	},
	And: {
		name:      "bitand",
		literal:   "&",
		isKeyword: false,
	},
	Or: {
		name:      "bitor",
		literal:   "|",
		isKeyword: false,
	},
	Mod: {
		name:      "mod",
		literal:   "%",
		isKeyword: false,
	},
	Xor: {
		name:      "bitxor",
		literal:   "^",
		isKeyword: false,
	},
	Div: {
		name:      "div",
		literal:   "/",
		isKeyword: false,
	},
	Mul: {
		name:      "mul",
		literal:   "*",
		isKeyword: false,
	},
	Not: {
		name:      "not",
		literal:   "!", // perhaps should use `NOT` here.
		isKeyword: false,
	},
	BitNeg: {
		name:      "bitneg",
		literal:   "~",
		isKeyword: false,
	},
	IntDiv: {
		name:      "intdiv",
		literal:   "DIV",
		isKeyword: true,
	},
	NullEQ: {
		name:      "nulleq",
		literal:   "<=>",
		isKeyword: false,
	},
	In: {
		name:      "in",
		literal:   "IN",
		isKeyword: true,
	},
	Like: {
		name:      "like",
		literal:   "LIKE",
		isKeyword: true,
	},
	Case: {
		name:      "case",
		literal:   "CASE",
		isKeyword: true,
	},
	Regexp: {
		name:      "regexp",
		literal:   "REGEXP",
		isKeyword: true,
	},
	IsNull: {
		name:      "isnull",
		literal:   "IS NULL",
		isKeyword: true,
	},
	IsTruth: {
		name:      "istrue",
		literal:   "IS TRUE",
		isKeyword: true,
	},
	IsFalsity: {
		name:      "isfalse",
		literal:   "IS FALSE",
		isKeyword: true,
	},
}

func clearSensitiveInfoInReason(s *string) *string {
	if s == nil || *s == "" {
		return s
	}
	if !strings.Contains(*s, `IP NOT IN white List`) {
		return s
	}
	return utils.StringRef(ipReg.ReplaceAllString(*s, `?`))
}

func clearSensitiveInfoInCommandItems(items []*model.CommandItem) (out []*model.CommandItem) {
	fp.StreamOf(items).Map(clearSensitiveInfoInCommandItem).ToSlice(&out)
	return
}

func clearSensitiveInfoInCommandItem(item *model.CommandItem) *model.CommandItem {
	if item == nil {
		return item
	}
	item.ReasonDetail = clearSensitiveInfoInReason(item.ReasonDetail)
	return item
}

func generateSQLTaskName(ticket *dao.Ticket) string {
	return strings.Join([]string{ticket.TenantId, ticket.InstanceId, ticket.TaskId}, consts.ActorSplitSymbol)
}

func calcuTimeBeforeExecute(now int64, timeWindowStart int64, timeWindowEnd int64) int64 {
	// 转换为时间
	createTime := time.UnixMilli(now)
	startTime := time.UnixMilli(timeWindowStart).UTC()
	endTime := time.UnixMilli(timeWindowEnd).UTC()
	// 忽略日期后的时间
	ti := time.Date(1, 1, 1, createTime.Hour(), createTime.Minute(), createTime.Second(), 0, time.UTC)

	// 给定的时间窗口
	st := time.Date(1, 1, 1, startTime.Hour(), startTime.Minute(), startTime.Second(), 0, time.UTC)
	et := time.Date(1, 1, 1, endTime.Hour(), endTime.Minute(), endTime.Second(), 0, time.UTC)

	// 时间窗口这里,分两种情况：
	// 1、如果开始时间大于当前时间,说明没有进入时间窗口
	if st.Sub(ti).Seconds() > 0 {
		return int64(st.Sub(ti).Seconds())
	}
	// 2、如果结束时间小于当前时间,说明已经过了时间窗口
	if ti.Sub(et).Seconds() > 0 {
		// 当前时间表示成0~86400000之间(毫秒),下一轮执行时间在明天的时间窗口(今天剩余时间 + 明天起始时间)
		nowTime := int64((createTime.Hour()*3600 + createTime.Minute()*60 + createTime.Second()) * 1000)
		nextTime := (86400000 - nowTime + timeWindowStart) / 1000
		return nextTime
	}
	return DefaultTimeDuration
}

func getCronTime(endTime int64) string {
	cronTime := time.Unix(endTime, 0)
	spec := fmt.Sprintf("%d %d %d %d %d *", cronTime.Second(), cronTime.Minute(), cronTime.Hour(), cronTime.Day(), int(cronTime.Month()))
	return spec
}

func GetTicketErrDesc(errMsg string) string {
	var desc = CommonReason
	rdsErr, recFlag := TicketRecognizeRdsMysqlError(errMsg)
	if recFlag {
		desc = model.ErrorCodeRdsMysqlMap[*rdsErr].DescCN
	}
	return desc
}

func TicketRecognizeRdsMysqlError(errStr string) (*model.ErrorCodeRdsMysql, bool) {
	// eg:
	// code=345012 status=PrecheckFailed_DuplicateKeyOrColumn msg=The operation is not permitted due to exist duplicate key or column.
	if errStr == "" {
		return nil, false
	}
	e := strings.Split(errStr, " ")
	if len(e) < 1 {
		return nil, false
	}
	// 如果是 code= 开头
	if strings.HasPrefix(e[0], "code=") {
		code := strings.Split(e[0], "=")
		if len(code) == 2 {
			rdsErrorCode, convErr := strconv.Atoi(code[1])
			if convErr == nil {
				ec := model.ErrorCodeRdsMysql(rdsErrorCode)
				if ec.String() == "<UNSET>" {
					return nil, false
				}
				return &ec, true
			}
		}
	}
	return nil, false
}

func IsSqlTaskInProgress(rsp *shared.GetDDLTaskStatusResp) bool {
	if rsp.SqlTaskStatus == model.SqlTaskStatus_Init.String() ||
		rsp.SqlTaskStatus == model.SqlTaskStatus_Pending.String() ||
		rsp.SqlTaskStatus == model.SqlTaskStatus_Precheck.String() ||
		rsp.SqlTaskStatus == model.SqlTaskStatus_Running.String() {
		return true
	}
	return false
}

// GetTables 获取语句中包含的表,并检查语句是否合法
func GetTables(node ast.StmtNode) ([]string, error) {
	tables, err := bizUtils.CheckFreeLockDMLLimit(node)
	if err != nil {
		return nil, err
	}
	return tables, nil
}

func GenerateGetMaxIndexSQL(tableName string, indexes []*datasource.TableIndexInfo, node ast.StmtNode) string {
	var columns []string
	var columnsOrder []string
	for _, val := range indexes {
		columns = append(columns, val.ColumnName)
		columnsOrder = append(columnsOrder, val.ColumnName+" desc ")
	}
	columnStr := strings.Join(columns, ", ")
	columnOrderStr := strings.Join(columnsOrder, ", ")
	// 求最大值也去掉用户where条件
	//return fmt.Sprintf("select %s from %s where %s order by %s limit 1", columnStr, tableName, GetWhereCondition(node), columnOrderStr)
	return fmt.Sprintf("select %s from %s order by %s limit 1", columnStr, tableName, columnOrderStr)
}

func GenerateGetMinIndexSQL(tableName string, indexes []*datasource.TableIndexInfo, node ast.StmtNode) string {
	var columns []string
	var columnsOrder []string
	for _, val := range indexes {
		columns = append(columns, val.ColumnName)
		columnsOrder = append(columnsOrder, val.ColumnName+" asc ")
	}
	columnStr := strings.Join(columns, ", ")
	columnOrderStr := strings.Join(columnsOrder, ", ")
	// 求最小值的时候,去掉用户的where条件
	// return fmt.Sprintf("select %s from %s where %s order by %s limit 1", columnStr, tableName, GetWhereCondition(node), columnOrderStr)
	return fmt.Sprintf("select %s from %s order by %s limit 1", columnStr, tableName, columnOrderStr)
}

func GenerateGetMinIndexSQLForDelete(tableName string, indexes []*datasource.TableIndexInfo, node ast.StmtNode) string {
	var columns []string
	var columnsOrder []string
	for _, val := range indexes {
		columns = append(columns, val.ColumnName)
		columnsOrder = append(columnsOrder, val.ColumnName+" asc ")
	}
	columnStr := strings.Join(columns, ", ")
	columnOrderStr := strings.Join(columnsOrder, ", ")
	// 原来带where条件，改成不带where条件
	return fmt.Sprintf("select %s from %s where %s order by %s limit 1", columnStr, tableName, GetWhereCondition(node), columnOrderStr)
}

func GenerateColumnList(indexes []*datasource.TableIndexInfo) []string {
	var columns []string
	for _, val := range indexes {
		columns = append(columns, val.ColumnName)
	}
	return columns
}

func GenerateFreeLockExecInfo(minValue []*IndexInfo, maxValue []*IndexInfo) []*FreeLockExecInfo {
	var res = make([]*FreeLockExecInfo, len(minValue))
	for idx, val := range minValue {
		res[idx] = new(FreeLockExecInfo)
		res[idx].IndexName = val.IndexName
		res[idx].MinValue = val.IndexValue
		res[idx].BatchLeft = val.IndexValue // 将最小值,作为第一次的左值
	}
	for idx, val := range maxValue {
		res[idx].IndexName = val.IndexName
		res[idx].MaxValue = val.IndexValue
	}
	return res
}

func GenerateFreeLockExecInfoForDelete(minValue []*IndexInfo) []*FreeLockExecInfo {
	var res = make([]*FreeLockExecInfo, len(minValue))
	for idx, val := range minValue {
		res[idx] = new(FreeLockExecInfo)
		res[idx].IndexName = val.IndexName
		res[idx].MinValue = val.IndexValue
		res[idx].BatchLeft = val.IndexValue // 将最小值,作为第一次的左值
	}
	return res
}

func getPkOrUniqKeyStr(idx []*datasource.TableIndexInfo) string {
	var res []string
	for _, val := range idx {
		res = append(res, val.ColumnName)
	}
	return strings.Join(res, ",")
}

func getPkOrUniqKeyOrderByStr(idx []*datasource.TableIndexInfo) string {
	var res []string
	for _, val := range idx {
		res = append(res, val.ColumnName+" asc ")
	}
	return strings.Join(res, ",")
}

func getIndexMaxCondition(array []*FreeLockExecInfo, index int) string {
	//如果数组长度已经小于 index 值，直接结束递归
	if len(array) < index {
		return ""
	}

	//遍历数组，获取数组的第 index 个及其前置所有的元素，先执行 X > 1 的逻辑
	greaterResult := make([]string, 0)
	for idx, val := range array {
		if idx < index {
			greaterResult = append(greaterResult, fmt.Sprintf("(%s = _binary'%s') ", val.IndexName, val.MaxValue))
		}
		//到达 index 的位置的时候，就执行 > 1，然后停止循环
		if idx >= index {
			greaterResult = append(greaterResult, fmt.Sprintf("(%s < _binary'%s') ", val.IndexName, val.MaxValue))
			break
		}
	}

	lessResult := make([]string, 0)
	//接下来执行 X = 1 的逻辑，判断当前index是否为最后一个，不是最后一个就向下递归
	if len(array) != index+1 {
		lessResult = append(lessResult, getIndexMaxCondition(array, index+1))
	} else {
		//如果当前size是最后一个元素，那么就遍历数组，获取数组的第size个及其前置所有的元素，先执行 X = 1 的逻辑
		for _, val := range array {
			lessResult = append(lessResult, fmt.Sprintf("(%s = _binary'%s' )", val.IndexName, val.MaxValue))
		}
	}
	//最后一步，将X > 1 和 X = 1拼接起来并返回
	return fmt.Sprintf("%s or %s", strings.Join(greaterResult, " and "), strings.Join(lessResult, " and "))
}

func getIndexLeftCondition(array []*FreeLockExecInfo, index int) string {
	//如果数组长度已经小于 index 值，直接结束递归
	if len(array) < index {
		return ""
	}

	//遍历数组，获取数组的第 index 个及其前置所有的元素，先执行 X > 1 的逻辑
	greaterResult := make([]string, 0)
	for idx, val := range array {
		if idx < index {
			greaterResult = append(greaterResult, fmt.Sprintf("(%s = _binary'%s') ", val.IndexName, val.BatchLeft))
		}
		//到达 index 的位置的时候，就执行 > 1，然后停止循环
		if idx >= index {
			greaterResult = append(greaterResult, fmt.Sprintf("(%s > _binary'%s') ", val.IndexName, val.BatchLeft))
			break
		}
	}

	lessResult := make([]string, 0)
	//接下来执行 X = 1 的逻辑，判断当前index是否为最后一个，不是最后一个就向下递归
	if len(array) != index+1 {
		lessResult = append(lessResult, getIndexLeftCondition(array, index+1))
	} else {
		//如果当前size是最后一个元素，那么就遍历数组，获取数组的第size个及其前置所有的元素，先执行 X = 1 的逻辑
		for _, val := range array {
			lessResult = append(lessResult, fmt.Sprintf("(%s = _binary'%s' )", val.IndexName, val.BatchLeft))
		}
	}
	//最后一步，将X > 1 和 X = 1拼接起来并返回
	return fmt.Sprintf("%s or %s", strings.Join(greaterResult, " and "), strings.Join(lessResult, " and "))
}

func getIndexRightCondition(array []*FreeLockExecInfo, index int) string {
	//如果数组长度已经小于 index 值，直接结束递归
	if len(array) < index {
		return ""
	}

	//遍历数组，获取数组的第 index 个及其前置所有的元素，先执行 X > 1 的逻辑
	greaterResult := make([]string, 0)
	for idx, val := range array {
		if idx < index {
			greaterResult = append(greaterResult, fmt.Sprintf("(%s = _binary'%s') ", val.IndexName, val.BatchRight))
		}
		//到达 index 的位置的时候，就执行 > 1，然后停止循环
		if idx >= index {
			greaterResult = append(greaterResult, fmt.Sprintf("(%s < _binary'%s') ", val.IndexName, val.BatchRight))
			break
		}
	}

	lessResult := make([]string, 0)
	//接下来执行 X = 1 的逻辑，判断当前index是否为最后一个，不是最后一个就向下递归
	if len(array) != index+1 {
		lessResult = append(lessResult, getIndexRightCondition(array, index+1))
	} else {
		//如果当前size是最后一个元素，那么就遍历数组，获取数组的第size个及其前置所有的元素，先执行 X = 1 的逻辑
		for _, val := range array {
			lessResult = append(lessResult, fmt.Sprintf("(%s = _binary'%s' )", val.IndexName, val.BatchRight))
		}
	}
	//最后一步，将X > 1 和 X = 1拼接起来并返回
	return fmt.Sprintf("%s or %s", strings.Join(greaterResult, " and "), strings.Join(lessResult, " and "))
}

func getIndexLeftConditionForDelete(array []*FreeLockExecInfo) string {
	var res []string
	for _, val := range array {
		lowerCondition := fmt.Sprintf("(%s > _binary'%s') ", val.IndexName, val.BatchLeft)
		equalCondition := fmt.Sprintf("(%s = _binary'%s' )", val.IndexName, val.BatchLeft)
		item := fmt.Sprintf(" (%s or %s) ", lowerCondition, equalCondition)
		res = append(res, item)
	}
	return strings.Join(res, " and ")
}

func getIndexResult(res string) string {
	result := make([]string, len(strings.Split(res, " or ")))
	for idx, str := range strings.Split(res, " or ") {
		if !strings.Contains(strings.ToLower(str), " and ") {
			result[idx] = str
			continue
		}
		result[idx] = fmt.Sprintf("(%s)", str)
	}
	return strings.Join(result, " or ")
}

func IsSliceContainsElement(list []string, element string) bool {
	for _, item := range list {
		if item == element {
			return true
		}
	}
	return false
}

func extractOperator(op Op) string {
	val, ok := Ops[op]
	if ok {
		return val.literal
	}
	return ""
}

func extractWhereCondition(expr ast.Node) string {
	switch ex := expr.(type) {
	case *ast.BinaryOperationExpr:
		left := extractWhereCondition(ex.L)
		right := extractWhereCondition(ex.R)
		operator := extractOperator(ex.Op)
		return fmt.Sprintf("(%s %s %s)", left, operator, right)
	case *ast.ParenthesesExpr:
		return extractWhereCondition(ex.Expr)
	case *ast.ColumnNameExpr:
		return ex.Name.Name.String()
	case *test_driver.ValueExpr:
		return extractTestDriverValue(ex)
	case *ast.BetweenExpr:
		left := extractWhereCondition(ex.Left)
		right := extractWhereCondition(ex.Right)
		return fmt.Sprintf("%s between %s and %s", ex.Expr.(*ast.ColumnNameExpr).Name.Name.String(), left, right)
	case *ast.PatternInExpr:
		var res string
		for idx, val := range ex.List {
			if idx == 0 {
				res += fmt.Sprintf("%s", extractWhereCondition(val))
				continue
			}
			res += "," + fmt.Sprintf("%s", extractWhereCondition(val))
		}
		return fmt.Sprintf("%s in (%s)", ex.Expr.(*ast.ColumnNameExpr).Name.Name.String(), res)
	case *ast.PatternLikeExpr:
		var res = extractWhereCondition(ex.Pattern)
		return fmt.Sprintf("%s like %s", ex.Expr.(*ast.ColumnNameExpr).Name.Name.String(), res)
	case *ast.PatternRegexpExpr:
		var res = extractWhereCondition(ex.Pattern)
		return fmt.Sprintf("%s regexp %s", ex.Expr.(*ast.ColumnNameExpr).Name.Name.String(), res)
	default:
		return " "
	}
}

// extractValue 获取value的值
func extractValue(in *driver.ValueExpr) string {
	switch typ := in.Datum.Kind(); typ {
	case types.KindInt64, types.KindUint64, types.KindMysqlDecimal:
		return utils.Int64ToStr(in.Datum.GetInt64())
	case types.KindString:
		return fmt.Sprintf("\"%s\"", in.Datum.GetString())
	}
	return ""
}

// extractValue 获取value的值
func extractTestDriverValue(in *test_driver.ValueExpr) string {
	switch typ := in.Datum.Kind(); typ {
	case types.KindInt64, types.KindUint64, types.KindMysqlDecimal:
		return utils.Int64ToStr(in.Datum.GetInt64())
	case types.KindString:
		return fmt.Sprintf("\"%s\"", in.Datum.GetString())
	}
	return ""
}

func GetWhereFromTicketSQL(sql string) string {
	p := parser.New()
	stmts, _, _ := p.Parse(sql, "utf8", "")
	if len(stmts) > 0 {
		return GetWhereCondition(stmts[0])
	}
	return ""
}

// FixMe 2024-09-10 这块现在用sql parser的format来实现,后续如果有bad case再说
func GetWhereCondition(stmt ast.StmtNode) string {
	res := extractWhereConditionFromSQL(stmt)
	if strings.TrimSpace(res) == "" { // 如果是空,则返回where条件是1=1
		return " 1 = 1 "
	}
	return res
}

func extractWhereConditionFromSQL(stmt ast.StmtNode) string {
	switch stmt := stmt.(type) {
	case *ast.UpdateStmt:
		if stmt.Where != nil {
			var b strings.Builder
			ctx := format.NewRestoreCtx(format.DefaultRestoreFlags, &b)
			stmt.Where.Restore(ctx)
			return b.String()
		}
		return ""
	case *ast.DeleteStmt:
		if stmt.Where != nil {
			var b strings.Builder
			ctx := format.NewRestoreCtx(format.DefaultRestoreFlags, &b)
			stmt.Where.Restore(ctx)
			return b.String()
		}
		return ""
	case *ast.InsertStmt:
		if stmt.Select != nil {
			fmt.Println("INSERT INTO ... SELECT 语句的 SELECT 部分:")
			// 将 stmt.Select 转换为 *ast.SelectStmt 类型
			if selectStmt, ok := stmt.Select.(*ast.SelectStmt); ok {
				extractWhereCondition(selectStmt) // 提取 SELECT 的 WHERE 条件
			}
			return ""
		}
		return ""
	default:
		return ""
	}
}

func CommandSetFinished(commandSet *model.DescribeCommandSetResp) bool {
	return *commandSet.Progress == 100
}

func CommandFailed(commandItem *model.CommandItem) bool {
	return *commandItem.State != model.CommandState_Terminated || *commandItem.Reason != model.CommandTerminatedReason_Success
}

func GetVeDBTicketErrDesc(errMsg string) string {
	var desc = CommonReason
	rdsErr, recFlag := VeDBTicketRecognizeRdsMysqlError(errMsg)
	if recFlag {
		desc = model.ErrorCodeRdsMysqlMap[*rdsErr].DescCN
	}
	return desc
}

func VeDBTicketRecognizeRdsMysqlError(errStr string) (*model.ErrorCodeRdsMysql, bool) {
	// eg:
	// mgr resp=`{\"RequestID\":\"3e3bb207b8e44bb1ac9819b9afd9c9fd\",\"Status\":{\"Code\":3003,\"Status\":\"SyntaxError\",\"Message\":\"The specified SQL statement unsupported\",\"Details\":{\"DebugInfo\":{\"Detail\":\"The sum of the lengths of the database name[this_is_a_long_database_for_test] and table name[this_is_a_long_table_for_online_ddl_test] exceeds the limit.\"}},\"HTTPCode\":400},\"Product\":\"MgrFramework\",\"Async\":true,\"ProductStr\":\"ByteNDB\"}`
	// code=345012 status=PrecheckFailed_DuplicateKeyOrColumn msg=The operation is not permitted due to exist duplicate key or column.
	if errStr == "" {
		return nil, false
	}
	e := strings.Split(errStr, " ")
	if len(e) < 1 {
		return nil, false
	}
	// 如果是 code= 开头
	if strings.HasPrefix(e[0], "code=") {
		code := strings.Split(e[0], "=")
		if len(code) == 2 {
			rdsErrorCode, convErr := strconv.Atoi(code[1])
			if convErr == nil {
				ec := model.ErrorCodeRdsMysql(rdsErrorCode)
				if ec.String() == "<UNSET>" {
					return nil, false
				}
				return &ec, true
			}
		}
	}
	return nil, false
}
