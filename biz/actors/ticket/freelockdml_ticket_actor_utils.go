package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/actors/ticket/sharding_free_lock_dml"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/mysql"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
	_ "github.com/pingcap/tidb/types/parser_driver"
	"gorm.io/gorm"
)

func (f *FreeLockDMLActor) updateActorAction(ctx types.Context, currentAction model.FreeLockDMLTicketAction) {
	log.Info(ctx, "update Actor state %s as state %s", f.state.CurrentAction.String(), currentAction.String())
	f.state.CurrentAction = currentAction
}

// initState 给actor的state里面赋值,计算
func (f *FreeLockDMLActor) initState(ctx types.Context, msg *shared.ExecFreeLockDMLTicket) error {
	// 1、基础信息赋值
	f.state.TenantId = msg.TenantID
	f.state.UserId = msg.UserID
	if msg.Source.Address == "" {
		f.ds.GetDatasourceAddress(ctx, msg.Source)
	}
	f.state.Ds = msg.Source
	f.state.SessionId = ctx.GetName() // 如果是无锁SQL变更类型的工单，则以ticketId作为sessionId来发起session
	f.BuildCtx(ctx)

	// 2、判断是否已经计算过总的批次，如果没有，则计算总批次(这是一个预估值)
	if f.state.TotalBatchNum == 0 {
		sharedTicket, err := f.GetSharedTicket(ctx)
		if err != nil {
			log.Warn(ctx, "ticket:%s get shared ticket error", ctx.GetName())
			return err
		}
		if err = f.getTotalBatchNum(ctx, sharedTicket); err != nil {
			log.Warn(ctx, "ticket:%v get total batch number error %v", sharedTicket.TicketId, err)
			return err
		}
	}
	return nil
}

// GetTableIndexBorderValue 获取当前表的主键或者非空唯一索引边界值,也就是最大最小值
func (f *FreeLockDMLActor) GetTableIndexBorderValue(ctx types.Context, msg *shared.ExecFreeLockDMLTicket) ([]*FreeLockExecInfo, error) {
	if f.state != nil && f.state.FreeLockExecInfo != nil {
		log.Info(ctx, "ticket %s index min value and max value is gotten ", ctx.GetName())
		return f.state.FreeLockExecInfo, nil
	}
	// 0、解析SQL
	stmts, err := GetParserStmts(msg.SqlText)
	if err != nil {
		log.Warn(ctx, "ticket %s parse sql error:%v", ctx.GetName(), err)
		return nil, err
	}
	tables, err := GetTables(stmts[0])
	if err != nil {
		log.Warn(ctx, "ticket %s get tables error:%v", ctx.GetName(), err)
		return nil, err
	}
	log.Info(ctx, "ticket %s tables is %s", ctx.GetName(), tables)

	// 给actor state里面的table赋值
	f.state.Tables = tables

	// 1、获取所有的索引
	resp, err := f.GetTableIndexInfo(ctx, msg.Source, tables[0])
	if err != nil {
		log.Warn(ctx, "ticket %s get table index info error:%v", ctx.GetName(), err)
		return nil, err
	}
	log.Info(ctx, "ticket %s get table index info is %v", ctx.GetName(), utils.Show(resp))
	if len(resp.TableIndexInfo) == 0 {
		log.Warn(ctx, "ticket %s get table index info is empty", ctx.GetName())
		return nil, fmt.Errorf("get empty primary key or non-null unique key, please check if db exists," +
			"or if table has primary key or non-null unique key ")
	}
	// 2、获取一下非空唯一索引或者主键最大最小值的SQL语句
	minSQL := GenerateGetMinIndexSQL(tables[0], resp.TableIndexInfo, stmts[0])
	maxSQL := GenerateGetMaxIndexSQL(tables[0], resp.TableIndexInfo, stmts[0])
	log.Info(ctx, "ticket %s minSQL is [%s],maxSQL is [%s]", ctx.GetName(), minSQL, maxSQL)

	columnList := GenerateColumnList(resp.TableIndexInfo)
	minValue, err := f.GetIndexValue(ctx, msg.Source, tables[0], minSQL, columnList)
	if err != nil {
		log.Warn(ctx, "ticket %s get minValue error:%s", ctx.GetName(), err.Error())
		return nil, err
	}
	log.Info(ctx, "ticket %s minValue is %s", ctx.GetName(), utils.Show(minValue))

	maxValue, err := f.GetIndexValue(ctx, msg.Source, tables[0], maxSQL, columnList)
	if err != nil {
		log.Warn(ctx, "ticket %s get maxValue error:%s", ctx.GetName(), err.Error())
		return nil, err
	}
	log.Info(ctx, "ticket %s maxValue is %s", ctx.GetName(), utils.Show(maxValue))

	// 3、得到最大最小值
	return GenerateFreeLockExecInfo(minValue, maxValue), nil
}

// GetIndexValue 获取主键或者非空唯一索引的最大值和最小值
func (f *FreeLockDMLActor) GetIndexValue(ctx types.Context, ds *shared.DataSource, table string, sqlText string, columnList []string) ([]*IndexInfo, error) {
	req := &datasource.GetIndexValueReq{
		TableName: table,
		Source:    ds,
		Command:   sqlText,
		Columns:   columnList,
	}
	resp, err := f.ticketService.GetIndexValue(ctx, req)
	if err != nil {
		log.Warn(ctx, "ticket:%s get table index value error:%s", ctx.GetName(), err.Error())
		return nil, err
	}
	var res []*IndexInfo
	if len(columnList) != len(resp.TableIndexValue) {
		log.Warn(ctx, "ticket:%s get table index value not match column %v!=%v ", ctx.GetName(), len(columnList), len(resp.TableIndexValue))
		return nil, err
	}

	for _, val := range resp.TableIndexValue {
		res = append(res, &IndexInfo{
			IndexValue: val.IndexValue,
			IndexName:  val.IndexName,
		})
	}
	log.Info(ctx, "ticket %s index max or min value is %s", ctx.GetName(), utils.Show(res))
	return res, nil
}

func (f *FreeLockDMLActor) BuildCtx(ctx types.Context) {
	var tenantId, userId string
	if f.state.TenantId != "" {
		tenantId = f.state.TenantId
	}
	if f.state.UserId != "" {
		userId = f.state.UserId
	}
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: tenantId,
		UserID:   userId,
	})
}

// GetTableIndexInfo 获取表的索引信息(主键或者非空唯一索引)
func (f *FreeLockDMLActor) GetTableIndexInfo(ctx types.Context, ds *shared.DataSource, table string) (
	*datasource.GetTableInfoIndexResp, error) {
	// 这里是一个幂等的判断,如果之前已经获取过了，但是mgr pod挂了，这块就不用重新获取了。
	if f.state != nil && f.state.TableIndexInfo != nil && len(f.state.TableIndexInfo) != 0 {
		log.Info(ctx, "ticket %s index info is already exists", ctx.GetName())
		return &datasource.GetTableInfoIndexResp{TableIndexInfo: f.state.TableIndexInfo}, nil
	}

	command := fmt.Sprintf("SELECT DISTINCT TABLE_NAME, INDEX_NAME,NULLABLE,SEQ_IN_INDEX, IF(INDEX_NAME='PRIMARY','Primary',"+
		"IF(NON_UNIQUE=1,IF(INDEX_TYPE='FULLTEXT','FullText',IF(INDEX_TYPE='SPATIAL','Spatial','Normal')),'Unique')) "+
		"as INDEX_TYPE, COLUMN_NAME, SUB_PART,INDEX_COMMENT FROM INFORMATION_SCHEMA.STATISTICS "+
		"WHERE TABLE_SCHEMA='%s' and TABLE_NAME in ('%s') %s;", ds.Db, table, mysql.DBW_CONSOLE_DEFAULT_HINT) //ignore_security_alert SQL_INJECTION
	req := &datasource.GetTableIndexInfoReq{
		TableName: table,
		Source:    ds,
		Command:   command,
	}
	resp, err := f.ticketService.GetTableIndexInfo(ctx, ds, req)
	if err != nil {
		log.Warn(ctx, "ticket:%s get table index info error:%s", ctx.GetName(), err.Error())
		return nil, err
	}

	log.Info(ctx, "ticket %s table pk index info is %s", ctx.GetName(), utils.Show(resp))
	// 这里处理一下数据,只返回非空唯一索引或者主键
	pkOrUniq, _ := f.GetTablePKOrUniqKey(resp)
	// 这里获取表的主键或者非空唯一索引信息
	f.state.TableIndexInfo = pkOrUniq
	return &datasource.GetTableInfoIndexResp{TableIndexInfo: pkOrUniq}, nil
}

// GetTablePKOrUniqKey 获取主键或者非空唯一索引信息
func (f *FreeLockDMLActor) GetTablePKOrUniqKey(rresp *datasource.GetTableInfoIndexResp) ([]*datasource.TableIndexInfo, bool) {
	var resPk = make([]*datasource.TableIndexInfo, 0)
	var resUniq = make([]*datasource.TableIndexInfo, 0)
	for _, val := range rresp.TableIndexInfo {
		if val.IndexType == "Primary" { // 有主键
			resPk = append(resPk, val)
		}
		if val.IndexType == "Unique" && val.Nullable == "" { // Nullable为Yes代表以为null，为空表示非null
			resUniq = append(resUniq, val)
		}
	}
	// 有主键,使用主键，无主键，使用非空唯一索引
	if len(resPk) != 0 { // 有主键,直接返回主键,不考虑非空唯一索引
		return resPk, true
	}
	if len(resUniq) == 0 { // 如果没有唯一索引和主键，直接返回false
		return nil, false
	}
	// 判断唯一索引是否为非空
	var uniqMap = make(map[string][]*datasource.TableIndexInfo, 1)
	for _, item := range resUniq {
		_, ok := uniqMap[item.IndexName]
		if !ok {
			uniqMap[item.IndexName] = []*datasource.TableIndexInfo{}
			uniqMap[item.IndexName] = append(uniqMap[item.IndexName], item)
		} else {
			uniqMap[item.IndexName] = append(uniqMap[item.IndexName], item)
		}
	}
	for _, value := range uniqMap {
		var isItemValid = true
		for _, j := range value {
			// 只要包含
			if j.Nullable == "Yes" { // 只要有一个是Yes,说明这个唯一索引不是非空,不可用
				isItemValid = false
				break
			}
		}
		if isItemValid {
			return value, true
		}
	}
	return nil, false
}

func (f *FreeLockDMLActor) doWhenBatchGap(ctx types.Context, ticket *shared.Ticket) {
	// TODO 这里新增一个行数的判断
	if !ticket.IsReplicaDelayEnable { // 未开启配置,直接跳过
		return
	}
	for {
		// 这里打印一下执行日志
		realSbm, err := f.GetSecondsBehindMaster(ctx, ticket)
		if err != nil {
			log.Warn(ctx, "get SecondsBehindMaster error:%s", err.Error())
		}
		log.Info(ctx, "SecondsBehindMaster is %d ms", realSbm)
		if realSbm > int64(ticket.ReplicaDelaySeconds*1000) {
			// 复制延迟,等待1s之后,继续判断延迟
			time.Sleep(time.Duration(1) * time.Second)
			f.ProducerToTls(
				ctx, ticket, fmt.Sprintf("%d%%", f.getProgress()),
				fmt.Sprintf("the current master-slave delay time is %dms,waiting for delayed recovery...", realSbm))
			continue
		}
		break
	}
}

func (f *FreeLockDMLActor) GetSecondsBehindMaster(ctx types.Context, ticket *shared.Ticket) (int64, error) {
	// 这里调用RDS的API获取SBM的真实值.
	req := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: ticket.InstanceId,
		Type:       ticket.InstanceType,
	}
	return f.ds.DescribeInstanceReplicaDelay(ctx, req)
}

// GenerateNextBatchSelectSQL 生成下一批次SQL的查询语句
func (f *FreeLockDMLActor) GenerateNextBatchSelectSQL(ctx types.Context, ticket *shared.Ticket) string {
	// 1、库表名称
	dbTable := fmt.Sprintf("%s.%s", ticket.DbName, f.state.Tables[0])

	// 2、左边界
	indexLeftCondition := getIndexResult(getIndexLeftCondition(f.state.FreeLockExecInfo, 0)) // isNeedDml左值
	log.Info(ctx, "indexMaxCondition before is %v", indexLeftCondition)

	// 3、最大值
	indexMaxCondition := getIndexResult(getIndexMaxCondition(f.state.FreeLockExecInfo, 0)) // isNeedDml最大值
	// 第一批执行的时候,需要去掉索引字段和右边界相等的那个条件
	if !f.IsFirstBatch() {
		sli := strings.Split(indexLeftCondition, " or ")
		indexLeftCondition = strings.Join(sli[:len(sli)-1], " or ")
	}
	log.Info(ctx, "indexMaxCondition after is %v", indexLeftCondition)

	isRecordInBatch := fmt.Sprintf("select * from %s where (%s) and (%s) order by %s limit %d ",
		dbTable, indexLeftCondition, indexMaxCondition, getPkOrUniqKeyOrderByStr(f.state.TableIndexInfo), ticket.BatchSize) // idNeedDml 当前批次是否有需要更新的数据

	ticketWhereCondition := fmt.Sprintf("(%s) and (%s) ", indexLeftCondition, indexMaxCondition) // 工单执行的SQL的where条件
	isNeedDmlSQL := fmt.Sprintf("select 1 as isNeedDml from (%s) t where %s limit 1", isRecordInBatch, GetWhereFromTicketSQL(ticket.SqlText))

	batchWhereCondition := fmt.Sprintf("(%s) and %s ", GetWhereFromTicketSQL(ticket.SqlText), ticketWhereCondition)
	batchSQL := fmt.Sprintf("select %s, ( %s ) as isNeedDml from %s where %s order by %s limit 1 offset %d",
		getPkOrUniqKeyStr(f.state.TableIndexInfo), isNeedDmlSQL, dbTable, batchWhereCondition, getPkOrUniqKeyOrderByStr(f.state.TableIndexInfo), ticket.BatchSize-1)
	return batchSQL
}

// GenerateNextBatchSelectSQLForDelete 生成下一批次SQL的查询语句
func (f *FreeLockDMLActor) GenerateNextBatchSelectSQLForDelete(ctx types.Context, ticket *shared.Ticket) string {
	// 1、库表名称
	dbTable := fmt.Sprintf("%s.%s", ticket.DbName, f.state.Tables[0])

	if ticket.InstanceType == shared.MySQLSharding {
		dbTable = f.state.Tables[0]
	}
	// 2、select 语句
	batchSQL := fmt.Sprintf("select %s from %s where %s order by %s limit 1",
		getPkOrUniqKeyStr(f.state.TableIndexInfo), dbTable, GetWhereFromTicketSQL(ticket.SqlText), getPkOrUniqKeyOrderByStr(f.state.TableIndexInfo))
	log.Info(ctx, "current ticket %v batch select sql for delete is: %s", ctx.GetName(), batchSQL)

	batchSQL = f.state.Hint + batchSQL

	return batchSQL
}

// GenerateNextBatchExecSQL 生成下一批次SQL的执行语句
func (f *FreeLockDMLActor) GenerateNextBatchExecSQL(ctx types.Context, ticket *shared.Ticket) string {
	// 这里进行一个判断,如果是第一批,那么执行的时候,不处理最后一行
	left := getIndexResult(getIndexLeftCondition(f.state.FreeLockExecInfo, 0))
	right := getIndexResult(getIndexRightCondition(f.state.FreeLockExecInfo, 0))
	if !f.IsFirstBatch() {
		sli := strings.Split(left, " or ")
		left = strings.Join(sli[:len(sli)-1], " or ")
	}
	batchCondition := fmt.Sprintf(" (%s) and (%s)", left, right)
	tmpSql := strings.TrimSuffix(strings.TrimSpace(ticket.SqlText), ";") // 去掉SQL最后面的分号
	// 如果包含，直接拼接即可；如果不包含where,增加where 1 = 1
	if strings.Contains(strings.ToLower(tmpSql), "where") {
		return fmt.Sprintf("%s and (%s) ", tmpSql, batchCondition)
	}
	return fmt.Sprintf("%s where (1=1) and %s ", tmpSql, batchCondition)
}

// GenerateNextBatchExecSQLForDelete 生成下一批次SQL的执行语句
func (f *FreeLockDMLActor) GenerateNextBatchExecSQLForDelete(ctx types.Context, ticket *shared.Ticket) string {
	leftCondition := getIndexLeftConditionForDelete(f.state.FreeLockExecInfo)
	tmpSql := strings.TrimSuffix(strings.TrimSpace(ticket.SqlText), ";") // 去掉SQL最后面的分号
	// 如果包含，直接拼接即可；如果不包含where,增加where 1 = 1
	if strings.Contains(strings.ToLower(tmpSql), "where") {
		return fmt.Sprintf("%s and (%s) limit %v ", tmpSql, leftCondition, ticket.BatchSize)
	}
	return fmt.Sprintf("%s where (1=1) and %s limit %v", tmpSql, leftCondition, ticket.BatchSize)
}

// UpdateTicketRepo 更新任务状态
func (f *FreeLockDMLActor) UpdateTicketRepo(ctx types.Context, ticket *dao.Ticket) error {
	log.Info(ctx, "update ticket %v status to %v", ctx.GetName(), ticket.TicketStatus)
	ticket.TicketId = utils.MustStrToInt64(ctx.GetName())
	err := f.workflowDal.UpdateWorkStatus(ctx, ticket)
	if err != nil {
		log.Warn(ctx, "update ticket %v status to %s error: %v", ticket.TicketId, ticket.TicketStatus, err.Error())
		return err
	}
	return nil
}

func (f *FreeLockDMLActor) IsNextBatchResultReachMaxValue(res []string, indexInfo []*FreeLockExecInfo) bool {
	for idx, val := range indexInfo {
		elem1 := res[idx]
		elem2 := val.MaxValue

		// 尝试将元素转换为整数进行比较
		num1, err1 := strconv.Atoi(elem1)
		num2, err2 := strconv.Atoi(elem2)
		if err1 == nil && err2 == nil {
			// 如果两个元素都可以转换为整数，则按照整数大小进行比较
			if num1 < num2 {
				return false
			} else if num1 > num2 {
				return true
			} else {
				continue // 如果相等,比较第二位
			}
		} else {
			// 如果无法转换为整数，则按照字符串进行比较
			if elem1 < elem2 {
				return false
			} else if elem1 > elem2 {
				return true
			} else {
				continue
			}
		}

	}
	return true
}

func (f *FreeLockDMLActor) GetSharedTicket(ctx types.Context) (*shared.Ticket, error) {
	ticket, err := f.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
	if err != nil {
		log.Warn(ctx, "ticket %s : get ticket from ticketId err:%s", ctx.GetName(), err.Error())
		return nil, err
	}
	if ticket == nil || (ticket != nil && ticket.TicketId == 0) {
		log.Warn(ctx, "ticket %s : get ticket from ticketId err", ctx.GetName())
		return nil, fmt.Errorf("ticket is nil")
	}
	log.Info(ctx, "describe ticket from meta db is %s", utils.Show(ticket))
	return workflow.ChangeTicketType(ticket), nil
}

func (f *FreeLockDMLActor) TicketSucceed(ctx types.Context, ticket *shared.Ticket) {
	// 说明执行完了,更新数据库为执行成功
	msg := fmt.Sprintf("Query OK, %v rows affected", f.state.AffectedRows)
	f.ProducerToTls(ctx, ticket, "100%", msg)
	f.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId, int32(model.TicketStatus_TicketFinished), msg, 100)
	f.updateActorAction(ctx, model.FreeLockDMLTicketAction_ExecuteFinished)
	log.Info(ctx, "ticket %v execute success,begin to suicide", ctx.GetName())
	return
}

func (f *FreeLockDMLActor) updateIndex(ctx types.Context) {
	for _, val := range f.state.FreeLockExecInfo {
		val.BatchLeft = val.BatchRight
	}
}

// LastBatch 把最大值赋值给Right
func (f *FreeLockDMLActor) LastBatch(ctx types.Context, indexInfo []*FreeLockExecInfo) {
	// 如果结果大于或者等于max值,直接将max给right
	for _, val := range indexInfo {
		val.BatchRight = val.MaxValue
	}
	f.state.IsLastBatch = true
}

func (f *FreeLockDMLActor) IsFirstBatch() bool {
	for _, val := range f.state.FreeLockExecInfo {
		if val.BatchLeft == val.MinValue {
			continue
		}
		return false
	}
	return true
}

func (f *FreeLockDMLActor) IsLastBatch() bool {
	for _, val := range f.state.FreeLockExecInfo {
		if val.BatchRight == val.MaxValue {
			continue
		}
		return false
	}
	return true
}

// NeedDml 将计算出来的值(当前批次删除到的那条记录)赋值给当前批次的right值
func (f *FreeLockDMLActor) NeedDml(ctx types.Context, res []string, indexInfo []*FreeLockExecInfo) {
	// 如果结果小于max值,直接将res给right
	if !f.IsNextBatchResultReachMaxValue(res, indexInfo) {
		for idx, val := range indexInfo {
			if res[idx] == "" {
				val.BatchRight = val.MaxValue
			} else {
				val.BatchRight = res[idx]
			}
		}
		return
	}

	// 如果结果大于或者等于max值,直接将max给right
	for idx, val := range indexInfo {
		val.BatchRight = val.MaxValue
		log.Info(ctx, "right border is %v ", f.state.FreeLockExecInfo[idx].BatchRight)
	}
}

func (f *FreeLockDMLActor) getMessage() string {
	var res string
	res = "当前批次左边界:"
	for _, val := range f.state.FreeLockExecInfo {
		res += fmt.Sprintf("%s=%s ", val.IndexName, val.BatchLeft)
	}
	//res += "当前批次右边界:"
	//for _, val := range f.state.FreeLockExecInfo {
	//	res += fmt.Sprintf("%s=%s ", val.IndexName, val.BatchRight)
	//}
	return res
}

// getTotalBatchNum 根据explain的结果和用户页面上设置的每一批的行数,计算一个总的执行批次数,保存在state里面
func (f *FreeLockDMLActor) getTotalBatchNum(ctx types.Context, ticket *shared.Ticket) error {
	var explainNum int64
	switch ticket.CreateFrom {
	case TicketFromOpenAPI, TicketFromDataArchive, sharding_free_lock_dml.InnerCreatedFrom: // 如果是OpenAPI获取的ticket,则需要Explain一下来获取真实的执行行数
		log.Info(ctx, "ticket %s from OpenAPI need explain", ctx.GetName())
		explainRes, err := f.ticketService.ExplainCommand(ctx, f.state.Ds, ticket.SqlText)
		if err != nil {
			log.Warn(ctx, "explain command error %s", err.Error())
			return err
		}
		for _, val := range explainRes.Command {
			if val.Rows == "" {
				continue
			}
			explainNum += utils.MustStrToInt64(val.Rows)
		}

	default:
		res, err := f.workflowDal.GetPreCheckResult(ctx, utils.MustStrToInt64(ctx.GetName()))
		if err != nil {
			log.Warn(ctx, "ticket:%s get preCheck result err:%v", err)
			return err
		}
		log.Info(ctx, "ticket %s precheck result is %v,len is %v", ctx.GetName(), utils.Show(res), len(res))
		for _, val := range res {
			if val.Item == workflow.PreCheckExplain {
				explainNum = utils.MustStrToInt64(val.Memo)
			}
		}
	}
	// 将计算的结果保存到数据库里面
	//f.SaveTicketAffectedRows(ctx, ticket, explainNum)

	// 这里做一个优化,如果预估的影响行数小于最小值，则将执行的批次置为1批，直接执行
	if explainNum <= consts.TicketBatchLimit {
		f.state.TotalBatchNum = 1
		log.Info(ctx, "ticket %s explain num is %v,lower than %v,we will execute this sql [%v] directly",
			ctx.GetName(), explainNum, consts.TicketBatchLimit, ticket.SqlText)
		return nil
	}
	f.state.TotalBatchNum = explainNum/ticket.BatchSize + 1
	return nil
}

// getProgress 获取真实的进度
func (f *FreeLockDMLActor) getProgress() int64 {
	return f.state.BatchNum * 100 / f.state.TotalBatchNum
}

func (f *FreeLockDMLActor) UpdateProgress(ctx types.Context, ticket *shared.Ticket) {
	f.state.BatchNum += 1
	progress := f.getProgress()
	if progress > 100 {
		progress = 100
	}
	log.Info(ctx, "ticket %v current BatchNum is %v,TotalBatchNum is %v,progress is %v", ctx.GetName(), f.state.BatchNum, f.state.TotalBatchNum, progress)
	// 推送tls
	f.ProducerToTls(ctx, ticket, fmt.Sprintf("%d%%", progress), f.getMessage())

	// 更新数据库
	f.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId, int32(model.TicketStatus_TicketExecute),
		"ticket is executing,please wait...", int32(progress))
}

func (f *FreeLockDMLActor) GetTableIndexBorderValueForDelete(ctx types.Context, msg *shared.ExecFreeLockDMLTicket) ([]*FreeLockExecInfo, error) {
	// 这里是为了幂等
	if f.state != nil && f.state.FreeLockExecInfo != nil {
		log.Info(ctx, "ticket %s index min value and max value is gotten ", ctx.GetName())
		return f.state.FreeLockExecInfo, nil
	}

	// 0、解析SQL
	stmts, err := GetParserStmts(msg.SqlText)
	if err != nil {
		log.Warn(ctx, "ticket %s parse sql error:%v", ctx.GetName(), err)
		return nil, err
	}
	tables, err := GetTables(stmts[0])
	if err != nil {
		log.Warn(ctx, "ticket %s get tables error:%v", ctx.GetName(), err)
		return nil, err
	}
	log.Info(ctx, "ticket %s tables is %s", ctx.GetName(), tables)

	// 给actor state里面的table赋值
	f.state.Tables = tables

	// 1、获取所有的索引
	resp, err := f.GetTableIndexInfo(ctx, msg.Source, tables[0])
	if err != nil {
		log.Warn(ctx, "ticket %s get table index info error:%v", ctx.GetName(), err)
		return nil, err
	}
	log.Info(ctx, "ticket %v get table index info is %v", ctx.GetName(), utils.Show(resp))
	if len(resp.TableIndexInfo) == 0 {
		log.Warn(ctx, "ticket %s get table index info is empty", ctx.GetName())
		return nil, fmt.Errorf("get empty primary key or non-null unique key, please check if db exists," +
			"or if table has primary key or non-null unique key ")
	}
	// 2、获取一下非空唯一索引或者主键最大最小值的SQL语句
	// TODO pos-x hint
	minSQL := GenerateGetMinIndexSQLForDelete(tables[0], resp.TableIndexInfo, stmts[0])
	if msg.CreateFrom == sharding_free_lock_dml.InnerCreatedFrom {
		minSQL = msg.Hint + minSQL
	}
	log.Info(ctx, "ticket %s minSQL is [%s]", ctx.GetName(), minSQL)

	columnList := GenerateColumnList(resp.TableIndexInfo)
	minValue, err := f.GetIndexValue(ctx, msg.Source, tables[0], minSQL, columnList)
	if err != nil {
		log.Warn(ctx, "ticket %v get minValue error:%v", ctx.GetName(), err.Error())
		return nil, err
	}
	log.Info(ctx, "ticket %v minValue is %v", ctx.GetName(), utils.Show(minValue))
	// 3、得到最大最小值
	return GenerateFreeLockExecInfoForDelete(minValue), nil
}

// SaveResToLeft 将计算出来的值赋值给当前批次的left值
func (f *FreeLockDMLActor) SaveResToLeft(ctx types.Context, res []string, indexInfo []*FreeLockExecInfo) {
	// 将执行结果赋值给左边界
	for idx, val := range indexInfo {
		if res[idx] != "" {
			val.BatchLeft = res[idx]
		}
		// 如果等于空,那么左边界不变
		log.Info(ctx, "ticket %v left border is %v ", ctx.GetName(), f.state.FreeLockExecInfo[idx].BatchLeft)
	}
}

func (f *FreeLockDMLActor) IsInstanceExistRunningFreeLockDMLTicket(ctx types.Context, msg *shared.ExecFreeLockDMLTicket) bool {
	if msg == nil || msg.Source == nil {
		log.Warn(ctx, "ticket %v msg is nil or source is nil ", ctx.GetName())
		return true
	}
	if msg.CreateFrom == sharding_free_lock_dml.InnerCreatedFrom {
		// 如果是sharding的，不做这个校验，由上层控制
		return false
	}
	tickets, err := f.workflowDal.GetTicketByInstanceID(ctx, msg.Source.InstanceId)
	if err != nil {
		// 如果不存在,则返回false
		if err == gorm.ErrRecordNotFound {
			log.Info(ctx, "ticket %v: check instance's running tickets success.", ctx.GetName())
			return false
		}
		log.Warn(ctx, "get ticket %v by instanceId error %v", ctx.GetName(), err)
	}
	if len(tickets) > 0 {
		for _, val := range tickets {
			log.Info(ctx, "ticket %v: find instance's running ticket,ticket is:%v", ctx.GetName(), val.TicketId)
		}
		// 只考虑和当前ticket不相等的
		for _, val := range tickets {
			if utils.Int64ToStr(val.TicketId) != ctx.GetName() {
				message := fmt.Sprintf("ticket %v : there are tickets running in this instance[%v],ticket id is  [%v]",
					ctx.GetName(), msg.Source.InstanceId, utils.Show(val.TicketId))
				log.Warn(ctx, message)
				f.UpdateTicketRepo(ctx, &dao.Ticket{
					TicketStatus: int8(model.TicketStatus_TicketError),
					Description:  message,
				})
				return true
			}
		}
	}
	return false
}

func GetParserStmts(sql string) ([]ast.StmtNode, error) {
	p := parser.New()
	stmts, _, err := p.Parse(sql, "utf8", "")
	return stmts, err
}

func GetSQLTextType(sql string) (string, error) {
	stmts, err := GetParserStmts(sql)
	if err != nil {
		return NormalType, err
	}
	if len(stmts) > 0 {
		switch stmts[0].(type) {
		case *ast.DeleteStmt:
			return DeleteType, nil
		default:
			return NormalType, nil
		}
	}
	return NormalType, nil
}

func IsNowReachExecEndTime(endTime int32) bool {
	return endTime != 0 && time.Now().Unix() > int64(endTime)
}

func NotReachCronStartTime(executeType int32, startTime int32) bool {
	return executeType == int32(model.ExecuteType_Cron) && time.Now().Unix() < int64(startTime)
}
