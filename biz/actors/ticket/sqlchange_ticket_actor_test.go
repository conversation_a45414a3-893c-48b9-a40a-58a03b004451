package ticket

import (
	bizConfig "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/mock_dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/context"
	"errors"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
)

type ExecTicketActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *ExecTicketActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *ExecTicketActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestExecTicketActorSuite(t *testing.T) {
	suite.Run(t, new(ExecTicketActorSuite))
}

func (suite *ExecTicketActorSuite) TestGetState() {
	ExecTicketActor := ExecTicketActor{}
	ret := ExecTicketActor.GetState()
	suite.NotEmpty(ret)
}

func (suite *ExecTicketActorSuite) TestNewExecTicketActor() {
	ret := NewExecTicketActor(ExecTicketActorIn{
		In: dig.In{},
	})
	suite.NotEmpty(ret)
}

func (suite *ExecTicketActorSuite) TestProcessStarted() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Started{}).Times(1)
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().GetName().AnyTimes()
	//dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	ExecTicketActor := NewExecTicketActor(ExecTicketActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.ExecTicketActorKind, consts.SingletonActorName, []byte("\"log_id\":1,\"tenant_id\":1,\"user_id\":1"))
	ExecTicketActor.GetState()
	ExecTicketActor.Process(Ctx)
}

func (suite *ExecTicketActorSuite) TestProcessStopping() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Stopping{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().GetName().AnyTimes()

	ExecTicketActor := ExecTicketActor{}
	ExecTicketActor.Process(Ctx)
}

func (suite *ExecTicketActorSuite) TestProcessStopped() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Stopped{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().GetName().AnyTimes()
	Ctx.EXPECT().Self().AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()

	//dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	ExecTicketActor := NewExecTicketActor(ExecTicketActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.ExecTicketActorKind, consts.SingletonActorName, []byte{})
	ExecTicketActor.Process(Ctx)
}

func (suite *ExecTicketActorSuite) TestProcessRestarting() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Restarting{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().GetName().Return("123").AnyTimes()

	//dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	ExecTicketActor := NewExecTicketActor(ExecTicketActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.ExecTicketActorKind, consts.SingletonActorName, []byte{})
	ExecTicketActor.Process(Ctx)
}

// mock一个actor
func mockTicketActor() *ExecTicketActor {
	return &ExecTicketActor{
		state:               &ExecTicketState{TenantId: "1", IsTaskCreated: false},
		cnf:                 &config.MockConfigProvider{},
		workflowDal:         &mocks.MockWorkflowDAL{},
		ticketCommonService: &mock_dbw_ticket.MockTicketCommonService{},
		ds:                  &mocks.MockDataSourceService{},
	}
}

func TestExecTicket(t *testing.T) {
	execActor := mockTicketActor()
	ctx := &mocks.MockContext{}
	// mock一个日志
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock4.UnPatch()
	baseMock5 := mockey.Mock((*config.MockConfigProvider).Get).Return(&bizConfig.Config{TicketSessionTimeout: 1}).Build()
	defer baseMock5.UnPatch()
	baseMock6 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock6.UnPatch()
	baseMock7 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock7.UnPatch()
	baseMock8 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock8.UnPatch()
	baseMock10 := mockey.Mock((*mocks.MockContext).SetReceiveTimeout).Return().Build()
	defer baseMock10.UnPatch()
	baseMock11 := mockey.Mock((*ExecTicketActor).UpdateTicketRepo).Return(nil).Build()
	defer baseMock11.UnPatch()
	baseMock12 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(nil).Build()
	defer baseMock12.UnPatch()
	baseMock121 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{}, nil).Build()
	defer baseMock121.UnPatch()
	baseMock1211 := mockey.Mock((*mocks.MockDataSourceService).GetDatasourceAddress).Return(nil).Build()
	defer baseMock1211.UnPatch()
	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          0,
		ExecuteType:         0,
		ExecutableStartTime: 0,
		ExecutableEndTime:   100,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          1,
		ExecuteType:         0,
		ExecutableStartTime: 0,
		ExecutableEndTime:   100,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          1,
		ExecuteType:         2,
		ExecutableStartTime: 0,
		ExecutableEndTime:   100,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          1,
		ExecuteType:         0,
		ExecutableStartTime: 0,
		ExecutableEndTime:   100,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          1,
		ExecuteType:         int32(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   2147483647,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          int32(model.TicketType_FreeLockStructChange),
		ExecuteType:         int32(model.ExecuteType_Cron),
		ExecutableStartTime: 2147483647,
		ExecutableEndTime:   0,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          1,
		ExecuteType:         0,
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
	})
}

func TestGetCommandSetResult(t *testing.T) {
	execActor := mockTicketActor()
	ctx := &mocks.MockContext{}
	// mock一个日志
	baseMock0 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock0.UnPatch()
	baseMock5 := mockey.Mock((*config.MockConfigProvider).Get).Return(&bizConfig.Config{TicketSessionTimeout: 1}).Build()
	defer baseMock5.UnPatch()
	baseMock6 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock6.UnPatch()
	baseMock7 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock7.UnPatch()
	baseMock8 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock8.UnPatch()
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{TicketType: int8(model.TicketType_NormalSqlChange)}, nil).Build()
	execActor.GetCommandSetResult(ctx)
	baseMock2.UnPatch()

	baseMock4 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{TicketType: int8(model.TicketType_FreeLockStructChange)}, nil).Build()
	execActor.GetCommandSetResult(ctx)
	baseMock4.UnPatch()

	baseMock20 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, errors.New("error")).Build()
	execActor.GetCommandSetResult(ctx)
	baseMock20.UnPatch()
}

func TestExecSqlChangeTicket(t *testing.T) {
	execActor := mockTicketActor()
	ctx := &mocks.MockContext{}

	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock4.UnPatch()
	baseMock5 := mockey.Mock((*config.MockConfigProvider).Get).Return(&bizConfig.Config{TicketSessionTimeout: 1}).Build()
	defer baseMock5.UnPatch()
	baseMock6 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock6.UnPatch()
	baseMock7 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock7.UnPatch()
	baseMock8 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock8.UnPatch()

	baseMock20 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{
		TicketId: 1,
	}, errors.New("error")).Build()
	baseMock11 := mockey.Mock((*ExecTicketActor).UpdateTicketRepo).Return(nil).Build()
	defer baseMock11.UnPatch()
	defer baseMock20.UnPatch()
	baseMock21 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqlsByDBType).Return([]*dbw_ticket.SqlInfo{
		{
			Sql:        "select 1",
			TableNames: []string{"a"},
			DbNames:    []string{"b"},
		},
	}).Build()
	baseMock24 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(nil).Build()
	defer baseMock24.UnPatch()

	baseMock22 := mockey.Mock((*mocks.MockDataSourceService).ExecuteSql).Return(fmt.Errorf("")).Build()
	execActor.execSqlChangeTicket(ctx, &shared.ExecTicket{
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
		ExecuteType:         int32(model.ExecuteType_Auto),
		Source: &shared.DataSource{
			Type: shared.MySQL,
		},
	})
	baseMock22.UnPatch()

	baseMock23 := mockey.Mock((*mocks.MockDataSourceService).ExecuteSql).Return(nil).Build()
	execActor.execSqlChangeTicket(ctx, &shared.ExecTicket{
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
		ExecuteType:         int32(model.ExecuteType_Auto),
		Source: &shared.DataSource{
			Type: shared.MySQL,
		},
	})
	defer baseMock23.UnPatch()
	baseMock21.UnPatch()

	baseMock211 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqlsByDBType).Return([]*dbw_ticket.SqlInfo{}).Build()
	execActor.execSqlChangeTicket(ctx, &shared.ExecTicket{
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
		ExecuteType:         int32(model.ExecuteType_Auto),
		Source: &shared.DataSource{
			Type: shared.MySQL,
		},
	})
	baseMock211.UnPatch()

}
