package ticket

import "code.byted.org/infcs/ds-sql-parser/ast"

// SQLWhereVisitor SQL的遍历器
type SQLWhereVisitor struct {
	where string
}

func (v *SQLWhereVisitor) Enter(in ast.Node) (ast.Node, bool) {
	switch node := in.(type) {
	case *ast.BinaryOperationExpr:
		v.where += extractWhereCondition(node)
		return in, true
	case *ast.BetweenExpr:
		v.where += extractWhereCondition(node)
		return in, true
	case *ast.PatternInExpr:
		val := extractWhereCondition(node)
		v.where += val
		return in, true
	case *ast.PatternLikeExpr:
		val := extractWhereCondition(node)
		v.where += val
		return in, true
	case *ast.PatternRegexpExpr:
		val := extractWhereCondition(node)
		v.where += val
		return in, true
	}
	return in, false
}

func (v *SQLWhereVisitor) Leave(in ast.Node) (ast.Node, bool) {
	return in, true
}
