package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"crypto/md5"
	"fmt"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"
	"time"
)

func (f *FreeLockDMLActor) ProducerToTls(ctx types.Context, ticket *shared.Ticket, progress string, msg string) {
	_, err := f.client.PutLogs(f.formatTlsLogs(ctx, utils.Int64ToStr(ticket.TicketId), progress, ticket.TenantId, ticket.InstanceId, msg))
	if err != nil {
		log.Warn(ctx, "ProducerToTls: write tls error:%s", err.Error())
	}
}

func (f *FreeLockDMLActor) formatTlsLogs(ctx types.Context, ticketId string, progress string, tenantId string, instanceId string, msg string) *tls.PutLogsRequest {
	metricLog := &pb.Log{
		Time: time.Now().Unix(),
		Contents: []*pb.LogContent{
			{Key: "TicketId", Value: ticketId},
			{Key: "Progress", Value: progress},
			{Key: "Message", Value: msg},
			{Key: "CreateTime", Value: time.Now().Format("2006-01-02 15:04:05")},
			{Key: "TenantId", Value: tenantId},
			{Key: "InstanceId", Value: instanceId},
		},
	}
	logs := []*pb.Log{metricLog}
	logGroup := &pb.LogGroup{
		Logs: logs,
	}
	//cnf := f.cnf.Get(ctx)
	c3Cfg := f.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	topicId := c3Cfg.TicketLogTopic
	// topicId = "9cadca36-ed6f-426d-ab6d-083cb64e64ce"

	request := &tls.PutLogsRequest{
		TopicID: topicId, //"cee26aa2-f1c8-42cd-ac35-dd9e84a33c99",
		LogBody: &pb.LogGroupList{LogGroups: []*pb.LogGroup{logGroup}},
		HashKey: f.getHash(instanceId),
	}
	return request
}

func (f *FreeLockDMLActor) getHash(value string) string {
	b := []byte(value)
	return fmt.Sprintf("%x", md5.Sum(b))
}
