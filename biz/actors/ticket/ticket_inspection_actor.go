package ticket

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"encoding/json"
	"github.com/google/uuid"
	"go.uber.org/dig"
	"time"
)

const timeout = 30 * time.Minute

type TicketInspectionActorIn struct {
	dig.In
	TicketService workflow.TicketService
	WorkflowDal   dal.WorkflowDAL
}

type TicketInspectionActor struct {
	state         *TicketInspectionState
	ticketService workflow.TicketService
	workflowDal   dal.WorkflowDAL
}

type TicketInspectionState struct {
	TicketId string
}

func NewTicketInspectionState(bytes []byte) *TicketInspectionState {
	ts := &TicketInspectionState{}
	json.Unmarshal(bytes, ts)
	return ts
}

// NewTicketInspectionActor 执行巡检工单的actor
func NewTicketInspectionActor(p TicketInspectionActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.InspectionTicketActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &TicketInspectionActor{
				state:         NewTicketInspectionState(state),
				ticketService: p.TicketService,
				workflowDal:   p.WorkflowDal,
			}

		}),
	}
}

func (t *TicketInspectionActor) GetState() []byte {
	state, _ := json.Marshal(t.state)
	return state
}

func (t *TicketInspectionActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(timeout)
	sysCtx := context.WithValue(context.Background(), "biz-context", &fwctx.BizContext{
		LogID: "LogTicketInspectionActor-" + uuid.New().String(),
	})
	ctx = types.BuildMyContext(sysCtx, ctx, nil)
	switch ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "ticket Inspection Actor Started")
		ctx.SetReceiveTimeout(timeout)
	case *shared.AreYouOK:
		log.Info(ctx, "ticket Inspection Actor OK")
		ctx.Respond(&shared.OK{})
	case *actor.ReceiveTimeout:
		log.Info(ctx, "deal abnormal ticket")
		t.checkTicketStatus(ctx)
		ctx.SetReceiveTimeout(timeout)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	case *actor.Stopping:
		return
	}
}

func (t *TicketInspectionActor) checkTicketStatus(ctx types.Context) {
	// 查询时间大于一小时前的所有工单记录,找到状态为"未执行"和"预检查"的工单,并发起执行
	timeAHourAgo := time.Now().Add(time.Hour * -1).UnixMilli()
	tickets, err := t.ticketService.GetTicketUnfinished(ctx, timeAHourAgo)
	if err != nil {
		return
	}
	if len(tickets) == 0 { // 此时没有未处理的工单
		return
	}
	for _, val := range tickets {
		log.Info(ctx, "ticket %s is abnormal", val.TicketId)
		if val.TicketStatus == 0 { // 未开始预检查
			_, err := t.ticketService.PreCheck(ctx, &model.PreCheckTicketReq{TicketId: conv.Int64ToStr(val.TicketId)})
			if err != nil {
				return
			}
		}
		if val.TicketStatus == 6 && val.ExecuteType == 0 { // 未开始,并且为自动执行
			t.ticketService.ExecuteTicket(ctx, &model.ExecuteTicketReq{TicketId: conv.Int64ToStr(val.TicketId)})
		}

	}
}
