package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/sqltask"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"errors"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
	"testing"
)

type OnlineDDLTicketActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *OnlineDDLTicketActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *OnlineDDLTicketActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestOnlineDDLTicketActorSuite(t *testing.T) {
	suite.Run(t, new(OnlineDDLTicketActorSuite))
}

func (suite *OnlineDDLTicketActorSuite) TestNewOnlineDDLTicketActor() {
	ret := NewOnlineDDLTicketActor(OnlineDDLTicketActorIn{
		In:          dig.In{},
		WorkflowDal: &mocks.MockWorkflowDAL{},
	})
	suite.NotEmpty(ret)
}

func (suite *OnlineDDLTicketActorSuite) TestProcessStarted() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Started{}).Times(1)
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().GetName().AnyTimes()
	//dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	Actor := NewOnlineDDLTicketActor(OnlineDDLTicketActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.OnlineDDLTicketActorKind, consts.SingletonActorName, []byte("\"log_id\":1,\"tenant_id\":1,\"user_id\":1"))
	Actor.GetState()
	Actor.Process(Ctx)
}

// mock一个actor
func mockOnlineDDLTicketActor() *OnlineDDLTicketActor {
	return &OnlineDDLTicketActor{
		state: &OnlineDDLTicketState{
			CurrentAction: model.OnlineDDLTicketAction_Started,
			TenantId:      "1",
			UserId:        "1"},
		cnf:           &config.MockConfigProvider{},
		workflowDal:   &mocks.MockWorkflowDAL{},
		sp:            &mocks.MockCommandParser{},
		idgenSvc:      &mocks.MockService{},
		cmdRepo:       &mocks.MockCommandRepo{},
		ticketService: &mocks.MockTicketService{},
		sqlTaskSvc:    &sqltask.MockSqlTaskService{},
	}
}

func TestNewOnlineDDLTicketActor(t *testing.T) {
	NewOnlineDDLTicketActor(OnlineDDLTicketActorIn{
		In:   dig.In{},
		Conf: &config.MockConfigProvider{},
	})
}

func TestNewOnlineDDLTicketState(t *testing.T) {
	newOnlineDDLTicketState([]byte{})
}

func TestOnlineDDLGetState(t *testing.T) {
	onlineDDLActor := mockOnlineDDLTicketActor()
	onlineDDLActor.GetState()
}

func TestOnlineDDLProcess(t *testing.T) {
	onlineDDLActor := mockOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock0000.UnPatch()

	baseMock1 := mockey.Mock((*mocks.MockContext).Message).Return(&actor.Started{}).Build()
	baseMock2 := mockey.Mock((*VeDBDDLTicketActor).OnStart).Return().Build()
	onlineDDLActor.Process(ctx)
	baseMock1.UnPatch()
	baseMock2.UnPatch()

	baseMock3 := mockey.Mock((*mocks.MockContext).Message).Return(&shared.ExecVeDBDDLTicket{}).Build()
	baseMock4 := mockey.Mock((*VeDBDDLTicketActor).ExecVeDBDDLTicket).Return().Build()
	onlineDDLActor.Process(ctx)
	baseMock3.UnPatch()
	baseMock4.UnPatch()

	baseMock5 := mockey.Mock((*mocks.MockContext).Message).Return(&shared.StopTicket{}).Build()
	baseMock6 := mockey.Mock((*VeDBDDLTicketActor).StopTicket).Return().Build()
	onlineDDLActor.Process(ctx)
	baseMock5.UnPatch()
	baseMock6.UnPatch()

	baseMock7 := mockey.Mock((*mocks.MockContext).Message).Return(&actor.ReceiveTimeout{}).Build()
	baseMock8 := mockey.Mock((*VeDBDDLTicketActor).GetVeDBTicketStatus).Return().Build()
	onlineDDLActor.Process(ctx)
	baseMock7.UnPatch()
	baseMock8.UnPatch()

	baseMock9 := mockey.Mock((*mocks.MockContext).Message).Return(&actor.Stopped{}).Build()
	onlineDDLActor.Process(ctx)
	baseMock9.UnPatch()
}

//	func TestVeDBOnStart(t *testing.T) {
//		vedbActor := mockVeDBOnlineDDLTicketActor()
//		ctx := &mocks.MockContext{}
//		logbMock := mockey.Mock(log.Log).Return().Build()
//		defer logbMock.UnPatch()
//		baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//		defer baseMock0.UnPatch()
//		baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//		defer baseMock00.UnPatch()
//		baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
//		defer baseMock000.UnPatch()
//		baseMock0000 := mockey.Mock(context.GetTenantID).Return("1").Build()
//		defer baseMock0000.UnPatch()
//
//		vedbActor.state = nil
//		vedbActor.OnStart(ctx)
//
//		vedbActor.state.CurrentAction = model.VeDBDDLTicketAction_ExecuteFailed
//		vedbActor.OnStart(ctx)
//
//		baseMock1 := mockey.Mock((*VeDBDDLTicketActor).repairTicketState).Return().Build()
//		defer baseMock1.UnPatch()
//		vedbActor.OnStart(ctx)
//	}
func TestOnlineDDLExecTicket(t *testing.T) {
	Actor := mockOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock0000.UnPatch()
	baseMock00000 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock00000.UnPatch()
	baseMock000000 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock000000.UnPatch()

	baseMock3 := mockey.Mock((*OnlineDDLTicketActor).execStructChangeTicket).Return().Build()
	Actor.ExecTicket(ctx, &shared.ExecTicket{
		ExecuteType: int32(model.ExecuteType_Cron),
	})
	Actor.ExecTicket(ctx, &shared.ExecTicket{
		TicketType: int32(model.TicketType_FreeLockStructChange),
	})
	baseMock3.UnPatch()

}

func TestExecStructChangeTicket(t *testing.T) {
	Actor := mockOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	err := fmt.Errorf("error")
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock0000.UnPatch()
	baseMock00000 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock00000.UnPatch()
	baseMock000000 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock000000.UnPatch()
	baseMock0000000 := mockey.Mock((*mocks.MockContext).SetReceiveTimeout).Return().Build()
	defer baseMock0000000.UnPatch()

	baseMock2 := mockey.Mock((*OnlineDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*OnlineDDLTicketActor).CreateOnlineDDLTask).Return(err).Build()
	Actor.execStructChangeTicket(ctx, &shared.ExecTicket{
		ExecuteType:       int32(model.ExecuteType_Cron),
		ExecutableEndTime: 100,
	})
	Actor.execStructChangeTicket(ctx, &shared.ExecTicket{
		ExecuteType:         int32(model.ExecuteType_Cron),
		ExecutableStartTime: 2015328319,
		ExecutableEndTime:   0,
	})
	Actor.execStructChangeTicket(ctx, &shared.ExecTicket{
		ExecuteType:         int32(model.ExecuteType_Cron),
		ExecutableStartTime: 100,
		ExecutableEndTime:   0,
	})
	baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*OnlineDDLTicketActor).CreateOnlineDDLTask).Return(nil).Build()
	Actor.execStructChangeTicket(ctx, &shared.ExecTicket{
		ExecuteType:         int32(model.ExecuteType_Cron),
		ExecutableStartTime: 100,
		ExecutableEndTime:   0,
	})
	baseMock4.UnPatch()

}

func TestOnlineDDLGetCommandSetResult(t *testing.T) {
	Actor := mockOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}

	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, errors.New("error")).Build()
	Actor.GetCommandSetResult(ctx)
	baseMock1.UnPatch()

	baseMock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{
		TicketType: int8(model.TicketType_NormalSqlChange),
	}, nil).Build()
	defer baseMock2.UnPatch()
	Actor.GetCommandSetResult(ctx)
}

//	func TestVeDBdoWhenReachExecEndTime(t *testing.T) {
//		vedbActor := mockVeDBOnlineDDLTicketActor()
//		ctx := &mocks.MockContext{}
//		er := fmt.Errorf("error")
//
//		logbMock := mockey.Mock(log.Log).Return().Build()
//		defer logbMock.UnPatch()
//		baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//		defer baseMock0.UnPatch()
//		baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//		defer baseMock00.UnPatch()
//		baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
//		defer baseMock000.UnPatch()
//		baseMock00000 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
//		defer baseMock00000.UnPatch()
//		baseMock1 := mockey.Mock((*VeDBDDLTicketActor).updateActorState).Return().Build()
//		defer baseMock1.UnPatch()
//		baseMock2 := mockey.Mock((*VeDBDDLTicketActor).UpdateTicketRepo).Return(er).Build()
//		defer baseMock2.UnPatch()
//
//		vedbActor.doWhenReachExecEndTime(ctx)
//	}
//
//	func TestVeDBIsNowUnReachCronTime(t *testing.T) {
//		vedbActor := mockVeDBOnlineDDLTicketActor()
//		ctx := &mocks.MockContext{}
//		logbMock := mockey.Mock(log.Log).Return().Build()
//		defer logbMock.UnPatch()
//		baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//		defer baseMock0.UnPatch()
//		baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//		defer baseMock00.UnPatch()
//		baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
//		defer baseMock000.UnPatch()
//
//		vedbActor.IsNowUnReachCronTime(ctx, &shared.ExecVeDBDDLTicket{ExecuteType: int32(model.ExecuteType_Cron),
//			ExecutableStartTime: math.MaxInt32})
//	}
//
//	func TestVeDBdoWhenUnReachCronTime(t *testing.T) {
//		vedbActor := mockVeDBOnlineDDLTicketActor()
//		ctx := &mocks.MockContext{}
//		logbMock := mockey.Mock(log.Log).Return().Build()
//		defer logbMock.UnPatch()
//		baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//		defer baseMock0.UnPatch()
//		baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//		defer baseMock00.UnPatch()
//		baseMock000 := mockey.Mock((*mocks.MockContext).SetReceiveTimeout).Return().Build()
//		defer baseMock000.UnPatch()
//		baseMock0000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
//		defer baseMock0000.UnPatch()
//		baseMock00000 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
//		defer baseMock00000.UnPatch()
//
//		vedbActor.doWhenUnReachCronTime(ctx, &shared.ExecVeDBDDLTicket{ExecutableStartTime: 0})
//	}
func TestProcessOnlineDDLTicketAction(t *testing.T) {
	Actor := mockOnlineDDLTicketActor()
	Actor.state.CurrentAction = model.OnlineDDLTicketAction_ReceiveCommand
	Actor.state.Ds = &shared.DataSource{}
	ctx := &mocks.MockContext{}
	err := fmt.Errorf("error")
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock0000.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, err).Build()
	Actor.processOnlineDDLTicketAction(ctx)
	baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{}, nil).Build()
	Actor.processOnlineDDLTicketAction(ctx)
	defer baseMock2.UnPatch()
}

func TestOnlineDDLUpdateTicketRepo(t *testing.T) {
	Actor := mockOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	er := fmt.Errorf("error")

	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	baseMock1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(er).Build()
	Actor.UpdateTicketRepo(ctx, &dao.Ticket{})
	baseMock1.UnPatch()

	baseMock2 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(nil).Build()
	Actor.UpdateTicketRepo(ctx, &dao.Ticket{})
	baseMock2.UnPatch()
}

func TestOnlineDDLDescribeOnlineDDLTask(t *testing.T) {
	vedbActor := mockOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	er := fmt.Errorf("error")

	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	vedbActor.state.IsDDLTaskCreated = false
	baseMock1 := mockey.Mock((*OnlineDDLTicketActor).sendExecTicketMsg).Return().Build()
	defer baseMock1.UnPatch()

	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
	})

	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   9999999999,
	})

	vedbActor.state.IsDDLTaskCreated = true
	baseMock2 := mockey.Mock((*mocks.MockContext).ClientOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   9999999999,
	})
	baseMock3.UnPatch()

	baseMock31 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.GetDDLTaskStatusResp{}, nil).Build()
	baseMock5 := mockey.Mock(IsSqlTaskInProgress).Return(true).Build()
	baseMock6 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(nil).Build()
	baseMock7 := mockey.Mock((*OnlineDDLTicketActor).doWhenReachEndTime).Return(er).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   1,
	})
	baseMock7.UnPatch()
	baseMock6.UnPatch()

	baseMock71 := mockey.Mock((*OnlineDDLTicketActor).doWhenReachEndTime).Return(nil).Build()
	baseMock71.UnPatch()
	baseMock8 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(er).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   1,
	})
	baseMock8.UnPatch()

	baseMock81 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(nil).Build()
	defer baseMock81.UnPatch()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   1,
	})
	baseMock5.UnPatch()
	baseMock31.UnPatch()

	baseMock32 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.GetDDLTaskStatusErr{}, nil).Build()
	baseMock4 := mockey.Mock((*OnlineDDLTicketActor).UpdateTicketRepo).Return(er).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   9999999999,
	})
	baseMock4.UnPatch()

	baseMock41 := mockey.Mock((*OnlineDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
	vedbActor.DescribeOnlineDDLTask(ctx, &dao.Ticket{
		ExecuteType:         int8(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   9999999999,
	})
	baseMock41.UnPatch()
	baseMock32.UnPatch()
}

func TestOnlineDDLSendExecTicketMsg(t *testing.T) {
	Actor := mockOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	Actor.sendExecTicketMsg(ctx, &dao.Ticket{})
}

func TestOnlineDDLdoWhenReachEndTime(t *testing.T) {
	Actor := mockOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock((*mocks.MockContext).ClientOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock0000.UnPatch()
	baseMock000000 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	defer baseMock000000.UnPatch()

	Actor.state.IsDDLTaskCreated = true
	Actor.doWhenReachEndTime(ctx, &dao.Ticket{ExecuteType: int8(model.ExecuteType_Cron)})
}

func TestOnlineDDLGenerateStopCronTask(t *testing.T) {
	vedbActor := mockOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()

	vedbActor.generateStopCronTask(ctx, &dao.Ticket{})
}

//
//func TestSendStopVeDBDDLTaskToSQLTaskActor(t *testing.T) {
//	//vedbActor := mockOnlineDDLTicketActor()
//	ctx := &mocks.MockContext{}
//	er := fmt.Errorf("error")
//	logbMock := mockey.Mock(log.Log).Return().Build()
//	defer logbMock.UnPatch()
//	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return(er).Build()
//	defer baseMock0.UnPatch()
//	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return().Build()
//	defer baseMock00.UnPatch()
//	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
//	defer baseMock000.UnPatch()
//	a := mocks.NewMockKindClient(&gomock.Controller{})
//	baseMock0000 := mockey.Mock((*mocks.MockContext).ClientOf).Return(a).Build()
//	defer baseMock0000.UnPatch()
//	baseMock000000 := mockey.Mock((*mocks.MockKindClient).Send).Return(fmt.Errorf("error")).Build()
//	defer baseMock000000.UnPatch()
//
//	SendStopOnlineDDLTaskToSQLTaskActor(ctx, &dao.Ticket{TaskId: "1", TicketId: 1})
//}

func TestOnlineDDLStopTicket(t *testing.T) {
	vedbActor := mockOnlineDDLTicketActor()
	ctx := &mocks.MockContext{}
	logbMock := mockey.Mock(log.Log).Return().Build()
	defer logbMock.UnPatch()
	baseMock0 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock0.UnPatch()
	baseMock00 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock00.UnPatch()
	baseMock000 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock000.UnPatch()
	baseMock0000 := mockey.Mock((*mocks.MockContext).ClientOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock0000.UnPatch()
	baseMock000000 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock000000.UnPatch()

	baseMock00000 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	vedbActor.StopTicket(ctx, &shared.StopTicket{})
	baseMock00000.UnPatch()

	baseMock00001 := mockey.Mock((*mocks.MockKindClient).Call).Return(&shared.StopDDLTaskResp{}, nil).Build()
	vedbActor.StopTicket(ctx, &shared.StopTicket{})
	baseMock00001.UnPatch()

	baseMock00002 := mockey.Mock((*mocks.MockKindClient).Call).Return("", nil).Build()
	vedbActor.StopTicket(ctx, &shared.StopTicket{})
	baseMock00002.UnPatch()

}
