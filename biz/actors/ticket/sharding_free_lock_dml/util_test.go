package sharding_free_lock_dml

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestGetTimeCase(t *testing.T) {
	assert.Equal(t, "Undo", SubShardStatusToString(SubTaskUndo))
	assert.Equal(t, "Running", SubShardStatusToString(SubTaskRunning))
	assert.Equal(t, "Finished", SubShardStatusToString(SubTaskSuccess))
	assert.Equal(t, "Failed", SubShardStatusToString(SubTaskError))
	assert.Equal(t, "", SubShardStatusToString(111))
}

func TestChangeGroupShardMapList(t *testing.T) {
	partitions := []*datasource.DbPartitionInfo{{GroupId: 1, ShardId: 1}, {GroupId: 2, ShardId: 1}, {GroupId: 1, ShardId: 2}, {GroupId: 2, ShardId: 2}}
	res := changeGroupShardMapList(partitions)
	assert.Equal(t, 2, len(res))
}

func TestGetSplitBatch(t *testing.T) {
	partitions := []*datasource.DbPartitionInfo{{GroupId: 1, ShardId: 1}, {GroupId: 2, ShardId: 1}, {GroupId: 1, ShardId: 2}, {GroupId: 2, ShardId: 2}}
	total, batch, runningInfo := getSplitBatch(partitions, "delete from t_test")
	assert.Equal(t, 4, total)
	assert.Equal(t, 2, len(batch))
	assert.Equal(t, 2, len(runningInfo))
}
