package sharding_free_lock_dml

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

func (f *ShardingFreeLockDMLActor) SplitShardingFreeLockDMLTicket(ctx types.Context, msg *shared.ExecShardingFreeLockDMLTicket) {
	if err := f.CheckInstanceHasOtherTicket(ctx); err != nil {
		f.state.Status = Error
		f.state.ErrorMsg = err.Error()
		return
	}
	f.state.Status = ShardingDmlSplitSql
	shardType, err := f.ds.GetShardingDbType(ctx, msg.Source, f.state.Ticket.DbName, msg.TableName)
	if err != nil {
		log.Warn(ctx, "get sharding db type error:%s", err.Error())
		f.state.Status = Error
		f.state.ErrorMsg = "get sharding db type error: " + err.Error()
		return
	}
	f.state.User = msg.Source.User
	f.state.Password = msg.Source.Password
	f.state.Address = msg.Source.Address

	if strings.TrimSpace(shardType) == Normal_ShardType {
		f.FormatNormalSubTasks(ctx, msg)
		return
	}
	f.FormatShardingSubTasks(ctx, msg)
}

func (f *ShardingFreeLockDMLActor) CheckInstanceHasOtherTicket(ctx types.Context) error {
	tickets, err := f.workflowDal.GetTicketByInstanceID(ctx, f.state.Ticket.InstanceId)
	if err != nil {
		// 如果不存在,则返回false
		if err == gorm.ErrRecordNotFound {
			log.Info(ctx, "ticket %v: check instance's running tickets success.", ctx.GetName())
			return nil
		}
		log.Warn(ctx, "get ticket %v by instanceId error %v", ctx.GetName(), err)
		return fmt.Errorf(fmt.Sprintf("get ticket %v by instanceId error %v", ctx.GetName(), err))
	}
	if len(tickets) > 0 {
		message := fmt.Sprintf("ticket %v : there are tickets running in this instance[%v],ticketId is [%d]", ctx.GetName(), f.state.Ticket.InstanceId, tickets[0].TicketId)
		log.Warn(ctx, message)
		return fmt.Errorf(message)
	}
	return nil
}

func (f *ShardingFreeLockDMLActor) FormatNormalSubTasks(ctx types.Context, msg *shared.ExecShardingFreeLockDMLTicket) {
	f.state.DbBatchNum = 1
	f.state.TotalSubTask = 1
	instanceBatch := &InstanceBatch{
		BatchIdx: 0,
		Total:    1,
		Sqls:     []string{msg.SqlText},
		GroupId:  0,
		Status:   ShardBatchExecuting,
	}
	f.state.InstanceBatches = []*InstanceBatch{instanceBatch}
	f.state.RunningInfo.ShardRunningInfo = []*ShardRunningInfo{{GroupID: 1, ShardProgress: []*ShardProgress{{ShardDBNo: 0, ExecSql: msg.SqlText, Status: SubShardStatusToString(SubTaskUndo)}}}}
	f.UpdateRunningInfo(ctx)
	ctx.Send(ctx.Self(), &shared.CreateShardingSubTicket{})
}

func (f *ShardingFreeLockDMLActor) FormatShardingSubTasks(ctx types.Context, msg *shared.ExecShardingFreeLockDMLTicket) {
	partitions, err := f.ds.GetPartitionInfos(ctx, msg.Source, f.state.Ticket.DbName)
	if err != nil {
		log.Warn(ctx, "get sharding partition info error:%s", err.Error())
		f.state.Status = Error
		f.state.ErrorMsg = "get sharding partition info error: " + err.Error()
		return
	}
	totalSubTask, instanceBatches, shardRunningInfos := getSplitBatch(partitions, msg.SqlText)
	f.state.InstanceBatches = instanceBatches
	f.state.RunningInfo.ShardRunningInfo = shardRunningInfos
	f.state.TotalSubTask = totalSubTask

	dbBatchNum := int32(1)
	if f.state.Ticket.Extra != nil && f.state.Ticket.Extra["DBBatchNum"] != nil {
		dbBatchNum = int32(f.state.Ticket.Extra["DBBatchNum"].(float64))
	}
	f.state.DbBatchNum = int(dbBatchNum)

	f.UpdateRunningInfo(ctx)
	ctx.Send(ctx.Self(), &shared.CreateShardingSubTicket{})
}

func (f *ShardingFreeLockDMLActor) CreateShardingSubTickets(ctx types.Context) {
	// 检查时间
	f.state.Status = CreateSubTicket
	for idx, batch := range f.state.InstanceBatches {
		f.CreateOneBatchSubTicket(ctx, batch, f.state.RunningInfo.ShardRunningInfo[idx])
	}
	f.UpdateRunningInfo(ctx)
}

func (f *ShardingFreeLockDMLActor) CreateOneBatchSubTicket(ctx types.Context, batch *InstanceBatch, shardRunningInfo *ShardRunningInfo) {
	if batch.Status == ShardBatchError || batch.Status == ShardBatchFinished || batch.Status == ShardBatchPartialFailure {
		// 如果这个batch（在这个group，也就是instance上的）已经失败/完成了，就不执行了
		return
	}
	// 不需要在其他地方关心时间是不是有问题，我们这里是任务核心的发起点
	// 下游任务如果超时了会自己kill，导致任务失败，所以我们只要这里关注‘可执行时间区间’就行了
	// 也不需要关心任务开始执行的时间，即便没有到也可以发起执行，因为下游会卡住等待执行，我们只需要关注结束时间即可，保障不多做
	if !f.CheckEndExecuteTimeLegal() {
		// 将这个instanceBatch转为部分失败，因为接下来会在check的时候，获取到下游任务都超时失败
		batch.Status = ShardBatchPartialFailure
		batch.ErrorMsg = "out execution time range"
		return
	}
	for i := batch.BatchIdx; i < batch.BatchIdx+f.state.DbBatchNum && i < batch.Total; i++ {
		ticketId, err := f.CreateSubTicket(ctx, batch.Sqls[i], shardRunningInfo.ShardProgress[i])
		if err != nil {
			// 中间失败了，就不继续往下走了
			log.Warn(ctx, "createSub free-lock dml ticket error:%s", err.Error())
			batch.ErrorMsg = "createSub free-lock dml ticket error: " + err.Error()
			batch.Status = ShardBatchPartialFailure
			shardRunningInfo.ShardProgress[i].Status = SubShardStatusToString(SubTaskError)
			shardRunningInfo.ShardProgress[i].ErrMsg = "createSub free-lock dml ticket error: " + err.Error()
			f.ProducerToTls(ctx, "createSub free-lock dml ticket error: "+err.Error())
			break
		}
		f.ProducerToTls(ctx, "start execute sub task, execute sql:  "+batch.Sqls[i])
		batch.TicketIds = append(batch.TicketIds, ticketId)
		shardRunningInfo.ShardProgress[i].Status = SubShardStatusToString(SubTaskRunning)
		shardRunningInfo.ShardProgress[i].SubTaskId = ticketId
	}
}

func (f *ShardingFreeLockDMLActor) CreateSubTicket(ctx types.Context, sqlStr string, shardProgress *ShardProgress) (int64, error) {
	ds := f.GenShardingDatasource(ctx)
	ticketId, err := f.CreateTicket(ctx, sqlStr)
	if err != nil {
		log.Warn(ctx, "create inner Ticket error:%s", err.Error())
		return 0, err
	}
	hint := fmt.Sprintf(" /*dbatman_shard_id=%d*/ ", shardProgress.ShardDBNo)
	_, err = f.actorClient.KindOf(consts.FreeLockDMLActorKind).
		Call(ctx, conv.Int64ToStr(ticketId), &shared.ExecFreeLockDMLTicket{
			TenantID:            f.state.Ticket.TenantId,
			UserID:              f.state.Ticket.TenantId,
			Source:              ds,
			TicketType:          int32(f.state.Ticket.TicketType),
			ExecuteType:         int32(f.state.Ticket.ExecuteType),
			SqlText:             sqlStr,
			ExecutableStartTime: int32(f.state.Ticket.ExecutableStartTime),
			ExecutableEndTime:   int32(f.state.Ticket.ExecutableEndTime),
			CreateFrom:          InnerCreatedFrom,
			Hint:                hint,
		})
	if err != nil {
		log.Warn(ctx, "ticketId:%d call FreeLockDMLActor error:%s ", ticketId, err.Error())
		errMsg := "send actor to execute error: " + err.Error()
		_ = f.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{TicketId: ticketId, TicketStatus: int8(model.TicketStatus_TicketError), Description: errMsg})
		return 0, err
	}
	return ticketId, nil
}

func (f *ShardingFreeLockDMLActor) GenShardingDatasource(ctx types.Context) *shared.DataSource {
	cnf := f.conf.Get(ctx)
	ds := &shared.DataSource{
		Type:             shared.DataSourceType(shared.DataSourceType_value[f.state.Ticket.InstanceType]),
		LinkType:         shared.Volc,
		ConnectTimeoutMs: cnf.TicketConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.TicketReadTimeout * 1000,
		WriteTimeoutMs:   cnf.TicketWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.TicketIdleTimeout * 1000,
		InstanceId:       f.state.Ticket.InstanceId,
		Db:               f.state.Ticket.DbName,
	}
	//account, err := f.ticketService.GetDBAccount(ctx, ds)
	//if err != nil {
	//	log.Warn(ctx, "get instance:%s account error:%s", f.state.Ticket.InstanceId, err.Error())
	//	return nil, err
	//}
	ds.User = f.state.User
	ds.Password = f.state.Password

	//addressRes, err := f.ds.GetDBInnerAddress(ctx, &datasource.GetDBInnerAddressReq{Source: ds})
	//if err != nil {
	//	log.Warn(ctx, "get instance:%s inner address error:%s", f.state.Ticket.InstanceId, err.Error())
	//	return nil, err
	//}
	ds.Address = f.state.Address
	return ds
}

func (f *ShardingFreeLockDMLActor) CreateTicket(ctx types.Context, sqlStr string) (int64, error) {
	ticketId, err := f.idgenSvc.NextID(ctx)
	if err != nil {
		errMsg := fmt.Sprintf("创建内部工单失败, 生成工单ID失败，err:%s", err.Error())
		log.Warn(ctx, errMsg)
		return 0, fmt.Errorf("reate ticket failed, generate ticket ID failed")
	}
	ticket := &dao.Ticket{
		TicketId:            ticketId,
		FlowConfigId:        0,
		WorkflowId:          0,
		ApprovalFlowId:      0,
		TicketType:          int8(model.TicketType_FreeLockSqlChange),
		TicketStatus:        workflow.TicketWaitExecute,
		FlowStep:            0,
		ExecuteType:         f.state.Ticket.TicketType,
		CreateTime:          time.Now().UnixMilli(),
		UpdateTime:          time.Now().UnixMilli(),
		CreateUserId:        fmt.Sprintf("%d", f.state.Ticket.TicketId),
		TenantId:            f.state.Ticket.TenantId,
		CreatedFrom:         InnerCreatedFrom,
		CurrentUserIds:      "",
		CurrentUserRole:     "",
		AllOperatorId:       "",
		InstanceType:        f.state.Ticket.InstanceType,
		InstanceId:          f.state.Ticket.InstanceId,
		SqlText:             sqlStr,
		DbName:              f.state.Ticket.DbName,
		ExecutableStartTime: f.state.Ticket.ExecutableStartTime,
		ExecutableEndTime:   f.state.Ticket.ExecutableEndTime,
		Extra:               f.state.Ticket.Extra,
		DataArchiveConfig:   f.state.Ticket.DataArchiveConfig,
		Memo:                "Inner Ticket",
	}
	if err = f.workflowDal.CreateTicket(ctx, ticket); err != nil {
		errMsg := fmt.Sprintf("create ticket error，ticketId:%d, err:%v", ticketId, err)
		log.Warn(ctx, errMsg)
		return 0, fmt.Errorf("failed to create ticket: internal error")
	}
	return ticketId, nil
}
