package sharding_free_lock_dml

import (
	config2 "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestSplitShardingFreeLockDMLTicket(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()

	actor := mockShardingFreeLockDMLActor()
	actor.state.Ticket = &dao.Ticket{}
	actor.state.RunningInfo = &RunningInfo{}
	mock7 := mockey.Mock((*ShardingFreeLockDMLActor).CheckInstanceHasOtherTicket).Return(fmt.Errorf("test")).Build()
	actor.SplitShardingFreeLockDMLTicket(&mocks.MockContext{}, &shared.ExecShardingFreeLockDMLTicket{})
	mock7.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock8 := mockey.Mock((*ShardingFreeLockDMLActor).CheckInstanceHasOtherTicket).Return(nil).Build()
	defer mock8.UnPatch()
	mock1 := mockey.Mock((*mocks.MockDataSourceService).GetShardingDbType).Return("", fmt.Errorf("test")).Build()
	actor.SplitShardingFreeLockDMLTicket(&mocks.MockContext{}, &shared.ExecShardingFreeLockDMLTicket{})
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*ShardingFreeLockDMLActor).UpdateRunningInfo).Return().Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mocks.MockDataSourceService).GetShardingDbType).Return(Normal_ShardType, nil).Build()
	actor.SplitShardingFreeLockDMLTicket(&mocks.MockContext{}, &shared.ExecShardingFreeLockDMLTicket{Source: &shared.DataSource{}})
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*mocks.MockDataSourceService).GetShardingDbType).Return(Shard_ShardType, nil).Build()
	mock6 := mockey.Mock((*ShardingFreeLockDMLActor).FormatShardingSubTasks).Return().Build()
	defer mock6.UnPatch()
	actor.SplitShardingFreeLockDMLTicket(&mocks.MockContext{}, &shared.ExecShardingFreeLockDMLTicket{Source: &shared.DataSource{}})
	mock5.UnPatch()
	time.Sleep(100 * time.Millisecond)
}

func TestFormatShardingSubTasks(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()

	actor := mockShardingFreeLockDMLActor()
	actor.state.Ticket = &dao.Ticket{}
	actor.state.RunningInfo = &RunningInfo{}
	mock1 := mockey.Mock((*ShardingFreeLockDMLActor).UpdateRunningInfo).Return().Build()
	defer mock1.UnPatch()
	partitions := []*datasource.DbPartitionInfo{{GroupId: 1, ShardId: 1}, {GroupId: 2, ShardId: 1}, {GroupId: 1, ShardId: 2}, {GroupId: 2, ShardId: 2}}
	mock2 := mockey.Mock((*mocks.MockDataSourceService).GetPartitionInfos).Return(partitions, fmt.Errorf("test")).Build()
	actor.FormatShardingSubTasks(&mocks.MockContext{}, &shared.ExecShardingFreeLockDMLTicket{})
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock3 := mockey.Mock((*mocks.MockDataSourceService).GetPartitionInfos).Return(partitions, nil).Build()
	defer mock3.UnPatch()
	actor.FormatShardingSubTasks(&mocks.MockContext{}, &shared.ExecShardingFreeLockDMLTicket{})
}

func TestCreateShardingSubTickets(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()

	actor := mockShardingFreeLockDMLActor()
	actor.state.RunningInfo = &RunningInfo{ShardRunningInfo: []*ShardRunningInfo{{}}}
	actor.state.InstanceBatches = []*InstanceBatch{{}}
	mock1 := mockey.Mock((*ShardingFreeLockDMLActor).CreateOneBatchSubTicket).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*ShardingFreeLockDMLActor).UpdateRunningInfo).Return().Build()
	defer mock2.UnPatch()
	actor.CreateShardingSubTickets(&mocks.MockContext{})
}

func TestCreateOneBatchSubTicket(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()

	actor := mockShardingFreeLockDMLActor()
	batch := &InstanceBatch{}
	runningInfo := &ShardRunningInfo{}
	batch.Status = ShardBatchError
	actor.CreateOneBatchSubTicket(&mocks.MockContext{}, batch, runningInfo)
	batch.Status = ShardBatchExecuting

	mock1 := mockey.Mock((*ShardingFreeLockDMLActor).CheckEndExecuteTimeLegal).Return(false).Build()
	actor.CreateOneBatchSubTicket(&mocks.MockContext{}, batch, runningInfo)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*ShardingFreeLockDMLActor).CheckEndExecuteTimeLegal).Return(true).Build()
	defer mock2.UnPatch()
	batch.BatchIdx = 0
	batch.Total = 1
	actor.state.DbBatchNum = 1
	batch.Status = ShardBatchExecuting
	batch.Sqls = []string{""}
	batch.TicketIds = []int64{}
	runningInfo.ShardProgress = []*ShardProgress{{}}
	mock3 := mockey.Mock((*ShardingFreeLockDMLActor).CreateSubTicket).Return(int64(1), fmt.Errorf("test")).Build()
	actor.CreateOneBatchSubTicket(&mocks.MockContext{}, batch, runningInfo)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*ShardingFreeLockDMLActor).CreateSubTicket).Return(int64(1), nil).Build()
	defer mock4.UnPatch()
	batch.Status = ShardBatchExecuting
	actor.CreateOneBatchSubTicket(&mocks.MockContext{}, batch, runningInfo)
}

func TestCreateSubTicket(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()

	mock1 := mockey.Mock((*config.MockConfigProvider).Get).Return(&config2.Config{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockService).NextID).Return(11111, nil).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*mocks.MockWorkflowDAL).CreateTicket).Return(fmt.Errorf("test")).Build()
	actor := mockShardingFreeLockDMLActor()
	actor.state.Ticket = &dao.Ticket{InstanceType: "MySQLSharding"}
	id, err := actor.CreateSubTicket(&mocks.MockContext{}, "", &ShardProgress{})
	assert.NotNil(t, err)
	assert.Equal(t, int64(0), id)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)
}

func TestCheckInstanceHasOtherTicket(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()

	actor := mockShardingFreeLockDMLActor()
	actor.state.Ticket = &dao.Ticket{InstanceType: "MySQLSharding"}

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).GetTicketByInstanceID).Return([]*dao.Ticket{{}}, fmt.Errorf("test")).Build()
	resp := actor.CheckInstanceHasOtherTicket(&mocks.MockContext{})
	assert.NotNil(t, resp)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).GetTicketByInstanceID).Return([]*dao.Ticket{{}}, nil).Build()
	defer mock2.UnPatch()
	resp = actor.CheckInstanceHasOtherTicket(&mocks.MockContext{})
	assert.NotNil(t, resp)
}
