package sharding_free_lock_dml

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"fmt"
)

const (
	ShardingDmlUndo     = 0
	ShardingDmlSplitSql = 1
	CreateSubTicket     = 2
	Error               = 3
	Finished            = 4

	ActorReceiveTimeout = 15
	MaxErrorNum         = 10 // 允许过程中出现的最多的失败次数，这个仅限于操作数据库/请求actor等可能的偶发失败，类似于工单执行失败的情况，则不包含在内

	ShardBatchExecuting      = 1
	ShardBatchPartialFailure = 2 // 部分失败，一个batch有多个并发，中间某个失败了，就是这个状态，这个时候不能再创建任务了，而是要等已经创建出的任务都完成，然后转为失败
	ShardBatchError          = 3
	ShardBatchFinished       = 4

	SubTaskUndo    = 0
	SubTaskRunning = 1
	SubTaskSuccess = 2
	SubTaskError   = 3

	InnerCreatedFrom = "InnerShardingFreeLockDml"

	Normal_ShardType = ""
	Shard_ShardType  = "partition"

	RunningStatus_Running  = "Running"
	RunningStatus_Finished = "Finished"
	RunningStatus_Failed   = "Failed"

	HintBase = " /*dbatman_shard_id=%d*/ %s"
)

type InstanceBatch struct {
	TicketIds []int64
	BatchIdx  int
	Sqls      []string
	Total     int
	Status    int
	GroupId   int32
	ErrorMsg  string
}

type BatchExecuteInfo struct {
	BatchTotal   int
	ErrorNum     int
	SuccessNum   int
	ExecutingNum int
	UndoNum      int
}

type RunningInfo struct {
	ShardStatus      string
	ShardRunningInfo []*ShardRunningInfo
}

type ShardRunningInfo struct {
	GroupID       int32
	ShardProgress []*ShardProgress
}

type ShardProgress struct {
	ShardDBNo int32
	Progress  int32
	Status    string
	ExecSql   string
	SubTaskId int64
	ErrMsg    string
}

func SubShardStatusToString(shardStatus int) string {
	switch shardStatus {
	case SubTaskUndo:
		return "Undo"
	case SubTaskRunning:
		return "Running"
	case SubTaskSuccess:
		return "Finished"
	case SubTaskError:
		return "Failed"
	}
	return ""
}

func changeGroupShardMapList(partitions []*datasource.DbPartitionInfo) map[int32][]int32 {
	groupShardMap := make(map[int32][]int32)
	for _, value := range partitions {

		if _, ok := groupShardMap[value.GroupId]; !ok {
			groupShardMap[value.GroupId] = []int32{}
		}

		groupShardMap[value.GroupId] = append(groupShardMap[value.GroupId], value.ShardId)
	}
	return groupShardMap
}

func getSplitBatch(partitions []*datasource.DbPartitionInfo, sqlStr string) (int, []*InstanceBatch, []*ShardRunningInfo) {

	var instanceBatches []*InstanceBatch
	var shardRunningInfos []*ShardRunningInfo
	total := 0

	groupShardMap := changeGroupShardMapList(partitions)
	for groupId, shards := range groupShardMap {
		instanceBatch := &InstanceBatch{
			BatchIdx: 0,
			Total:    len(shards),
			Sqls:     []string{},
			GroupId:  groupId,
			Status:   ShardBatchExecuting,
		}
		shardRunningInfo := &ShardRunningInfo{GroupID: groupId, ShardProgress: []*ShardProgress{}}
		for _, shardId := range shards {
			execSql := fmt.Sprintf(HintBase, shardId, sqlStr)
			instanceBatch.Sqls = append(instanceBatch.Sqls, execSql)
			shardRunningInfo.ShardProgress = append(shardRunningInfo.ShardProgress, &ShardProgress{ShardDBNo: shardId, Status: SubShardStatusToString(SubTaskUndo), ExecSql: execSql})
			total++
		}
		instanceBatches = append(instanceBatches, instanceBatch)
		shardRunningInfos = append(shardRunningInfos, shardRunningInfo)
	}
	return total, instanceBatches, shardRunningInfos
}

func interceptStr1k(str string) string {
	if len(str) > 1024 {
		return str[0:1024]
	} else {
		return str
	}
}
