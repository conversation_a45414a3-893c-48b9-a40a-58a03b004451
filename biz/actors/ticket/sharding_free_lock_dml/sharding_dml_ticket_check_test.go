package sharding_free_lock_dml

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestCheckShardingDmlTicket(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()
	basicMock5 := mockey.Mock((*ShardingFreeLockDMLActor).ProducerToTls).Return().Build()
	defer basicMock5.UnPatch()

	actor := mockShardingFreeLockDMLActor()
	actor.state.Status = ShardingDmlUndo
	actor.state.Ticket = &dao.Ticket{}
	actor.CheckShardingDmlTicket(&mocks.MockContext{})

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(fmt.Errorf("test")).Build()
	defer mock1.UnPatch()
	mock4 := mockey.Mock((*ShardingFreeLockDMLActor).updateSqlTaskRunningInfo).Return().Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(nil).Build()
	defer mock5.UnPatch()
	actor.state.Status = Error
	actor.state.TotalSubTask = 1
	actor.CheckShardingDmlTicket(&mocks.MockContext{})

	actor.state.Status = CreateSubTicket
	actor.state.InstanceBatches = []*InstanceBatch{{}}
	actor.state.RunningInfo = &RunningInfo{ShardRunningInfo: []*ShardRunningInfo{{}}}
	mock2 := mockey.Mock((*ShardingFreeLockDMLActor).CheckOneBatchTicket).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*ShardingFreeLockDMLActor).CheckAllBatchStatus).Return().Build()
	defer mock3.UnPatch()

	actor.CheckShardingDmlTicket(&mocks.MockContext{})
}

func TestCheckOneBatchTicket(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()

	actor := mockShardingFreeLockDMLActor()
	actor.state.Ticket = &dao.Ticket{}

	batch := &InstanceBatch{Total: 1, BatchIdx: 0, Sqls: []string{""}, TicketIds: []int64{1}}
	runningInfo := &ShardRunningInfo{ShardProgress: []*ShardProgress{{}}}
	batch.Status = ShardBatchError
	err := actor.CheckOneBatchTicket(&mocks.MockContext{}, batch, runningInfo)
	assert.Nil(t, err)
	actor.state.DbBatchNum = 1

	batch.Status = ShardBatchExecuting
	mock1 := mockey.Mock((*ShardingFreeLockDMLActor).UpdateRunningInfo).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*ShardingFreeLockDMLActor).CheckOneBatchStatus).Return().Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*ShardingFreeLockDMLActor).GetSubTicketStatus).Return(ShardingDmlUndo, "", nil).Build()
	_ = actor.CheckOneBatchTicket(&mocks.MockContext{}, batch, runningInfo)
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*ShardingFreeLockDMLActor).GetSubTicketStatus).Return(SubTaskRunning, "", nil).Build()
	_ = actor.CheckOneBatchTicket(&mocks.MockContext{}, batch, runningInfo)
	mock4.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock5 := mockey.Mock((*ShardingFreeLockDMLActor).GetSubTicketStatus).Return(SubTaskSuccess, "", nil).Build()
	_ = actor.CheckOneBatchTicket(&mocks.MockContext{}, batch, runningInfo)
	mock5.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock6 := mockey.Mock((*ShardingFreeLockDMLActor).GetSubTicketStatus).Return(SubTaskError, "", nil).Build()
	_ = actor.CheckOneBatchTicket(&mocks.MockContext{}, batch, runningInfo)
	mock6.UnPatch()
	time.Sleep(100 * time.Millisecond)
}

func TestCheckAllBatchStatus(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()

	mock1 := mockey.Mock((*ShardingFreeLockDMLActor).FinishedTicket).Return().Build()
	defer mock1.UnPatch()

	actor := mockShardingFreeLockDMLActor()
	actor.state.Ticket = &dao.Ticket{}
	instanceBatches := []*InstanceBatch{{Status: ShardBatchPartialFailure}}
	actor.state.InstanceBatches = instanceBatches
	actor.CheckAllBatchStatus(&mocks.MockContext{})
	instanceBatches = []*InstanceBatch{{Status: ShardBatchError}}
	actor.state.InstanceBatches = instanceBatches
	actor.CheckAllBatchStatus(&mocks.MockContext{})
	instanceBatches = []*InstanceBatch{{Status: ShardBatchFinished}}
	actor.state.InstanceBatches = instanceBatches
	actor.CheckAllBatchStatus(&mocks.MockContext{})
}

func TestCheckOneBatchStatus(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()
	basicMock5 := mockey.Mock((*ShardingFreeLockDMLActor).ProducerToTls).Return().Build()
	defer basicMock5.UnPatch()

	mock1 := mockey.Mock((*ShardingFreeLockDMLActor).DoNextBatch).Return().Build()
	defer mock1.UnPatch()

	actor := mockShardingFreeLockDMLActor()
	actor.state.Ticket = &dao.Ticket{}
	batchExecuteInfo := &BatchExecuteInfo{}
	batchExecuteInfo.ExecutingNum = 1
	actor.CheckOneBatchStatus(&mocks.MockContext{}, &InstanceBatch{}, batchExecuteInfo, &ShardRunningInfo{})
	batchExecuteInfo.ExecutingNum = 0
	actor.CheckOneBatchStatus(&mocks.MockContext{}, &InstanceBatch{}, batchExecuteInfo, &ShardRunningInfo{})
	batchExecuteInfo.ErrorNum = 1
	actor.CheckOneBatchStatus(&mocks.MockContext{}, &InstanceBatch{}, batchExecuteInfo, &ShardRunningInfo{})
}

func TestDoNextBatch(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()

	mock1 := mockey.Mock((*ShardingFreeLockDMLActor).CreateOneBatchSubTicket).Return().Build()
	defer mock1.UnPatch()
	actor := mockShardingFreeLockDMLActor()

	batchExecuteInfo := &BatchExecuteInfo{}
	instanceBatch := &InstanceBatch{}
	actor.DoNextBatch(&mocks.MockContext{}, instanceBatch, batchExecuteInfo, &ShardRunningInfo{})
	instanceBatch.Total = 100
	actor.DoNextBatch(&mocks.MockContext{}, instanceBatch, batchExecuteInfo, &ShardRunningInfo{})
}

func TestGetSubTicketStatus(t *testing.T) {
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()

	mock1 := mockey.Mock((*ShardingFreeLockDMLActor).CreateOneBatchSubTicket).Return().Build()
	defer mock1.UnPatch()
	actor := mockShardingFreeLockDMLActor()
	status, _, err := actor.GetSubTicketStatus(&mocks.MockContext{}, 0, &ShardProgress{})
	assert.Nil(t, err)
	assert.Equal(t, SubTaskUndo, status)

	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeTicketWithoutTenant).Return(&dao.Ticket{}, fmt.Errorf("test")).Build()
	_, _, _ = actor.GetSubTicketStatus(&mocks.MockContext{}, 1, &ShardProgress{})
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	ticket := &dao.Ticket{}
	mock3 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeTicketWithoutTenant).Return(ticket, nil).Build()
	defer mock3.UnPatch()
	ticket.TicketStatus = int8(model.TicketStatus_TicketError)
	_, _, _ = actor.GetSubTicketStatus(&mocks.MockContext{}, 1, &ShardProgress{})
	ticket.TicketStatus = int8(model.TicketStatus_TicketFinished)
	_, _, _ = actor.GetSubTicketStatus(&mocks.MockContext{}, 1, &ShardProgress{})
	ticket.TicketStatus = int8(0)
	_, _, _ = actor.GetSubTicketStatus(&mocks.MockContext{}, 1, &ShardProgress{})
}
