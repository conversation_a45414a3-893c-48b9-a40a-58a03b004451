package sharding_free_lock_dml

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"crypto/md5"
	"fmt"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"
	"os"
	"time"
)

func (f *ShardingFreeLockDMLActor) initTlsClient(ctx types.Context) {
	c3Cfg := f.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	Ak := c3Cfg.TLSServiceAccessKey
	Sk := c3Cfg.TLSServiceSecretKey
	regionId := os.Getenv(`BDC_REGION_ID`)
	tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, regionId)
	f.tlsClient = tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)
}

func (f *ShardingFreeLockDMLActor) ProducerToTls(ctx types.Context, msg string) {
	if f.state.Ticket == nil {
		return
	}
	process := 0
	if f.state.TotalSubTask != 0 {
		process = f.state.SuccessTask * 100 / f.state.TotalSubTask
	}
	_, err := f.tlsClient.PutLogs(f.formatTlsLogs(ctx, utils.Int64ToStr(f.state.Ticket.TicketId), fmt.Sprintf("%d%%", process), f.state.Ticket.TenantId, f.state.Ticket.InstanceId, msg))
	if err != nil {
		log.Warn(ctx, "ProducerToTls: write tls error:%s", err.Error())
	}
	time.Sleep(1 * time.Second)
}

func (f *ShardingFreeLockDMLActor) formatTlsLogs(ctx types.Context, ticketId string, progress string, tenantId string, instanceId string, msg string) *tls.PutLogsRequest {
	metricLog := &pb.Log{
		Time: time.Now().Unix(),
		Contents: []*pb.LogContent{
			{Key: "TicketId", Value: ticketId},
			{Key: "Progress", Value: progress},
			{Key: "Message", Value: msg},
			{Key: "CreateTime", Value: time.Now().Format("2006-01-02 15:04:05")},
			{Key: "TenantId", Value: tenantId},
			{Key: "InstanceId", Value: instanceId},
		},
	}
	logs := []*pb.Log{metricLog}
	logGroup := &pb.LogGroup{
		Logs: logs,
	}
	//cnf := f.cnf.Get(ctx)
	c3Cfg := f.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	topicId := c3Cfg.TicketLogTopic
	// topicId = "9cadca36-ed6f-426d-ab6d-083cb64e64ce"

	request := &tls.PutLogsRequest{
		TopicID: topicId, //"cee26aa2-f1c8-42cd-ac35-dd9e84a33c99",
		LogBody: &pb.LogGroupList{LogGroups: []*pb.LogGroup{logGroup}},
		HashKey: f.getHash(instanceId),
	}
	return request
}

func (f *ShardingFreeLockDMLActor) getHash(value string) string {
	b := []byte(value)
	return fmt.Sprintf("%x", md5.Sum(b))
}
