package actors

import (
	"fmt"
	"strconv"

	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

func (a *SessionActor) listKeyNumbers(ctx types.Context, msg *shared.ListKeyNumbers) {
	DB, err := strconv.ParseInt(msg.DB, 10, 32)
	if err != nil {
		errMsg := fmt.Sprintf("Invalid DB Number: %s", msg.DB)
		log.Warn(ctx, errMsg)
		ctx.Respond(&shared.ListKeyNumbersFailed{
			ErrorMessage: errMsg,
		})
	}
	req := &datasource.ListKeyNumbersReq{
		Source: a.state.DataSource,
		DB:     int(DB),
	}
	// FIXME: overflow? use uint64
	resp, err := a.sources.ListKeyNumbers(ctx, req)
	if err != nil {
		log.Warn(ctx, "list key numbers fail %v", err)
		ctx.Respond(&shared.ListKeyNumbersFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.ListKeyNumbersResult{
		KeyNumbers: resp.Total,
	})
}

func (a *SessionActor) listKeyMembers(ctx types.Context, msg *shared.ListKeyMembers) {
	DB, err := strconv.ParseInt(msg.DB, 10, 32)
	if err != nil {
		msg := fmt.Sprintf("Invalid DB Number: %s", msg.DB)
		log.Warn(ctx, msg)
		ctx.Respond(&shared.ListKeyMembersFailed{
			ErrorMessage: msg,
		})
	}
	req := &datasource.ListKeyMembersReq{
		Source:  a.state.DataSource,
		DB:      int(DB),
		Key:     msg.Key,
		KeyType: msg.KeyType,
		Offset:  msg.Offset,
		Limit:   msg.Limit,
	}
	resp, err := a.sources.ListKeyMembers(ctx, req)
	if err != nil {
		log.Warn(ctx, "list key members fail %v", err)
		ctx.Respond(&shared.ListKeyMembersFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.ListKeyMembersResult{
		Total:   resp.Total,
		Members: resp.Members,
	})
}

func (a *SessionActor) listKeys(ctx types.Context, msg *shared.ListKeys) {
	DB, err := strconv.ParseInt(msg.DB, 10, 32)
	if err != nil {
		msg := fmt.Sprintf("Invalid DB Number: %s", msg.DB)
		log.Warn(ctx, msg)
		ctx.Respond(&shared.ListKeysFailed{
			ErrorMessage: msg,
		})
	}
	if msg.IsExact {
		a.listKeyExact(ctx, int(DB), msg.Query)
		return
	}
	a.listKeysByPattern(ctx, int(DB), msg)
}

func (a *SessionActor) listKeysByPattern(ctx types.Context, DB int, param *shared.ListKeys) {
	req := &datasource.ListKeysReq{
		Source: a.state.DataSource,
		ScanArgs: &datasource.ScanReq{
			Args:  []interface{}{"SCAN"},
			DB:    DB,
			Limit: param.Limit,
			Query: param.Query,
		},
	}
	if a.isSameQuery(param) {
		if param.Limit > a.state.QueryContext.PrePageSize {
			req.ScanArgs.Cursor = a.state.QueryContext.CurCursor
		} else {
			req.ScanArgs.Cursor = a.state.QueryContext.CursorMap[param.Offset-1]
		}
		req.ScanArgs.IgnoreTotalKeys = true
	} else {
		req.ScanArgs.Cursor = 0
		a.state.clearQueryContext()
	}
	resp, err := a.sources.ListKeys(ctx, req)
	if err != nil {
		log.Warn(ctx, "list keys fail %v", err)
		ctx.Respond(&shared.ListKeysFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	a.state.QueryContext.CurCursor = resp.ScanResult.NewCursor
	a.state.QueryContext.DB = DB
	a.state.QueryContext.SetCursor(param.Offset, resp.ScanResult.NewCursor)
	a.state.QueryContext.PrePageNumber = int64(param.Offset)
	a.state.QueryContext.PrePageSize = param.Limit
	a.state.QueryContext.Query = param.Query
	//if !a.isSameQuery(param) {
	//	a.state.QueryContext.TotalKeys = resp.Total
	//}
	ctx.Respond(&shared.ListKeysResult{
		Keys:  resp.ScanResult.Keys,
		Types: resp.ScanResult.Types,
		TTLs:  resp.ScanResult.TTL,
		//Total: a.state.QueryContext.TotalKeys,
	})
}

func (a *SessionActor) listKeyExact(ctx types.Context, db int, key string) {
	req := &datasource.GetKeyReq{
		Source: a.state.DataSource,
		DB:     db,
		Key:    key,
	}
	resp, err := a.sources.GetKey(ctx, req)
	if err != nil {
		log.Warn(ctx, "list keys fail %v", err)
		ctx.Respond(&shared.ListKeysFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	ret := &shared.ListKeysResult{}
	if resp.Type != "" {
		ret.Keys = []string{resp.Key}
		ret.Types = []string{resp.Type}
		ret.TTLs = []string{resp.TTL}
		ret.Total = 1
	}
	ctx.Respond(ret)
}

func (a *SessionActor) isSameQuery(param *shared.ListKeys) bool {
	if param.DB != strconv.Itoa(a.state.QueryContext.DB) ||
		param.Query != a.state.QueryContext.Query ||
		param.Limit != a.state.QueryContext.PrePageSize ||
		param.Offset == 1 ||
		AbsoluteValue(int64(param.Offset), a.state.QueryContext.PrePageNumber) > 1 {
		return false
	}
	return true
}

func (a *SessionActor) listAlterKVsCommands(ctx types.Context, msg *shared.ListAlterKVsCommands) {
	DB, err := strconv.ParseInt(msg.DB, 10, 32)
	if err != nil {
		msg := fmt.Sprintf("Invalid DB Number: %s", msg.DB)
		log.Warn(ctx, msg)
		ctx.Respond(&shared.ListAlterKVsFailed{
			ErrorMessage: msg,
		})
	}
	req := &datasource.ListAlterKVsCommandsReq{
		Source: a.state.DataSource,
		DB:     int(DB),
		KVs:    msg.KVs,
	}
	resp, err := a.sources.ListAlterKVsCommands(ctx, req)
	if err != nil {
		log.Warn(ctx, "list alter kvs commands failed %v", err)
		ctx.Respond(&shared.ListAlterKVsFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.ListAlterKVsResult{
		Commands: resp.Commands,
	})
}

func AbsoluteValue(a, b int64) int64 {
	v := a - b
	if v < 0 {
		return -v
	}
	return v
}
