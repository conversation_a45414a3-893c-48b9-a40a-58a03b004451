package tenant

import (
	"fmt"
	"go.uber.org/dig"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/qjpcpu/fp"
)

type NewTenantMgrActorIn struct {
	dig.In
	TenantRepo repository.TenantRepo
}

func NewTenantMgrActor(in NewTenantMgrActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.TenantMgrKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &TenantMgrActor{
				tenantRepo: in.TenantRepo,
			}
		}),
	}
}

type TenantMgrActor struct {
	tenantRepo repository.TenantRepo
}

func (t *TenantMgrActor) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		return
	case *shared.AddTenant:
		t.addTenant(ctx, msg)
	case *shared.RemoveTenant:
		t.removeTenant(ctx, msg)
	case *shared.ListTenants:
		t.listTenants(ctx, msg)
	}
}

func (t *TenantMgrActor) addTenant(ctx types.Context, msg *shared.AddTenant) {
	resp, err := t.tenantRepo.Get(ctx, msg.TenantId)
	if err != nil {
		log.WarnS(ctx, "failed to get tenant "+msg.TenantId, "err", err.Error())
		ctx.Respond(&shared.AddTenantFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	if resp.ID != 0 {
		log.Info(ctx, "tenant id %s already exists", msg.TenantId)
		ctx.Respond(&shared.AddTenantSucceed{})
		return
	}

	now := time.Now().Unix() * 1000
	if err = t.tenantRepo.Create(ctx, &entity.Tenant{
		TenantID:     msg.GetTenantId(),
		State:        entity.TenantState_Agree,
		CreateTimeMs: now,
		ModifyTimeMs: now,
	}); err != nil {
		log.WarnS(ctx, "failed to add tenant "+msg.TenantId, "err", err.Error())
		ctx.Respond(&shared.AddTenantFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.AddTenantSucceed{})
}

func (t *TenantMgrActor) removeTenant(ctx types.Context, msg *shared.RemoveTenant) {
	now := time.Now().Unix() * 1000
	if err := t.tenantRepo.Save(ctx, &entity.Tenant{
		State:        entity.TenantState_Disagree,
		ModifyTimeMs: now,
	}); err != nil {
		log.WarnS(ctx, "failed to remove tenant "+msg.TenantId, "err", err.Error())
		//ctx.Respond(&shared.RemoveTenantFailed{
		//	ErrorMessage: err.Error(),
		//})
		return
	}
	//ctx.Respond(&shared.RemoveTenantSucceed{})
}

func (t *TenantMgrActor) listTenants(ctx types.Context, msg *shared.ListTenants) {
	var state entity.TenantState
	var sp *entity.TenantState
	if msg.HasState {
		state = entity.TenantState(msg.State)
		sp = &state
	}

	tenantInfos, err := t.tenantRepo.List(ctx, sp, msg.Offset, msg.Limit)
	if err != nil {
		log.WarnS(ctx, "failed to list tenants", "state", fmt.Sprintf("%v", state))
		ctx.Respond(&shared.ListTenantsFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	var ret []*shared.TenantInfo
	fp.StreamOf(tenantInfos.Tenants).Map(func(t *entity.Tenant) *shared.TenantInfo {
		return &shared.TenantInfo{
			TenantID: t.TenantID,
			State:    shared.TenantState(t.State),
		}
	}).ToSlice(&ret)
	ctx.Respond(&shared.TenantInfos{
		Total:   tenantInfos.Total,
		Tenants: ret,
	})
}
