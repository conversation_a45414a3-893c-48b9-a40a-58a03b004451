package sqlflow

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"
	"go.uber.org/dig"
	"gorm.io/gorm"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/entity/taskflow"
	dbwerrors "code.byted.org/infcs/dbw-mgr/biz/errors"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/cmdset"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbwinstance"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/metrics"
	"code.byted.org/infcs/dbw-mgr/biz/service/parser"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

type jobStage int

const (
	Creating jobStage = 1
	Running  jobStage = 2
	Finished jobStage = 3
	Failed   jobStage = 4
)

const (
	sessionTimeoutMs = 1 * 3600 * 1000
)
const (
	OpenConnError = "open connection error"
	UnknownError  = "unknown error"
)

type NewJobActorIn struct {
	dig.In
	TaskFlowRepo       repository.TaskFlowRepo
	JobRepo            repository.JobRepo
	IdgenSvc           idgen.Service
	CommandParser      parser.CommandParser
	ActorClient        cli.ActorClient
	CommandSetSvc      cmdset.CommandSetExecutor
	C3ConfProvider     c3.ConfigProvider
	ConfProvider       config.ConfigProvider
	DbwInstanceService dbwinstance.DbwInstanceInterface
	Threshold          metrics.ConnectionMetrics
}

func NewJobActor(in NewJobActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.TaskFlowJobActor,
		Producer: types.PersistActorProducerFunc(func(kind string, name string, state []byte) types.IPersistActor {
			return &jobActor{
				state:              newState(state),
				taskFlowRepo:       in.TaskFlowRepo,
				jobRepo:            in.JobRepo,
				cp:                 in.CommandParser,
				commandSetSvc:      in.CommandSetSvc,
				c3ConfProvider:     in.C3ConfProvider,
				tlsClient:          initTLSClient(in.C3ConfProvider),
				actorClient:        in.ActorClient,
				confProvider:       in.ConfProvider,
				dbwInstanceService: in.DbwInstanceService,
				threshold:          in.Threshold,
				idGenSvc:           in.IdgenSvc,
				topicID:            in.ConfProvider.Get(context.Background()).TaskFlowExecuteLogTopic,
			}
		}),
	}
}

type jobActor struct {
	jobRepo            repository.JobRepo
	taskFlowRepo       repository.TaskFlowRepo
	cmds               []*parser.Command
	commandSetSvc      cmdset.CommandSetExecutor
	cp                 parser.CommandParser
	idGenSvc           idgen.Service
	tlsClient          tls.Client
	c3ConfProvider     c3.ConfigProvider
	confProvider       config.ConfigProvider
	actorClient        cli.ActorClient
	dbwInstanceService dbwinstance.DbwInstanceInterface
	threshold          metrics.ConnectionMetrics
	state              *state
	topicID            string
}

type state struct {
	TenantID            string
	TaskFlowID          string
	SessionID           string
	ConnectionID        string
	CommandSetID        string
	CurrentCommandIndex int
	Supervisor          *shared.ActorRef
	JobStage            jobStage
}

func (j *jobActor) Process(ctx types.Context) {
	sysCtx := log.WithTraceTag(context.Background(), fmt.Sprintf("%v/%v", "job", ctx.GetName()))
	if bz := fctx.GetBizContext(sysCtx); bz == nil {
		sysCtx = fctx.SetBizContext(sysCtx)
	}
	if j.state.TenantID != "" {
		fctx.GetBizContext(sysCtx).TenantID = j.state.TenantID
	}
	ctx = types.BuildMyContext(sysCtx, ctx, j.actorClient)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		j.onStarted(ctx)
	case *shared.CreateJob:
		j.createJob(ctx, msg)
	case *actor.ReceiveTimeout:
		j.onReceiveTimeout(ctx)
	}
}

func (j *jobActor) GetState() []byte {
	state, _ := json.Marshal(j.state)
	return state
}

func newState(bytes []byte) *state {
	js := &state{}
	json.Unmarshal(bytes, js)
	return js
}

func (j *jobActor) onStarted(ctx types.Context) {
	if j.state.TaskFlowID == "" {
		if err := j.initTaskFlowID(ctx); err != nil {
			log.Error(ctx, "init taskFlowID error %s", err.Error())
			j.goToDead(ctx)
			return
		}
	}
	job, err := j.jobRepo.Get(ctx, ctx.GetName())
	if err != nil {
		log.Error(ctx, "get job error on started %s", err)
	} else {
		if job.Status == model.JobState_Successed {
			j.finalize(ctx)
			return
		}
	}
	taskFlow, err := j.getTaskFlow(ctx, j.state.TaskFlowID)
	if err != nil {
		log.Warn(ctx, "get task flow %s error %s", j.state.TaskFlowID, err)
		_ = j.markJobFailed(ctx, UnknownError)
		j.putLogs(ctx, UnknownError)
		j.goToDead(ctx)
		return
	}
	ds := &shared.DataSource{
		Type: conv.ToSharedTypeV2(taskFlow.InstanceType),
	}
	j.cmds, err = j.cp.Explain(ctx, ds, taskFlow.Statement, model.SqlExecuteType_Connection)
	if err != nil {
		log.Info(ctx, "split command `%s` fail %v", taskFlow.Statement, err)
		j.cmds = []*parser.Command{parser.NewCommand(taskFlow.Statement, nil)}
	}
	if taskFlow.Config.EnableTransaction {
		j.enableTransaction(ctx)
	}
	if j.state.JobStage == Running || j.state.JobStage == Creating {
		ctx.SetReceiveTimeout(500 * time.Millisecond)
		return
	}
}

func (j *jobActor) initTaskFlowID(ctx types.Context) error {
	job, err := j.jobRepo.Get(ctx, ctx.GetName())
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Info(ctx, "job has been deleted")
			return err
		}
		return err
	}
	j.state.TaskFlowID = job.TaskFlowID
	return nil
}

func (j *jobActor) getTaskFlow(ctx types.Context, taskFlowID string) (*taskflow.TaskFlow, error) {
	taskFlow, err := j.taskFlowRepo.InnerGet(ctx, taskFlowID)
	if err != nil {
		return nil, err
	}
	return taskFlow, nil
}

func (j *jobActor) createJob(ctx types.Context, msg *shared.CreateJob) {
	log.Info(ctx, "job actor received create job message")
	j.setStage(ctx, Creating)
	j.putLogs(ctx, "prepare executing")
	j.state.TenantID = fctx.GetTenantID(ctx)
	j.state.Supervisor = msg.Supervisor
	ctx.SetReceiveTimeout(200 * time.Millisecond)
}

func (j *jobActor) onReceiveTimeout(ctx types.Context) {
	defer ctx.SetReceiveTimeout(2 * time.Second)

	switch j.state.JobStage {
	case Creating:
		j.createSession(ctx)
	case Running:
		j.checkJob(ctx)
	}
}

func (j *jobActor) checkJob(ctx types.Context) {
	taskFlowID := j.state.TaskFlowID
	taskFlow, err := j.getTaskFlow(ctx, taskFlowID)
	if err != nil {
		log.Warn(ctx, "get task flow error %s", taskFlowID)
		return
	}
	cs, err := j.commandSetSvc.DescribeCommandSet(ctx, j.state.CommandSetID)
	if err != nil {
		log.Warn(ctx, "DescribeCommand error %s", err)
		return
	}
	if cs.IsFinished() {
		log.Debug(ctx, "command finished")
		job, err := j.jobRepo.Get(ctx, ctx.GetName())
		if err != nil {
			log.Warn(ctx, "get job error %s", err.Error())
			return
		}
		j.state.CurrentCommandIndex += 1
		job.EndTime = time.Now().Unix()
		if cs.Commands[0].IsFailed() {
			_ = j.markJobFailed(ctx, cs.Commands[0].ReasonDetail)
			j.putLogs(ctx, fmt.Sprintf("execute command %s failed, error %s", cs.Commands[0].Content, cs.Commands[0].ReasonDetail))
			if !taskFlow.Config.IgnoreError {
				if taskFlow.Config.EnableTransaction {
					j.rollback(ctx)
				}
				executeAfter := taskFlow.GetNextExecuteTime(ctx)
				log.Info(ctx, "next execute time after %f", executeAfter.Seconds())
				if executeAfter <= 0 {
					_ = j.markTaskFlowExpired(ctx)
				}
				j.finalize(ctx)
				return
			}
		}
		j.putLogs(ctx, fmt.Sprintf("command `%s` finished", cs.Commands[0].Content))
		if j.hasCommand() {
			if err := j.executeCommand(ctx); err != nil {
				_ = j.markJobFailed(ctx, UnknownError)
				executeAfter := taskFlow.GetNextExecuteTime(ctx)
				log.Info(ctx, "next execute time after %f", executeAfter.Seconds())
				if executeAfter <= 0 {
					_ = j.markTaskFlowExpired(ctx)
				}
				j.finalize(ctx)
			}
			return
		}

		job.EndTime = time.Now().Unix()
		if job.Status != model.JobState_Failed {
			job.Status = model.JobState_Successed
		}
		if err = j.jobRepo.Update(ctx, job); err != nil {
			log.Error(ctx, "update job error %s", err.Error())
		}
		executeAfter := taskFlow.GetNextExecuteTime(ctx)
		log.Info(ctx, "next execute time after %f", executeAfter.Seconds())
		if executeAfter <= 0 {
			_ = j.markTaskFlowExpired(ctx)
		}
		j.finalize(ctx)
		return
	}
}

func (j *jobActor) hasCommand() bool {
	return j.state.CurrentCommandIndex < len(j.cmds)
}

func (j *jobActor) enableTransaction(ctx types.Context) {
	var cmds []*parser.Command
	cmds = append(cmds, parser.NewCommand("BEGIN", nil))
	cmds = append(cmds, j.cmds...)
	cmds = append(cmds, parser.NewCommand("COMMIT", nil))
	j.cmds = cmds
}

func (j *jobActor) createSession(ctx types.Context) {
	var err error
	defer func() {
		if err != nil {
			j.putLogs(ctx, OpenConnError)
			j.state.JobStage = Failed
			_ = j.markJobFailed(ctx, openConnectionError)
			j.finalize(ctx)
		} else {
			if err = j.executeCommand(ctx); err != nil {
				_ = j.markJobFailed(ctx, err.Error())
				j.finalize(ctx)
				return
			}
			j.setStage(ctx, Running)
			ctx.SetReceiveTimeout(200 * time.Millisecond)
		}
	}()
	createFunc := func(_ context.Context) error {
		if j.state.SessionID != "" && j.state.ConnectionID != "" {
			return nil
		}
		job, err := j.jobRepo.Get(ctx, ctx.GetName())
		if err != nil {
			log.Error(ctx, "get job error: %s", err.Error())
			return err
		}
		taskFlow, err := j.taskFlowRepo.InnerGet(ctx, job.TaskFlowID)
		if err != nil {
			log.Error(ctx, "get task flow error: %s", err.Error())
			return err
		}
		j.rewriteUserNamePassword(ctx, taskFlow)
		ds := &shared.DataSource{
			Type:             conv.ToSharedTypeV2(taskFlow.InstanceType),
			LinkType:         conv.ToSharedLinkType(model.LinkType_Volc),
			Db:               taskFlow.Config.Database,
			User:             taskFlow.Config.UserName,
			Password:         taskFlow.Config.Password,
			ConnectTimeoutMs: j.confProvider.Get(ctx).ConnectionConnectTimeout * 1000,
			ReadTimeoutMs:    sessionTimeoutMs,
			WriteTimeoutMs:   j.confProvider.Get(ctx).ConnectionWriteTimeout * 1000,
			InstanceId:       taskFlow.InstanceId,
		}
		if err = j.checkConn(ctx, ds); err != nil {
			return err
		}
		req := &shared.CreateSession{
			Source:                ds,
			SessionTimeoutSeconds: sessionTimeoutMs,
		}
		id, err := j.idGenSvc.NextIDStr(ctx)
		if err != nil {
			// TODO handle error
			return err
		}
		var resp interface{}
		callActorOpts := &cli.GrainCallOptions{}
		resp, err = j.actorClient.KindOf(consts.SessionActorKind).Call(ctx,
			id,
			req,
			callActorOpts.WithRetry(1),
			callActorOpts.WithTimeout(25*time.Second),
		)
		if err != nil {
			log.Warn(ctx, "get session error %s", err)
			return err
		}
		log.Debug(ctx, "session mgr response %s", utils.Show(resp))
		switch rsp := resp.(type) {
		case *shared.SessionCreated:
			if rsp.Code == shared.CreatedOK {
				j.state.SessionID, j.state.ConnectionID = rsp.SessionId, rsp.DefaultConnectionId
				return nil
			}
			log.Warn(ctx, "create session failed %s", rsp.ErrorMessage)
			err = errors.New(rsp.ErrorMessage)
			return err
		default:
			err = errors.New("unknown error")
			return err
		}
	}
	err = utils.Retry(ctx, createFunc, utils.RetryMaxTimes(1, 5*time.Second))
}

func (j *jobActor) checkConn(ctx types.Context, ds *shared.DataSource) error {
	connections, err := j.threshold.GetActiveConnections(ctx, ds)
	if err != nil {
		log.Warn(ctx, "get active connections fail %v", err)
		return err
	}
	log.Info(ctx, "%s current connections %d", ds.InstanceId, len(connections))
	if len(connections) >= int(j.confProvider.Get(ctx).MaxConnectionPerDataSource) {
		return dbwerrors.TooManyConnection
	}
	return nil
}

func (j *jobActor) cleanSession(ctx types.Context) {
	j.closeSession(ctx, j.state.SessionID)
	j.state.SessionID = ""
	j.state.ConnectionID = ""
	j.state.CurrentCommandIndex = 0
	j.state.CommandSetID = ""
}

func (j *jobActor) closeSession(ctx types.Context, sessionId string) {
	if j.state.SessionID != "" {
		ctx.ClientOf(consts.SessionActorKind).Send(ctx, sessionId, &shared.CloseSession{})
	}
}

func (j *jobActor) tellSupervisor(ctx types.Context) {
	if j.state.Supervisor != nil {
		ctx.ClientOf(j.state.Supervisor.Kind).Send(ctx, j.state.Supervisor.Name, &shared.ReportResult{JobID: ctx.GetName()})
	}
}

func (j *jobActor) executeCommand(ctx types.Context) error {
	args := &cmdset.ExecuteCommandSetArgs{
		SessionId:    j.state.SessionID,
		ConnectionId: j.state.ConnectionID,
		Command:      j.cmds[j.state.CurrentCommandIndex].Text(),
	}
	j.putLogs(ctx, fmt.Sprintf("start execute command `%s`", j.cmds[j.state.CurrentCommandIndex].Text()))
	log.Info(ctx, "start execute command %s", j.cmds[j.state.CurrentCommandIndex].Text())
	ret, err := j.commandSetSvc.Execute(ctx, args)
	if err != nil {
		log.Warn(ctx, "execute command error %s", err)
		j.putLogs(ctx, fmt.Sprintf("execute command `%s` failed. reason: %s", j.cmds[j.state.CurrentCommandIndex].Text(), err.Error()))
		return err
	}
	j.state.CommandSetID = ret.CommandSetID
	ctx.SetReceiveTimeout(500 * time.Millisecond)
	return nil
}

func (j *jobActor) rollback(ctx types.Context) {
	args := &cmdset.ExecuteCommandSetArgs{
		SessionId:    j.state.SessionID,
		ConnectionId: j.state.ConnectionID,
		Command:      "ROLLBACK",
	}
	j.putLogs(ctx, fmt.Sprintf("ROLLBACK"))
	_, err := j.commandSetSvc.Execute(ctx, args)
	if err != nil {
		log.Warn(ctx, "rollback error %s", err)
	}
}

func (j *jobActor) markJobFailed(ctx types.Context, reason string) error {
	job := &taskflow.Job{
		ID:         ctx.GetName(),
		TaskFlowID: j.state.TaskFlowID,
		Status:     model.JobState_Failed,
		EndTime:    time.Now().Unix(),
		Reason:     reason,
	}
	if err := j.jobRepo.MarkFailed(ctx, job); err != nil {
		log.Warn(ctx, "update job error %s", err)
		return err
	}
	return nil
}

func (j *jobActor) markTaskFlowExpired(ctx types.Context) error {
	return j.taskFlowRepo.Update(ctx, j.state.TaskFlowID, map[string]interface{}{
		"state": model.State_Expired.String(),
	})
}

func (j *jobActor) rewriteUserNamePassword(ctx types.Context, taskFlow *taskflow.TaskFlow) {
	instance, err := j.dbwInstanceService.GetManagedInstance(ctx, taskFlow.InstanceId, conv.ToSharedTypeV2(taskFlow.InstanceType))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return
		}
		log.Warn(ctx, "get instance error %s", err)
		return
	}
	taskFlow.Config.Password = instance.DatabasePassword
	taskFlow.Config.UserName = instance.DatabaseUser
}

// TODO use atomic
func (j *jobActor) setStage(ctx types.Context, stage jobStage) {
	j.state.JobStage = stage
}

func (j *jobActor) goToDead(ctx types.Context) {
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
}

func (j *jobActor) finalize(ctx types.Context) {
	j.tellSupervisor(ctx)
	j.closeSession(ctx, j.state.SessionID)
	j.goToDead(ctx)
}

func (j *jobActor) putLogs(ctx types.Context, message string) {
	metricLog := &pb.Log{
		Time: time.Now().Unix(),
		Contents: []*pb.LogContent{
			{Key: "TaskFlowID", Value: j.state.TaskFlowID},
			{Key: "JobID", Value: ctx.GetName()},
			{Key: "Message", Value: message},
			{Key: "Timestamp", Value: time.Now().Format("2006-01-02 15:04:05")},
		},
	}
	logs := []*pb.Log{metricLog}
	logGroup := &pb.LogGroup{
		Logs: logs,
	}
	//cnf := f.cnf.Get(ctx)
	request := &tls.PutLogsRequest{
		TopicID: j.topicID,
		LogBody: &pb.LogGroupList{LogGroups: []*pb.LogGroup{logGroup}},
		HashKey: j.hash(ctx.GetName()),
	}
	if _, err := j.tlsClient.PutLogs(request); err != nil {
		log.Warn(ctx, "put logs error, jobId %s error %s", ctx.GetName(), err)
	}
}

func (j *jobActor) hash(key string) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(key)))
}
