package sqlflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"context"
	"fmt"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"os"
)

func initTLSClient(cfg c3.ConfigProvider) tls.Client {
	c3Cfg := cfg.GetNamespace(context.TODO(), consts.C3ApplicationNamespace)
	Ak := c3Cfg.TLSServiceAccessKey
	Sk := c3Cfg.TLSServiceSecretKey
	tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, os.Getenv(consts.REGION_ID))
	return tls.NewClient(tlsEndpoint, Ak, Sk, "", os.Getenv(consts.REGION_ID))
}
