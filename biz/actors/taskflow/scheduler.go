package sqlflow

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"go.uber.org/dig"
	"gorm.io/gorm"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity/taskflow"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/cmdset"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/parser"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

const (
	maxRetries          = 3
	openConnectionError = "open connection error"
	internalError       = "internal error"
	invalidTime         = "invalid time"
)

type NewTaskFlowScheduleActorIn struct {
	dig.In
	TaskFlowRepo   repository.TaskFlowRepo
	JobRepo        repository.JobRepo
	IdgenSvc       idgen.Service
	CommandParser  parser.CommandParser
	ActorClient    cli.ActorClient
	CommandSetSvc  cmdset.CommandSetExecutor
	C3ConfProvider c3.ConfigProvider
	ConfProvider   config.ConfigProvider
}

func NewTaskFlowScheduleActor(in NewTaskFlowScheduleActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.TaskFlowSchedulerActor,
		Producer: types.PersistActorProducerFunc(func(kind string, name string, state []byte) types.IPersistActor {
			return &ScheduleActor{
				state:          newSchedulerState(state),
				taskFlowRepo:   in.TaskFlowRepo,
				jobRepo:        in.JobRepo,
				idgenSvc:       in.IdgenSvc,
				cp:             in.CommandParser,
				actorClient:    in.ActorClient,
				commandSetSvc:  in.CommandSetSvc,
				c3ConfProvider: in.C3ConfProvider,
				tlsClient:      initTLSClient(in.C3ConfProvider),
				confProvider:   in.ConfProvider,
				topicID:        in.ConfProvider.Get(context.Background()).TaskFlowExecuteLogTopic,
			}
		}),
	}
}

type ScheduleActor struct {
	state          *schedulerState
	cmds           []*parser.Command
	taskFlowRepo   repository.TaskFlowRepo
	jobRepo        repository.JobRepo
	commandSetSvc  cmdset.CommandSetExecutor
	idgenSvc       idgen.Service
	cp             parser.CommandParser
	actorClient    cli.ActorClient
	c3ConfProvider c3.ConfigProvider
	tlsClient      tls.Client
	confProvider   config.ConfigProvider
	topicID        string
}

type schedulerState struct {
	TenantID       string
	ScheduleMisses int64
}

func (ea *ScheduleActor) Process(ctx types.Context) {
	// TODO build context
	sysCtx := log.WithTraceTag(context.Background(), fmt.Sprintf("%v/%v", "taskflow", ctx.GetName()))
	if bz := fctx.GetBizContext(sysCtx); bz == nil {
		sysCtx = fctx.SetBizContext(sysCtx)
	}
	if ea.state.TenantID != "" {
		fctx.GetBizContext(sysCtx).TenantID = ea.state.TenantID
	}
	ctx = types.BuildMyContext(sysCtx, ctx, ea.actorClient)
	log.Debug(ctx, "current tenant id %s", fctx.GetTenantID(ctx))
	switch ctx.Message().(type) {
	case *actor.Started:
		ea.onStarted(ctx)
	case *shared.CreateTaskFlow:
		ea.createTaskFlow(ctx)
	case *shared.CancelTaskFlow:
		ea.cancelTaskFlow(ctx)
	case *shared.PauseTaskFlow:
		ea.goToSleep(ctx)
	case *shared.WakeUpTaskFlow:
		ea.wakeUp(ctx)
	case *shared.ReportResult:
		ea.handleReport(ctx)
	case *shared.UpdateTaskFlow:
		log.Info(ctx, "received update taskflow message")
		ea.handleUpdate(ctx)
	case *actor.ReceiveTimeout:
		ea.schedule(ctx)
	}
}
func (ea *ScheduleActor) GetState() []byte {
	state, _ := json.Marshal(ea.state)
	return state
}

func newSchedulerState(bytes []byte) *schedulerState {
	ts := &schedulerState{}
	json.Unmarshal(bytes, ts)
	return ts
}

func (ea *ScheduleActor) onStarted(ctx types.Context) {
	log.Info(ctx, "on started")
	taskFlowID := ctx.GetName()
	taskFlow, err := ea.taskFlowRepo.InnerGet(ctx, taskFlowID)
	if err != nil {
		log.Error(ctx, "get taskflow error %s", err.Error())
		if err == gorm.ErrRecordNotFound {
			ea.goToDead(ctx)
		}
		return
	}
	if taskFlow.IsExpired() {
		log.Debug(ctx, "mark expired")
		_ = ea.markTaskFlowExpired(ctx)
		ea.goToDead(ctx)
		return
	}
	if taskFlow.State == model.State_Disabled {
		return
	}
	executeAfter := taskFlow.GetNextExecuteTime(ctx)
	if executeAfter <= 0 {
		_ = ea.markTaskFlowExpired(ctx)
		ea.goToDead(ctx)
		return
	}
	log.Info(ctx, "task will execute after %s seconds", executeAfter.Seconds())
	ctx.SetReceiveTimeout(executeAfter)
}

func (ea *ScheduleActor) createTaskFlow(ctx types.Context) {
	ea.state.TenantID = fctx.GetTenantID(ctx)
	taskFlowID := ctx.GetName()
	taskFlow, err := ea.taskFlowRepo.Get(ctx, taskFlowID)
	if err != nil {
		log.Warn(ctx, "release task flow %s, because of error %s", taskFlowID, err)
		ctx.Respond(&shared.CreateTaskFlowFailed{
			Reason: err.Error(),
		})
		ea.goToDead(ctx)
		return
	}
	after := taskFlow.GetNextExecuteTime(ctx)
	log.Info(ctx, "task will execute after %f seconds", after.Seconds())
	if after <= 0 {
		_ = ea.markTaskFlowExpired(ctx)
		ctx.Respond(&shared.CreateTaskFlowFailed{
			Reason: invalidTime,
		})
		ea.goToDead(ctx)
		return
	}
	ctx.SetReceiveTimeout(after)
	ctx.Respond(&shared.CreateTaskFlowAccepted{})
}

// build the jobs. If the DAG is utilized in the future, construct the dependency relationships here.
func (ea *ScheduleActor) schedule(ctx types.Context) {
	log.Info(ctx, "start executing %s", ctx.GetName())
	var err error
	var taskFlow *taskflow.TaskFlow
	var jobCreated bool
	taskFlow, err = ea.taskFlowRepo.Get(ctx, ctx.GetName())
	if err != nil {
		log.Error(ctx, "get task flow error %s", err)
		return
	}
	defer func() {
		if err != nil {
			log.Error(ctx, "taskflow %s create job error, err: %s", ctx.GetName(), err)
		}
		executeAfter := taskFlow.GetNextExecuteTime(ctx)
		log.Info(ctx, "task will execute after %f seconds", executeAfter.Seconds())
		//
		if executeAfter <= 0 {
			log.Info(ctx, "task flow expired, go to dead")
			// if job created, mark expired will at job finished or failed
			if !jobCreated {
				_ = ea.markTaskFlowExpired(ctx)
			}
			ea.goToDead(ctx)
		} else {
			ctx.SetReceiveTimeout(executeAfter)
		}
	}()
	canSchedule, err := ea.canSchedule(ctx, taskFlow)
	if err != nil {
		// TODO handle
	}
	if !canSchedule {
		return
	}
	if err := ea.createJob(ctx); err != nil {
		// alarm
		log.Error(ctx, "create job error %s", err)
		return
	}
	jobCreated = true
}

func (ea *ScheduleActor) canSchedule(ctx types.Context, taskFlow *taskflow.TaskFlow) (bool, error) {
	hasRunningJob, total, err := ea.hasRunningJob(ctx)
	if err != nil {
		return false, err
	}
	if hasRunningJob {
		if total >= int32(ea.confProvider.Get(ctx).MaxParallelJobsPerInstance) {
			log.Error(ctx, "task flow %s too many running jobs, skip", ctx.GetName())
			return false, nil
		}
		if taskFlow.Config.StillRunningPolicy == model.StillRunningPolicy_Continue {
			return true, nil
		}
		if taskFlow.Config.StillRunningPolicy == model.StillRunningPolicy_Delay {
			ea.state.ScheduleMisses += 1
		}
		return false, nil
	}
	return true, nil
}

func (ea *ScheduleActor) hasRunningJob(ctx types.Context) (bool, int32, error) {
	filter := &model.DescribeTaskExecuteRecordsReq{
		PageSize: utils.Int32Ref(20),
		Filter: &model.JobFilter{
			JobState: model.JobStatePtr(model.JobState_Executing),
		},
	}
	_, total, err := ea.jobRepo.List(ctx, ctx.GetName(), repository.NewJobsFilterOption(filter))
	if err != nil {
		return false, total, err
	}
	return total > 0, total, nil
}

func (ea *ScheduleActor) createJob(ctx types.Context) error {
	id, err := ea.idgenSvc.NextID(ctx)
	if err != nil {
		log.Error(ctx, "generate id error %s", err.Error())
		return err
	}
	job := taskflow.NewJob(
		strconv.FormatInt(id, 10),
		ctx.GetName(),
		taskflow.WithStartTime(time.Now().Unix()),
		taskflow.WithStatus(model.JobState_Executing))
	if err := ea.jobRepo.Create(ctx, job); err != nil {
		log.Warn(ctx, "create job model error %s", err.Error())
		return err
	}
	if err = ctx.ClientOf(consts.TaskFlowJobActor).Send(ctx,
		strconv.FormatInt(id, 10),
		&shared.CreateJob{Supervisor: &shared.ActorRef{Kind: consts.TaskFlowSchedulerActor, Name: ctx.GetName()}}); err != nil {
		log.Error(ctx, "create job error %s", err)
		job.Status = model.JobState_Failed
		job.Reason = internalError
		job.EndTime = job.StartTime
		_ = ea.jobRepo.MarkFailed(ctx, job)
		return err
	}
	return nil
}

func (ea *ScheduleActor) handleReport(ctx types.Context) {
	if ea.state.ScheduleMisses > 0 {
		ea.state.ScheduleMisses -= 1
	}
	taskFlow, err := ea.taskFlowRepo.Get(ctx, ctx.GetName())
	if err != nil {
		log.Error(ctx, "get task flow error %s", err)
		return
	}
	if taskFlow.State == model.State_Disabled {
		ea.state.ScheduleMisses = 0
		ea.goToSleep(ctx)
		return
	}
	var i int64
	// retry misses times
	for i = 0; i < ea.state.ScheduleMisses; i++ {
		if err := ea.createJob(ctx); err == nil {
			break
		} else {
			log.Error(ctx, "create job error %s", err)
		}
	}
	executeAfter := taskFlow.GetNextExecuteTime(ctx)
	log.Info(ctx, "task will execute after %f seconds", executeAfter.Seconds())
	//
	if executeAfter <= 0 {
		log.Info(ctx, "task flow expired, go to dead")
		// if job created, mark expired will at job finished or failed
		_ = ea.markTaskFlowExpired(ctx)
		ea.goToDead(ctx)
		return
	}
	ctx.SetReceiveTimeout(executeAfter)
}

func (ea *ScheduleActor) handleUpdate(ctx types.Context) {
	taskFlow, err := ea.taskFlowRepo.Get(ctx, ctx.GetName())
	if err != nil {
		log.Error(ctx, "get task flow error %s", err)
		ctx.Respond(&shared.TaskFlowUpdatedFailed{
			Reason: err.Error(),
		})
		return
	}
	executeAfter := taskFlow.GetNextExecuteTime(ctx)
	if executeAfter <= 0 {
		_ = ea.markTaskFlowExpired(ctx)
		ea.goToDead(ctx)
		ctx.Respond(&shared.TaskFlowUpdated{})
		return
	}
	log.Info(ctx, "task updated success, will execute after %s seconds", executeAfter.Seconds())
	ctx.SetReceiveTimeout(executeAfter)
	ctx.Respond(&shared.TaskFlowUpdated{})
}

func (ea *ScheduleActor) goToDead(ctx types.Context) {
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	log.Info(ctx, "task flow canceled")
}

func (ea *ScheduleActor) goToSleep(ctx types.Context) {
	ea.state.ScheduleMisses = 0
	ctx.Send(ctx.Self(), &proto.SleepMessage{})
}

func (ea *ScheduleActor) cancelTaskFlow(ctx types.Context) {
	ea.goToDead(ctx)
	if err := ea.taskFlowRepo.Delete(ctx, ctx.GetName()); err != nil {
		log.Warn(ctx, "delete task flow error %s", err)
		if err == gorm.ErrRecordNotFound {
			return
		}
		log.Error(ctx, "delete task flow error %s", err)
		return
	}
	ctx.Respond(shared.TaskFlowCanceled{})
}

func (ea *ScheduleActor) markTaskFlowExpired(ctx types.Context) error {
	return ea.taskFlowRepo.MarkExpired(ctx, ctx.GetName())
}

func (ea *ScheduleActor) wakeUp(ctx types.Context) {
	taskFlow, err := ea.taskFlowRepo.Get(ctx, ctx.GetName())
	if err != nil {
		log.Warn(ctx, "get taskflow error %s", ctx.GetName())
		return
	}
	executeAfter := taskFlow.GetNextExecuteTime(ctx)
	log.Info(ctx, "task flow wakeup , will execute after  %s seconds", executeAfter.Seconds())
	if executeAfter <= 0 {
		ea.markTaskFlowExpired(ctx)
		ea.goToDead(ctx)
		log.Info(ctx, "mark task flow expired")
		return
	}
	ctx.SetReceiveTimeout(executeAfter)
}
