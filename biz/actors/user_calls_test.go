package actors

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/ds-lib/common/log"
	"encoding/json"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/go-playground/assert"
	"testing"
)

func TestGetDescribeTableSpaceReq(t *testing.T) {
	actor := &SessionActor{}
	msg := &shared.DescribeTableSpace{
		Product:    0,
		InstanceId: "instance",
		RegionId:   "region",
		PageNumber: 0,
		PageSize:   0,
		OrderItem:  "",
		OrderRule:  "",
	}
	jsonBytes, _ := json.Marshal(msg)
	fmt.Println(string(jsonBytes))
	actor.state = &sessionState{}
	actor.state.DataSource = &shared.DataSource{}
	req := actor.getDescribeTableSpaceReq(msg)
	assert.Equal(t, req.Limit, int32(10))
	assert.Equal(t, req.Offset, int32(0))
	assert.Equal(t, req.OrderItem, "TableSpace")
	assert.Equal(t, req.OrderRule, "ASC")
}

func TestGetTableSpaceReqLimit(t *testing.T) {
	actor := &SessionActor{}
	limit := actor.getTableSpaceReqLimit(50, 20)
	assert.Equal(t, limit, int32(20))
	limit = actor.getTableSpaceReqLimit(41, 50)
	assert.Equal(t, limit, int32(0))
	limit = actor.getTableSpaceReqLimit(34, 60)
	assert.Equal(t, limit, int32(20))
}

func TestListSchemaTables(t *testing.T) {
	actor := &SessionActor{
		state:   &sessionState{DataSource: &shared.DataSource{}},
		sources: &mocks.MockDataSourceService{},
	}
	msg := &shared.ListSchemaTables{
		Keyword: "",
		Schema:  "",
		Offset:  0,
		Limit:   0,
		Filters: nil,
	}
	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*mocks.MockDataSourceService).ListSchemaTables).Return(nil, fmt.Errorf("error")).Build()
	actor.listSchemaTables(ctx, msg)
	baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockDataSourceService).ListSchemaTables).Return(&datasource.ListSchemaTablesResp{}, nil).Build()
	actor.listSchemaTables(ctx, msg)
	baseMock4.UnPatch()
}

func TestSessionActor_listAllTables(t *testing.T) {
	actor := &SessionActor{
		state:   &sessionState{DataSource: &shared.DataSource{}},
		sources: &mocks.MockDataSourceService{},
	}
	msg := &shared.ListAllTable{
		Keyword: "",
		Schema:  "",
		Offset:  0,
		Limit:   0,
		Filters: nil,
	}
	ctx := &mocks.MockContext{}
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*mocks.MockDataSourceService).ListAllTables).Return(nil, fmt.Errorf("error")).Build()
	actor.listAllTables(ctx, msg)
	baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockDataSourceService).ListAllTables).Return(&datasource.ListTablesResp{}, nil).Build()
	actor.listAllTables(ctx, msg)
	baseMock4.UnPatch()
}
