package billing

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls/tls_enhance"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/billing"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/ds-lib/common/log"
	"go.uber.org/dig"
)

type NewPushBillingIn struct {
	dig.In
	AccDAL       dal.AccountantDAL
	ExtraTlsDAL  dal.InstanceExtraTlsDAL
	TlsDAL       dal.TlsDAL
	BillSvc      billing.BillingService
	AuditService audit.SqlAuditService
	InstanceDAL  dal.ObInstDAL
}
type PushBilling interface {
	Process(ctx context.Context, accountantTime string) error
}

func NewPushBilling(in NewPushBillingIn) PushBilling {
	return &logAuditBilling{
		billSvc:      in.BillSvc,
		auditService: in.AuditService,
		accDAL:       in.AccDAL,
		extraTlsDAL:  in.ExtraTlsDAL,
		tlsDAL:       in.TlsDAL,
		instanceDAL:  in.InstanceDAL,
	}
}

type logAuditBilling struct {
	accDAL       dal.AccountantDAL
	billSvc      billing.BillingService
	tlsDAL       dal.TlsDAL
	auditService audit.SqlAuditService
	extraTlsDAL  dal.InstanceExtraTlsDAL
	instanceDAL  dal.ObInstDAL
}

func (a *logAuditBilling) Process(ctx context.Context, accountantTime string) error {
	log.Info(ctx, "exec region: %s, msg: %s", accountantTime)
	a.PushMeasure(ctx, accountantTime, model.LogProductType_AuditLog, model.DSType_Redis)
	a.PushMeasure(ctx, accountantTime, model.LogProductType_AuditLog, model.DSType_MySQL)
	a.PushMeasure(ctx, accountantTime, model.LogProductType_AuditLog, model.DSType_VeDBMySQL)
	a.PushMeasure(ctx, accountantTime, model.LogProductType_AuditLog, model.DSType_Postgres)
	a.PushMeasure(ctx, accountantTime, model.LogProductType_AuditLog, model.DSType_Mongo)
	a.PushMeasure(ctx, accountantTime, model.LogProductType_FullSqlLog, model.DSType_MySQL)
	a.PushMeasure(ctx, accountantTime, model.LogProductType_FullSqlLog, model.DSType_VeDBMySQL)
	// meta mysql计费项与mysql相同，所以不单独处理
	//a.PushMeasure(ctx, accountantTime, model.LogProductType_FullSqlLog, model.DSType_MetaMySQL)

	return nil
}

func (a *logAuditBilling) PushMeasure(ctx context.Context, accountantTime string, logProductType model.LogProductType, dsType model.DSType) error {
	chargeItems := GetChargeItemCode(logProductType, dsType)
	a.pushRawMeasure(ctx, accountantTime, logProductType, chargeItems)
	return nil
}

func (a *logAuditBilling) pushRawMeasure(ctx context.Context, accountantTime string, logProductType model.LogProductType, chargeItem []model.ChargeItem) error {
	accTime, _ := strconv.ParseInt(accountantTime, 10, 64)
	cTime := time.Unix(accTime, 0)
	startTime := cTime.Add(-1 * time.Hour).Unix()
	log.Info(ctx, "GetInstanceTopics, status: 0, accountantTime: %d, productType:%s, chargeItem:%s", accTime, logProductType, chargeItem)
	instTopicList, err := a.accDAL.GetInstanceTopicsWithoutTenant(ctx, int64(model.AuditInstanceAccountantStatus_INIT), accTime, logProductType.String(), chargeItem[0].String())
	if err != nil {
		log.Warn(ctx, "GetByTenantID error %s", err)
		return err
	}
	log.Info(ctx, "PushAuditRawMeasure, productType:%s, chargeItem:%s", logProductType, chargeItem)
	// 拉取tls使用量
	for _, instanceTopic := range instTopicList {
		log.Info(ctx, "instanceTopic:%s", libutils.Show(instanceTopic))
		if instanceTopic.TlsTopicID == "" {
			log.Error(ctx, "实例topic不存在, instance: %s", utils.JSONStr(instanceTopic))
			continue
		}
		instanceInfo, err := a.instanceDAL.GetByIDWithoutTenant(ctx, instanceTopic.InstanceID)
		if err != nil {
			log.Error(ctx, "GetByIDWithoutTenant error: %s", err)
			continue
		}
		enableFunc, err := full_sql.ConvEnableFunc(instanceInfo.EnableFunctions)
		if err != nil {
			log.Error(ctx, "ConvEnableFunc error: %s", err)
			return consts.ErrorOf(model.ErrorCode_ConvertTypeError)
		}
		extraTls, err := a.extraTlsDAL.Get(ctx, instanceTopic.InstanceID)
		if err != nil {
			log.Error(ctx, "get extraTls error: %s", err)
			continue
		}
		var instTopicIdList []string
		instTopicIdList = append(instTopicIdList, instanceTopic.TlsTopicID)
		for _, et := range extraTls {
			get, err := a.tlsDAL.Get(ctx, et.TlsId, instanceTopic.TlsTenantID)
			if err != nil {
				log.Error(ctx, "get extraTls error: %s", err)
				continue
			}
			instTopicIdList = append(instTopicIdList, get.TlsTopicId)
		}

		pollReq := &tls_enhance.PollTlsUsageRequest{
			TopicIDList: instTopicIdList,
			Ctime:       cTime.Format(consts.AccountTimeFormat),
		}
		tlsClient, err := a.auditService.GetTlsClient(ctx, instanceTopic.TlsTenantID)
		if err != nil {
			log.Warn(ctx, "GetTlsClient error:%s", err)
			continue
		}
		log.Info(ctx, "PollTlsUsageRequest req: %+v", pollReq)
		resp, err := tls_enhance.PollTlsUsage(tlsClient, pollReq)
		if err != nil {
			log.Warn(ctx, "PushAuditRawMeasure PollTlsUsage error: %s, wait to retry", err)
			// AccessDenied用户授权失败，可能是由于欠费导致
			if strings.Contains(err.Error(), "UsageNotProgressed") {
				log.Info(ctx, "%s req:%+v, wait few second", err, pollReq)
				// 此租户推量等待下一次调度
				break
			}

			if strings.Contains(err.Error(), "ExceedQPSLimit") {
				log.Info(ctx, "%s req:%+v, wait few second", err, pollReq)
				continue
			}

			if strings.Contains(err.Error(), "AccessDenied") {
				log.Error(ctx, "%s req:%+v", err, pollReq)
				// 此租户推量等待下一次调度
				break
			}

			if strings.Contains(err.Error(), "InvalidArgument") ||
				strings.Contains(err.Error(), "InternalServerError") {
				// 系统性问题
				log.Error(ctx, "%s req:%+v", err, pollReq)
				break
			}
			if strings.Contains(err.Error(), "TopicNotExist") {
				log.Error(ctx, "实例topic不存在：%s req:%+v; instance:%+v", err, pollReq, instanceTopic)
				// pass
			}
			continue
		}
		log.Info(ctx, "PushAuditRawMeasure resp: %+v", resp)
		log.Info(ctx, "%s %+v", instanceTopic.TlsTopicID, resp.UsageMap)
		usage := make(map[model.ChargeItem]float64)
		for instTopic, topicUsageInfo := range resp.UsageMap {
			for _, item := range chargeItem {
				if ChargeItemNotEnabled(item, instanceInfo.ProductType, enableFunc) {
					log.Info(ctx, "instance:%s, Topic: %s, chargeItem:%s, not enabled", instanceTopic.InstanceID, instTopic, item.String())
					usage[item] = 0
					continue
				}
				use := GetUsage(ctx, item, topicUsageInfo)
				log.Info(ctx, "instance:%s, Topic: %s, chargeItem:%s, use:%d", instanceTopic.InstanceID, instTopic, item.String(), use)
				if _, ok := usage[item]; !ok {
					usage[item] = float64(use)
				} else {
					usage[item] = usage[item] + float64(use)
				}
			}

		}
		for item, totalUsage := range usage {
			log.Info(ctx, "instance:%s, chargeItem:%s, use:%f", instanceTopic.InstanceID, item.String(), usage)
			usageGB := float64(totalUsage) / (1024 * 1024 * 1024)
			err = a.billSvc.PushAuditRawMeasure(ctx, instanceTopic.InstanceID, startTime, accTime, usageGB, item, instanceTopic.InstanceTenantId)
			if err != nil {
				log.Warn(ctx, "a.billSvc.PushAuditRawMeasure %s", err)
				continue
			}
			err = a.accDAL.Update(context.Background(), &dao.AuditAccountant{
				InstanceID:     instanceTopic.InstanceID,
				AccountantTime: accTime,
				ProductType:    logProductType.String(),
				ChargeItemCode: item.String(),
				TlsUsage:       int64(totalUsage),
				Status:         int32(model.AuditInstanceAccountantStatus_PUSHED),
			})
			if err != nil {
				log.Warn(ctx, "err: %s", err)
				break
			}
		}

	}

	return nil
}

func ChargeItemNotEnabled(item model.ChargeItem, productType string, enableFunc map[model.FullSqlFuncName]bool) bool {
	if productType != model.LogProductType_FullSqlLog.String() {
		return false
	}
	if v, ok := enableFunc[model.FullSqlFuncName_SQLInsight]; !ok || !v {
		switch item {
		case model.ChargeItem_mysql_aggregation_sql_insight:
			return true
		case model.ChargeItem_vedb_aggregation_sql_insight:
			return true
		default:
			return false
		}
	}
	return false
}

func GetUsage(ctx context.Context, chargeItem model.ChargeItem, topicUsageInfo tls_enhance.TopicUsageInfo) int64 {
	var use int64
	switch chargeItem {
	case model.ChargeItem_log_storage:
		use = topicUsageInfo.IndexTotalUsage + topicUsageInfo.StorageTotalUsage
	case model.ChargeItem_log_collect:
		use = topicUsageInfo.StorageHourIncrement

	case model.ChargeItem_redis_log_storage_audit:
		use = topicUsageInfo.StorageTotalUsage
	case model.ChargeItem_redis_index_storage_audit:
		use = topicUsageInfo.IndexTotalUsage
	case model.ChargeItem_redis_log_collect_audit:
		use = topicUsageInfo.StorageHourIncrement
	case model.ChargeItem_redis_index_traffic_audit:
		use = topicUsageInfo.IndexHourIncrement

	case model.ChargeItem_mysql_log_storage_sql_insight:
		use = topicUsageInfo.StorageTotalUsage
	case model.ChargeItem_mysql_index_storage_sql_insight:
		use = topicUsageInfo.IndexTotalUsage
	case model.ChargeItem_mysql_log_collect_sql_insight:
		use = topicUsageInfo.StorageHourIncrement
	case model.ChargeItem_mysql_index_traffic_sql_insight:
		use = topicUsageInfo.IndexHourIncrement
	case model.ChargeItem_mysql_aggregation_sql_insight:
		use = topicUsageInfo.StorageHourIncrement

	case model.ChargeItem_vedb_log_storage_sql_insight:
		use = topicUsageInfo.StorageTotalUsage
	case model.ChargeItem_vedb_index_storage_sql_insight:
		use = topicUsageInfo.IndexTotalUsage
	case model.ChargeItem_vedb_log_collect_sql_insight:
		use = topicUsageInfo.StorageHourIncrement
	case model.ChargeItem_vedb_index_traffic_sql_insight:
		use = topicUsageInfo.IndexHourIncrement
	case model.ChargeItem_vedb_aggregation_sql_insight:
		use = topicUsageInfo.StorageHourIncrement

	case model.ChargeItem_mongo_log_storage_audit:
		use = topicUsageInfo.StorageTotalUsage
	case model.ChargeItem_mongo_index_storage_audit:
		use = topicUsageInfo.IndexTotalUsage
	case model.ChargeItem_mongo_log_collect_audit:
		use = topicUsageInfo.StorageHourIncrement
	case model.ChargeItem_mongo_index_traffic_audit:
		use = topicUsageInfo.IndexHourIncrement
	default:
		log.Error(ctx, "unknown chargeItem: %s", chargeItem.String())
		use = 0
	}
	return use
}
