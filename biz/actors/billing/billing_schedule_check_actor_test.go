package billing

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	mock_dal "code.byted.org/infcs/dbw-mgr/biz/test/mocks/dal"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/bill"

	"code.byted.org/videoarch/cloud-volc_sdk_go/tradeapi"

	ycnf "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	bill_svc "code.byted.org/infcs/dbw-mgr/biz/service/billing"
	conf_svc "code.byted.org/infcs/dbw-mgr/biz/service/config"
	local_dal "code.byted.org/infcs/dbw-mgr/biz/test/dal"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	mocks_audit "code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/audit"
	rdsModel "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model"
	rdsModel_v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model/v2"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	volctrade "code.byted.org/infcs/lib-mgr-common/volc/trade"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"

	. "github.com/bytedance/mockey"
)

type BillingScheduleCheckActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *BillingScheduleCheckActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}
func (suite *BillingScheduleCheckActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}
func TestBillingScheduleCheckActorSuite(t *testing.T) {
	suite.Run(t, new(BillingScheduleCheckActorSuite))
}

func (suite *BillingScheduleCheckActorSuite) TestBillingScheduleCheckActorCheckReportTask() {
	type fields struct {
		state                      map[string]string
		checkCnt                   int64
		auditReportMeasure         int64
		auditReportMeasureCallback int64
		billSvc                    billing.BillingService
		accDAL                     dal.AuditAccountantDAL
		accGenDAL                  dal.AccountantGenDAL
		auditTlsDAL                dal.AuditTlsDAL
		actorClient                cli.ActorClient
		conf                       conf_svc.ConfigProvider
	}
	type args struct {
		ctx context.Context
	}
	PatchConvey("", suite.T(), func() {
		ctx := context.Background()
		ctx, cfg, c3Cnf, local, _, publishEventSvc := suite.MockBillNeed()
		//billSvc := billing.NewBillingService(cfg, c3Cnf, local.AuditDAL, publishEventSvc, local.DbProvider, local.AccDAL, local.EventDAL)
		billSvc := bill_svc.NewBillingService(bill_svc.BillingServiceIn{
			In:              dig.In{},
			Cfg:             cfg,
			C3Cnf:           c3Cnf,
			PublishEventSvc: publishEventSvc,
			DbProvider:      local.DbProvider,
			AccDAL:          local.AccountDAL,
			EventDAL:        local.EventDAL,
			AccGenDAL:       local.AccountGentDAL,
		})
		actorCli := mocks.NewMockActorClient(suite.ctrl)
		kc := mocks.NewMockKindClient(suite.ctrl)
		kc.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		actorCli.EXPECT().KindOf(gomock.Any()).Return(kc).AnyTimes()

		tests := []struct {
			name   string
			fields fields
			args   args
		}{
			{
				name: "a",
				fields: fields{
					state:                      nil,
					checkCnt:                   0,
					auditReportMeasure:         0,
					auditReportMeasureCallback: 0,
					billSvc:                    billSvc,
					accDAL:                     local.AccDAL,
					accGenDAL:                  local.AccountGentDAL,
					auditTlsDAL:                local.AuditDAL,
					actorClient:                actorCli,
					conf:                       cfg,
				},
				args: args{
					ctx: ctx,
				},
			},
		}
		for _, tt := range tests {
			suite.T().Run(tt.name, func(t *testing.T) {
				b := BillingScheduleCheckActor{
					state:                      tt.fields.state,
					checkCnt:                   tt.fields.checkCnt,
					auditReportMeasure:         tt.fields.auditReportMeasure,
					auditReportMeasureCallback: tt.fields.auditReportMeasureCallback,
					billSvc:                    tt.fields.billSvc,
					actDAL:                     local.AccountDAL,
					fullSqlDAL:                 local.FullSqlDAL,
					accGenDAL:                  tt.fields.accGenDAL,
					auditTlsDAL:                tt.fields.auditTlsDAL,
					conf:                       tt.fields.conf,
				}
				b.checkReportTask(tt.args.ctx)

				cfg := config.NewMockConfigProvider(suite.ctrl)
				// boe-stable
				cfg.EXPECT().Get(gomock.Any()).Return(&ycnf.Config{
					VolcProfile:                "boe_stable",
					BillingAK:                  "MDgyYjM0NWRiYWY",
					BillingSK:                  "GMxZjk3MTczZTMxNGYzlhODg3NmRk",
					VolcServiceAccountAK:       "AKLTY2RmNGQ4NjhmZmJlNGI5YzhkZTM0ZjQyOWY0MTlmOTI",
					VolcServiceAccountSK:       "TURReU9UY3hOR05tTnpJME5HWmhZVGt4WVRZek5tUTVaRFprTm1RMk1EQQ==",
					BillingTopic:               "trade_instance",
					BillingCluster:             "rmq_test_new",
					BillingConsumerGroup:       "dbw_cn-nanjing-bbit",
					EnableBillingTenantIDList:  []string{"**********", "**********"},
					DisableBillingTenantIDList: []string{},
					EnableBillingScheduleCheck: false,
				}).AnyTimes()
				b = BillingScheduleCheckActor{
					state:                      tt.fields.state,
					checkCnt:                   tt.fields.checkCnt,
					auditReportMeasure:         tt.fields.auditReportMeasure,
					auditReportMeasureCallback: tt.fields.auditReportMeasureCallback,
					billSvc:                    tt.fields.billSvc,
					actDAL:                     local.AccountDAL,
					fullSqlDAL:                 local.FullSqlDAL,
					accGenDAL:                  tt.fields.accGenDAL,
					auditTlsDAL:                tt.fields.auditTlsDAL,
					conf:                       cfg,
				}
				b.checkReportTask(tt.args.ctx)
			})
		}
	})
}

func (suite *BillingScheduleCheckActorSuite) MockBillNeed() (
	context.Context,
	*config.MockConfigProvider,
	*mocks.MockC3ConfigProvider,
	*local_dal.LocalAudit,
	*mocks_audit.MockSqlAuditService,
	*mocks.MockPublishEventService,
) {
	// CONSUL_HTTP_HOST=************;CONSUL_HTTP_PORT=2280;RUNTIME_IDC_NAME=boe;SEC_TOKEN_STRING=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************.MqaypODNXw4CfSWQ1S5rLkR6R7LFqL82DpxA1Jixrl-BFZ8qn9L4zKX7ayDNDb37KlnRE1_REy6pZRQMqAZmUl47A-tAxbk_L2Vgzgzh876v6g_bygsnAyFzJEfhcn2DoucHizSB-oF0G8uOFOBsj2ARJfx6J8Y4ZKCjMg8BKKKq3rg68dSLkyeG2kz7VMIO5DVEj26P4MMHN3PCshySKOT5gUpZwpY62G8G1Q-OxOcYPvAFGpDijy2WQ4HXwJHYCp1nrrEkIY3YYLz5IBWHyW5BOLy-QMTPgA_SMysGkjGcflyyZ5DeZgLRpu80f78vnRPmH34tCwWx82CGI4FrfA
	os.Setenv("CONSUL_HTTP_HOST", "************")
	os.Setenv("CONSUL_HTTP_PORT", "2280")
	os.Setenv("RUNTIME_IDC_NAME", "boe-stable")
	os.Setenv("SEC_TOKEN_STRING", "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************.MqaypODNXw4CfSWQ1S5rLkR6R7LFqL82DpxA1Jixrl-BFZ8qn9L4zKX7ayDNDb37KlnRE1_REy6pZRQMqAZmUl47A-tAxbk_L2Vgzgzh876v6g_bygsnAyFzJEfhcn2DoucHizSB-oF0G8uOFOBsj2ARJfx6J8Y4ZKCjMg8BKKKq3rg68dSLkyeG2kz7VMIO5DVEj26P4MMHN3PCshySKOT5gUpZwpY62G8G1Q-OxOcYPvAFGpDijy2WQ4HXwJHYCp1nrrEkIY3YYLz5IBWHyW5BOLy-QMTPgA_SMysGkjGcflyyZ5DeZgLRpu80f78vnRPmH34tCwWx82CGI4FrfA")
	os.Setenv("BDC_REGION_ID", "cn-nanjing-bbit")
	ctx := context.TODO()
	ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
	cfg := config.NewMockConfigProvider(suite.ctrl)
	// boe-stable
	cfg.EXPECT().Get(gomock.Any()).Return(&ycnf.Config{
		VolcProfile:                "boe_stable",
		BillingAK:                  "MDgyYjM0NWRiYWY",
		BillingSK:                  "GMxZjk3MTczZTMxNGYzlhODg3NmRk",
		VolcServiceAccountAK:       "AKLTY2RmNGQ4NjhmZmJlNGI5YzhkZTM0ZjQyOWY0MTlmOTI",
		VolcServiceAccountSK:       "TURReU9UY3hOR05tTnpJME5HWmhZVGt4WVRZek5tUTVaRFprTm1RMk1EQQ==",
		BillingTopic:               "trade_instance",
		BillingCluster:             "rmq_test_new",
		BillingConsumerGroup:       "dbw_cn-nanjing-bbit",
		EnableBillingTenantIDList:  []string{"**********", "**********"},
		DisableBillingTenantIDList: []string{},
		EnableBillingScheduleCheck: true,
	}).AnyTimes()
	c3Cnf := mocks.NewMockC3ConfigProvider(suite.ctrl)
	c3Cnf.EXPECT().GetNamespace(gomock.Any(), gomock.Any()).Return(&ycnf.C3Config{
		Application: ycnf.Application{
			TOPServiceAccessKey: "AKLTODY2M2EzMzRlNTdhNGU3ZDhhZTlhNDgxYzA3MjkyODY",
			TOPServiceSecretKey: "WVdSak1UQTNNR1F6TURjMk5ETTJZVGt6TldJNU5XTXpZemxqT1RoallUSQ==",
			TLSServiceAccessKey: "AKLTMmMwNzM5ZWUxYjliNGUzY2FjNWZkMTg1YmZkZWQ0MTU",
			TLSServiceSecretKey: "TldWaVlUTXpNemszWlRJeE5ETTFNamhpTXpKaE56SmxPVE15TWpGbE5HSQ==",
		},
	}).AnyTimes()
	Mock(fwctx.GetTenantID).Return("**********").Build()
	Mock((*volctrade.VolcTradeClient).IsAccountRealNameAuthentication).Return(true).Build()
	Mock((*volctrade.VolcTradeClient).PushInstanceMeasure).Return(&volctrade.TradeResInfo{}, nil).Build()
	Mock((*volctrade.VolcTradeClient).MeasureCallback).Return(&volctrade.TradeResInfo{
		RequestId: "123",
		Status:    0,
		Resp:      tradeapi.ResponseMetadata{},
		Result:    nil,
	}, nil).Build()

	mgrProv := mocks.NewMockMgrProvider(suite.ctrl)
	mgrClient := mocks.NewMockMgrClient(suite.ctrl)
	mgrProv.EXPECT().Get().Return(mgrClient).AnyTimes()

	nodedetail := &rdsModel_v2.DescribeDBInstanceDetailResp{}
	json.Unmarshal([]byte("{\"BasicInfo\":{\"InstanceId\":\"mysql-0844ce4826e0\",\"InstanceName\":\"dyh_80_勿删\",\"InstanceStatus\":\"Running\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"DBEngine\":\"Mysql\",\"DBEngineVersion\":\"MySQL_8_0\",\"InstanceType\":\"HA\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"NodeNumber\":\"3\",\"CreateTime\":\"2022-11-16 12:01:36\",\"UpdateTime\":\"\",\"StorageUse\":4.17,\"StorageSpace\":100,\"StorageType\":\"LocalSSD\",\"BackupUse\":0.76,\"VpcId\":\"vpc-8gvvy8uzp3i88k0tze9vkak9\",\"TimeZone\":\"UTC +08:00\",\"DataSyncMode\":\"SemiSync\",\"ProjectName\":\"\",\"InnerVersion\":\"\",\"IsLatestVersion\":false,\"LowerCaseTableNames\":\"1\",\"SubnetId\":\"subnet-h0alcmc3e96o3kyadhv0ggix\",\"ShardNumber\":0,\"StorageDataSize\":**********,\"StorageLogSize\":********,\"StorageBinLogSize\":********,\"StorageErrorLogSize\":859390,\"StorageAuditLogSize\":4096,\"StorageSlowLogSize\":4220526,\"BackupDataSize\":*********,\"BackupLogSize\":*********,\"BackupBinLogSize\":*********,\"BackupErrorLogSize\":868625,\"BackupAuditLogSize\":0,\"BackupSlowLogSize\":7292344,\"PrimaryDBAccount\":\"\",\"AllowListVersion\":\"v2\",\"ServerCollation\":\"\",\"MaintenanceWindow\":{\"MaintenanceTime\":\"18:00Z-22:00Z\",\"DayKind\":\"Week\",\"DayOfWeek\":[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\",\"Sunday\"],\"DayOfMonth\":[]}},\"ConnectionInfo\":[{\"EndpointId\":\"mysql-0844ce4826e0-cluster\",\"EndpointType\":\"Cluster\",\"Description\":\"\",\"Address\":[{\"NetworkType\":\"Ingress\",\"Domain\":\"mysql0844ce4826e0.rds-boe.infcs.tob\",\"IPAddress\":\"\",\"Port\":\"25345\",\"SubnetId\":\"\",\"EipId\":\"\"},{\"NetworkType\":\"Private\",\"Domain\":\"mysql0844ce4826e0.rds-boe.ivolces.com\",\"IPAddress\":\"*************\",\"Port\":\"3306\",\"SubnetId\":\"subnet-h0alcmc3e96o3kyadhv0ggix\",\"EipId\":\"\"},{\"NetworkType\":\"Carma\",\"Domain\":\"mysql-0844ce4826e0-proxy-agent-hs.rds.svc.mix-panel-a.org\",\"IPAddress\":\"\",\"Port\":\"3679\",\"SubnetId\":\"\",\"EipId\":\"\"}],\"EnableReadWriteSplitting\":\"Disable\",\"EnableReadOnly\":\"Disable\",\"EndpointName\":\"\",\"ReadWriteMode\":\"ReadWrite\",\"ReadOnlyNodeWeight\":[{\"NodeType\":\"Primary\",\"NodeId\":\"\",\"Weight\":200},{\"NodeType\":\"ReadOnly\",\"NodeId\":\"mysql-0844ce4826e0-r9d3f\",\"Weight\":0}],\"AutoAddNewNodes\":\"Enable\",\"ReadOnlyNodeDistributionType\":\"Default\",\"ReadOnlyNodeMaxDelayTime\":30},{\"EndpointId\":\"mysql-0844ce4826e0-direct\",\"EndpointType\":\"Direct\",\"Description\":\"\",\"Address\":[{\"NetworkType\":\"Carma\",\"Domain\":\"mysql-0844ce4826e0-hs.rds.svc.mix-panel-a.org\",\"IPAddress\":\"\",\"Port\":\"3306\",\"SubnetId\":\"\",\"EipId\":\"\"}],\"EnableReadWriteSplitting\":\"Disable\",\"EnableReadOnly\":\"Disable\",\"EndpointName\":\"\",\"ReadWriteMode\":\"ReadWrite\",\"AutoAddNewNodes\":\"Disable\",\"ReadOnlyNodeDistributionType\":\"Default\"}],\"ChargeDetail\":{\"ChargeType\":\"PostPaid\",\"AutoRenew\":false,\"PeriodUnit\":\"Month\",\"Period\":1,\"Number\":1,\"ChargeStatus\":\"Normal\",\"ChargeStartTime\":\"\",\"ChargeEndTime\":\"\",\"OverdueTime\":\"\",\"OverdueReclaimTime\":\"\"},\"NodeDetailInfo\":[{\"InstanceId\":\"mysql-0844ce4826e0\",\"NodeId\":\"mysql-0844ce4826e0-0\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"NodeType\":\"Primary\",\"NodeStatus\":\"Running\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"CreateTime\":\"2022-11-16 12:01:36\",\"UpdateTime\":\"\",\"ShardId\":\"\"},{\"InstanceId\":\"mysql-0844ce4826e0\",\"NodeId\":\"mysql-0844ce4826e0-1\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"NodeType\":\"Secondary\",\"NodeStatus\":\"Running\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"CreateTime\":\"2022-11-16 12:01:36\",\"UpdateTime\":\"\",\"ShardId\":\"\"},{\"InstanceId\":\"mysql-0844ce4826e0\",\"NodeId\":\"mysql-0844ce4826e0-r9d3f\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"NodeType\":\"ReadOnly\",\"NodeStatus\":\"Running\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"CreateTime\":\"2022-11-21 22:26:47\",\"UpdateTime\":\"2022-11-22 16:47:43\",\"ShardId\":\"\"}],\"ShardInfo\":null}"),
		nodedetail)
	mgrClient.EXPECT().Call(gomock.Any(), rdsModel_v2.Action_DescribeDBInstanceDetail.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, *nodedetail).Return(nil).AnyTimes()

	pods := &rdsModel.ListInstancePodsResp{}
	json.Unmarshal([]byte("{\"Total\":7,\"Datas\":[{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-ha-controller-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"200mC\",\"MemInfo\":\"200Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:1/1\",\"LastestStartTime\":\"2022-11-23 10:31:40\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"ha-controller\",\"Port\":\"2338\",\"Image\":\"hub.byted.org/infcs/ha_controller:20221115.1718-epic_v1.1.10-1b9759\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"200Mi\"}],\"Component\":\"HA Controller\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-ha-controller-1\",\"NodeIP\":\"***********\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"200mC\",\"MemInfo\":\"200Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:1/1\",\"LastestStartTime\":\"2022-11-23 10:31:40\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"ha-controller\",\"Port\":\"2338\",\"Image\":\"hub.byted.org/infcs/ha_controller:20221115.1718-epic_v1.1.10-1b9759\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"200Mi\"}],\"Component\":\"HA Controller\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-sbf4a-ha-controller-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"200mC\",\"MemInfo\":\"200Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:1/1\",\"LastestStartTime\":\"2022-11-23 10:31:41\",\"Resource\":\"mysql-bfb26ba0773e-sbf4a\",\"Containers\":[{\"Name\":\"ha-controller\",\"Port\":\"2338\",\"Image\":\"hub.byted.org/infcs/ha_controller:20221115.1718-epic_v1.1.10-1b9759\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"200Mi\"}],\"Component\":\"HA Controller\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"1C 200mC\",\"MemInfo\":\"2458Mi500Mi\",\"DiskInfo\":\"20Gi\",\"RunningInfo\":\"Container:3/3\",\"LastestStartTime\":\"2022-11-23 10:31:26\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"mysql\",\"Port\":\"3306\",\"Image\":\"hub.byted.org/infcs/infcs_rds_mysql_server_5_7:1c16f3a2b1b86f0ab416221869608d41\",\"Status\":\"Ready\",\"Cpu\":\"1C\",\"Mem\":\"2458Mi\"},{\"Name\":\"backup-server\",\"Port\":\"8889\",\"Image\":\"hub.byted.org/infcs/rds_backup:20221109.2245-epic_v1.1.10-2c756b\",\"Status\":\"Ready\",\"Cpu\":\"\",\"Mem\":\"\"},{\"Name\":\"mysql-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/infcs/tob_rds_mysql_agent:20221116.1608-epic_v1.1.10-c1b154\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"MySQL\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-sbf4a-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"1C 200mC\",\"MemInfo\":\"2458Mi500Mi\",\"DiskInfo\":\"20Gi\",\"RunningInfo\":\"Container:3/3\",\"LastestStartTime\":\"2022-11-23 10:31:26\",\"Resource\":\"mysql-bfb26ba0773e-sbf4a\",\"Containers\":[{\"Name\":\"mysql\",\"Port\":\"3306\",\"Image\":\"hub.byted.org/infcs/infcs_rds_mysql_server_5_7:1c16f3a2b1b86f0ab416221869608d41\",\"Status\":\"Ready\",\"Cpu\":\"1C\",\"Mem\":\"2458Mi\"},{\"Name\":\"backup-server\",\"Port\":\"8889\",\"Image\":\"hub.byted.org/infcs/rds_backup:20221109.2245-epic_v1.1.10-2c756b\",\"Status\":\"Ready\",\"Cpu\":\"\",\"Mem\":\"\"},{\"Name\":\"mysql-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/infcs/tob_rds_mysql_agent:20221116.1608-epic_v1.1.10-c1b154\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"MySQL\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-proxy-agent-544dbc9ff7-fd4l6\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"500mC 200mC\",\"MemInfo\":\"2Gi500Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:2/2\",\"LastestStartTime\":\"2022-11-23 10:31:48\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"proxy\",\"Port\":\"3679 23679 3680 23680 3681 23681 3682 23682 3683 23683 3684 23684 3685 23685 3686 23686 3687 23687 3688 23688 3689 23689 3690 23690 3691 23691 3692 23692 3693 23693 3694 23694 3695 23695\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_sidecar:20221115.1403-epic_v1.1.10-e73d0b\",\"Status\":\"Ready\",\"Cpu\":\"500mC\",\"Mem\":\"2Gi\"},{\"Name\":\"proxy-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_agent_sidecar:07383ec561c92137e9b26bc326b5af06\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"Proxy\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-sbf4a-proxy-agent-bb476c4fb-4mblj\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"500mC 200mC\",\"MemInfo\":\"2Gi500Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:2/2\",\"LastestStartTime\":\"2022-11-23 10:31:49\",\"Resource\":\"mysql-bfb26ba0773e-sbf4a\",\"Containers\":[{\"Name\":\"proxy\",\"Port\":\"3679 23679 3680 23680 3681 23681 3682 23682 3683 23683 3684 23684 3685 23685 3686 23686 3687 23687 3688 23688 3689 23689 3690 23690 3691 23691 3692 23692 3693 23693 3694 23694 3695 23695\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_sidecar:20221115.1403-epic_v1.1.10-e73d0b\",\"Status\":\"Ready\",\"Cpu\":\"500mC\",\"Mem\":\"2Gi\"},{\"Name\":\"proxy-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_agent_sidecar:07383ec561c92137e9b26bc326b5af06\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"Proxy\"}]}"),
		pods)
	mgrClient.EXPECT().Call(gomock.Any(), rdsModel.Action_ListInstancePods.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, *pods).Return(nil).AnyTimes()

	local := &local_dal.LocalAudit{}
	local.Init(ctx, suite.ctrl)

	auditSvc := mocks_audit.NewMockSqlAuditService(suite.ctrl)
	auditSvc.EXPECT().TerminateSqlAuditOrderCallback(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	// auditSvc1 模拟创建失败的场景
	//auditSvc.EXPECT().TerminateSqlAuditOrderCallback(gomock.Any(), gomock.Any(), gomock.Any()).Return(consts.ErrorOf(model.ErrorCode_InternalError)).AnyTimes()

	dsn := "root:123456@tcp(127.0.0.1:3306)/dbwmgr?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		fmt.Errorf("%v", err)
	}
	publishEventSvc := mocks.NewMockPublishEventService(suite.ctrl)
	publishEventSvc.EXPECT().PublishEvent(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, event *entity.Event) error {
		return db.Create(event.ToDao()).Error
	}).AnyTimes()
	publishEventSvc.EXPECT().UpdateEventResult(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, msgID string, instanceID string, result int32) error {
		return db.Model(&dao.Event{}).Where("uuid=? AND instance_id=?", msgID, instanceID).Update("result", result).Error
	}).AnyTimes()
	return ctx, cfg, c3Cnf, local, auditSvc, publishEventSvc
}

func (suite *BillingScheduleCheckActorSuite) TestBillingScheduleCheckActor_genAccIfNotExist() {
	type fields struct {
		state                      map[string]string
		checkCnt                   int64
		auditReportMeasure         int64
		auditReportMeasureCallback int64
		billSvc                    bill_svc.BillingService
		accDAL                     dal.AuditAccountantDAL
		accGenDAL                  dal.AccountantGenDAL
		auditTlsDAL                dal.AuditTlsDAL
		actorClient                cli.ActorClient
		mysqlMgr                   mgr.Provider
	}
	type args struct {
		ctx            context.Context
		AccountantTime int64
	}

	ctx := context.Background()
	local := &local_dal.LocalAudit{}
	local.Init(ctx, suite.ctrl)
	billSvc := bill.NewMockBillingService(suite.ctrl)
	billSvc.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(nil, errors.New("get instance time out")).AnyTimes()
	billSvc1 := bill.NewMockBillingService(suite.ctrl)
	billSvc1.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	billSvc2 := bill.NewMockBillingService(suite.ctrl)
	billSvc2.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(&tradeapi.Instance{
		Status:        1,
		BillingStatus: nil,
	}, nil).AnyTimes()

	actorCli := mocks.NewMockActorClient(suite.ctrl)
	kc := mocks.NewMockKindClient(suite.ctrl)
	kc.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	actorCli.EXPECT().KindOf(gomock.Any()).Return(kc).AnyTimes()

	aagDal := mock_dal.NewMockAccountantGenDAL(suite.ctrl)
	aagDal.EXPECT().Create(gomock.Any(), gomock.Any()).Return(int64(1), nil).AnyTimes()
	aagDal.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	aagDal.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, gorm.ErrRecordNotFound).AnyTimes()
	local.AccountGentDAL = aagDal

	atDal := mock_dal.NewMockAuditTlsDAL(suite.ctrl)
	auditTls := &dao.AuditTls{
		ID:               0,
		TlsId:            0,
		InstanceID:       "0",
		FollowInstanceID: "0",
		DeployType:       "0",
		TenantID:         "0",
		DbType:           "MySQL",
		Status:           2,
		Deleted:          0,
	}
	atDal.EXPECT().GetAll(gomock.Any()).Return([]*dao.AuditTls{auditTls}, nil).AnyTimes()
	local.AuditDAL = atDal
	aaDal := mock_dal.NewMockAuditAccountantDAL(suite.ctrl)
	aaDal.EXPECT().Create(gomock.Any(), gomock.Any()).Return(int64(1), nil).AnyTimes()
	aaDal.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, gorm.ErrRecordNotFound).AnyTimes()

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "aaa",
			fields: fields{
				state:                      nil,
				checkCnt:                   0,
				auditReportMeasure:         0,
				auditReportMeasureCallback: 0,
				billSvc:                    billSvc,
				accDAL:                     local.AccDAL,
				accGenDAL:                  local.AccountGentDAL,
				auditTlsDAL:                local.AuditDAL,
				actorClient:                actorCli,
			},
			args: args{
				ctx: ctx,
			},
			wantErr: false,
		},
		{
			name: "bbb",
			fields: fields{
				state:                      nil,
				checkCnt:                   0,
				auditReportMeasure:         0,
				auditReportMeasureCallback: 0,
				billSvc:                    billSvc,
				accDAL:                     aaDal,
				accGenDAL:                  local.AccountGentDAL,
				auditTlsDAL:                local.AuditDAL,
				actorClient:                actorCli,
			},
			args: args{
				ctx: ctx,
			},
			wantErr: false,
		},
		{
			name: "ccc",
			fields: fields{
				state:                      nil,
				checkCnt:                   0,
				auditReportMeasure:         0,
				auditReportMeasureCallback: 0,
				billSvc:                    billSvc1,
				accDAL:                     aaDal,
				accGenDAL:                  local.AccountGentDAL,
				auditTlsDAL:                local.AuditDAL,
				actorClient:                actorCli,
			},
			args: args{
				ctx: ctx,
			},
			wantErr: false,
		},
		{
			name: "ddd",
			fields: fields{
				state:                      nil,
				checkCnt:                   0,
				auditReportMeasure:         0,
				auditReportMeasureCallback: 0,
				billSvc:                    billSvc2,
				accDAL:                     aaDal,
				accGenDAL:                  local.AccountGentDAL,
				auditTlsDAL:                local.AuditDAL,
				actorClient:                actorCli,
			},
			args: args{
				ctx: ctx,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		PatchConvey("", suite.T(), func() {
			b := &BillingScheduleCheckActor{
				state:                      tt.fields.state,
				checkCnt:                   tt.fields.checkCnt,
				auditReportMeasure:         tt.fields.auditReportMeasure,
				auditReportMeasureCallback: tt.fields.auditReportMeasureCallback,
				billSvc:                    tt.fields.billSvc,
				actDAL:                     local.AccountDAL,
				fullSqlDAL:                 local.FullSqlDAL,
				accGenDAL:                  tt.fields.accGenDAL,
				auditTlsDAL:                tt.fields.auditTlsDAL,
				mysqlMgr:                   tt.fields.mysqlMgr,
			}
			if err := b.genAccIfNotExist(tt.args.ctx, tt.args.AccountantTime); (err != nil) != tt.wantErr {
				suite.T().Errorf("wantErr:%t , but err: %s", tt.wantErr, err)
			}
		})
	}
}
