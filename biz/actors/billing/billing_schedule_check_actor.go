package billing

import (
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"fmt"
	"strconv"
	"time"

	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	"code.byted.org/infcs/dbw-mgr/biz/mgr"

	"gorm.io/gorm"

	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

	"code.byted.org/infcs/ds-lib/common/log"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"go.uber.org/dig"
)

type NewBillingScheduleCheckActorIn struct {
	dig.In
	BillSvc     billing.BillingService
	ActDAL      dal.AccountantDAL
	AccGenDAL   dal.AccountantGenDAL
	AuditTlsDAL dal.AuditTlsDAL
	FullSqlDAL  dal.FullSqlDAL
	MysqlMgr    mgr.Provider `name:"mysql"`
	Conf        config.ConfigProvider
	PushBill    PushBilling
}

type BillingScheduleCheckActor struct {
	state    map[string]string
	checkCnt int64

	auditReportMeasure         int64
	auditReportMeasureCallback int64

	billSvc     billing.BillingService
	actDAL      dal.AccountantDAL
	accGenDAL   dal.AccountantGenDAL
	auditTlsDAL dal.AuditTlsDAL
	fullSqlDAL  dal.FullSqlDAL
	mysqlMgr    mgr.Provider `name:"mysql"`
	conf        config.ConfigProvider
	pushBill    PushBilling
}

const (
	timeout = 10 * time.Minute
)

func NewBillingScheduleCheckActor(i NewBillingScheduleCheckActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.BillingScheduleCheckActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			bscActor := &BillingScheduleCheckActor{
				billSvc:     i.BillSvc,
				actDAL:      i.ActDAL,
				auditTlsDAL: i.AuditTlsDAL,
				accGenDAL:   i.AccGenDAL,
				fullSqlDAL:  i.FullSqlDAL,
				mysqlMgr:    i.MysqlMgr,
				checkCnt:    0,
				conf:        i.Conf,
				pushBill:    i.PushBill,
			}

			return bscActor
		}),
	}
}

func (b *BillingScheduleCheckActor) GetState() []byte {
	return []byte{}
}

func (b *BillingScheduleCheckActor) Process(ctx types.Context) {
	sysCtx := context.WithValue(context.Background(), "biz-context", &fwctx.BizContext{
		LogID: fmt.Sprintf("%s-%d-%d-%d", "BillingScheduleCheck", time.Now().Hour(), time.Now().Minute(), time.Now().Second()),
	})
	ctx = types.BuildMyContext(sysCtx, ctx, nil)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "started BillingScheduleCheckActor")
		ctx.SetReceiveTimeout(timeout)
	case *actor.ReceiveTimeout:
		log.Info(ctx, "timeout msg: %s", msg)
		b.checkReportTask(ctx)
		ctx.SetReceiveTimeout(timeout)
	case *actor.Stopping:
		context.Background()
		log.Info(ctx, "Billing schedule is stopping")
	}
}

func (b *BillingScheduleCheckActor) checkReportTask(ctx context.Context) {
	b.checkCnt = b.checkCnt + 1
	if !b.conf.Get(ctx).EnableBillingScheduleCheck {
		log.Info(ctx, "EnableBillingScheduleCheck:%s", b.conf.Get(ctx).EnableBillingScheduleCheck)
		return
	}
	// 查找当前有效的账期
	var accountantTimes []int64
	now := time.Now()
	err := b.genAccIfNotExist(ctx, time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location()).Unix())
	if err != nil {
		log.Error(ctx, "如果持续一小时生成失败会造成无法推量，出现频率不高可以忽略，generate accountant record error %s", err)
	}
	for i := 0; i < 3; i++ {
		accountantTimes = append(accountantTimes, time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-i, 0, 0, 0, now.Location()).Unix())
	}
	log.Info(ctx, "checkReportTask checkCnt:%d", b.checkCnt)
	region := utils.GetRegion()
	// 将账期内未推量实例进行推量，按region-租户-账期 进行推送
	b.reportAll(ctx, accountantTimes, region)
	// 各账期尝试callback，如果已经callback则跳过，如果有实例超时未callback则报警
	b.callbackAll(ctx, accountantTimes, region)
}

func (b *BillingScheduleCheckActor) reportAll(ctx context.Context, accountantTimes []int64, region string) {
	for _, accTime := range accountantTimes {
		err := b.ReportAuditCollectInstanceMeasure(ctx, &model.ReportAllInstanceMeasureReq{
			AccountantTime: accTime,
			RegionID:       region,
			MeasureUnit:    0,
		})
		if err != nil {
			log.Warn(ctx, "Report audit collect instance measure error : %s", err)
		}
	}
}

func (b *BillingScheduleCheckActor) callbackAll(ctx context.Context, accountantTimes []int64, region string) {
	for _, accTime := range accountantTimes {
		log.Info(ctx, "PushAuditRawMeasureFinish AccountantTime:%d(%s), region:%s", accTime, time.Unix(accTime, 0).Format(consts.AccountTimeFormat), region)
		err := b.billSvc.PushAuditRawMeasureFinish(ctx, accTime, region)
		if err != nil {
			log.Warn(ctx, "PushAuditRawMeasureFinish error : %s", err)
		}
	}
}

func (b *BillingScheduleCheckActor) ReportAuditCollectInstanceMeasure(ctx context.Context, req *model.ReportAllInstanceMeasureReq) error {
	// 检查回调状态
	accGen, err := b.accGenDAL.Get(ctx, req.GetAccountantTime())
	if err != nil {
		log.Error(ctx, "query failed:%s, accountant gen table : %d", err, req.GetAccountantTime())
		return err
	}
	if accGen.Status == int32(model.AuditInstanceAccountantStatus_CALLBACK) {
		log.Info(ctx, "ReportAuditCollectInstanceMeasure AccountantTime %s was callback, no need to report.", req.GetAccountantTime())
		return nil
	}

	log.Info(ctx, "ReportAuditCollectInstanceMeasure request: %+v", req)
	err = b.pushBill.Process(ctx, strconv.FormatInt(req.AccountantTime, 10))
	if err != nil {
		log.Warn(ctx, "pushBill.Process error:%s, AccountantTime:%s", err, req.GetAccountantTime())
	}
	return nil
}

// 初始化账期数据（在实例删除时需添加实例下一期的账期数据）（可以重试）
func (b *BillingScheduleCheckActor) genAccIfNotExist(ctx context.Context, AccountantTime int64) error {
	_, err := b.accGenDAL.Get(ctx, AccountantTime)
	if err == gorm.ErrRecordNotFound {
		// pass
	} else if err != nil {
		log.Warn(ctx, "meta data get audit instance accountant error: %s ", err)
		return err
	} else {
		return nil
	}
	// mysql pg vedb redis
	auditInstanceList, err := b.auditTlsDAL.GetAll(ctx)
	if err != nil {
		log.Warn(ctx, "meta data GetAll audit instance error: %s ", err)
		return err
	}
	if len(auditInstanceList) == 0 {
		log.Info(ctx, "audit instance not exist")
	}
	err = b.genAccRecord(ctx, AccountantTime, auditInstanceList)
	if err != nil {
		log.Warn(ctx, "%s", err)
		return err
	}

	// fullsql
	fullsqlInstanceList, err := b.fullSqlDAL.GetAll(ctx)
	if err != nil {
		log.Warn(ctx, "meta data GetAll fullsql instance error: %s ", err)
		return err
	}
	if len(auditInstanceList) == 0 {
		log.Info(ctx, "fullsql instance not exist")
	}
	err = b.genAccRecord(ctx, AccountantTime, fullsqlInstanceList)
	if err != nil {
		log.Warn(ctx, "%s", err)
		return err
	}

	log.Info(ctx, "gen all instance accountant:%s", time.Unix(AccountantTime, 0).Format(consts.AccountTimeFormat))
	_, err = b.accGenDAL.Create(ctx, &dao.AuditAccountantGen{
		AccountantTime: AccountantTime,
		Status:         0,
		Deleted:        0,
	})
	if err != nil {
		log.Warn(ctx, "%s", err)
		return err
	}
	log.Info(ctx, "gen AccountantTime: %d(%s)", AccountantTime, time.Unix(AccountantTime, 0).Format(consts.AccountTimeFormat))
	return nil
}

func (b *BillingScheduleCheckActor) genAccRecord(ctx context.Context, AccountantTime int64, auditInstanceList []*dao.AuditTls) error {
	for _, auditInstance := range auditInstanceList {
		productType, _ := model.LogProductTypeFromString(auditInstance.ProductType)
		dsType, _ := model.DSTypeFromString(auditInstance.DbType)
		chargeItemCodes := GetChargeItemCode(productType, dsType)

		ctx = utils.Su(ctx, auditInstance.TenantID)
		billInstance, err := b.billSvc.GetInstance(ctx, auditInstance.InstanceID)
		if err != nil {
			// 查询订单失败需要等待下一次调度
			log.Warn(ctx, "bill service get instance error: %s , InstanceID:%s", err, auditInstance.InstanceID)
			continue
		}
		if billInstance == nil {
			// 实例不存在订单，需要走补单或者将不存在订单的实例删除
			log.Error(ctx, "bill service get instance nil, %+v", auditInstance)
			continue
		}
		if auditInstance.Status < int32(model.AuditStatus_Running) {
			log.Info(ctx, "instance is creating.")
			continue
		}
		// 实例处于正常状态-订单已经结束，报警
		if (auditInstance.Status == int32(model.AuditStatus_Running)) &&
			(billInstance.Status == 2 || billInstance.Status == 3 || billInstance.Status == 5 || billInstance.Status == 7) {
			log.Error(ctx, "bill instance is OVER. audit instance status(%d) is not consistent with bill instance status(%d).", auditInstance.Status, billInstance.Status)
			continue
		}
		billCreateTime, err := time.Parse(consts.NormalTimeFormat, billInstance.CreatedTime)
		if err == nil && AccountantTime <= (billCreateTime.Unix()+3600) {
			log.Info(ctx, "accountant time is less than bill create time.")
			continue
		}

		log.Info(ctx, "create record for audit instance:%s accountant:%s", auditInstance.InstanceID, time.Unix(AccountantTime, 0).Format(consts.AccountTimeFormat))

		for _, chargeItemCode := range chargeItemCodes {
			_, err := b.actDAL.Get(ctx, AccountantTime, auditInstance.InstanceID, productType.String(), chargeItemCode.String())
			if err == nil {
				// 已经存在不需要再次生成记录
				continue
			}

			if err == gorm.ErrRecordNotFound {
				// 没有这个实例的账期记录
			} else {
				// 查询元数据失败需要等待下一次调度
				log.Warn(ctx, "meta data get instance error: %s , InstanceID:%s", err, auditInstance.InstanceID)
				return err
			}

			_, err = b.actDAL.Create(ctx, &dao.AuditAccountant{
				InstanceID:     auditInstance.InstanceID,
				AccountantTime: AccountantTime,
				TenantID:       auditInstance.TenantID,
				ProductType:    productType.String(),
				ChargeItemCode: chargeItemCode.String(),
				TlsUsage:       0,
				Status:         int32(model.AuditInstanceAccountantStatus_INIT),
				Deleted:        0,
			})
			if err != nil {
				// 创建失败等待下一次调度
				log.Warn(ctx, "create record error: %s", err)
				return err
			}
		}
	}
	return nil
}

func GetChargeItemCode(productType model.LogProductType, dsType model.DSType) []model.ChargeItem {
	switch productType {
	case model.LogProductType_AuditLog:
		switch dsType {
		case model.DSType_MySQL:
			return []model.ChargeItem{model.ChargeItem_log_collect}
		case model.DSType_VeDBMySQL:
			return []model.ChargeItem{model.ChargeItem_log_collect}
		case model.DSType_Postgres:
			return []model.ChargeItem{model.ChargeItem_log_collect}
		case model.DSType_Redis:
			return []model.ChargeItem{
				model.ChargeItem_redis_log_storage_audit,
				model.ChargeItem_redis_log_collect_audit,
				model.ChargeItem_redis_index_storage_audit,
				model.ChargeItem_redis_index_traffic_audit,
			}
		case model.DSType_Mongo:
			return []model.ChargeItem{
				model.ChargeItem_mongo_log_storage_audit,
				model.ChargeItem_mongo_log_collect_audit,
				model.ChargeItem_mongo_index_storage_audit,
				model.ChargeItem_mongo_index_traffic_audit,
			}
		}
	case model.LogProductType_FullSqlLog:
		switch dsType {
		case model.DSType_MySQL:
			return []model.ChargeItem{
				model.ChargeItem_mysql_log_storage_sql_insight,
				model.ChargeItem_mysql_index_storage_sql_insight,
				model.ChargeItem_mysql_index_traffic_sql_insight,
				model.ChargeItem_mysql_log_collect_sql_insight,
				model.ChargeItem_mysql_aggregation_sql_insight,
			}
		case model.DSType_VeDBMySQL:
			return []model.ChargeItem{
				model.ChargeItem_vedb_log_storage_sql_insight,
				model.ChargeItem_vedb_index_storage_sql_insight,
				model.ChargeItem_vedb_index_traffic_sql_insight,
				model.ChargeItem_vedb_log_collect_sql_insight,
				model.ChargeItem_vedb_aggregation_sql_insight,
			}
		case model.DSType_MetaMySQL:
			return []model.ChargeItem{
				model.ChargeItem_mysql_log_storage_sql_insight,
				model.ChargeItem_mysql_index_storage_sql_insight,
				model.ChargeItem_mysql_index_traffic_sql_insight,
				model.ChargeItem_mysql_log_collect_sql_insight,
				model.ChargeItem_mysql_aggregation_sql_insight,
			}
		}
	}
	return nil
}
