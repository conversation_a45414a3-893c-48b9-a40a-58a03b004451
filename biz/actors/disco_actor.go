package actors

import (
	"fmt"
	"strconv"

	mongoModel "code.byted.org/infcs/dbw-mgr/gen/mongo-mgr/2022-01-01/kitex_gen/infcs/mongodb/v2"
	redisModel_v2 "code.byted.org/infcs/dbw-mgr/gen/redis-mgr/2020-12-07/kitex_gen/base"
	redisModel "code.byted.org/infcs/dbw-mgr/gen/redis-mgr/kitex_gen/base"
	"code.byted.org/infcs/protoactor-go/actor"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/ds-lib/common/log"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	discoModel "code.byted.org/ti/disco_sdk/byte_dns_volc_underlay"
	dns "code.byted.org/ti/disco_sdk/private_dns"
	"go.uber.org/dig"
)

type NewDiscoActorIn struct {
	dig.In
	Cnf      c3.ConfigProvider
	Loc      location.Location
	RedisMgr mgr.Provider `name:"redis"`
	MongoMgr mgr.Provider `name:"mongo"`
}

func NewDiscoActor(p NewDiscoActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.DiscoActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			actor := &discoActor{
				cnf:      p.Cnf,
				nosql:    p.RedisMgr,
				loc:      p.Loc,
				mongomgr: p.MongoMgr,
				opts:     []dns.Option{
					// disco.WithTimeout(10 * time.Second),
				},
			}
			return actor
		}),
	}
}

type Address struct {
	Ip   string
	Port string
}

type discoActor struct {
	cnf   c3.ConfigProvider
	loc   location.Location
	dance *dns.Client
	opts  []dns.Option
	// FIXME: LRU?
	cache    map[string]Address
	nosql    mgr.Provider
	mongomgr mgr.Provider
}

func (d *discoActor) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		d.initDiscoClient(ctx, msg)
	/* for redis */
	case *shared.CacheDomain:
		d.cacheDomain(ctx, msg)
	case *shared.ResolveDomain:
		d.resolveDomain(ctx, msg)
	case *shared.ResolveDomain2:
		d.resolveDomain2(ctx, msg)
	/* for mongo */
	case *shared.CacheMongoAddress:
		d.cacheMongoAddress(ctx, msg)
	}
}

func (d *discoActor) makeSureClient(ctx types.Context) {
	if d.dance == nil {
		d.initDiscoClient(ctx, &actor.Started{})
	}
}

func (d *discoActor) initDiscoClient(ctx types.Context, msg *actor.Started) {
	cfg := d.cnf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	cli, err := dns.NewClient(cfg.CloudServiceSecret, d.loc.RegionID())
	log.Info(ctx, "New Disco Client err: %v", err)
	d.dance = cli
	d.cache = make(map[string]Address)
}

/* for redis */
// NOTE: 实例的域名解析记录在创建以后就不会改变
func (d *discoActor) cacheDomain(ctx types.Context, msg *shared.CacheDomain) {
	d.makeSureClient(ctx)
	info := msg.Info
	addr, ok := d.cache[info.InstanceId]
	if !ok || addr.Ip == "" || addr.Port == "" {
		domain, port, err := d.getInstancePrivateDomain(ctx, info.InstanceId, info.TenantId)
		if err != nil {
			log.Warn(ctx, "get instance [%s] private domain failed: %s", info.InstanceId, err.Error())
			return
		}
		ip, err := d.cacheDomainHelper(ctx, domain)
		if err != nil {
			log.Warn(ctx, "cache domain failed: %s", err.Error())
			return
		}
		d.cache[info.InstanceId] = Address{Ip: ip, Port: port}
	}
}

func (d *discoActor) getInstancePrivateDomain(ctx types.Context, instanceId string, tenantId string) (ip string, port string, err error) {
	// FIXME: duplicate code
	req := &redisModel.DescribeInstanceAttributeReq{
		InstanceId: libutils.StringRef(instanceId),
	}
	// TODO call redis mgr move to datasource
	resp := &redisModel.DescribeInstanceAttributeResp{}
	if err = d.nosql.Get().Call(ctx, redisModel.Action_DescribeInstanceAttribute.String(), req, resp, client.WithTenantID(tenantId)); err != nil {
		return
	}

	for i := range resp.VisitAddrs {
		if resp.VisitAddrs[i].AddrType == redisModel.VisitAddressType_Private.String() {
			ip = resp.VisitAddrs[i].IPAddress
			port = resp.VisitAddrs[i].Port
			break
		}
	}

	if ip == "" || port == "" {
		err = fmt.Errorf("can't resolve redis %s address, get empty info for addr type %s", resp.InstanceId, redisModel.VisitAddressType_Private.String())
	}
	return
}

func (d *discoActor) getEnterprisePrivateDomain(ctx types.Context, instanceId string, tenantId string) (ip string, port string, err error) {
	req := redisModel_v2.DescribeEnterpriseDBInstanceDetailReq{
		InstanceId: libutils.StringRef(instanceId),
	}
	resp := &redisModel_v2.DescribeEnterpriseDBInstanceDetailResp{}

	if err = d.nosql.Get().Call(ctx, redisModel_v2.Action_DescribeEnterpriseDBInstanceDetail.String(), req, resp, client.WithTenantID(tenantId)); err != nil {
		log.Warn(ctx, "describe enterprise instance error %s", err)
		return
	}
	for i := range resp.VisitAddrs {
		if resp.VisitAddrs[i].AddrType == redisModel.VisitAddressType_Private.String() {
			ip = resp.VisitAddrs[i].IPAddress
			port = resp.VisitAddrs[i].Port
			break
		}
	}
	if ip == "" || port == "" {
		err = fmt.Errorf("can't resolve redis %s address, get empty info for addr type %s", resp.InstanceId, redisModel.VisitAddressType_Private.String())
	}
	return
}

// cacheDomainHelper 解析域名
func (d *discoActor) cacheDomainHelper(ctx types.Context, domain string) (string, error) {
	host, rootDomain := utils.SplitDomain(domain)
	if host == "" || rootDomain == "" {
		msg := fmt.Sprintf("Split Domain [%s] Error: get host[%s] and rootDomain[%s]", domain, host, rootDomain)
		log.Info(ctx, msg)
		return "", fmt.Errorf(msg)
	}
	log.Info(ctx, "domain is %s,host is %s,rootDomain is %s", domain, host, rootDomain)
	resp, err := d.dance.ListDomainRecords(ctx, &dns.ListDomainRecordsRequest{
		Domain:      rootDomain,
		RRKeyWord:   host,
		SearchMode:  discoModel.SearchModeExact,
		PageNumber:  "1",
		PageSize:    "20",
		NameKeyWord: domain,
	})
	if err != nil {
		msg := fmt.Sprintf("rootDomain is %s, host is %s,ListDomainRecords Err: %s", rootDomain, host, err.Error())
		log.Info(ctx, msg)
		return "", fmt.Errorf(msg)
	}

	records := resp.DomainRecords
	var value string
	for i := range records {
		// 这里只获取IPv4的地址,因为shuttle目前只支持IPv4的地址
		if records[i].DomainName == rootDomain && records[i].RR == host && utils.IsIPv4(records[i].Value) {
			value = records[i].Value
			break
		}
	}

	if value == "" {
		msg := fmt.Sprintf("Resolve [%s] failed: didn't find", domain)
		log.Info(ctx, msg)
		return "", fmt.Errorf(msg)
	}

	log.Info(ctx, "cache domain [%s] with value [%s]", domain, value)
	return value, nil
}

func (d *discoActor) resolveDomain(ctx types.Context, msg *shared.ResolveDomain) {
	d.makeSureClient(ctx)
	instanceId := msg.Info.InstanceId
	address, ok := d.cache[instanceId]
	if !ok || address.Ip == "" || address.Port == "" {
		d.cacheDomain(ctx, &shared.CacheDomain{Info: msg.Info})
		address, ok = d.cache[instanceId]
		// not cache forever
		delete(d.cache, instanceId)
		if !ok || address.Ip == "" || address.Port == "" {
			msg := fmt.Sprintf("resolve domain failed for instance %s", instanceId)
			log.Info(ctx, msg)
			ctx.Respond(&shared.ResolveDomainFailed{
				ErrMessage: msg,
			})
			return
		}
	}

	port, err := strconv.ParseInt(address.Port, 10, 64)
	if err != nil {
		msg := fmt.Sprintf("get invalid port: %s", address.Port)
		log.Info(ctx, msg)
		ctx.Respond(&shared.ResolveDomainFailed{
			ErrMessage: msg,
		})
	}

	log.Info(ctx, "Resolve Instance [%s] with address [%s:%d]", instanceId, address.Ip, port)
	ctx.Respond(&shared.ResolveDomainResult{
		Ip:   address.Ip,
		Port: uint64(port),
	})
}

func (d *discoActor) resolveDomain2(ctx types.Context, msg *shared.ResolveDomain2) {
	d.makeSureClient(ctx)
	domain := msg.Domain
	ip, err := d.cacheDomainHelper(ctx, domain)
	if err != nil {
		msg := fmt.Sprintf("resolve domain %s failed: %v", domain, err)
		log.Info(ctx, msg)
		ctx.Respond(&shared.ResolveDomainFailed{
			ErrMessage: msg,
		})
		return
	}
	ctx.Respond(&shared.ResolveDomainResult{
		Ip:   ip,
		Port: 0,
	})
}

/* for mongodb */
// 先查缓存，如果缓存里面没有，就get ip port，然后存入返回，如果有，就直接返回
func (d *discoActor) cacheMongoAddress(ctx types.Context, msg *shared.CacheMongoAddress) {
	d.makeSureClient(ctx)
	info := msg.Info
	ip, port, err := d.getInstancePrivateAddress(ctx, info.InstanceId, info.TenantId, info.MongoNodeId)
	if err != nil {
		log.Warn(ctx, "mongoImpl: get instance [%s] private address failed: %s", info.InstanceId, err.Error())
		ctx.Respond(&shared.GetMongoAddressFailed{
			ErrMessage: fmt.Sprintf("get instance [%s] private address failed: %s ", info.InstanceId, err.Error()),
		})
		return
	}
	intNum, _ := strconv.Atoi(port)
	log.Info(ctx, "mongoImpl: Resolve Mongo Instance [%s] with address [%s:%d]", info.InstanceId, ip, uint64(intNum))
	ctx.Respond(&shared.GetMongoAddressResult{
		Ip:   ip,
		Port: uint64(intNum),
	})
}

func (d *discoActor) getInstancePrivateAddress(ctx types.Context, instanceID string, tenantID string, nodeID string) (ip string, port string, err error) {
	req := &mongoModel.DescribeDBEndpointReq{
		InstanceId: instanceID,
	}
	resp := &mongoModel.DescribeDBEndpointResp{}
	if err = d.mongomgr.Get().Call(ctx, mongoModel.Action_DescribeDBEndpoint.String(), req, resp, client.WithTenantID(tenantID), client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		log.Warn(ctx, "discoActor getInstancePrivateAddress DescribeDBEndpoint failed!, err is:%v", utils.Show(err))
		return
	}
	log.Info(ctx, "mongoImpl: resp DescribeDBEndpoint is %v", resp)
	for i := range resp.DBEndpoints {
		if resp.DBEndpoints[i].NetworkType == mongoModel.NetworkType_Private &&
			(resp.DBEndpoints[i].EndpointType == mongoModel.EndpointType_Mongos ||
				resp.DBEndpoints[i].EndpointType == mongoModel.EndpointType_ReplicaSet) {
			for j := range resp.DBEndpoints[i].DBAddresses {
				// 原来是只取主节点连接，需要换成取传入的nodeId连接
				// if resp.DBEndpoints[i].DBAddresses[j].AddressType == mongoModel.AddressType_Primary {
				if resp.DBEndpoints[i].DBAddresses[j].NodeId == nodeID {
					//domain = resp.DBEndpoints[i].DBAddresses[j].AddressDomain
					ip = resp.DBEndpoints[i].DBAddresses[j].AddressIP
					port = resp.DBEndpoints[i].DBAddresses[j].AddressPort
					break
				}
			}
		}
	}

	if ip == "" || port == "" {
		err = fmt.Errorf("can't get mongo %s address, get empty info for addr type %s", instanceID, mongoModel.NetworkType_Private.String())
	}
	log.Info(ctx, "mongoImpl: vpc ip is : %s,port is %s", ip, port)
	return
}
