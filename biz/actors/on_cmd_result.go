package actors

import (
	"runtime/debug"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

	"code.byted.org/infcs/dbw-mgr/biz/consts"

	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/qjpcpu/fp"
)

func (a *SessionActor) onCommandResult(ctx types.Context, msg *shared.CommandResult) {
	if msg.Code == shared.ErrCommandOK {
		a.onCommandResultOK(ctx, msg)
		return
	}
	a.onCommandResultFail(ctx, msg)
}

func (a *SessionActor) onCommandResultFail(ctx types.Context, msg *shared.CommandResult) {
	defer a.releaseConnLock(ctx, msg.ConnectionId)
	ctx.Respond(&shared.OK{})
	a.killQuery(ctx, msg.CommandId)
	cs, err := a.cmdRepo.GetCommandSetByCmdID(ctx, msg.CommandId)
	if err != nil {
		log.Warn(ctx, "get command set by cmd id %s fail %v", msg.CommandId, err)
		return
	}
	now := time.Now().UnixMilli()
	cs.MarkCommandFail(msg.CommandId, now, formatErrorMessage(msg.ErrorMessage)).
		CancelNonTerminated(now, `cancel executed command set`)
	a.cmdRepo.SaveCommandSet(ctx, cs)
	log.Info(ctx, "command %s failed because %s, cancel command set %s", msg.CommandId, msg.ErrorMessage, cs.ID)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Warn(ctx, "CreateConsoleRecordBySQLExecute func panic, err=%v, stack=%s", r, string(debug.Stack()))
			}
		}()
		//获取本次connection对应的DB名称
		DbName := a.state.DataSource.Db
		for _, c := range a.state.Conn {
			if c.ID == msg.ConnectionId {
				DbName = c.CurrentDB
				break
			}
		}
		//保存操作审计记录
		a.OperateRecordService.CreateConsoleRecordBySQLExecute(ctx, msg.CommandId, model.ConsoleOperationStatus_FAILED, a.state.DataSource, DbName)
	}()

	if isConnectionBroken(msg.ErrorMessage) {
		a.closeSessionConn(ctx, msg.ConnectionId)
	}
}

func (a *SessionActor) releaseConnLock(ctx types.Context, connID string) {
	delete(a.state.ConnLock, connID)
}

func (a *SessionActor) killQuery(ctx types.Context, connID string) {
	if conn := a.state.getConn(connID); conn != nil {
		if err := a.sources.KillQuery(
			ctx,
			a.state.DataSource,
			&shared.ConnectionInfo{
				ConnectionId:      connID,
				OuterConnectionId: conn.OuterID},
		); err != nil {
			log.Warn(ctx, "kill conn %s error %s", err)
		}
	}
}

func (a *SessionActor) onCommandResultOK(ctx types.Context, msg *shared.CommandResult) {
	if a.isFirstCommandResult(ctx, msg) {
		err := a.PriSvc.UpdateCurExecuteCount(ctx, a.state.DataSource)
		if err != nil {
			log.Warn(ctx, "UpdateCurExecuteCount fail %v", err)
			//result := &shared.CommandResult{
			//	CommandId:    msg.CommandId,
			//	Code:         shared.ErrBadCommand,
			//	ConnectionId: msg.ConnectionId,
			//	ErrorMessage: err.Error(),
			//}
			//ctx.Send(ctx.Self(), result)
		}
		a.onCommandMeta(ctx, msg)
	}
	/* write command result and update current result count, crRepo result should before cmdRepo state */
	a.memoCommandResult(ctx, msg)
	// when result write DB, this call is finished
	ctx.Respond(&shared.OK{})

	if a.isLastCommandResult(ctx, msg) {
		a.onCommandFinished(ctx, msg)
	}
}

func (a *SessionActor) memoCommandResult(ctx types.Context, msg *shared.CommandResult) {
	now := time.Now().Unix() * 1000
	var records []*entity.CommandResult
	fp.StreamOf(msg.Payload).
		FlatMap(func(chunk *shared.CommandResultChunk) fp.Stream {
			return fp.StreamOf(chunk.Rows).
				Reject(func(r *shared.CommandResultChunk_Row) bool {
					return r == nil || len(r.Cells) == 0
				}).
				Map(func(r *shared.CommandResultChunk_Row) *entity.CommandResult {
					return &entity.CommandResult{
						SessionID:      a.GetSessionID(ctx),
						SessionExpired: false,
						ConnectionID:   msg.ConnectionId,
						CommandID:      msg.CommandId,
						ChunkOffset:    chunk.Offset,
						Chunk:          r,
						CreateTimeMS:   now,
					}
				})
		}).
		ToSlice(&records)
	a.crRepo.BulkCreate(ctx, records)
	if len(records) != 0 {
		err := a.PriSvc.UpdateCurResultCount(ctx, len(records), a.state.DataSource)
		if err != nil {
			log.Warn(ctx, "UpdateCurExecuteCount fail %v", err)
			//result := &shared.CommandResult{
			//	CommandId:    msg.CommandId,
			//	Code:         shared.ErrBadCommand,
			//	ConnectionId: msg.ConnectionId,
			//	ErrorMessage: err.Error(),
			//}
			//ctx.Send(ctx.Self(), result)
		}
	}
	log.Info(ctx, "write %d command result", len(records))
}

func (a *SessionActor) isLastCommandResult(ctx types.Context, msg *shared.CommandResult) bool {
	return fp.StreamOf(msg.Payload).
		Map(func(chunk *shared.CommandResultChunk) bool {
			return chunk.HasMore
		}).
		Filter(func(hasMore bool) bool {
			return !hasMore
		}).
		Exists()
}

func (a *SessionActor) isFirstCommandResult(ctx types.Context, msg *shared.CommandResult) bool {
	return fp.StreamOf(msg.Payload).
		Filter(func(chunk *shared.CommandResultChunk) bool {
			return chunk.Offset == 0
		}).
		Exists()
}

func (a *SessionActor) onCommandMeta(ctx types.Context, msg *shared.CommandResult) {
	cs, err := a.cmdRepo.GetCommandSetByCmdID(ctx, msg.CommandId)
	if err != nil {
		log.Warn(ctx, "get command set by cmd id %s fail %v", msg.CommandId, err)
		return
	}
	if len(msg.Payload) > 0 {
		cs.SetCommandResultMeta(msg.CommandId, entity.TableResult, msg.Payload[0].Header)
		cs.SetCommandConnCurrentDB(msg.CommandId, a.getConnCurrentDB(ctx, msg.ConnectionId))
		a.cmdRepo.SaveCommandSet(ctx, cs)
	}
}

func (a *SessionActor) onCommandFinished(ctx types.Context, msg *shared.CommandResult) {
	cs, err := a.cmdRepo.GetCommandSetByCmdID(ctx, msg.CommandId)
	if err != nil {
		log.Warn(ctx, "get command set by cmd id %s fail %v", msg.CommandId, err)
		return
	}
	if cs.GetCommand(msg.CommandId).State != entity.CommandExecuting {
		return
	}
	if err = a.checkResultNum(ctx, msg); err != nil {
		log.Error(ctx, "The number of results obtained is not as expected, cmd id is %s, err: %v", msg.CommandId, err)
		return
	}
	now := time.Now().UnixMilli()
	cs.MarkCommandSuccess(msg.CommandId, now).UpdateProgress(now)
	if next := cs.NextNonTerminatedCommand(); next != nil {
		if next.State == entity.CommandPending {
			// add check
			ret := &shared.CheckSessionSuccess{
				SessionId: a.GetSessionID(ctx),
				Source:    a.state.DataSource,
			}
			fp.StreamOf(a.state.Conn).
				Map(func(c *Connection) *shared.SessionConnectionInfo {
					return &shared.SessionConnectionInfo{
						Id:        c.ID,
						Name:      c.Name,
						CurrentDb: c.CurrentDB,
					}
				}).Filter(func(info *shared.SessionConnectionInfo) bool {
				return info.Id == msg.ConnectionId
			}).ToSlice(&ret.ConnectionList)
			for _, Connection := range ret.ConnectionList {
				a.state.DataSource.Db = Connection.CurrentDb
			}
			err = a.PriSvc.SecurityCheck(ctx, next.Content, a.state.DataSource, a.state.IgnoreSecurityCheck, model.SqlExecutionType_SqlQuery)
			if err != nil {
				log.Warn(ctx, "SecurityCheck error:%s", err.Error())
				result := &shared.CommandResult{
					CommandId:    next.ID,
					Code:         shared.ErrBadCommand,
					ConnectionId: msg.ConnectionId,
					ErrorMessage: err.Error(),
				}
				ctx.Send(ctx.Self(), result)
				_ = ctx.ClientOf(consts.ConnectionActorKind).Send(ctx, msg.ConnectionId, &shared.ForceRollback{})
			} else {
				// 判断如何提交sql：1.connection 2.task 3.ticket
				switch next.Extra["SqlExecuteType"] {
				case model.SqlExecuteType_Connection.String():
					a.submitToConnection(ctx, next, msg.ConnectionId)
				case model.SqlExecuteType_Task.String():
					taskId := a.submitToOnlineDDLTask(ctx, next, msg.ConnectionId)
					next.Extra[SQLTaskID] = taskId
				default:
					a.submitToConnection(ctx, next, msg.ConnectionId)
				}
			}
			cs.MarkCommandExecuting(next.ID, now)
		}
	} else {
		/* well done, all commnad finished */
		a.releaseConnLock(ctx, msg.ConnectionId)
		ctx.SaveState()
		log.Info(ctx, "conn lock released:%s", msg.ConnectionId)
	}
	a.cmdRepo.SaveCommandSet(ctx, cs)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Warn(ctx, "CreateConsoleRecordBySQLExecute func panic, err=%v, stack=%s", r, string(debug.Stack()))
			}
		}()
		//获取本次connection对应的DB名称
		DbName := a.state.DataSource.Db
		for _, c := range a.state.Conn {
			if c.ID == msg.ConnectionId {
				DbName = c.CurrentDB
				break
			}
		}
		//保存操作审计记录
		a.OperateRecordService.CreateConsoleRecordBySQLExecute(ctx, msg.CommandId, model.ConsoleOperationStatus_SUCCESS, a.state.DataSource, DbName)
	}()

	log.Info(ctx, "command %s/%s finished, progress %v%%", cs.ID, msg.CommandId, cs.Progress)
}

func (a *SessionActor) checkResultNum(ctx types.Context, msg *shared.CommandResult) error {
	lastOffset := fp.StreamOf(msg.Payload).
		Filter(func(chunk *shared.CommandResultChunk) bool {
			return !chunk.HasMore
		}).
		Map(func(chunk *shared.CommandResultChunk) int64 {
			return chunk.Offset
		}).First().Int64()
	gotResultNum := a.crRepo.Count(ctx, msg.CommandId)
	log.Info(ctx, "got %d result find %d result in database", lastOffset, gotResultNum)
	//if lastOffset != gotResultNum {
	//	return errors.New("the number of results obtained is not as expected")
	//}
	return nil
}

func (a *SessionActor) getConnCurrentDB(ctx types.Context, connID string) (dbName string) {
	dbName = a.state.DataSource.Db
	for _, c := range a.state.Conn {
		if c.ID == connID {
			dbName = c.CurrentDB
			return
		}
	}
	return
}

func formatErrorMessage(errmsg string) string {
	if isConnectionBroken(errmsg) {
		return `执行时间超过工作台限制`
	}
	return errmsg
}

func isConnectionBroken(errmsg string) bool {
	return strings.Contains(errmsg, `invalid connection`) ||
		strings.Contains(errmsg, `connection is already closed`) ||
		strings.Contains(errmsg, `driver: bad connection`)
}
