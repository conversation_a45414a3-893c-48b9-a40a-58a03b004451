package actors

import (
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/operate_record"
	"code.byted.org/infcs/dbw-mgr/biz/service/sqltask"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/protoactor-go/actor"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

type NewSessionActorIn struct {
	dig.In
	Config               config.ConfigProvider
	Location             location.Location
	DataSource           datasource.DataSourceService
	IDSvc                idgen.Service
	ShuttleSvc           shuttle.PGWShuttleService
	CmdRepo              repository.CommandRepo
	CrRepo               repository.CommandResultRepo
	MigRepo              repository.MigrationRepo
	DbwInstanceDal       dal.DbwInstanceDAL
	PriSvc               usermgmt.PrivilegeServiceInterface
	ActorClient          cli.ActorClient
	SqlTaskSvc           sqltask.SqlTaskService
	C3ConfProvider       c3.ConfigProvider
	OperateRecordService operate_record.OperateRecordService
}

func NewSessionActor(p NewSessionActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.SessionActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &SessionActor{
				state:                newSessionState(state),
				loc:                  p.Location,
				cnf:                  p.Config,
				sources:              p.DataSource,
				idSvc:                p.IDSvc,
				ShuttleSvc:           p.ShuttleSvc,
				cmdRepo:              p.CmdRepo,
				crRepo:               p.CrRepo,
				MigRepo:              p.MigRepo,
				DbwInstanceDal:       p.DbwInstanceDal,
				PriSvc:               p.PriSvc,
				actorCli:             p.ActorClient,
				sqlTaskSvc:           p.SqlTaskSvc,
				c3ConfProvider:       p.C3ConfProvider,
				OperateRecordService: p.OperateRecordService,
			}
		}),
	}
}

type SessionActor struct {
	state *sessionState
	/* dependency */
	loc                  location.Location
	cnf                  config.ConfigProvider
	sources              datasource.DataSourceService
	idSvc                idgen.Service
	ShuttleSvc           shuttle.PGWShuttleService
	cmdRepo              repository.CommandRepo
	crRepo               repository.CommandResultRepo
	MigRepo              repository.MigrationRepo
	DbwInstanceDal       dal.DbwInstanceDAL
	PriSvc               usermgmt.PrivilegeServiceInterface
	actorCli             cli.ActorClient
	sqlTaskSvc           sqltask.SqlTaskService
	c3ConfProvider       c3.ConfigProvider
	OperateRecordService operate_record.OperateRecordService
}

const (
	sessionTimeIntervalSeconds int64 = 10
)

func (a *SessionActor) Process(ctx types.Context) {
	if !a.ensureState(ctx) {
		return
	}
	defer ctx.SetReceiveTimeout(time.Duration(sessionTimeIntervalSeconds) * time.Second)
	defer a.resetIdlePeriod(ctx) // 重置session的空闲时间.(除了ReceiveTimeout和Ping消息之外)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		a.repairState(ctx)
	case *actor.Stopping:
		a.unregisterMySelf(ctx, a.state.DataSource)
	case *shared.CheckSession:
		a.checkSession(ctx, msg)
	case *shared.CreateSession:
		a.createSession(ctx, msg)
	case *shared.CreateInternalSession:
		a.createInternalSession(ctx, msg)
	case *shared.CheckInternalSession:
		a.checkInternalSession(ctx, msg)
	case *shared.CloseSession:
		a.closeSession(ctx, msg)
	case *shared.NewSessionConnection:
		a.createSessionConn(ctx, msg)
	case *shared.CloseSessionConnection:
		a.closeSessionConn(ctx, msg.ConnectionId)
	case *shared.Ping:
		a.pingSession(ctx)
	case *actor.ReceiveTimeout:
		a.pollingSqlTask(ctx)
		a.doWhenIdle(ctx)
	case *shared.ExecuteCommand:
		a.executeCommand(ctx, msg)
	case *shared.CancelCommand:
		a.cancelCommnad(ctx, msg)
		/* sychronized calls */
	case *shared.ListDatabase:
		a.protectUserCall(ctx, func() {
			a.listDatabase(ctx, msg)
		})
	case *shared.ListTable:
		a.protectUserCall(ctx, func() {
			a.listTables(ctx, msg)
		})
	case *shared.ListAllTable:
		a.protectUserCall(ctx, func() {
			a.listAllTables(ctx, msg)
		})
	case *shared.ListSchemaTables:
		a.protectUserCall(ctx, func() {
			a.listSchemaTables(ctx, msg)
		})
	case *shared.ListTablesInfo:
		a.protectUserCall(ctx, func() {
			a.listTablesInfo(ctx, msg)
		})
	case *shared.DescribeTable:
		a.protectUserCall(ctx, func() {
			a.describeTable(ctx, msg)
		})
	case *shared.DescribePgTable:
		a.protectUserCall(ctx, func() {
			a.describePgTable(ctx, msg)
		})
	case *shared.ListTableSpaces:
		a.protectUserCall(ctx, func() {
			a.listTableSpaces(ctx, msg)
		})
	case *shared.ListPgCollations:
		a.protectUserCall(ctx, func() {
			a.listPgCollations(ctx, msg)
		})
	case *shared.ListPgUsers:
		a.protectUserCall(ctx, func() {
			a.listPgUsers(ctx, msg)
		})
	case *shared.ListView:
		a.protectUserCall(ctx, func() {
			a.listViews(ctx, msg)
		})
	case *shared.DescribeView:
		a.protectUserCall(ctx, func() {
			a.describeView(ctx, msg)
		})
	case *shared.DescribeFunction:
		a.protectUserCall(ctx, func() {
			a.describeFunction(ctx, msg)
		})
	case *shared.DescribeProcedure:
		a.protectUserCall(ctx, func() {
			a.describeProcedure(ctx, msg)
		})

	case *shared.ListFunction:
		a.protectUserCall(ctx, func() {
			a.listFunctions(ctx, msg)
		})
	case *shared.ListProcedure:
		a.protectUserCall(ctx, func() {
			a.listProcedures(ctx, msg)
		})
	case *shared.ListTrigger:
		a.protectUserCall(ctx, func() {
			a.listTriggers(ctx, msg)
		})
	case *shared.ListKeyNumbers:
		a.protectUserCall(ctx, func() {
			a.listKeyNumbers(ctx, msg)
		})
	case *shared.ListKeyMembers:
		a.protectUserCall(ctx, func() {
			a.listKeyMembers(ctx, msg)
		})
	case *shared.ListKeys:
		a.protectUserCall(ctx, func() {
			a.listKeys(ctx, msg)
		})
	case *shared.ListAlterKVsCommands:
		a.protectUserCall(ctx, func() {
			a.listAlterKVsCommands(ctx, msg)
		})
	case *shared.DescribeEvent:
		a.protectUserCall(ctx, func() {
			a.describeEvent(ctx, msg)
		})
	case *shared.ListEvents:
		a.protectUserCall(ctx, func() {
			a.listEvents(ctx, msg)
		})
	case *shared.ChangeDB:
		a.changeDB(ctx, msg)
	case *shared.DescribeTrigger:
		a.protectUserCall(ctx, func() {
			a.describeTrigger(ctx, msg)
		})
	case *shared.GetAdvice:
		a.protectUserCall(ctx, func() {
			a.getAdvice(ctx, msg)
		})
	case *shared.ListCharsets:
		a.protectUserCall(ctx, func() {
			a.listCharsets(ctx, msg)
		})
	case *shared.ListCollations:
		a.protectUserCall(ctx, func() {
			a.listCollations(ctx, msg)
		})
	case *shared.DescribeDialogDetails:
		a.protectUserCall(ctx, func() {
			a.describeDialogDetails(ctx, msg)
		})
	case *shared.DescribeDialogStatistics:
		a.protectUserCall(ctx, func() {
			a.describeDialogStatistics(ctx, msg)
		})
	case *shared.ListSchema:
		a.protectUserCall(ctx, func() {
			a.listSchema(ctx, msg)
		})
	case *shared.ListSequence:
		a.protectUserCall(ctx, func() {
			a.listSequence(ctx, msg)
		})
	case *shared.KillProcess:
		a.protectUserCall(ctx, func() {
			a.killProcess(ctx, msg)
		})
	case *shared.DescribeEngineStatus:
		a.protectUserCall(ctx, func() {
			a.describeEngineStatus(ctx, msg)
		})
	case *shared.CreateDbExportTaskReq:
		a.protectUserCall(ctx, func() {
			log.Info(ctx, "Receive CreateDbExportTaskReq is %s", msg)
			a.CreateDbExportTask(ctx, msg)
		})
	case *shared.CreateDbImportTaskReq:
		a.protectUserCall(ctx, func() {
			log.Info(ctx, "Receive CreateDbExportTaskReq is %s", msg)
			a.CreateDbImportTask(ctx, msg)
		})
		log.Info(ctx, "sesssion actor response for import task")
	case *shared.ExecuteCCLReq:
		a.protectUserCall(ctx, func() {
			a.ExecuteCCL(ctx, msg)
		})
	case *shared.CCLShow:
		a.protectUserCall(ctx, func() {
			a.CCLShow(ctx, msg)
		})
		/* callback trx and lock*/
	case *shared.DescribeTrxAndLocks:
		a.protectUserCall(ctx, func() {
			a.describeTrxAndLocks(ctx, msg)
		})
	case *shared.DescribeDeadlock:
		a.protectUserCall(ctx, func() {
			a.describeDeadlock(ctx, msg)
		})
	case *shared.DescribeDeadlockDetect:
		a.protectUserCall(ctx, func() {
			a.describeDeadlockDetect(ctx, msg)
		})
	case *shared.DescribeDialogInfos:
		a.protectUserCall(ctx, func() {
			a.describeDialogInfos(ctx, msg)
		})
	case *shared.ListCollections:
		a.protectUserCall(ctx, func() {
			a.listCollections(ctx, msg)
		})
	case *shared.ListIndexs:
		a.protectUserCall(ctx, func() {
			a.listIndexs(ctx, msg)
		})
	case *shared.ListMongoDBs:
		a.protectUserCall(ctx, func() {
			a.listMongoDBs(ctx, msg)
		})

		/* callback events */
	case *shared.ConnectionClosed:
		a.onConnClosed(ctx, msg.ConnectionId)
	case *shared.CommandResult:
		a.onCommandResult(ctx, msg)
	case *shared.PollingSqlTask:
		a.pollingSqlTask(ctx)
	case *shared.Inspect:
		ctx.Respond(&shared.ActorStateInfo{State: string(a.GetState())})
	case *shared.ConnectionInfo:
		// use db
		a.onConnInfoUpdated(ctx, msg)

		/* shuttle mgr*/
	case *shuttle.PingActor:
		a.onPingActor(ctx, msg)

		/* diagnosis*/
	case *shared.DescribeTableSpace:
		a.protectUserCall(ctx, func() {
			a.describeTableSpace(ctx, msg)
		})
	case *shared.DescribeTableColumn:
		a.protectUserCall(ctx, func() {
			a.describeTableColumn(ctx, msg)
		})
	case *shared.DescribeTableIndex:
		a.protectUserCall(ctx, func() {
			a.describeTableIndex(ctx, msg)
		})
		/* callback tickets */
	case *shared.ExplainCommandReq:
		a.protectUserCall(ctx, func() {
			a.explainCommand(ctx, msg)
		})
	case *shared.GetIndexInfoReq:
		a.protectUserCall(ctx, func() {
			a.getIndexInfo(ctx, msg)
		})
	case *shared.GetIndexValueReq:
		a.protectUserCall(ctx, func() {
			a.getIndexValue(ctx, msg)
		})
	case *shared.ConnectionRestart:
		// conn重启了，就移除ConnLock
		a.protectUserCall(ctx, func() {
			a.removeConnLock(ctx, msg)
		})

	case *shared.ValidateDryRun:
		a.protectUserCall(ctx, func() {
			a.ValidateDryRun(ctx, msg)
		})
	case *shared.ValidateOriginalTable:
		a.protectUserCall(ctx, func() {
			a.ValidateOriginalTable(ctx, msg)
		})
	case *shared.ValidateUniqIndex:
		a.protectUserCall(ctx, func() {
			a.ValidateUniqIndex(ctx, msg)
		})
	case *shared.GetTableSpace:
		a.protectUserCall(ctx, func() {
			a.GetTableSpace(ctx, msg)
		})
	}
}

func (a *SessionActor) removeConnLock(ctx types.Context, msg *shared.ConnectionRestart) {
	log.Warn(ctx, "connId:%s connection restart", msg.ConnectionId)
	if msg != nil && a.state.ConnLock != nil {
		delete(a.state.ConnLock, msg.ConnectionId)
	}

}

func (a *SessionActor) ProcessWhenDead(ctx types.Context) {
	log.Info(ctx, "receive message %#v when dead", ctx.Message())
	switch msg := ctx.Message().(type) {
	case *shared.ConnectionClosed:
		a.onConnClosed(ctx, msg.ConnectionId)
		return
	case *actor.Started:
		a.onPingActor(ctx, nil)
		return
	}
	msg := ctx.Message()
	if types.IsUserMessage(msg) {
		log.Info(ctx, "session %s already released,but got message %#v", ctx.GetName(), msg)
		ctx.Respond(&shared.SessionNotExist{})
	}
}

func (a *SessionActor) ensureState(ctx types.Context) bool {
	msg := ctx.Message()
	if !types.IsUserMessage(msg) {
		return true
	}
	if a.state.SessionCreated {
		if a.shouldEnsureTenant(ctx) && !a.validTenant(ctx) {
			ctx.Respond(&shared.SessionAccessDenied{})
			return false
		}
		return true
	}

	switch ctx.Message().(type) {
	case *shared.CreateSession:
		return true
	case *shared.CreateInternalSession:
		return true
	}
	ctx.Respond(&shared.SessionNotExist{})
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	return false
}

func (a *SessionActor) shouldEnsureTenant(ctx types.Context) bool {
	// FIXME: add User Action here
	switch ctx.Message().(type) {
	case *shared.CheckSession:
	case *shared.CloseSession:
	case *shared.NewSessionConnection:
	case *shared.CloseSessionConnection:
	case *shared.Ping:
	case *shared.ExecuteCommand, *shared.CancelCommand:
	case *shared.ListDatabase:
	case *shared.ListTable:
	case *shared.DescribeTable:
	case *shared.DescribePgTable:
	case *shared.ListTableSpaces:
	case *shared.ListPgUsers:
	case *shared.ListPgCollations:
	case *shared.DescribeView:
	case *shared.ListView:
	case *shared.ListFunction:
	case *shared.ListProcedure:
	case *shared.ListTrigger:
	case *shared.ListKeys:
	case *shared.ListKeyMembers:
	case *shared.ListKeyNumbers:
	case *shared.ListSchema:
	case *shared.ListSequence:
	default:
		return false
	}
	return true
}

func (a *SessionActor) validTenant(ctx types.Context) bool {
	msgTenant := fwctx.GetTenantID(ctx)
	if msgTenant == `0` || msgTenant == `1` {
		return true
	}
	if msgTenant != "" && msgTenant != a.state.TenantID {
		log.Warn(ctx, "tenant %s in message not match session tenant %s", msgTenant, a.state.TenantID)
		return false
	}
	return true
}

// session空闲的时候做的一些事情
func (a *SessionActor) doWhenIdle(ctx types.Context) {
	a.state.increaseIdlePeriod(sessionTimeIntervalSeconds)
	if currentTenant := fwctx.GetTenantID(ctx); currentTenant == "" {
		ctx.WithValue(fwctx.BIZ_CONTEXT_KEY, fwctx.NewBizContext())
		if c := fwctx.GetBizContext(ctx); c != nil {
			c.TenantID = a.state.TenantID
			c.UserID = a.state.UserID
		}
	}
	if a.state.isIdleTimeout() {
		log.Info(ctx, "connection timeout, i should be closed now")
		a.closeSession(ctx, &shared.CloseSession{})
		return
	}
	if !a.state.SessionCreated {
		log.Info(ctx, "session creating failed, we're closing now")
		a.closeSession(ctx, &shared.CloseSession{})
	}
}

func (a *SessionActor) resetIdlePeriod(ctx types.Context) {
	/* clear idle period */
	switch ctx.Message().(type) {
	case *actor.ReceiveTimeout:
	case *shuttle.PingActor:
	default:
		a.state.resetIdlePeriod()
	}
}

func (a *SessionActor) GetSessionID(ctx types.Context) string {
	return ctx.GetName()
}

func (a *SessionActor) GetState() []byte {
	return a.state.Bytes()
}

func (a *SessionActor) repairState(ctx types.Context) {
	if a.state.MyIP == "" {
		a.state.MyIP = a.loc.HostIP()
		log.Info(ctx, "set my ip %s", a.state.MyIP)
		return
	}
	log.Info(ctx, "session started: register shuttle.")
	/* update shuttle */
	if a.state.DataSource != nil {
		if err := a.registerMySelf(ctx, a.state.DataSource); err != nil {
			log.Warn(ctx, "register shuttle mgr fail %v ", err)
		}
	}
	if a.state.MyIP != a.loc.HostIP() {
		// TODO: 更新shuttle for connector!!!
		log.Info(ctx, "session location changed, new location %s", a.loc.HostIP())
		a.state.MyIP = a.loc.HostIP()
		return
	}
}

func (a *SessionActor) checkSession(ctx types.Context, msg *shared.CheckSession) {
	if msg.ConnectionId == "" || a.state.containsConn(msg.ConnectionId) {
		ret := &shared.CheckSessionSuccess{
			SessionId: a.GetSessionID(ctx),
			Source:    a.state.DataSource,
		}
		fp.StreamOf(a.state.Conn).
			Map(func(c *Connection) *shared.SessionConnectionInfo {
				return &shared.SessionConnectionInfo{
					Id:        c.ID,
					Name:      c.Name,
					CurrentDb: c.CurrentDB,
				}
			}).
			ToSlice(&ret.ConnectionList)
		ctx.Respond(ret)
		return
	}
	ctx.Respond(&shared.SessionNotExist{})
}

func (a *SessionActor) getConnectionActorName(ctx types.Context, connID string) string {
	return connID
}

func (a *SessionActor) explainCommand(ctx types.Context, msg *shared.ExplainCommandReq) {
	src := a.state.DataSource
	src.Db = msg.DB

	req := &datasource.ExplainCommandReq{
		Source:  src,
		Command: msg.SQLText,
	}

	resp, err := a.sources.ExplainCommand(ctx, req)
	if err != nil {
		log.Warn(ctx, "获取工单SQL语句影响行数信息失败 %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	log.Info(ctx, "explain command success,resp is %s", utils.Show(resp))
	var res []*shared.ExplainCommandResult
	for _, val := range resp.Command {
		commandResItem := &shared.ExplainCommandResult{
			Id:           val.Id,
			SelectType:   val.SelectType,
			Table:        val.Table,
			Partitions:   val.Partitions,
			Type:         val.Type,
			PossibleKeys: val.PossibleKeys,
			Key:          val.Key,
			KeyLen:       val.KeyLen,
			Ref:          val.Ref,
			Rows:         val.Rows,
			Filtered:     val.Filtered,
			Extra:        val.Extra,
		}
		res = append(res, commandResItem)
	}
	ctx.Respond(&shared.ExplainCommandResp{
		CommandRes: res,
	})
}

func (a *SessionActor) getIndexInfo(ctx types.Context, msg *shared.GetIndexInfoReq) {
	src := a.state.DataSource
	src.Db = msg.DB

	req := &datasource.GetTableIndexInfoReq{
		TableName: msg.TableName,
		Source:    src,
		Command:   msg.Command,
	}
	resp, err := a.sources.GetTableIndexInfo(ctx, req)
	if err != nil {
		log.Warn(ctx, "获取工单SQL语句索引信息失败 %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	log.Info(ctx, "get table index info success,resp is %s", utils.Show(resp))
	var res []*shared.TableIndexInfo
	for _, val := range resp.TableIndexInfo {
		item := &shared.TableIndexInfo{
			TableName:    val.TableName,
			IndexName:    val.IndexName,
			Nullable:     val.Nullable,
			SeqInIndex:   0,
			IndexType:    val.IndexType,
			ColumnName:   val.ColumnName,
			SubPart:      val.SubPart,
			IndexComment: val.IndexComment,
		}
		res = append(res, item)
	}
	ctx.Respond(&shared.GetIndexInfoResp{
		TableIndexInfo: res,
	})
}

func (a *SessionActor) getIndexValue(ctx types.Context, msg *shared.GetIndexValueReq) {
	src := a.state.DataSource
	src.Db = msg.Source.Db

	req := &datasource.GetIndexValueReq{
		TableName: msg.TableName,
		Source:    src,
		Command:   msg.Command,
		Columns:   msg.Columns,
	}
	resp, err := a.sources.GetTableIndexValue(ctx, req)
	if err != nil {
		log.Warn(ctx, "获取工单SQL语句索引信息失败 %v", err)
		ctx.Respond(&shared.DataSourceOpFailed{
			Error:        shared.UnknownOpError,
			ErrorMessage: err.Error(),
		})
		return
	}
	log.Info(ctx, "get table index info success,resp is %s", utils.Show(resp))
	var res []*shared.TableIndexValue
	for _, val := range resp.TableIndexValue {
		item := &shared.TableIndexValue{
			IndexName:  val.IndexName,
			IndexValue: val.IndexValue,
		}
		res = append(res, item)
	}
	ctx.Respond(&shared.GetIndexValueResp{
		TableIndexValue: res,
	})
}

func (a *SessionActor) ValidateDryRun(ctx types.Context, msg *shared.ValidateDryRun) {
	req := &datasource.ValidateDryRunReq{Type: msg.InstanceType, Source: a.state.DataSource, SqlList: msg.SqlList}
	res := a.sources.ValidateDryRun(ctx, req)
	ctx.Respond(res)
}

func (a *SessionActor) ValidateOriginalTable(ctx types.Context, msg *shared.ValidateOriginalTable) {
	req := &datasource.ValidateOriginalTableReq{Type: msg.InstanceType, Source: a.state.DataSource, SqlList: msg.SqlList}
	res := a.sources.ValidateOriginalTable(ctx, req)
	ctx.Respond(res)
}

func (a *SessionActor) ValidateUniqIndex(ctx types.Context, msg *shared.ValidateUniqIndex) {
	req := &datasource.ValidateUniqIndexReq{Type: msg.InstanceType, Source: a.state.DataSource, IdxInfos: msg.IndexInfos}
	res := a.sources.ValidateUniqIndex(ctx, req)
	ctx.Respond(res)
}

func (a *SessionActor) GetTableSpace(ctx types.Context, msg *shared.GetTableSpace) {
	maxSpace := int64(0)
	totalSpace := int64(0)
	resp := &shared.GetTableSpaceResp{Success: false}
	for _, table := range msg.TableNames {
		DescribeTableSpaceMsg := &shared.DescribeTableSpace{
			Product:    int64(msg.InstanceType),
			InstanceId: msg.InstanceId,
			Database:   a.state.DataSource.Db,
			TableName:  table,
		}
		req := a.getDescribeTableSpaceReq(DescribeTableSpaceMsg)
		tableSpaceResp, err := a.sources.DescribeTableSpace(ctx, req)
		if err != nil {
			log.Warn(ctx, "DescribeTableSpace error:%s", err.Error())
			resp.ErrMsg = err.Error()
			ctx.Respond(resp)
			return
		}
		if len(tableSpaceResp.TableStats) > 0 {
			if maxSpace < int64(tableSpaceResp.TableStats[0].TableSpace) {
				maxSpace = int64(tableSpaceResp.TableStats[0].TableSpace)
			}
			totalSpace += int64(tableSpaceResp.TableStats[0].TableSpace)
		}
	}
	resp.TotalSpace = totalSpace
	resp.MaxSpace = maxSpace
	resp.Success = true
	ctx.Respond(resp)
}
