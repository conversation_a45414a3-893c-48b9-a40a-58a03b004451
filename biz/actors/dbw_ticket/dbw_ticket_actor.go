package dbw_ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	dbw_ticket_service "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"fmt"
	"go.uber.org/dig"
	"runtime/debug"
)

type DbwTicketActorIn struct {
	dig.In
	Conf          config.ConfigProvider
	TicketService dbw_ticket_service.DbwTicketService
	TicketRepo    repository.TicketRepo
}

func NewDbwTicketActor(p DbwTicketActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.DbwTicketActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &DbwTicketActor{
				state:         newDbwTicketActorState(state),
				cnf:           p.Conf,
				ticketService: p.TicketService,
				ticketRepo:    p.TicketRepo,
			}
		}),
	}
}

type DbwTicketActor struct {
	state         *DbwTicketActorState
	cnf           config.ConfigProvider
	ticketService dbw_ticket_service.DbwTicketService
	ticketRepo    repository.TicketRepo

	ticket *entity.Ticket
}

func (d *DbwTicketActor) GetState() []byte {
	state, _ := json.Marshal(d.state)
	return state
}

/**
为了未来整合所有的工单，将所有的工单放入一个表中，我们这里对工单进行功能整合

所有工单都有的操作
基本逻辑：
预检查  创建  修改
通过审批，拒绝审批
取消工单 执行工单  停止工单

时间逻辑：
定时执行，自动执行

其中审批相关的内容为一致
创建，修改，预检查/ 取消 执行，停止 为各个类型自需，我们将工单类型进行抽象，各自实现其功能，其余功能公共建设，减少代码重复性
*/

func (d *DbwTicketActor) Process(ctx types.Context) {
	d.BuildCtx(ctx)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		d.protectUserCall(ctx, func() {
			d.OnStart(ctx)
		})
	case *shared.SubmitTicket:
		d.protectUserCall(ctx, func() {
			d.SubmitDbwTicket(ctx, msg)
		})
	case *shared.PassDbwTicket:
		d.protectUserCall(ctx, func() {
			// TODO 这一期不做
		})
	case *shared.RejectDbwTicket:
		d.protectUserCall(ctx, func() {
			// TODO 这一期不做
		})
	case *shared.CancelDbwTicket:
		d.protectUserCall(ctx, func() {
			// TODO 这一期不做
		})
	case *shared.ExecuteDbwTicket:
		d.protectUserCall(ctx, func() {
			d.ExecuteTicket(ctx, msg)
		})
	case *shared.StopDbwTicket:
		d.protectUserCall(ctx, func() {
			d.StopTicket(ctx)
		})
	case *shared.FinishedDbwSubTask:
		d.protectUserCall(ctx, func() {
			d.FinishedDbwSubTask(ctx, msg)
		})
	case *actor.Stopped:
		log.Info(ctx, "Actor %s stop", ctx.GetName())
	}
	return
}

func (d *DbwTicketActor) OnStart(ctx types.Context) {
	ticket, err := d.ticketRepo.GetByID(ctx, ctx.GetName())
	if err != nil || ticket == nil || ticket.TicketId == "0" {
		log.Warn(ctx, "get ticket error:%v", err)
		// 没拿到工单信息，直接报错退出
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
	d.ticket = ticket
	log.Info(ctx, "DbwTicketActor: %s start", ticket.TicketId)
}

func (d *DbwTicketActor) BuildCtx(ctx types.Context) {
	// var logID, tenantID, userID string
	var tenantID string
	if d.ticket != nil && d.ticket.TenantId != "" {
		tenantID = d.ticket.TenantId
	}
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: tenantID,
	})
}

// protectUserCall 防止panic之后程序挂掉
func (d *DbwTicketActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call DbwTicketActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (d *DbwTicketActor) ExecuteTicket(ctx types.Context, msg *shared.ExecuteDbwTicket) {
	log.Info(ctx, "start execute sub task")
	if d.state.IsRunning {
		// 避免重复调用
		return
	}
	d.state.IsRunning = true
	hasNext, err := d.ticketService.RunNextTask(ctx, d.state.RunningInfo)
	if err != nil {
		errMsg := fmt.Sprintf("run next sql error:%v", err)
		log.Warn(ctx, errMsg)
		err = d.ticketRepo.UpdateRunningInfo(ctx, &entity.TicketRunningInfo{TicketId: d.ticket.TicketId, TicketStatus: int(model.TicketStatus_TicketError), Description: errMsg})
		if err != nil {
			log.Warn(ctx, "UpdateRunningInfo error:%s", err.Error())
		}
		// 结束actor
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	if !hasNext {
		// 没有任务了
		err = d.ticketRepo.UpdateRunningInfo(ctx, &entity.TicketRunningInfo{TicketId: d.ticket.TicketId, TicketStatus: int(model.TicketStatus_TicketFinished), Description: "", Process: 100})
		if err != nil {
			log.Warn(ctx, "UpdateRunningInfo error:%s", err.Error())
		}
		// 结束actor
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}

func (d *DbwTicketActor) SubmitDbwTicket(ctx types.Context, msg *shared.SubmitTicket) {
	runningInfo := d.ticketService.InitRunningInfo(ctx, &dbw_ticket_service.InitRunningInfo{Ticket: d.ticket, TicketType: msg.TicketType})
	d.state.RunningInfo = runningInfo
	err := d.ticketRepo.SubmitTicket(ctx, msg.TicketId)
	if err != nil {
		log.Warn(ctx, "SubmitTicket error:%s", err.Error())
		ctx.Respond(&shared.DbwTicketResponse{Success: false, ErrMsg: "submit ticket error"})
	}
	if runningInfo != nil {
		log.Info(ctx, "running info:%s", utils.Show(runningInfo))
	} else {
		log.Info(ctx, "running info:%v", runningInfo)
	}
	ctx.Respond(&shared.DbwTicketResponse{Success: true, ErrMsg: ""})
}

func (d *DbwTicketActor) FinishedDbwSubTask(ctx types.Context, msg *shared.FinishedDbwSubTask) {
	if !msg.Success {
		err := d.ticketRepo.UpdateRunningInfo(ctx, &entity.TicketRunningInfo{TicketId: d.ticket.TicketId, TicketStatus: int(model.TicketStatus_TicketError), Description: msg.ErrorMsg})
		if err != nil {
			log.Warn(ctx, "UpdateRunningInfo error:%s", err.Error())
		}
		// 结束actor
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	d.state.IsRunning = false
	ctx.SaveState()
	ctx.Send(ctx.Self(), &shared.ExecuteDbwTicket{TicketId: d.ticket.TicketId})
}

func (d *DbwTicketActor) StopTicket(ctx types.Context) {
	// 定时任务，如果没开始，不用走下面逻辑
	if d.state.IsRunning {
		err := d.ticketService.StopTask(ctx, d.state.RunningInfo)
		if err != nil {
			log.Warn(ctx, "stop ticket error: %v", err.Error())
			ctx.Respond(&shared.DbwTicketResponse{Success: false, ErrMsg: "stop ticket error"})
			return
		}
	}
	err := d.ticketRepo.UpdateRunningInfo(ctx, &entity.TicketRunningInfo{TicketId: d.ticket.TicketId, TicketStatus: int(model.TicketStatus_TicketTermination), Description: "user stop ticket"})
	if err != nil {
		log.Warn(ctx, "UpdateRunningInfo error:%s", err.Error())
	}
	ctx.Respond(&shared.DbwTicketResponse{Success: true})
	// 结束actor
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
}
