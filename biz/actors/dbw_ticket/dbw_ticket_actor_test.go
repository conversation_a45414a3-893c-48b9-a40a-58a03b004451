package dbw_ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/repository"
	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/bytedance/mockey"
	"testing"
)

func TestStopTicket(t *testing.T) {
	actor := &DbwTicketActor{
		ticketRepo: &repository.MockTicketRepo{},
	}
	actor.state = &DbwTicketActorState{}
	actor.ticket = &entity.Ticket{}

	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	mock1 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*repository.MockTicketRepo).UpdateRunningInfo).Return(nil).Build()
	defer mock4.UnPatch()

	actor.StopTicket(&mocks.MockContext{})
}
