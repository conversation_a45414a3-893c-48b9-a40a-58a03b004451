package online_ddl

import (
	dbwutils "code.byted.org/infcs/dbw-mgr/biz/utils"
	ddl_model "code.byted.org/infcs/dbw-mgr/gen/ddl-agent/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"encoding/json"
	"fmt"
	"testing"
)

func Test1(t *testing.T) {
	jsonStr := "{\"Data\":{\"ETA\":\"0\",\"Lag\":0.008801368,\"CopiedRows\":59,\"Applied\":0,\"TotalRows\":59,\"ProgressPercent\":100,\"State\":\"Migrating\"},\"Status\":{\"Code\":0,\"Status\":\"ok\",\"Message\":\"\",\"HTTPCode\":200}}"
	resp := &DDlHttpResp{}
	if err := json.Unmarshal([]byte(jsonStr), resp); err != nil {
		log.Warn(context.Background(), "1")
		panic(err)
	}
	respPtr := &ddl_model.GetProgressResp{}
	if err := json.Unmarshal(resp.Data, respPtr); err != nil {
		log.Warn(context.Background(), "2")
		panic(err)
	}
	// fmt.Println(utils.Show(respPtr))

	fmt.Println(dbwutils.DecryptData("@190@240@216@227@157@95@106@101@81", "mysql-7109a6dc390a"))
}
