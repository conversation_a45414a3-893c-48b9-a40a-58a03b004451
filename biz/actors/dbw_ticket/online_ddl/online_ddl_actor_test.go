package online_ddl

import (
	config2 "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/dbw_ticket/online_ddl/runtime"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/mock_dbw_ticket"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
	"time"
)

func mockOnlineDDLActor() *OnlineDDLActor {
	return &OnlineDDLActor{
		state:               &OnlineDDLActorState{},
		ticket:              &entity.Ticket{},
		ticketCommonService: &mock_dbw_ticket.MockTicketCommonService{},
		ddlClient:           &DDLClient{},
		cnf:                 &config.MockConfigProvider{},
		podRuntime:          &runtime.MockRuntime{},
	}
}

func TestDropGhcTable(t *testing.T) {
	actor := mockOnlineDDLActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).Generate6MD5).Return("").Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockContext).GetName).Return("").Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*OnlineDDLActor).doExecuteSql).Return(fmt.Errorf("test")).Build()
	defer mock3.UnPatch()

	actor.dropGhcTable(&mocks.MockContext{})
}

func TestDropGhoTable(t *testing.T) {
	actor := mockOnlineDDLActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).Generate6MD5).Return("").Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockContext).GetName).Return("").Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*OnlineDDLActor).doExecuteSql).Return(fmt.Errorf("test")).Build()
	defer mock3.UnPatch()

	actor.dropGhoTable(&mocks.MockContext{})
}

func TestCheckExecuteTaskStatus(t *testing.T) {
	actor := mockOnlineDDLActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*OnlineDDLActor).finishedTask).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*OnlineDDLActor).dropCopyTable).Return().Build()
	defer mock2.UnPatch()
	mock5 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).Generate6MD5).Return("").Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*mocks.MockContext).GetName).Return("").Build()
	defer mock6.UnPatch()
	mock7 := mockey.Mock((*OnlineDDLActor).dropGhcTable).Return().Build()
	defer mock7.UnPatch()
	mock8 := mockey.Mock((*OnlineDDLActor).dropGhoTable).Return().Build()
	defer mock8.UnPatch()

	mock3 := mockey.Mock((*DDLClient).Call).Return(fmt.Errorf("test")).Build()
	actor.CheckExecuteTaskStatus(&mocks.MockContext{})
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*DDLClient).Call).Return(nil).Build()
	defer mock4.UnPatch()
	actor.CheckExecuteTaskStatus(&mocks.MockContext{})
}

func TestStartTask(t *testing.T) {
	actor := mockOnlineDDLActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*mocks.MockContext).GetName).Return("").Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*OnlineDDLActor).getChunkSize).Return(1).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*OnlineDDLActor).covertTimeFormat).Return("").Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).Generate6MD5).Return("").Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*OnlineDDLActor).formatLoad).Return("", nil).Build()
	defer mock5.UnPatch()
	mock6 := mockey.Mock((*OnlineDDLActor).getThrottleControlReplicas).Return(nil, fmt.Errorf("test")).Build()
	defer mock6.UnPatch()
	mock7 := mockey.Mock((*OnlineDDLActor).finishedTask).Return().Build()
	defer mock7.UnPatch()

	actor.startTask(&mocks.MockContext{}, &shared.ExecuteDbwTicket{})
}

func TestCreateOnlineDDLPod(t *testing.T) {
	actor := mockOnlineDDLActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*mocks.MockContext).GetName).Return("").Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*OnlineDDLActor).getPodName).Return("").Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*runtime.MockRuntime).Create).Return(nil).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*config.MockConfigProvider).Get).Return(&config2.Config{}).Build()
	defer mock4.UnPatch()

	_ = actor.createOnlineDDLPod(&mocks.MockContext{})
}

func TestFormatLoad(t *testing.T) {
	actor := mockOnlineDDLActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	mock1 := mockey.Mock((*mocks.MockContext).GetName).Return("").Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).GetDBDataSource).Return(&shared.DataSource{}, fmt.Errorf("test")).Build()
	defer mock2.UnPatch()

	_, _ = actor.formatLoad(&mocks.MockContext{})

}
