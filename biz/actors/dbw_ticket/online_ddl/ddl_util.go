package online_ddl

import (
	"bytes"
	"code.byted.org/infcs/ds-lib/common/http"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	syshttp "net/http"
	"time"
)

const (
	//ddlClientProduct = "ddlAgent"

	ActionStartTask   = "StartTask"
	ActionPauseTask   = "PauseTask"
	ActionResumeTask  = "ResumeTask"
	ActionStopTask    = "StopTask"
	ActionGetProgress = "GetProgress"

	MaxDiskUsage = 90
)

type DDLClient struct {
	ddlAgentAddr string
	httpClient   http.Client
	TaskId       string
	TicketId     string
}

type ErrInfo struct {
	Status ErrStatus
}

type ErrStatus struct {
	Code     int
	Status   string
	Message  string
	HTTPCode int
}

func NewDDLClient(ddlAgentAddr string, ticketId string, taskId string) *DDLClient {
	ddlClient := &DDLClient{
		ddlAgentAddr: ddlAgentAddr,
		httpClient:   http.NewClient().SetTimeout(30 * time.Second),
		TaskId:       taskId,
		TicketId:     ticketId,
	}
	ddlClient.httpClient.SetRetry(http.RetryOption{RetryMax: 3, RetryWaitMin: time.Second, RetryWaitMax: 2 * time.Second})
	return ddlClient
}

type DDLClientOptional func(*DDLClient)

func (selfClient *DDLClient) Call(ctx context.Context, action string, req interface{}, respPtr interface{}) error {

	bodyBytes, err := utils.Marshal(req)
	if err != nil {
		log.Warn(ctx, "[OnlineDDL] encode error %s", err.Error())
		return err
	}
	uri := fmt.Sprintf("http://%s/ddl/%s", selfClient.ddlAgentAddr, action)
	log.Info(ctx, "call uri:%s action:%s", uri, action)
	httpReq, err := selfClient.formatHttpReq(ctx, uri, bodyBytes)
	if err != nil {
		log.Warn(ctx, "formatHttpReq error:%v", err)
		return err
	}
	httpResp := selfClient.httpClient.DoRequest(httpReq)
	defer func(httpResp *http.Response) {
		if httpResp != nil && httpResp.Body != nil {
			_ = httpResp.Body.Close()
		}
	}(httpResp)

	log.Info(ctx, "pos-xxx %s", utils.Show(httpResp))

	if httpResp.Err != nil || httpResp.StatusCode != 200 {
		return selfClient.dealRequestError(ctx, httpResp)
	}

	bodyResp, err := ioutil.ReadAll(httpResp.Body)
	if err != nil {
		log.Warn(ctx, "read ddl-agent result fail %v", err)
		return err
	}

	//log.Info(ctx, "pos-xxx %s", bodyResp)

	resp := &DDlHttpResp{}
	if err = json.Unmarshal(bodyResp, resp); err != nil {
		log.Warn(ctx, "uri: %s unmarshal ddl-agent result fail %v, result: %s", uri, err, string(bodyResp))
		return err
	}
	if resp.Status.HTTPCode != 200 {
		log.Warn(ctx, "resp: %s", utils.Show(resp))
		return fmt.Errorf(resp.Status.Message)
	}
	if err = json.Unmarshal(resp.Data, respPtr); err != nil {
		log.Warn(ctx, "uri: %s unmarshal ddl-agent data result fail %v, result: %s", uri, err, string(bodyResp))
		return err
	}
	return nil
}

type DDlHttpResp struct {
	Data   json.RawMessage `json:"Data,omitempty"`
	Status Status
}

type Status struct {
	Code     int32
	Status   string
	Message  string
	HTTPCode int64
}

func (selfClient *DDLClient) formatHttpReq(ctx context.Context, uri string, bodyBytes []byte) (*syshttp.Request, error) {
	httpReq, err := syshttp.NewRequest(syshttp.MethodPost, uri, bytes.NewBuffer(bodyBytes))
	if err != nil {
		log.Warn(ctx, "new agent request error: %s", err.Error())
		return nil, fmt.Errorf("inner error: new agent request error")
	}
	httpReq.Header.Set("Content-Type", "application/json; charset=utf-8")

	headerCtx := make(map[string]string)
	headerCtx["TaskId"] = selfClient.TaskId
	headerCtx["TicketId"] = selfClient.TicketId
	headerCtxBytes, _ := json.Marshal(headerCtx)
	httpReq.Header.Set("x-tob-ctx", string(headerCtxBytes))
	return httpReq, nil
}

func (selfClient *DDLClient) dealRequestError(ctx context.Context, httpResp *http.Response) error {
	if httpResp.Err != nil {
		return httpResp.Err
	}
	if httpResp.StatusCode != 200 {
		bodyResp, err := ioutil.ReadAll(httpResp.Body)
		if err != nil {
			log.Warn(ctx, "read ddl-agent result fail %v", err)
			return err
		}
		errInfo := &ErrInfo{}
		if err = json.Unmarshal(bodyResp, errInfo); err != nil {
			log.Warn(ctx, "unmarshal ddl-agent result fail %v, result: %s", err, string(bodyResp))
			return err
		}
		return fmt.Errorf(errInfo.Status.Message)
	}
	return nil
}
