package online_ddl

import (
	"encoding/json"
	"strings"
)

type OnlineDDLActorState struct {
	TicketStatus int
	TicketId     string
	Status       int
	PodAddress   string
	TableName    string
	SqlText      string
	PodCreated   bool
}

const (
	Undo      = 0
	CreatePod = 1
	Execute   = 2
	Pause     = 3
	Finished  = 4
	Error     = 5
)

const (
	DefaultChunkSize = 500
	MaxChunkSize     = 3000
)

const PodPort = 8080

func newOnlineDDLActorState(bytes []byte) *OnlineDDLActorState {
	ts := &OnlineDDLActorState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		return ts
	}
	return ts
}

func EndsWithDel(s string) bool {
	// 1. 去除字符串首尾的空格
	trimmed := strings.TrimSpace(s)

	// 2. 长度不足直接返回 false
	if len(trimmed) < 4 {
		return false
	}

	// 3. 判断是否以 "_del" 结尾
	return strings.HasSuffix(trimmed, "_del")
}
