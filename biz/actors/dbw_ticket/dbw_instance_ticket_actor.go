package dbw_ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	dbw_ticket_service "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"go.uber.org/dig"
	"runtime/debug"
	"time"
)

type DbwInstanceTicketActorIn struct {
	dig.In
	Conf          config.ConfigProvider
	TicketService dbw_ticket_service.DbwTicketService
	TicketRepo    repository.TicketRepo
	ActorClient   cli.ActorClient
	IDSvc         idgen.Service
}

func NewDbwInstanceTicketActor(p DbwInstanceTicketActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.DbwInstanceTicketActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &DbwInstanceTicketActor{
				state:         newDbwInstanceTicketActorState(state),
				cnf:           p.Conf,
				ticketService: p.TicketService,
				ticketRepo:    p.TicketRepo,
				actorClient:   p.ActorClient,
				iDSvc:         p.IDSvc,
			}
		}),
	}
}

type DbwInstanceTicketActor struct {
	state         *DbwInstanceTicketActorState
	cnf           config.ConfigProvider
	ticketService dbw_ticket_service.DbwTicketService
	ticketRepo    repository.TicketRepo
	actorClient   cli.ActorClient
	iDSvc         idgen.Service
}

func (d *DbwInstanceTicketActor) Process(ctx types.Context) {
	ctx.SetReceiveTimeout(time.Duration(5) * time.Second)

	switch msg := ctx.Message().(type) {
	case *actor.Started:
		d.protectUserCall(ctx, func() {
			d.OnStart(ctx)
		})
	case *shared.SubmitTicket:
		d.protectUserCall(ctx, func() {
			d.submitTicket(ctx, msg)
		})
	case *shared.ExecuteDbwTicket:
		d.protectUserCall(ctx, func() {
			d.executeTicket(ctx, msg)
		})
	case *shared.StopDbwTicket:
		d.protectUserCall(ctx, func() {
			d.stopTicket(ctx, msg)
		})
	case *actor.ReceiveTimeout:
		d.protectUserCall(ctx, func() {
			d.checkInstanceTicket(ctx)
		})
	}
}

func (d *DbwInstanceTicketActor) GetState() []byte {
	state, _ := json.Marshal(d.state)
	return state
}

func (d *DbwInstanceTicketActor) OnStart(ctx types.Context) {
	d.state.InstanceId = ctx.GetName()
}

// protectUserCall 防止panic之后程序挂掉
func (d *DbwInstanceTicketActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call DbwTicketActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (d *DbwInstanceTicketActor) submitTicket(ctx types.Context, msg *shared.SubmitTicket) {
	actorResp, err := d.actorClient.KindOf(consts.DbwTicketActorKind).Call(ctx, msg.TicketId, msg)
	if err != nil {
		log.Warn(ctx, "submit ticket error:%v", err)
		ctx.Respond(&shared.DbwTicketResponse{
			Success: false,
			ErrMsg:  "submit ticket error",
		})
		return
	}
	resp, ok := actorResp.(*shared.DbwTicketResponse)
	if !ok {
		log.Warn(ctx, "resp error, resp:%s", actorResp)
		ctx.Respond(&shared.DbwTicketResponse{
			Success: false,
			ErrMsg:  "unKnow inner error",
		})
		return
	}
	ctx.Respond(&shared.DbwTicketResponse{
		Success: resp.Success,
		ErrMsg:  resp.ErrMsg,
	})
}

func (d *DbwInstanceTicketActor) executeTicket(ctx types.Context, msg *shared.ExecuteDbwTicket) {
	// 检查有没有任务在执行，如果有就执行报错，执行错误
	// TODO 目前只有online ddl，其他的后面再说吧
	hasTicketRunning, err := d.ticketRepo.HasTicketRunning(ctx, d.state.InstanceId, int(model.TicketType_FreeLockStructChange))
	if err != nil {
		log.Warn(ctx, "ticketId: %s, check has running ticket error:%v", msg.TicketId, err.Error())
		err = d.ticketRepo.UpdateRunningInfo(ctx, &entity.TicketRunningInfo{TicketId: msg.TicketId, TicketStatus: int(model.TicketStatus_TicketError), Description: "check has running ticket error"})
		if err != nil {
			log.Warn(ctx, "UpdateRunningInfo error:%s", err.Error())
		}
		return
	}
	log.Info(ctx, "instanceId:%s hasTicketRunning:%v", d.state.InstanceId, hasTicketRunning)
	if hasTicketRunning {
		err = d.ticketRepo.UpdateRunningInfo(ctx, &entity.TicketRunningInfo{TicketId: msg.TicketId, TicketStatus: int(model.TicketStatus_TicketError), Description: "there is ticket running, can't execute"})
		if err != nil {
			log.Warn(ctx, "UpdateRunningInfo error:%s", err.Error())
		}
		return
	}
	// 改为执行中
	err = d.ticketRepo.UpdateRunningInfo(ctx, &entity.TicketRunningInfo{TicketId: msg.TicketId, TicketStatus: int(model.TicketStatus_TicketExecute), Description: "t"})
	if err != nil {
		log.Warn(ctx, "UpdateRunningInfo error:%s", err.Error())
		return
	}
	err = d.actorClient.KindOf(consts.DbwTicketActorKind).Send(ctx, msg.TicketId, msg)
	if err != nil {
		log.Warn(ctx, "send to actor execute error:%v", err)
		err = d.ticketRepo.UpdateRunningInfo(ctx, &entity.TicketRunningInfo{TicketId: msg.TicketId, TicketStatus: int(model.TicketStatus_TicketError), Description: "system error, send to execute error"})
		if err != nil {
			log.Warn(ctx, "UpdateRunningInfo error:%s", err.Error())
		}
	}
	return
}

func (d *DbwInstanceTicketActor) stopTicket(ctx types.Context, msg *shared.StopDbwTicket) {
	resp, err := d.actorClient.KindOf(consts.DbwTicketActorKind).Call(ctx, msg.TicketId, msg)
	if err != nil {
		log.Warn(ctx, "send to actor stop error:%v", err)
		ctx.Respond(&shared.DbwTicketResponse{Success: false, ErrMsg: "stop ticket error"})
		return
	}

	switch rsp := resp.(type) {
	case *shared.DbwTicketResponse:
		if rsp.Success {
			ctx.Respond(&shared.DbwTicketResponse{Success: true, ErrMsg: ""})
			return
		}
		log.Warn(ctx, "stopTicket: execute ticket failed %v", rsp.ErrMsg)
		ctx.Respond(&shared.DbwTicketResponse{Success: false, ErrMsg: "stop ticket error"})
	default:
		log.Error(ctx, "stopTicket: stop ticket failed %v", rsp)
		ctx.Respond(&shared.DbwTicketResponse{Success: false, ErrMsg: "stop ticket error"})
	}
}

func (d *DbwInstanceTicketActor) checkInstanceTicket(ctx types.Context) {
	// 1.检查有没有在执行中超时的任务,有就停了
	d.checkInstanceTimeout(ctx)
	// 2.检查有没有要定时准备执行的任务
	d.checkInstanceNeedRun(ctx)
}

func (d *DbwInstanceTicketActor) checkInstanceTimeout(ctx types.Context) {
	// TODO 这期只做online ddl
	tickets, err := d.ticketRepo.GetByTicketStatus(ctx, d.state.InstanceId, int(model.TicketStatus_TicketExecute), int(model.TicketType_FreeLockStructChange))
	if err != nil {
		log.Warn(ctx, "GetByTicketStatus error:%s", err.Error())
		return
	}
	nowTime := time.Now().Unix()
	for _, ticket := range tickets {
		if ticket == nil {
			continue
		}
		if ticket.ExecutableEndTime != 0 && ticket.ExecutableEndTime < nowTime {
			d.stopTicket(ctx, &shared.StopDbwTicket{
				TicketId: ticket.TicketId,
			})
		}
	}
}

func (d *DbwInstanceTicketActor) checkInstanceNeedRun(ctx types.Context) {
	// TODO 这期只做online ddl
	tickets, err := d.ticketRepo.GetByTicketStatus(ctx, d.state.InstanceId, int(model.TicketStatus_TicketWaitExecute), int(model.TicketType_FreeLockStructChange))
	if err != nil {
		log.Warn(ctx, "GetByTicketStatus error:%s", err.Error())
		return
	}
	nowTime := time.Now().Unix()
	for _, ticket := range tickets {
		if ticket == nil {
			continue
		}
		if ticket.ExecuteType == int8(model.ExecuteType_Cron) && ticket.ExecutableStartTime < nowTime {
			log.Info(ctx, "%s ticket start execute", ticket.TicketId)
			d.executeTicket(ctx, &shared.ExecuteDbwTicket{
				TicketId: ticket.TicketId,
			})
		}
	}
}
