package sql_review

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/operate_record"
	"code.byted.org/infcs/dbw-mgr/biz/service/sql_review"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/robfig/cron/v3"
	"go.uber.org/dig"
)

type SqlReviewActorIn struct {
	dig.In
	SqlReviewService     sql_review.SqlReviewService
	OperateRecordService operate_record.OperateRecordService
}

func NewSqlReviewActor(in SqlReviewActorIn) types.SingletonProducer {
	return types.SingletonProducer{
		Name: consts.SQLReviewActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &SqlReviewActor{
				cron:                 cron.New(),
				SqlReviewService:     in.SqlReviewService,
				OperateRecordService: in.OperateRecordService,
			}
		}),
	}
}

type SqlReviewActor struct {
	cron                 *cron.Cron
	SqlReviewService     sql_review.SqlReviewService
	OperateRecordService operate_record.OperateRecordService
}

func (self *SqlReviewActor) Process(ctx types.Context) {
	switch ctx.Message().(type) {
	case *actor.Started:
		self.initCronTask(ctx)
		self.initOperateRecordTask(ctx)
	}
}

// initCronTask 启动定时任务
func (self *SqlReviewActor) initCronTask(ctx types.Context) {
	log.Info(ctx, "SqlReviewActor initCronTask start!")
	//定时任务设置当前序任务未完成，后续任务直接跳过
	self.cron = cron.New(cron.WithChain(
		cron.SkipIfStillRunning(cron.DefaultLogger),
	))
	self.cron.Start()
	_, err := self.cron.AddFunc("@every 20s", func() {
		ctx.WithValue(fwctx.BIZ_CONTEXT_KEY, fwctx.NewBizContext())
		self.SqlReviewService.SystemReview(ctx)
	})
	log.Info(ctx, "SqlReviewActor initCronTask finished!")
	if err != nil {
		log.Error(ctx, "SqlReviewActor initCronTask error, err is: %v", err)
	}
}

func (self *SqlReviewActor) initOperateRecordTask(ctx types.Context) {
	log.Info(ctx, "OperateRecordTask initCronTask start!")
	//定时任务设置当前序任务未完成，后续任务直接跳过
	self.cron = cron.New(cron.WithChain(
		cron.SkipIfStillRunning(cron.DefaultLogger),
	))
	self.cron.Start()
	_, err := self.cron.AddFunc("@midnight", func() {
		ctx.WithValue(fwctx.BIZ_CONTEXT_KEY, fwctx.NewBizContext())
		self.OperateRecordService.ClearRecordOnTime(ctx)
	})
	log.Info(ctx, "initOperateRecordTask finished!")
	if err != nil {
		log.Error(ctx, "initOperateRecordTask error, err is: %v", err)
	}
}
