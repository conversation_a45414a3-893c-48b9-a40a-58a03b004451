package dbgpt

import (
	"errors"
	"strconv"
	"time"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
	"golang.org/x/exp/maps"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	myerror "code.byted.org/infcs/dbw-mgr/biz/errors"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/repository/chat"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbgpt"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
)

type SqlAssistantActorIn struct {
	dig.In
	ActorClient cli.ActorClient
	DsSvc       datasource.DataSourceService
	ChatRepo    chat.ChatRepo
	NL2SqlSvc   dbgpt.NL2SQLService
}

func NewSqlAssistantActor(in SqlAssistantActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.SqlAssistantActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &SqlAssistantActor{
				tablesMap:   make(map[string]map[string]*shared.TableMeta),
				actorClient: in.ActorClient,
				nl2sqlSvc:   in.NL2SqlSvc,
				chatRepo:    in.ChatRepo,
				isNewChat:   true,
			}
		}),
	}
}

type SqlAssistantActor struct {
	tablesMap            map[string]map[string]*shared.TableMeta
	actorClient          cli.ActorClient
	describeTableHandler handler.DescribeTableHandler
	nl2sqlSvc            dbgpt.NL2SQLService
	chatRepo             chat.ChatRepo
	isNewChat            bool
}

func (s *SqlAssistantActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(10 * time.Minute)
	log.Info(ctx, "actor received tables request: %s", utils.Show(ctx.Message()))
	switch msg := ctx.Message().(type) {
	case actor.Started:
		//ctx.SetReceiveTimeout(10 * time.Minute)
	case *shared.SqlAssistantReq:
		s.do(ctx, msg)
	case *shared.CloseSqlAssistant:
		ctx.Stop(ctx.Self())
	case *shared.DescribeDBTablesReq:

		s.getDBTables(ctx, msg)
	case *actor.ReceiveTimeout:
		s.clearTableCache(ctx)
		ctx.SetReceiveTimeout(10 * time.Minute)
	}
}

func (s *SqlAssistantActor) do(ctx types.Context, msg *shared.SqlAssistantReq) {
	switch msg.Action {
	case shared.Generate:
		s.generateSql(ctx, msg)
	}
}

func (s *SqlAssistantActor) getDBTables(ctx types.Context, msg *shared.DescribeDBTablesReq) {
	if s.isNewChat {
		chatId, err := strconv.ParseInt(ctx.GetName(), 10, 64)
		_, err = s.chatRepo.GetChat(ctx, chatId)
		if err == myerror.NotFound {
			if err = s.chatRepo.CreateInnerChat(ctx, chatId); err != nil {
			}
		}
		s.isNewChat = false
	}
	dbMetadata := &shared.DescribeDBTablesResp{}
	if tables, ok := s.tablesMap[msg.Database]; ok {
		if len(msg.Tables) != 0 {
			for _, t := range msg.Tables {
				tableInfo, exist := tables[t]
				if !exist {
					tableInfo, err := s.getTableInfo(ctx, msg.Database, t)
					if err != nil {
						log.Warn(ctx, "DescribeTable %s err:%s", t, err)
						continue
					}
					s.tablesMap[msg.Database][t] = tableInfo
					dbMetadata.Tables = append(dbMetadata.Tables, tableInfo)
				} else {
					dbMetadata.Tables = append(dbMetadata.Tables, tableInfo)
				}
			}
		} else {
			for _, v := range s.tablesMap[msg.Database] {
				dbMetadata.Tables = append(dbMetadata.Tables, v)
			}
		}

	} else {
		s.tablesMap[msg.Database] = make(map[string]*shared.TableMeta)
		log.Info(ctx, "start list all table")
		tables, err := s.listTablesInfo(ctx, msg.Database)
		if err != nil {
			log.Warn(ctx, "list tables error %s", err)
		}
		log.Info(ctx, "list %d tables", len(tables))
		m := make(map[string]*shared.TableMeta)
		for _, t := range tables {
			dbMetadata.Tables = append(dbMetadata.Tables, t)
			m[t.Name] = t
		}
		s.tablesMap[msg.Database] = m
	}
	ctx.Respond(dbMetadata)
}
func (s *SqlAssistantActor) generateSql(ctx types.Context, msg *shared.SqlAssistantReq) {
	log.Info(ctx, "generate sql request: %s", utils.Show(msg))
	dbMetadata := &dbgpt.DBMetadata{}
	log.Info(ctx, "tables map %s", utils.Show(s.tablesMap))
	if tables, ok := s.tablesMap[msg.Database]; ok {
		if len(msg.Tables) != 0 {
			for _, t := range msg.Tables {
				tableInfo, exist := tables[t]
				if !exist {
					tableInfo, err := s.getTableInfo(ctx, msg.Database, t)
					if err != nil {
						log.Warn(ctx, "DescribeTable %s err:%s", t, err)
						continue
					}
					s.tablesMap[msg.Database][t] = tableInfo
					dbMetadata.Tables = append(dbMetadata.Tables, convertTableInfo(tableInfo))
				} else {
					dbMetadata.Tables = append(dbMetadata.Tables, convertTableInfo(tableInfo))
				}
			}
		} else {
			for _, v := range s.tablesMap[msg.Database] {
				dbMetadata.Tables = append(dbMetadata.Tables, convertTableInfo(v))
			}
		}

	} else {
		s.tablesMap[msg.Database] = make(map[string]*shared.TableMeta)
		log.Info(ctx, "start list all table")
		tables, err := s.listTablesInfo(ctx, msg.Database)
		if err != nil {
			log.Warn(ctx, "list tables error %s", err)
		}
		log.Info(ctx, "all tables %s", utils.Show(tables))
		m := make(map[string]*shared.TableMeta)
		for _, t := range tables {
			dbMetadata.Tables = append(dbMetadata.Tables, convertTableInfo(t))
			m[t.Name] = t
		}
		s.tablesMap[msg.Database] = m
	}
	dbMetadata.DBID = msg.Database
	s.toChat(ctx, msg.Query, dbMetadata)
}

func (s *SqlAssistantActor) toChat(ctx types.Context, query string, dbMetadata *dbgpt.DBMetadata) {
	chatId, err := strconv.ParseInt(ctx.GetName(), 10, 64)
	if err != nil {
		ctx.Respond(&shared.SqlAssistantResp{Answer: ""})
		return
	}
	if s.isNewChat {
		_, err = s.chatRepo.GetChat(ctx, chatId)
		if err == myerror.NotFound {
			if err = s.chatRepo.CreateInnerChat(ctx, chatId); err != nil {
			}
		}
		s.isNewChat = false
	}
	res, err := s.nl2sqlSvc.Chat(ctx, dbgpt.NewSQLChatRequest(chatId, 1, query, dbMetadata, "markdown"))
	if err != nil {
		log.Warn(ctx, "chat error", err)
	}
	ctx.Respond(&shared.SqlAssistantResp{Answer: res})
}

func (s *SqlAssistantActor) getTableInfo(ctx types.Context, db, table string) (*shared.TableMeta, error) {
	resp, err := s.actorClient.KindOf(consts.SessionActorKind).Call(ctx, ctx.GetName(), &shared.DescribeTable{
		Db:    db,
		Table: table,
	})
	if err != nil {
		log.Warn(ctx, "Describe table % error err:%s", table, err)
		return nil, err
	}
	switch rsp := resp.(type) {
	case *shared.TableInfo:
		ret := &shared.TableMeta{}
		err := fp.StreamOf(rsp.Columns).
			Map(func(c *shared.TableInfo_ColumnInfo) shared.ColumnMeta {
				return shared.ColumnMeta{
					Name:        c.Name,
					Type:        c.Type,
					IsPrimary:   c.IsKey,
					Description: c.Comment,
				}
			}).
			ToSlice(&ret.Columns)
		if err != nil {
			log.WarnS(ctx, "get table error", "table", table, "error", err)
			ctx.Respond(&shared.DataSourceOpFailed{ErrorMessage: err.Error()})
		}
		ret.Name = table
		ret.Description = rsp.TableOption.Comment
		return ret, nil
	case *shared.DataSourceOpFailed:
		log.Warn(ctx, "datasource operation failed")
		ctx.Respond(&shared.DataSourceOpFailed{ErrorMessage: rsp.ErrorMessage})
	}
	return nil, errors.New("unknown error")
}

func (s *SqlAssistantActor) listTablesInfo(ctx types.Context, database string) ([]*shared.TableMeta, error) {
	resp, err := s.actorClient.KindOf(consts.SessionActorKind).Call(ctx, ctx.GetName(), &shared.ListTablesInfo{
		Db: database,
	})
	if err != nil {
		log.Warn(ctx, " List tablesInfo error err:%s", err)
		return nil, err
	}
	switch rsp := resp.(type) {
	case *shared.Tables:
		var ret []*shared.TableMeta
		for _, t := range rsp.Tables {
			dt := &shared.TableMeta{
				Name:       t.Name,
				Definition: t.Definition,
			}
			if t.TableOption != nil {
				dt.Description = t.TableOption.Comment
			}
			for _, c := range t.Columns {
				dc := &shared.ColumnMeta{
					Name:        c.Name,
					Type:        c.Type,
					IsPrimary:   c.IsKey,
					Description: c.Comment,
				}
				dt.Columns = append(dt.Columns, dc)
			}
			ret = append(ret, dt)
		}
		return ret, nil
	case shared.DataSourceOpFailed:
		log.Warn(ctx, "datasource operation failed")
		return nil, err
	default:
		return nil, errors.New("unknown error")
	}
}

func (s *SqlAssistantActor) clearTableCache(ctx types.Context) {
	maps.Clear(s.tablesMap)
}

func convertTableInfo(table *shared.TableMeta) *dbgpt.Table {
	t := &dbgpt.Table{
		Name:        table.Name,
		Description: table.Description,
	}
	t.Columns = make([]dbgpt.Column, 0)
	for _, column := range table.Columns {
		t.Columns = append(t.Columns, dbgpt.Column{
			Name:        column.Name,
			Type:        column.Type,
			Description: column.Description,
			IsPrimary:   column.IsPrimary,
		})
	}
	return t
}
