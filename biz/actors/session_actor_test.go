package actors

import (
	"errors"
	"fmt"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
)

type SessionActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *SessionActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}
func (suite *SessionActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}
func TestSessionActorSuite(t *testing.T) {
	suite.Run(t, new(SessionActorSuite))
}

func (suite *SessionActorSuite) TestNewSessionActor() {
	ret := NewSessionActor(NewSessionActorIn{
		In: dig.In{},
	})
	suite.NotEmpty(ret)
}

//func TestGetImportTosUrl(t *testing.T) {
//	actor := &SessionActor{
//		c3ConfProvider: &mocks.MockC3ConfigProvider{},
//	}
//	ctx := &mocks.MockContext{}
//	mock1 := mockey.Mock(tos.NewTOSClient).Return(mocks.).Build()
//
//	NewTOSClient
//
//	MockClient
//	actor.GetImportTosUrl(ctx, "aaa")
//
//}

//func (suite *SessionActorSuite) TestProcessCreateDbExportTaskOk() {
//	Ctx := mocks.NewMockContext(suite.ctrl)
//	cnf := config2.NewMockConfigProvider(suite.ctrl)
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
//	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
//	migRepo := dmRepoMocks.NewMockMigrationRepo(suite.ctrl)
//	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
//	idSvc := mocks.NewMockService(suite.ctrl)
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().Message().Return(&shared.CreateDbExportTaskReq{
//		SessionId:              "",
//		DataSource:             nil,
//		FileType:               0,
//		FileEncoding:           0,
//		CreateTime:             0,
//		ExecuteTime:            0,
//		ContentFormat:          0,
//		DbName:                 "",
//		TableList:              nil,
//		Structures:             nil,
//		CsvFirstRowIsColumnDef: false,
//		Comment:                "",
//		AdvancedOptions:        nil,
//		ExportType:             "",
//		KubeCluster:            "",
//		TenantId:               "111",
//		Config:                 "",
//		AK:                     "",
//		SK:                     "",
//		Token:                  "",
//		LogID:                  "xxx",
//		UserID:                 "",
//		UserName:               "",
//	}).AnyTimes()
//	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
//	idSvc.EXPECT().NextID(gomock.Any()).AnyTimes()
//	describeInstanceAddressResp := &datasource.DescribeInstanceAddressResp{
//		IP:   "*******",
//		Port: 3306,
//	}
//	dsSvc.EXPECT().DescribeInstanceAddress(gomock.Any(), gomock.Any()).Return(describeInstanceAddressResp, nil)
//	taskInfo := &entity.MigrationTasksInfo{
//		Total: 0,
//	}
//	migRepo.EXPECT().ListActiveTasks(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(taskInfo, nil).AnyTimes()
//	migRepo.EXPECT().CreateTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	rawState := "{\"IdlePeriodSeconds\":0,\"MyIP\":\"*************\",\"ConnectionIPList\":null,\"SessionCreated\":true,\"SesstionTimeoutSeconds\":1800,\"DataSource\":{\"address\":\"mysql-81c74241f028-hs.rds.svc.mix-panel-azb.org:3306\",\"user\":\"dbw_admin\",\"password\":\"Dbw_0fccb8dfc264e6565c59da7452\",\"extra_dsn\":{\"multiStatements\":\"true\"},\"connect_timeout_ms\":5000,\"read_timeout_ms\":60000,\"write_timeout_ms\":30000,\"idle_timeout_ms\":30000,\"instance_id\":\"mysql-81c74241f028\",\"cadidate_address\":\"*************:3306\",\"max_open_conns\":1},\"Conn\":null,\"DeadConn\":null,\"TenantID\":\"2100000746\",\"WlID\":\"acl-04cbe6b369b74a4782d8f70b43773469\",\"CurCursor\":0,\"CurOffset\":0,\"CachedKeys\":null,\"BeforeQuery\":\"\",\"BeforeDB\":0,\"ConnLock\":{}}"
//	state := &sessionState{}
//	json.Unmarshal([]byte(rawState), state)
//	kc := mocks.NewMockKindClient(suite.ctrl)
//	Ctx.EXPECT().ClientOf(consts.TaskActorKind).Return(kc).AnyTimes()
//	resp := &shared.CreateDbExportTaskResp{}
//	kc.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes()
//	sessionActor := SessionActor{
//		state:                state,
//		cnf:                  cnf,
//		sources:              dsSvc,
//		idSvc:                idSvc,
//		MigRepo:              migRepo,
//		OperateRecordService: mocks.NewMockOperateRecordService(suite.ctrl),
//	}
//	sessionActor.Process(Ctx)
//}

//func (suite *SessionActorSuite) TestProcessCreateDbExportTaskError() {
//	Ctx := mocks.NewMockContext(suite.ctrl)
//	cnf := config2.NewMockConfigProvider(suite.ctrl)
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
//	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
//	migRepo := dmRepoMocks.NewMockMigrationRepo(suite.ctrl)
//	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
//	idSvc := mocks.NewMockService(suite.ctrl)
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().Message().Return(&shared.CreateDbExportTaskReq{}).AnyTimes()
//	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
//	idSvc.EXPECT().NextID(gomock.Any()).AnyTimes()
//	migRepo.EXPECT().CreateTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	taskInfo := &entity.MigrationTasksInfo{
//		Total: 0,
//	}
//	migRepo.EXPECT().ListActiveTasks(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(taskInfo, nil).AnyTimes()
//	rawState := "{\"IdlePeriodSeconds\":0,\"MyIP\":\"*************\",\"ConnectionIPList\":null,\"SessionCreated\":true,\"SesstionTimeoutSeconds\":1800,\"DataSource\":{\"address\":\"mysql-81c74241f028-hs.rds.svc.mix-panel-azb.org:3306\",\"user\":\"dbw_admin\",\"password\":\"Dbw_0fccb8dfc264e6565c59da7452\",\"extra_dsn\":{\"multiStatements\":\"true\"},\"connect_timeout_ms\":5000,\"read_timeout_ms\":60000,\"write_timeout_ms\":30000,\"idle_timeout_ms\":30000,\"instance_id\":\"mysql-81c74241f028\",\"cadidate_address\":\"*************:3306\",\"max_open_conns\":1},\"Conn\":null,\"DeadConn\":null,\"TenantID\":\"2100000746\",\"WlID\":\"acl-04cbe6b369b74a4782d8f70b43773469\",\"CurCursor\":0,\"CurOffset\":0,\"CachedKeys\":null,\"BeforeQuery\":\"\",\"BeforeDB\":0,\"ConnLock\":{}}"
//	state := &sessionState{
//		DataSource: &shared.DataSource{
//			Type: shared.DataSourceType(model.DSType_VeDBMySQL),
//		},
//	}
//	json.Unmarshal([]byte(rawState), state)
//	kc := mocks.NewMockKindClient(suite.ctrl)
//	describeInstanceAddressResp := &datasource.DescribeInstanceAddressResp{
//		IP:   "*******",
//		Port: 3306,
//	}
//	dsSvc.EXPECT().DescribeInstanceAddress(gomock.Any(), gomock.Any()).Return(describeInstanceAddressResp, nil)
//	Ctx.EXPECT().ClientOf(consts.TaskActorKind).Return(kc).AnyTimes()
//	resp := &shared.CreateDbExportTaskResp{}
//	kc.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes()
//	sessionActor := SessionActor{
//		state:                state,
//		cnf:                  cnf,
//		sources:              dsSvc,
//		idSvc:                idSvc,
//		MigRepo:              migRepo,
//		OperateRecordService: mocks.NewMockOperateRecordService(suite.ctrl),
//	}
//	sessionActor.Process(Ctx)
//}

//func (suite *SessionActorSuite) TestProcessCreateDbImportTaskOk() {
//	Ctx := mocks.NewMockContext(suite.ctrl)
//	cnf := config2.NewMockConfigProvider(suite.ctrl)
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
//	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
//	migRepo := dmRepoMocks.NewMockMigrationRepo(suite.ctrl)
//	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
//	idSvc := mocks.NewMockService(suite.ctrl)
//	cnf.EXPECT().Get(gomock.Any()).Return(&config.Config{DataMigToolBatchNum: 10}).AnyTimes()
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().Message().Return(&shared.CreateDbImportTaskReq{
//		TenantId: "111",
//		LogID:    "xxx",
//	}).AnyTimes()
//	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
//	idSvc.EXPECT().NextID(gomock.Any()).AnyTimes()
//	taskInfo := &entity.MigrationTasksInfo{
//		Total: 0,
//	}
//	migRepo.EXPECT().ListActiveTasks(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(taskInfo, nil).AnyTimes()
//	migRepo.EXPECT().CreateTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//	describeInstanceAddressResp := &datasource.DescribeInstanceAddressResp{
//		IP:   "*******",
//		Port: 3306,
//	}
//	dsSvc.EXPECT().DescribeInstanceAddress(gomock.Any(), gomock.Any()).Return(describeInstanceAddressResp, nil)
//	rawState := "{\"IdlePeriodSeconds\":0,\"MyIP\":\"*************\",\"ConnectionIPList\":null,\"SessionCreated\":true,\"SesstionTimeoutSeconds\":1800,\"DataSource\":{\"address\":\"mysql-81c74241f028-hs.rds.svc.mix-panel-azb.org:3306\",\"user\":\"dbw_admin\",\"password\":\"Dbw_0fccb8dfc264e6565c59da7452\",\"extra_dsn\":{\"multiStatements\":\"true\"},\"connect_timeout_ms\":5000,\"read_timeout_ms\":60000,\"write_timeout_ms\":30000,\"idle_timeout_ms\":30000,\"instance_id\":\"mysql-81c74241f028\",\"cadidate_address\":\"*************:3306\",\"max_open_conns\":1},\"Conn\":null,\"DeadConn\":null,\"TenantID\":\"2100000746\",\"WlID\":\"acl-04cbe6b369b74a4782d8f70b43773469\",\"CurCursor\":0,\"CurOffset\":0,\"CachedKeys\":null,\"BeforeQuery\":\"\",\"BeforeDB\":0,\"ConnLock\":{}}"
//	state := &sessionState{
//		DataSource: &shared.DataSource{
//			Type: shared.DataSourceType(model.DSType_VeDBMySQL),
//		},
//	}
//	json.Unmarshal([]byte(rawState), state)
//	kc := mocks.NewMockKindClient(suite.ctrl)
//	Ctx.EXPECT().ClientOf(consts.TaskActorKind).Return(kc).AnyTimes()
//	resp := &shared.CreateDbImportTaskResp{}
//	kc.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes()
//	sessionActor := SessionActor{
//		state:                state,
//		cnf:                  cnf,
//		sources:              dsSvc,
//		idSvc:                idSvc,
//		MigRepo:              migRepo,
//		OperateRecordService: mocks.NewMockOperateRecordService(suite.ctrl),
//	}
//	sessionActor.Process(Ctx)
//}

//func (suite *SessionActorSuite) TestProcessCreateDbImportTaskError() {
//	Ctx := mocks.NewMockContext(suite.ctrl)
//	cnf := config2.NewMockConfigProvider(suite.ctrl)
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
//	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
//	migRepo := dmRepoMocks.NewMockMigrationRepo(suite.ctrl)
//	dsSvc := mocks.NewMockDataSourceService(suite.ctrl)
//	idSvc := mocks.NewMockService(suite.ctrl)
//	cnf.EXPECT().Get(gomock.Any()).Return(&config.Config{DataMigToolBatchNum: 10}).AnyTimes()
//	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).AnyTimes()
//	Ctx.EXPECT().Message().Return(&shared.CreateDbImportTaskReq{}).AnyTimes()
//	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
//	idSvc.EXPECT().NextID(gomock.Any()).AnyTimes()
//	taskInfo := &entity.MigrationTasksInfo{
//		Total: 0,
//	}
//	migRepo.EXPECT().ListActiveTasks(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(taskInfo, nil).AnyTimes()
//	migRepo.EXPECT().CreateTask(gomock.Any(), gomock.Any()).Return(errors.New("")).AnyTimes()
//	describeInstanceAddressResp := &datasource.DescribeInstanceAddressResp{
//		IP:   "*******",
//		Port: 3306,
//	}
//	dsSvc.EXPECT().DescribeInstanceAddress(gomock.Any(), gomock.Any()).Return(describeInstanceAddressResp, nil)
//	rawState := "{\"IdlePeriodSeconds\":0,\"MyIP\":\"*************\",\"ConnectionIPList\":null,\"SessionCreated\":true,\"SesstionTimeoutSeconds\":1800,\"DataSource\":{\"address\":\"mysql-81c74241f028-hs.rds.svc.mix-panel-azb.org:3306\",\"user\":\"dbw_admin\",\"password\":\"Dbw_0fccb8dfc264e6565c59da7452\",\"extra_dsn\":{\"multiStatements\":\"true\"},\"connect_timeout_ms\":5000,\"read_timeout_ms\":60000,\"write_timeout_ms\":30000,\"idle_timeout_ms\":30000,\"instance_id\":\"mysql-81c74241f028\",\"cadidate_address\":\"*************:3306\",\"max_open_conns\":1},\"Conn\":null,\"DeadConn\":null,\"TenantID\":\"2100000746\",\"WlID\":\"acl-04cbe6b369b74a4782d8f70b43773469\",\"CurCursor\":0,\"CurOffset\":0,\"CachedKeys\":null,\"BeforeQuery\":\"\",\"BeforeDB\":0,\"ConnLock\":{}}"
//	state := &sessionState{
//		DataSource: &shared.DataSource{
//			Type: shared.DataSourceType(model.DSType_VeDBMySQL),
//		},
//	}
//	json.Unmarshal([]byte(rawState), state)
//	kc := mocks.NewMockKindClient(suite.ctrl)
//	Ctx.EXPECT().ClientOf(consts.TaskActorKind).Return(kc).AnyTimes()
//	resp := &shared.CreateDbImportTaskResp{}
//	kc.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).Return(resp, nil).AnyTimes()
//	sessionActor := SessionActor{
//		state:                state,
//		cnf:                  cnf,
//		sources:              dsSvc,
//		idSvc:                idSvc,
//		MigRepo:              migRepo,
//		OperateRecordService: mocks.NewMockOperateRecordService(suite.ctrl),
//	}
//	sessionActor.Process(Ctx)
//}

func TestExplainCommand(t *testing.T) {
	sessionActor := &SessionActor{
		state: &sessionState{
			DataSource: &shared.DataSource{Db: "xxx"},
		},
		crRepo:  &mocks.MockCommandResultRepo{},
		sources: &mocks.MockDataSourceService{},
	}
	ctx := &mocks.MockContext{}

	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockDataSourceService).ExplainCommand).Return(nil, fmt.Errorf("err")).Build()
	sessionActor.explainCommand(ctx, &shared.ExplainCommandReq{})
	baseMock2.UnPatch()

	baseMock21 := mockey.Mock((*mocks.MockDataSourceService).ExplainCommand).Return(&datasource.ExplainCommandResp{
		Command: []*datasource.ExplainCommandResult{
			{Id: 0},
		},
	}, nil).Build()
	sessionActor.explainCommand(ctx, &shared.ExplainCommandReq{})
	baseMock21.UnPatch()
}

func TestGetIndexInfo(t *testing.T) {
	sessionActor := &SessionActor{
		state: &sessionState{
			DataSource: &shared.DataSource{Db: "xxx"},
		},
		crRepo:  &mocks.MockCommandResultRepo{},
		sources: &mocks.MockDataSourceService{},
	}
	ctx := &mocks.MockContext{}

	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockDataSourceService).GetTableIndexInfo).Return(nil, fmt.Errorf("err")).Build()
	sessionActor.getIndexInfo(ctx, &shared.GetIndexInfoReq{})
	baseMock2.UnPatch()

	baseMock21 := mockey.Mock((*mocks.MockDataSourceService).GetTableIndexInfo).Return(&datasource.GetTableInfoIndexResp{
		TableIndexInfo: []*datasource.TableIndexInfo{
			{IndexComment: ""},
		},
	}, nil).Build()
	sessionActor.getIndexInfo(ctx, &shared.GetIndexInfoReq{})
	baseMock21.UnPatch()
}

func TestGetIndexValue(t *testing.T) {
	sessionActor := &SessionActor{
		state: &sessionState{
			DataSource: &shared.DataSource{Db: "xxx"},
		},
		crRepo:  &mocks.MockCommandResultRepo{},
		sources: &mocks.MockDataSourceService{},
	}
	ctx := &mocks.MockContext{}

	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockDataSourceService).GetTableIndexValue).Return(nil, fmt.Errorf("err")).Build()
	sessionActor.getIndexValue(ctx, &shared.GetIndexValueReq{
		Source: &shared.DataSource{
			Db: "xxx",
		},
	})
	baseMock2.UnPatch()

	baseMock21 := mockey.Mock((*mocks.MockDataSourceService).GetTableIndexValue).Return(&datasource.GetIndexValueResp{
		TableIndexValue: []*datasource.TableIndexValue{
			{IndexValue: "1"},
		},
	}, nil).Build()
	sessionActor.getIndexValue(ctx, &shared.GetIndexValueReq{
		Source: &shared.DataSource{
			Db: "xxx",
		},
	})
	baseMock21.UnPatch()
}

func getSessionActor() *SessionActor {
	return &SessionActor{
		actorCli: &mocks.MockActorClient{},
		crRepo:   &mocks.MockCommandResultRepo{},
		state: &sessionState{
			DataSource: &shared.DataSource{InstanceId: "xxx"},
		},
	}
}

//func Test_cancelTask(t *testing.T) {
//	mockey.PatchConvey("Test_cancelTask", t, func() {
//		svc := getSessionActor()
//		ctx := &mocks.MockContext{}
//		mockey.Mock((*mocks.MockActorClient).KindOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
//		mockey.Mock((*mocks.MockKindClient).Call).Return(nil, nil).Build()
//		mockey.Mock(fwctx.GetTenantID).Return("2100000746").Build()
//		mockey.PatchConvey("task case", func() {
//			mockey.Mock((*mocks.MockCommandResultRepo).List).Return([]*entity.CommandResult{
//				{Chunk: &shared.CommandResultChunk_Row{
//					Cells: []string{"SQLTaskID: 1800424326558703616"},
//				}},
//			}, nil).Build()
//			cs := &entity.CommandSet{Commands: []*entity.Command{{ID: "123456789", Extra: map[string]interface{}{"CanEdit": false, "SqlExecuteType": "Connection", "SqlLanguageType": "Normal"}, State: entity.CommandExecuting}}}
//			err := svc.cancelTask(ctx, cs)
//			convey.So(err, convey.ShouldBeNil)
//		})
//		mockey.PatchConvey("conn case", func() {
//			mockey.Mock((*mocks.MockCommandResultRepo).List).Return([]*entity.CommandResult{
//				{Chunk: &shared.CommandResultChunk_Row{
//					Cells: []string{"SQLTaskID: 1800424326558703616"},
//				}},
//			}, nil).Build()
//			cs := &entity.CommandSet{Commands: []*entity.Command{{ID: "123456789", Extra: map[string]interface{}{"SqlTaskID": "1800424326558703616", "CanEdit": false, "SqlExecuteType": "Task", "SqlLanguageType": "Normal"}, State: entity.CommandExecuting}}}
//			err := svc.cancelTask(ctx, cs)
//			convey.So(err, convey.ShouldBeNil)
//		})
//	})
//}

//func TestCreateSessionConn(t *testing.T) {
//	sessionActor := &SessionActor{
//		state: &sessionState{
//			DataSource: &shared.DataSource{Db: "xxx", Type: shared.Mongo, AuthDb: "db1"},
//		},
//		crRepo:  &mocks.MockCommandResultRepo{},
//		sources: &mocks.MockDataSourceService{},
//	}
//	ctx := &mocks.MockContext{}
//	baseMock0 := mockey.Mock(log.Log).Return().Build()
//	defer baseMock0.UnPatch()
//	baseMock1 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
//	defer baseMock1.UnPatch()
//
//	mock1 := mockey.Mock((*SessionActor)._createSessionConn).Return(&createSessionConnResult{Conn: &Connection{}}, nil).Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*SessionActor)._changeDb).Return(nil).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*SessionActor).GetSessionID).Return("").Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock5.UnPatch()
//
//	sessionActor.createSessionConn(ctx, &shared.NewSessionConnection{DbName: "DB2"})
//}

func TestCreateSessionConnError(t *testing.T) {
	sessionActor := &SessionActor{
		state: &sessionState{
			DataSource: &shared.DataSource{Db: "xxx", Type: shared.Mongo, AuthDb: "db1"},
		},
		crRepo:  &mocks.MockCommandResultRepo{},
		sources: &mocks.MockDataSourceService{},
	}
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock1.UnPatch()

	mock1 := mockey.Mock((*SessionActor)._createSessionConn).Return(&createSessionConnResult{Conn: &Connection{}}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*SessionActor)._changeDb).Return(fmt.Errorf("test")).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*SessionActor).GetSessionID).Return("").Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock(fwctx.GetUserID).Return("").Build()
	defer mock5.UnPatch()

	sessionActor.createSessionConn(ctx, &shared.NewSessionConnection{DbName: "DB2"})
}

func TestCChangeDB(t *testing.T) {
	sessionActor := &SessionActor{
		state: &sessionState{
			DataSource: &shared.DataSource{Db: "xxx", Type: shared.Mongo, AuthDb: "db1"},
		},
		crRepo:  &mocks.MockCommandResultRepo{},
		sources: &mocks.MockDataSourceService{},
	}
	ctx := &mocks.MockContext{}
	baseMock0 := mockey.Mock(log.Log).Return().Build()
	defer baseMock0.UnPatch()
	baseMock1 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock1.UnPatch()

	mock1 := mockey.Mock((*mocks.MockContext).ClientOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	defer mock2.UnPatch()

	err := sessionActor._changeDb(ctx, &shared.ChangeDB{})
	assert.NotNil(t, err)
}

func Test_memoCommandResult(t *testing.T) {
	ctx := &mocks.MockContext{}
	msg := &shared.CommandResult{
		CommandId:    "xxx",
		ConnectionId: "xxx",
		Payload: []*shared.CommandResultChunk{{
			HasMore: false,
			Header:  []string{"sql_task"},
			Rows: []*shared.CommandResultChunk_Row{{
				Cells: []string{"abc"},
			}},
			Offset: 1,
		}},
	}
	sessionActor := &SessionActor{
		state: &sessionState{
			DataSource: &shared.DataSource{Db: "xxx", Type: shared.MySQL, AuthDb: "db1"},
		},
		PriSvc:  &mocks.MockPrivilegeServiceInterface{},
		crRepo:  &mocks.MockCommandResultRepo{},
		sources: &mocks.MockDataSourceService{},
	}
	PatchConvey("Test case1", t, func() {
		mock0 := mockey.Mock(log.Log).Return().Build()
		defer mock0.UnPatch()
		mock1 := mockey.Mock((*SessionActor).GetSessionID).Return("").Build()
		defer mock1.UnPatch()
		Mock((*mocks.MockCommandResultRepo).BulkCreate).Return(nil).Build()
		Mock((*mocks.MockPrivilegeServiceInterface).UpdateCurResultCount).Return(errors.New("error")).Build()
		Mock((*mocks.MockContext).Send).Return().Build()
		Mock((*mocks.MockContext).Self).Return(nil).Build()
		sessionActor.memoCommandResult(ctx, msg)
		// Add assertions based on expected successful behavior
	})
}
