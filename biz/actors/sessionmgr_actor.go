package actors

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/metrics"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"math"
	"math/rand"
	"runtime/debug"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	rdsModel "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

const (
	SessionMgrTimeIntervalSeconds  = 60
	SessionMgrCloseIntervalSeconds = 60 * 10
)

type NewSessionMgrActorIn struct {
	dig.In
	Config    config.ConfigProvider
	C3Config  c3.ConfigProvider
	Location  location.Location
	Source    datasource.DataSourceService
	IdGen     idgen.Service
	Threshold metrics.ConnectionMetrics
}

func NewSessionMgrActor(p NewSessionMgrActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.SessionMgrActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &SessionMgrActor{
				cnf:       p.Config,
				loc:       p.Location,
				source:    p.Source,
				c3Conf:    p.C3Config,
				idg:       p.IdGen,
				threshold: p.Threshold,
			}
		}),
	}
}

type SessionMgrActor struct {
	cnf                      config.ConfigProvider
	c3Conf                   c3.ConfigProvider
	loc                      location.Location
	source                   datasource.DataSourceService
	idg                      idgen.Service
	dbwAccountName           string
	dbwAccountPasswordGenKey string
	sessionPool              *instanceSessionPool
	lastRequestTime          int64 // 上次请求时间
	closeIntervalSeconds     int64 // 多久没有收到请求关闭
	lastCheckTime            int64 // 上次检查时间
	threshold                metrics.ConnectionMetrics
}

func (s *SessionMgrActor) Process(actorCtx types.Context) {
	defer actorCtx.SetReceiveTimeout(time.Duration(SessionMgrTimeIntervalSeconds) * time.Second)
	switch msg := actorCtx.Message().(type) {
	case *actor.Started:
		log.Info(actorCtx, "[SessionMgrActor] SessionMgrActor Started")
		s.Initialization(actorCtx)
	case *actor.Stopping:
		return
	case *actor.Stopped:
		return
	case *actor.Restarting:
		return
	case *shared.GetSession:
		log.Info(actorCtx, "received getSession msg %s", utils.Show(msg))
		s.protectUserCall(actorCtx, func() {
			s.lastRequestTime = time.Now().Unix()
			s.getSession(actorCtx, msg)
		})
	case *shared.GiveBackSession:
		s.protectUserCall(actorCtx, func() {
			s.lastRequestTime = time.Now().Unix()
			s.giveBackSession(actorCtx, msg)
		})
	case *shared.CloseInternalSession:
		s.protectUserCall(actorCtx, func() {
			s.closeSession(actorCtx, msg)
		})
	case *shared.AddPrivilegeToDB:
		s.protectUserCall(actorCtx, func() {
			s.lastRequestTime = time.Now().Unix()
			s.addPrivilegeToDB(actorCtx, msg)
		})
	case *actor.ReceiveTimeout:
		s.check(actorCtx)
	}
}

func (s *SessionMgrActor) getSession(ctx types.Context, msg *shared.GetSession) {
	//instanceId := ctx.GetName()
	instanceId := msg.InstanceId
	// if instance pool does not exist, create it
	if s.sessionPool == nil {
		ds := &model.DataSource{
			Type:       conv.ToModelType(msg.DSType),
			LinkType:   conv.ToModelLinkType(msg.LinkType),
			InstanceId: utils.StringRef(instanceId),
			Username:   utils.StringRef(s.dbwAccountName),
			Password:   utils.StringRef(s.getAccountPassword(s.dbwAccountPasswordGenKey, instanceId)),
		}

		cfg := s.cnf.Get(ctx)
		s.sessionPool = NewInstanceSessionPool(ds, cfg.SessionPoolMaxIdleSeconds,
			cfg.SessionPoolMaxUsedSeconds, s.idg, s.cnf, s.threshold)
		s.sessionPool.SetMaxIdleSeconds(cfg.SessionPoolMaxIdleSeconds)
		s.sessionPool.SetMaxUsedSeconds(cfg.SessionPoolMaxUsedSeconds)
		s.sessionPool.SetIdGenerator(s.idg)
		s.sessionPool.SetConfigProvider(s.cnf)
	}
	mds := new(model.DataSource)
	*mds = *s.sessionPool.DataSource
	if msg.UserAccountName != "" && msg.UserPassword != "" {
		mds.Username = utils.StringRef(msg.UserAccountName)
		mds.Password = utils.StringRef(msg.UserPassword)
	}
	if msg.Address != "" {
		mds.Address = &msg.Address
	}
	if msg.Database != "" {
		mds.DBName = &msg.Database
	}
	// take session from instance session pool
	session, err := s.sessionPool.Take(ctx, mds)
	if err == nil {
		ctx.Respond(&shared.SessionInfo{
			SessionId:    session.SessionId,
			ConnectionId: session.ConnectionId,
		})
		return
	}

	// make sure account exists
	if (strings.Contains(strings.ToLower(err.Error()), consts.MySQLAccountError) ||
		strings.Contains(err.Error(), consts.AccountErrorCn) || strings.Contains(strings.ToLower(err.Error()), consts.PostgreAccountError)) && s.isInnerAccount(*mds.Username) {
		if err = s.resetAccount(ctx, instanceId, msg.DSType); err != nil {
			log.Warn(ctx, "reset account failed, instanceId=%s, err=%v", instanceId, err)
			ctx.Respond(&shared.GetSessionFailed{
				ErrorMessage:  "InternalError",
				StandardError: consts.TranslateStandardErrorToShared(err),
			})
			return
		}
	}

	// try again
	session, err = s.sessionPool.Take(ctx, mds)
	if err != nil {
		log.Warn(ctx, "failed to take session from pool, err=%v", err)
		ctx.Respond(&shared.GetSessionFailed{
			ErrorMessage:  err.Error(),
			StandardError: consts.TranslateStandardErrorToShared(err),
		})
		return
	}

	ctx.Respond(&shared.SessionInfo{
		SessionId:    session.SessionId,
		ConnectionId: session.ConnectionId,
	})

}

func (s *SessionMgrActor) resetAccount(ctx types.Context, instanceId string, dsType shared.DataSourceType) error {
	log.Warn(ctx, "internal account does not exist or password error, try to create a new one")
	if !s.checkInstanceIsRunning(ctx, instanceId, dsType) {
		return consts.ErrorOf(model.ErrorCode_NotSupportAction)
	}
	// 避免并发设置随机休眠时间
	minNum := 100 // 最小延迟时间100ms
	maxNum := 500 // 最大延迟时间500ms
	time.Sleep(time.Millisecond * time.Duration(minNum+rand.Intn(maxNum-minNum+1)))
	if err := s.deleteAccount(ctx, instanceId, dsType); err != nil {
		return err
	}
	if err := s.createAccount(ctx, instanceId, dsType); err != nil {
		return err
	}
	if err := s.grantAccountPrivilegeOnSystemDB(ctx, instanceId, dsType); err != nil {
		return err
	}
	return nil
}

func (s *SessionMgrActor) checkInstanceIsRunning(ctx types.Context, instanceId string, dsType shared.DataSourceType) bool {
	var retryCount int8
LOOP:
	//check instance state
	retryCount += 1
	if retryCount > 3 {
		log.Info(ctx, "Check instance status exceed over 3 times,quit")
		return false
	}
	describeDBInstanceDetailReq := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
		Type:       dsType,
	}
	resp, err := s.source.DescribeDBInstanceDetail(ctx, describeDBInstanceDetailReq)
	if err != nil {
		return false
	}
	if resp.InstanceStatus != "Running" {
		time.Sleep(5 * time.Second)
		goto LOOP
	}
	return true
}

func (s *SessionMgrActor) giveBackSession(ctx types.Context, msg *shared.GiveBackSession) {
	if s.sessionPool == nil {
		log.Warn(ctx, "failed to give back session, the instance session pool not exists")
		return
	}
	if err := s.sessionPool.GiveBack(ctx, msg.SessionId); err != nil {
		log.Warn(ctx, "failed to give back session, err=%v", err)
	}
}

func (s *SessionMgrActor) closeSession(ctx types.Context, msg *shared.CloseInternalSession) {
	if s.sessionPool == nil {
		return
	}
	s.sessionPool.closeSession(ctx, msg.SessionId)
	ctx.Respond(&shared.CloseInternalSessionSucceed{})
}

func (s *SessionMgrActor) getAccountPassword(key string, instanceId string) string {
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(instanceId))
	expectedMac := hex.EncodeToString(mac.Sum(nil))
	return "Dbw_" + expectedMac[:26]
}

func (s *SessionMgrActor) isAccountExist(ctx types.Context, dsType shared.DataSourceType, instanceId string, accountName string) bool {
	desReq := &datasource.DescribeAccountsReq{
		DSType:      dsType,
		InstanceId:  instanceId,
		AccountName: accountName,
		PageNumber:  1,
		PageSize:    500,
	}
	resp, err := s.source.DescribeAccounts(ctx, desReq)
	if err != nil {
		log.Warn(ctx, "failed to list accounts, err=%v", err)
		return false
	}

	return fp.StreamOf(resp.AccountsInfo).Reject(func(account *rdsModel_v2.AccountsInfoObject) bool {
		return account.AccountName != accountName
	}).Exists()
}

func (s *SessionMgrActor) createAccount(ctx types.Context, instanceId string, dsType shared.DataSourceType) error {
	if err := s.source.CreateAccount(ctx, &datasource.CreateAccountReq{
		DSType:          dsType,
		InstanceId:      instanceId,
		AccountName:     s.dbwAccountName,
		AccountPassword: s.getAccountPassword(s.dbwAccountPasswordGenKey, instanceId),
		AccountType:     rdsModel_v2.AccountType_Normal.String(),
	}); err != nil {
		log.Warn(ctx, "failed to create account, err=%v", err)
		return err
	}
	return nil
}

func (s *SessionMgrActor) deleteAccount(ctx types.Context, instanceId string, dsType shared.DataSourceType) error {
	// 我们直接调用删除接口，确保删除调这个账号后重建
	if err := s.source.DeleteAccount(ctx, &datasource.DeleteAccountReq{
		DSType:      dsType,
		InstanceId:  instanceId,
		AccountName: s.dbwAccountName,
	}); err != nil {
		log.Warn(ctx, "failed to delete account, err=%v", err)
		return err
	}
	return nil
}

func (s *SessionMgrActor) isInnerAccount(userName string) bool {
	return userName == s.dbwAccountName
}

func (s *SessionMgrActor) isOpenAccountSysDb(ctx types.Context) bool {
	cnf := s.cnf.Get(ctx)
	return cnf.IsOpenAccountSysDB
}

func (s *SessionMgrActor) grantAccountPrivilegeOnSystemDB(ctx types.Context, instanceId string, dsType shared.DataSourceType) error {

	switch dsType {
	case shared.MySQL:
		if err := s.source.GrantAccountPrivilege(ctx, &datasource.GrantAccountPrivilegeReq{
			DSType:      dsType,
			InstanceId:  instanceId,
			AccountName: s.dbwAccountName,
			DBName:      "sys",
			//AccountPrivilege:    rdsModel.AccountPrivilege_Custom.String(),
			//AccountPrivilegeStr: "SELECT,INSERT,UPDATE,DELETE,CREATE,DROP,REFERENCES,INDEX,ALTER,CREATE TEMPORARY TABLES,LOCK TABLES,EXECUTE,CREATE VIEW,SHOW VIEW,EVENT,TRIGGER,CREATE ROUTINE,ALTER ROUTINE",
			//AccountPrivilegeStr: "SELECT",
		}); err != nil {
			log.Warn(ctx, "failed to grant privilege on sys, err=%v", err)
			return err
		}
		// 不能给mysql库加权限，会报错
		//if err := s.source.GrantAccountPrivilege(ctx, &datasource.GrantAccountPrivilegeReq{
		//	DSType:      dsType,
		//	InstanceId:  instanceId,
		//	AccountName: s.dbwAccountName,
		//	DBName:      "mysql",
		//}); err != nil {
		//	log.Warn(ctx, "failed to grant privilege on mysql, err=%v", err)
		//	return err
		//}
		if err := s.source.GrantAccountPrivilege(ctx, &datasource.GrantAccountPrivilegeReq{
			DSType:      dsType,
			InstanceId:  instanceId,
			AccountName: s.dbwAccountName,
			DBName:      "performance_schema",
			//AccountPrivilege:    rdsModel.AccountPrivilege_Custom.String(),
			//AccountPrivilegeStr: "SELECT",
		}); err != nil {
			log.Warn(ctx, "failed to grant privilege on performance_schema, err=%v", err)
			return err
		}
	case shared.VeDBMySQL:
		// 新版本创建dbw_admin账号,无需再授权
		//if err := s.source.GrantAccountPrivilege(ctx, &datasource.GrantAccountPrivilegeReq{
		//	DSType:      dsType,
		//	InstanceId:  instanceId,
		//	AccountName: s.dbwAccountName,
		//	DBName:      "sys",
		//}); err != nil {
		//	log.Warn(ctx, "failed to grant privilege on sys, err=%v", err)
		//	return err
		//}
		//if err := s.source.GrantAccountPrivilege(ctx, &datasource.GrantAccountPrivilegeReq{
		//	DSType:      dsType,
		//	InstanceId:  instanceId,
		//	AccountName: s.dbwAccountName,
		//	DBName:      "mysql",
		//}); err != nil {
		//	log.Warn(ctx, "failed to grant privilege on mysql, err=%v", err)
		//	return err
		//}
		//if err := s.source.GrantAccountPrivilege(ctx, &datasource.GrantAccountPrivilegeReq{
		//	DSType:      dsType,
		//	InstanceId:  instanceId,
		//	AccountName: s.dbwAccountName,
		//	DBName:      "performance_schema",
		//}); err != nil {
		//	log.Warn(ctx, "failed to grant privilege on performance_schema, err=%v", err)
		//	return err
		//}
	default:
		return nil
	}
	return nil
}

func (s *SessionMgrActor) addPrivilegeToDB(ctx types.Context, msg *shared.AddPrivilegeToDB) {
	if !s.checkInstanceIsRunning(ctx, msg.InstanceId, msg.DSType) {
		log.Warn(ctx, "checkInstanceError instance:%s in not running", msg.InstanceId)
		ctx.Respond(&shared.AddPrivilegeToDBFailed{
			ErrorMessage: consts.ErrorOf(model.ErrorCode_NotSupportAction).Error(),
		})
		return
	}
	switch msg.DSType {
	case shared.Postgres:
		s.addPostgrePrivilegeToDB(ctx, msg)
		return
	case shared.VeDBMySQL:
		// VeDB 不需要做额外授权逻辑
		ctx.Respond(&shared.AddPrivilegeToDBSucceed{})
		return
	default:
		s.addMySQLPrivilegeToDB(ctx, msg)
		return
	}
}

func (s *SessionMgrActor) addPostgrePrivilegeToDB(ctx types.Context, msg *shared.AddPrivilegeToDB) {
	// 暂时没有这个操作
	ctx.Respond(&shared.AddPrivilegeToDBSucceed{})
}

func (s *SessionMgrActor) addMySQLPrivilegeToDB(ctx types.Context, msg *shared.AddPrivilegeToDB) {
	// 授权系统库
	if err := s.grantAccountPrivilegeOnSystemDB(ctx, msg.GetInstanceId(), msg.DSType); err != nil {
		log.Warn(ctx, "instance:%s, grantAccountPrivilegeOnSystemDB err: ", msg.InstanceId, err.Error())
		ctx.Respond(&shared.AddPrivilegeToDBFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	// 授权用户库
	if err := s.addAccountPrivilegeToAllDB(ctx, msg.GetInstanceId(), msg.DSType, s.dbwAccountName); err != nil {
		log.Warn(ctx, "instance:%s, addAccountPrivilegeToAllDB err: ", msg.InstanceId, err.Error())
		ctx.Respond(&shared.AddPrivilegeToDBFailed{
			ErrorMessage: err.Error(),
		})
		return
	}
	ctx.Respond(&shared.AddPrivilegeToDBSucceed{})
}

func (s *SessionMgrActor) addAccountPrivilegeToAllDB(ctx context.Context, instanceId string, dsType shared.DataSourceType, AccountName string) error {
	// list all databases
	resp, err := s.source.ListDatabasesWithAccount(ctx, &datasource.ListDatabasesWithAccountReq{
		DSType:     dsType,
		InstanceId: instanceId,
		PageSize:   math.MaxInt32,
		PageNumber: 1,
	})
	log.Info(ctx, "ListDatabasesWithAccount resp is %s", utils.Show(resp))
	if err != nil {
		log.Warn(ctx, "failed to list databases, instanceId=%s, err=%v", instanceId, err)
		return err
	}

	// grant DML+DDL to databases
	var privileges []*datasource.ModifyAccountPrivilegeInfo
	fp.StreamOf(resp.DBInfos).Map(func(db *rdsModel.DBInfo) *datasource.ModifyAccountPrivilegeInfo {
		return &datasource.ModifyAccountPrivilegeInfo{
			DBName:     db.DBName,
			ActionType: rdsModel_v2.ActionType_Grant.String(),
			Privilege:  rdsModel_v2.PrivilegeType_ReadWrite.String(),
		}
	}).ToSlice(&privileges)

	// no db
	if len(privileges) == 0 {
		return nil
	}
	//check instance status
	describeDBInstanceDetailReq := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
		Type:       dsType,
	}
	describeDBInstanceDetailResp, err := s.source.DescribeDBInstanceDetail(ctx, describeDBInstanceDetailReq)
	if err != nil {
		return err
	}
	if describeDBInstanceDetailResp.InstanceStatus != "Running" {
		time.Sleep(5 * time.Second)
		describeDBInstanceDetailResp, err = s.source.DescribeDBInstanceDetail(ctx, describeDBInstanceDetailReq)
		if err != nil {
			return err
		}
	}
	log.Info(ctx, " InstanceStatus is %s ModifyAccountPrivilegeReq is %s", describeDBInstanceDetailResp.InstanceStatus, utils.Show(privileges))
	if err = s.source.ModifyAccountPrivilege(ctx, &datasource.ModifyAccountPrivilegeReq{
		DSType:                      dsType,
		InstanceId:                  instanceId,
		AccountName:                 AccountName,
		ModifyAccountPrivilegesInfo: privileges,
	}); err != nil {
		log.Warn(ctx, "failed to add privilege to account, instanceId=%s, "+
			"accountName=%s, err=%v", instanceId, AccountName, err)
		return err
	}
	return nil
}

func (s *SessionMgrActor) check(ctx types.Context) {
	now := time.Now().Unix()
	if now-s.lastCheckTime >= SessionMgrTimeIntervalSeconds {
		s.doWhenIdle(ctx)
		s.lastCheckTime = now
	}
}

func (s *SessionMgrActor) doWhenIdle(ctx types.Context) {
	if s.sessionPool == nil {
		return
	}
	// check session pool
	s.sessionPool.CheckSessions(ctx)

	// if long time no call, close self
	if time.Now().Unix()-s.lastRequestTime > s.closeIntervalSeconds {
		log.Info(ctx, "long time no call, close myself")
		s.sessionPool.Close(ctx)
		ctx.Stop(ctx.Self())
	}
}

func (s *SessionMgrActor) getDataSourceTypes(ctx context.Context) ([]model.DSType, error) {
	return []model.DSType{model.DSType_MySQL}, nil
}

func (s *SessionMgrActor) Initialization(ctx types.Context) {
	now := time.Now().Unix()
	s.lastRequestTime = now
	s.lastCheckTime = now
	c3Cfg := s.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	s.dbwAccountName = c3Cfg.DBWAccountName
	s.dbwAccountPasswordGenKey = c3Cfg.DbwAccountPasswordGenKey
	s.closeIntervalSeconds = SessionMgrCloseIntervalSeconds
}

func (s *SessionMgrActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, "[SessionMgrActor] panic, err=%v, stack=%s", r, debug.Stack())
		}
	}()
	fn()
}
