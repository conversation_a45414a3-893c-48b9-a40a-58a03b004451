package actors

import (
	"code.byted.org/infcs/protoactor-go/actor"
	"strconv"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/bwmarrin/snowflake"
)

func NewIDGenerator() types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.IDGenActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &IDGenerator{}
		}),
	}
}

type IDGenerator struct {
	node *snowflake.Node
}

func (a *IDGenerator) Process(ctx types.Context) {
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		a.loadState(ctx)
	case *shared.NextID:
		a.nextID(ctx, msg.Size_)
	}

}

func (a *IDGenerator) loadState(ctx types.Context) {
	n, err := snowflake.NewNode(a.nodeID(ctx))
	if err != nil {
		panic(err)
	}
	a.node = n
}

func (a *IDGenerator) nextID(ctx types.Context, size int64) {
	ret := make([]int64, size)
	for i := range ret {
		ret[i] = a.node.Generate().Int64()
	}
	ctx.Respond(&shared.IDList{Id: ret})
}

func (a *IDGenerator) nodeID(ctx types.Context) int64 {
	id, _ := strconv.ParseInt(ctx.GetName(), 10, 64)
	return id
}
