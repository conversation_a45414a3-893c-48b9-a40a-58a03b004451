package apigateway

import (
	"code.byted.org/gin/ginex"
	"github.com/gin-gonic/gin"
	// "code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/handler"
)

// customizeRegister register customize routers.
func customizeRegister(r *ginex.Engine) {
	// your code ...
}

func _action_mws() []gin.HandlerFunc {
	return nil
}

func _Action_mws() []gin.HandlerFunc {
	return nil
}

func _ActionGet_mws() []gin.HandlerFunc {
	return nil
}
