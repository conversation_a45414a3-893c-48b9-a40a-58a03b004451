package rpc

import (
	"context"
	"time"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/kite/kitex/pkg/connpool"
	"code.byted.org/kite/kitex/transport"

	CtrlClient "code.byted.org/infcs/dbw-mgr/gen/web-controller/kitex_gen/infcs/web/controller/controllerservice"
)

type Client interface{}

func GetClient() (CtrlClient.Client, error) {
	var opts []client.Option
	opts = append(opts, client.WithRPCTimeout(30*time.Second))
	opts = append(opts, client.WithConnectTimeout(5*time.Second))
	opts = append(opts, client.WithLongConnection(connpool.IdleConfig{
		MaxIdlePerAddress: 10,
		MaxIdleGlobal:     1000,
		MaxIdleTimeout:    120 * time.Second,
	}))
	opts = append(opts, client.WithTransportProtocol(transport.TTHeaderFramed))
	cli, err := CtrlClient.NewClient("dbw.mgr", opts...)
	if err != nil {
		log.Error(context.Background(), "Failed to mgr client")
		return nil, err
	}
	return cli, nil
}
