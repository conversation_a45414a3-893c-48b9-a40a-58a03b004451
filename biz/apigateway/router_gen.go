// Code generated by gintool. DO NOT EDIT.

package apigateway

import (
	"code.byted.org/gin/ginex"

	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/handler"
)

// register register routes based on the IDL 'api.${HTTP Method}' annotation.
func register(r *ginex.Engine) {
	customizeRegister(r)

	r.GET("/ping", handler.Ping)
	{
		_action := r.Group("/api", _action_mws()...)
		_action.POST("/", append(_Action_mws(), handler.Action)...)
		_action.GET("/*subfix", append(_ActionGet_mws(), handler.ActionGet)...)
	}
	{
		_action := r.Group("/action", _action_mws()...)
		_action.POST("/", append(_Action_mws(), handler.Action)...)
		_action.GET("/*subfix", append(_ActionGet_mws(), handler.ActionGet)...)
	}
}
