// Code generated by gintool.

package service

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"io/ioutil"

	"code.byted.org/gin/ginext/errors"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/common"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/gin-gonic/gin"
	"github.com/gofrs/uuid"
)

const (
	jwtTokenHeader = "X-Jwt-Token"
	logIDHeader    = "X-TT-LOGID"
)

type ActionService struct {
}

// check param valid
func (ss *ActionService) checkParam(ctx *gin.Context, req *model.Request) error {

	if req.Host == nil && ctx.Request.Host != "" {
		req.Host = &ctx.Request.Host
	}
	if token := ctx.GetHeader(jwtTokenHeader); token != "" {
		req.Authorization = token
	}
	var requestID string

	if requestID = ctx.GetHeader(logIDHeader); requestID != "" {
		req.TopRequestID = utils.StringRef(requestID)
	} else {
		requestID, _ := uuid.NewV4()
		req.TopRequestID = utils.StringRef(requestID.String())
	}
	httpCode := common.HTTPInternalError

	encoding := ctx.GetHeader("Content-Encoding")
	if encoding == "gzip" {
		body, err := gzip.NewReader(bytes.NewReader(req.ReqBytes))
		if err != nil {
			return errors.NewError(httpCode, httpCode, fmt.Sprintf("uncompress gzip data error, %s", err.Error()))
		}

		defer body.Close()
		data, err := ioutil.ReadAll(body)
		if err != nil {
			return errors.NewError(httpCode, httpCode, fmt.Sprintf("failed to read all data, %s", err.Error()))
		}
		req.ReqBytes = data
	}

	var err error
	err = securityCheck(ctx, req)
	if err != nil {
		return errors.NewError(common.HTTPAuthError, common.HTTPAuthError,
			fmt.Sprintf("failed to do security check, %s", err.Error()))
	}
	return nil
}

// execute
func (ss *ActionService) Execute(ctx *gin.Context, req *model.Request) (*model.Response, error) {
	resp := &model.Response{}
	resp.CommonResp = new(model.CommoneResp)
	resp.CommonResp.Action = req.Action
	resp.CommonResp.Service = req.Service
	resp.CommonResp.Version = req.Version
	resp.CommonResp.Region = req.Region
	if err := ss.checkParam(ctx, req); err != nil {
		setErrorResp(resp, "InvalidParameterValue", err.Error())
		return resp, err
	}
	err := sendRPC(ctx, req, resp)
	return resp, err
}

func setErrorResp(resp *model.Response, code, message string) {
	resp.CommonResp.Error = new(model.Error)
	if code == "" {
		code = "InternalError"
	}
	resp.CommonResp.Error.Code = code
	resp.CommonResp.Error.Message = message
}
