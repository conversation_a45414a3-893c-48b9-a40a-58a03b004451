package service

import (
	"context"
	"encoding/json"
	"time"

	"code.byted.org/gin/ginext/errors"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/kite/kitex/client/callopt"
	"code.byted.org/paas/cloud-sdk-go/jwt"
	"github.com/cloudwego/kitex/pkg/kerrors"
	"github.com/gin-gonic/gin"

	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/cache/user"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/common"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/model"
	gatewayBase "code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/model/base"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/rpc"
	MgrFramework "code.byted.org/infcs/dbw-mgr/gen/web-controller/kitex_gen/infcs/mgr/framework"
	"code.byted.org/infcs/dbw-mgr/gen/web-controller/kitex_gen/infcs/web/controller"
)

const (
	ByteCloudUserKey = "x-bytecloud-user"
	ByteCloudDepKey  = "x-bytecloud-department"
)

func securityCheck(ctx context.Context, req *model.Request) error {
	// TODO valid jwt-token
	if isValid, err := validateAuth(req.Authorization); err != nil {
		return err
	} else if !isValid {
		return errors.NewError(-1, 403, "token is invalid")
	}
	if err := fillUserByToken(ctx, req); err != nil {
		return err
	}
	return nil
}

func validateAuth(auth string) (bool, error) {
	return true, nil
}

func fillUserByToken(ctx context.Context, req *model.Request) error {
	var userInfo *jwt.UserInfo
	var err error
	userInfoStr, found := user.GlobalCache.Get(req.Authorization)
	if found {
		err = json.Unmarshal([]byte(userInfoStr), userInfo)
	}
	if !found || err != nil {
		region := req.Region
		if req.Region == "boe" || req.Region == "boe2" {
			region = "cn"
		}
		col := jwt.NewCollector(jwt.WithRegion(region))
		userInfo, err = col.Userinfo(ctx, req.Authorization)
		if err != nil {
			return err
		}
	}
	if req.Base == nil {
		req.Base = &gatewayBase.Base{}
	}
	if req.Base.Extra == nil {
		req.Base.Extra = make(map[string]string)
	}
	req.Base.Extra[ByteCloudUserKey] = userInfo.Username
	req.Base.Extra[ByteCloudDepKey] = userInfo.Department.Name
	return nil
}

func sendRPC(ctx *gin.Context, req *model.Request, resp *model.Response) (CallErr *errors.Error) {
	startTime := time.Now()
	var forwardLatency time.Duration
	var reqData []byte
	var err error
	reqData, err = json.Marshal(req)
	if err != nil {
		log.Warn(ctx, "Failed to marshal request data, %s", err.Error())
	} else {
		log.Info(ctx, "Request data: %s", reqData)
	}

	ssoUser := ctx.GetHeader("X-User-Name")
	accountID := ctx.GetHeader("X-Top-Account-Id")
	userID := ctx.GetHeader("X-Top-User-Id")
	tlsEnabled := ctx.Request.TLS != nil

	log.Info(ctx, "RequestID:%s SSOUser:%s AccountID:%s UserID:%s Service:%s Action:%s Data:%s TLS:%v",
		req.TopRequestID, ssoUser, accountID, userID, req.Service, req.Action, string(req.ReqBytes), tlsEnabled)
	requestID := *req.TopRequestID
	rpcReq := MgrFramework.NewRequest()
	rpcReq.PrivateReq = MgrFramework.NewPrivateReq()
	rpcReq.MgrReq = MgrFramework.NewMgrReq()

	var accountId32 *int32 = nil
	if req.AccountID != nil {
		a32 := int32(*req.AccountID)
		accountId32 = &a32
	}

	rpcReq.MgrReq.Ctx = &MgrFramework.JobContext{
		RequestID: requestID,
		Action:    req.Action,
		AccountID: accountId32,
		UserID:    req.UserID,
		Top: &MgrFramework.TopInfo{
			Region:  req.Region,
			Service: req.Service,
		},
		ProductStr: "Dbw",
	}

	if req.Source != nil {
		rpcReq.MgrReq.Ctx.Top.Source = *req.Source
	}
	if req.RealIP != nil {
		rpcReq.MgrReq.Ctx.Top.RealIP = *req.RealIP
	}
	domain := ctx.GetHeader("X-Cluster-Domain")
	if domain != "" {
		rpcReq.MgrReq.Ctx.ClusterDomain = &domain
	}

	if req.Psm != nil {
		rpcReq.MgrReq.Ctx.Top.RealIP = *req.Psm
	}
	if req.Site != nil {
		rpcReq.MgrReq.Ctx.Top.RealIP = *req.Site
	}
	if req.Version != "" {
		rpcReq.MgrReq.Ctx.Top.Version = req.Version
	}

	rpcReq.MgrReq.Ctx.Extra = make(map[string]string)
	if req.MgrService != nil {
		rpcReq.MgrReq.Ctx.Extra["MgrService"] = *req.MgrService
	}

	// Add the header to rpc request.
	for k := range ctx.Request.Header {
		rpcReq.MgrReq.Ctx.Extra[k] = ctx.Request.Header.Get(k)
	}
	for k := range req.Base.Extra {
		rpcReq.MgrReq.Ctx.Extra[k] = req.Base.Extra[k]
	}
	isOpsRequest := false
	if req.AccountID != nil && *req.AccountID == 1 {
		isOpsRequest = true
	}
	rpcReq.MgrReq.Ctx.Top.IsOpsRequest = isOpsRequest

	if req.ReqBytes == nil {
		empty := make(map[string]interface{})
		out, err := json.Marshal(empty)
		if err != nil {
			log.Error(ctx, "Failed to marshal empty json object, %s", err.Error())
			return errors.NewError(int(controller.Error_InvalidParam), common.HTTPBadRequest, err.Error())
		}
		req.ReqBytes = out
	}

	rpcReq.PrivateReq.ReqBytes = req.ReqBytes
	httpErr := errors.NewError(int(controller.Error_OK), common.HTTPSuccess, "")
	rpcCli, err := rpc.GetClient()
	if err != nil {
		log.Error(ctx, "Failed to get rpc client, %s", err.Error())
		return errors.NewError(int(controller.Error_ConnErr), common.HTTPBadGateway, err.Error())
	}

	var rpcResp *MgrFramework.Response
	rpcResp, err = rpcCli.Action(ctx, rpcReq, callopt.WithHostPort(config.Cfg.Get(ctx).MGRAddress))
	forwardLatency = time.Since(startTime)

	if err != nil {
		log.Error(ctx, "[%s] RPC action %s:%s err:%s", requestID, req.Service, req.Action, err.Error())
		resp.CommonResp.Error = new(model.Error)
		resp.CommonResp.Error.Code = "InternalError"
		resp.CommonResp.Error.Message = err.Error()

		if kerrors.IsTimeoutError(err) {
			httpErr.HttpCode = common.HTTPGatewayTimeout
		} else {
			httpErr.HttpCode = common.HTTPBadGateway
		}
		httpErr.Code = int(controller.Error_InternalErr)
		resp.CommonResp.RequestID = requestID
		httpErr.Message = err.Error()
	}

	if rpcResp != nil {
		if rpcResp.MgrResp != nil && rpcResp.MgrResp.Common != nil {
			if requestID != "" {
				resp.CommonResp.RequestID = requestID
			} else {
				resp.CommonResp.RequestID = rpcResp.MgrResp.Common.RequestID
			}

			httpErr.Code = int(rpcResp.MgrResp.Common.Status.Code)
			if rpcResp.MgrResp.Common.Status.HTTPCode != int64(common.HTTPSuccess) {
				httpErr.HttpCode = int(rpcResp.MgrResp.Common.Status.HTTPCode)
				resp.CommonResp.Error = new(model.Error)

				if rpcResp.MgrResp.Common.Status.Status != "" {
					resp.CommonResp.Error.Code = rpcResp.MgrResp.Common.Status.Status
				}
				if rpcResp.MgrResp.Common.Status.Message != "" {
					resp.CommonResp.Error.Message = rpcResp.MgrResp.Common.Status.Message
				}

				if rpcResp.MgrResp.Common.Status.Details != nil && isOpsRequest {
					resp.CommonResp.Error.Details = new(model.Details)
					resp.CommonResp.Error.Details.DebugInfo = new(model.DebugInfo)
					resp.CommonResp.Error.Details.DebugInfo.Detail = rpcResp.MgrResp.Common.Status.Details.DebugInfo.Detail
					resp.CommonResp.Error.Details.DebugInfo.StackEntries = rpcResp.MgrResp.Common.Status.Details.DebugInfo.StackEntries
				}
			}
		}

		if rpcResp.PrivateResp != nil {
			resp.RespBytes = rpcResp.PrivateResp.RespBytes
		}
	}

	log.Info(ctx, "[%s] RPC call  done, take %s, dns resolve: %s, error:%v", requestID,
		time.Since(startTime).String(), forwardLatency.String(), err)
	return httpErr
}
