// Code generated by gintool.

package service

import (
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/model"
	"github.com/gin-gonic/gin"
)

type ActionGetService struct {
}

// check param valid
func (ss *ActionGetService) checkParam(ctx *gin.Context, req *model.Request) error {

	return nil
}

// execute
func (ss *ActionGetService) Execute(ctx *gin.Context, req *model.Request) (*model.Response, error) {
	if err := ss.checkParam(ctx, req); err != nil {
		return nil, err
	}
	resp := &model.Response{}
	//code local service
	return resp, nil
}
