// Code generated by gintool beta.

package handler

import (
	"code.byted.org/gin/ginext/binding"
	"code.byted.org/gopkg/logs"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/model"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/response"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/service"
	"github.com/gin-gonic/gin"
)

// Action .
// @router /action/ [POST]
func Action(ctx *gin.Context) {
	var err error
	method := "Action"
	var req model.Request
	err = binding.BindAndValidate(ctx, &req)
	if err != nil {
		logs.CtxError(ctx, "bind param failed, err:%v", err)
		response.Response(ctx, err, nil, method)
		return
	}
	var ss service.ActionService
	resp, err := ss.Execute(ctx, &req)
	if err != nil {
		response.Response(ctx, err, resp, method)
		return
	}
	err = binding.WriteHeader(ctx, resp)
	if err != nil {
		response.Response(ctx, err, nil, method)
		return
	}
	response.Response(ctx, nil, resp, method)
}
