// Code generated by gintool.

package response

import (
	"bytes"
	"encoding/json"
	"reflect"
	"strings"
	"unsafe"

	"code.byted.org/gin/ginext/errors"
	"code.byted.org/gopkg/logs"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/config"
	log "code.byted.org/infcs/lib-log"
	"github.com/gin-gonic/gin"
)

func GetSerializer(ctx *gin.Context, method string) string {
	//define your serializer

	//get serializer from api.serializer
	if serizlizer, ok := config.ApiSerializerMap[method]; ok {
		if serizlizer != "" {
			return serizlizer
		}
	}
	//get serizlizer from content type
	defaultSerializer := "json"
	if ctx.ContentType() == config.MIMEPROTOBUF {
		defaultSerializer = config.SERIZLIZERPB
	}
	return defaultSerializer
}

// genResponse ...
func genJsonResponse(code int, msg string, data interface{}) gin.H {
	return gin.H{
		"status_code": code,
		"data":        data,
		"msg":         msg,
	}
}

func Response(ctx *gin.Context, err error, obj interface{}, method string) {
	serializer := GetSerializer(ctx, method)
	httpCode := 500
	message := "internal error"
	statusCode := 0
	if err == nil {
		httpCode = 200
		message = "success"
	} else {
		message = err.Error()
		statusCode = -1
		httpCode = 400
	}
	if dalErr, ok := err.(*errors.Error); ok {
		httpCode = dalErr.HttpCode
		message = dalErr.Error()
		statusCode = dalErr.Code
	}
	logs.CtxNotice(ctx, "process done, uri:%v, code:%v, message:%v",
		ctx.Request.RequestURI, httpCode, message)
	if serializer == config.SERIZLIZERJSON {
		ctx.JSON(httpCode, genJsonResponseV2(statusCode, message, obj))
		return
	}
	ctx.ProtoBuf(httpCode, obj)
}

// genResponse ...
func genJsonResponseV2(code int, msg string, s interface{}) map[string]interface{} {
	if IsNil(s) {
		return gin.H{
			"status_code": code,
			"msg":         msg,
		}
	}
	ret := make(map[string]interface{})
	v := reflect.ValueOf(s)
	t := reflect.TypeOf(s)
	kind := t.Kind()
	if kind == reflect.Ptr {
		v = v.Elem()
		kind = v.Kind()
	}
	//分析s变量的类型，如果是结构体类型，那么遍历所有的字段
	switch kind {
	case reflect.Struct:
		//NumFiled()获取字段数，v.Field(i)可以取得下标位置的字段信息，返回的是一个Value类型的值
		for i := 0; i < v.NumField(); i++ {
			field := v.Field(i)
			ft := v.Type().Field(i)
			firstLetter := string(ft.Name[0])
			if strings.ToUpper(firstLetter) != firstLetter {
				//private field
				continue
			}
			name := SnakeString(ft.Name)
			if jsonTag := GetFieldTag(ft, "json"); jsonTag != "" {
				name = jsonTag
			}
			if name == "-" {
				continue
			}

			// Change the result info from json string to json object.
			if name == "Result" && !field.IsNil() {
				var result map[string]interface{}
				d := json.NewDecoder(bytes.NewReader(field.Bytes()))
				d.UseNumber()
				err := d.Decode(&result)
				if err != nil {
					log.Error("Failed to parse data:%s", string(field.Bytes()))
					continue
				}
				ret[name] = result
			} else {
				ret[name] = field.Interface()
			}
		}
	default:
		name := t.Name()
		ret[name] = v.Interface()
	}

	// Do not return useless status code and message, the response body already have the same messages.
	//ret["status_code"] = code
	//ret["msg"] = msg
	return ret
}

func GetFieldTag(field reflect.StructField, tagName string) string {
	defaultTag := ""
	tagStr := field.Tag.Get(tagName)
	if tagStr == defaultTag {
		return defaultTag
	}
	tags := strings.Split(tagStr, ",")
	if len(tags) >= 0 {
		return tags[0]
	}
	return defaultTag
}

func IsNil(s interface{}) bool {
	if s == nil {
		return true
	}
	vi := reflect.ValueOf(s)
	if vi.Kind() == reflect.Ptr {
		return vi.IsNil()
	}
	return false
}

// SnakeString converts the accepted string to a snake string (XxYy to xx_yy)
func SnakeString(s string) string {
	data := make([]byte, 0, len(s)*2)
	j := false
	for _, d := range StringToBytes(s) {
		if d >= 'A' && d <= 'Z' {
			if j {
				data = append(data, '_')
				j = false
			}
		} else if d != '_' {
			j = true
		}
		data = append(data, d)
	}
	return strings.ToLower(BytesToString(data))
}

// BytesToString convert []byte type to string type.
func BytesToString(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

// StringToBytes convert string type to []byte type.
// NOTE: panic if modify the member value of the []byte.
func StringToBytes(s string) []byte {
	sp := *(*[2]uintptr)(unsafe.Pointer(&s))
	bp := [3]uintptr{sp[0], sp[1], sp[1]}
	return *(*[]byte)(unsafe.Pointer(&bp))
}
