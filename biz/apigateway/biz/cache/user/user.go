package user

import (
	"time"

	"github.com/patrickmn/go-cache"
)

var (
	defaultTTL           = 20 * time.Minute
	defaultCleanInterval = 1 * time.Minute
	GlobalCache          Cache
)

type Cache interface {
	Get(key string) (value string, found bool)
	Set(key, value string)
}

func init() {
	GlobalCache = NewUserCache()
}

func NewUserCache() Cache {
	return &userCache{
		cache: cache.New(defaultTTL, defaultCleanInterval),
	}
}

type userCache struct {
	cache *cache.Cache
}

func (u *userCache) Get(key string) (value string, found bool) {
	var v interface{}
	v, found = u.cache.Get(key)
	if !found {
		return
	}
	var ok bool
	if value, ok = v.(string); !ok {
		found = ok
	}
	return
}

func (u *userCache) Set(key, value string) {
	u.cache.Set(key, value, defaultTTL)
}
