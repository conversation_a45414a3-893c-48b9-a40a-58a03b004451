// This file is automatically generated. Do not modify.

package base

import (
	"fmt"
)

var _ = fmt.Sprintf

type Base struct {
	LogID      string            `form:"LogID" json:"LogID" query:"LogID"`
	Caller     string            `form:"Caller" json:"Caller" query:"Caller"`
	Addr       string            `form:"Addr" json:"Addr" query:"Addr"`
	Client     string            `form:"Client" json:"Client" query:"Client"`
	TrafficEnv *TrafficEnv       `form:"TrafficEnv,omitempty" json:"TrafficEnv,omitempty" query:"TrafficEnv,omitempty"`
	Extra      map[string]string `form:"Extra,omitempty" json:"Extra,omitempty" query:"Extra,omitempty"`
}

type BaseResp struct {
	StatusMessage string            `form:"StatusMessage" json:"StatusMessage" query:"StatusMessage"`
	StatusCode    int32             `form:"StatusCode" json:"StatusCode" query:"StatusCode"`
	Extra         map[string]string `form:"Extra,omitempty" json:"Extra,omitempty" query:"Extra,omitempty"`
}

type TrafficEnv struct {
	Open bool   `form:"Open" json:"Open" query:"Open"`
	Env  string `form:"Env" json:"Env" query:"Env"`
}
