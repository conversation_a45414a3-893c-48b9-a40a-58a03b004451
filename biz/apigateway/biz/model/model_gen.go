// This file is automatically generated. Do not modify.

package model

import (
	"fmt"

	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/model/base"
)

var _ = fmt.Sprintf

type CommoneResp struct {
	RequestID string `form:"RequestId,required" json:"RequestId"`
	Action    string `form:"Action,required" json:"Action"`
	Version   string `form:"Version,required" json:"Version"`
	Service   string `form:"Service,required" json:"Service"`
	Region    string `form:"Region,required" json:"Region"`
	Error     *Error `form:"Error" json:"Error,omitempty"`
}

// Describes additional debugging info.
type DebugInfo struct {

	// The stack trace entries indicating where the error occurred.
	StackEntries [][]byte `form:"StackEntries,omitempty" json:"StackEntries,omitempty" query:"StackEntries,omitempty"`

	// Additional debugging information provided by the server.
	Detail *string `form:"Detail,omitempty" json:"Detail,omitempty" query:"Detail,omitempty"`
}

type Details struct {
	DebugInfo *DebugInfo `form:"DebugInfo,omitempty" json:"DebugInfo,omitempty" query:"DebugInfo,omitempty"`
}

type Error struct {

	// The string version of error code.
	Code string `form:"Code,required" json:"Code" query:"Code,required"`

	// A developer-facing error message, which should be in English. Any
	// user-facing error message should be localized and sent in the Details.
	Message string `form:"Message,required" json:"Message" query:"Message,required"`

	// Additional error information that the client code can use to handle
	// the error, such as debug information or a localized message.
	Details *Details `form:"Details,omitempty" json:"Details,omitempty" query:"Details,omitempty"`
}

// Provides a localized error message that is safe to return to the user
// which can be attached to an RPC error.
type LocalizedMessage struct {

	// The locale used following the specification defined at
	// http://www.rfc-editor.org/rfc/bcp/bcp47.txt.
	// Examples are: "en-US", "fr-CH", "es-MX"
	Locale string `form:"Locale" json:"Locale" query:"Locale"`

	// The localized error message in the above locale.
	Message string `form:"Message" json:"Message" query:"Message"`
}

type Request struct {

	// URL query part.
	Action  string `json:"Action" query:"Action,required" vd:"$!='?'"`
	Version string `json:"Version" query:"Version,required" vd:"$!='?'"`

	// Header part.
	Accept        *string `header:"Accept" json:"Accept,omitempty" vd:"$!='?'"`
	ContentType   *string `header:"Content-Type" json:"ContentType,omitempty" vd:"$!='?'"`
	Host          *string `header:"Host" json:"Host,omitempty" vd:"$!='?'"`
	Date          string  `header:"X-Date,required" json:"Date" vd:"$!='?'"`
	Authorization string  `header:"Authorization,required" json:"Authorization" vd:"$!='?'"`

	// Information from top.
	UserID            *int32     `header:"X-Top-User-Id" json:"UserID,omitempty"`
	AccountID         *int32     `header:"X-Top-Account-Id" json:"AccountID,omitempty"`
	Region            string     `header:"X-Top-Region,required" json:"Region" vd:"$!='?'"`
	Service           string     `header:"X-Top-Service,required" json:"Service" vd:"$!='?'"`
	Source            *string    `header:"X-Top-Source" json:"Source,omitempty" vd:"$!='?'"`
	RealIP            *string    `header:"X-Top-Real-Ip" json:"RealIP,omitempty" vd:"$!='?'"`
	AccountIsInternal *string    `header:"X-Top-Account-IsInternal" json:"AccountIsInternal,omitempty" vd:"$!='?'"`
	RoleID            *string    `header:"X-Top-Role-Id" json:"RoleID,omitempty" vd:"$!='?'"`
	Psm               *string    `header:"X-Psm" json:"PSM,omitempty" vd:"$!='?'"`
	Site              *string    `header:"X-Top-Site" json:"Site,omitempty" vd:"$!='?'"`
	TopRequestID      *string    `header:"X-Top-Request-Id" json:"TopRequestID,omitempty" vd:"$!='?'"`
	MgrService        *string    `header:"X-Mgr-Service" json:"MgrService,omitempty" vd:"$!='?'"`
	ClusterDomain     *string    `header:"X-Cluster-Domain" json:"ClusterDomain,omitempty" vd:"$!='?'"`
	ReqBytes          []byte     `json:"-" raw_body:""`
	Base              *base.Base `json:"Base,omitempty"`
}

type Response struct {
	CommonResp *CommoneResp `form:"ResponseMetadata,required" json:"ResponseMetadata"`

	// binary data will be transformed to json object
	RespBytes []byte         `form:"Result,required" json:"Result"`
	BaseResp  *base.BaseResp `form:"-" json:"-,omitempty"`
}
