package config

import "code.byted.org/infcs/dbw-mgr/biz/service/config"

// Content-Type MIME of the most common data formats.
const (
	MIMEJSON     = "application/json"
	MIMEPROTOBUF = "application/x-protobuf"
)

// serizlizer method
const (
	SERIZLIZERPB   = "pb"
	SERIZLIZERJSON = "json"
)

var ApiSerializerMap = map[string]string{
	"Action":    "",
	"ActionGet": "",
}

var Cfg config.ConfigProvider
