// Code generated by gintool. DO NOT EDIT.

package debug

func init() {
	debugServer = &DebugServer{
		idlMap: make(map[string]string),
	}

	debugServer.SetIDL("../../../inf-openapi-gateway-idl/base.thrift", `namespace py base
namespace go base
namespace java com.bytedance.thrift.base

struct TrafficEnv {
    1: bool Open = false,
    2: string Env = "",
}

struct Base {
    1: string LogID = "",
    2: string Caller = "",
    3: string Addr = "",
    4: string Client = "",
    5: optional TrafficEnv TrafficEnv,
    6: optional map<string, string> Extra,
}

struct BaseResp {
    1: string StatusMessage = "",
    2: i32 StatusCode = 0,
    3: optional map<string, string> Extra,
}`)

	debugServer.SetIDL("../../../inf-openapi-gateway-idl/openapi-gateway-idl.thrift", `namespace go infcs.openapi.gateway
namespace py infcs.openapi.gateway

include "base.thrift"

// Describes additional debugging info.
struct DebugInfo {
    // The stack trace entries indicating where the error occurred.
    1: optional list<binary> StackEntries,

    // Additional debugging information provided by the server.
    2: optional string Detail,
}

// Provides a localized error message that is safe to return to the user
// which can be attached to an RPC error.
struct LocalizedMessage {
    // The locale used following the specification defined at
    // http://www.rfc-editor.org/rfc/bcp/bcp47.txt.
    // Examples are: "en-US", "fr-CH", "es-MX"
    1: string Locale,

    // The localized error message in the above locale.
    2: string Message,
}

struct Details {
    1: optional DebugInfo DebugInfo,
}

struct Error {
    // The string version of error code.
    1: required string Code,

    // A developer-facing error message, which should be in English. Any
    // user-facing error message should be localized and sent in the Details.
    2: required string Message,

    // Additional error information that the client code can use to handle
    // the error, such as debug information or a localized message.
    3: optional Details Details,
}

struct CommoneResp {
    1: required string RequestID (api.body="RequestId"),
    2: required string Action (api.body="Action"),
    3: required string Version (api.body="Version"),
    4: required string Service (api.body="Service"),
    5: required string Region (api.body="Region"),
    6: optional Error Error (api.body="Error"),
}

struct Request {
    // URL query part.
    1: required string Action (api.query="Action", api.vd="$!='?'"),
    2: required string Version (api.query="Version", api.vd="$!='?'"),

    // Header part.
    21: optional string Accept (api.header="Accept", api.vd="$!='?'"),
    22: optional string ContentType (api.header="Content-Type", api.vd="$!='?'")
    23: optional string Host (api.header="Host", api.vd="$!='?'"),
    24: required string Date (api.header="X-Date", api.vd="$!='?'"),
    25: required string Authorization (api.header="Authorization", api.vd="$!='?'"),

    // Information from top.
    27: optional i32 UserID (api.header="X-Top-User-Id"),
    28: optional i32 AccountID (api.header="X-Top-Account-Id"),
    29: required string Region (api.header="X-Top-Region", api.vd="$!='?'"),
    30: required string Service (api.header="X-Top-Service", api.vd="$!='?'")
    31: optional string Source (api.header="X-Top-Source", api.vd="$!='?'")
    32: optional string RealIP (api.header="X-Top-Real-Ip", api.vd="$!='?'")
    33: optional string AccountIsInternal (api.header="X-Top-Account-IsInternal", api.vd="$!='?'")
    34: optional string RoleID (api.header="X-Top-Role-Id", api.vd="$!='?'")
    35: optional string PSM (api.header="X-Psm", api.vd="$!='?'")
    36: optional string Site (api.header="X-Top-Site", api.vd="$!='?'")
    37: optional string TopRequestID (api.header="X-Top-Request-Id", api.vd="$!='?'")
    38: optional string MgrService (api.header="X-Mgr-Service", api.vd="$!='?'")
    39: optional string ClusterDomain (api.header="X-Cluster-Domain", api.vd="$!='?'")


    100: optional binary reqBytes (api.raw_body=""),

    255: optional base.Base Base,
}

struct Response {
    1: required CommoneResp CommonResp (api.body="ResponseMetadata"),

    50: required binary respBytes (api.body="Result"), // binary data will be transformed to json object

    255: optional base.BaseResp BaseResp (api.body="-"),
}

service gateway {
    Response Action(1: Request req) (api.POST='/action/')
    Response ActionGet(1: Request req) (api.GET='/action/*subfix')
}`)

	curVersion := "v2.2.42-beta"
	if curVersion != "" {
		debugServer.SetGintoolVersion(curVersion)
	}
	psm := ""
	if psm != "" {
		debugServer.SetPsm(psm)
	}

}
