// Code generated by gintool. DO NOT EDIT.

// Code generated by gintool. DO NOT EDIT.

package debug

import (
	"bytes"
	"encoding/json"
	"net/http"

	"code.byted.org/gin/ginex"
)

type ThriftFiles struct {
	PSM   string
	Files map[string]string
}

type ServerInfo struct {
	Server []ThriftFiles
}

type DebugServer struct {
	psm            string
	idlMap         map[string]string
	gintoolVersion string
	rootTree       string //json格式表示idl解析结构
}

var debugServer *DebugServer

func StartDebugServer() {
	ginex.RegisterDebugHandler("/runtime/gintool", getRuntimeInfo)
}

func (s *DebugServer) SetIDL(fname, content string) {
	s.idlMap[fname] = content
}

func (s *DebugServer) SetPsm(psm string) {
	s.psm = psm
}

func (s *DebugServer) SetGintoolVersion(version string) {
	s.gintoolVersion = version
}

func (s *DebugServer) SetRootTree(rootTree string) {
	s.rootTree = rootTree
}

func getRuntimeInfo(resp http.ResponseWriter, request *http.Request) {
	resp.WriteHeader(200)
	thriftFiles := ThriftFiles{
		PSM:   debugServer.psm,
		Files: debugServer.idlMap,
	}
	serverInfo := &ServerInfo{
		Server: []ThriftFiles{
			thriftFiles,
		},
	}
	respData, _ := json.Marshal(serverInfo)
	var out bytes.Buffer
	json.Indent(&out, respData, "", "\t")
	resp.Write(out.Bytes())
}
