// Code generated by gintool.

package apigateway

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync/atomic"
	"syscall"

	"code.byted.org/gin/ginex"
	netex "code.byted.org/gin/ginex/net"
	gatewayConfig "code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway/biz/debug"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/pkg/errors"
	// "code.byted.org/gin/ginext/shutdown"
)

func NewGatewayService(conf config.ConfigProvider) GatewayService {
	return &gatewayServiceImpl{
		conf: conf,
	}
}

type GatewayService interface {
	Start() error
}

type gatewayServiceImpl struct {
	conf config.ConfigProvider
}

func (g *gatewayServiceImpl) Start() error {
	if !g.conf.Get(context.Background()).EnableAPIGateway {
		return nil
	}
	gatewayConfig.Cfg = g.conf
	ginex.Init()
	r := ginex.Default()
	register(r)

	debug.StartDebugServer()

	var listener net.Listener
	var err error
	if listener, err = createListener(g.conf.Get(context.TODO()).APIGatewayListen); nil != err {
		return err
	}
	errCh := make(chan error, 1)
	var activeConnCount int32
	server := &http.Server{
		Handler: r,
		ConnState: func(c net.Conn, cs http.ConnState) {
			if cs == http.StateNew {
				atomic.AddInt32(&activeConnCount, 1)
			} else if cs == http.StateClosed || cs == http.StateHijacked {
				atomic.AddInt32(&activeConnCount, -1)
			}
		},
	}
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Error(context.Background(), "server listen panic: %v", r)
				errCh <- fmt.Errorf("server listen panic: %v", r)
			}
		}()
		errCh <- netex.ListenAndServe(listener, server)
	}()
	go func() {
		waitSignal(errCh)
	}()
	log.Info(context.Background(), "apigateway started")
	return nil
}

func createListener(addr string) (net.Listener, error) {
	var listener net.Listener
	var err error
	listener, err = netex.ListenWithConfig("tcp", addr, false)
	if nil != err {
		return nil, err
	}
	return listener, err
}

func waitSignal(errCh <-chan error) error {
	stopCh := make(chan os.Signal, 1)
	signal.Notify(stopCh, syscall.SIGINT, syscall.SIGHUP, syscall.SIGTERM)

	for {
		select {
		case sig := <-stopCh:
			return errors.New(sig.String())
		case err := <-errCh:
			return err
		}
	}
}
