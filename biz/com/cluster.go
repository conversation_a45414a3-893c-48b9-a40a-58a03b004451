package com

import (
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	infraModel "code.byted.org/infcs/dbw-mgr/gen/web-infra/kitex_gen/infcs/web/inframgmt"
	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
	"golang.org/x/sync/singleflight"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

type ClusterControl interface {
	GetCurrentCluster(ctx context.Context) (*infraModel.KubeCluster, error)
	GetKubeByClusterInfo(ctx context.Context, azInfo string) (*K8sCli, error)
}

type NewClusterControlIn struct {
	dig.In
	InfraMgrProvider mgr.Provider `name:"infra"`
}

func NewClusterControl(p NewClusterControlIn) ClusterControl {
	return &clusterControl{
		mgr:                  p.InfraMgrProvider,
		single:               &singleflight.Group{},
		currentCluster:       nil,
		lastUpdateCurCluster: time.Time{},
	}
}

type clusterControl struct {
	mgr mgr.Provider

	// internal info
	single               *singleflight.Group
	currentCluster       *infraModel.KubeCluster
	lastUpdateCurCluster time.Time
}

// GetCurrentCluster 获取当前所在集群
// 如何知道当前mgr所在集群？使用拉到所有集群信息后，根据集群的domain和当前环境变量 BDC_K8S_DOMAIN匹配，得到ClusterName。
func (self *clusterControl) GetCurrentCluster(ctx context.Context) (*infraModel.KubeCluster, error) {
	if self.currentCluster != nil && time.Now().Sub(self.lastUpdateCurCluster) < 300*time.Second {
		return self.currentCluster, nil
	}

	_, err, _ := self.single.Do("GetCurrentCluster", func() (interface{}, error) {
		allCluster, err := self.ListCluster(ctx)
		if err != nil {
			log.Warn(ctx, "Err %s", err.Error())
			return nil, err
		}

		podK8sDomain := os.Getenv(`BDC_K8S_DOMAIN`)
		for _, v := range allCluster {
			if v.Domain == podK8sDomain {
				self.currentCluster = v
				self.lastUpdateCurCluster = time.Now()
				return nil, nil
			}
		}
		return nil, errors.New("Not find current cluster")
	})

	if err != nil {
		log.Warn(ctx, "Err %s", err.Error())
		return nil, err
	}

	return self.currentCluster, nil
}

// 获取k8s客户端直接可用的配置
func (self *clusterControl) GetKubeByClusterInfo(ctx context.Context, clusterName string) (*K8sCli, error) {

	resp, err := self.GetKubeConfig(ctx, clusterName)
	if err != nil {
		log.Warn(ctx, "get kubeconfig of %s fail %v", clusterName, err)
		return nil, err
	}
	if len(resp.KubeConfigs) == 0 {
		log.Warn(ctx, "get empty kubeconfig of %s", clusterName)
		return nil, fmt.Errorf("found no kubeconfig by %s", clusterName)
	}
	kubeConfigBinary := fp.KVStreamOf(resp.KubeConfigs).Values().First().Bytes()
	os.MkdirAll("run", 0755)
	file, err := ioutil.TempFile("run", fmt.Sprintf("kubeconfig-%s", clusterName))
	if err != nil {
		log.Warn(ctx, "create kubeconfig file of %s fail %v", clusterName, err)
		return nil, err
	}
	defer file.Close()

	_, err = file.Write(kubeConfigBinary)
	if err != nil {
		log.Warn(ctx, "Write kube config file error %s", err.Error())
		return nil, err
	}

	cfg, err := clientcmd.BuildConfigFromFlags("", file.Name())
	if err != nil {
		log.Warn(ctx, "Err %s", err.Error())
		return nil, nil
	}

	cs, err := kubernetes.NewForConfig(cfg)
	if err != nil {
		log.Warn(ctx, "create kubeconfig file of %s fail %v", clusterName, err)
		return nil, err
	}
	return &K8sCli{
		Clientset: cs,
	}, nil
}

// copy from infra svc
func (self *clusterControl) GetKubeConfig(ctx context.Context, clusterName string) (*infraModel.GetKubeConfigResp, error) {
	req := &infraModel.GetKubeConfigReq{
		ClusterName: clusterName,
		Zones:       []string{},
		Region:      os.Getenv(`BDC_REGION_ID`),
	}
	res := &infraModel.GetKubeConfigResp{}
	err := self.mgr.Get().Call(ctx, infraModel.Action_GetKubeConfig.String(), req, res)
	return res, err
}

func (self *clusterControl) ListCluster(ctx context.Context) ([]*infraModel.KubeCluster, error) {
	req := &infraModel.ListClustersReq{
		Region: os.Getenv(`BDC_REGION_ID`),
	}
	res := &infraModel.ListClustersResp{}
	if err := self.mgr.Get().Call(ctx, infraModel.Action_ListClusters.String(), req, res); err != nil {
		log.Warn(ctx, "list cluster fail %v", err)
		return nil, err
	}
	return res.Clusters, nil
}

type KubeCluster struct {
	infraModel.KubeCluster
}

func (self *KubeCluster) GetRegion() string {
	return self.Region
}

func (self *KubeCluster) GetClusterName() string {
	return self.Name
}
