package com

import (
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/ds-lib/common/log"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type K8sSecretProvider interface {
	GetSecretStrData(ctx context.Context, secretNamespace string, secretName string) (map[string]string, error)
	PutSecretStrData(ctx context.Context, secretNamespace string, secretName string, data map[string]string) error
}

func NewK8sSecretProvider(k8sPro K8sClientsetProvider) K8sSecretProvider {
	return &k8sSecretProviderImpl{k8sPro: k8sPro}
}

type k8sSecretProviderImpl struct {
	k8sPro K8sClientsetProvider
}

func (impl *k8sSecretProviderImpl) GetSecretStrData(ctx context.Context, secretNamespace string, secretName string) (map[string]string, error) {
	cliset, err := impl.k8sPro.GetCluster(ctx, nil)
	if err != nil {
		log.Warn(ctx, "GetSecretStrData [%s] [%s] err: %v", secretNamespace, secretName, err)
		return nil, err
	}
	secretClient := cliset.CoreV1().Secrets(secretNamespace)
	secret, err := secretClient.Get(ctx, secretName, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	configMap := make(map[string]string)
	for configKey, value := range secret.Data {
		configMap[configKey] = utils.Bytes2String(value)
	}
	return configMap, nil
}

// dts-mgr
func (impl *k8sSecretProviderImpl) PutSecretStrData(ctx context.Context, secretNamespace string, secretName string, data map[string]string) error {
	secret := &v1.Secret{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Name:      secretName,
			Namespace: secretNamespace,
		},
		Immutable:  nil,
		StringData: data,
		Type:       "",
	}
	cliset, err := impl.k8sPro.GetCluster(ctx, nil)
	if err != nil {
		log.Warn(ctx, "PutSecretStrData [%s] [%s] err: %v", secretNamespace, secretName, err)
		return err
	}
	secretClient := cliset.CoreV1().Secrets(secretNamespace)

	oldSecret, err := secretClient.Get(ctx, secretName, metav1.GetOptions{})
	if err != nil {
		// 获取secret出错，尝试创建流程
		log.Warn(ctx, "get secret [%s] err try to create secret, error = %v", secretName, err)
		_, err = secretClient.Create(ctx, secret, metav1.CreateOptions{})
		if err != nil {
			// 创建流程出错，尝试更新
			_, err = secretClient.Update(ctx, secret, metav1.UpdateOptions{})
			log.Info(ctx, "Update Secret with Data %v err: %v", data, err)
		}
	} else if oldSecret != nil && oldSecret.Name != "" {
		log.Info(ctx, "secret [%s] already created,update it to %s", oldSecret.Name, secret.Name)
		_, err = secretClient.Update(ctx, secret, metav1.UpdateOptions{})
	} else {
		log.Info(ctx, "trying create secret [%s]", secret.Name)
		_, err = secretClient.Create(ctx, secret, metav1.CreateOptions{})
	}
	if err != nil {
		log.Warn(ctx, "create secret wrong error = %s ", err.Error())
	} else {
		log.Info(ctx, "create c3 config information secret [%s] success", secret.Name)
	}
	return err
}
