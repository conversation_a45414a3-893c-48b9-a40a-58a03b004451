package com

import (
	"context"
	"sync"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/k8s"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/kubernetes"
)

type _K8sClientsetProvider struct {
	multiK8s     map[string]*K8sCli
	multiK8sLock *sync.RWMutex
	az           ClusterControl
	config       config.ConfigProvider
}

func NewK8sClientsetProvider(az ClusterControl, config config.ConfigProvider) K8sClientsetProvider {
	return &_K8sClientsetProvider{
		az:           az,
		config:       config,
		multiK8s:     make(map[string]*K8sCli),
		multiK8sLock: new(sync.RWMutex),
	}
}

func (self *_K8sClientsetProvider) MustGetDefaultCluster(ctx context.Context) K8sClientset {
	r, err := self.GetCluster(ctx, nil)
	if err != nil {
		log.Warn(ctx, "GetCluster error %s", err.Error())
		panic(err)
	}
	return r

}

func (pro *_K8sClientsetProvider) GetCluster(ctx context.Context, cluster *entity.ClusterInfo) (K8sClientset, error) {
	return pro.getCli(ctx, cluster)
}

// 如果cluster信息为空，则使用默认的集群信息
func (pro *_K8sClientsetProvider) getCli(ctx context.Context, cluster *entity.ClusterInfo) (*K8sCli, error) {
	if cluster == nil {
		cur, err := pro.az.GetCurrentCluster(ctx)
		if err != nil {
			log.Warn(ctx, "GetCluster err %s", err.Error())
			return nil, err
		}

		cluster = &entity.ClusterInfo{
			ClusterName: cur.Name,
			// FIXME: NodePool
			NodePool: pro.config.Get(ctx).NodePool,
			// NodePool:  `mix-panel-a-default`,
			Namespace: consts.MgrNamespace,
		}
	}

	pro.multiK8sLock.RLock()
	if cli, ok := pro.multiK8s[cluster.GetClusterName()]; ok {
		pro.multiK8sLock.RUnlock()
		return cli, nil
	}
	pro.multiK8sLock.RUnlock()

	pro.multiK8sLock.Lock()
	defer pro.multiK8sLock.Unlock()

	if cli, ok := pro.multiK8s[cluster.GetClusterName()]; ok {
		return cli, nil
	}

	cli, err := pro.az.GetKubeByClusterInfo(ctx, cluster.GetClusterName())
	if err != nil {
		log.Warn(ctx, "GetKubeByClusterInfo err %s", err.Error())
		return nil, err
	}

	pro.multiK8s[cluster.GetClusterName()] = cli
	return cli, nil
}

type K8sCli struct {
	*kubernetes.Clientset
}

func (self *K8sCli) GetPodsByLabel(ctx context.Context, ns string, labelMap map[string]string) ([]v1.Pod, error) {
	if pods, err := self.CoreV1().Pods(ns).List(ctx, metav1.ListOptions{
		LabelSelector: labels.SelectorFromSet(labels.Set(labelMap)).String(),
	}); err != nil {
		return nil, err
	} else {
		return pods.Items, nil
	}
}

func (self *K8sCli) GetResource(jsonData string) (k8s.Resource, error) {
	v := &k8s.K8sClient{Clientset: self.Clientset}
	return v.GetResource(jsonData)
}
