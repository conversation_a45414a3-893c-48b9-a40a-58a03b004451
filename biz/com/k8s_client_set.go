package com

import (
	"context"

	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/ds-lib/framework/k8s"

	v1 "k8s.io/api/core/v1"
	appsv1 "k8s.io/client-go/kubernetes/typed/apps/v1"
	appsv1beta1 "k8s.io/client-go/kubernetes/typed/apps/v1beta1"
	batchv1 "k8s.io/client-go/kubernetes/typed/batch/v1"
	corev1 "k8s.io/client-go/kubernetes/typed/core/v1"
	rbacv1 "k8s.io/client-go/kubernetes/typed/rbac/v1"
)

type K8sClientsetProvider interface {
	MustGetDefaultCluster(context.Context) K8sClientset
	GetCluster(context.Context, *entity.ClusterInfo) (K8sClientset, error)
}

type K8sClientset interface {
	RbacV1() rbacv1.RbacV1Interface
	CoreV1() corev1.CoreV1Interface
	BatchV1() batchv1.BatchV1Interface
	AppsV1beta1() appsv1beta1.AppsV1beta1Interface
	AppsV1() appsv1.AppsV1Interface
	/* shortcuts */
	GetPodsByLabel(ctx context.Context, ns string, labelMap map[string]string) ([]v1.Pod, error)
	GetResource(jsonData string) (k8s.Resource, error)
}
