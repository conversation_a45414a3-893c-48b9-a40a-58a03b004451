// Code generated by MockGen. DO NOT EDIT.
// Source: biz/service/dbwinstance/dbwinstance.go

// Package dbwinstance is a generated GoMock package.
package dbwinstance

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/infcs/dbw-mgr/biz/entity"
	shared "code.byted.org/infcs/dbw-mgr/biz/shared"
	model "code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	gomock "github.com/golang/mock/gomock"
)

// MockDbwInstanceInterface is a mock of DbwInstanceInterface interface.
type MockDbwInstanceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDbwInstanceInterfaceMockRecorder
}

// MockDbwInstanceInterfaceMockRecorder is the mock recorder for MockDbwInstanceInterface.
type MockDbwInstanceInterfaceMockRecorder struct {
	mock *MockDbwInstanceInterface
}

// NewMockDbwInstanceInterface creates a new mock instance.
func NewMockDbwInstanceInterface(ctrl *gomock.Controller) *MockDbwInstanceInterface {
	mock := &MockDbwInstanceInterface{ctrl: ctrl}
	mock.recorder = &MockDbwInstanceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDbwInstanceInterface) EXPECT() *MockDbwInstanceInterfaceMockRecorder {
	return m.recorder
}

// CreateInstanceHistory mocks base method.
func (m *MockDbwInstanceInterface) CreateInstanceHistory(ctx context.Context, req *model.AddRecentlyUsedDBInstanceReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceHistory", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateInstanceHistory indicates an expected call of CreateInstanceHistory.
func (mr *MockDbwInstanceInterfaceMockRecorder) CreateInstanceHistory(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceHistory", reflect.TypeOf((*MockDbwInstanceInterface)(nil).CreateInstanceHistory), ctx, req)
}

// DeleteInstanceHistory mocks base method.
func (m *MockDbwInstanceInterface) DeleteInstanceHistory(ctx context.Context, req *model.DeleteRecentlyUsedDBInstancesReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstanceHistory", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstanceHistory indicates an expected call of DeleteInstanceHistory.
func (mr *MockDbwInstanceInterfaceMockRecorder) DeleteInstanceHistory(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstanceHistory", reflect.TypeOf((*MockDbwInstanceInterface)(nil).DeleteInstanceHistory), ctx, req)
}

// DescribeInstances mocks base method.
func (m *MockDbwInstanceInterface) DescribeInstances(ctx context.Context, req *model.DescribeInstancesReq) ([]*model.InstanceInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstances", ctx, req)
	ret0, _ := ret[0].([]*model.InstanceInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstances indicates an expected call of DescribeInstances.
func (mr *MockDbwInstanceInterfaceMockRecorder) DescribeInstances(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstances", reflect.TypeOf((*MockDbwInstanceInterface)(nil).DescribeInstances), ctx, req)
}

// GetManagedInstance mocks base method.
func (m *MockDbwInstanceInterface) GetManagedInstance(ctx context.Context, instanceId string, instanceType shared.DataSourceType) (*entity.ManagedInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManagedInstance", ctx, instanceId, instanceType)
	ret0, _ := ret[0].(*entity.ManagedInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManagedInstance indicates an expected call of GetManagedInstance.
func (mr *MockDbwInstanceInterfaceMockRecorder) GetManagedInstance(ctx, instanceId, instanceType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManagedInstance", reflect.TypeOf((*MockDbwInstanceInterface)(nil).GetManagedInstance), ctx, instanceId, instanceType)
}

// ListInstanceHistory mocks base method.
func (m *MockDbwInstanceInterface) ListInstanceHistory(ctx context.Context, req *model.DescribeRecentlyUsedDBInstancesReq) (*model.DescribeRecentlyUsedDBInstancesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceHistory", ctx, req)
	ret0, _ := ret[0].(*model.DescribeRecentlyUsedDBInstancesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceHistory indicates an expected call of ListInstanceHistory.
func (mr *MockDbwInstanceInterfaceMockRecorder) ListInstanceHistory(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceHistory", reflect.TypeOf((*MockDbwInstanceInterface)(nil).ListInstanceHistory), ctx, req)
}

// SyncInstances mocks base method.
func (m *MockDbwInstanceInterface) SyncInstances(ctx context.Context, sourceType shared.DataSourceType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncInstances", ctx, sourceType)
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncInstances indicates an expected call of SyncInstances.
func (mr *MockDbwInstanceInterfaceMockRecorder) SyncInstances(ctx, sourceType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncInstances", reflect.TypeOf((*MockDbwInstanceInterface)(nil).SyncInstances), ctx, sourceType)
}
