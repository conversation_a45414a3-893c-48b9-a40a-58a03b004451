// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket (interfaces: TicketCommonService)

// Package mock_dbw_ticket is a generated GoMock package.
package mock_dbw_ticket

import (
	context "context"
	reflect "reflect"

	dbw_ticket "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	shared "code.byted.org/infcs/dbw-mgr/biz/shared"
	ast "code.byted.org/infcs/ds-sql-parser/ast"
	gomock "github.com/golang/mock/gomock"
)

// MockTicketCommonService is a mock of TicketCommonService interface.
type MockTicketCommonService struct {
	ctrl     *gomock.Controller
	recorder *MockTicketCommonServiceMockRecorder
}

// MockTicketCommonServiceMockRecorder is the mock recorder for MockTicketCommonService.
type MockTicketCommonServiceMockRecorder struct {
	mock *MockTicketCommonService
}

// NewMockTicketCommonService creates a new mock instance.
func NewMockTicketCommonService(ctrl *gomock.Controller) *MockTicketCommonService {
	mock := &MockTicketCommonService{ctrl: ctrl}
	mock.recorder = &MockTicketCommonServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTicketCommonService) EXPECT() *MockTicketCommonServiceMockRecorder {
	return m.recorder
}

// CheckSqlFormat mocks base method.
func (m *MockTicketCommonService) CheckSqlFormat(arg0 string) ([]ast.StmtNode, bool, string) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckSqlFormat", arg0)
	ret0, _ := ret[0].([]ast.StmtNode)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(string)
	return ret0, ret1, ret2
}

// CheckSqlFormat indicates an expected call of CheckSqlFormat.
func (mr *MockTicketCommonServiceMockRecorder) CheckSqlFormat(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckSqlFormat", reflect.TypeOf((*MockTicketCommonService)(nil).CheckSqlFormat), arg0)
}

// CheckTablePermission mocks base method.
func (m *MockTicketCommonService) CheckTablePermission(arg0 context.Context, arg1 *dbw_ticket.SqlInfo, arg2 *dbw_ticket.UserPrivilege, arg3 *shared.PreCheckDbwTicket) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckTablePermission", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckTablePermission indicates an expected call of CheckTablePermission.
func (mr *MockTicketCommonServiceMockRecorder) CheckTablePermission(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTablePermission", reflect.TypeOf((*MockTicketCommonService)(nil).CheckTablePermission), arg0, arg1, arg2, arg3)
}

// Generate6MD5 mocks base method.
func (m *MockTicketCommonService) Generate6MD5(arg0 string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Generate6MD5", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// Generate6MD5 indicates an expected call of Generate6MD5.
func (mr *MockTicketCommonServiceMockRecorder) Generate6MD5(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Generate6MD5", reflect.TypeOf((*MockTicketCommonService)(nil).Generate6MD5), arg0)
}

// GetAdminAccountPassword mocks base method.
func (m *MockTicketCommonService) GetAdminAccountPassword(arg0, arg1 string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdminAccountPassword", arg0, arg1)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetAdminAccountPassword indicates an expected call of GetAdminAccountPassword.
func (mr *MockTicketCommonServiceMockRecorder) GetAdminAccountPassword(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdminAccountPassword", reflect.TypeOf((*MockTicketCommonService)(nil).GetAdminAccountPassword), arg0, arg1)
}

// GetDBDataSource mocks base method.
func (m *MockTicketCommonService) GetDBDataSource(arg0 context.Context, arg1 dbw_ticket.TicketBasicInfo) (*shared.DataSource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDBDataSource", arg0, arg1)
	ret0, _ := ret[0].(*shared.DataSource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDBDataSource indicates an expected call of GetDBDataSource.
func (mr *MockTicketCommonServiceMockRecorder) GetDBDataSource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDBDataSource", reflect.TypeOf((*MockTicketCommonService)(nil).GetDBDataSource), arg0, arg1)
}

// GetDbwAdminDatasource mocks base method.
func (m *MockTicketCommonService) GetDbwAdminDatasource(arg0 context.Context, arg1 dbw_ticket.TicketBasicInfo) (*shared.DataSource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDbwAdminDatasource", arg0, arg1)
	ret0, _ := ret[0].(*shared.DataSource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDbwAdminDatasource indicates an expected call of GetDbwAdminDatasource.
func (mr *MockTicketCommonServiceMockRecorder) GetDbwAdminDatasource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDbwAdminDatasource", reflect.TypeOf((*MockTicketCommonService)(nil).GetDbwAdminDatasource), arg0, arg1)
}

// GetSessionId mocks base method.
func (m *MockTicketCommonService) GetSessionId(arg0 context.Context, arg1 dbw_ticket.TicketBasicInfo) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSessionId", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSessionId indicates an expected call of GetSessionId.
func (mr *MockTicketCommonServiceMockRecorder) GetSessionId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSessionId", reflect.TypeOf((*MockTicketCommonService)(nil).GetSessionId), arg0, arg1)
}

// GetUserAllInstancePrivilege mocks base method.
func (m *MockTicketCommonService) GetUserAllInstancePrivilege(arg0 context.Context, arg1, arg2, arg3, arg4 string) (*dbw_ticket.UserPrivilege, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAllInstancePrivilege", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*dbw_ticket.UserPrivilege)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAllInstancePrivilege indicates an expected call of GetUserAllInstancePrivilege.
func (mr *MockTicketCommonServiceMockRecorder) GetUserAllInstancePrivilege(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAllInstancePrivilege", reflect.TypeOf((*MockTicketCommonService)(nil).GetUserAllInstancePrivilege), arg0, arg1, arg2, arg3, arg4)
}

// GiveBackInstanceSession mocks base method.
func (m *MockTicketCommonService) GiveBackInstanceSession(arg0 context.Context, arg1, arg2 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "GiveBackInstanceSession", arg0, arg1, arg2)
}

// GiveBackInstanceSession indicates an expected call of GiveBackInstanceSession.
func (mr *MockTicketCommonServiceMockRecorder) GiveBackInstanceSession(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GiveBackInstanceSession", reflect.TypeOf((*MockTicketCommonService)(nil).GiveBackInstanceSession), arg0, arg1, arg2)
}

// IsCreateTableSql mocks base method.
func (m *MockTicketCommonService) IsCreateTableSql(arg0 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsCreateTableSql", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsCreateTableSql indicates an expected call of IsCreateTableSql.
func (mr *MockTicketCommonServiceMockRecorder) IsCreateTableSql(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCreateTableSql", reflect.TypeOf((*MockTicketCommonService)(nil).IsCreateTableSql), arg0)
}

// IsDDLSql mocks base method.
func (m *MockTicketCommonService) IsDDLSql(arg0 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsDDLSql", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsDDLSql indicates an expected call of IsDDLSql.
func (mr *MockTicketCommonServiceMockRecorder) IsDDLSql(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsDDLSql", reflect.TypeOf((*MockTicketCommonService)(nil).IsDDLSql), arg0)
}

// IsDirectExecuteDDLSql mocks base method.
func (m *MockTicketCommonService) IsDirectExecuteDDLSql(arg0 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsDirectExecuteDDLSql", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsDirectExecuteDDLSql indicates an expected call of IsDirectExecuteDDLSql.
func (mr *MockTicketCommonServiceMockRecorder) IsDirectExecuteDDLSql(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsDirectExecuteDDLSql", reflect.TypeOf((*MockTicketCommonService)(nil).IsDirectExecuteDDLSql), arg0)
}

// IsInGhostDDLWhite mocks base method.
func (m *MockTicketCommonService) IsInGhostDDLWhite(arg0 context.Context, arg1, arg2 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsInGhostDDLWhite", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsInGhostDDLWhite indicates an expected call of IsInGhostDDLWhite.
func (mr *MockTicketCommonServiceMockRecorder) IsInGhostDDLWhite(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsInGhostDDLWhite", reflect.TypeOf((*MockTicketCommonService)(nil).IsInGhostDDLWhite), arg0, arg1, arg2)
}

// IsModifyIndexSqlWithoutAlter mocks base method.
func (m *MockTicketCommonService) IsModifyIndexSqlWithoutAlter(arg0 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsModifyIndexSqlWithoutAlter", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsModifyIndexSqlWithoutAlter indicates an expected call of IsModifyIndexSqlWithoutAlter.
func (mr *MockTicketCommonServiceMockRecorder) IsModifyIndexSqlWithoutAlter(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsModifyIndexSqlWithoutAlter", reflect.TypeOf((*MockTicketCommonService)(nil).IsModifyIndexSqlWithoutAlter), arg0)
}

// SplitSqls mocks base method.
func (m *MockTicketCommonService) SplitSqls(arg0 context.Context, arg1 string) []*dbw_ticket.SqlInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SplitSqls", arg0, arg1)
	ret0, _ := ret[0].([]*dbw_ticket.SqlInfo)
	return ret0
}

// SplitSqls indicates an expected call of SplitSqls.
func (mr *MockTicketCommonServiceMockRecorder) SplitSqls(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SplitSqls", reflect.TypeOf((*MockTicketCommonService)(nil).SplitSqls), arg0, arg1)
}

// SplitSqlsByDBType mocks base method.
func (m *MockTicketCommonService) SplitSqlsByDBType(arg0 context.Context, arg1 shared.DataSourceType, arg2 string) []*dbw_ticket.SqlInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SplitSqlsByDBType", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*dbw_ticket.SqlInfo)
	return ret0
}

// SplitSqlsByDBType indicates an expected call of SplitSqlsByDBType.
func (mr *MockTicketCommonServiceMockRecorder) SplitSqlsByDBType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SplitSqlsByDBType", reflect.TypeOf((*MockTicketCommonService)(nil).SplitSqlsByDBType), arg0, arg1, arg2)
}

// ValidateOneAddColumnSql mocks base method.
func (m *MockTicketCommonService) ValidateOneAddColumnSql(arg0 ast.StmtNode) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateOneAddColumnSql", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateOneAddColumnSql indicates an expected call of ValidateOneAddColumnSql.
func (mr *MockTicketCommonServiceMockRecorder) ValidateOneAddColumnSql(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateOneAddColumnSql", reflect.TypeOf((*MockTicketCommonService)(nil).ValidateOneAddColumnSql), arg0)
}
