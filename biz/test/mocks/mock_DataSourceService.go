// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/infcs/dbw-mgr/biz/service/datasource (interfaces: DataSourceService)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	datasource "code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	shared "code.byted.org/infcs/dbw-mgr/biz/shared"
	model "code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	types "code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	gomock "github.com/golang/mock/gomock"
)

// MockDataSourceService is a mock of DataSourceService interface.
type MockDataSourceService struct {
	ctrl     *gomock.Controller
	recorder *MockDataSourceServiceMockRecorder
}

// MockDataSourceServiceMockRecorder is the mock recorder for MockDataSourceService.
type MockDataSourceServiceMockRecorder struct {
	mock *MockDataSourceService
}

// NewMockDataSourceService creates a new mock instance.
func NewMockDataSourceService(ctrl *gomock.Controller) *MockDataSourceService {
	mock := &MockDataSourceService{ctrl: ctrl}
	mock.recorder = &MockDataSourceServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDataSourceService) EXPECT() *MockDataSourceServiceMockRecorder {
	return m.recorder
}

// AddSQLCCLRule mocks base method.
func (m *MockDataSourceService) AddSQLCCLRule(arg0 context.Context, arg1 *datasource.AddSQLCCLRuleReq) (*datasource.AddSQLCCLRuleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSQLCCLRule", arg0, arg1)
	ret0, _ := ret[0].(*datasource.AddSQLCCLRuleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddSQLCCLRule indicates an expected call of AddSQLCCLRule.
func (mr *MockDataSourceServiceMockRecorder) AddSQLCCLRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSQLCCLRule", reflect.TypeOf((*MockDataSourceService)(nil).AddSQLCCLRule), arg0, arg1)
}

// AddWhiteList mocks base method.
func (m *MockDataSourceService) AddWhiteList(arg0 context.Context, arg1 string, arg2 *shared.DataSource) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWhiteList", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddWhiteList indicates an expected call of AddWhiteList.
func (mr *MockDataSourceServiceMockRecorder) AddWhiteList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWhiteList", reflect.TypeOf((*MockDataSourceService)(nil).AddWhiteList), arg0, arg1, arg2)
}

// BandwidthScale mocks base method.
func (m *MockDataSourceService) BandwidthScale(arg0 context.Context, arg1 *datasource.BandwidthScaleReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BandwidthScale", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BandwidthScale indicates an expected call of BandwidthScale.
func (mr *MockDataSourceServiceMockRecorder) BandwidthScale(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BandwidthScale", reflect.TypeOf((*MockDataSourceService)(nil).BandwidthScale), arg0, arg1)
}

// CCLShow mocks base method.
func (m *MockDataSourceService) CCLShow(arg0 context.Context, arg1 *datasource.CCLShowReq) (*datasource.CCLShowResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CCLShow", arg0, arg1)
	ret0, _ := ret[0].(*datasource.CCLShowResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CCLShow indicates an expected call of CCLShow.
func (mr *MockDataSourceServiceMockRecorder) CCLShow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CCLShow", reflect.TypeOf((*MockDataSourceService)(nil).CCLShow), arg0, arg1)
}

// CalculateSpecAfterScale mocks base method.
func (m *MockDataSourceService) CalculateSpecAfterScale(arg0 context.Context, arg1 *datasource.CalculateSpecAfterScaleReq) (*datasource.CalculateSpecAfterScaleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateSpecAfterScale", arg0, arg1)
	ret0, _ := ret[0].(*datasource.CalculateSpecAfterScaleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateSpecAfterScale indicates an expected call of CalculateSpecAfterScale.
func (mr *MockDataSourceServiceMockRecorder) CalculateSpecAfterScale(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateSpecAfterScale", reflect.TypeOf((*MockDataSourceService)(nil).CalculateSpecAfterScale), arg0, arg1)
}

// CheckAccountPrivilege mocks base method.
func (m *MockDataSourceService) CheckAccountPrivilege(arg0 context.Context, arg1 *datasource.CheckDBWAccountReq) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAccountPrivilege", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAccountPrivilege indicates an expected call of CheckAccountPrivilege.
func (mr *MockDataSourceServiceMockRecorder) CheckAccountPrivilege(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAccountPrivilege", reflect.TypeOf((*MockDataSourceService)(nil).CheckAccountPrivilege), arg0, arg1)
}

// CheckConn mocks base method.
func (m *MockDataSourceService) CheckConn(arg0 context.Context, arg1 *shared.DataSource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckConn", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckConn indicates an expected call of CheckConn.
func (mr *MockDataSourceServiceMockRecorder) CheckConn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckConn", reflect.TypeOf((*MockDataSourceService)(nil).CheckConn), arg0, arg1)
}

// CheckDBInstanceAuditLogStatus mocks base method.
func (m *MockDataSourceService) CheckDBInstanceAuditLogStatus(arg0 context.Context, arg1 *datasource.CheckDBInstanceAuditLogStatusReq) (*datasource.CheckDBInstanceAuditLogStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckDBInstanceAuditLogStatus", arg0, arg1)
	ret0, _ := ret[0].(*datasource.CheckDBInstanceAuditLogStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckDBInstanceAuditLogStatus indicates an expected call of CheckDBInstanceAuditLogStatus.
func (mr *MockDataSourceServiceMockRecorder) CheckDBInstanceAuditLogStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckDBInstanceAuditLogStatus", reflect.TypeOf((*MockDataSourceService)(nil).CheckDBInstanceAuditLogStatus), arg0, arg1)
}

// CheckDataSource mocks base method.
func (m *MockDataSourceService) CheckDataSource(arg0 context.Context, arg1 *shared.DataSource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckDataSource", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckDataSource indicates an expected call of CheckDataSource.
func (mr *MockDataSourceServiceMockRecorder) CheckDataSource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckDataSource", reflect.TypeOf((*MockDataSourceService)(nil).CheckDataSource), arg0, arg1)
}

// CheckInstanceState mocks base method.
func (m *MockDataSourceService) CheckInstanceState(arg0 context.Context, arg1 string, arg2 shared.DataSourceType, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInstanceState", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckInstanceState indicates an expected call of CheckInstanceState.
func (mr *MockDataSourceServiceMockRecorder) CheckInstanceState(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInstanceState", reflect.TypeOf((*MockDataSourceService)(nil).CheckInstanceState), arg0, arg1, arg2, arg3)
}

// CheckPrivilege mocks base method.
func (m *MockDataSourceService) CheckPrivilege(arg0 context.Context, arg1, arg2, arg3, arg4 string, arg5 shared.DataSourceType) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckPrivilege", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckPrivilege indicates an expected call of CheckPrivilege.
func (mr *MockDataSourceServiceMockRecorder) CheckPrivilege(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPrivilege", reflect.TypeOf((*MockDataSourceService)(nil).CheckPrivilege), arg0, arg1, arg2, arg3, arg4, arg5)
}

// CloseDBInstanceAuditLog mocks base method.
func (m *MockDataSourceService) CloseDBInstanceAuditLog(arg0 context.Context, arg1 *datasource.CloseDBInstanceAuditLogReq) (*datasource.CloseDBInstanceAuditLogResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseDBInstanceAuditLog", arg0, arg1)
	ret0, _ := ret[0].(*datasource.CloseDBInstanceAuditLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CloseDBInstanceAuditLog indicates an expected call of CloseDBInstanceAuditLog.
func (mr *MockDataSourceServiceMockRecorder) CloseDBInstanceAuditLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseDBInstanceAuditLog", reflect.TypeOf((*MockDataSourceService)(nil).CloseDBInstanceAuditLog), arg0, arg1)
}

// ConvertTableColumnToModel mocks base method.
func (m *MockDataSourceService) ConvertTableColumnToModel(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableColumnResp) *model.DescribeTableColumnResp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConvertTableColumnToModel", arg0, arg1, arg2)
	ret0, _ := ret[0].(*model.DescribeTableColumnResp)
	return ret0
}

// ConvertTableColumnToModel indicates an expected call of ConvertTableColumnToModel.
func (mr *MockDataSourceServiceMockRecorder) ConvertTableColumnToModel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertTableColumnToModel", reflect.TypeOf((*MockDataSourceService)(nil).ConvertTableColumnToModel), arg0, arg1, arg2)
}

// ConvertTableIndexToModel mocks base method.
func (m *MockDataSourceService) ConvertTableIndexToModel(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableIndexResp) *model.DescribeTableIndexResp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConvertTableIndexToModel", arg0, arg1, arg2)
	ret0, _ := ret[0].(*model.DescribeTableIndexResp)
	return ret0
}

// ConvertTableIndexToModel indicates an expected call of ConvertTableIndexToModel.
func (mr *MockDataSourceServiceMockRecorder) ConvertTableIndexToModel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertTableIndexToModel", reflect.TypeOf((*MockDataSourceService)(nil).ConvertTableIndexToModel), arg0, arg1, arg2)
}

// ConvertTableSpaceToModel mocks base method.
func (m *MockDataSourceService) ConvertTableSpaceToModel(arg0 context.Context, arg1 shared.DataSourceType, arg2 *shared.DescribeTableSpaceResp) *model.DescribeTableSpaceResp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConvertTableSpaceToModel", arg0, arg1, arg2)
	ret0, _ := ret[0].(*model.DescribeTableSpaceResp)
	return ret0
}

// ConvertTableSpaceToModel indicates an expected call of ConvertTableSpaceToModel.
func (mr *MockDataSourceServiceMockRecorder) ConvertTableSpaceToModel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertTableSpaceToModel", reflect.TypeOf((*MockDataSourceService)(nil).ConvertTableSpaceToModel), arg0, arg1, arg2)
}

// CreateAccount mocks base method.
func (m *MockDataSourceService) CreateAccount(arg0 context.Context, arg1 *datasource.CreateAccountReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAccount", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAccount indicates an expected call of CreateAccount.
func (mr *MockDataSourceServiceMockRecorder) CreateAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAccount", reflect.TypeOf((*MockDataSourceService)(nil).CreateAccount), arg0, arg1)
}

// CreateAccountAndGrant mocks base method.
func (m *MockDataSourceService) CreateAccountAndGrant(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 string, arg6 shared.DataSourceType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAccountAndGrant", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAccountAndGrant indicates an expected call of CreateAccountAndGrant.
func (mr *MockDataSourceServiceMockRecorder) CreateAccountAndGrant(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAccountAndGrant", reflect.TypeOf((*MockDataSourceService)(nil).CreateAccountAndGrant), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// CreateFreeLockCorrectOrder mocks base method.
func (m *MockDataSourceService) CreateFreeLockCorrectOrder(arg0 context.Context, arg1 *datasource.CreateFreeLockCorrectOrderReq) (*datasource.CreateFreeLockCorrectOrderResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateFreeLockCorrectOrder", arg0, arg1)
	ret0, _ := ret[0].(*datasource.CreateFreeLockCorrectOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateFreeLockCorrectOrder indicates an expected call of CreateFreeLockCorrectOrder.
func (mr *MockDataSourceServiceMockRecorder) CreateFreeLockCorrectOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFreeLockCorrectOrder", reflect.TypeOf((*MockDataSourceService)(nil).CreateFreeLockCorrectOrder), arg0, arg1)
}

// CreateFreeLockCorrectOrderDryRun mocks base method.
func (m *MockDataSourceService) CreateFreeLockCorrectOrderDryRun(arg0 context.Context, arg1 *datasource.CreateFreeLockCorrectOrderReq) (*datasource.CreateFreeLockCorrectOrderDryRunResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateFreeLockCorrectOrderDryRun", arg0, arg1)
	ret0, _ := ret[0].(*datasource.CreateFreeLockCorrectOrderDryRunResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateFreeLockCorrectOrderDryRun indicates an expected call of CreateFreeLockCorrectOrderDryRun.
func (mr *MockDataSourceServiceMockRecorder) CreateFreeLockCorrectOrderDryRun(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFreeLockCorrectOrderDryRun", reflect.TypeOf((*MockDataSourceService)(nil).CreateFreeLockCorrectOrderDryRun), arg0, arg1)
}

// CreateLogDownloadTask mocks base method.
func (m *MockDataSourceService) CreateLogDownloadTask(arg0 context.Context, arg1 *datasource.CreateLogDownloadTaskReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateLogDownloadTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateLogDownloadTask indicates an expected call of CreateLogDownloadTask.
func (mr *MockDataSourceServiceMockRecorder) CreateLogDownloadTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLogDownloadTask", reflect.TypeOf((*MockDataSourceService)(nil).CreateLogDownloadTask), arg0, arg1)
}

// DeleteAccount mocks base method.
func (m *MockDataSourceService) DeleteAccount(arg0 context.Context, arg1 *datasource.DeleteAccountReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAccount", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAccount indicates an expected call of DeleteAccount.
func (mr *MockDataSourceServiceMockRecorder) DeleteAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAccount", reflect.TypeOf((*MockDataSourceService)(nil).DeleteAccount), arg0, arg1)
}

// DeleteSQLCCLRule mocks base method.
func (m *MockDataSourceService) DeleteSQLCCLRule(arg0 context.Context, arg1 *datasource.DeleteSQLCCLRuleReq) (*datasource.DeleteSQLCCLRuleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSQLCCLRule", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DeleteSQLCCLRuleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSQLCCLRule indicates an expected call of DeleteSQLCCLRule.
func (mr *MockDataSourceServiceMockRecorder) DeleteSQLCCLRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSQLCCLRule", reflect.TypeOf((*MockDataSourceService)(nil).DeleteSQLCCLRule), arg0, arg1)
}

// DescribeAccounts mocks base method.
func (m *MockDataSourceService) DescribeAccounts(arg0 context.Context, arg1 *datasource.DescribeAccountsReq) (*datasource.DescribeAccountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAccounts", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeAccountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAccounts indicates an expected call of DescribeAccounts.
func (mr *MockDataSourceServiceMockRecorder) DescribeAccounts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAccounts", reflect.TypeOf((*MockDataSourceService)(nil).DescribeAccounts), arg0, arg1)
}

// DescribeAccounts2 mocks base method.
func (m *MockDataSourceService) DescribeAccounts2(arg0 context.Context, arg1 *datasource.DescribeAccountsReq) (*datasource.DescribeAccountResp2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAccounts2", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeAccountResp2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAccounts2 indicates an expected call of DescribeAccounts2.
func (mr *MockDataSourceServiceMockRecorder) DescribeAccounts2(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAccounts2", reflect.TypeOf((*MockDataSourceService)(nil).DescribeAccounts2), arg0, arg1)
}

// DescribeAutoKillSessionConfig mocks base method.
func (m *MockDataSourceService) DescribeAutoKillSessionConfig(arg0 context.Context, arg1 *datasource.DescribeAutoKillSessionConfigReq) (*datasource.DescribeAutoKillSessionConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeAutoKillSessionConfig", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeAutoKillSessionConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeAutoKillSessionConfig indicates an expected call of DescribeAutoKillSessionConfig.
func (mr *MockDataSourceServiceMockRecorder) DescribeAutoKillSessionConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeAutoKillSessionConfig", reflect.TypeOf((*MockDataSourceService)(nil).DescribeAutoKillSessionConfig), arg0, arg1)
}

// DescribeBigKeys mocks base method.
func (m *MockDataSourceService) DescribeBigKeys(arg0 context.Context, arg1 *datasource.DescribeBigKeysReq) (*datasource.DescribeBigKeysResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeBigKeys", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeBigKeysResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeBigKeys indicates an expected call of DescribeBigKeys.
func (mr *MockDataSourceServiceMockRecorder) DescribeBigKeys(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeBigKeys", reflect.TypeOf((*MockDataSourceService)(nil).DescribeBigKeys), arg0, arg1)
}

// DescribeCurrentConn mocks base method.
func (m *MockDataSourceService) DescribeCurrentConn(arg0 context.Context, arg1 *datasource.DescribeCurrentConnsReq) (*datasource.DescribeCurrentConnsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeCurrentConn", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeCurrentConnsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeCurrentConn indicates an expected call of DescribeCurrentConn.
func (mr *MockDataSourceServiceMockRecorder) DescribeCurrentConn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeCurrentConn", reflect.TypeOf((*MockDataSourceService)(nil).DescribeCurrentConn), arg0, arg1)
}

// DescribeDBAutoScaleEvents mocks base method.
func (m *MockDataSourceService) DescribeDBAutoScaleEvents(arg0 context.Context, arg1 *datasource.DescribeDBAutoScaleEventsReq) (*datasource.DescribeDBAutoScaleEventsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBAutoScaleEvents", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDBAutoScaleEventsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBAutoScaleEvents indicates an expected call of DescribeDBAutoScaleEvents.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBAutoScaleEvents(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBAutoScaleEvents", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBAutoScaleEvents), arg0, arg1)
}

// DescribeDBAutoScalingConfig mocks base method.
func (m *MockDataSourceService) DescribeDBAutoScalingConfig(arg0 context.Context, arg1 *datasource.DescribeDBAutoScalingConfigReq) (*datasource.DescribeDBAutoScalingConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBAutoScalingConfig", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDBAutoScalingConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBAutoScalingConfig indicates an expected call of DescribeDBAutoScalingConfig.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBAutoScalingConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBAutoScalingConfig", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBAutoScalingConfig), arg0, arg1)
}

// DescribeDBInstanceAuditCollectedPod mocks base method.
func (m *MockDataSourceService) DescribeDBInstanceAuditCollectedPod(arg0 context.Context, arg1 *datasource.DescribeDBInstanceAuditCollectedPodReq) (*datasource.DescribeDBInstanceAuditCollectedPodResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBInstanceAuditCollectedPod", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDBInstanceAuditCollectedPodResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBInstanceAuditCollectedPod indicates an expected call of DescribeDBInstanceAuditCollectedPod.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBInstanceAuditCollectedPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBInstanceAuditCollectedPod", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBInstanceAuditCollectedPod), arg0, arg1)
}

// DescribeDBInstanceCluster mocks base method.
func (m *MockDataSourceService) DescribeDBInstanceCluster(arg0 context.Context, arg1 *datasource.DescribeDBInstanceClusterReq) (*datasource.DescribeDBInstanceClusterResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBInstanceCluster", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDBInstanceClusterResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBInstanceCluster indicates an expected call of DescribeDBInstanceCluster.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBInstanceCluster(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBInstanceCluster", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBInstanceCluster), arg0, arg1)
}

// DescribeDBInstanceDetail mocks base method.
func (m *MockDataSourceService) DescribeDBInstanceDetail(arg0 context.Context, arg1 *datasource.DescribeDBInstanceDetailReq) (*datasource.DescribeDBInstanceDetailResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBInstanceDetail", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDBInstanceDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBInstanceDetail indicates an expected call of DescribeDBInstanceDetail.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBInstanceDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBInstanceDetail", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBInstanceDetail), arg0, arg1)
}

// DescribeDBInstanceDetailForPilot mocks base method.
func (m *MockDataSourceService) DescribeDBInstanceDetailForPilot(arg0 context.Context, arg1 *datasource.DescribeDBInstanceDetailReq) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBInstanceDetailForPilot", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBInstanceDetailForPilot indicates an expected call of DescribeDBInstanceDetailForPilot.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBInstanceDetailForPilot(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBInstanceDetailForPilot", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBInstanceDetailForPilot), arg0, arg1)
}

// DescribeDBInstanceEndpoints mocks base method.
func (m *MockDataSourceService) DescribeDBInstanceEndpoints(arg0 context.Context, arg1 *datasource.DescribeDBInstanceEndpointsReq) (*datasource.DescribeDBInstanceEndpointsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBInstanceEndpoints", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDBInstanceEndpointsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBInstanceEndpoints indicates an expected call of DescribeDBInstanceEndpoints.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBInstanceEndpoints(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBInstanceEndpoints", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBInstanceEndpoints), arg0, arg1)
}

// DescribeDBInstanceParametersForPilot mocks base method.
func (m *MockDataSourceService) DescribeDBInstanceParametersForPilot(arg0 context.Context, arg1 *datasource.DescribeDBInstanceDetailReq) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBInstanceParametersForPilot", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBInstanceParametersForPilot indicates an expected call of DescribeDBInstanceParametersForPilot.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBInstanceParametersForPilot(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBInstanceParametersForPilot", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBInstanceParametersForPilot), arg0, arg1)
}

// DescribeDBInstanceSSL mocks base method.
func (m *MockDataSourceService) DescribeDBInstanceSSL(arg0 context.Context, arg1 *datasource.DescribeDBInstanceSSLReq) (*datasource.DescribeDBInstanceSSLResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBInstanceSSL", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDBInstanceSSLResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBInstanceSSL indicates an expected call of DescribeDBInstanceSSL.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBInstanceSSL(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBInstanceSSL", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBInstanceSSL), arg0, arg1)
}

// DescribeDBInstanceShardInfos mocks base method.
func (m *MockDataSourceService) DescribeDBInstanceShardInfos(arg0 context.Context, arg1 *datasource.DescribeDBInstanceShardInfosReq) (*datasource.DescribeDBInstanceShardInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBInstanceShardInfos", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDBInstanceShardInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBInstanceShardInfos indicates an expected call of DescribeDBInstanceShardInfos.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBInstanceShardInfos(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBInstanceShardInfos", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBInstanceShardInfos), arg0, arg1)
}

// DescribeDBInstanceSpec mocks base method.
func (m *MockDataSourceService) DescribeDBInstanceSpec(arg0 context.Context, arg1 *datasource.DescribeDBInstanceSpecReq) (*datasource.DescribeDBInstanceSpecResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBInstanceSpec", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDBInstanceSpecResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBInstanceSpec indicates an expected call of DescribeDBInstanceSpec.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBInstanceSpec(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBInstanceSpec", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBInstanceSpec), arg0, arg1)
}

// DescribeDBProxyConfig mocks base method.
func (m *MockDataSourceService) DescribeDBProxyConfig(arg0 context.Context, arg1 *datasource.DescribeDBProxyConfigReq) (*datasource.DescribeDBProxyConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBProxyConfig", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDBProxyConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBProxyConfig indicates an expected call of DescribeDBProxyConfig.
func (mr *MockDataSourceServiceMockRecorder) DescribeDBProxyConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBProxyConfig", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDBProxyConfig), arg0, arg1)
}

// DescribeDeadlock mocks base method.
func (m *MockDataSourceService) DescribeDeadlock(arg0 context.Context, arg1 *datasource.DescribeDeadlockReq) (*datasource.DescribeDeadlockResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDeadlock", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDeadlockResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDeadlock indicates an expected call of DescribeDeadlock.
func (mr *MockDataSourceServiceMockRecorder) DescribeDeadlock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDeadlock", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDeadlock), arg0, arg1)
}

// DescribeDeadlockDetect mocks base method.
func (m *MockDataSourceService) DescribeDeadlockDetect(arg0 context.Context, arg1 *datasource.DescribeDeadlockDetectReq) (*datasource.DescribeDeadlockDetectResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDeadlockDetect", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDeadlockDetectResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDeadlockDetect indicates an expected call of DescribeDeadlockDetect.
func (mr *MockDataSourceServiceMockRecorder) DescribeDeadlockDetect(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDeadlockDetect", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDeadlockDetect), arg0, arg1)
}

// DescribeDialogDetails mocks base method.
func (m *MockDataSourceService) DescribeDialogDetails(arg0 context.Context, arg1 *datasource.DescribeDialogDetailsReq) (*datasource.DescribeDialogDetailsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDialogDetails", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDialogDetailsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDialogDetails indicates an expected call of DescribeDialogDetails.
func (mr *MockDataSourceServiceMockRecorder) DescribeDialogDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDialogDetails", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDialogDetails), arg0, arg1)
}

// DescribeDialogInfos mocks base method.
func (m *MockDataSourceService) DescribeDialogInfos(arg0 context.Context, arg1 *datasource.DescribeDialogInfosReq) (*datasource.DescribeDialogInfosResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDialogInfos", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDialogInfosResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDialogInfos indicates an expected call of DescribeDialogInfos.
func (mr *MockDataSourceServiceMockRecorder) DescribeDialogInfos(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDialogInfos", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDialogInfos), arg0, arg1)
}

// DescribeDialogStatistics mocks base method.
func (m *MockDataSourceService) DescribeDialogStatistics(arg0 context.Context, arg1 *datasource.DescribeDialogStatisticsReq) (*datasource.DescribeDialogStatisticsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDialogStatistics", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeDialogStatisticsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDialogStatistics indicates an expected call of DescribeDialogStatistics.
func (mr *MockDataSourceServiceMockRecorder) DescribeDialogStatistics(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDialogStatistics", reflect.TypeOf((*MockDataSourceService)(nil).DescribeDialogStatistics), arg0, arg1)
}

// DescribeEngineStatus mocks base method.
func (m *MockDataSourceService) DescribeEngineStatus(arg0 context.Context, arg1 *datasource.DescribeEngineStatusReq) (*datasource.DescribeEngineStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeEngineStatus", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeEngineStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeEngineStatus indicates an expected call of DescribeEngineStatus.
func (mr *MockDataSourceServiceMockRecorder) DescribeEngineStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeEngineStatus", reflect.TypeOf((*MockDataSourceService)(nil).DescribeEngineStatus), arg0, arg1)
}

// DescribeEvent mocks base method.
func (m *MockDataSourceService) DescribeEvent(arg0 context.Context, arg1 *datasource.DescribeEventReq) (*datasource.DescribeEventResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeEvent", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeEventResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeEvent indicates an expected call of DescribeEvent.
func (mr *MockDataSourceServiceMockRecorder) DescribeEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeEvent", reflect.TypeOf((*MockDataSourceService)(nil).DescribeEvent), arg0, arg1)
}

// DescribeFreeLockCorrectOrders mocks base method.
func (m *MockDataSourceService) DescribeFreeLockCorrectOrders(arg0 context.Context, arg1 *datasource.DescribeFreeLockCorrectOrdersReq) (*datasource.DescribeFreeLockCorrectOrdersResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeFreeLockCorrectOrders", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeFreeLockCorrectOrdersResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeFreeLockCorrectOrders indicates an expected call of DescribeFreeLockCorrectOrders.
func (mr *MockDataSourceServiceMockRecorder) DescribeFreeLockCorrectOrders(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeFreeLockCorrectOrders", reflect.TypeOf((*MockDataSourceService)(nil).DescribeFreeLockCorrectOrders), arg0, arg1)
}

// DescribeFullSQLLogConfig mocks base method.
func (m *MockDataSourceService) DescribeFullSQLLogConfig(arg0 context.Context, arg1 *datasource.DescribeFullSQLLogConfigReq) (*datasource.DescribeFullSQLLogConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeFullSQLLogConfig", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeFullSQLLogConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeFullSQLLogConfig indicates an expected call of DescribeFullSQLLogConfig.
func (mr *MockDataSourceServiceMockRecorder) DescribeFullSQLLogConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeFullSQLLogConfig", reflect.TypeOf((*MockDataSourceService)(nil).DescribeFullSQLLogConfig), arg0, arg1)
}

// DescribeFunction mocks base method.
func (m *MockDataSourceService) DescribeFunction(arg0 context.Context, arg1 *datasource.DescribeFunctionReq) (*datasource.DescribeFunctionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeFunction", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeFunctionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeFunction indicates an expected call of DescribeFunction.
func (mr *MockDataSourceServiceMockRecorder) DescribeFunction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeFunction", reflect.TypeOf((*MockDataSourceService)(nil).DescribeFunction), arg0, arg1)
}

// DescribeHotKeys mocks base method.
func (m *MockDataSourceService) DescribeHotKeys(arg0 context.Context, arg1 *datasource.DescribeHotKeysReq) (*datasource.DescribeHotKeysResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeHotKeys", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeHotKeysResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeHotKeys indicates an expected call of DescribeHotKeys.
func (mr *MockDataSourceServiceMockRecorder) DescribeHotKeys(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeHotKeys", reflect.TypeOf((*MockDataSourceService)(nil).DescribeHotKeys), arg0, arg1)
}

// DescribeInstanceAddress mocks base method.
func (m *MockDataSourceService) DescribeInstanceAddress(arg0 context.Context, arg1 *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstanceAddress", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeInstanceAddressResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstanceAddress indicates an expected call of DescribeInstanceAddress.
func (mr *MockDataSourceServiceMockRecorder) DescribeInstanceAddress(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstanceAddress", reflect.TypeOf((*MockDataSourceService)(nil).DescribeInstanceAddress), arg0, arg1)
}

// DescribeInstanceAddressList mocks base method.
func (m *MockDataSourceService) DescribeInstanceAddressList(arg0 context.Context, arg1 *datasource.DescribeInstanceAddressReq) ([]*datasource.DescribeInstanceAddressResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstanceAddressList", arg0, arg1)
	ret0, _ := ret[0].([]*datasource.DescribeInstanceAddressResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstanceAddressList indicates an expected call of DescribeInstanceAddressList.
func (mr *MockDataSourceServiceMockRecorder) DescribeInstanceAddressList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstanceAddressList", reflect.TypeOf((*MockDataSourceService)(nil).DescribeInstanceAddressList), arg0, arg1)
}

// DescribeInstanceFeatures mocks base method.
func (m *MockDataSourceService) DescribeInstanceFeatures(arg0 context.Context, arg1 *datasource.DescribeInstanceFeaturesReq) (*datasource.DescribeInstanceFeaturesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstanceFeatures", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeInstanceFeaturesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstanceFeatures indicates an expected call of DescribeInstanceFeatures.
func (mr *MockDataSourceServiceMockRecorder) DescribeInstanceFeatures(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstanceFeatures", reflect.TypeOf((*MockDataSourceService)(nil).DescribeInstanceFeatures), arg0, arg1)
}

// DescribeInstancePodAddress mocks base method.
func (m *MockDataSourceService) DescribeInstancePodAddress(arg0 context.Context, arg1 *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstancePodAddress", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeInstanceAddressResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstancePodAddress indicates an expected call of DescribeInstancePodAddress.
func (mr *MockDataSourceServiceMockRecorder) DescribeInstancePodAddress(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstancePodAddress", reflect.TypeOf((*MockDataSourceService)(nil).DescribeInstancePodAddress), arg0, arg1)
}

// DescribeInstanceProxyAddress mocks base method.
func (m *MockDataSourceService) DescribeInstanceProxyAddress(arg0 context.Context, arg1 *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstanceProxyAddress", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeInstanceAddressResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstanceProxyAddress indicates an expected call of DescribeInstanceProxyAddress.
func (mr *MockDataSourceServiceMockRecorder) DescribeInstanceProxyAddress(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstanceProxyAddress", reflect.TypeOf((*MockDataSourceService)(nil).DescribeInstanceProxyAddress), arg0, arg1)
}

// DescribeInstanceReplicaDelay mocks base method.
func (m *MockDataSourceService) DescribeInstanceReplicaDelay(arg0 context.Context, arg1 *datasource.DescribeDBInstanceDetailReq) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstanceReplicaDelay", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstanceReplicaDelay indicates an expected call of DescribeInstanceReplicaDelay.
func (mr *MockDataSourceServiceMockRecorder) DescribeInstanceReplicaDelay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstanceReplicaDelay", reflect.TypeOf((*MockDataSourceService)(nil).DescribeInstanceReplicaDelay), arg0, arg1)
}

// DescribeInstanceVariables mocks base method.
func (m *MockDataSourceService) DescribeInstanceVariables(arg0 context.Context, arg1 *datasource.DescribeInstanceVariablesReq) (*datasource.DescribeInstanceVariablesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstanceVariables", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeInstanceVariablesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstanceVariables indicates an expected call of DescribeInstanceVariables.
func (mr *MockDataSourceServiceMockRecorder) DescribeInstanceVariables(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstanceVariables", reflect.TypeOf((*MockDataSourceService)(nil).DescribeInstanceVariables), arg0, arg1)
}

// DescribeInstanceVersion mocks base method.
func (m *MockDataSourceService) DescribeInstanceVersion(arg0 context.Context, arg1 *datasource.DescribeInstanceVersionReq) (*datasource.DescribeInstanceVersionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeInstanceVersion", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeInstanceVersionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeInstanceVersion indicates an expected call of DescribeInstanceVersion.
func (mr *MockDataSourceServiceMockRecorder) DescribeInstanceVersion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeInstanceVersion", reflect.TypeOf((*MockDataSourceService)(nil).DescribeInstanceVersion), arg0, arg1)
}

// DescribeLockCurrentWaits mocks base method.
func (m *MockDataSourceService) DescribeLockCurrentWaits(arg0 context.Context, arg1 *datasource.DescribeLockCurrentWaitsReq) (*datasource.DescribeLockCurrentWaitsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeLockCurrentWaits", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeLockCurrentWaitsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeLockCurrentWaits indicates an expected call of DescribeLockCurrentWaits.
func (mr *MockDataSourceServiceMockRecorder) DescribeLockCurrentWaits(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeLockCurrentWaits", reflect.TypeOf((*MockDataSourceService)(nil).DescribeLockCurrentWaits), arg0, arg1)
}

// DescribePgTable mocks base method.
func (m *MockDataSourceService) DescribePgTable(arg0 context.Context, arg1 *datasource.DescribePgTableReq) (*datasource.DescribePgTableResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribePgTable", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribePgTableResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribePgTable indicates an expected call of DescribePgTable.
func (mr *MockDataSourceServiceMockRecorder) DescribePgTable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribePgTable", reflect.TypeOf((*MockDataSourceService)(nil).DescribePgTable), arg0, arg1)
}

// DescribePrimaryKeyRange mocks base method.
func (m *MockDataSourceService) DescribePrimaryKeyRange(arg0 context.Context, arg1 *datasource.DescribePrimaryKeyRangeReq) (*datasource.DescribePrimaryKeyRangeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribePrimaryKeyRange", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribePrimaryKeyRangeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribePrimaryKeyRange indicates an expected call of DescribePrimaryKeyRange.
func (mr *MockDataSourceServiceMockRecorder) DescribePrimaryKeyRange(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribePrimaryKeyRange", reflect.TypeOf((*MockDataSourceService)(nil).DescribePrimaryKeyRange), arg0, arg1)
}

// DescribeProcedure mocks base method.
func (m *MockDataSourceService) DescribeProcedure(arg0 context.Context, arg1 *datasource.DescribeProcedureReq) (*datasource.DescribeProcedureResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeProcedure", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeProcedureResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeProcedure indicates an expected call of DescribeProcedure.
func (mr *MockDataSourceServiceMockRecorder) DescribeProcedure(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeProcedure", reflect.TypeOf((*MockDataSourceService)(nil).DescribeProcedure), arg0, arg1)
}

// DescribeSQLAdvisorTableMeta mocks base method.
func (m *MockDataSourceService) DescribeSQLAdvisorTableMeta(arg0 context.Context, arg1 *datasource.DescribeSQLAdvisorTableMetaReq) (*datasource.DescribeSQLAdvisorTableMetaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSQLAdvisorTableMeta", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeSQLAdvisorTableMetaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSQLAdvisorTableMeta indicates an expected call of DescribeSQLAdvisorTableMeta.
func (mr *MockDataSourceServiceMockRecorder) DescribeSQLAdvisorTableMeta(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSQLAdvisorTableMeta", reflect.TypeOf((*MockDataSourceService)(nil).DescribeSQLAdvisorTableMeta), arg0, arg1)
}

// DescribeSQLCCLConfig mocks base method.
func (m *MockDataSourceService) DescribeSQLCCLConfig(arg0 context.Context, arg1 *datasource.DescribeSQLCCLConfigReq) (*datasource.DescribeSQLCCLConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSQLCCLConfig", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeSQLCCLConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSQLCCLConfig indicates an expected call of DescribeSQLCCLConfig.
func (mr *MockDataSourceServiceMockRecorder) DescribeSQLCCLConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSQLCCLConfig", reflect.TypeOf((*MockDataSourceService)(nil).DescribeSQLCCLConfig), arg0, arg1)
}

// DescribeSampleData mocks base method.
func (m *MockDataSourceService) DescribeSampleData(arg0 context.Context, arg1 *datasource.DescribeSampleDataReq) (*datasource.DescribeSampleDataResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSampleData", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeSampleDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSampleData indicates an expected call of DescribeSampleData.
func (mr *MockDataSourceServiceMockRecorder) DescribeSampleData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSampleData", reflect.TypeOf((*MockDataSourceService)(nil).DescribeSampleData), arg0, arg1)
}

// DescribeSqlFingerPrintOrKeywords mocks base method.
func (m *MockDataSourceService) DescribeSqlFingerPrintOrKeywords(arg0 context.Context, arg1 *datasource.DescribeSqlFingerPrintOrKeywordsReq) (*datasource.DescribeSqlFingerPrintOrKeywordsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSqlFingerPrintOrKeywords", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeSqlFingerPrintOrKeywordsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSqlFingerPrintOrKeywords indicates an expected call of DescribeSqlFingerPrintOrKeywords.
func (mr *MockDataSourceServiceMockRecorder) DescribeSqlFingerPrintOrKeywords(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSqlFingerPrintOrKeywords", reflect.TypeOf((*MockDataSourceService)(nil).DescribeSqlFingerPrintOrKeywords), arg0, arg1)
}

// DescribeSqlType mocks base method.
func (m *MockDataSourceService) DescribeSqlType(arg0 context.Context, arg1 *datasource.DescribeSqlTypeReq) (*datasource.DescribeSqlTypeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSqlType", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeSqlTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSqlType indicates an expected call of DescribeSqlType.
func (mr *MockDataSourceServiceMockRecorder) DescribeSqlType(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSqlType", reflect.TypeOf((*MockDataSourceService)(nil).DescribeSqlType), arg0, arg1)
}

// DescribeTLSConnectionInfo mocks base method.
func (m *MockDataSourceService) DescribeTLSConnectionInfo(arg0 context.Context, arg1 *datasource.DescribeTLSConnectionInfoReq) (*datasource.DescribeTLSConnectionInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTLSConnectionInfo", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeTLSConnectionInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTLSConnectionInfo indicates an expected call of DescribeTLSConnectionInfo.
func (mr *MockDataSourceServiceMockRecorder) DescribeTLSConnectionInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTLSConnectionInfo", reflect.TypeOf((*MockDataSourceService)(nil).DescribeTLSConnectionInfo), arg0, arg1)
}

// DescribeTable mocks base method.
func (m *MockDataSourceService) DescribeTable(arg0 context.Context, arg1 *datasource.DescribeTableReq) (*datasource.DescribeTableResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTable", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeTableResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTable indicates an expected call of DescribeTable.
func (mr *MockDataSourceServiceMockRecorder) DescribeTable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTable", reflect.TypeOf((*MockDataSourceService)(nil).DescribeTable), arg0, arg1)
}

// DescribeTableColumn mocks base method.
func (m *MockDataSourceService) DescribeTableColumn(arg0 context.Context, arg1 *datasource.DescribeTableInfoReq) (*shared.DescribeTableColumnResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTableColumn", arg0, arg1)
	ret0, _ := ret[0].(*shared.DescribeTableColumnResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTableColumn indicates an expected call of DescribeTableColumn.
func (mr *MockDataSourceServiceMockRecorder) DescribeTableColumn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTableColumn", reflect.TypeOf((*MockDataSourceService)(nil).DescribeTableColumn), arg0, arg1)
}

// DescribeTableIndex mocks base method.
func (m *MockDataSourceService) DescribeTableIndex(arg0 context.Context, arg1 *datasource.DescribeTableInfoReq) (*shared.DescribeTableIndexResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTableIndex", arg0, arg1)
	ret0, _ := ret[0].(*shared.DescribeTableIndexResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTableIndex indicates an expected call of DescribeTableIndex.
func (mr *MockDataSourceServiceMockRecorder) DescribeTableIndex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTableIndex", reflect.TypeOf((*MockDataSourceService)(nil).DescribeTableIndex), arg0, arg1)
}

// DescribeTableSpace mocks base method.
func (m *MockDataSourceService) DescribeTableSpace(arg0 context.Context, arg1 *datasource.DescribeTableSpaceReq) (*shared.DescribeTableSpaceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTableSpace", arg0, arg1)
	ret0, _ := ret[0].(*shared.DescribeTableSpaceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTableSpace indicates an expected call of DescribeTableSpace.
func (mr *MockDataSourceServiceMockRecorder) DescribeTableSpace(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTableSpace", reflect.TypeOf((*MockDataSourceService)(nil).DescribeTableSpace), arg0, arg1)
}

// DescribeTableSpaceAutoIncr mocks base method.
func (m *MockDataSourceService) DescribeTableSpaceAutoIncr(arg0 context.Context, arg1 *datasource.DescribeTableSpaceReq) (*shared.DescribeTableSpaceAutoIncrResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTableSpaceAutoIncr", arg0, arg1)
	ret0, _ := ret[0].(*shared.DescribeTableSpaceAutoIncrResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTableSpaceAutoIncr indicates an expected call of DescribeTableSpaceAutoIncr.
func (mr *MockDataSourceServiceMockRecorder) DescribeTableSpaceAutoIncr(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTableSpaceAutoIncr", reflect.TypeOf((*MockDataSourceService)(nil).DescribeTableSpaceAutoIncr), arg0, arg1)
}

// DescribeTrigger mocks base method.
func (m *MockDataSourceService) DescribeTrigger(arg0 context.Context, arg1 *datasource.DescribeTriggerReq) (*datasource.DescribeTriggerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTrigger", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeTriggerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTrigger indicates an expected call of DescribeTrigger.
func (mr *MockDataSourceServiceMockRecorder) DescribeTrigger(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTrigger", reflect.TypeOf((*MockDataSourceService)(nil).DescribeTrigger), arg0, arg1)
}

// DescribeTrxAndLocks mocks base method.
func (m *MockDataSourceService) DescribeTrxAndLocks(arg0 context.Context, arg1 *datasource.DescribeTrxAndLocksReq) (*datasource.DescribeTrxAndLocksResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTrxAndLocks", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeTrxAndLocksResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTrxAndLocks indicates an expected call of DescribeTrxAndLocks.
func (mr *MockDataSourceServiceMockRecorder) DescribeTrxAndLocks(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTrxAndLocks", reflect.TypeOf((*MockDataSourceService)(nil).DescribeTrxAndLocks), arg0, arg1)
}

// DescribeView mocks base method.
func (m *MockDataSourceService) DescribeView(arg0 context.Context, arg1 *datasource.DescribeViewReq) (*datasource.DescribeViewResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeView", arg0, arg1)
	ret0, _ := ret[0].(*datasource.DescribeViewResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeView indicates an expected call of DescribeView.
func (mr *MockDataSourceServiceMockRecorder) DescribeView(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeView", reflect.TypeOf((*MockDataSourceService)(nil).DescribeView), arg0, arg1)
}

// DiagRootCauseALL mocks base method.
func (m *MockDataSourceService) DiagRootCauseALL(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.DescribeDiagRootCauseResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DiagRootCauseALL", arg0, arg1)
	ret0, _ := ret[0].(*model.DescribeDiagRootCauseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DiagRootCauseALL indicates an expected call of DiagRootCauseALL.
func (mr *MockDataSourceServiceMockRecorder) DiagRootCauseALL(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DiagRootCauseALL", reflect.TypeOf((*MockDataSourceService)(nil).DiagRootCauseALL), arg0, arg1)
}

// DiagRootCauseDiskMetrics mocks base method.
func (m *MockDataSourceService) DiagRootCauseDiskMetrics(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DiagRootCauseDiskMetrics", arg0, arg1)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DiagRootCauseDiskMetrics indicates an expected call of DiagRootCauseDiskMetrics.
func (mr *MockDataSourceServiceMockRecorder) DiagRootCauseDiskMetrics(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DiagRootCauseDiskMetrics", reflect.TypeOf((*MockDataSourceService)(nil).DiagRootCauseDiskMetrics), arg0, arg1)
}

// DiagRootCauseYoYQoQ mocks base method.
func (m *MockDataSourceService) DiagRootCauseYoYQoQ(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (map[string]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DiagRootCauseYoYQoQ", arg0, arg1)
	ret0, _ := ret[0].(map[string]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DiagRootCauseYoYQoQ indicates an expected call of DiagRootCauseYoYQoQ.
func (mr *MockDataSourceServiceMockRecorder) DiagRootCauseYoYQoQ(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DiagRootCauseYoYQoQ", reflect.TypeOf((*MockDataSourceService)(nil).DiagRootCauseYoYQoQ), arg0, arg1)
}

// EnsureAccount mocks base method.
func (m *MockDataSourceService) EnsureAccount(arg0 context.Context, arg1 *datasource.EnsureAccountReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnsureAccount", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// EnsureAccount indicates an expected call of EnsureAccount.
func (mr *MockDataSourceServiceMockRecorder) EnsureAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnsureAccount", reflect.TypeOf((*MockDataSourceService)(nil).EnsureAccount), arg0, arg1)
}

// ExecuteCCL mocks base method.
func (m *MockDataSourceService) ExecuteCCL(arg0 context.Context, arg1 *datasource.ExecuteCCLReq) (*datasource.ExecuteCCLResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteCCL", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ExecuteCCLResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteCCL indicates an expected call of ExecuteCCL.
func (mr *MockDataSourceServiceMockRecorder) ExecuteCCL(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteCCL", reflect.TypeOf((*MockDataSourceService)(nil).ExecuteCCL), arg0, arg1)
}

// ExecuteDMLAndGetAffectedRows mocks base method.
func (m *MockDataSourceService) ExecuteDMLAndGetAffectedRows(arg0 context.Context, arg1 *datasource.ExecuteDMLAndGetAffectedRowsReq) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteDMLAndGetAffectedRows", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteDMLAndGetAffectedRows indicates an expected call of ExecuteDMLAndGetAffectedRows.
func (mr *MockDataSourceServiceMockRecorder) ExecuteDMLAndGetAffectedRows(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteDMLAndGetAffectedRows", reflect.TypeOf((*MockDataSourceService)(nil).ExecuteDMLAndGetAffectedRows), arg0, arg1)
}

// ExecuteDQL mocks base method.
func (m *MockDataSourceService) ExecuteDQL(arg0 context.Context, arg1 *datasource.ExecuteDQLReq) (*datasource.ExecuteDQLResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteDQL", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ExecuteDQLResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteDQL indicates an expected call of ExecuteDQL.
func (mr *MockDataSourceServiceMockRecorder) ExecuteDQL(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteDQL", reflect.TypeOf((*MockDataSourceService)(nil).ExecuteDQL), arg0, arg1)
}

// ExecuteSql mocks base method.
func (m *MockDataSourceService) ExecuteSql(arg0 context.Context, arg1 *datasource.ExecuteReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteSql", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ExecuteSql indicates an expected call of ExecuteSql.
func (mr *MockDataSourceServiceMockRecorder) ExecuteSql(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteSql", reflect.TypeOf((*MockDataSourceService)(nil).ExecuteSql), arg0, arg1)
}

// ExplainCommand mocks base method.
func (m *MockDataSourceService) ExplainCommand(arg0 context.Context, arg1 *datasource.ExplainCommandReq) (*datasource.ExplainCommandResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExplainCommand", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ExplainCommandResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExplainCommand indicates an expected call of ExplainCommand.
func (mr *MockDataSourceServiceMockRecorder) ExplainCommand(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExplainCommand", reflect.TypeOf((*MockDataSourceService)(nil).ExplainCommand), arg0, arg1)
}

// FillDataSource mocks base method.
func (m *MockDataSourceService) FillDataSource(arg0 context.Context, arg1 *shared.DataSource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FillDataSource", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// FillDataSource indicates an expected call of FillDataSource.
func (mr *MockDataSourceServiceMockRecorder) FillDataSource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FillDataSource", reflect.TypeOf((*MockDataSourceService)(nil).FillDataSource), arg0, arg1)
}

// FillInnerDataSource mocks base method.
func (m *MockDataSourceService) FillInnerDataSource(arg0 context.Context, arg1 *shared.DataSource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FillInnerDataSource", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// FillInnerDataSource indicates an expected call of FillInnerDataSource.
func (mr *MockDataSourceServiceMockRecorder) FillInnerDataSource(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FillInnerDataSource", reflect.TypeOf((*MockDataSourceService)(nil).FillInnerDataSource), arg0, arg1)
}

// FlushSQLCCLRule mocks base method.
func (m *MockDataSourceService) FlushSQLCCLRule(arg0 context.Context, arg1 *datasource.FlushSQLCCLRuleReq) (*datasource.FlushSQLCCLRuleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlushSQLCCLRule", arg0, arg1)
	ret0, _ := ret[0].(*datasource.FlushSQLCCLRuleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FlushSQLCCLRule indicates an expected call of FlushSQLCCLRule.
func (mr *MockDataSourceServiceMockRecorder) FlushSQLCCLRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlushSQLCCLRule", reflect.TypeOf((*MockDataSourceService)(nil).FlushSQLCCLRule), arg0, arg1)
}

// FormatDescribeStorageCapacityResp mocks base method.
func (m *MockDataSourceService) FormatDescribeStorageCapacityResp(arg0 shared.DataSourceType, arg1 *datasource.GetDiskSizeResp, arg2 float64) *model.DescribeStorageCapacityResp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FormatDescribeStorageCapacityResp", arg0, arg1, arg2)
	ret0, _ := ret[0].(*model.DescribeStorageCapacityResp)
	return ret0
}

// FormatDescribeStorageCapacityResp indicates an expected call of FormatDescribeStorageCapacityResp.
func (mr *MockDataSourceServiceMockRecorder) FormatDescribeStorageCapacityResp(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FormatDescribeStorageCapacityResp", reflect.TypeOf((*MockDataSourceService)(nil).FormatDescribeStorageCapacityResp), arg0, arg1, arg2)
}

// GetAdvice mocks base method.
func (m *MockDataSourceService) GetAdvice(arg0 context.Context, arg1 *datasource.GetAdviceReq) (*datasource.GetAdviceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdvice", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetAdviceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdvice indicates an expected call of GetAdvice.
func (mr *MockDataSourceServiceMockRecorder) GetAdvice(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdvice", reflect.TypeOf((*MockDataSourceService)(nil).GetAdvice), arg0, arg1)
}

// GetAvgConnectionUsage mocks base method.
func (m *MockDataSourceService) GetAvgConnectionUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvgConnectionUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvgConnectionUsage indicates an expected call of GetAvgConnectionUsage.
func (mr *MockDataSourceServiceMockRecorder) GetAvgConnectionUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvgConnectionUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetAvgConnectionUsage), arg0, arg1)
}

// GetAvgCpuUsage mocks base method.
func (m *MockDataSourceService) GetAvgCpuUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvgCpuUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvgCpuUsage indicates an expected call of GetAvgCpuUsage.
func (mr *MockDataSourceServiceMockRecorder) GetAvgCpuUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvgCpuUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetAvgCpuUsage), arg0, arg1)
}

// GetAvgDiskUsage mocks base method.
func (m *MockDataSourceService) GetAvgDiskUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvgDiskUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvgDiskUsage indicates an expected call of GetAvgDiskUsage.
func (mr *MockDataSourceServiceMockRecorder) GetAvgDiskUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvgDiskUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetAvgDiskUsage), arg0, arg1)
}

// GetAvgMemUsage mocks base method.
func (m *MockDataSourceService) GetAvgMemUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvgMemUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvgMemUsage indicates an expected call of GetAvgMemUsage.
func (mr *MockDataSourceServiceMockRecorder) GetAvgMemUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvgMemUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetAvgMemUsage), arg0, arg1)
}

// GetAvgQpsUsage mocks base method.
func (m *MockDataSourceService) GetAvgQpsUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvgQpsUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvgQpsUsage indicates an expected call of GetAvgQpsUsage.
func (mr *MockDataSourceServiceMockRecorder) GetAvgQpsUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvgQpsUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetAvgQpsUsage), arg0, arg1)
}

// GetAvgSessionUsage mocks base method.
func (m *MockDataSourceService) GetAvgSessionUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvgSessionUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvgSessionUsage indicates an expected call of GetAvgSessionUsage.
func (mr *MockDataSourceServiceMockRecorder) GetAvgSessionUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvgSessionUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetAvgSessionUsage), arg0, arg1)
}

// GetAvgSlowQueries mocks base method.
func (m *MockDataSourceService) GetAvgSlowQueries(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvgSlowQueries", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvgSlowQueries indicates an expected call of GetAvgSlowQueries.
func (mr *MockDataSourceServiceMockRecorder) GetAvgSlowQueries(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvgSlowQueries", reflect.TypeOf((*MockDataSourceService)(nil).GetAvgSlowQueries), arg0, arg1)
}

// GetAvgTpsUsage mocks base method.
func (m *MockDataSourceService) GetAvgTpsUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvgTpsUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvgTpsUsage indicates an expected call of GetAvgTpsUsage.
func (mr *MockDataSourceServiceMockRecorder) GetAvgTpsUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvgTpsUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetAvgTpsUsage), arg0, arg1)
}

// GetConnectedRatioMetricDetail mocks base method.
func (m *MockDataSourceService) GetConnectedRatioMetricDetail(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConnectedRatioMetricDetail", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConnectedRatioMetricDetail indicates an expected call of GetConnectedRatioMetricDetail.
func (mr *MockDataSourceServiceMockRecorder) GetConnectedRatioMetricDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConnectedRatioMetricDetail", reflect.TypeOf((*MockDataSourceService)(nil).GetConnectedRatioMetricDetail), arg0, arg1)
}

// GetCpuMetricDetail mocks base method.
func (m *MockDataSourceService) GetCpuMetricDetail(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCpuMetricDetail", arg0, arg1)
	ret0, _ := ret[0].(*model.DescribeDiagItemDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCpuMetricDetail indicates an expected call of GetCpuMetricDetail.
func (mr *MockDataSourceServiceMockRecorder) GetCpuMetricDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCpuMetricDetail", reflect.TypeOf((*MockDataSourceService)(nil).GetCpuMetricDetail), arg0, arg1)
}

// GetCpuUsageMetricDetail mocks base method.
func (m *MockDataSourceService) GetCpuUsageMetricDetail(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCpuUsageMetricDetail", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCpuUsageMetricDetail indicates an expected call of GetCpuUsageMetricDetail.
func (mr *MockDataSourceServiceMockRecorder) GetCpuUsageMetricDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCpuUsageMetricDetail", reflect.TypeOf((*MockDataSourceService)(nil).GetCpuUsageMetricDetail), arg0, arg1)
}

// GetCreateTableInfo mocks base method.
func (m *MockDataSourceService) GetCreateTableInfo(arg0 context.Context, arg1 *shared.DataSource, arg2 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCreateTableInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreateTableInfo indicates an expected call of GetCreateTableInfo.
func (mr *MockDataSourceServiceMockRecorder) GetCreateTableInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreateTableInfo", reflect.TypeOf((*MockDataSourceService)(nil).GetCreateTableInfo), arg0, arg1, arg2)
}

// GetCurrentBandwidth mocks base method.
func (m *MockDataSourceService) GetCurrentBandwidth(arg0 context.Context, arg1 *datasource.GetCurrentBandwidthReq) (*datasource.InstanceBandwidthInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentBandwidth", arg0, arg1)
	ret0, _ := ret[0].(*datasource.InstanceBandwidthInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentBandwidth indicates an expected call of GetCurrentBandwidth.
func (mr *MockDataSourceServiceMockRecorder) GetCurrentBandwidth(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentBandwidth", reflect.TypeOf((*MockDataSourceService)(nil).GetCurrentBandwidth), arg0, arg1)
}

// GetCurrentMaxConnections mocks base method.
func (m *MockDataSourceService) GetCurrentMaxConnections(arg0 context.Context, arg1 *datasource.GetCurrentMaxConnectionsReq) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentMaxConnections", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentMaxConnections indicates an expected call of GetCurrentMaxConnections.
func (mr *MockDataSourceServiceMockRecorder) GetCurrentMaxConnections(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentMaxConnections", reflect.TypeOf((*MockDataSourceService)(nil).GetCurrentMaxConnections), arg0, arg1)
}

// GetCurrentMetricData mocks base method.
func (m *MockDataSourceService) GetCurrentMetricData(arg0 context.Context, arg1 *datasource.GetMetricDatapointsReq) (*datasource.GetMetricDatapointsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentMetricData", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricDatapointsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentMetricData indicates an expected call of GetCurrentMetricData.
func (mr *MockDataSourceServiceMockRecorder) GetCurrentMetricData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentMetricData", reflect.TypeOf((*MockDataSourceService)(nil).GetCurrentMetricData), arg0, arg1)
}

// GetDBInnerAddress mocks base method.
func (m *MockDataSourceService) GetDBInnerAddress(arg0 context.Context, arg1 *datasource.GetDBInnerAddressReq) (*datasource.GetDBInnerAddressResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDBInnerAddress", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetDBInnerAddressResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDBInnerAddress indicates an expected call of GetDBInnerAddress.
func (mr *MockDataSourceServiceMockRecorder) GetDBInnerAddress(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDBInnerAddress", reflect.TypeOf((*MockDataSourceService)(nil).GetDBInnerAddress), arg0, arg1)
}

// GetDBInstanceInfo mocks base method.
func (m *MockDataSourceService) GetDBInstanceInfo(arg0 context.Context, arg1 *datasource.GetDBInstanceInfoReq) (*datasource.GetDBInstanceInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDBInstanceInfo", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetDBInstanceInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDBInstanceInfo indicates an expected call of GetDBInstanceInfo.
func (mr *MockDataSourceServiceMockRecorder) GetDBInstanceInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDBInstanceInfo", reflect.TypeOf((*MockDataSourceService)(nil).GetDBInstanceInfo), arg0, arg1)
}

// GetDBServiceTreeMountInfo mocks base method.
func (m *MockDataSourceService) GetDBServiceTreeMountInfo(arg0 context.Context, arg1 *datasource.GetDBServiceTreeMountInfoReq) (*datasource.GetDBServiceTreeMountInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDBServiceTreeMountInfo", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetDBServiceTreeMountInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDBServiceTreeMountInfo indicates an expected call of GetDBServiceTreeMountInfo.
func (mr *MockDataSourceServiceMockRecorder) GetDBServiceTreeMountInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDBServiceTreeMountInfo", reflect.TypeOf((*MockDataSourceService)(nil).GetDBServiceTreeMountInfo), arg0, arg1)
}

// GetDataPointCountSlowQueries mocks base method.
func (m *MockDataSourceService) GetDataPointCountSlowQueries(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDataPointCountSlowQueries", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDataPointCountSlowQueries indicates an expected call of GetDataPointCountSlowQueries.
func (mr *MockDataSourceServiceMockRecorder) GetDataPointCountSlowQueries(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDataPointCountSlowQueries", reflect.TypeOf((*MockDataSourceService)(nil).GetDataPointCountSlowQueries), arg0, arg1)
}

// GetDatasourceAddress mocks base method.
func (m *MockDataSourceService) GetDatasourceAddress(arg0 context.Context, arg1 *shared.DataSource) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDatasourceAddress", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetDatasourceAddress indicates an expected call of GetDatasourceAddress.
func (mr *MockDataSourceServiceMockRecorder) GetDatasourceAddress(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDatasourceAddress", reflect.TypeOf((*MockDataSourceService)(nil).GetDatasourceAddress), arg0, arg1)
}

// GetDiskAvailableDays mocks base method.
func (m *MockDataSourceService) GetDiskAvailableDays(arg0 context.Context, arg1 *datasource.GetDiskAvailableDaysReq) (*datasource.GetDiskAvailableDaysResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiskAvailableDays", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetDiskAvailableDaysResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiskAvailableDays indicates an expected call of GetDiskAvailableDays.
func (mr *MockDataSourceServiceMockRecorder) GetDiskAvailableDays(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiskAvailableDays", reflect.TypeOf((*MockDataSourceService)(nil).GetDiskAvailableDays), arg0, arg1)
}

// GetDiskFutureSize mocks base method.
func (m *MockDataSourceService) GetDiskFutureSize(arg0 context.Context, arg1 *datasource.GetDiskFutureSizeReq) (*datasource.GetDiskFutureSizeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiskFutureSize", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetDiskFutureSizeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiskFutureSize indicates an expected call of GetDiskFutureSize.
func (mr *MockDataSourceServiceMockRecorder) GetDiskFutureSize(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiskFutureSize", reflect.TypeOf((*MockDataSourceService)(nil).GetDiskFutureSize), arg0, arg1)
}

// GetDiskMetricDetail mocks base method.
func (m *MockDataSourceService) GetDiskMetricDetail(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiskMetricDetail", arg0, arg1)
	ret0, _ := ret[0].(*model.DescribeDiagItemDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiskMetricDetail indicates an expected call of GetDiskMetricDetail.
func (mr *MockDataSourceServiceMockRecorder) GetDiskMetricDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiskMetricDetail", reflect.TypeOf((*MockDataSourceService)(nil).GetDiskMetricDetail), arg0, arg1)
}

// GetDiskSize mocks base method.
func (m *MockDataSourceService) GetDiskSize(arg0 context.Context, arg1 *datasource.GetDiskSizeReq) (*datasource.GetDiskSizeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiskSize", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetDiskSizeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiskSize indicates an expected call of GetDiskSize.
func (mr *MockDataSourceServiceMockRecorder) GetDiskSize(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiskSize", reflect.TypeOf((*MockDataSourceService)(nil).GetDiskSize), arg0, arg1)
}

// GetDiskUsageMetricDetail mocks base method.
func (m *MockDataSourceService) GetDiskUsageMetricDetail(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiskUsageMetricDetail", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiskUsageMetricDetail indicates an expected call of GetDiskUsageMetricDetail.
func (mr *MockDataSourceServiceMockRecorder) GetDiskUsageMetricDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiskUsageMetricDetail", reflect.TypeOf((*MockDataSourceService)(nil).GetDiskUsageMetricDetail), arg0, arg1)
}

// GetInspectionBpHitMetric mocks base method.
func (m *MockDataSourceService) GetInspectionBpHitMetric(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInspectionBpHitMetric", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInspectionBpHitMetric indicates an expected call of GetInspectionBpHitMetric.
func (mr *MockDataSourceServiceMockRecorder) GetInspectionBpHitMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInspectionBpHitMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetInspectionBpHitMetric), arg0, arg1)
}

// GetInspectionConnRatioMetric mocks base method.
func (m *MockDataSourceService) GetInspectionConnRatioMetric(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInspectionConnRatioMetric", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInspectionConnRatioMetric indicates an expected call of GetInspectionConnRatioMetric.
func (mr *MockDataSourceServiceMockRecorder) GetInspectionConnRatioMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInspectionConnRatioMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetInspectionConnRatioMetric), arg0, arg1)
}

// GetInspectionConnectedMetric mocks base method.
func (m *MockDataSourceService) GetInspectionConnectedMetric(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInspectionConnectedMetric", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInspectionConnectedMetric indicates an expected call of GetInspectionConnectedMetric.
func (mr *MockDataSourceServiceMockRecorder) GetInspectionConnectedMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInspectionConnectedMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetInspectionConnectedMetric), arg0, arg1)
}

// GetInspectionCpuMetric mocks base method.
func (m *MockDataSourceService) GetInspectionCpuMetric(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInspectionCpuMetric", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInspectionCpuMetric indicates an expected call of GetInspectionCpuMetric.
func (mr *MockDataSourceServiceMockRecorder) GetInspectionCpuMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInspectionCpuMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetInspectionCpuMetric), arg0, arg1)
}

// GetInspectionDiskMetric mocks base method.
func (m *MockDataSourceService) GetInspectionDiskMetric(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInspectionDiskMetric", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInspectionDiskMetric indicates an expected call of GetInspectionDiskMetric.
func (mr *MockDataSourceServiceMockRecorder) GetInspectionDiskMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInspectionDiskMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetInspectionDiskMetric), arg0, arg1)
}

// GetInspectionInputMetric mocks base method.
func (m *MockDataSourceService) GetInspectionInputMetric(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInspectionInputMetric", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInspectionInputMetric indicates an expected call of GetInspectionInputMetric.
func (mr *MockDataSourceServiceMockRecorder) GetInspectionInputMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInspectionInputMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetInspectionInputMetric), arg0, arg1)
}

// GetInspectionMemMetric mocks base method.
func (m *MockDataSourceService) GetInspectionMemMetric(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInspectionMemMetric", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInspectionMemMetric indicates an expected call of GetInspectionMemMetric.
func (mr *MockDataSourceServiceMockRecorder) GetInspectionMemMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInspectionMemMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetInspectionMemMetric), arg0, arg1)
}

// GetInspectionOutputMetric mocks base method.
func (m *MockDataSourceService) GetInspectionOutputMetric(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInspectionOutputMetric", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInspectionOutputMetric indicates an expected call of GetInspectionOutputMetric.
func (mr *MockDataSourceServiceMockRecorder) GetInspectionOutputMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInspectionOutputMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetInspectionOutputMetric), arg0, arg1)
}

// GetInspectionQpsMetric mocks base method.
func (m *MockDataSourceService) GetInspectionQpsMetric(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInspectionQpsMetric", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInspectionQpsMetric indicates an expected call of GetInspectionQpsMetric.
func (mr *MockDataSourceServiceMockRecorder) GetInspectionQpsMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInspectionQpsMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetInspectionQpsMetric), arg0, arg1)
}

// GetInspectionTpsMetric mocks base method.
func (m *MockDataSourceService) GetInspectionTpsMetric(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInspectionTpsMetric", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInspectionTpsMetric indicates an expected call of GetInspectionTpsMetric.
func (mr *MockDataSourceServiceMockRecorder) GetInspectionTpsMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInspectionTpsMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetInspectionTpsMetric), arg0, arg1)
}

// GetInstancePrimaryNodeId mocks base method.
func (m *MockDataSourceService) GetInstancePrimaryNodeId(arg0 context.Context, arg1 *datasource.GetInstancePrimaryNodeIdReq) (*datasource.GetInstancePrimaryNodeIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancePrimaryNodeId", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetInstancePrimaryNodeIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancePrimaryNodeId indicates an expected call of GetInstancePrimaryNodeId.
func (mr *MockDataSourceServiceMockRecorder) GetInstancePrimaryNodeId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancePrimaryNodeId", reflect.TypeOf((*MockDataSourceService)(nil).GetInstancePrimaryNodeId), arg0, arg1)
}

// GetInstanceProxyTopo mocks base method.
func (m *MockDataSourceService) GetInstanceProxyTopo(arg0 context.Context, arg1 *datasource.GetInstanceTopoReq) ([]*model.InnerRdsInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceProxyTopo", arg0, arg1)
	ret0, _ := ret[0].([]*model.InnerRdsInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceProxyTopo indicates an expected call of GetInstanceProxyTopo.
func (mr *MockDataSourceServiceMockRecorder) GetInstanceProxyTopo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceProxyTopo", reflect.TypeOf((*MockDataSourceService)(nil).GetInstanceProxyTopo), arg0, arg1)
}

// GetInstanceSlaveAddress mocks base method.
func (m *MockDataSourceService) GetInstanceSlaveAddress(arg0 types.Context, arg1 *datasource.GetInstanceSlaveAddressReq) (*datasource.GetInstanceSlaveAddressResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceSlaveAddress", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetInstanceSlaveAddressResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceSlaveAddress indicates an expected call of GetInstanceSlaveAddress.
func (mr *MockDataSourceServiceMockRecorder) GetInstanceSlaveAddress(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceSlaveAddress", reflect.TypeOf((*MockDataSourceService)(nil).GetInstanceSlaveAddress), arg0, arg1)
}

// GetInstanceTopo mocks base method.
func (m *MockDataSourceService) GetInstanceTopo(arg0 context.Context, arg1 *datasource.GetInstanceTopoReq) ([]*model.InnerRdsInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceTopo", arg0, arg1)
	ret0, _ := ret[0].([]*model.InnerRdsInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceTopo indicates an expected call of GetInstanceTopo.
func (mr *MockDataSourceServiceMockRecorder) GetInstanceTopo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceTopo", reflect.TypeOf((*MockDataSourceService)(nil).GetInstanceTopo), arg0, arg1)
}

// GetKey mocks base method.
func (m *MockDataSourceService) GetKey(arg0 context.Context, arg1 *datasource.GetKeyReq) (*datasource.GetKeyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKey", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetKeyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKey indicates an expected call of GetKey.
func (mr *MockDataSourceServiceMockRecorder) GetKey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKey", reflect.TypeOf((*MockDataSourceService)(nil).GetKey), arg0, arg1)
}

// GetLatestDiskUsage mocks base method.
func (m *MockDataSourceService) GetLatestDiskUsage(arg0 context.Context, arg1 *datasource.GetLatestDiskUsageReq) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestDiskUsage", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestDiskUsage indicates an expected call of GetLatestDiskUsage.
func (mr *MockDataSourceServiceMockRecorder) GetLatestDiskUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestDiskUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetLatestDiskUsage), arg0, arg1)
}

// GetLogDownloadList mocks base method.
func (m *MockDataSourceService) GetLogDownloadList(arg0 context.Context, arg1 *datasource.GetLogDownloadListReq) (*datasource.GetLogDownloadListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogDownloadList", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetLogDownloadListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLogDownloadList indicates an expected call of GetLogDownloadList.
func (mr *MockDataSourceServiceMockRecorder) GetLogDownloadList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogDownloadList", reflect.TypeOf((*MockDataSourceService)(nil).GetLogDownloadList), arg0, arg1)
}

// GetManagedAccountAndPwd mocks base method.
func (m *MockDataSourceService) GetManagedAccountAndPwd(arg0 context.Context, arg1 *shared.DataSource) (*datasource.GetManagedAccountAndPwdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetManagedAccountAndPwd", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetManagedAccountAndPwdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManagedAccountAndPwd indicates an expected call of GetManagedAccountAndPwd.
func (mr *MockDataSourceServiceMockRecorder) GetManagedAccountAndPwd(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManagedAccountAndPwd", reflect.TypeOf((*MockDataSourceService)(nil).GetManagedAccountAndPwd), arg0, arg1)
}

// GetMaxBandwidth mocks base method.
func (m *MockDataSourceService) GetMaxBandwidth(arg0 context.Context, arg1 *datasource.GetMinMaxBandwidthReq) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxBandwidth", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxBandwidth indicates an expected call of GetMaxBandwidth.
func (mr *MockDataSourceServiceMockRecorder) GetMaxBandwidth(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxBandwidth", reflect.TypeOf((*MockDataSourceService)(nil).GetMaxBandwidth), arg0, arg1)
}

// GetMaxConnectionUsage mocks base method.
func (m *MockDataSourceService) GetMaxConnectionUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxConnectionUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxConnectionUsage indicates an expected call of GetMaxConnectionUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMaxConnectionUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxConnectionUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMaxConnectionUsage), arg0, arg1)
}

// GetMaxConnections mocks base method.
func (m *MockDataSourceService) GetMaxConnections(arg0 context.Context, arg1 *datasource.GetMaxConnectionsReq) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxConnections", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxConnections indicates an expected call of GetMaxConnections.
func (mr *MockDataSourceServiceMockRecorder) GetMaxConnections(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxConnections", reflect.TypeOf((*MockDataSourceService)(nil).GetMaxConnections), arg0, arg1)
}

// GetMaxCpuUsage mocks base method.
func (m *MockDataSourceService) GetMaxCpuUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxCpuUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxCpuUsage indicates an expected call of GetMaxCpuUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMaxCpuUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxCpuUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMaxCpuUsage), arg0, arg1)
}

// GetMaxDiskUsage mocks base method.
func (m *MockDataSourceService) GetMaxDiskUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxDiskUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxDiskUsage indicates an expected call of GetMaxDiskUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMaxDiskUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxDiskUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMaxDiskUsage), arg0, arg1)
}

// GetMaxMemUsage mocks base method.
func (m *MockDataSourceService) GetMaxMemUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxMemUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxMemUsage indicates an expected call of GetMaxMemUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMaxMemUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxMemUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMaxMemUsage), arg0, arg1)
}

// GetMaxQpsUsage mocks base method.
func (m *MockDataSourceService) GetMaxQpsUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxQpsUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxQpsUsage indicates an expected call of GetMaxQpsUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMaxQpsUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxQpsUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMaxQpsUsage), arg0, arg1)
}

// GetMaxSessionUsage mocks base method.
func (m *MockDataSourceService) GetMaxSessionUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxSessionUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxSessionUsage indicates an expected call of GetMaxSessionUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMaxSessionUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxSessionUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMaxSessionUsage), arg0, arg1)
}

// GetMaxTpsUsage mocks base method.
func (m *MockDataSourceService) GetMaxTpsUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxTpsUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxTpsUsage indicates an expected call of GetMaxTpsUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMaxTpsUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxTpsUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMaxTpsUsage), arg0, arg1)
}

// GetMemMetricDetail mocks base method.
func (m *MockDataSourceService) GetMemMetricDetail(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMemMetricDetail", arg0, arg1)
	ret0, _ := ret[0].(*model.DescribeDiagItemDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMemMetricDetail indicates an expected call of GetMemMetricDetail.
func (mr *MockDataSourceServiceMockRecorder) GetMemMetricDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMemMetricDetail", reflect.TypeOf((*MockDataSourceService)(nil).GetMemMetricDetail), arg0, arg1)
}

// GetMemUsageMetricDetail mocks base method.
func (m *MockDataSourceService) GetMemUsageMetricDetail(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.ItemDataResult_, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMemUsageMetricDetail", arg0, arg1)
	ret0, _ := ret[0].(*model.ItemDataResult_)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMemUsageMetricDetail indicates an expected call of GetMemUsageMetricDetail.
func (mr *MockDataSourceServiceMockRecorder) GetMemUsageMetricDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMemUsageMetricDetail", reflect.TypeOf((*MockDataSourceService)(nil).GetMemUsageMetricDetail), arg0, arg1)
}

// GetMinBandwidth mocks base method.
func (m *MockDataSourceService) GetMinBandwidth(arg0 context.Context, arg1 *datasource.GetMinMaxBandwidthReq) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinBandwidth", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinBandwidth indicates an expected call of GetMinBandwidth.
func (mr *MockDataSourceServiceMockRecorder) GetMinBandwidth(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinBandwidth", reflect.TypeOf((*MockDataSourceService)(nil).GetMinBandwidth), arg0, arg1)
}

// GetMinConnectionUsage mocks base method.
func (m *MockDataSourceService) GetMinConnectionUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinConnectionUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinConnectionUsage indicates an expected call of GetMinConnectionUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMinConnectionUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinConnectionUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMinConnectionUsage), arg0, arg1)
}

// GetMinCpuUsage mocks base method.
func (m *MockDataSourceService) GetMinCpuUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinCpuUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinCpuUsage indicates an expected call of GetMinCpuUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMinCpuUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinCpuUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMinCpuUsage), arg0, arg1)
}

// GetMinDiskUsage mocks base method.
func (m *MockDataSourceService) GetMinDiskUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinDiskUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinDiskUsage indicates an expected call of GetMinDiskUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMinDiskUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinDiskUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMinDiskUsage), arg0, arg1)
}

// GetMinMemUsage mocks base method.
func (m *MockDataSourceService) GetMinMemUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinMemUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinMemUsage indicates an expected call of GetMinMemUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMinMemUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinMemUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMinMemUsage), arg0, arg1)
}

// GetMinQpsUsage mocks base method.
func (m *MockDataSourceService) GetMinQpsUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinQpsUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinQpsUsage indicates an expected call of GetMinQpsUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMinQpsUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinQpsUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMinQpsUsage), arg0, arg1)
}

// GetMinSessionUsage mocks base method.
func (m *MockDataSourceService) GetMinSessionUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinSessionUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinSessionUsage indicates an expected call of GetMinSessionUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMinSessionUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinSessionUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMinSessionUsage), arg0, arg1)
}

// GetMinTpsUsage mocks base method.
func (m *MockDataSourceService) GetMinTpsUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMinTpsUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMinTpsUsage indicates an expected call of GetMinTpsUsage.
func (mr *MockDataSourceServiceMockRecorder) GetMinTpsUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMinTpsUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetMinTpsUsage), arg0, arg1)
}

// GetMonitorByMetric mocks base method.
func (m *MockDataSourceService) GetMonitorByMetric(arg0 context.Context, arg1 *datasource.GetMonitorByMetricReq) ([]*model.MetricResource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMonitorByMetric", arg0, arg1)
	ret0, _ := ret[0].([]*model.MetricResource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMonitorByMetric indicates an expected call of GetMonitorByMetric.
func (mr *MockDataSourceServiceMockRecorder) GetMonitorByMetric(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMonitorByMetric", reflect.TypeOf((*MockDataSourceService)(nil).GetMonitorByMetric), arg0, arg1)
}

// GetPartitionInfos mocks base method.
func (m *MockDataSourceService) GetPartitionInfos(arg0 context.Context, arg1 *shared.DataSource, arg2 string) ([]*datasource.DbPartitionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPartitionInfos", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*datasource.DbPartitionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPartitionInfos indicates an expected call of GetPartitionInfos.
func (mr *MockDataSourceServiceMockRecorder) GetPartitionInfos(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPartitionInfos", reflect.TypeOf((*MockDataSourceService)(nil).GetPartitionInfos), arg0, arg1, arg2)
}

// GetPreSecondMetricData mocks base method.
func (m *MockDataSourceService) GetPreSecondMetricData(arg0 context.Context, arg1 *datasource.GetMetricDatapointsReq) (*datasource.GetMetricDatapointsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreSecondMetricData", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricDatapointsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreSecondMetricData indicates an expected call of GetPreSecondMetricData.
func (mr *MockDataSourceServiceMockRecorder) GetPreSecondMetricData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreSecondMetricData", reflect.TypeOf((*MockDataSourceService)(nil).GetPreSecondMetricData), arg0, arg1)
}

// GetPreSecondMetricDataByInstance mocks base method.
func (m *MockDataSourceService) GetPreSecondMetricDataByInstance(arg0 context.Context, arg1 *datasource.GetMetricDatapointsReq) (*datasource.GetMetricDatapointsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreSecondMetricDataByInstance", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricDatapointsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreSecondMetricDataByInstance indicates an expected call of GetPreSecondMetricDataByInstance.
func (mr *MockDataSourceServiceMockRecorder) GetPreSecondMetricDataByInstance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreSecondMetricDataByInstance", reflect.TypeOf((*MockDataSourceService)(nil).GetPreSecondMetricDataByInstance), arg0, arg1)
}

// GetQpsUsage mocks base method.
func (m *MockDataSourceService) GetQpsUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQpsUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQpsUsage indicates an expected call of GetQpsUsage.
func (mr *MockDataSourceServiceMockRecorder) GetQpsUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQpsUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetQpsUsage), arg0, arg1)
}

// GetSessionMetricDetail mocks base method.
func (m *MockDataSourceService) GetSessionMetricDetail(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*model.DescribeDiagItemDetailResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSessionMetricDetail", arg0, arg1)
	ret0, _ := ret[0].(*model.DescribeDiagItemDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSessionMetricDetail indicates an expected call of GetSessionMetricDetail.
func (mr *MockDataSourceServiceMockRecorder) GetSessionMetricDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSessionMetricDetail", reflect.TypeOf((*MockDataSourceService)(nil).GetSessionMetricDetail), arg0, arg1)
}

// GetShardingDbType mocks base method.
func (m *MockDataSourceService) GetShardingDbType(arg0 context.Context, arg1 *shared.DataSource, arg2, arg3 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShardingDbType", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShardingDbType indicates an expected call of GetShardingDbType.
func (mr *MockDataSourceServiceMockRecorder) GetShardingDbType(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShardingDbType", reflect.TypeOf((*MockDataSourceService)(nil).GetShardingDbType), arg0, arg1, arg2, arg3)
}

// GetTableIndexInfo mocks base method.
func (m *MockDataSourceService) GetTableIndexInfo(arg0 context.Context, arg1 *datasource.GetTableIndexInfoReq) (*datasource.GetTableInfoIndexResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTableIndexInfo", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetTableInfoIndexResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTableIndexInfo indicates an expected call of GetTableIndexInfo.
func (mr *MockDataSourceServiceMockRecorder) GetTableIndexInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTableIndexInfo", reflect.TypeOf((*MockDataSourceService)(nil).GetTableIndexInfo), arg0, arg1)
}

// GetTableIndexValue mocks base method.
func (m *MockDataSourceService) GetTableIndexValue(arg0 context.Context, arg1 *datasource.GetIndexValueReq) (*datasource.GetIndexValueResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTableIndexValue", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetIndexValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTableIndexValue indicates an expected call of GetTableIndexValue.
func (mr *MockDataSourceServiceMockRecorder) GetTableIndexValue(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTableIndexValue", reflect.TypeOf((*MockDataSourceService)(nil).GetTableIndexValue), arg0, arg1)
}

// GetTpsUsage mocks base method.
func (m *MockDataSourceService) GetTpsUsage(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) (*datasource.GetMetricUsageResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTpsUsage", arg0, arg1)
	ret0, _ := ret[0].(*datasource.GetMetricUsageResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTpsUsage indicates an expected call of GetTpsUsage.
func (mr *MockDataSourceServiceMockRecorder) GetTpsUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTpsUsage", reflect.TypeOf((*MockDataSourceService)(nil).GetTpsUsage), arg0, arg1)
}

// GetUsedSize mocks base method.
func (m *MockDataSourceService) GetUsedSize(arg0 context.Context, arg1 *datasource.GetDiskSizeReq) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsedSize", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsedSize indicates an expected call of GetUsedSize.
func (mr *MockDataSourceServiceMockRecorder) GetUsedSize(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsedSize", reflect.TypeOf((*MockDataSourceService)(nil).GetUsedSize), arg0, arg1)
}

// GrantAccountPrivilege mocks base method.
func (m *MockDataSourceService) GrantAccountPrivilege(arg0 context.Context, arg1 *datasource.GrantAccountPrivilegeReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GrantAccountPrivilege", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// GrantAccountPrivilege indicates an expected call of GrantAccountPrivilege.
func (mr *MockDataSourceServiceMockRecorder) GrantAccountPrivilege(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrantAccountPrivilege", reflect.TypeOf((*MockDataSourceService)(nil).GrantAccountPrivilege), arg0, arg1)
}

// GrantReplicationPrivilege mocks base method.
func (m *MockDataSourceService) GrantReplicationPrivilege(arg0 context.Context, arg1 *shared.DataSource, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GrantReplicationPrivilege", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// GrantReplicationPrivilege indicates an expected call of GrantReplicationPrivilege.
func (mr *MockDataSourceServiceMockRecorder) GrantReplicationPrivilege(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrantReplicationPrivilege", reflect.TypeOf((*MockDataSourceService)(nil).GrantReplicationPrivilege), arg0, arg1, arg2)
}

// HealthSummary mocks base method.
func (m *MockDataSourceService) HealthSummary(arg0 context.Context, arg1 *datasource.GetMetricUsageReq) ([]*model.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HealthSummary", arg0, arg1)
	ret0, _ := ret[0].([]*model.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthSummary indicates an expected call of HealthSummary.
func (mr *MockDataSourceServiceMockRecorder) HealthSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthSummary", reflect.TypeOf((*MockDataSourceService)(nil).HealthSummary), arg0, arg1)
}

// InstanceIsExist mocks base method.
func (m *MockDataSourceService) InstanceIsExist(arg0 context.Context, arg1 *datasource.InstanceIsExistReq) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstanceIsExist", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InstanceIsExist indicates an expected call of InstanceIsExist.
func (mr *MockDataSourceServiceMockRecorder) InstanceIsExist(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstanceIsExist", reflect.TypeOf((*MockDataSourceService)(nil).InstanceIsExist), arg0, arg1)
}

// IsMyOwnInstance mocks base method.
func (m *MockDataSourceService) IsMyOwnInstance(arg0 context.Context, arg1 string, arg2 shared.DataSourceType) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsMyOwnInstance", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsMyOwnInstance indicates an expected call of IsMyOwnInstance.
func (mr *MockDataSourceServiceMockRecorder) IsMyOwnInstance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsMyOwnInstance", reflect.TypeOf((*MockDataSourceService)(nil).IsMyOwnInstance), arg0, arg1, arg2)
}

// KillProcess mocks base method.
func (m *MockDataSourceService) KillProcess(arg0 context.Context, arg1 *datasource.KillProcessReq) (*datasource.KillProcessResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KillProcess", arg0, arg1)
	ret0, _ := ret[0].(*datasource.KillProcessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KillProcess indicates an expected call of KillProcess.
func (mr *MockDataSourceServiceMockRecorder) KillProcess(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KillProcess", reflect.TypeOf((*MockDataSourceService)(nil).KillProcess), arg0, arg1)
}

// KillQuery mocks base method.
func (m *MockDataSourceService) KillQuery(arg0 context.Context, arg1 *shared.DataSource, arg2 *shared.ConnectionInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KillQuery", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// KillQuery indicates an expected call of KillQuery.
func (mr *MockDataSourceServiceMockRecorder) KillQuery(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KillQuery", reflect.TypeOf((*MockDataSourceService)(nil).KillQuery), arg0, arg1, arg2)
}

// ListAllTables mocks base method.
func (m *MockDataSourceService) ListAllTables(arg0 context.Context, arg1 *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllTables", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListTablesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllTables indicates an expected call of ListAllTables.
func (mr *MockDataSourceServiceMockRecorder) ListAllTables(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllTables", reflect.TypeOf((*MockDataSourceService)(nil).ListAllTables), arg0, arg1)
}

// ListAlterKVsCommands mocks base method.
func (m *MockDataSourceService) ListAlterKVsCommands(arg0 context.Context, arg1 *datasource.ListAlterKVsCommandsReq) (*datasource.ListAlterKVsCommandsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAlterKVsCommands", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListAlterKVsCommandsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAlterKVsCommands indicates an expected call of ListAlterKVsCommands.
func (mr *MockDataSourceServiceMockRecorder) ListAlterKVsCommands(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAlterKVsCommands", reflect.TypeOf((*MockDataSourceService)(nil).ListAlterKVsCommands), arg0, arg1)
}

// ListCharsets mocks base method.
func (m *MockDataSourceService) ListCharsets(arg0 context.Context, arg1 *datasource.ListCharsetsReq) (*datasource.ListCharsetsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCharsets", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListCharsetsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCharsets indicates an expected call of ListCharsets.
func (mr *MockDataSourceServiceMockRecorder) ListCharsets(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCharsets", reflect.TypeOf((*MockDataSourceService)(nil).ListCharsets), arg0, arg1)
}

// ListCollations mocks base method.
func (m *MockDataSourceService) ListCollations(arg0 context.Context, arg1 *datasource.ListCollationsReq) (*datasource.ListCollationsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCollations", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListCollationsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCollations indicates an expected call of ListCollations.
func (mr *MockDataSourceServiceMockRecorder) ListCollations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCollations", reflect.TypeOf((*MockDataSourceService)(nil).ListCollations), arg0, arg1)
}

// ListCollections mocks base method.
func (m *MockDataSourceService) ListCollections(arg0 context.Context, arg1 *datasource.ListCollectionsReq) (*datasource.ListCollectionsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListCollections", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListCollectionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCollections indicates an expected call of ListCollections.
func (mr *MockDataSourceServiceMockRecorder) ListCollections(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCollections", reflect.TypeOf((*MockDataSourceService)(nil).ListCollections), arg0, arg1)
}

// ListDatabases mocks base method.
func (m *MockDataSourceService) ListDatabases(arg0 context.Context, arg1 *datasource.ListDatabasesReq) (*datasource.ListDatabasesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDatabases", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListDatabasesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDatabases indicates an expected call of ListDatabases.
func (mr *MockDataSourceServiceMockRecorder) ListDatabases(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDatabases", reflect.TypeOf((*MockDataSourceService)(nil).ListDatabases), arg0, arg1)
}

// ListDatabasesWithAccount mocks base method.
func (m *MockDataSourceService) ListDatabasesWithAccount(arg0 context.Context, arg1 *datasource.ListDatabasesWithAccountReq) (*datasource.ListDatabasesWithAccountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDatabasesWithAccount", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListDatabasesWithAccountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDatabasesWithAccount indicates an expected call of ListDatabasesWithAccount.
func (mr *MockDataSourceServiceMockRecorder) ListDatabasesWithAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDatabasesWithAccount", reflect.TypeOf((*MockDataSourceService)(nil).ListDatabasesWithAccount), arg0, arg1)
}

// ListErrLogs mocks base method.
func (m *MockDataSourceService) ListErrLogs(arg0 context.Context, arg1 *datasource.ListErrLogsReq) (*datasource.ListErrLogsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListErrLogs", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListErrLogsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListErrLogs indicates an expected call of ListErrLogs.
func (mr *MockDataSourceServiceMockRecorder) ListErrLogs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListErrLogs", reflect.TypeOf((*MockDataSourceService)(nil).ListErrLogs), arg0, arg1)
}

// ListEvents mocks base method.
func (m *MockDataSourceService) ListEvents(arg0 context.Context, arg1 *datasource.ListEventsReq) (*datasource.ListEventsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListEvents", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListEventsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEvents indicates an expected call of ListEvents.
func (mr *MockDataSourceServiceMockRecorder) ListEvents(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEvents", reflect.TypeOf((*MockDataSourceService)(nil).ListEvents), arg0, arg1)
}

// ListFunctions mocks base method.
func (m *MockDataSourceService) ListFunctions(arg0 context.Context, arg1 *datasource.ListFunctionsReq) (*datasource.ListFunctionsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListFunctions", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListFunctionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListFunctions indicates an expected call of ListFunctions.
func (mr *MockDataSourceServiceMockRecorder) ListFunctions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListFunctions", reflect.TypeOf((*MockDataSourceService)(nil).ListFunctions), arg0, arg1)
}

// ListIndexs mocks base method.
func (m *MockDataSourceService) ListIndexs(arg0 context.Context, arg1 *datasource.ListIndexesReq) (*datasource.ListIndexesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListIndexs", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListIndexesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListIndexs indicates an expected call of ListIndexs.
func (mr *MockDataSourceServiceMockRecorder) ListIndexs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListIndexs", reflect.TypeOf((*MockDataSourceService)(nil).ListIndexs), arg0, arg1)
}

// ListInstance mocks base method.
func (m *MockDataSourceService) ListInstance(arg0 context.Context, arg1 *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstance", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListInstanceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstance indicates an expected call of ListInstance.
func (mr *MockDataSourceServiceMockRecorder) ListInstance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstance", reflect.TypeOf((*MockDataSourceService)(nil).ListInstance), arg0, arg1)
}

// ListInstanceLightWeight mocks base method.
func (m *MockDataSourceService) ListInstanceLightWeight(arg0 context.Context, arg1 *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceLightWeight", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListInstanceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceLightWeight indicates an expected call of ListInstanceLightWeight.
func (mr *MockDataSourceServiceMockRecorder) ListInstanceLightWeight(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceLightWeight", reflect.TypeOf((*MockDataSourceService)(nil).ListInstanceLightWeight), arg0, arg1)
}

// ListInstanceNodes mocks base method.
func (m *MockDataSourceService) ListInstanceNodes(arg0 context.Context, arg1 *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceNodes", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListInstanceNodesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceNodes indicates an expected call of ListInstanceNodes.
func (mr *MockDataSourceServiceMockRecorder) ListInstanceNodes(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceNodes", reflect.TypeOf((*MockDataSourceService)(nil).ListInstanceNodes), arg0, arg1)
}

// ListInstanceNodesOri mocks base method.
func (m *MockDataSourceService) ListInstanceNodesOri(arg0 context.Context, arg1 *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesOriResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceNodesOri", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListInstanceNodesOriResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceNodesOri indicates an expected call of ListInstanceNodesOri.
func (mr *MockDataSourceServiceMockRecorder) ListInstanceNodesOri(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceNodesOri", reflect.TypeOf((*MockDataSourceService)(nil).ListInstanceNodesOri), arg0, arg1)
}

// ListInstancePods mocks base method.
func (m *MockDataSourceService) ListInstancePods(arg0 context.Context, arg1 *datasource.ListInstancePodsReq) (*datasource.ListInstancePodsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstancePods", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListInstancePodsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstancePods indicates an expected call of ListInstancePods.
func (mr *MockDataSourceServiceMockRecorder) ListInstancePods(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstancePods", reflect.TypeOf((*MockDataSourceService)(nil).ListInstancePods), arg0, arg1)
}

// ListKeyMembers mocks base method.
func (m *MockDataSourceService) ListKeyMembers(arg0 context.Context, arg1 *datasource.ListKeyMembersReq) (*datasource.ListKeyMembersResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListKeyMembers", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListKeyMembersResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListKeyMembers indicates an expected call of ListKeyMembers.
func (mr *MockDataSourceServiceMockRecorder) ListKeyMembers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListKeyMembers", reflect.TypeOf((*MockDataSourceService)(nil).ListKeyMembers), arg0, arg1)
}

// ListKeyNumbers mocks base method.
func (m *MockDataSourceService) ListKeyNumbers(arg0 context.Context, arg1 *datasource.ListKeyNumbersReq) (*datasource.ListKeyNumbersResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListKeyNumbers", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListKeyNumbersResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListKeyNumbers indicates an expected call of ListKeyNumbers.
func (mr *MockDataSourceServiceMockRecorder) ListKeyNumbers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListKeyNumbers", reflect.TypeOf((*MockDataSourceService)(nil).ListKeyNumbers), arg0, arg1)
}

// ListKeys mocks base method.
func (m *MockDataSourceService) ListKeys(arg0 context.Context, arg1 *datasource.ListKeysReq) (*datasource.ListKeysResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListKeys", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListKeysResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListKeys indicates an expected call of ListKeys.
func (mr *MockDataSourceServiceMockRecorder) ListKeys(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListKeys", reflect.TypeOf((*MockDataSourceService)(nil).ListKeys), arg0, arg1)
}

// ListMongoDBs mocks base method.
func (m *MockDataSourceService) ListMongoDBs(arg0 context.Context, arg1 *datasource.ListMongoDBsReq) (*datasource.ListMongoDBsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMongoDBs", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListMongoDBsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMongoDBs indicates an expected call of ListMongoDBs.
func (mr *MockDataSourceServiceMockRecorder) ListMongoDBs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMongoDBs", reflect.TypeOf((*MockDataSourceService)(nil).ListMongoDBs), arg0, arg1)
}

// ListPgCollations mocks base method.
func (m *MockDataSourceService) ListPgCollations(arg0 context.Context, arg1 *datasource.ListPgCollationsReq) (*datasource.ListPgCollationsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPgCollations", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListPgCollationsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPgCollations indicates an expected call of ListPgCollations.
func (mr *MockDataSourceServiceMockRecorder) ListPgCollations(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPgCollations", reflect.TypeOf((*MockDataSourceService)(nil).ListPgCollations), arg0, arg1)
}

// ListPgUsers mocks base method.
func (m *MockDataSourceService) ListPgUsers(arg0 context.Context, arg1 *datasource.ListPgUsersReq) (*datasource.ListPgUsersResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPgUsers", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListPgUsersResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPgUsers indicates an expected call of ListPgUsers.
func (mr *MockDataSourceServiceMockRecorder) ListPgUsers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPgUsers", reflect.TypeOf((*MockDataSourceService)(nil).ListPgUsers), arg0, arg1)
}

// ListProcedures mocks base method.
func (m *MockDataSourceService) ListProcedures(arg0 context.Context, arg1 *datasource.ListProceduresReq) (*datasource.ListProceduresResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListProcedures", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListProceduresResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListProcedures indicates an expected call of ListProcedures.
func (mr *MockDataSourceServiceMockRecorder) ListProcedures(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListProcedures", reflect.TypeOf((*MockDataSourceService)(nil).ListProcedures), arg0, arg1)
}

// ListSQLCCLRules mocks base method.
func (m *MockDataSourceService) ListSQLCCLRules(arg0 context.Context, arg1 *datasource.ListSQLCCLRulesReq) (*datasource.ListSQLCCLRulesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSQLCCLRules", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListSQLCCLRulesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSQLCCLRules indicates an expected call of ListSQLCCLRules.
func (mr *MockDataSourceServiceMockRecorder) ListSQLCCLRules(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSQLCCLRules", reflect.TypeOf((*MockDataSourceService)(nil).ListSQLCCLRules), arg0, arg1)
}

// ListSQLKillRules mocks base method.
func (m *MockDataSourceService) ListSQLKillRules(arg0 context.Context, arg1 *datasource.ListSQLKillRulesReq) (*datasource.ListSQLKillRulesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSQLKillRules", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListSQLKillRulesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSQLKillRules indicates an expected call of ListSQLKillRules.
func (mr *MockDataSourceServiceMockRecorder) ListSQLKillRules(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSQLKillRules", reflect.TypeOf((*MockDataSourceService)(nil).ListSQLKillRules), arg0, arg1)
}

// ListSchema mocks base method.
func (m *MockDataSourceService) ListSchema(arg0 context.Context, arg1 *datasource.ListSchemaReq) (*datasource.ListSchemaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSchema", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListSchemaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSchema indicates an expected call of ListSchema.
func (mr *MockDataSourceServiceMockRecorder) ListSchema(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSchema", reflect.TypeOf((*MockDataSourceService)(nil).ListSchema), arg0, arg1)
}

// ListSchemaTables mocks base method.
func (m *MockDataSourceService) ListSchemaTables(arg0 context.Context, arg1 *datasource.ListSchemaTablesReq) (*datasource.ListSchemaTablesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSchemaTables", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListSchemaTablesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSchemaTables indicates an expected call of ListSchemaTables.
func (mr *MockDataSourceServiceMockRecorder) ListSchemaTables(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSchemaTables", reflect.TypeOf((*MockDataSourceService)(nil).ListSchemaTables), arg0, arg1)
}

// ListSequence mocks base method.
func (m *MockDataSourceService) ListSequence(arg0 context.Context, arg1 *datasource.ListSequenceReq) (*datasource.ListSequenceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSequence", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListSequenceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSequence indicates an expected call of ListSequence.
func (mr *MockDataSourceServiceMockRecorder) ListSequence(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSequence", reflect.TypeOf((*MockDataSourceService)(nil).ListSequence), arg0, arg1)
}

// ListTableSpaces mocks base method.
func (m *MockDataSourceService) ListTableSpaces(arg0 context.Context, arg1 *datasource.ListTableSpacesReq) (*datasource.ListTableSpacesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTableSpaces", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListTableSpacesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTableSpaces indicates an expected call of ListTableSpaces.
func (mr *MockDataSourceServiceMockRecorder) ListTableSpaces(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTableSpaces", reflect.TypeOf((*MockDataSourceService)(nil).ListTableSpaces), arg0, arg1)
}

// ListTables mocks base method.
func (m *MockDataSourceService) ListTables(arg0 context.Context, arg1 *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTables", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListTablesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTables indicates an expected call of ListTables.
func (mr *MockDataSourceServiceMockRecorder) ListTables(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTables", reflect.TypeOf((*MockDataSourceService)(nil).ListTables), arg0, arg1)
}

// ListTablesInfo mocks base method.
func (m *MockDataSourceService) ListTablesInfo(arg0 context.Context, arg1 *datasource.ListTablesInfoReq) (*datasource.ListTablesInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTablesInfo", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListTablesInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTablesInfo indicates an expected call of ListTablesInfo.
func (mr *MockDataSourceServiceMockRecorder) ListTablesInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTablesInfo", reflect.TypeOf((*MockDataSourceService)(nil).ListTablesInfo), arg0, arg1)
}

// ListTriggers mocks base method.
func (m *MockDataSourceService) ListTriggers(arg0 context.Context, arg1 *datasource.ListTriggersReq) (*datasource.ListTriggersResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTriggers", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListTriggersResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTriggers indicates an expected call of ListTriggers.
func (mr *MockDataSourceServiceMockRecorder) ListTriggers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTriggers", reflect.TypeOf((*MockDataSourceService)(nil).ListTriggers), arg0, arg1)
}

// ListViews mocks base method.
func (m *MockDataSourceService) ListViews(arg0 context.Context, arg1 *datasource.ListViewsReq) (*datasource.ListViewsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListViews", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ListViewsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListViews indicates an expected call of ListViews.
func (mr *MockDataSourceServiceMockRecorder) ListViews(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListViews", reflect.TypeOf((*MockDataSourceService)(nil).ListViews), arg0, arg1)
}

// ModifyAccountPrivilege mocks base method.
func (m *MockDataSourceService) ModifyAccountPrivilege(arg0 context.Context, arg1 *datasource.ModifyAccountPrivilegeReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyAccountPrivilege", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyAccountPrivilege indicates an expected call of ModifyAccountPrivilege.
func (mr *MockDataSourceServiceMockRecorder) ModifyAccountPrivilege(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyAccountPrivilege", reflect.TypeOf((*MockDataSourceService)(nil).ModifyAccountPrivilege), arg0, arg1)
}

// ModifyAutoKillSessionConfig mocks base method.
func (m *MockDataSourceService) ModifyAutoKillSessionConfig(arg0 context.Context, arg1 *datasource.ModifyAutoKillSessionConfigReq) (*datasource.ModifyAutoKillSessionConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyAutoKillSessionConfig", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ModifyAutoKillSessionConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyAutoKillSessionConfig indicates an expected call of ModifyAutoKillSessionConfig.
func (mr *MockDataSourceServiceMockRecorder) ModifyAutoKillSessionConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyAutoKillSessionConfig", reflect.TypeOf((*MockDataSourceService)(nil).ModifyAutoKillSessionConfig), arg0, arg1)
}

// ModifyDBAutoScalingConfig mocks base method.
func (m *MockDataSourceService) ModifyDBAutoScalingConfig(arg0 context.Context, arg1 *datasource.ModifyDBAutoScalingConfigReq) (*datasource.ModifyDBAutoScalingConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyDBAutoScalingConfig", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ModifyDBAutoScalingConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyDBAutoScalingConfig indicates an expected call of ModifyDBAutoScalingConfig.
func (mr *MockDataSourceServiceMockRecorder) ModifyDBAutoScalingConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyDBAutoScalingConfig", reflect.TypeOf((*MockDataSourceService)(nil).ModifyDBAutoScalingConfig), arg0, arg1)
}

// ModifyDBInstanceSpec mocks base method.
func (m *MockDataSourceService) ModifyDBInstanceSpec(arg0 context.Context, arg1 *datasource.ModifyDBInstanceSpecReq) (*datasource.ModifyDBInstanceSpecResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyDBInstanceSpec", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ModifyDBInstanceSpecResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyDBInstanceSpec indicates an expected call of ModifyDBInstanceSpec.
func (mr *MockDataSourceServiceMockRecorder) ModifyDBInstanceSpec(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyDBInstanceSpec", reflect.TypeOf((*MockDataSourceService)(nil).ModifyDBInstanceSpec), arg0, arg1)
}

// ModifyDBLocalSpecManually mocks base method.
func (m *MockDataSourceService) ModifyDBLocalSpecManually(arg0 context.Context, arg1 *datasource.ModifyDBLocalSpecManuallyReq) (*datasource.ModifyDBLocalSpecManuallyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyDBLocalSpecManually", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ModifyDBLocalSpecManuallyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyDBLocalSpecManually indicates an expected call of ModifyDBLocalSpecManually.
func (mr *MockDataSourceServiceMockRecorder) ModifyDBLocalSpecManually(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyDBLocalSpecManually", reflect.TypeOf((*MockDataSourceService)(nil).ModifyDBLocalSpecManually), arg0, arg1)
}

// ModifyFullSQLLogConfig mocks base method.
func (m *MockDataSourceService) ModifyFullSQLLogConfig(arg0 context.Context, arg1 *datasource.ModifyFullSQLLogConfigReq) (*datasource.ModifyFullSQLLogConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyFullSQLLogConfig", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ModifyFullSQLLogConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyFullSQLLogConfig indicates an expected call of ModifyFullSQLLogConfig.
func (mr *MockDataSourceServiceMockRecorder) ModifyFullSQLLogConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyFullSQLLogConfig", reflect.TypeOf((*MockDataSourceService)(nil).ModifyFullSQLLogConfig), arg0, arg1)
}

// ModifyProxyThrottleRule mocks base method.
func (m *MockDataSourceService) ModifyProxyThrottleRule(arg0 context.Context, arg1 *datasource.ModifyProxyThrottleRuleReq) (*datasource.ModifyProxyThrottleRuleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyProxyThrottleRule", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ModifyProxyThrottleRuleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyProxyThrottleRule indicates an expected call of ModifyProxyThrottleRule.
func (mr *MockDataSourceServiceMockRecorder) ModifyProxyThrottleRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyProxyThrottleRule", reflect.TypeOf((*MockDataSourceService)(nil).ModifyProxyThrottleRule), arg0, arg1)
}

// ModifySQLCCLConfig mocks base method.
func (m *MockDataSourceService) ModifySQLCCLConfig(arg0 context.Context, arg1 *datasource.ModifySQLCCLConfigReq) (*datasource.ModifySQLCCLConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifySQLCCLConfig", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ModifySQLCCLConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifySQLCCLConfig indicates an expected call of ModifySQLCCLConfig.
func (mr *MockDataSourceServiceMockRecorder) ModifySQLCCLConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifySQLCCLConfig", reflect.TypeOf((*MockDataSourceService)(nil).ModifySQLCCLConfig), arg0, arg1)
}

// ModifySQLKillRule mocks base method.
func (m *MockDataSourceService) ModifySQLKillRule(arg0 context.Context, arg1 *datasource.ModifySQLKillRuleReq) (*datasource.ModifySQLKillRuleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifySQLKillRule", arg0, arg1)
	ret0, _ := ret[0].(*datasource.ModifySQLKillRuleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifySQLKillRule indicates an expected call of ModifySQLKillRule.
func (mr *MockDataSourceServiceMockRecorder) ModifySQLKillRule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifySQLKillRule", reflect.TypeOf((*MockDataSourceService)(nil).ModifySQLKillRule), arg0, arg1)
}

// OpenDBInstanceAuditLog mocks base method.
func (m *MockDataSourceService) OpenDBInstanceAuditLog(arg0 context.Context, arg1 *datasource.OpenDBInstanceAuditLogReq) (*datasource.OpenDBInstanceAuditLogResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenDBInstanceAuditLog", arg0, arg1)
	ret0, _ := ret[0].(*datasource.OpenDBInstanceAuditLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenDBInstanceAuditLog indicates an expected call of OpenDBInstanceAuditLog.
func (mr *MockDataSourceServiceMockRecorder) OpenDBInstanceAuditLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenDBInstanceAuditLog", reflect.TypeOf((*MockDataSourceService)(nil).OpenDBInstanceAuditLog), arg0, arg1)
}

// OpenTunnel mocks base method.
func (m *MockDataSourceService) OpenTunnel(arg0 context.Context, arg1 *shared.DataSource, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OpenTunnel", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// OpenTunnel indicates an expected call of OpenTunnel.
func (mr *MockDataSourceServiceMockRecorder) OpenTunnel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenTunnel", reflect.TypeOf((*MockDataSourceService)(nil).OpenTunnel), arg0, arg1, arg2)
}

// PreCheckFreeLockCorrectOrders mocks base method.
func (m *MockDataSourceService) PreCheckFreeLockCorrectOrders(arg0 context.Context, arg1 *datasource.PreCheckFreeLockCorrectOrdersReq) (*datasource.PreCheckFreeLockCorrectOrdersResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PreCheckFreeLockCorrectOrders", arg0, arg1)
	ret0, _ := ret[0].(*datasource.PreCheckFreeLockCorrectOrdersResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PreCheckFreeLockCorrectOrders indicates an expected call of PreCheckFreeLockCorrectOrders.
func (mr *MockDataSourceServiceMockRecorder) PreCheckFreeLockCorrectOrders(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PreCheckFreeLockCorrectOrders", reflect.TypeOf((*MockDataSourceService)(nil).PreCheckFreeLockCorrectOrders), arg0, arg1)
}

// RemoveWhiteList mocks base method.
func (m *MockDataSourceService) RemoveWhiteList(arg0 context.Context, arg1 string, arg2 *shared.DataSource, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveWhiteList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveWhiteList indicates an expected call of RemoveWhiteList.
func (mr *MockDataSourceServiceMockRecorder) RemoveWhiteList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveWhiteList", reflect.TypeOf((*MockDataSourceService)(nil).RemoveWhiteList), arg0, arg1, arg2, arg3)
}

// ResetAccount mocks base method.
func (m *MockDataSourceService) ResetAccount(arg0 context.Context, arg1 string, arg2 shared.DataSourceType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetAccount indicates an expected call of ResetAccount.
func (mr *MockDataSourceServiceMockRecorder) ResetAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetAccount", reflect.TypeOf((*MockDataSourceService)(nil).ResetAccount), arg0, arg1, arg2)
}

// StopFreeLockCorrectOrders mocks base method.
func (m *MockDataSourceService) StopFreeLockCorrectOrders(arg0 context.Context, arg1 *datasource.StopFreeLockCorrectOrdersReq) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopFreeLockCorrectOrders", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// StopFreeLockCorrectOrders indicates an expected call of StopFreeLockCorrectOrders.
func (mr *MockDataSourceServiceMockRecorder) StopFreeLockCorrectOrders(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopFreeLockCorrectOrders", reflect.TypeOf((*MockDataSourceService)(nil).StopFreeLockCorrectOrders), arg0, arg1)
}

// Type mocks base method.
func (m *MockDataSourceService) Type() shared.DataSourceType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Type")
	ret0, _ := ret[0].(shared.DataSourceType)
	return ret0
}

// Type indicates an expected call of Type.
func (mr *MockDataSourceServiceMockRecorder) Type() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Type", reflect.TypeOf((*MockDataSourceService)(nil).Type))
}

// UpdateWhiteList mocks base method.
func (m *MockDataSourceService) UpdateWhiteList(arg0 context.Context, arg1 string, arg2 *shared.DataSource, arg3 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWhiteList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWhiteList indicates an expected call of UpdateWhiteList.
func (mr *MockDataSourceServiceMockRecorder) UpdateWhiteList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWhiteList", reflect.TypeOf((*MockDataSourceService)(nil).UpdateWhiteList), arg0, arg1, arg2, arg3)
}

// ValidateDryRun mocks base method.
func (m *MockDataSourceService) ValidateDryRun(arg0 context.Context, arg1 *datasource.ValidateDryRunReq) *shared.ValidateResponse {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateDryRun", arg0, arg1)
	ret0, _ := ret[0].(*shared.ValidateResponse)
	return ret0
}

// ValidateDryRun indicates an expected call of ValidateDryRun.
func (mr *MockDataSourceServiceMockRecorder) ValidateDryRun(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateDryRun", reflect.TypeOf((*MockDataSourceService)(nil).ValidateDryRun), arg0, arg1)
}

// ValidateOriginalTable mocks base method.
func (m *MockDataSourceService) ValidateOriginalTable(arg0 context.Context, arg1 *datasource.ValidateOriginalTableReq) *shared.ValidateResponse {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateOriginalTable", arg0, arg1)
	ret0, _ := ret[0].(*shared.ValidateResponse)
	return ret0
}

// ValidateOriginalTable indicates an expected call of ValidateOriginalTable.
func (mr *MockDataSourceServiceMockRecorder) ValidateOriginalTable(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateOriginalTable", reflect.TypeOf((*MockDataSourceService)(nil).ValidateOriginalTable), arg0, arg1)
}

// ValidateUniqIndex mocks base method.
func (m *MockDataSourceService) ValidateUniqIndex(arg0 context.Context, arg1 *datasource.ValidateUniqIndexReq) *shared.ValidateResponse {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateUniqIndex", arg0, arg1)
	ret0, _ := ret[0].(*shared.ValidateResponse)
	return ret0
}

// ValidateUniqIndex indicates an expected call of ValidateUniqIndex.
func (mr *MockDataSourceServiceMockRecorder) ValidateUniqIndex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateUniqIndex", reflect.TypeOf((*MockDataSourceService)(nil).ValidateUniqIndex), arg0, arg1)
}
