package consts

import (
	"fmt"
	"strings"
)

var RuntimeByteDanceGatewayProtocol ProxyProtocol = `runtime-bytedance-gateway`
var ByteDanceGatewayProtocol ProxyProtocol = `bytedance-gateway`

type ProxyProtocol string

func (p ProxyProtocol) WithSuffix(suffix ...string) ProxyProtocol {
	if len(suffix) == 0 {
		return p
	}
	for i := range suffix {
		if strings.HasPrefix(suffix[i], "-") {
			suffix[i] = strings.TrimPrefix(suffix[i], "-")
		}
		if strings.HasSuffix(suffix[i], "-") {
			suffix[i] = strings.TrimSuffix(suffix[i], "-")
		}
	}
	return ProxyProtocol(fmt.Sprintf("%s-%s", string(p), strings.Join(suffix, "-")))
}

func (p ProxyProtocol) String() string {
	return string(p)
}
