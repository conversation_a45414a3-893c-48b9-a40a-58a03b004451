package consts

import (
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

var (
	TaskNotPaid         = ErrorOf(model.ErrorCode_TaskNotPaid)
	ChargeStatusInvalid = ErrorOf(model.ErrorCode_ChargeStatusInvalid)
	ChargeTypeInvalid   = ErrorOf(model.ErrorCode_ChargeTypeInvalid)
	InstanceOrderExist  = ErrorOf(model.ErrorCode_OrderExist)

	UpgradeJobError = ErrorOf(model.ErrorCode_UpgradeJobError)

	AccountNotRealError   = ErrorOf(model.ErrorCode_AccountNotReal)
	AccountNotBalanceLack = ErrorOf(model.ErrorCode_AccountBalanceLack)
	CreateOrderFailed     = ErrorOf(model.ErrorCode_CreateOrderFailed)
	ProductTypeInvalid    = ErrorOf(model.ErrorCode_ProductTypeInvalid)
)

const (
	ErrCode_KafkaTopicBadStatus        = "-210003"
	ErrCodeTop_KafkaTopicBadStatus     = "IllegalOperationWithTopicStatus"
	ErrCode_KafkaTopicAlreadyExists    = "-220005"
	ErrCodeTop_KafkaTopicAlreadyExists = "TopicAlreadyExist"
	ErrCode_TopicNotExist              = "-230000"
	ErrCodeTop_TopicNotExist           = "TopicNotExist"
)

var KafkaCreateTopicIgnoreErrorCode = []string{
	ErrCode_KafkaTopicAlreadyExists,
	ErrCodeTop_KafkaTopicAlreadyExists,
}

var KafkaDelTopicIgnoreErrorCode = []string{
	ErrCode_TopicNotExist,
	ErrCodeTop_TopicNotExist,
}

func NewThirdPartyErrorBy(rdsCode int32, httpCode int64, msg string, status string, reason string) standardError {
	return standardError{
		code:     rdsCode,
		msg:      msg,
		status:   status,
		httpCode: httpCode,
		reason:   reason,
	}
}

// ThirdPartyError 第三方报错的返回结果的结构体
type ThirdPartyError struct {
	RequestID  string
	Status     ThirdPartyErrorStatus
	Async      bool
	ProductStr string
}

type ThirdPartyErrorStatus struct {
	Code     int32
	Status   string
	Message  string
	HTTPCode int64
}
