package consts

const (
	EnvDBInstanceType                 string = "BDC_DB_INSTANCE_TYPE"
	EnvPrivateMySQLHost               string = "PRIVATE_MYSQL_HOST"
	EnvPrivateMySQLPort               string = "PRIVATE_MYSQL_PORT"
	EnvPrivateMySQLPrivilegedUser     string = "PRIVATE_MYSQL_PRIVILEGED_USERNAME"
	EnvPrivateMySQLPrivilegedPassword string = "PRIVATE_MYSQL_PRIVILEGED_PASSWORD"
	EnvPrivateMySQLUser               string = "MYSQL_USERNAME"
	EnvPrivateMySQLPassword           string = "MYSQL_PASSWORD"
	EnvPrivateZKADDR                  string = "ZK_ADDR"

	EnvBDCMysqlHost = "BDC_MYSQL_DOMAIN"
	EnvBDCMysqlPort = "BDC_MYSQL_PORT"
	EnvBDCZKADDR    = "BDC_ZK_DOMAIN"
	EnvBDCREGION    = "BDC_REGION_ID"
)

const (
	DBInstanceTypeMetardsShared  = "metards_shared"
	DBInstanceTypeMetardsPrivate = "metards_private"
	DBInstanceTypeMysql          = "mysql"
)

const (
	C3PrivateNamespace     = `env.private`
	C3ApplicationNamespace = `application`
)
