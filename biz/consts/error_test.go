package consts

import (
	"fmt"
	"testing"

	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

func Test_ErrorWithParamBySite(t *testing.T) {
	// VolcanoEngineSiteName 场景
	PatchConvey("Test VolcanoEngineSiteName with no params", t, func() {
		Mock(getStandardErrorByErrorCodeCn).Return(&standardError{msg: "Error message"}).Build()
		err := ErrorWithParamBySite("zh", model.ErrorCode(1))
		So(err.GetMessage(), ShouldEqual, "Error message")
	})
	PatchConvey("Test VolcanoEngineSiteName with params", t, func() {
		Mock(getStandardErrorByErrorCodeCn).Return(&standardError{msg: "Error message with %s"}).Build()
		err := ErrorWithParamBySite("zh", model.ErrorCode(1), "param")
		So(err.GetMessage(), ShouldEqual, "Error message with param")
	})
	// BytePlusSiteName 场景
	PatchConvey("Test BytePlusSiteName with no params", t, func() {
		Mock(getStandardErrorByErrorCode).Return(&standardError{msg: "Another error message"}).Build()
		err := ErrorWithParamBySite("en", model.ErrorCode(2))
		So(err.GetMessage(), ShouldEqual, "Another error message")
	})
	PatchConvey("Test BytePlusSiteName with params", t, func() {
		Mock(getStandardErrorByErrorCode).Return(&standardError{msg: "Another error message with %v"}).Build()
		err := ErrorWithParamBySite("en", model.ErrorCode(2), "param")
		So(err.GetMessage(), ShouldEqual, "Another error message with param")
	})
	// Default 场景
	PatchConvey("Test Default with no params", t, func() {
		Mock(getStandardErrorByErrorCode).Return(&standardError{msg: "Default error message"}).Build()
		err := ErrorWithParamBySite("UnknownSite", model.ErrorCode(3))
		So(err.GetMessage(), ShouldEqual, "Default error message")
	})
	PatchConvey("Test Default with params", t, func() {
		Mock(getStandardErrorByErrorCode).Return(&standardError{msg: "Default error message with %s"}).Build()
		err := ErrorWithParamBySite("UnknownSite", model.ErrorCode(3), "param")
		So(err.GetMessage(), ShouldEqual, "Default error message with param")
	})
}

func Test_ErrorBySite(t *testing.T) {
	site := "zh"
	e := model.ErrorCode(1)
	stdErr := &standardError{
		code:     int32(e),
		msg:      "detail.DescEn",
		status:   e.String(),
		httpCode: 200,
	}
	PatchConvey("Test VolcanoEngineSiteName", t, func() {
		Mock(getStandardErrorByErrorCodeCn).Return(stdErr).Build()
		actual := ErrorBySite(site, e)
		So(actual, ShouldNotBeNil)
	})
	site = "BytePlusSiteName"
	PatchConvey("Test BytePlusSiteName", t, func() {
		Mock(getStandardErrorByErrorCode).Return(stdErr).Build()
		actual := ErrorBySite(site, e)
		So(actual, ShouldNotBeNil)
	})
	site = "UnknownSite"
	PatchConvey("Test default case", t, func() {
		Mock(getStandardErrorByErrorCode).Return(stdErr).Build()
		actual := ErrorBySite(site, e)
		So(actual, ShouldNotBeNil)
	})
}

func Test_ErrorWithParam(t *testing.T) {
	PatchConvey("Test Param", t, func() {
		Mock(getStandardErrorByErrorCodeCn).Return(&standardError{msg: "Error message"}).Build()
		//err := ErrorWithParam(model.ErrorCode(1))
		Instance_id := "RDSsfeasefse1111"
		fmt.Print(BuildDBErrorWithParam(model.ErrorCode_CreateSessionError, Instance_id))
		fmt.Print(ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf(" %s Length must be Integer", Instance_id)))

	})

}
