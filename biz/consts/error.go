package consts

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"runtime"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/shared"

	"code.byted.org/infcs/ds-lib/common/log"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/mgr"
)

type ReasonError interface {
	error
	Reason() string
}

type StandardError interface {
	mgr.StandardError
}

func ErrorOf(e model.ErrorCode) StandardError {
	return getStandardErrorByErrorCode(e)
}

func ErrorBySite(site string, e model.ErrorCode) StandardError {
	switch site {
	case "zh":
		return getStandardErrorByErrorCodeCn(e)
	case "en":
		return getStandardErrorByErrorCode(e)
	default:
		return getStandardErrorByErrorCode(e)
	}
}

func getStandardErrorByErrorCode(e model.ErrorCode) *standardError {
	if detail, ok := model.ErrorCodeMap[e]; ok {
		return &standardError{
			code:     int32(e),
			msg:      detail.DescEn,
			status:   e.String(),
			httpCode: detail.HttpErrCode,
		}
	}
	e = model.ErrorCode_SystemError
	detail := model.ErrorCodeMap[e]
	stdErr := &standardError{
		code:     int32(e),
		msg:      detail.DescEn,
		status:   e.String(),
		httpCode: detail.HttpErrCode,
	}
	_, file0, line0, _ := runtime.Caller(1)
	_, file1, line1, _ := runtime.Caller(2)
	log.Warn(context.Background(), "%s --%s:%d;%s:%d", stdErr.Error(), filepath.Base(file0), line0, filepath.Base(file1), line1)
	return stdErr
}

func getStandardErrorByErrorCodeCn(e model.ErrorCode) *standardError {
	if detail, ok := model.ErrorCodeMap[e]; ok {
		return &standardError{
			code:     int32(e),
			msg:      detail.DescCN,
			status:   e.String(),
			httpCode: detail.HttpErrCode,
		}
	}
	e = model.ErrorCode_SystemError
	detail := model.ErrorCodeMap[e]
	stdErr := &standardError{
		code:     int32(e),
		msg:      detail.DescCN,
		status:   e.String(),
		httpCode: detail.HttpErrCode,
	}
	_, file0, line0, _ := runtime.Caller(1)
	_, file1, line1, _ := runtime.Caller(2)
	log.Warn(context.Background(), "%s --%s:%d;%s:%d", stdErr.Error(), filepath.Base(file0), line0, filepath.Base(file1), line1)
	return stdErr
}

func ErrorWithParam(e model.ErrorCode, params ...interface{}) StandardError {
	err := getStandardErrorByErrorCode(e)
	if len(params) > 0 && (strings.Contains(err.msg, "%s") || strings.Contains(err.msg, "%v")) {
		err.msg = fmt.Sprintf(err.msg, params...)
	} else {
		if err.msg != "" {
			err.msg += " "
		}
		err.msg += fmt.Sprint(params...)
	}
	return err
}

func BuildDBErrorWithParam(e model.ErrorCode, ds string, params ...interface{}) StandardError {
	errFrom := fmt.Sprintf("DBErr:")
	errSolution := fmt.Sprintf("Please check the datasource %s and retry.", ds)

	err := getStandardErrorByErrorCode(e)
	if len(params) > 0 && (strings.Contains(err.msg, "%s") || strings.Contains(err.msg, "%v")) {
		err.msg = fmt.Sprintf(err.msg, params...)
	} else {
		if err.msg != "" {
			err.msg += " "
		}
		err.msg += fmt.Sprint(params...)
	}
	err.msg = fmt.Sprintf("%s %s, %s", errFrom, err.msg, errSolution)
	return err
}

func ErrorWithParamBySite(site string, e model.ErrorCode, params ...interface{}) StandardError {
	var err *standardError
	switch site {
	case "zh":
		err = getStandardErrorByErrorCodeCn(e)
	case "en":
		err = getStandardErrorByErrorCode(e)
	default:
		err = getStandardErrorByErrorCode(e)
	}
	if len(params) > 0 && (strings.Contains(err.msg, "%s") || strings.Contains(err.msg, "%v")) {
		err.msg = fmt.Sprintf(err.msg, params...)
	}
	return err
}

// ErrorWithParamAndSolution 转换为业务错误码 需要包含：e错误码 param错误附加提示，solution解决方案
func ErrorWithParamAndSolution(e model.ErrorCode, param string, solution model.ErrorSolution) StandardError {
	err := getStandardErrorByErrorCode(e)
	if param != "" && (strings.Contains(err.msg, "%s") || strings.Contains(err.msg, "%v")) {
		err.msg = fmt.Sprintf(err.msg, param)
	}

	solut, ok := model.ErrorSolutionMap[solution]
	if solution != model.ErrorSolution_None && ok {
		err.msg = err.msg + fmt.Sprintf(", solution：%s", solut.DescEn)
	}
	return err
}

func BuildThirdPartyStandardError(errStr string) error {
	if errStr == "" {
		return nil
	}
	e := strings.Split(errStr, ",")
	if len(e) < 3 {
		return errors.New(errStr)
	}
	if e[0] != "[Mgr] Biz error" {
		//这里面再做二次判断，错误返回的开头是rpc timeout，说明是接口调用超时后在TOP层的报错，则转换报错封装信息
		if strings.Contains(e[0], "rpc timeout") {
			detail := model.ErrorCodeMap[model.ErrorCode_CallThirdPartyTimeout]
			return standardError{
				code:     int32(model.ErrorCode_CallThirdPartyTimeout),
				msg:      model.ErrorCode_CallThirdPartyTimeout.String(),
				status:   detail.DescEn,
				httpCode: detail.HttpErrCode,
			}
		}

		return errors.New(errStr)
	}
	j := strings.Split(errStr, "mgr resp=")
	if len(j) != 2 {
		return errors.New(errStr)
	}
	js := strings.ReplaceAll(j[1], "`", "")
	rdsErr := &ThirdPartyError{}
	jsonErr := json.Unmarshal([]byte(js), rdsErr)
	if jsonErr != nil {
		return errors.New(errStr)
	}

	return standardError{
		code:     rdsErr.Status.Code,
		msg:      "[" + rdsErr.ProductStr + "]" + rdsErr.Status.Message,
		status:   rdsErr.Status.Status,
		httpCode: rdsErr.Status.HTTPCode,
	}
}

// TranslateStandardErrorToShared 应对Actor转换，将业务Error转换为bp模型下的Error
func TranslateStandardErrorToShared(err error) *shared.StandardErr {
	shardErr := &shared.StandardErr{}
	if stderr, ok := err.(StandardError); ok {
		shardErr.Code = stderr.GetCode()
		shardErr.Status = stderr.GetStatus()
		shardErr.Msg = stderr.GetMessage()
		shardErr.HttpCode = shardErr.GetHttpCode()
	} else {
		detail := model.ErrorCodeMap[model.ErrorCode_InternalError]
		shardErr.Code = int32(model.ErrorCode_InternalError)
		shardErr.Status = model.ErrorCode_InternalError.String()
		shardErr.Msg = detail.DescEn
		shardErr.HttpCode = detail.HttpErrCode
	}

	return shardErr
}

// TranslateSharedToStandardError 应对Actor转换，将bp模型下的Error转换为业务Error
func TranslateSharedToStandardError(err *shared.StandardErr) StandardError {
	if err == nil || err.Code == 0 {
		detail := model.ErrorCodeMap[model.ErrorCode_InternalError]
		return &standardError{
			code:     int32(model.ErrorCode_InternalError),
			msg:      detail.DescEn,
			status:   model.ErrorCode_InternalError.String(),
			httpCode: detail.HttpErrCode,
		}
	}

	return standardError{
		code:     err.Code,
		msg:      err.Msg,
		status:   err.Status,
		httpCode: err.HttpCode,
	}
}

type standardError struct {
	code     int32
	msg      string
	status   string
	httpCode int64
	detail   string
	reason   string
}

func (se standardError) GetCode() int32     { return se.code }
func (se standardError) GetMessage() string { return se.msg }
func (se standardError) GetStatus() string  { return se.status }
func (se standardError) GetHTTPCode() int64 { return se.httpCode }
func (se standardError) Error() string {
	return fmt.Sprintf(`code=%v status=%v msg=%v %v`, se.code, se.status, se.msg, se.detail)
}
