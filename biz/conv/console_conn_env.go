package conv

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"github.com/qjpcpu/fp"
)

func ConsoleConnEnvEntityToDao(env *entity.ConsoleConnEnv) *dao.ConsoleConnEnv {
	envDao := &dao.ConsoleConnEnv{
		ID:           env.ID,
		InstanceId:   env.InstanceId,
		InstanceType: env.InstanceType.String(),
		AccountName:  env.AccountName,
		Env:          env.Env,
	}
	if env.Password != "" {
		envDao.Password = utils.EncryptData(env.Password, env.InstanceId)
	}
	return envDao
}

func ConsoleConnEnvDaoToEntity(env *dao.ConsoleConnEnv) *entity.ConsoleConnEnv {
	instanceType, _ := model.InstanceTypeFromString(env.InstanceType)
	envEntity := &entity.ConsoleConnEnv{
		ID:           env.ID,
		InstanceId:   env.InstanceId,
		InstanceType: instanceType,
		AccountName:  env.AccountName,
		CreateTime:   env.CreateTime,
		UpdateTime:   env.UpdateTime,
		Env:          env.Env,
	}
	if env.Password != "" {
		envEntity.Password = utils.DecryptData(env.Password, env.InstanceId)
	}
	return envEntity
}

func ConsoleConnEnvDaoToEntities(envs []*dao.ConsoleConnEnv) (ret []*entity.ConsoleConnEnv) {
	_ = fp.StreamOf(envs).Map(ConsoleConnEnvDaoToEntity).ToSlice(&ret)
	return
}
