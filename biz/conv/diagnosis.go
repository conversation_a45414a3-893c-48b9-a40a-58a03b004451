package conv

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"errors"
)

func ConvertTableStatAutoIncr(input []*shared.TableStatAutoIncr) []*model.TableStatAutoIncr {
	result := make([]*model.TableStatAutoIncr, len(input))
	for i, item := range input {
		result[i] = &model.TableStatAutoIncr{
			DBName:             item.DBName,
			TableName:          item.TableName,
			ColumnName:         item.ColumnName,
			AutoIncrementRatio: item.AutoIncrementRatio,
			AutoIncrement:      item.AutoIncrement,
			DataType:           item.DataType,
			MaxValue:           item.MaxValue,
		}
	}
	return result
}

func CnvInspectionMetricToDiagItem(in model.InspectionMetric) (model.DiagItem, error) {
	switch in {
	case model.InspectionMetric_SlowLog:
		return model.DiagItem_SlowLog, nil
	case model.InspectionMetric_CpuUsage:
		return model.DiagItem_CpuUsage, nil
	case model.InspectionMetric_MemUsage:
		return model.DiagItem_MemUsage, nil
	case model.InspectionMetric_SpaceUsage:
		return model.DiagItem_SpaceUsage, nil
	case model.InspectionMetric_SessionUsage:
		return model.DiagItem_SessionUsage, nil
	default:
		return model.DiagItem_SlowLog, errors.New("unknown diag item")
	}
}
