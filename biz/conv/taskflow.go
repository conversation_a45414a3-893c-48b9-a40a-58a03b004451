package conv

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	etf "code.byted.org/infcs/dbw-mgr/biz/entity/taskflow"
	dbwutils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/qjpcpu/fp"
	"strconv"
)

func TaskFlowEntityToDao(tf *etf.TaskFlow) (*dao.TaskFlow, *dao.TaskFlowConfig) {
	taskFlow := &dao.TaskFlow{
		ID:               tf.ID,
		Name:             tf.Name,
		TaskType:         tf.TaskType.String(),
		InstanceId:       tf.InstanceId,
		InstanceType:     tf.InstanceType.String(),
		ExecuteMethod:    tf.Config.ExecuteMethod.String(),
		CreateTime:       tf.CreateTime,
		Statement:        tf.Statement,
		Comment:          tf.Comment,
		LastExecuteState: tf.LastExecuteState.String(),
		LastExecuteTime:  tf.LastExecuteTime,
		FailedTimes:      tf.FailedTimes,
		ExecutedTimes:    tf.ExecutedTimes,
		State:            tf.State.String(),
	}
	config := &dao.TaskFlowConfig{
		ID:                 tf.Config.ID,
		TaskFlowID:         taskFlow.ID,
		UserName:           tf.Config.UserName,
		Password:           dbwutils.EncryptData(tf.Config.Password, tf.InstanceId),
		Database:           tf.Config.Database,
		StartTime:          tf.Config.StartTime,
		EndTime:            tf.Config.EndTime,
		Interval:           tf.Config.Interval,
		EnableTransaction:  tf.Config.EnableTransaction,
		IgnoreError:        tf.Config.IgnoreError,
		StillRunningPolicy: int(tf.Config.StillRunningPolicy),
		ExecuteTime:        tf.Config.ExecuteTime,
	}
	if taskFlow.ExecuteMethod == model.ExecuteMethod_Cycle.String() {
		config.IntervalUnit = tf.Config.IntervalUnit.String()
	}
	return taskFlow, config
}

func TaskFlowDaoToEntity(tf *dao.TaskFlow, tfc *dao.TaskFlowConfig) *etf.TaskFlow {
	taskType, _ := model.TaskTypeFromString(tf.TaskType)
	state, _ := model.StateFromString(tf.State)
	instanceType, _ := model.InstanceTypeFromString(tf.InstanceType)
	lastExecuteState, _ := model.JobStateFromString(tf.LastExecuteState)
	taskFlow := &etf.TaskFlow{
		ID:               tf.ID,
		Name:             tf.Name,
		TaskType:         taskType,
		InstanceId:       tf.InstanceId,
		InstanceType:     instanceType,
		Statement:        tf.Statement,
		CreateTime:       tf.CreateTime,
		TenantID:         tf.TenantID,
		UserID:           tf.Creator,
		Comment:          tf.Comment,
		LastExecuteState: lastExecuteState,
		FailedTimes:      tf.FailedTimes,
		ExecutedTimes:    tf.ExecutedTimes,
		State:            state,
	}
	taskFlow.Config = &etf.Config{
		ID:                 tfc.ID,
		ExecuteTime:        tfc.ExecuteTime,
		UserName:           tfc.UserName,
		Password:           dbwutils.DecryptData(tfc.Password, tf.InstanceId),
		Database:           tfc.Database,
		StartTime:          tfc.StartTime,
		EndTime:            tfc.EndTime,
		Interval:           tfc.Interval,
		StillRunningPolicy: model.StillRunningPolicy(tfc.StillRunningPolicy),
		EnableTransaction:  tfc.EnableTransaction,
		IgnoreError:        tfc.IgnoreError,
	}
	executeMethod, _ := model.ExecuteMethodFromString(tf.ExecuteMethod)
	intervalUnit, _ := model.IntervalUnitFromString(tfc.IntervalUnit)
	taskFlow.Config.ExecuteMethod = executeMethod
	taskFlow.Config.IntervalUnit = intervalUnit
	return taskFlow
}

func TaskFlowDaoToEntityWithoutConfig(tf *dao.TaskFlow) *etf.TaskFlow {
	taskType, _ := model.TaskTypeFromString(tf.TaskType)
	state, _ := model.StateFromString(tf.State)
	lastExecuteState, _ := model.JobStateFromString(tf.LastExecuteState)
	instanceType, _ := model.InstanceTypeFromString(tf.InstanceType)
	taskFlow := &etf.TaskFlow{
		ID:               tf.ID,
		Name:             tf.Name,
		TaskType:         taskType,
		InstanceId:       tf.InstanceId,
		InstanceType:     instanceType,
		Statement:        tf.Statement,
		CreateTime:       tf.CreateTime,
		TenantID:         tf.TenantID,
		UserID:           tf.Creator,
		Comment:          tf.Comment,
		State:            state,
		LastExecuteState: lastExecuteState,
		LastExecuteTime:  tf.LastExecuteTime,
		ExecutedTimes:    tf.ExecutedTimes,
		FailedTimes:      tf.FailedTimes,
	}
	return taskFlow
}
func TaskFlowToEntities(tfs []*dao.TaskFlow) (ret []*etf.TaskFlow) {
	_ = fp.StreamOf(tfs).Map(TaskFlowDaoToEntityWithoutConfig).ToSlice(&ret)
	return
}

func TaskFlowEntityToModel(tf *etf.TaskFlow) *model.TaskFlow {
	ret := &model.TaskFlow{
		ID:               tf.ID,
		Name:             tf.Name,
		TaskType:         tf.TaskType,
		InstanceId:       tf.InstanceId,
		InstanceType:     tf.InstanceType,
		CreateTime:       strconv.FormatInt(tf.CreateTime, 10),
		LastExecuteTime:  strconv.FormatInt(tf.LastExecuteTime, 10),
		LastExecuteState: tf.LastExecuteState,
		Statement:        tf.Statement,
		State:            tf.State,
		ExecutedTimes:    strconv.FormatInt(tf.ExecutedTimes, 10),
		FailedTimes:      strconv.FormatInt(tf.FailedTimes, 10),
		Comment:          tf.Comment,
	}
	if tf.Config != nil {
		if tf.Config.ExecuteMethod == model.ExecuteMethod_Cycle {
			cycleConfig := &model.CycleConfig{
				StartTime:    strconv.FormatInt(*tf.Config.StartTime, 10),
				Interval:     int8(tf.Config.Interval),
				IntervalUnit: tf.Config.IntervalUnit,
			}
			if tf.Config.EndTime != nil {
				cycleConfig.EndTime = utils.StringRef(strconv.FormatInt(*tf.Config.EndTime, 10))
			}
			cycleConfig.StillRunningPolicy = model.StillRunningPolicy(tf.Config.StillRunningPolicy)
			ret.CycleConfig = cycleConfig
		}
		instanceConfig := &model.InstanceConfig{
			UserName: utils.StringRef(tf.Config.UserName),
			Password: utils.StringRef("************"),
			Database: tf.Config.Database,
		}
		ret.InstanceConfig = instanceConfig
		ret.EnableTransaction = tf.Config.EnableTransaction
		ret.IgnoreError = tf.Config.IgnoreError
		ret.ExecuteTime = strconv.FormatInt(tf.Config.ExecuteTime, 10)
	}
	return ret
}

func TaskFlowEntitiesToModels(tfs []*etf.TaskFlow) (ret []*model.TaskFlow) {
	_ = fp.StreamOf(tfs).Map(TaskFlowEntityToModel).ToSlice(&ret)
	return
}

func TaskFlowJobDaoToEntity(j *dao.TaskFlowJob) *etf.Job {
	status, _ := model.JobStateFromString(j.Status)
	return &etf.Job{
		ID:         j.ID,
		TaskFlowID: j.TaskFlowID,
		StartTime:  j.StartTime,
		EndTime:    j.EndTime,
		Status:     status,
		Reason:     j.Reason,
	}
}

func TaskFlowJobDaoToEntities(jobs []*dao.TaskFlowJob) (ret []*etf.Job) {
	_ = fp.StreamOf(jobs).Map(TaskFlowJobDaoToEntity).ToSlice(&ret)
	return
}

func TaskFlowJobEntityToDao(j *etf.Job) *dao.TaskFlowJob {
	return &dao.TaskFlowJob{
		ID:         j.ID,
		TaskFlowID: j.TaskFlowID,
		StartTime:  j.StartTime,
		EndTime:    j.EndTime,
		Status:     j.Status.String(),
		Reason:     j.Reason,
	}
}

func TaskFlowJobToModel(j *etf.Job) *model.ExecuteRecord {
	return &model.ExecuteRecord{
		RecordID:  j.ID,
		StartTime: strconv.FormatInt(j.StartTime, 10),
		EndTime:   utils.StringRef(strconv.FormatInt(j.EndTime, 10)),
		State:     j.Status,
		Reason:    j.Reason,
	}
}

func TaskFlowJobsToModel(jobs []*etf.Job) (ret []*model.ExecuteRecord) {
	_ = fp.StreamOf(jobs).Map(TaskFlowJobToModel).ToSlice(&ret)
	return
}
