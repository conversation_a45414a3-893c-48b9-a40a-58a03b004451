package conv

import "time"

func TimestampMSToDateTimeString(timestamp int64) string {
	tm := time.Unix(timestamp/1000, (timestamp%1000)*int64(time.Millisecond))
	localDateTime := tm.In(time.Now().Location()).Format("2006-01-02 15:04:05")
	return localDateTime
}

func TimestampSToDateTimeString(timestamp int64) string {
	tm := time.Unix(timestamp, (timestamp)*int64(time.Second))
	localDateTime := tm.In(time.Now().Location()).Format("2006-01-02 15:04:05")
	return localDateTime
}
