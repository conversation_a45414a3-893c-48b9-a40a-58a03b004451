package conv

import (
	"testing"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

func TestGetDsTypeByShardInstanceId(t *testing.T) {
	type args struct {
		shardInstanceId string
	}
	tests := []struct {
		name string
		args args
		want model.DSType
	}{
		// TODO: Add test cases.
		{
			"vedb",
			args{
				shardInstanceId: "vedbm-123",
			},
			model.DSType_VeDBMySQL,
		},
		{
			"rds",
			args{
				shardInstanceId: "mysql-123",
			},
			model.DSType_MySQL,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetDsTypeByShardInstanceId(tt.args.shardInstanceId); got != tt.want {
				t.Errorf("GetDsTypeByShardInstanceId() = %v, want %v", got, tt.want)
			}
		})
	}
}
