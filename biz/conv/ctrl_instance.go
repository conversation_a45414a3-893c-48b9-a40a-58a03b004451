package conv

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity/instance"
)

func CtrlInstanceEntityToDao(ins *instance.CtrlInstance) *dao.DbwInstance {
	return &dao.DbwInstance{
		ID:                   ins.ID,
		InstanceId:           ins.InstanceId,
		InstanceType:         ins.InstanceType,
		TenantId:             ins.TenantId,
		Region:               ins.Region,
		Source:               ins.Source.String(),
		DatabaseUser:         ins.DatabaseUser,
		DatabasePassword:     ins.DatabasePassword,
		OwnerUid:             ins.OwnerUid,
		DbaUid:               ins.DbaUid,
		WorkflowTemplateId:   ins.WorkflowTemplateId,
		ApprovalFlowConfigId: ins.ApprovalFlowConfigId,
		SecurityGroupId:      ins.SecurityGroupId,
		CreatedAt:            ins.CreatedAt,
		UpdatedAt:            ins.UpdatedAt,
	}
}

func CtrlInstanceDaoToEntity(ins *dao.DbwInstance) *instance.CtrlInstance {
	return &instance.CtrlInstance{
		ID:                   ins.ID,
		InstanceId:           ins.InstanceId,
		InstanceType:         ins.InstanceType,
		TenantId:             ins.TenantId,
		Region:               ins.Region,
		DatabaseUser:         ins.DatabaseUser,
		DatabasePassword:     ins.DatabasePassword,
		OwnerUid:             ins.OwnerUid,
		DbaUid:               ins.DbaUid,
		WorkflowTemplateId:   ins.WorkflowTemplateId,
		ApprovalFlowConfigId: ins.ApprovalFlowConfigId,
		SecurityGroupId:      ins.SecurityGroupId,
		CreatedAt:            ins.CreatedAt,
		UpdatedAt:            ins.UpdatedAt,
	}
}
