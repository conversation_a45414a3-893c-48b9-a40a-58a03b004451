package conv

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"reflect"
	"testing"
)

func TestInspectionReportEntityToBasicInfoModel(t *testing.T) {
	type args struct {
		c *entity.InspectionReport
	}
	tests := []struct {
		name string
		args args
		want *model.InspectionInstanceBasicInfo
	}{
		{
			name: "test1",
			args: args{c: &entity.InspectionReport{
				InstanceId: "1",
			}},
			want: &model.InspectionInstanceBasicInfo{
				InstanceId: "1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionReportEntityToBasicInfoModel(tt.args.c); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("InspectionReportEntityToBasicInfoModel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionReportEntityToConnectionInfoModel(t *testing.T) {
	type args struct {
		c *entity.InspectionReport
	}
	tests := []struct {
		name string
		args args
		want *model.InspectionConnectInfo
	}{
		{
			name: "test1",
			args: args{c: &entity.InspectionReport{
				InstanceId: "1",
			}},
			want: &model.InspectionConnectInfo{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionReportEntityToConnectionInfoModel(tt.args.c); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InspectionReportEntityToConnectionInfoModel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionReportEntityToResourceUsageModel(t *testing.T) {
	type args struct {
		c *entity.InspectionReport
	}
	tests := []struct {
		name string
		args args
		want []*model.InspectionInstanceResourceUsage
	}{
		{
			name: "test1",
			args: args{c: &entity.InspectionReport{
				InstanceId: "1",
			}},
			want: []*model.InspectionInstanceResourceUsage{
				{Name: model.InspectionMetricName_CpuUsage, Unit: "%"},
				{Name: model.InspectionMetricName_MemUsage, Unit: "%"},
				{Name: model.InspectionMetricName_DiskUsage, Unit: "%"},
				{Name: model.InspectionMetricName_ConnUsage, Unit: "%"}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionReportEntityToResourceUsageModel(tt.args.c); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InspectionReportEntityToResourceUsageModel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionSlowLogEntityToSlowLogModels(t *testing.T) {
	type args struct {
		c *entity.InspectionSlowLog
	}
	tests := []struct {
		name string
		args args
		want *model.SlowLog
	}{
		{
			name: "test1",
			args: args{c: &entity.InspectionSlowLog{
				InstanceId: "1",
			}},
			want: &model.SlowLog{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionSlowLogEntityToSlowLogModels(tt.args.c); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InspectionSlowLogEntityToSlowLogModels() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionSlowLogsEntityToSlowLogModels(t *testing.T) {
	type args struct {
		c []*entity.InspectionSlowLog
	}
	tests := []struct {
		name string
		args args
		want []*model.SlowLog
	}{
		{
			name: "test1",
			args: args{c: []*entity.InspectionSlowLog{
				{InstanceId: "1"},
			}},
			want: []*model.SlowLog{{}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionSlowLogsEntityToSlowLogModels(tt.args.c); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InspectionSlowLogsEntityToSlowLogModels() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionTaskInfoEntityToModel(t *testing.T) {
	type args struct {
		c *entity.InspectionTask
	}
	tests := []struct {
		name string
		args args
		want *model.InspectionItem
	}{
		{
			name: "test1",
			args: args{c: &entity.InspectionTask{
				InstanceId: "1",
			}},
			want: &model.InspectionItem{InstanceId: "1"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionTaskInfoEntityToModel(tt.args.c); reflect.DeepEqual(got, tt.want) {
				t.Errorf("InspectionTaskInfoEntityToModel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInspectionTaskInfosEntityToModels(t *testing.T) {
	type args struct {
		c []*entity.InspectionTask
	}
	tests := []struct {
		name string
		args args
		want []*model.InspectionItem
	}{
		{
			name: "test1",
			args: args{c: []*entity.InspectionTask{
				{InstanceId: "1"},
			}},
			want: []*model.InspectionItem{{InstanceId: "1"}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InspectionTaskInfosEntityToModels(tt.args.c); reflect.DeepEqual(got, tt.want) {
				t.Errorf("InspectionTaskInfosEntityToModels() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInstanceInfoModelToShare(t *testing.T) {
	type args struct {
		modelInfo []*model.InspectionInstanceInfo
	}
	tests := []struct {
		name string
		args args
		want []*shared.InspectionInstanceInfo
	}{
		{
			name: "test1",
			args: args{modelInfo: []*model.InspectionInstanceInfo{
				{InstanceId: "1"},
			}},
			want: []*shared.InspectionInstanceInfo{{InstanceId: "1", InstanceType: shared.MySQL}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := InstanceInfoModelToShare(model.InstanceType_MySQL, tt.args.modelInfo); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InstanceInfoModelToShare() = %v, want %v", got, tt.want)
			}
		})
	}
}
