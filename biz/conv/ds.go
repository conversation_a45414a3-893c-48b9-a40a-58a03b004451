package conv

import (
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

func ToSharedDataSource(cnf *config.Config, ds *model.DataSource) *shared.DataSource {
	if ds == nil {
		return nil
	}
	sharedDS := &shared.DataSource{
		Type:             ToSharedType(ds.Type),
		LinkType:         ToSharedLinkType(ds.LinkType),
		Address:          ds.GetAddress(),
		Db:               ds.GetDBName(),
		User:             ds.GetUsername(),
		Password:         ds.GetPassword(),
		ExtraDsn:         ds.ExtraDsn,
		ConnectTimeoutMs: cnf.ConnectionConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.ConnectionReadTimeout * 1000,
		WriteTimeoutMs:   cnf.ConnectionWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.ConnectionIdleTimeout * 1000,
		InstanceId:       ds.GetInstanceId(),
		VpcID:            ds.GetVpcId(),
		Region:           ds.GetRegionId(),
		MongoNodeId:      ds.GetMongoNodeId(),
		NodeId:           ds.GetNodeId(),
	}
	if ds.GetRegionId() == "sg1" {
		sharedDS.Region = "alisg"
	}
	return sharedDS
}

func ToSharedType(typ model.DSType) shared.DataSourceType {
	switch typ {
	case model.DSType_MySQL:
		return shared.MySQL
	case model.DSType_Postgres:
		return shared.Postgres
	case model.DSType_Mongo:
		return shared.Mongo
	case model.DSType_Redis:
		return shared.Redis
	case model.DSType_VeDBMySQL:
		return shared.VeDBMySQL
	case model.DSType_MetaRDS:
		return shared.MetaRDS
	case model.DSType_MSSQL:
		return shared.MSSQL
	case model.DSType_ByteRDS:
		return shared.ByteRDS
	case model.DSType_MySQLSharding:
		return shared.MySQLSharding
	case model.DSType_MetaMySQL:
		return shared.MetaMySQL
	case model.DSType_ByteDoc:
		return shared.ByteDoc
	}
	return shared.DataSourceType(0)
}

// GetDsTypeByShardInstanceId 获取shard实例底层是vedb还是rds，仅用于慢日志逻辑中
func GetDsTypeByShardInstanceId(shardInstanceId string) model.DSType {
	if strings.HasPrefix(shardInstanceId, "vedbm-") {
		return model.DSType_VeDBMySQL
	}
	return model.DSType_MySQL
}

func ToSharedTypeV2(typ model.InstanceType) shared.DataSourceType {
	switch typ {
	case model.InstanceType_MySQL:
		return shared.MySQL
	case model.InstanceType_Postgres:
		return shared.Postgres
	case model.InstanceType_Mongo:
		return shared.Mongo
	case model.InstanceType_Redis:
		return shared.Redis
	case model.InstanceType_VeDBMySQL:
		return shared.VeDBMySQL
	case model.InstanceType_MetaRDS:
		return shared.MetaRDS
	case model.InstanceType_MSSQL:
		return shared.MSSQL
	case model.InstanceType_MySQLSharding:
		return shared.MySQLSharding
	case model.InstanceType_MetaMySQL:
		return shared.MetaMySQL
	case model.InstanceType_ByteRDS:
		return shared.ByteRDS
	case model.InstanceType_ByteDoc:
		return shared.ByteDoc
	}
	return shared.DataSourceType(0)
}

func TosharedLinkType(typ model.LinkType) shared.LinkType {
	switch typ {
	case model.LinkType_Volc:
		return shared.Volc
	case model.LinkType_ECS:
		return shared.Ecs
	case model.LinkType_Public:
		return shared.Public
	}
	return shared.LinkType(0)
}
func ToModelType(typ shared.DataSourceType) model.DSType {
	switch typ {
	case shared.MySQL:
		return model.DSType_MySQL
	case shared.Postgres:
		return model.DSType_Postgres
	case shared.Mongo:
		return model.DSType_Mongo
	case shared.Redis:
		return model.DSType_Redis
	case shared.VeDBMySQL:
		return model.DSType_VeDBMySQL
	case shared.MySQLSharding:
		return model.DSType_MySQLSharding
	case shared.MSSQL:
		return model.DSType_MSSQL
	case shared.MetaMySQL:
		return model.DSType_MetaMySQL
	case shared.ByteRDS:
		return model.DSType_ByteRDS
	}
	return model.DSType(0)
}

func ToSharedLinkType(typ model.LinkType) shared.LinkType {
	switch typ {
	case model.LinkType_Volc:
		return shared.Volc
	case model.LinkType_ECS:
		return shared.Ecs
	case model.LinkType_Public:
		return shared.Public
	}
	return shared.LinkType(0)
}

func ToModelLinkType(typ shared.LinkType) model.LinkType {
	switch typ {
	case shared.Volc:
		return model.LinkType_Volc
	case shared.Ecs:
		return model.LinkType_ECS
	case shared.Public:
		return model.LinkType_Public
	case shared.ByteCloud:
		return model.LinkType_ByteInner
	}
	return model.LinkType(1)
}

func ToSharedDownloadTaskStatusType(typ string) shared.DownloadTaskStatus {
	switch typ {
	case "creating":
		return shared.Creating
	case "created_cut":
		return shared.CreatedCut
	case "success":
		return shared.Success
	case "wait":
		return shared.Wait
	case "fail":
		return shared.Fail
	}
	return shared.DownloadTaskStatus(0)
}

func ToModelDownloadTaskStatusType(typ shared.DownloadTaskStatus) model.DownloadTaskStatus {
	switch typ {
	case shared.Creating:
		return model.DownloadTaskStatus_Creating
	case shared.CreatedCut:
		return model.DownloadTaskStatus_CreatedCut
	case shared.Success:
		return model.DownloadTaskStatus_Success
	case shared.Wait:
		return model.DownloadTaskStatus_Wait
	case shared.Fail:
		return model.DownloadTaskStatus_Fail
	}
	return model.DownloadTaskStatus(0)
}

func ToShardAction(action model.QueryAction) shared.Action {
	switch action {
	case model.QueryAction_Generate:
		return shared.Generate
	}
	return shared.Action(0)
}
