package conv

import (
	"fmt"
	"strconv"

	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/qjpcpu/fp"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

func CommandEntityToModels(c []*entity.Command) (ret []*model.CommandItem) {
	fp.StreamOf(c).Map(CommandEntityToModel).ToSlice(&ret)
	return
}

func CommandEntityToModel(c *entity.Command) *model.CommandItem {
	if c == nil {
		return nil
	}
	var st *model.CommandState
	switch c.State {
	case entity.CommandPending:
		st = model.CommandStatePtr(model.CommandState_Pending)
	case entity.CommandExecuting:
		st = model.CommandStatePtr(model.CommandState_Executing)
	case entity.CommandTerminated:
		st = model.CommandStatePtr(model.CommandState_Terminated)
	}
	var r *model.CommandTerminatedReason
	if c.Reason != nil {
		switch *c.Reason {
		case entity.CommandTerminated_Success:
			r = model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Success)
		case entity.CommandTerminated_Failed:
			r = model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Failed)
		case entity.CommandTerminated_Cancel:
			r = model.CommandTerminatedReasonPtr(model.CommandTerminatedReason_Cancel)
		}
	}
	var stm, etm *int64
	if c.StartTimeMS > 0 {
		stm = utils.Int64Ref(c.StartTimeMS)
	}
	if c.EndTimeMS > 0 {
		etm = utils.Int64Ref(c.EndTimeMS)
	}
	var why *string
	if c.ReasonDetail != "" {
		why = utils.StringRef(c.ReasonDetail)
	}
	item := &model.CommandItem{
		CommandStr:   utils.StringRef(c.Content),
		CommandId:    utils.StringRef(c.ID),
		State:        st,
		Reason:       r,
		StartTime:    stm,
		EndTime:      etm,
		ReasonDetail: why,
		Extra:        make(map[string]string),
	}
	for k, v := range c.Extra {
		key := fmt.Sprintf("%v", k)
		val := fmt.Sprintf("%v", v)
		item.Extra[key] = val
	}
	return item
}

func CommandSetToModel(cs *entity.CommandSet) *model.DescribeCommandSetResp {
	if cs == nil {
		return nil
	}
	var stm, etm *int64
	if cs.StartTimeMS > 0 {
		stm = utils.Int64Ref(cs.StartTimeMS)
	}
	if cs.EndTimeMS > 0 {
		etm = utils.Int64Ref(cs.EndTimeMS)
	}
	return &model.DescribeCommandSetResp{
		StartTime: stm,
		EndTime:   etm,
		Progress:  utils.Int32Ref(int32(cs.Progress)),
		Content:   utils.StringRef(cs.Content),
		Commands:  CommandEntityToModels(cs.Commands),
	}
}

func CommandSetDaoToEntity(cs *dao.CommandSet, cmds []*dao.Command) *entity.CommandSet {
	return &entity.CommandSet{
		ID:           strconv.FormatInt(cs.ID, 10),
		SessionID:    strconv.FormatInt(cs.SessionID, 10),
		ConnectionID: strconv.FormatInt(cs.ConnectionID, 10),
		CreateTimeMS: cs.CreateTimeMS,
		StartTimeMS:  cs.StartTimeMS,
		EndTimeMS:    cs.EndTimeMS,
		Progress:     int64(cs.Progress),
		Content:      cs.Content,
		Commands:     CommandDaoToEntities(cmds),
		TenantID:     cs.TenantID,
	}
}

func CommandSetsToEntityWithoutCommands(cs *dao.CommandSet) *entity.CommandSet {
	return &entity.CommandSet{
		ID:           strconv.FormatInt(cs.ID, 10),
		SessionID:    strconv.FormatInt(cs.SessionID, 10),
		ConnectionID: strconv.FormatInt(cs.ConnectionID, 10),
		CreateTimeMS: cs.CreateTimeMS,
		StartTimeMS:  cs.StartTimeMS,
		EndTimeMS:    cs.EndTimeMS,
		Progress:     int64(cs.Progress),
		Content:      cs.Content,
	}
}
func CommandSetDaoToEntities(css []*dao.CommandSet) (ret []*entity.CommandSet, err error) {
	if err = fp.StreamOf(css).Map(CommandSetsToEntityWithoutCommands).ToSlice(&ret); err != nil {
		return
	}
	return
}

func CommandDaoToEntity(cmd *dao.Command) *entity.Command {
	var reason *entity.CommandTerminatedReason
	if cmd.Reason != nil {
		reason = entity.CommandTerminatedReasonPtr(entity.CommandTerminatedReason(*cmd.Reason))
	}
	var rt *entity.ResultType
	if cmd.ResultType != nil {
		v := entity.ResultType(*cmd.ResultType)
		rt = &v
	}
	var rd string
	if cmd.ReasonDetail != nil {
		rd = *cmd.ReasonDetail
	}
	var hder []string
	if cmd.Header != nil {
		utils.MustUnmarshal([]byte(*cmd.Header), &hder)
	}
	return &entity.Command{
		ID:           strconv.FormatInt(cmd.ID, 10),
		CommandSetID: strconv.FormatInt(cmd.CommandSetID, 10),
		State:        entity.CommandState(cmd.State),
		Reason:       reason,
		Content:      cmd.Content,
		StartTimeMS:  cmd.StartTimeMS,
		EndTimeMS:    cmd.EndTimeMS,
		ResultType:   rt,
		Header:       hder,
		ReasonDetail: rd,
		Extra:        cmd.Extra,
		TenantID:     cmd.TenantID,
	}
}

func CommandDaoToEntities(cmd []*dao.Command) (ret []*entity.Command) {
	fp.StreamOf(cmd).Map(CommandDaoToEntity).ToSlice(&ret)
	return
}

func CommandSetEntityToDao(ecs *entity.CommandSet) (cs *dao.CommandSet, cmds []*dao.Command) {
	cs = &dao.CommandSet{
		ID:           utils.MustStrToInt64(ecs.ID),
		SessionID:    utils.MustStrToInt64(ecs.SessionID),
		ConnectionID: utils.MustStrToInt64(ecs.ConnectionID),
		Content:      ecs.Content,
		StartTimeMS:  ecs.StartTimeMS,
		EndTimeMS:    ecs.EndTimeMS,
		CreateTimeMS: ecs.CreateTimeMS,
		Progress:     int(ecs.Progress),
	}
	fp.StreamOf(ecs.Commands).Map(CommandEntityToDao).ToSlice(&cmds)
	return
}

func CommandEntityToDao(cmd *entity.Command) *dao.Command {
	var r *int
	if cmd.Reason != nil {
		r = utils.IntRef(int(*cmd.Reason))
	}
	var rt *int
	if cmd.ResultType != nil {
		rt = utils.IntRef(int(*cmd.ResultType))
	}
	var header *string
	if cmd.Header != nil {
		header = utils.StringRef(string(utils.MustJSONMarshal(cmd.Header)))
	}
	var rd *string
	if cmd.ReasonDetail != "" {
		rd = utils.StringRef(cmd.ReasonDetail)
	}
	return &dao.Command{
		ID:           utils.MustStrToInt64(cmd.ID),
		CommandSetID: utils.MustStrToInt64(cmd.CommandSetID),
		State:        int(cmd.State),
		Reason:       r,
		Content:      cmd.Content,
		StartTimeMS:  cmd.StartTimeMS,
		EndTimeMS:    cmd.EndTimeMS,
		ResultType:   rt,
		Header:       header,
		ReasonDetail: rd,
		Extra:        cmd.Extra, // todo:Extra类型不支持
	}
}

func ResultTypeEntityToModel(rt *entity.ResultType) *model.ResultType {
	if rt == nil {
		return nil
	}
	switch *rt {
	case entity.TableResult:
		return model.ResultTypePtr(model.ResultType_Table)
	}
	return nil
}
