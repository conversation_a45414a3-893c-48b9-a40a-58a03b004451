package conv

import (
	"github.com/qjpcpu/fp"
	"strconv"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
)

func SlowLogsSharedToModel(inputs []*shared.SlowLog) []*model.SlowLog {
	var convert = func(input *shared.SlowLog) *model.SlowLog {
		return &model.SlowLog{
			SQLTemplate:  input.SQLTemplate,
			Timestamp:    int32(input.Timestamp),
			SQLText:      input.SQLText,
			DB:           input.DB,
			User:         input.User,
			SourceIP:     input.SourceIP,
			ConnectionId: int32(input.ConnectionId),
			QueryTime:    input.QueryTime,
			LockTime:     input.LockTime,
			RowsExamined: int32(input.RowsExamined),
			RowsSent:     int32(input.RowsSent),
			ContextDB:    &input.ContextDB,
		}
	}

	outputs := make([]*model.SlowLog, 0, len(inputs))
	fp.StreamOf(inputs).Map(convert).ToSlice(&outputs)
	return outputs
}

func AggregateSlowLogsSharedToModel(inputs []*shared.AggregateSlowLog) []*model.AggregateSlowLog {
	var convert = func(input *shared.AggregateSlowLog) *model.AggregateSlowLog {
		return &model.AggregateSlowLog{
			SQLTemplate:       input.SQLTemplate,
			SQLTemplateID:     input.SQLTemplateID,
			DB:                input.DB,
			User:              input.User,
			SourceIP:          input.SourceIP,
			ExecuteCount:      int32(input.ExecuteCount),
			ExecuteCountRatio: input.ExecuteCountRatio,
			QueryTimeRatio:    input.QueryTimeRatio,
			LockTimeRatio:     input.LockTimeRatio,
			RowsSentRatio:     input.RowsSentRatio,
			RowsExaminedRatio: input.RowsExaminedRatio,
			SqlMethod:         utils.StringRef(input.SQLType),
			QueryTimeStats: &model.StatisticResult_{
				Total:   input.QueryTimeStats.Total,
				Min:     input.QueryTimeStats.Min,
				Max:     input.QueryTimeStats.Max,
				Average: input.QueryTimeStats.Average,
				//Stddev:  input.QueryTimeStats.Stddev,
				//Median:  input.QueryTimeStats.Median,
				//P95:     input.QueryTimeStats.P95,
			},
			LockTimeStats: &model.StatisticResult_{
				Total:   input.LockTimeStats.Total,
				Min:     input.LockTimeStats.Min,
				Max:     input.LockTimeStats.Max,
				Average: input.LockTimeStats.Average,
				//Stddev:  input.LockTimeStats.Stddev,
				//Median:  input.LockTimeStats.Median,
				//P95:     input.LockTimeStats.P95,
			},
			RowsSentStats: &model.StatisticResult_{
				Total:   input.RowsSentStats.Total,
				Min:     input.RowsSentStats.Min,
				Max:     input.RowsSentStats.Max,
				Average: input.RowsSentStats.Average,
				//Stddev:  input.RowsSentStats.Stddev,
				//Median:  input.RowsSentStats.Median,
				//P95:     input.RowsSentStats.P95,
			},
			RowsExaminedStats: &model.StatisticResult_{
				Total:   input.RowsExaminedStats.Total,
				Min:     input.RowsExaminedStats.Min,
				Max:     input.RowsExaminedStats.Max,
				Average: input.RowsExaminedStats.Average,
				//Stddev:  input.RowsExaminedStats.Stddev,
				//Median:  input.RowsExaminedStats.Median,
				//P95:     input.RowsExaminedStats.P95,
			},
			FirstAppearTime:   int32(input.FirstAppearTime),
			LastAppearTime:    int32(input.LastAppearTime),
			PTAnalysisResult_: input.PTAnalysisResult,
			PSM:               &input.PSM,
			Table:             &input.Table,
			SqlFingerprint:    &input.SqlFingerprint,
		}
	}

	outputs := make([]*model.AggregateSlowLog, 0, len(inputs))
	fp.StreamOf(inputs).Map(convert).ToSlice(&outputs)
	return outputs
}

func ExportTaskInfoToModel(detail *shared.TaskInfo) *model.TaskInfo {
	convertTime, _ := time.Parse("2006-01-02 15:04:05", detail.CreateTime)
	expireTime := convertTime.AddDate(0, 0, 1).Format("2006-01-02 15:04:05")
	return &model.TaskInfo{
		TaskId:         detail.TaskId,
		TaskName:       detail.TaskName,
		TaskStatus:     ToModelDownloadTaskStatusType(detail.TaskStatus),
		TopicId:        detail.TopicId,
		DataFormat:     &detail.DataFormat,
		CreateTime:     detail.CreateTime,
		ExpirationTime: expireTime,
		StartTime:      detail.StartTime,
		EndTime:        detail.EndTime,
		Query:          detail.Query,
		LogSize:        strconv.FormatInt(detail.LogSize, 10),
		LogCount:       strconv.FormatInt(detail.LogCount, 10),
		Compression:    &detail.Compression,
	}
}