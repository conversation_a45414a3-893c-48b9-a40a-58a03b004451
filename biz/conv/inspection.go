package conv

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"encoding/json"
	"github.com/qjpcpu/fp"
	"strconv"
	"time"
)

func InspectionTaskInfosEntityToModels(c []*entity.InspectionTask) []*model.InspectionItem {
	ret := make([]*model.InspectionItem, 0, len(c))
	fp.StreamOf(c).Map(InspectionTaskInfoEntityToModel).ToSlice(&ret)
	return ret
}

func InspectionTaskInfoEntityToModel(c *entity.InspectionTask) *model.InspectionItem {
	if c == nil {
		return nil
	}
	// 这里进行真正的转换过程
	var diskUsage = strconv.FormatFloat(c.DiskUsage, 'f', 2, 64) + "%"
	if c.InstanceType == shared.VeDBMySQL.String() {
		diskUsage = strconv.FormatFloat(c.DiskUsage, 'f', 2, 64) + "GiB"
	}
	item := &model.InspectionItem{
		TaskId:              conv.Int64ToStr(c.ID),
		InstanceId:          c.InstanceId,
		InstanceName:        c.InstanceName,
		TaskStatus:          c.InspectionStatus,
		ExecuteTime:         time.UnixMilli(c.InspectionExecuteTime).UTC().Format(time.RFC3339),
		TaskType:            c.InspectionType,
		InspectionStartTime: time.UnixMilli(c.InspectionStartTime).UTC().Format(time.RFC3339),
		InspectionEndTime:   time.UnixMilli(c.InspectionEndTime).UTC().Format(time.RFC3339),
		HealthScore:         int32(c.HealthScore),
		CpuUsage:            strconv.FormatFloat(c.CpuUsage, 'f', 2, 64) + "%",
		MemUsage:            strconv.FormatFloat(c.MemUsage, 'f', 2, 64) + "%",
		DiskUsage:           diskUsage,
		ConnUsage:           strconv.FormatFloat(c.ConnUsage, 'f', 2, 64) + "%",
		QPS:                 strconv.FormatFloat(c.Qps, 'f', 2, 64),
		TPS:                 strconv.FormatFloat(c.Tps, 'f', 2, 64),
		SlowLogNum:          c.SlowLog,
	}
	json.Unmarshal([]byte(c.LastOneNodeAgg), &item.LastOneAgg)
	return item
}

func InspectionReportEntityToBasicInfoModel(c *entity.InspectionReport) *model.InspectionInstanceBasicInfo {
	if c == nil {
		return nil
	}
	// 这里进行真正的转换过程
	item := &model.InspectionInstanceBasicInfo{
		InstanceId:            c.InstanceId,
		InstanceSpecification: c.InstanceSpec,
		InstanceVersion:       c.InstanceVersion,
	}
	return item
}

func InspectionReportEntityToResourceUsageModel(c *entity.InspectionReport) []*model.InspectionInstanceResourceUsage {
	ret := make([]*model.InspectionInstanceResourceUsage, 0)
	cpuUsage := &model.InspectionInstanceResourceUsage{
		Name: model.InspectionMetricName_CpuUsage,
		Max:  c.MaxCpuUsage,
		Min:  c.MinCpuUsage,
		Avg:  c.AvgCpuUsage,
		Unit: "%",
	}
	memUsage := &model.InspectionInstanceResourceUsage{
		Name: model.InspectionMetricName_MemUsage,
		Max:  c.MaxMemUsage,
		Min:  c.MinMemUsage,
		Avg:  c.AvgMemUsage,
		Unit: "%",
	}
	diskUsage := &model.InspectionInstanceResourceUsage{
		Name: model.InspectionMetricName_DiskUsage,
		Max:  c.MaxDiskUsage,
		Min:  c.MinDiskUsage,
		Avg:  c.AvgDiskUsage,
		Unit: "%",
	}
	connUsage := &model.InspectionInstanceResourceUsage{
		Name: model.InspectionMetricName_ConnUsage,
		Max:  c.MaxConnUsage,
		Min:  c.MinConnUsage,
		Avg:  c.AvgConnUsage,
		Unit: "%",
	}
	ret = append(ret, cpuUsage)
	ret = append(ret, memUsage)
	ret = append(ret, diskUsage)
	ret = append(ret, connUsage)
	return ret
}

func InspectionReportEntityToConnectionInfoModel(c *entity.InspectionReport) *model.InspectionConnectInfo {
	if c == nil {
		return nil
	}
	// 这里进行真正的转换过程
	return &model.InspectionConnectInfo{
		MaxConnection:      int32(c.MaxConnected),
		CurrentConnections: int32(c.ThreadConnected),
		RunningConnections: int32(c.ThreadRunning),
	}
}

func InspectionSlowLogsEntityToSlowLogModels(c []*entity.InspectionSlowLog) []*model.SlowLog {
	ret := make([]*model.SlowLog, 0, len(c))
	fp.StreamOf(c).Map(InspectionSlowLogEntityToSlowLogModels).ToSlice(&ret)
	return ret
}

func InspectionSlowLogEntityToSlowLogModels(c *entity.InspectionSlowLog) *model.SlowLog {
	if c == nil {
		return nil
	}
	// TODO 这里进行真正的转换过程
	item := &model.SlowLog{
		Timestamp:    0,
		SQLText:      "",
		DB:           c.DBName,
		User:         c.ExecuteUser,
		SourceIP:     "",
		ConnectionId: 0,
		QueryTime:    c.TotalQueryTime,
		LockTime:     c.AvgLockTime,
		RowsExamined: 0,
		RowsSent:     int32(c.AvgRowsSent),
		SQLTemplate:  c.SqlTemplate,
	}
	return item
}

func InstanceInfoModelToShare(instanceType model.InstanceType, modelInfo []*model.InspectionInstanceInfo) []*shared.InspectionInstanceInfo {
	ret := make([]*shared.InspectionInstanceInfo, 0, len(modelInfo))
	for _, val := range modelInfo {
		ret = append(ret, &shared.InspectionInstanceInfo{
			InstanceId:   val.InstanceId,
			InstanceName: val.InstanceName,
			InstanceType: shared.DataSourceType(instanceType),
		})
	}
	return ret
}
