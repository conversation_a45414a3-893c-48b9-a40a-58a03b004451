package conv

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"encoding/json"
	"fmt"
	"github.com/qjpcpu/fp"
	"strings"
	"time"
)

func TaskInfosEntityToModels(c []*entity.MigrationTask) []*model.DBTaskInfo {
	ret := make([]*model.DBTaskInfo, 0, len(c))
	fp.StreamOf(c).Map(TaskInfoEntityToModel).ToSlice(&ret)
	return ret
}

func TaskInfoEntityToModel(c *entity.MigrationTask) *model.DBTaskInfo {
	if c == nil {
		return nil
	}

	item := &model.DBTaskInfo{
		TaskId:       conv.Int64ToStr(c.ID),
		TaskType:     c.Type,
		DB:           c.DbName,
		Tables:       c.Tables,
		TaskStatus:   model.DBMigrationStatus(c.Status),
		TaskProgress: int32(c.ProgressPt),
		User:         c.UserName,
		CreateTime:   timestampMSToDateTimeString(c.CreatedAt),
		ExecTime:     timestampMSToDateTimeString(c.ExecutedAt),
		ExpiredTime:  timestampMSToDateTimeString(c.CreatedAt + 86400000),
	}
	exportConfig := &entity.ExportConfig{}
	if c.Type == entity.ExportType {
		err := json.Unmarshal([]byte(c.Config), exportConfig)
		if err != nil {
			return item
		}
		fromString, err := model.ExportContentFormatFromString(exportConfig.ContentFormat)
		if err != nil {
			return item
		}
		item.ContentFormat = fromString
	}
	return item
}

func TasksEntityToModels(c []*entity.MigrationTask) []*model.DBTaskItem {
	ret := make([]*model.DBTaskItem, 0, len(c))
	fp.StreamOf(c).Map(TaskItemEntityToModel).ToSlice(&ret)
	return ret
}

func TaskItemEntityToModel(c *entity.MigrationTask) *model.DBTaskItem {
	if c == nil {
		return nil
	}

	item := &model.DBTaskItem{
		TaskId:       conv.Int64ToStr(c.ID),
		InstanceId:   c.InstanceID,
		TenantId:     c.TenantID,
		TaskType:     c.Type,
		DB:           c.DbName,
		Tables:       c.Tables,
		TaskStatus:   model.DBMigrationStatus(c.Status),
		TaskProgress: int32(c.ProgressPt),
		User:         c.UserName,
		CreateTime:   timestampMSToDateTimeString(c.CreatedAt),
		ExecTime:     timestampMSToDateTimeString(c.ExecutedAt),
		ExpiredTime:  timestampMSToDateTimeString(c.CreatedAt + 86400000),
		InstanceType: c.DBType,
	}
	return item
}

func ProgressDetailEntityToModels(c []*entity.MigrationProgress) []*model.ProgressDetail {
	ret := make([]*model.ProgressDetail, 0, len(c))
	fp.StreamOf(c).Map(ProgressDetailEntityToModel).ToSlice(&ret)
	return ret
}

func ProgressDetailEntityToModel(c *entity.MigrationProgress) *model.ProgressDetail {
	if c == nil {
		return nil
	}

	item := &model.ProgressDetail{
		EventId:     c.ID,
		CreateTime:  timestampMSToDateTimeString(c.CreatedAt),
		Event:       c.ProgressDetail,
		ErrorStatus: int64(c.ErrorStatus),
	}
	return item
}

func timestampMSToDateTimeString(timestamp int64) string {
	tm := time.Unix(timestamp/1000, (timestamp%1000)*int64(time.Millisecond))
	localDateTime := tm.In(time.Now().Location()).Format("2006-01-02 15:04:05")
	return localDateTime
}

// StringSliceToString 字符串分片转换为字符串
func StringSliceToString(a []string) string {
	s := make([]string, len(a))
	for i, v := range a {
		s[i] = fmt.Sprintf("%v", v)
	}
	return strings.Join(s, ", ")
}

// StringToStringSlice 字符串转换为字符串切片
func StringToStringSlice(s string) []string {
	ret := strings.Split(s, ", ")
	return ret
}
