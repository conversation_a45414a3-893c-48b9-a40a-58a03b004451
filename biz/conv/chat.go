package conv

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity/chat"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"github.com/qjpcpu/fp"
	"strconv"
)

func ChatDaoToEntity(chatDao *dao.Chat) *chat.Chat {
	if chatDao == nil {
		return nil
	}
	return &chat.Chat{
		ID:         chatDao.ID,
		Name:       chatDao.Name,
		CreateTime: chatDao.CreateTime,
	}
}

func ChatDaoToEntities(chatsDao []*dao.Chat) (ret []*chat.Chat, err error) {
	err = fp.StreamOf(chatsDao).Map(ChatDaoToEntity).ToSlice(&ret)
	return
}

func ChatEntityToDao(chat *chat.Chat) *dao.Chat {
	if chat == nil {
		return nil
	}
	return &dao.Chat{
		ID:         chat.ID,
		Name:       chat.Name,
		CreateTime: chat.CreateTime,
		Deleted:    chat.Deleted,
	}
}
func ChatEntityToModel(chat *chat.Chat) *model.Chat {
	return &model.Chat{
		ChatId:      strconv.FormatInt(chat.ID, 10),
		Name:        chat.Name,
		Description: chat.Description,
		CreateTime:  int32(chat.CreateTime),
	}
}

func ChatEntitiesToModel(chats []*chat.Chat) (ret []*model.Chat, err error) {
	err = fp.StreamOf(chats).Map(ChatEntityToModel).ToSlice(&ret)
	return
}

func MessageDaoToEntity(messageDao *dao.Message) *chat.Message {
	if messageDao == nil {
		return nil
	}

	rateType, _ := model.RateModelReplyEnumFromString(messageDao.RatedType)
	return &chat.Message{
		ID:         messageDao.ID,
		Text:       messageDao.Text,
		ReplyID:    messageDao.ReplyID,
		CreateTime: messageDao.CreateTime,
		RateType:   &rateType,
	}
}

func MessagesDaoToEntities(messagesDao []*dao.Message) (ret []*chat.Message, err error) {
	err = fp.StreamOf(messagesDao).Map(MessageDaoToEntity).ToSlice(&ret)
	return
}

func MessageEntityToModel(message *chat.Message) *model.Message {
	return &model.Message{
		MessageId:  strconv.FormatInt(message.ID, 10),
		Text:       message.Text,
		ReplyId:    strconv.FormatInt(message.ReplyID, 10),
		CreateTime: int32(message.CreateTime),
		Reply:      message.RateType,
	}
}
func MessageEntitiesToModel(messages []*chat.Message) (ret []*model.Message, err error) {
	err = fp.StreamOf(messages).Map(MessageEntityToModel).ToSlice(&ret)
	return
}
