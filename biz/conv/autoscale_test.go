package conv

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"reflect"
	"testing"
)

func TestAutoScaleRulesEntityToModel(t *testing.T) {
	type args struct {
		c []*entity.AutoScaleRule
	}
	tests := []struct {
		name string
		args args
		want *model.DescribeAutoScaleRulesResp
	}{
		{
			name: "test",
			args: args{[]*entity.AutoScaleRule{
				{RuleId: 1, InstanceId: "123"},
			}},
			want: &model.DescribeAutoScaleRulesResp{
				InstanceId: "123",
			},
		},
		{
			name: "test1",
			args: args{[]*entity.AutoScaleRule{
				{RuleId: 1, InstanceId: "123", Enable: 1, ScalingType: 1},
			}},
			want: &model.DescribeAutoScaleRulesResp{
				InstanceId: "123",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := AutoScaleRulesEntityToModel(tt.args.c); reflect.DeepEqual(got, tt.want) {
				t.Errorf("AutoScaleRulesEntityToModel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAutoScaleEventEntityToModel(t *testing.T) {
	type args struct {
		c *entity.AutoScaleEvent
	}
	tests := []struct {
		name string
		args args
		want *model.AutoScaleEventItem
	}{
		{
			name: "test1",
			args: args{
				&entity.AutoScaleEvent{},
			},
			want: &model.AutoScaleEventItem{},
		},
		{
			name: "test2",
			args: args{
				nil,
			},
			want: &model.AutoScaleEventItem{
				EventId: "1",
			},
		},
		{
			name: "test3",
			args: args{},
			want: &model.AutoScaleEventItem{
				EventId: "1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := AutoScaleEventEntityToModel(tt.args.c); reflect.DeepEqual(got, tt.want) {
				t.Errorf("AutoScaleEventEntityToModel() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAutoScaleEventsEntityToModels(t *testing.T) {
	type args struct {
		c []*entity.AutoScaleEvent
	}
	tests := []struct {
		name string
		args args
		want []*model.AutoScaleEventItem
	}{
		{
			name: "test1",
			args: args{
				[]*entity.AutoScaleEvent{},
			},
			want: []*model.AutoScaleEventItem{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := AutoScaleEventsEntityToModels(tt.args.c); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AutoScaleEventsEntityToModels() = %v, want %v", got, tt.want)
			}
		})
	}
}
