package conv

import (
	"fmt"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity/instance"
	"code.byted.org/infcs/dbw-mgr/biz/entity/privilege"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
)

func UserEntityToDao(user *privilege.User) *dao.DbwUser {
	return &dao.DbwUser{
		ID:                    user.ID,
		UserID:                user.UserID,
		UserName:              user.UserName,
		Role:                  user.Role,
		MaxResultCount:        user.MaxResultCount,
		MaxResultExpiredTime:  user.MaxResultExpiredTime,
		MaxExecuteExpiredTime: user.MaxExecuteExpiredTime,
		MaxExecuteCount:       user.MaxExecuteCount,
		State:                 user.State,
	}
}

func UserDaoToEntity(user *dao.DbwUser) *privilege.User {
	return &privilege.User{
		ID:                    user.ID,
		UserID:                user.UserID,
		UserName:              user.UserName,
		MaxExecuteExpiredTime: user.MaxExecuteExpiredTime,
		MaxResultCount:        user.MaxExecuteCount,
		MaxExecuteCount:       user.MaxExecuteCount,
		State:                 user.State,
	}
}

func DatabasePrivilegeEntityToDao(dbPriv *privilege.DatabasePrivilege) *dao.DatabasePrivilege {
	return &dao.DatabasePrivilege{
		ID:            dbPriv.ID,
		TenantID:      dbPriv.TenantID,
		InstanceId:    dbPriv.InstanceId,
		InstanceType:  dbPriv.InstanceType,
		DbName:        dbPriv.DbName,
		UserID:        dbPriv.UserID,
		UserName:      dbPriv.UserName,
		PrivilegeType: dbPriv.PrivilegeType,
		ExpiredAt:     dbPriv.ExpiredAt,
	}
}

func DatabasePrivilegeDaoToEntity(databasePriv *dao.DatabasePrivilege) *privilege.DatabasePrivilege {
	return &privilege.DatabasePrivilege{
		ID:           databasePriv.ID,
		TenantID:     databasePriv.TenantID,
		InstanceId:   databasePriv.InstanceId,
		InstanceType: databasePriv.InstanceType,
	}
}

func InstanceEntityToDao(ins *instance.CtrlInstance) *dao.DbwInstance {
	insDao := &dao.DbwInstance{
		ID:                   ins.ID,
		InstanceId:           ins.InstanceId,
		InstanceName:         ins.InstanceName,
		InstanceType:         ins.InstanceType,
		TenantId:             ins.TenantId,
		Region:               ins.Region,
		DatabaseUser:         ins.DatabaseUser,
		DatabasePassword:     ins.DatabasePassword,
		OwnerUid:             ins.OwnerUid,
		DbaUid:               ins.DbaUid,
		WorkflowTemplateId:   ins.WorkflowTemplateId,
		ApprovalFlowConfigId: ins.ApprovalFlowConfigId,
		SecurityGroupId:      ins.SecurityGroupId,
		Address:              ins.Address,
		ControlMode:          ins.ControlMode,
		Tags:                 ins.Tags,
		Extra:                make(map[string]interface{}),
	}
	if _, err := model.LinkTypeFromString(ins.Source.String()); err == nil {
		insDao.Source = ins.Source.String()
	}
	if ins.Extra != nil {
		for k, v := range ins.Extra {
			insDao.Extra[k] = v
		}
	}
	return insDao
}

func InstanceDaoToEntity(ins *dao.DbwInstance) *instance.CtrlInstance {
	ctrlInstance := &instance.CtrlInstance{
		ID:                   ins.ID,
		InstanceId:           ins.InstanceId,
		InstanceType:         ins.InstanceType,
		InstanceName:         ins.InstanceName,
		TenantId:             ins.TenantId,
		Region:               ins.Region,
		SecurityGroupId:      ins.SecurityGroupId,
		WorkflowTemplateId:   ins.WorkflowTemplateId,
		ApprovalFlowConfigId: ins.ApprovalFlowConfigId,
		DatabasePassword:     ins.DatabasePassword,
		DatabaseUser:         ins.DatabaseUser,
		Address:              ins.Address,
		Extra:                make(map[string]string),
	}
	for k, v := range ins.Extra {
		key := fmt.Sprintf("%v", k)
		val := fmt.Sprintf("%v", v)
		ctrlInstance.Extra[key] = val
	}
	linkType, err := model.LinkTypeFromString(ins.Source)
	if err != nil {
		linkType = model.LinkType_Volc
	}
	ctrlInstance.Source = linkType
	return ctrlInstance
}

func InstanceDaoListToEntity(ins []*dao.DbwInstance) []*instance.CtrlInstance {
	res := make([]*instance.CtrlInstance, 0)
	for _, i := range ins {
		res = append(res, InstanceDaoToEntity(i))
	}
	return res
}
