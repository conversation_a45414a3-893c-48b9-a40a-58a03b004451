package conv

import (
	"strconv"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/qjpcpu/fp"
)

func CommandResultToDao(cr *entity.CommandResult) *dao.CommandResult {
	expired := 0
	if cr.SessionExpired {
		expired = 1
	}
	return &dao.CommandResult{
		SessionID:      utils.MustStrToInt64(cr.SessionID),
		ConnectionID:   utils.MustStrToInt64(cr.ConnectionID),
		CommandID:      utils.MustStrToInt64(cr.CommandID),
		SessionExpired: expired,
		Offset:         cr.ChunkOffset,
		Row:            utils.StringRef(string(utils.MustJSONMarshal(cr.Chunk))),
		CreateTimeMS:   cr.CreateTimeMS,
	}
}

func CommandResultToEntity(cr *dao.CommandResult) *entity.CommandResult {
	var row *shared.CommandResultChunk_Row
	if cr.Row != nil {
		row = &shared.CommandResultChunk_Row{}
		utils.MustUnmarshal([]byte(*cr.Row), row)
	}
	return &entity.CommandResult{
		SessionID:      strconv.FormatInt(cr.SessionID, 10),
		ConnectionID:   strconv.FormatInt(cr.ConnectionID, 10),
		CommandID:      strconv.FormatInt(cr.CommandID, 10),
		SessionExpired: cr.SessionExpired == 1,
		ChunkOffset:    cr.Offset,
		Chunk:          row,
		CreateTimeMS:   cr.CreateTimeMS,
	}
}

func CommandResultToDaos(cr []*entity.CommandResult) (ret []*dao.CommandResult) {
	fp.StreamOf(cr).Map(CommandResultToDao).ToSlice(&ret)
	return
}

func CommandResultToEntities(cr []*dao.CommandResult) (ret []*entity.CommandResult) {
	fp.StreamOf(cr).Map(CommandResultToEntity).ToSlice(&ret)
	return
}
