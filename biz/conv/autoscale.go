package conv

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	"github.com/qjpcpu/fp"
	"time"
)

func AutoScaleEventsEntityToModels(c []*entity.AutoScaleEvent) []*model.AutoScaleEventItem {
	ret := make([]*model.AutoScaleEventItem, 0, len(c))
	fp.StreamOf(c).Map(AutoScaleEventEntityToModel).ToSlice(&ret)
	return ret
}

func AutoScaleEventEntityToModel(c *entity.AutoScaleEvent) *model.AutoScaleEventItem {
	if c == nil {
		return nil
	}
	// 这里进行真正的转换过程
	return &model.AutoScaleEventItem{
		EventId:         utils.Int64ToStr(c.EventId),
		BeforeAutoScale: c.BeforeValue,
		AfterAutoScale:  c.AfterValue,
		Action:          model.AutoScaleAction(c.ScalingType),
		StartTime:       time.UnixMilli(c.StartTime * 1000).UTC().Format(time.RFC3339),
		RuleId:          utils.Int64ToStr(c.RuleId),
		EventStatus:     model.EventStatus(c.Status),
		TriggerValue:    c.TargetUtilization,
		Before:          c.BeforeMetric,
		After:           c.AfterMetric,
		Memo:            c.Memo,
	}
}

func AutoScaleRulesEntityToModel(c []*entity.AutoScaleRule) *model.DescribeAutoScaleRulesResp {
	if c == nil || len(c) == 0 {
		return nil
	}
	// 这里进行真正地转换过程
	var observationWindow int32
	var instanceId string
	var configs []*model.RuleConfig
	for _, val := range c {
		var isOpen = false
		var scalingType = model.AutoScaleAction_Expand
		observationWindow = val.ObservationWindow
		instanceId = val.InstanceId
		if val.Enable == 1 {
			isOpen = true
		}
		if val.ScalingType == 1 {
			scalingType = model.AutoScaleAction_Reduce
		}
		configs = append(configs, &model.RuleConfig{
			Action:       scalingType,
			IsOpen:       isOpen,
			Threshold:    val.ScalingThreshold,
			ScalingLimit: utils.StringRef(val.ScalingLimit),
		})
	}
	return &model.DescribeAutoScaleRulesResp{
		InstanceId:        instanceId,
		ObservationWindow: utils.Int32Ref(observationWindow),
		Configs:           configs,
	}
}
