package conv

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"github.com/qjpcpu/fp"
	"strconv"
)

func SensitiveDatabaseEntityToDao(db *entity.SensitiveDatabase) *dao.SensitiveDatabase {
	return &dao.SensitiveDatabase{
		ID:           db.ID,
		TenantID:     db.TenantID,
		InstanceId:   db.InstanceId,
		InstanceType: db.InstanceType,
		Name:         db.Name,
		Comment:      db.Comment,
	}
}

func SensitiveDatabaseDaoToEntity(db *dao.SensitiveDatabase) *entity.SensitiveDatabase {
	return &entity.SensitiveDatabase{
		ID:           db.ID,
		TenantID:     db.TenantID,
		InstanceId:   db.InstanceId,
		InstanceType: db.InstanceType,
		Name:         db.Name,
		Comment:      db.Comment,
	}
}

func SensitiveDatabaseEntityToModel(db *entity.SensitiveDatabase) *model.SensitiveDatabase {
	res := &model.SensitiveDatabase{}
	res.Name = db.Name
	fp.StreamOf(db.GrantUsers).Map(func(u *entity.GrantUser) *model.GrantUser {
		return &model.GrantUser{
			ID:       strconv.FormatInt(u.ID, 10),
			UserName: u.UserName,
			UserId:   u.UserID,
			Grantor:  u.Grantor,
		}
	}).ToSlice(&res.GrantUsers)
	return res
}

func SensitiveDatabaseEntitiesToModel(dbs []*entity.SensitiveDatabase) []*model.SensitiveDatabase {
	var res []*model.SensitiveDatabase
	fp.StreamOf(dbs).Map(func(d *entity.SensitiveDatabase) *model.SensitiveDatabase {
		return SensitiveDatabaseEntityToModel(d)
	}).ToSlice(&res)
	return res
}
