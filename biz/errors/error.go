package errors

import (
	"errors"
	"strings"
)

var NotFound = errors.New("not found")

var ErrAlreadyExist = errors.New("already exist")

func IsVedbAccountNotExistErr(err error) bool {
	return strings.Contains(err.<PERSON><PERSON>r(), "InvalidAccountName_NotFound")
}

var TooManyConnection = errors.New("too many connection error")

func IsWhiteIPError(err error) bool {
	return strings.Contains(err.<PERSON>r(), `IP NOT IN white List`)
}

func IsUsernamePasswordError(err error) bool {
	return strings.Contains(err.Error(), "check username/password") ||
		strings.Contains(err.<PERSON>rror(), "连接错误，请检查账户名、密码") ||
		strings.Contains(err.Error(), "请检查用户名/密码") ||
		strings.Contains(err.Error(), "authentication failed")
}

func IsEmptyVPCError(err error) bool {
	return strings.Contains(err.<PERSON>rror(), "VpcId is null")
}

func IsAccessDeniedError(err error) bool {
	return strings.Contains(strings.ToLower(err.Error()), "access denied")
}

func IsOperateAllowlistError(err error) bool {
	return strings.Contains(strings.ToLower(err.Error()), strings.ToLower("OperationDenied.AllowListStatus"))
}

func IsLoginFailedError(err error) bool {
	return strings.Contains(strings.ToLower(err.Error()), strings.ToLower("login error"))
}

func IsPSMAuthFailed(err error) bool {
	return strings.Contains(strings.ToLower(err.Error()), strings.ToLower("fail bytedmysql: auth failed"))
}
