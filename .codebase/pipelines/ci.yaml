name: dbwmgr
trigger: [change]
jobs:
  build:
    name: build project
    image: hub.byted.org/codebase/ci_go_1_21:latest
    runs-on:
      spec: m1.4xlarge
    steps:
      - commands:
          - export TCE_HOST_IP=********** # 临时解决metrics导致的单测覆盖率执行失败问题
          - go mod tidy
          - go test -gcflags="all=-l -N" ./... -coverprofile=c.out -coverpkg=./...
      - name: codecov
        uses: actions/codecov@v1
        inputs:
          file: "c.out"   # 覆盖率结果文件位置
          fail_ci_if_error: true
