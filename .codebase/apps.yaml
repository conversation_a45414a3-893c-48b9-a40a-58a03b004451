codecov:
   status:
     # 全量覆盖率配置
     project:
       default: 
         threshold: 1%  
         base: "change" 
         line_limit: 10
         minimum_coverage: 1%
         paths:
           - "biz"
           - "!biz/infrastructure"
           - "!*decorator.go"
           - "!*.pb.go" 
           - "!bootstrap"
           - "!biz/middleware"
           - "!biz/test"
         if_not_found: "success"
     diff: 
       default:  
         minimum_coverage: 50%
         threshold: 1%
         if_not_found: error
         paths:
           - "biz"
           - "!biz/infrastructure"
           - "!*decorator.go"
           - "!*.pb.go"
           - "!bootstrap"
           - "!biz/middleware"
           - "!biz/test"
